{"name": "lingmyat/filament-starter-kit", "description": "Filament starter kit include ready make Multi-Tenant, Filament Shield, Filament Exception, Customize Login Page", "type": "project", "license": "MIT", "keywords": ["laravel", "filament", "starter kit"], "require": {"php": "^8.2", "awcodes/filament-badgeable-column": "^2.3", "awcodes/filament-table-repeater": "^3.0", "awcodes/filament-tiptap-editor": "^3.0", "barryvdh/laravel-dompdf": "^3.1", "bezhansalleh/filament-exceptions": "^2.1", "bezhansalleh/filament-shield": "^3.1", "doctrine/dbal": "^3.8", "drafolin/filament-collapse": "^1.1", "ecomphp/tiktokshop-php": "^2.6", "filament/filament": "^3.2", "filament/minimal-theme": "^4.0", "filament/spatie-laravel-media-library-plugin": "^3.2", "filament/spatie-laravel-settings-plugin": "^3.2", "filipfonal/filament-log-manager": "^2.0", "guzzlehttp/guzzle": "^7.9", "icetalker/filament-table-repeater": "^1.3", "intervention/image": "^3.9", "intervention/image-laravel": "^1.3", "irazasyed/telegram-bot-sdk": "^3.15", "jaocero/activity-timeline": "^1.2", "jeffgreco13/filament-breezy": "*", "laravel/framework": "^11.0", "laravel/horizon": "^5.30", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "^3.24", "maatwebsite/excel": "^3.1", "malzariey/filament-daterangepicker-filter": "^3.3", "mohamedsabil83/filament-forms-tinyeditor": "^2.3", "mokhosh/filament-kanban": "^2.9", "mokhosh/filament-rating": "^1.4", "novadaemon/filament-combobox": "^1.1", "opcodesio/log-viewer": "^3.12", "openai-php/client": "^0.10.3", "openai-php/laravel": "^0.11.0", "parallax/filament-comments": "^1.3", "parfaitementweb/filament-country-field": "^2.0", "phpoffice/phpspreadsheet": "^1.29", "pusher/pusher-php-server": "^7.2", "shuvroroy/filament-spatie-laravel-backup": "^2.2", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-activitylog": "^4.7", "stechstudio/filament-impersonate": "*", "symfony/css-selector": "^7.1", "symfony/dom-crawler": "^7.1", "valentin-morice/filament-json-column": "^1.3", "webklex/laravel-imap": "^4.1", "webklex/php-imap": "^4.1", "wildbit/postmark-php": "^6.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan vendor:publish --force --tag=livewire-assets", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"filament-themes": {"type": "composer", "url": "https://filamentthemes.com/composer"}, "whizzy": {"type": "composer", "url": "https://whizzy.dev/composer"}}}