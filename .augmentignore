# ===========================================
# AUGMENT IGNORE FILE - OPTIMIZED FOR LARAVEL + FILAMENT
# ===========================================
# Tối ưu cho dự án Laravel với Filament, TikTok Shop, POD Business
# Loại trừ các file không cần thiết để AI load nhanh hơn

# ===========================================
# DEPENDENCIES & VENDOR FILES
# ===========================================
# PHP Dependencies
/vendor/
composer.lock

# Node.js Dependencies  
/node_modules/
package-lock.json
yarn.lock
pnpm-lock.yaml

# ===========================================
# BUILD & COMPILED FILES
# ===========================================
# Laravel Mix/Vite Build
/public/build/
/public/hot
/public/storage
/public/mix-manifest.json

# CSS/JS Compiled
/public/css/
/public/js/
/public/livewire/

# ===========================================
# CACHE & TEMPORARY FILES
# ===========================================
# Laravel Cache
/bootstrap/cache/
/storage/framework/cache/
/storage/framework/sessions/
/storage/framework/views/
/storage/framework/testing/

# Application Cache
*.cache
.cache/
.tmp/
temp/

# ===========================================
# LOGS & DEBUG FILES
# ===========================================
# Laravel Logs
/storage/logs/
*.log

# Debug Files
.debug
debug.log
error.log

# ===========================================
# ENVIRONMENT & CONFIG
# ===========================================
# Environment Files
.env
.env.*
!.env.example

# IDE Helper Files
_ide_helper.php
_ide_helper_models.php
.phpstorm.meta.php

# ===========================================
# MEDIA & ASSETS
# ===========================================
# Large Media Files
*.mp4
*.avi
*.mov
*.mkv
*.webm
*.flv

# Images (keep small ones for context)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.webp
*.svg
*.ico

# Audio Files
*.mp3
*.wav
*.flac
*.aac
*.ogg

# Archive Files
*.zip
*.rar
*.tar
*.gz
*.7z
*.bz2

# ===========================================
# DATABASE & STORAGE
# ===========================================
# Database Files
*.sqlite
*.db
*.sql
database.sqlite

# Storage Files
/storage/app/public/
/storage/app/private/

# ===========================================
# DOCUMENTATION & MISC
# ===========================================
# Documentation
/docs/
*.md
!README.md
*.txt
!composer.json
!package.json

# License Files
LICENSE*
CHANGELOG*
HISTORY*

# ===========================================
# IDE & EDITOR FILES
# ===========================================
# VSCode
.vscode/
*.code-workspace

# PhpStorm
.idea/
*.iml

# Sublime Text
*.sublime-*

# Vim
*.swp
*.swo
*~

# ===========================================
# OS GENERATED FILES
# ===========================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.directory

# ===========================================
# VERSION CONTROL
# ===========================================
.git/
.gitignore
.gitattributes
.gitmodules

# ===========================================
# TESTING & CI/CD
# ===========================================
# PHPUnit
/tests/
phpunit.xml
.phpunit.result.cache

# Coverage Reports
/coverage/
clover.xml

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
circle.yml

# ===========================================
# SPECIFIC TO THIS PROJECT
# ===========================================
# TikTok Data Files
/public/csv/
*.csv

# Supervisor Config
supervisor.conf

# Custom Logs
/storage/logs/auto-fulfill/
/storage/logs/flashdeal/

# Public Vendor Assets (already compiled)
/public/vendor/

# Language Files (if not needed for context)
/lang/vendor/

# ===========================================
# LARGE CONFIGURATION FILES
# ===========================================
# Large config files that don't change often
/config/filesystems.php
/config/broadcasting.php
/config/cors.php
/config/hashing.php
/config/view.php

/database
/resources

# ===========================================
# FILAMENT SPECIFIC
# ===========================================
# Filament compiled assets (already in public)
# Keep source files in app/Filament/ for context

# ===========================================
# KEEP IMPORTANT FILES FOR CONTEXT
# ===========================================
# These files are important for AI context:
# - /app/ (all application code)
# - /routes/ (routing definitions)  
# - /config/app.php (main config)
# - /config/database.php (database config)
# - /database/migrations/ (database structure)
# - composer.json (dependencies)
# - package.json (frontend dependencies)
# - artisan (CLI tool)
