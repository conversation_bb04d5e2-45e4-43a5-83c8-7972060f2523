<?php

use App\Http\Controllers\DesignController;
use App\Http\Controllers\OrderSyncController;
use App\Http\Controllers\TextToSpeechController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Order;
use App\Models\Store;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\ImageUploadController;
use App\Http\Controllers\TelegramWebhookController;
use App\Http\Controllers\Telegram\TelegramBotController;
use App\Http\Controllers\VerificationCodeController;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\ProductStatisticsController;
use App\Http\Controllers\Api\TikTokSyncController;
use App\Http\Controllers\Api\IdeogramSyncController;
use App\Http\Controllers\Api\VectorQueryController;
use App\Http\Controllers\Api\SimpleVectorQueryController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('/get-store-data', [OrderSyncController::class, 'getStoreData']);
Route::post('/order_sync', [OrderSyncController::class, 'sync']);
Route::post('/check-token', [OrderSyncController::class, 'checkToken']);
Route::post('/check-multiple-order-sync-status', [OrderSyncController::class, 'checkMultipleOrderSyncStatus']);

Route::get('/auth-tiktok', [OrderSyncController::class, 'authTiktok']);
Route::get('/get-tiktok-product', [OrderSyncController::class, 'getProduct']);

Route::post('/sync-tiktok-label', [OrderSyncController::class, 'syncLabel']);
Route::post('/sync-tiktok-bulk-order', [OrderSyncController::class, 'syncBulkOrder']);

// API lấy số tiền đang hold tại shop TikTok
Route::get('/tiktok-hold-amount', [OrderSyncController::class, 'getHoldAmount']);

// Route::post('/update-cookies', [OrderSyncController::class, 'updateCookies']);
Route::post('/check-update-needed', [OrderSyncController::class, 'checkUpdateNeeded']);

Route::get('/design/max-clone-id', [DesignController::class, 'max_clone_id']);

Route::middleware(['throttle:api'])->group(function () {

    Route::get('/design/import-design', [DesignController::class, 'import']);
});

Route::get('/celebration/stats', function () {
    $yesterdayOrders = Order::whereDate('created_at', today()->subDay())->count();
    $todayOrders = Order::whereDate('created_at', today())->count();

    return response()->json([
        'todayOrders' => $todayOrders,
        'yesterdayOrders' => $yesterdayOrders,
        'todayRevenue' => Order::whereDate('created_at', today())->sum('total'),
        'bestSellerName' => Store::withCount(['orders' => function ($query) {
            $query->whereDate('created_at', today());
        }])->orderByDesc('orders_count')->first()->owner->name
    ]);
});



Route::post('/text-to-speech', [TextToSpeechController::class, 'convert']);

Route::post('/upload-mockup', [ImageUploadController::class, 'storeBase64']);


// Route::get('/productions/{production}', function (Production $production) {
//     return $production->load(['blank', 'assignedUser']);
// })->middleware('auth');

Route::post('/check-trademark', [App\Http\Controllers\Api\TrademarkController::class, 'check'])
    ->middleware('auth:sanctum');

Route::post('/verification-codes', [VerificationCodeController::class, 'store']);
Route::post('/verification-codes/lookup', [VerificationCodeController::class, 'lookup']);
Route::post('/verification-codes/mark-as-used', [VerificationCodeController::class, 'markAsUsed']);
Route::get('/verification-codes/latest', [VerificationCodeController::class, 'latestCodes']);
Route::post('/incoming-email', [VerificationCodeController::class, 'processIncomingEmail']);


Route::prefix('telegram')->group(function () {
    // New bot webhook endpoint - for API token registration system
    Route::post('/webhook', [TelegramBotController::class, 'handleWebhook']);
    // Legacy webhook endpoint - for backward compatibility
    Route::post('/legacy/webhook', [TelegramWebhookController::class, 'handle']);
    // Special bot webhook endpoint - for auto-registration system
    Route::post('/special/webhook', [TelegramWebhookController::class, 'handle']);
    // Webhook management endpoints
    Route::get('/setup-webhook', [TelegramWebhookController::class, 'setupWebhook']);
    Route::get('/webhook-info', [TelegramWebhookController::class, 'getWebhookInfo']);
});

// Product Statistics Routes
Route::prefix('v1')->group(function () {
    Route::get('/products/topkeywords', [ProductStatisticsController::class, 'getTopFiveProducts'])
        ->middleware('api.token');

    // Seller API Routes
    Route::post('/seller', [App\Http\Controllers\Api\SellerController::class, 'index'])
        ->middleware('api.token');

    // Speed Analytics API Routes
    Route::prefix('analytics')->middleware('api.token')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Api\SpeedAnalyticsController::class, 'dashboard']);
        Route::get('/order-speed', [App\Http\Controllers\Api\SpeedAnalyticsController::class, 'orderSpeed']);
        Route::get('/design-speed', [App\Http\Controllers\Api\SpeedAnalyticsController::class, 'designSpeed']);
        Route::get('/fulfillment-speed', [App\Http\Controllers\Api\SpeedAnalyticsController::class, 'fulfillmentSpeed']);
        Route::get('/bottlenecks', [App\Http\Controllers\Api\SpeedAnalyticsController::class, 'bottlenecks']);
        Route::get('/alerts', [App\Http\Controllers\Api\SpeedAnalyticsController::class, 'alerts']);
        Route::get('/capacity-planning', [App\Http\Controllers\Api\SpeedAnalyticsController::class, 'capacityPlanning']);
    });
});

// TikTok Shop Sync API Routes
Route::post('/sysn-tiktok-data', [App\Http\Controllers\Api\TikTokSyncController::class, 'syncTikTokData']);


Route::post('/ideo-sycn', [IdeogramSyncController::class, 'sync']);
Route::get('/ideo-sycn', [IdeogramSyncController::class, 'getCredentials']);

// Ideogram Job API Routes (fake endpoints)
Route::get('/get-ideo-job', [IdeogramSyncController::class, 'getJob']);
Route::post('/update-ideo-job', [IdeogramSyncController::class, 'updateJob']);

// Ideogram Sync API Routes
Route::prefix('ideo')->group(function () {
    Route::post('/sync', [IdeogramSyncController::class, 'sync']);
    Route::get('/status', [IdeogramSyncController::class, 'status']);
    Route::get('/credentials', [IdeogramSyncController::class, 'getCredentials']);
});

// Vector Query API Routes
Route::prefix('vector')->group(function () {
    Route::post('/query', [VectorQueryController::class, 'query'])->middleware('api.token');
    Route::post('/simple-query', [SimpleVectorQueryController::class, 'query'])->middleware('api.token');
});

// Test API Token Route
Route::get('/test-token', function (Request $request) {
    $user = $request->user();
    return response()->json([
        'success' => true,
        'message' => 'API token is valid!',
        'user' => [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
        ],
        'timestamp' => now()->toISOString(),
    ]);
})->middleware('api.token');
