<?php

use App\Http\Controllers\ImageController;
use App\Http\Controllers\OrderSyncController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\BusinessIntelligenceController;
use App\Livewire\PrintOrderItem;
use App\Livewire\RoadmapSeller;
use App\Livewire\ViewTiktokProduct;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Broadcast;
use App\Models\Store;
use App\Http\Controllers\ImageUploadController;
use App\Models\Invoice;
use App\Models\User;
use App\Models\Design;
use App\Exports\SellerFinanceExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Controllers\TeamInvoiceController;
use App\Http\Controllers\TelegramWebhookController;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;
use App\Models\Post;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});
// Vô hiệu hóa route đăng ký
Route::get('/roadmap-seller', RoadmapSeller::class)->name('roadmap-seller');
Route::get('/app/print-order/{id}', PrintOrderItem::class)->name('print-order')->middleware('auth');
Route::get('/app/view-tiktok-product', ViewTiktokProduct::class)->name('view-tiktok-product');
Route::get('/image/{encryptedUrl}', [ImageController::class, 'showImage'])->name('image.show');
Route::get('/export-tiktok-product', [ViewTiktokProduct::class, 'exportToExcel'])->name('export-tiktok-product');
Route::get('/login', function () {
    return redirect(route('filament.app.auth.login'));
})->name('login');

Route::middleware(['web'])->group(function () {
    Route::get('/tiktok-auth/{id}', [OrderSyncController::class, 'tiktokAuth'])->name('tiktok.auth');
    Route::get('/tiktok-callback', [OrderSyncController::class, 'tiktokCallback'])->name('tiktok.callback');
    Route::get('/tiktok-webhook', [OrderSyncController::class, 'tiktokWebhook'])->name('tiktok.webhook');
    Route::get('/get-tiktok-product', [OrderSyncController::class, 'getProduct']);
});

Route::middleware(['auth', 'role:super_admin'])->group(function () {
    Route::get('admin/resources/logs/{record}/download', function ($record) {
        $path = base64_decode($record);
        if (File::exists($path)) {
            return response()->download($path);
        }
        return back()->with('error', 'Log file not found');
    })->name('filament.admin.resources.logs.download');
});

// Chỉ cho phép trong môi trường local và development
Route::middleware(['env.check'])->group(function () {
    Route::get('/test/celebration', [TestController::class, 'testOrderCelebration']);
    Route::get('/test/new-order', [TestController::class, 'simulateNewOrder']);
});

Route::get('/test-n', [TestController::class, 'testNotification']);
Broadcast::routes(['middleware' => ['web']]);

// Route::get('/kkk', function () {
//     $storeStats = Store::getStoreTargetStats();
//     return view('order-celebration', compact('storeStats'));
// });

Route::post('/upload-image', [ImageUploadController::class, 'store'])
    ->name('upload.image')
    ->middleware(['auth']);


Route::get('/privacy-policy', function () {
    return view('privacy-policy');
})->name('privacy.policy');

Route::middleware(['auth'])->group(function () {

    Route::get('invoice/{invoice}/print', [Invoice::class, 'renderPrintView'])->name('invoice.print');

    Route::get('invoice-design/{designer_id}/print', [Design::class, 'renderPrintView'])->name('invoice-design.print');

    // Team invoice printing route
    Route::get('team/{team}/invoice/{month?}', [TeamInvoiceController::class, 'printTeamInvoice'])
        ->name('team.invoice.print')
        ->where('month', '[0-9]{4}-[0-9]{2}');

    Route::get('/download-finance/{record}', function ($record) {
        $finance = \App\Models\SellerFinance::find($record);
        $timestamp = now()->format('YmdHis');

        return Excel::download(
            new \App\Exports\SellerFinanceExport($finance),
            "finance_report_{$finance->seller->name}_{$finance->month->format('Y_m')}_{$timestamp}.xlsx"
        )
            ->deleteFileAfterSend(true);
    })->name('download.finance');



});

// Post preview route - sử dụng slug (cho admin)
Route::get('/posts/preview/{post:slug}', function (Post $post) {
    return view('posts.preview', compact('post'));
})->name('posts.preview');

// Public post view route - sử dụng slug
Route::get('/posts/{post:slug}', function (Post $post) {
    // Chỉ hiển thị bài viết đã publish
    if (!$post->isPublished()) {
        abort(404);
    }

    // Tăng view count
    $post->incrementViews();

    return view('posts.show', compact('post'));
})->name('posts.show');



// Test route để kiểm tra proxy purchase
Route::get('/test-proxy-buy', function () {
    return 'Test route hoạt động! URL: /app/proxy-purchases/buy';
})->name('test.proxy.buy');

// Business Intelligence PDF Viewer Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/bi-reports/{report}/pdf', [BusinessIntelligenceController::class, 'viewPdf'])
        ->name('bi-reports.pdf');

    Route::get('/bi-reports/{report}/download', [BusinessIntelligenceController::class, 'downloadPdf'])
        ->name('bi-reports.download');

    Route::get('/bi-reports/{report}/preview', [BusinessIntelligenceController::class, 'previewReport'])
        ->name('bi-reports.preview');
});



