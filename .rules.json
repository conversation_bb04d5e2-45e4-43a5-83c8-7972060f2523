{"projectDescription": "Laravel, FilamentPHP, and TALL stack application with TikTok Shop integration", "keyPrinciples": ["Write concise, technical responses with accurate PHP 8.2+ examples", "Follow Laravel and FilamentPHP best practices and conventions", "Use object-oriented programming with a focus on SOLID principles", "Prefer iteration and modularization over duplication", "Use descriptive variable and method names", "Favor dependency injection and service containers"], "criticalFeatures": {"teamManagement": ["Implement robust role and permission system using Filament Shield", "Design team hierarchy with proper access control", "Create team-based dashboards and workspaces", "Implement team activity logging and auditing", "Design collaborative workflows with proper notifications"], "storeManagement": ["Implement multi-store architecture with proper isolation", "Design inventory management systems with stock tracking", "Create store performance analytics and reporting", "Implement store-specific configurations and settings", "Design store onboarding and verification workflows"], "orderOperations": ["Implement comprehensive order lifecycle management", "Design efficient order processing workflows", "Create order tracking and status notification systems", "Implement order filtering and batch operations", "Design fulfillment tracking and logistics integration"], "sellerPaymentSystem": ["Implement accurate commission and fee calculations", "Design transparent payment reporting for sellers", "Create automated payment scheduling and processing", "Implement financial reconciliation and audit trails", "Design dispute resolution and adjustment workflows"]}, "context7Integration": {"features": ["Use Context7 MCP Server for documentation access", "Implement real-time data loading with Context7", "Create widgets that utilize Context7 for enhanced functionality", "Implement lazy loading for performance optimization"], "documentationLibraries": ["Laravel Framework", "FilamentPHP", "Livewire", "Alpine.js", "Tailwind CSS", "Spatie packages", "TikTok Shop PHP SDK", "<PERSON>vel <PERSON>", "OpenAI PHP SDK"]}, "dependencies": [{"name": "<PERSON><PERSON>", "version": "11.x"}, {"name": "PHP", "version": "8.2+"}, {"name": "FilamentPHP", "version": "3.x"}, {"name": "Livewire", "version": "3.x"}, {"name": "Alpine.js", "version": "latest"}, {"name": "Tailwind CSS", "version": "latest"}, {"name": "Spatie packages", "description": "Media Library, Activity Log, Backup, Settings"}, {"name": "TikTok Shop API", "description": "Integration"}, {"name": "<PERSON>vel <PERSON>", "description": "Queue management"}, {"name": "OpenAI", "description": "Integration"}, {"name": "Context7", "description": "Documentation and enhanced development"}]}