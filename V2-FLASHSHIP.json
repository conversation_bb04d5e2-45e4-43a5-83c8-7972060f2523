{"info": {"_postman_id": "40fe3ea5-8b5d-4987-9e94-90be27af97ca", "name": "V2-FLASHSHIP POD API FOR SELLERS", "description": "## **Create and manage your orders with FlashShip API.**\n\nWelcome to the FlashShip API documentation. This API enables seamless integration with our services, allowing you to create and manage order, track deliveries, and access real-time updates efficiently.\n\nThe API is designed to integrate with consumers' back-end services. It accepts and returns JSON-encoded data.\n\nCustomers must provide the IP addresses of their production servers to FlashShip for whitelisting before using the production API.\n\n**Base API URL:**\n\n- Test environment: [https://devpod.flashship.net/seller-api-v2](https://devpod.flashship.net/seller-api-v2)\n    \n- Production environment: [https://api.flashship.net/seller-api-v2](https://api.flashship.net/seller-api-v2)\n    \n\n---\n\n## **Change logs:**\n\n**Update 29 Apr 2025:**\n\n- Launched Webhooks API.\n    \n\n**Update 20 Feb 2025:**\n\n- Added API Token support.\n    \n\n**Update 12 Feb 2025:**\n\n- Introduced the List Variant API.\n    \n\n**Update 03 Dec 2024:**\n\n- Added two new \"get List of order detail\" APIs:\n    \n    - By list of order codes.\n        \n    - By list of partner order IDs.\n        \n\n**Update 11 Oct 2024:**\n\n- Deprecated the following order statuses:\n    \n    - ~~GROUPED~~\n        \n    - ~~WAIT_SHIPPING~~\n        \n    - ~~REJECT_REQUESTED~~\n        \n    - ~~REJECTED~~\n        \n- Introduced new order statuses:\n    \n    - REQUEST_CANCEL\n        \n    - CANCELED\n        \n    - REQUEST_REFUND\n        \n    - REFUNDED\n        \n- The get OrderDetail API now includes a refund_amount field for orders with statuses CANCELED or REFUNDED.\n    \n- Added a \"code\" field to API response json, indicating error status:\n    \n    - FLS-200: success\n        \n    - FLS-406: Insufficient balance – order created but payment is PENDING; seller must repay via the web admin panel.  \n        FLS-400: other error\n        \n\n(Refer to the API details below for more information).", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "31852094"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\" : \"testuser\",\r\n    \"password\" : \"testpassword\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_api_url}}/token", "description": "API requests to FlashShip use the <PERSON><PERSON> authentication scheme. To authenticate a request, provide the token in the Authorization header of the request:\n\n**`Authorization: Bearer access_token`**\n\nThe Login API returns an \"access_token\", which can then be used to make other API calls.\n\n### **Note**: you can use **API token** generated from the website as the access_token, without calling Login API. Each API token remains valid for one year."}, "response": [{"name": "Login Success", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\" : \"testuser\",\r\n    \"password\" : \"testpassword\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "https://devpod.flashship.net/seller-api-v2/token"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.18.0 (Ubuntu)"}, {"key": "Date", "value": "Tue, 05 Dec 2023 03:52:38 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "267"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Security-Policy", "value": "upgrade-insecure-requests"}], "cookie": [], "body": "{\n    \"code\":\"FLS_200\",\n    \"msg\": \"success\",\n    \"err\": null,\n    \"data\": {\n        \"access_token\": \"token\",\n        \"token_type\": \"Bearer\",\n        \"expires_in\": 18000\n    }\n}"}]}]}, {"name": "Orders", "item": [{"name": "get Variant list", "request": {"auth": {"type": "bearer", "bearer": {"token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwIiwiaWF0IjoxNzA1NjQ4Nzg5LCJleHAiOjE3MDU2NjY3ODl9.WeABlgodZx7TEHygqT4MrNpnDThtdrQvkcqB-ADldofWt0YSKNTkWF-qEo6Btz1YZgiRMEmDqfe3ul3w_LD6gw"}}, "method": "GET", "header": [], "url": "{{base_api_url}}/orders/list-variant-sku", "description": "Use this API to retrieve the list of variants.\n\nYou may also select a variant from the following link: [https://docs.google.com/spreadsheets/d/1uLofViVPDXeslEiwYg8zPc2_loeudjsb/edit](https://docs.google.com/spreadsheets/d/1uLofViVPDXeslEiwYg8zPc2_loeudjsb/edit)"}, "response": [{"name": "Variant list", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": "https://devpod.flashship.net/seller-api-v2/orders/list-variant-sku"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.18.0 (Ubuntu)"}, {"key": "Date", "value": "Tue, 05 Dec 2023 03:57:24 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "1862"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Security-Policy", "value": "upgrade-insecure-requests"}], "cookie": [], "body": "{\n    \"code\": \"FLS_200\",\n    \"msg\": \"success\",\n    \"data\": [\n        {\n            \"variant_id\": 12126,\n            \"product_type\": \"SHIRT\",\n            \"brand\": \"GILDAN\",\n            \"style\": \"G5000\",\n            \"size\": \"S\",\n            \"color\": \"BLACK\"\n        },\n        {\n            \"variant_id\": 12125,\n            \"product_type\": \"SHIRT\",\n            \"brand\": \"GILDAN\",\n            \"style\": \"G5000\",\n            \"size\": \"M\",\n            \"color\": \"BLACK\"\n        },\n        {\n            \"variant_id\": 84138,\n            \"product_type\": \"TANKTOP\",\n            \"brand\": \"GILDAN\",\n            \"style\": \"G2200\",\n            \"size\": \"3XL\",\n            \"color\": \"SPORTGREY\"\n        }\n    ],\n    \"err\": null,\n    \"expect_date\": null\n}"}]}, {"name": "Create Order", "request": {"auth": {"type": "bearer", "bearer": {"token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwIiwiaWF0IjoxNzA1NjQ4Nzg5LCJleHAiOjE3MDU2NjY3ODl9.WeABlgodZx7TEHygqT4MrNpnDThtdrQvkcqB-ADldofWt0YSKNTkWF-qEo6Btz1YZgiRMEmDqfe3ul3w_LD6gw"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"order_id\": \"order_id_0000001\", // if order_id is empty system will return you a random order_id, order_id is unique\r\n    \"buyer_first_name\": \"<PERSON>\", // required\r\n    \"buyer_last_name\": \"Sparrow\",\r\n    \"buyer_email\": \"<EMAIL>\",\r\n    \"buyer_phone\": \"766612489\",\r\n    \"buyer_address1\": \"18462 Edinbrook Ln\", // required\r\n    \"buyer_address2\": \"\",\r\n    \"buyer_city\": \"Westfield\", // required\r\n    \"buyer_province_code\": \"IN\", // required and use abbreviations for US state names\r\n    \"buyer_zip\": \"46074\", // required\r\n    \"buyer_country_code\": \"US\", // default value is US\r\n    \"shipment\": \"1\",\r\n    \"link_label\":null,\r\n    \"products\": [\r\n        {\r\n            \"variant_id\": 12128, // required - list variant in description\r\n            \"printer_design_front_url\": \"https://drive.google.com/file/d/XXXXXXXXXX/view?usp=sharing\", // Minimum requirement: must specify at least one print design image url for (front, back, right, left, neck, pocket, hood)\r\n            \"printer_design_back_url\": null,\r\n            \"printer_design_right_url\": null,\r\n            \"printer_design_left_url\": null,\r\n            \"printer_design_neck_url\": null,\r\n            \"printer_design_pocket_url\": null,\r\n            \"printer_design_hood_url\": null,\r\n            \"mockup_front_url\": \"https://drive.google.com/file/d/XXXXXXXXXX/view?usp=sharing\",\r\n            \"mockup_back_url\": null,\r\n            \"mockup_right_url\": null,\r\n            \"mockup_left_url\": null,\r\n            \"mockup_neck_url\": null,\r\n            \"mockup_pocket_url\": null,\r\n            \"mockup_hood_url\": null,\r\n            \"quantity\": 2, // required\r\n            \"note\": \"\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_api_url}}/orders/shirt-add", "description": "Use this API to create an order.\n\n**Request parameters:**\n\n| Param | **Data type** | **Required** | Description |\n| --- | --- | --- | --- |\n| order_id | String |  | Unique ID of the order from customer's system.  <br>If order_id is empty, FlashShip will use a unique random order_id. |\n| buyer_first_name | String  <br>Max length: 100 | Required | First name of buyer |\n| buyer_last_name | String  <br>Max length: 100 |  | Last name of buyer |\n| buyer_email | String  <br>Max length: 100 |  | Buyer's email address |\n| buyer_phone | String  <br>Max length: 15 |  | Buyer's phone number |\n| buyer_address1 | String  <br>Max length: 200 | Required |  |\n| buyer_address2 | String  <br>Max length: 200 |  |  |\n| buyer_city | String  <br>Max length: 50 | Required |  |\n| buyer_province_code | String  <br>Length: 2 | Required | Use abbreviations for US state names  <br>[https://about.usps.com/who/profile/history/state-abbreviations.htm](https://about.usps.com/who/profile/history/state-abbreviations.htm) |\n| buyer_zip | String  <br>Length: 4-12 | Required | Zipcode of buyer |\n| buyer_country_code | String  <br>Max length: 20 | Required | default value is **US** |\n| shipment | Number | Required | 1: FirstClass  <br>2: Priority  <br>3: RushProduction  <br>4: OverNight  <br>6: Expedite |\n| products\\[\\].variant_id | Number | Required | Variant ID |\n| products\\[\\].printer_design_front_url | String | Must be a URL for one of supported print areas (front, back, right, left, neck, pocket, hood) | URL of front design image |\n| products\\[\\].printer_design_back_url | String | Must be a URL for one of supported print areas (front, back, right, left, neck, pocket, hood) | URL of back design image |\n| products\\[\\].printer_design_right_url | String | Must be a URL for one of supported print areas (front, back, right, left, neck, pocket, hood) | URL of right hand design image |\n| products\\[\\].printer_design_left_url | String | Must be a URL for one of supported print areas (front, back, right, left, neck, pocket, hood) | URL of left hand design image |\n| products\\[\\].printer_design_neck_url | String | Must be a URL for one of supported print areas (front, back, right, left, neck, pocket, hood)l | URL of neck design image |\n| products\\[\\].printer_design_pocket_url | String | Must be a URL for one of supported print areas (front, back, right, left, neck, pocket, hood) | URL of pocket design image |\n| products\\[\\].printer_design_hood_url | String | Must be a URL for one of supported print areas (front, back, right, left, neck, pocket, hood) | URL of hood design image |\n| products\\[\\].mockup_front_url | String |  | URL of front mockup image |\n| products\\[\\].mockup_back_url | String |  | URL of back mockup image |\n| products\\[\\].mockup_right_url | String |  | URL of right hand mockup image |\n| products\\[\\].mockup_left_url | String |  | URL of left hand mockup image |\n| products\\[\\].mockup_neck_url | String |  | URL of neck mockup image |\n| products\\[\\].mockup_pocket_url | String |  | URL of pocket mockup image |\n| products\\[\\].mockup_hood_url | String |  | URL of hood mockup image |\n| products\\[\\].quantity | Number | Required | Total quantity of product |\n| products\\[\\].special_print | Number |  | 1: special print |\n| products\\[\\].note | String |  |  |\n| link_label | String | Optional | Link of label's PDF file (if using seller's label) |\n\n**Response parameters**:\n\n| Param | **Data type** | **Required** | Description |\n| --- | --- | --- | --- |\n| code | String |  | Error code |\n| msg | String |  | success / fail |\n| data | String |  | order_code of the order. |\n| error | String |  | description if error |"}, "response": [{"name": "Create Order Success", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"order_id\": \"\",\r\n    \"buyer_first_name\": \"<PERSON>\",\r\n    \"buyer_last_name\": \"Sparrow\",\r\n    \"buyer_email\": \"<EMAIL>\",\r\n    \"buyer_phone\": \"766612489\",\r\n    \"buyer_address1\": \"18462 Edinbrook Ln\",\r\n    \"buyer_address2\": \"\",\r\n    \"buyer_city\": \"Westfield\",\r\n    \"buyer_province_code\": \"IN\",\r\n    \"buyer_zip\": \"46074\",\r\n    \"buyer_country_code\": \"US\",\r\n    \"shipment\": \"1\",\r\n    \"link_label\":null,\r\n    \"products\": [\r\n        {\r\n            \"variant_id\": 12128,\r\n            \"printer_design_front_url\": \"https://drive.google.com/file/d/XXXXXXXXXX/view?usp=sharing\",// Minimum requirement must have one URL (front ,back,right,left,neck)\r\n            \"printer_design_back_url\": null,// Minimum requirement must have one URL (front ,back,right,left,neck)\r\n            \"printer_design_right_url\": null,// Minimum requirement must have one URL (front ,back,right,left,neck)\r\n            \"printer_design_left_url\": null,// Minimum requirement must have one URL (front ,back,right,left,neck)\r\n            \"printer_design_neck_url\": null,// Minimum requirement must have one URL (front ,back,right,left,neck)\r\n            \"mockup_front_url\": \"https://drive.google.com/file/d/XXXXXXXXXX/view?usp=sharing\",\r\n            \"mockup_back_url\": null,\r\n            \"mockup_right_url\": null,\r\n            \"mockup_left_url\": null,\r\n            \"mockup_neck_url\": null,\r\n            \"quantity\": 2,\r\n            \"special_print\": 1,\r\n            \"note\": \"\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "https://devpod.flashship.net/seller-api-v2/orders/shirt-add"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.18.0 (Ubuntu)"}, {"key": "Date", "value": "Tue, 05 Dec 2023 03:59:04 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "47"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Security-Policy", "value": "upgrade-insecure-requests"}], "cookie": [], "body": "{\n    \"code\":\"FLS_200\",\n    \"msg\": \"success\",\n    \"data\": \"KPOWDPRCA\",\n    \"err\": null\n}"}]}, {"name": "Cancel Order", "request": {"auth": {"type": "bearer", "bearer": {"token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwIiwiaWF0IjoxNzA1NjQ4Nzg5LCJleHAiOjE3MDU2NjY3ODl9.WeABlgodZx7TEHygqT4MrNpnDThtdrQvkcqB-ADldofWt0YSKNTkWF-qEo6Btz1YZgiRMEmDqfe3ul3w_LD6gw"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"order_code_list\": [\r\n        \"GSC4HUQ1B\" // required\r\n    ],\r\n    \"reject_note\": \"wrong design\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_api_url}}/orders/seller-reject", "description": "Cancel an order by providing order_code, which returned by FlashShip after calling shirt-add API.\n\nCurrent status of order must not in \\[\"COMPLETED\"\\]."}, "response": [{"name": "Cancel Order Success", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"order_code_list\": [\r\n        \"GSC4HUQ1B\"\r\n    ],\r\n    \"reject_note\": \"wrong design\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "https://devpod.flashship.net/seller-api-v2/orders/seller-reject"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.18.0 (Ubuntu)"}, {"key": "Date", "value": "Tue, 05 Dec 2023 03:56:48 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "118"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Security-Policy", "value": "upgrade-insecure-requests"}], "cookie": [], "body": "{\n    \"code\":\"FLS_200\",\n    \"msg\": \"success\",\n    \"data\": {\n        \"order_id_list\": [\n            233292\n        ],\n        \"order_code_list\": [\n            \"GSC4HUQ1B\"\n        ],\n        \"reject_note\": \"wrong design\"\n    },\n    \"err\": null\n}"}]}, {"name": "get Order Detail by order_code", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwIiwiaWF0IjoxNzA1NjQ4Nzg5LCJleHAiOjE3MDU2NjY3ODl9.WeABlgodZx7TEHygqT4MrNpnDThtdrQvkcqB-ADldofWt0YSKNTkWF-qEo6Btz1YZgiRMEmDqfe3ul3w_LD6gw"}}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{base_api_url}}/orders/{{order_code}}", "description": "Get order detail using FlashShip's order_code.\n\nOutput:\n\n| Param | **Data type** | Description |\n| --- | --- | --- |\n| order_code | String |  |\n| partner_order_id | String | Your order id |\n| shipment_method | Number | shipment method:  <br>1: FirstClass  <br>2: Priority  <br>3: RushProduction  <br>4: OverNight  <br>6: Expedite |\n| first_name | String | Customer's first name |\n| last_name | String | Customer's last name |\n| email | String | Customer's email |\n| phone | String | Customer's phone |\n| province | String | Customer's province code |\n| country | String | Customer's country code |\n| region | String | Customer's region code |\n| address_line_1 | String | Customer's address 1 |\n| address_line_2 | String | Customer's address 2 |\n| city | String | Customer's city |\n| zip | String | Customer'szip code |\n| note | String | Note |\n| status | String | Order status |\n| created | Timestamp |  |\n| quantity | Number | Total quantity of product |\n| tracking_number | String |  |\n| reject_user | String | Rejected by user |\n| reject_note | String | Reject reason |\n| reject_type | String | Rejected by one of types below:  <br>**SELLER, ADMIN, FACTORY** |\n| reject_request_date | Timestamp | Reject request datetime |\n| reject_confirm_date | Timestamp | Reject confirm datetime |\n| total_fee | Float |  |\n| refund_amount | Float | <u><b>Added from 11 Oct 2024: </b></u> refund amount if order status is CANCELED, REFUNDED |\n| carrier | String | Carrier name |\n| products\\[\\].variant_id | Number |  |\n| products\\[\\].variant_sku | String |  |\n| products\\[\\].front_print_url | String | URL of front design image |\n| products\\[\\].back_print_url | String | URL of back design image |\n| products\\[\\].left_print_url | String | URL of left hand design image |\n| products\\[\\].right_print_url | String | URL of right hand design image |\n| products\\[\\].neck_print_url | String | URL of neck design image |\n| products\\[\\].pocket_print_url | String | URL of pocket design image |\n| products\\[\\].hood_print_url | String | URL of hood design image |\n| products\\[\\].mockup_front | String | URL of front mockup image |\n| products\\[\\].mockup_back | String | URL of back mockup image |\n| products\\[\\].mockup_right | String | URL of right hand mockup image |\n| products\\[\\].mockup_left | String | URL of left hand mockup image |\n| products\\[\\].mockup_neck | String | URL of neck mockup image |\n| products\\[\\].mockup_pocket | String | URL of pocket mockup image |\n| products\\[\\].mockup_hood | String | URL of hood mockup image |\n| products\\[\\].quantity | Number | Quantity of product |\n| products\\[\\].note | String |  |\n| products\\[\\].product_type_enum | String | Type of product  <br>SHIRT, HOODIE, SWEATSHIRT... |"}, "response": [{"name": "Detail Order", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": "https://devpod.flashship.net/seller-api-v2/orders/KXILCU5MK"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.18.0 (Ubuntu)"}, {"key": "Date", "value": "Tue, 05 Dec 2023 03:57:24 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "1862"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Security-Policy", "value": "upgrade-insecure-requests"}], "cookie": [], "body": "{\n    \"order_code\": \"MWFT8V123\",    \n    \"partner_order_id\": \"#123456\",\n    \"shipment_method\": 1,\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"Sparrow\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"\",\n    \"province\": \"AZ\",\n    \"country\": \"US\",\n    \"region\": \"AZ\",\n    \"address_line_1\": \"6445 McCauley Ter\",\n    \"address_line_2\": \"\",\n    \"city\": \"EDINA\",\n    \"zip\": \"55439\",\n    \"note\": null,\n    \"status\": \"CONFIRMED\",\n    \"created\": \"2024-02-14T14:13:29+07:00\",\n    \"quantity\": 1,\n    \"tracking_number\": null,\n    \"reject_user\": null,\n    \"reject_note\": null,\n    \"reject_type\": null,\n    \"reject_request_date\": null,\n    \"reject_confirm_date\": null,\n    \"total_fee\": 10.99,\n    \"carrier\": null,   \n    \"products\": [\n        {\n            \"variant_id\": 12101,\n            \"variant_sku\": \"M/WHITE\",\n            \"front_print_url\": \"https://drive.google.com/file/d/XXXXXXXXXX/view?usp=sharing\",\n            \"back_print_url\": null,\n            \"left_print_url\": null,\n            \"right_print_url\": null,\n            \"neck_print_url\": null,\n            \"pocket_print_url\": null,\n            \"hood_print_url\": null,\n            \"mockup_front\": \"https://drive.google.com/file/d/XXXXXXXXXX/view?usp=sharing\",\n            \"mockup_back\": null,            \n            \"mockup_right\": null,\n            \"mockup_left\": null,            \n            \"mockup_neck\": null,\n            \"mockup_pocket\": null,\n            \"mockup_hood\": null,\n            \"quantity\": 1,\n            \"note\": \"\",\n            \"product_type_enum\": \"SHIRT\"\n        }\n    ]    \n}"}]}, {"name": "get Order Detail by partner_order_id", "request": {"auth": {"type": "bearer", "bearer": {"token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwIiwiaWF0IjoxNzA1NjQ4Nzg5LCJleHAiOjE3MDU2NjY3ODl9.WeABlgodZx7TEHygqT4MrNpnDThtdrQvkcqB-ADldofWt0YSKNTkWF-qEo6Btz1YZgiRMEmDqfe3ul3w_LD6gw"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"partner_order_id\": \"#123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_api_url}}/orders/partner-order-id", "description": "Get order detail by partner_order_id.\n\nInput: partner_order_id.\n\nOutput: list order detail informations."}, "response": [{"name": "Detail Order Success", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"partner_order_id\": \"#123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "https://devpod.flashship.net/seller-api-v2/orders/partner-order-id"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.18.0 (Ubuntu)"}, {"key": "Date", "value": "Tue, 05 Dec 2023 03:56:48 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "118"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Security-Policy", "value": "upgrade-insecure-requests"}], "cookie": [], "body": "{\n    \"order_code\": \"MWFT8V123\",    \n    \"partner_order_id\": \"#123456\",\n    \"shipment_method\": 1,\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"Sparrow\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"\",\n    \"province\": \"AZ\",\n    \"country\": \"US\",\n    \"region\": \"AZ\",\n    \"address_line_1\": \"6445 McCauley Ter\",\n    \"address_line_2\": \"\",\n    \"city\": \"EDINA\",\n    \"zip\": \"55439\",\n    \"note\": null,\n    \"status\": \"CONFIRMED\",\n    \"created\": \"2024-02-14T14:13:29+07:00\",\n    \"quantity\": 1,\n    \"tracking_number\": null,\n    \"reject_user\": null,\n    \"reject_note\": null,\n    \"reject_type\": null,\n    \"reject_request_date\": null,\n    \"reject_confirm_date\": null,\n    \"total_fee\": 10.99,\n    \"carrier\": null,   \n    \"products\": [\n        {\n            \"variant_id\": 12101,\n            \"variant_sku\": \"M/WHITE\",\n            \"front_print_url\": \"https://drive.google.com/file/d/XXXXXXXXXX/view?usp=sharing\",\n            \"back_print_url\": null,\n            \"left_print_url\": null,\n            \"right_print_url\": null,\n            \"neck_print_url\": null,\n            \"pocket_print_url\": null,\n            \"hood_print_url\": null,\n            \"mockup_front\": \"https://drive.google.com/file/d/XXXXXXXXXX/view?usp=sharing\",\n            \"mockup_back\": null,            \n            \"mockup_right\": null,\n            \"mockup_left\": null,            \n            \"mockup_neck\": null,\n            \"mockup_pocket\": null,\n            \"quantity\": 1,\n            \"note\": \"\",\n            \"product_type_enum\": \"SHIRT\"\n        }\n    ]    \n}"}]}, {"name": "get List of Order Details by Partner Order Ids", "request": {"auth": {"type": "bearer", "bearer": {"token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwIiwiaWF0IjoxNzA1NjQ4Nzg5LCJleHAiOjE3MDU2NjY3ODl9.WeABlgodZx7TEHygqT4MrNpnDThtdrQvkcqB-ADldofWt0YSKNTkWF-qEo6Btz1YZgiRMEmDqfe3ul3w_LD6gw"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"list_partner_order_id\": [\r\n        \"#123456\",\r\n        \"#123456\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_api_url}}/orders/list-partner-order-id", "description": "Get List of order details by Partner Order Ids, maximum 20 orders per request.\n\nInput: list_partner_order_id\n\nOutput: order detail informations."}, "response": []}, {"name": "get List of Order Details by Order Codes", "request": {"auth": {"type": "bearer", "bearer": {"token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwIiwiaWF0IjoxNzA1NjQ4Nzg5LCJleHAiOjE3MDU2NjY3ODl9.WeABlgodZx7TEHygqT4MrNpnDThtdrQvkcqB-ADldofWt0YSKNTkWF-qEo6Btz1YZgiRMEmDqfe3ul3w_LD6gw"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"list_order_code\": [\r\n        \"#123456\",\r\n        \"#123456\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_api_url}}/orders/list-order-code", "description": "Get List order details by order codes, maximum 20 orders per request.\n\nInput: list_order_code.\n\nOutput: order detail informations."}, "response": []}], "description": "The Orders API allows you to create new orders and retreive order information.\n\n## Order status:\n\n  \nEach order will go through different states while being processed. The following order status types indicate those states:\n\n| Status | Description |\n| --- | --- |\n| **CONFIRMED** | Order has been created and confirmed. |\n| **IN_PRODUCING** | The order is being processed. |\n| **COMPLETED** | Production completed |\n| **HOLD** | Order has problem in producing progress: wrong design, invalid customer address... |\n| **REQUEST_CANCEL** | Se<PERSON> requested to cancel the order. |\n| **CANCELED** | Order has been cancelled. |\n| **REQUEST_REFUND** | Refund request waiting for approval. |\n| **REFUNDED** | Order has been refunded. |"}, {"name": "Webhook", "item": [{"name": "Order created", "request": {"method": "POST", "header": [{"key": "x-signature", "value": "abcdef", "type": "text"}, {"key": "type", "value": "order:created", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"type\": \"order:created\",\r\n\t\"created_at\": \"2025-04-16T12:50:11.615120283+07:00\",\r\n\t\"resource\": {\r\n\t\t\"partner_order_id\": \"partner-id-12345\",\r\n\t\t\"order_code\": \"ABCDEFGHI\",\r\n\t\t\"status\": \"CONFIRMED\",\r\n\t\t\"payment_status\": \"INIT\",\r\n\t\t\"tracking_status\": null,\r\n\t\t\"tracking_number\": null,\r\n\t\t\"quantity\": 2,\r\n\t\t\"total_fee\": 20\r\n\t}\r\n}", "options": {"raw": {"language": "json"}}}, "url": "https://partner-domain.com/webhook-url"}, "response": []}, {"name": "Order status updated", "request": {"method": "POST", "header": [{"key": "x-signature", "value": "abcdef", "type": "text"}, {"key": "type", "value": "order:status:updated", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"type\": \"order:status:updated\",\r\n\t\"created_at\": \"2025-04-16T12:50:11.615120283+07:00\",\r\n\t\"resource\": {\r\n\t\t\"partner_order_id\": \"partner-id-12345\",\r\n\t\t\"order_code\": \"ABCDEFGHI\",\r\n\t\t\"status\": \"COMPLETED\"\r\n\t}\r\n}", "options": {"raw": {"language": "json"}}}, "url": "https://partner-domain.com/webhook-url"}, "response": []}, {"name": "Order shipment created", "request": {"method": "POST", "header": [{"key": "x-signature", "value": "abcdef", "type": "text"}, {"key": "type", "value": "order:shipment:created", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"type\": \"order:shipment:created\",\r\n\t\"created_at\": \"2025-04-16T12:50:11.615120283+07:00\",\r\n\t\"resource\": {\r\n\t\t\"partner_order_id\": \"partner-id-12345\",\r\n\t\t\"order_code\": \"ABCDEFGHI\",\r\n\t\t\"shipping_carrier\": \"USPS\",\r\n\t\t\"tracking_number\": \"1234567890123456789012\"\r\n\t}\r\n}", "options": {"raw": {"language": "json"}}}, "url": "https://partner-domain.com/webhook-url"}, "response": []}, {"name": "Order shipment status changed", "request": {"method": "POST", "header": [{"key": "x-signature", "value": "abcdef", "type": "text"}, {"key": "type", "value": "order:shipment:status", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"type\": \"order:shipment:status\",\r\n\t\"created_at\": \"2025-04-16T12:50:11.615120283+07:00\",\r\n\t\"resource\": {\r\n\t\t\"partner_order_id\": \"partner-id-12345\",\r\n\t\t\"order_code\": \"ABCDEFGHI\",\r\n\t\t\"tracking_status\": \"Delivered\",\r\n\t\t\"tracking_number\": \"1234567890123456789012\"\r\n\t}\r\n}", "options": {"raw": {"language": "json"}}}, "url": "https://partner-domain.com/webhook-url"}, "response": []}, {"name": "Order payment pending", "request": {"method": "POST", "header": [{"key": "x-signature", "value": "abcdef", "type": "text"}, {"key": "type", "value": "order:payment:pending", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"type\": \"order:payment:pending\",\r\n\t\"created_at\": \"2025-04-23T15:40:27.829409256+07:00\",\r\n\t\"resource\": {\r\n\t\t\"partner_order_id\": \"partner-id-12345\",\r\n\t\t\"order_code\": \"ABCDEFGHI\",\r\n\t\t\"payment_status\": \"PENDING\"\r\n\t}\r\n}", "options": {"raw": {"language": "json"}}}, "url": "https://partner-domain.com/webhook-url"}, "response": []}], "description": "Using Webhooks allows you to receive notifications about certain events, i.e an order is created.\n\nWhen an event occurs, FlashShip server will make a POST request to your defined URL that will contain a JSON object in the request body. Your server has to respond with HTTP status `200 OK`, otherwise, the request will be retried 4 times, each after 5 minutes interval. After the 4th time, the message will be discarded. Continued webhook delivery failures may result in temporarily disabling your webhooks for 1 hour.\n\n**Signature**:\n\nx-signature on a webhook's headers can be used to verify that a webhook came from FlashS<PERSON>, calculated using HMAC SHA-256 algorithm with the request json payload and the webhook secret:\n\n> x-signature = HmacSHA256(payload_json, webhook_secret) \n  \n\n**Webhook event types:**  \n<PERSON><PERSON> can use value of \"type\" in request header to identify type of webhook events. FlashShip will deliver the following webhook events:\n\n1. order:created - a new order has been created.\n    \n2. order:status:updated - the status of an order has changed, for example from IN-PRODUCING to COMPLETED.\n    \n3. order:shipment:created - when FlashShip retreives tracking_number from your link_label url, or when a shipping label is created, FlashShip will send the tracking number.\n    \n4. order:shipment:status - when the delivery status of the package changes: Pre-Shipment, In Transit, Delivered\n    \n5. order:payment:pending - payment status of the order has changed to PENDING.\n    \n\nTo set up webhooks, use API requests described below:"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "username", "value": "test", "type": "string"}, {"key": "password", "value": "@Test123", "type": "string"}, {"key": "order_code", "value": "KXILCU5MK"}]}