<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\Column;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Enums\TiktokShopStatus;

class StoreMetrics extends Column
{
    protected string $view = 'tables.columns.store-metrics';

    public function getState(): array
    {
        $record = $this->getRecord();
        $user = Auth::user();
        $lastSync = $record->last_sync_tiktok;

        // Tính số ngày kể từ lần đồng bộ cuối cùng
        $daysSinceSync = $lastSync ? now()->diffInDays($lastSync) : null;

        // Lấy trực tiếp status vì đã là enum
        $status = $record->tiktok_shop_status;

        // Lấy app_name từ PartnerApp
        $appName = $record->partnerApp?->app_name ?? 'N/A';

        // Xử lý hiển thị bank info dựa trên role
        $bankInfo = $this->getBankInfo($record, $user);

        // Lấy thông tin orders và payments
        $ordersCount = $record->orders()->count();
        $paidOrdersCount = $record->orders()
            ->where('settlement_amount', '>', 0)
            ->count();

        // Lấy tổng payment từ cả 2 bảng
        $currentMonth = now()->format('Y-m');

        // Tính tiền fulfil tháng này từ supplier orders (loại bỏ đơn cancelled)
        $fulfilTotal = \App\Models\SupplierOrder::whereHas('order', function($query) use ($record) {
                $query->where('store_id', $record->id)
                      ->where('status', '!=', 'Cancelled');
            })
            ->where('status', '!=', 'Cancelled')
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->sum('base_cost');

        $payoutTotal = $record->payout()
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->sum('amount');

        return [
            'shop' => [
                'name' => Str::limit($record->tiktok_shop_name, 20, '...'),
                'code' => $record->tiktok_shop_code,
                'app_name' => $appName,
                'status' => [
                    'label' => $status?->getLabel() ?? 'Unknown',
                    'color' => $status?->getColor() ?? 'gray',
                    'icon' => $status?->getIcon(),
                ],
                'last_sync' => $lastSync,
                'last_sync_human' => $lastSync ? Carbon::parse($lastSync)->diffForHumans() : 'Never',
                'days_since_sync' => $daysSinceSync,
            ],
            'bank' => $bankInfo,
            'metrics' => [
                'products' => [
                    'count' => $record->tiktok_product_count ?? 0,
                    'last_created' => $record->last_created_tiktok_product,
                    'last_created_human' => $record->last_created_tiktok_product
                        ? Carbon::parse($record->last_created_tiktok_product)->diffForHumans()
                        : null,
                ],
                'orders' => [
                    'total' => $ordersCount,
                    'paid' => $paidOrdersCount,
                ],
                'payments' => [
                    'fulfil' => number_format($fulfilTotal, 2),
                    'payout' => number_format($payoutTotal, 2),
                    'total' => number_format($fulfilTotal + $payoutTotal, 2),
                ],
                'health' => $record->shop_health ?? '0/48',
                'payout' => [
                    'days' => $record->tiktok_payout_day,
                    'on_hold' => $record->tiktok_payout_on_hold,
                ],
                'violation' => [
                    'score' => $record->violation_score ?? 0,
                ],
            ],
        ];
    }

    private function getBankInfo($record, $user): array
    {
        $canViewFullInfo = $user->hasRole(['super_admin', 'Leader']);

        if (!$record->bank_account && !$record->card) {
            return [
                'status' => 'missing',
                'message' => 'No bank',
                'color' => 'danger',
                'details' => null
            ];
        }

        if (!$record->bank_account || !$record->card) {
            $details = $canViewFullInfo ? [
                'bank' => $record->bank_account,
                'card' => $record->card
            ] : null;

            return [
                'status' => 'incomplete',
                'message' => 'Incomplete',
                'color' => 'warning',
                'details' => $details
            ];
        }

        $lastFourBank = substr($record->bank_account, -4);
        $lastFourCard = substr($record->card, -4);

        if ($canViewFullInfo) {
            $details = [
                'bank' => $record->bank_account,
                'card' => $record->card
            ];
        } else {
            $details = [
                'bank' => "****{$lastFourBank}",
                'card' => $record->card
            ];
        }

        if ($lastFourBank === $lastFourCard) {
            return [
                'status' => 'matched',
                'message' => 'Verified',
                'color' => 'success',
                'details' => $details
            ];
        }

        return [
            'status' => 'mismatch',
            'message' => 'Mismatch',
            'color' => 'danger',
            'details' => $details
        ];
    }

    private function getSyncStatusColor(?int $daysDiff): string
    {
        if (!$daysDiff) return 'gray';
        if ($daysDiff <= 3) return 'success';
        if ($daysDiff <= 5) return 'warning';
        return 'danger';
    }

    private function getSyncStatusIcon(?int $daysDiff): string
    {
        if (!$daysDiff) return 'heroicon-m-clock';
        if ($daysDiff <= 3) return 'heroicon-m-check-circle';
        if ($daysDiff <= 5) return 'heroicon-m-exclamation-circle';
        return 'heroicon-m-x-circle';
    }
}
