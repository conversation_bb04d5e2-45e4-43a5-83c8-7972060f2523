<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\Column;

class MetricsSlider extends Column
{
    protected string $view = 'tables.columns.metrics-slider';

    public function getState(): array
    {
        $state = parent::getState();
        if (is_null($state)) {
            return [];
        }
        if (is_string($state)) {
            $state = json_decode($state, true);
        }
        return $state ?? [];
    }
}