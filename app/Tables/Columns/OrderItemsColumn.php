<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\Column;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;

class OrderItemsColumn extends Column
{
    protected string $view = 'tables.columns.order-items-column';

    public static function make(?string $name = null): static
    {
        $static = app(static::class, ['name' => $name ?? 'orderItems']);
        $static->configure();

        return $static;
    }

    public function configure(): static
    {
        $this
            ->label('Order Items')
            ->searchable(query: function ($query, string $search) {
                // Kiểm tra xem đây là Order hay SupplierOrder
                $model = $query->getModel();

                if ($model instanceof \App\Models\Order) {
                    // Nếu là Order, tìm kiếm trực tiếp
                    return $query->where('order_code', 'like', "%{$search}%")
                                 ->orWhere('order_number', 'like', "%{$search}%");
                } else {
                    // Nếu là SupplierOrder, tìm kiếm thông qua relationship
                    return $query->whereHas('order', function ($query) use ($search) {
                        $query->where('order_code', 'like', "%{$search}%")
                              ->orWhere('order_number', 'like', "%{$search}%");
                    });
                }
            })
            ->sortable(false);

        return $this;
    }
}
