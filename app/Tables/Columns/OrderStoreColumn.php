<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\Column;
use Illuminate\Support\Str;

class OrderStoreColumn extends Column
{
    protected string $view = 'filament.tables.columns.order.store';

    public static function make(?string $name = null): static
    {
        $static = app(static::class, ['name' => $name ?? 'store']);
        $static->configure();

        return $static;
    }

    public function configure(): static
    {
        $this
            ->label('Store')
            ->alignCenter()
            ->sortable()
            ->searchable(query: function ($query, string $search) {
                return $query->whereHas('store', function ($query) use ($search) {
                    $query->where('name', 'like', "%{$search}%")
                          ->orWhere('tiktok_shop_name', 'like', "%{$search}%")
                          ->orWhere('tiktok_shop_code', 'like', "%{$search}%");
                });
            });

        return $this;
    }
}
