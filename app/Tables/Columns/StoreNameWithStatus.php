<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\Column;
use Illuminate\Support\Str;

class StoreNameWithStatus extends Column
{
    protected string $view = 'tables.columns.store-name-with-status';

    public function getState(): array
    {
        $record = $this->getRecord();
        
        // Lấy thông tin store
        $storeName = $record->name;
        $ownerName = $record->seller?->name;
        $storeStatus = $record->status; // Active/InActive
        $tiktokShopStatus = $record->tiktok_shop_status; // Live/NotConnected/Suspended
        
        // Format owner name
        $formattedOwner = 'No Owner';
        if ($ownerName) {
            $formattedOwner = strlen($ownerName) > 30 
                ? '...' . Str::substr($ownerName, -30)
                : $ownerName;
        }
        
        // Xác định màu cho store status
        $storeStatusColor = match($storeStatus) {
            'Active' => 'success',
            'InActive' => 'warning',
            default => 'gray'
        };
        
        // Xác định màu cho TikTok status
        $tiktokStatusColor = match($tiktokShopStatus?->value ?? 'unknown') {
            '2' => 'success',    // Live
            '-1' => 'warning',   // Not Connected
            '3' => 'danger',     // Suspended
            default => 'gray'
        };
        
        $tiktokStatusLabel = match($tiktokShopStatus?->value ?? 'unknown') {
            '2' => 'Live',
            '-1' => 'Not Connected',
            '3' => 'Suspended',
            default => 'Unknown'
        };
        
        return [
            'store_name' => $storeName,
            'owner_name' => $formattedOwner,
            'store_status' => $storeStatus,
            'store_status_color' => $storeStatusColor,
            'tiktok_status' => $tiktokStatusLabel,
            'tiktok_status_color' => $tiktokStatusColor,
        ];
    }

    public static function make(string $name = 'store_name_with_status'): static
    {
        return parent::make($name);
    }
}
