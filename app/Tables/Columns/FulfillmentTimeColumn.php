<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\Column;
use App\Models\SupplierOrder;
use Illuminate\Database\Eloquent\Builder;

class FulfillmentTimeColumn extends Column
{
    protected string $view = 'filament.tables.columns.order.fulfillment-time';

    public static function make(?string $name = null): static
    {
        $static = app(static::class, ['name' => $name ?? 'fulfillment_time']);
        $static->configure();

        return $static;
    }

    public function configure(): static
    {
        $this
            ->label('Thời gian xử lý')
            ->alignCenter()
            ->width('280px')
            ->sortable(
                query: function (Builder $query, string $direction): Builder {
                    return $query->orderBy(
                        SupplierOrder::select('created_at')
                            ->whereColumn('supplier_orders.order_id', 'orders.id')
                            ->latest()
                            ->take(1),
                        $direction
                    );
                }
            )
            ->searchable(false);

        return $this;
    }
}
