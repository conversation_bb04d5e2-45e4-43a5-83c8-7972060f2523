<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\Column;

class OrderFulfillmentColumn extends Column
{
    protected string $view = 'filament.tables.columns.order.fulfillment';

    public static function make(?string $name = null): static
    {
        $static = app(static::class, ['name' => $name ?? 'fulfillment']);
        $static->configure();

        return $static;
    }

    public function configure(): static
    {
        $this
            ->label('Fulfillment')
            ->alignCenter();

        return $this;
    }
}
