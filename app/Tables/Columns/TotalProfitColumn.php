<?php

namespace App\Tables\Columns;

use Filament\Tables\Columns\Column;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class TotalProfitColumn extends Column
{
    protected string $view = 'filament.tables.columns.order.total-profit';

    public static function make(?string $name = null): static
    {
        $static = app(static::class, ['name' => $name ?? 'settlement_amount']);
        $static->configure();

        return $static;
    }

    public function configure(): static
    {
        $this
            ->label('Total / Profit')
            ->alignCenter()
            ->searchable(['order_number', 'order_code'])
            ->sortable(query: function (Builder $query, string $direction): Builder {
                return $query->orderBy(
                    DB::raw('COALESCE(settlement_amount, total)'), 
                    $direction
                );
            });

        return $this;
    }
}
