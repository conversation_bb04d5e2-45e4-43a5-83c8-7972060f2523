<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Carbon\Carbon;

class LosAngelesDateTime implements CastsAttributes
{
    public function get($model, string $key, $value, array $attributes)
    {
        return $value ? Carbon::parse($value)->setTimezone('America/Los_Angeles') : null;
    }

    public function set($model, string $key, $value, array $attributes)
    {
        return $value ? Carbon::parse($value)->setTimezone('UTC') : null;
    }
} 