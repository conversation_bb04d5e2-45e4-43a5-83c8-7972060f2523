<?php

namespace App\Utilities;

class CountryCode
{
    /**
     * A static array of country names to their codes.
     *
     * @var array
     */
    protected static $countryCodes = [
        "United States" => "US",
        "Canada" => "CA",
        "United Kingdom" => "GB",
        "Australia" => "AU",
        "Germany" => "DE",
        "France" => "FR",
        "Spain" => "ES",
        "Italy" => "IT",
        "Russia" => "RU",
        "China" => "CN",
        "Japan" => "JP",
        "South Korea" => "KR",
        "India" => "IN",
        "Brazil" => "BR",
        "Mexico" => "MX",
        "South Africa" => "ZA",
        "Nigeria" => "NG",
        "Egypt" => "EG",
        "Saudi Arabia" => "SA",
        "United Arab Emirates" => "AE",
        "Singapore" => "SG",
        "Malaysia" => "MY",
        "Indonesia" => "ID",
        "Philippines" => "PH",
        "Pakistan" => "PK",
        "Bangladesh" => "BD",
        "Vietnam" => "VN",
        "Thailand" => "TH",
        "New Zealand" => "NZ",
        "Netherlands" => "NL",
        "Belgium" => "BE",
        "Switzerland" => "CH",
        "Sweden" => "SE",
        "Norway" => "NO",
        "Poland" => "PL",
        "Austria" => "AT",
        "Greece" => "GR",
        "Portugal" => "PT",
        "Denmark" => "DK",
        "Finland" => "FI",
        "Ireland" => "IE",
        "Czech Republic" => "CZ",
        "Romania" => "RO",
        "Hungary" => "HU",
        "Turkey" => "TR",
        "Iran" => "IR",
        "Israel" => "IL",
        "South Africa" => "ZA",
        // Bạn có thể tiếp tục thêm các quốc gia khác
    ];
    protected static $stateUs = [
        'Alabama' => 'AL',
        'Alaska' => 'AK',
        'Arizona' => 'AZ',
        'Arkansas' => 'AR',
        'California' => 'CA',
        'Colorado' => 'CO',
        'Connecticut' => 'CT',
        'Delaware' => 'DE',
        'Florida' => 'FL',
        'Georgia' => 'GA',
        'Hawaii' => 'HI',
        'Idaho' => 'ID',
        'Illinois' => 'IL',
        'Indiana' => 'IN',
        'Iowa' => 'IA',
        'Kansas' => 'KS',
        'Kentucky' => 'KY',
        'Louisiana' => 'LA',
        'Maine' => 'ME',
        'Maryland' => 'MD',
        'Massachusetts' => 'MA',
        'Michigan' => 'MI',
        'Minnesota' => 'MN',
        'Mississippi' => 'MS',
        'Missouri' => 'MO',
        'Montana' => 'MT',
        'Nebraska' => 'NE',
        'Nevada' => 'NV',
        'New Hampshire' => 'NH',
        'New Jersey' => 'NJ',
        'New Mexico' => 'NM',
        'New York' => 'NY',
        'North Carolina' => 'NC',
        'North Dakota' => 'ND',
        'Ohio' => 'OH',
        'Oklahoma' => 'OK',
        'Oregon' => 'OR',
        'Pennsylvania' => 'PA',
        'Rhode Island' => 'RI',
        'South Carolina' => 'SC',
        'South Dakota' => 'SD',
        'Tennessee' => 'TN',
        'Texas' => 'TX',
        'Utah' => 'UT',
        'Vermont' => 'VT',
        'Virginia' => 'VA',
        'Washington' => 'WA',
        'West Virginia' => 'WV',
        'Wisconsin' => 'WI',
        'Wyoming' => 'WY'
    ];
    /**
     * Get the country code by country name.
     *
     * @param  string  $countryName
     * @return string|null
     */
    public static function getCode($countryName)
    {
        return self::$countryCodes[$countryName] ?? null;
    }
    public static function getStateUs($state)
    {
        return self::$stateUs[$state] ?? null;
    }
    /**
     * Get all country codes.
     *
     * @return array
     */
    public static function getAllCodes()
    {
        return self::$countryCodes;
    }
}
