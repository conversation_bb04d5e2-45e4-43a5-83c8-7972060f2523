<?php

namespace App\Transformers;

class SupplierOrderTransformer
{
    public function transform($record)
    {
        $formData = $record->form_data ?? [];
        $supplier = $record->supplier;

        // Xác định loại supplier và áp dụng transformer tương ứng
        $transformer = match ($supplier->name) {
            'Supplier1' => new Supplier1Transformer(),
            'Supplier2' => new Supplier2Transformer(),
            default => new DefaultTransformer(),
        };

        return $transformer->transform($formData);
    }
}

class DefaultTransformer
{
    public function transform($data)
    {
        return [
            'shipment_number' => $data['shipment_number'] ?? null,
            'shipping_label' => $data['shipping_label'] ?? null,
            'items' => $this->transformItems($data['items'] ?? []),
        ];
    }

    protected function transformItems($items)
    {
        $transformedItems = [];
        foreach ($items as $key => $item) {
            $transformedItems[] = [
                'my_sku' => $item['my_sku'] ?? null,
                'quantity' => $item['quantity'] ?? null,
                'variant' => $item['variant'] ?? null,
                'design' => $item['design'] ?? null,
                'note' => $item['note'] ?? null,
                'front_design' => $item['front_design'] ?? null,
                'back_design' => $item['back_design'] ?? null,
                'mockup_front' => $item['mockup_front'] ?? null,
                'mockup_back' => $item['mockup_back'] ?? null,
            ];
        }
        return $transformedItems;
    }
}

class Supplier1Transformer extends DefaultTransformer
{
    public function transform($data)
    {
        return [
            'shipment_number' => $data['shipment']['number'] ?? null,
            'shipping_label' => $data['shipment']['label_url'] ?? null,
            'items' => $this->transformItems($data['order_items'] ?? []),
        ];
    }

    protected function transformItems($items)
    {
        $transformedItems = [];
        foreach ($items as $key => $item) {
            $transformedItems[] = [
                'my_sku' => $item['product_sku'] ?? null,
                'quantity' => $item['qty'] ?? null,
                'variant' => $item['variation'] ?? null,
                'design' => $item['design_id'] ?? null,
                'note' => $item['customer_note'] ?? null,
                'front_design' => $item['design_urls']['front'] ?? null,
                'back_design' => $item['design_urls']['back'] ?? null,
                'mockup_front' => $item['mockups']['front'] ?? null,
                'mockup_back' => $item['mockups']['back'] ?? null,
            ];
        }
        return $transformedItems;
    }
}

class Supplier2Transformer extends DefaultTransformer
{
    public function transform($data)
    {
        return [
            'shipment_number' => $data['tracking']['shipment_id'] ?? null,
            'shipping_label' => $data['tracking']['label_download'] ?? null,
            'items' => $this->transformItems($data['products'] ?? []),
        ];
    }

    protected function transformItems($items)
    {
        $transformedItems = [];
        foreach ($items as $key => $item) {
            $transformedItems[] = [
                'my_sku' => $item['sku_code'] ?? null,
                'quantity' => $item['order_quantity'] ?? null,
                'variant' => $item['variant_name'] ?? null,
                'design' => $item['artwork_id'] ?? null,
                'note' => $item['notes'] ?? null,
                'front_design' => $item['artwork']['front_url'] ?? null,
                'back_design' => $item['artwork']['back_url'] ?? null,
                'mockup_front' => $item['preview']['front'] ?? null,
                'mockup_back' => $item['preview']['back'] ?? null,
            ];
        }
        return $transformedItems;
    }
}
