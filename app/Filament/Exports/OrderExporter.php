<?php

namespace App\Filament\Exports;

use App\Models\Order;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class OrderExporter extends Exporter
{
    protected static ?string $model = Order::class;

    public static function getColumns(): array
    {
        return [

            ExportColumn::make('order_number'),
            ExportColumn::make('total'),
            ExportColumn::make('shipping_cost'),
            ExportColumn::make('status'),
            ExportColumn::make('shipping_first_name'),
            ExportColumn::make('shipping_last_name'),
            ExportColumn::make('shipping_email'),
            ExportColumn::make('shipping_phone'),
            ExportColumn::make('shipping_country'),
            ExportColumn::make('shipping_region'),
            ExportColumn::make('shipping_address_line1'),
            ExportColumn::make('shipping_address_line2'),
            ExportColumn::make('shipping_city'),
            ExportColumn::make('shipping_zip'),
            ExportColumn::make('created_at'),
            ExportColumn::make('updated_at'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your order export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
}
