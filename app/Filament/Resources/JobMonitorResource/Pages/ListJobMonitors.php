<?php

namespace App\Filament\Resources\JobMonitorResource\Pages;

use App\Filament\Resources\JobMonitorResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\Action;

class ListJobMonitors extends ListRecords
{
    protected static string $resource = JobMonitorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('refresh')
                ->icon('heroicon-o-arrow-path')
                ->action(fn () => $this->refreshTable())
        ];
    }

    public function getPollingInterval(): ?string
    {
        return '10s';
    }
}
