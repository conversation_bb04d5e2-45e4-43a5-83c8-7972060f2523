<?php

namespace App\Filament\Pages;

use App\Models\CloudflareDomain;
use Filament\Pages\Page;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Table;
use Filament\Actions\Action;  // Change this import
use Filament\Forms\Components\Textarea;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\TextInputColumn;
use GuzzleHttp\Client;
use Illuminate\Support\Collection;
use Symfony\Component\Process\Process;
use App\Models\CloudflareApiKey;
use Filament\Tables\Filters\SelectFilter;

class CloudflareManager extends Page implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-cloud';
    protected static ?string $navigationGroup = 'System';
    protected static string $view = 'filament.pages.cloudflare-manager';

    public $selectedApiKey;
    public $availableEmails = [];
    public array $domainSearch = [];

    public function mount(): void
    {
        $this->selectedApiKey = CloudflareApiKey::where('is_active', true)->first()?->id;
        $this->loadAvailableEmails();
    }

    protected function getAccountId(CloudflareApiKey $apiKey): string
    {
        try {
            $client = $this->getClientFromApiKey($apiKey);
            $response = $client->get('accounts');
            $data = json_decode($response->getBody(), true);

            if (!empty($data['result'][0]['id'])) {
                return $data['result'][0]['id'];
            }

            throw new \Exception('No account ID found');
        } catch (\Exception $e) {
            \Log::error("Error fetching account ID: " . $e->getMessage());
            throw new \Exception('Unable to fetch account ID: ' . $e->getMessage());
        }
    }
    protected function getHeaderActions(): array
    {
        return [
            Action::make('importDomains')
                ->label('Import Domains')
                ->icon('heroicon-o-plus')
                ->form([
                    Textarea::make('domains')
                        ->label('Domains')
                        ->placeholder('Enter domains (one per line)')
                        ->required()
                        ->helperText('Enter each domain on a new line')
                ])
                ->action(function (array $data) {
                    $this->importDomains($data['domains']);
                }),

            Action::make('manageApiKeys')
                ->label('Add API Key')
                ->icon('heroicon-o-key')
                ->form([
                    TextInput::make('name')
                        ->label('Name')
                        ->required(),

                    TextInput::make('email')
                        ->label('Email')
                        ->email()
                        ->required(),

                    TextInput::make('api_key')
                        ->label('Global API Key')
                        ->required(),
                ])
                ->action(function (array $data) {
                    CloudflareApiKey::create([
                        'name' => $data['name'],
                        'email' => $data['email'],
                        'api_key' => $data['api_key'],
                        'is_active' => true
                    ]);

                    Notification::make()
                        ->title('API Key added successfully')
                        ->success()
                        ->send();
                }),

            Action::make('searchDomains')
                ->label('Search Domains')
                ->icon('heroicon-o-magnifying-glass')
                ->form([
                    Textarea::make('domains')
                        ->label('Domains')
                        ->placeholder('Enter domains (one per line)')
                        ->required()
                        ->helperText('Enter each domain on a new line')
                ])
                ->action(function (array $data) {
                    $this->searchDomains($data['domains']);
                }),
        ];
    }

    protected function importDomains(string $domains): void
    {
        $lines = explode("\n", trim($domains));
        $imported = 0;
        $skipped = 0;
        $failed = 0;
        $needActivation = [];
        $needVerification = [];
        $errors = [];

        $apiKey = CloudflareApiKey::find($this->selectedApiKey);
        if (!$apiKey) {
            Notification::make()
                ->title('Error')
                ->body('Please select a Cloudflare Account first')
                ->danger()
                ->send();
            return;
        }

        $client = $this->getClientFromApiKey($apiKey);
        
        foreach ($lines as $index => $line) {
            try {
                $data = str_getcsv($line);
                if (empty($data[0])) {
                    $errors[] = "Line {$index}: Empty domain name";
                    continue;
                }

                $domain = trim($data[0]);

                // Log processing
                \Log::info("Processing domain: {$domain}");

                // Check existing in our database
                if (CloudflareDomain::where('name', $domain)->exists()) {
                    $skipped++;
                    $errors[] = "Domain {$domain}: Already exists in database";
                    continue;
                }

                // Check if domain exists in Cloudflare
                try {
                    $checkResponse = $client->get('zones', [
                        'query' => [
                            'name' => $domain,
                            'status' => 'active,pending,initializing,moved',
                        ]
                    ]);

                    $checkResult = json_decode($checkResponse->getBody(), true);
                    if (!empty($checkResult['result'][0])) {
                        $skipped++;
                        $errors[] = "Domain {$domain}: Already exists in Cloudflare";
                        continue;
                    }
                } catch (\Exception $e) {
                    \Log::warning("Error checking domain {$domain}: " . $e->getMessage());
                }

                // Try to add domain
                $accountId = $this->getAccountId($apiKey);
                try {
                    $response = $client->post('zones', [
                        'json' => [
                            'account' => [
                                'id' => $accountId
                            ],
                            'name' => $domain,
                            'type' => 'full',
                            'jump_start' => true
                        ]
                    ]);

                    $result = json_decode($response->getBody(), true);

                    if (!empty($result['result'])) {
                        $zone = $result['result'];

                        CloudflareDomain::create([
                            'id' => $zone['id'],
                            'name' => $zone['name'],
                            'status' => $zone['status'],
                            'nameservers' => $zone['name_servers'] ?? [],
                            'ssl_status' => false,
                            'email_enabled' => false,
                            'cloudflare_api_key_id' => $apiKey->id
                        ]);

                        $needActivation[] = [
                            'domain' => $zone['name'],
                            'nameservers' => $zone['name_servers']
                        ];

                        $imported++;
                        \Log::info("Successfully imported domain: {$domain}");
                    }
                } catch (\Exception $e) {
                    $errorMessage = $e->getMessage();
                    if ($e->hasResponse()) {
                        $errorBody = json_decode($e->getResponse()->getBody(), true);
                        if (!empty($errorBody['errors'])) {
                            $errorDetails = collect($errorBody['errors'])->map(function ($error) {
                                return $error['message'] . (
                                    !empty($error['error_chain'])
                                    ? ' (' . collect($error['error_chain'])->pluck('message')->implode(', ') . ')'
                                    : ''
                                );
                            })->implode('; ');
                            $errorMessage = $errorDetails;
                        }
                    }

                    $failed++;
                    $errors[] = "Domain {$domain}: {$errorMessage}";
                    \Log::error("Failed to import domain {$domain}: {$errorMessage}");

                    $needVerification[] = [
                        'domain' => $domain,
                        'error' => $errorMessage
                    ];
                }

                // Add small delay between requests
                usleep(500000); // 0.5 second delay

            } catch (\Exception $e) {
                $failed++;
                $errors[] = "Unexpected error processing {$domain}: " . $e->getMessage();
                \Log::error("Unexpected error processing {$domain}: " . $e->getMessage());
            }
        }

        // Prepare detailed report
        $message = "Import Summary:\n";
        $message .= "============\n";
        $message .= "Total Processed: " . count($lines) . "\n";
        $message .= "Imported: {$imported}\n";
        $message .= "Skipped: {$skipped}\n";
        $message .= "Failed: {$failed}\n\n";

        if (!empty($needActivation)) {
            $message .= "Domains Needing Nameserver Update:\n";
            $message .= "================================\n";
            foreach ($needActivation as $domain) {
                $message .= "\n{$domain['domain']}:\n";
                foreach ($domain['nameservers'] as $ns) {
                    $message .= "- {$ns}\n";
                }
            }
            $message .= "\n";
        }

        if (!empty($errors)) {
            $message .= "Detailed Error Log:\n";
            $message .= "=================\n";
            foreach ($errors as $error) {
                $message .= "• {$error}\n";
            }
        }

        // Log full report
        \Log::info("Import Report:\n" . $message);

        Notification::make()
            ->title('Import Complete')
            ->body($message)
            ->success()
            ->persistent()
            ->send();
    }

    protected function addDomain(array $data): void
    {
        try {
            $apiKey = CloudflareApiKey::find($this->selectedApiKey);
            if (!$apiKey) {
                throw new \Exception('No API key selected');
            }

            $client = $this->getClientFromApiKey($apiKey);
            $accountId = $this->getAccountId($apiKey);

            // Add zone to Cloudflare
            $response = $client->post('zones', [
                'json' => [
                    'name' => $data['name'],
                    'account' => [
                        'id' => $accountId
                    ],
                    'type' => $data['type'],
                    'jump_start' => true
                ]
            ]);

            $result = json_decode($response->getBody(), true);

            if (!empty($result['result'])) {
                $zone = $result['result'];

                // Create initial DNS records
                $this->setupInitialDNS($zone['id'], $zone['name']);

                // Enable SSL
                $this->updateSSL(new CloudflareDomain([
                    'id' => $zone['id'],
                    'name' => $zone['name']
                ]));

                // Refresh domains list
                $this->refreshDomains();

                Notification::make()
                    ->title('Domain added successfully')
                    ->body("Please update your nameservers to:\n" . implode("\n", $zone['name_servers']))
                    ->success()
                    ->persistent()
                    ->send();
            }
        } catch (\Exception $e) {
            \Log::error("Error adding domain: " . $e->getMessage());

            Notification::make()
                ->title('Error adding domain')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
    protected function setupInitialDNS(string $zoneId, string $domain): void
    {
        try {
            $client = $this->getClient();
            $defaultIp = config('services.cloudflare.default_ip');

            $records = [
                [
                    'type' => 'A',
                    'name' => $domain,
                    'content' => $defaultIp,
                    'proxied' => true
                ],
                [
                    'type' => 'CNAME',
                    'name' => "www.{$domain}",
                    'content' => $domain,
                    'proxied' => true
                ]
            ];

            foreach ($records as $record) {
                $client->post("zones/{$zoneId}/dns_records", [
                    'json' => $record
                ]);
            }

            \Log::info("Initial DNS records created for {$domain}");
        } catch (\Exception $e) {
            \Log::error("Error setting up initial DNS: " . $e->getMessage());
        }
    }
    protected function addDestinationEmail(string $email): void
    {
        try {
            $apiKey = CloudflareApiKey::find($this->selectedApiKey);
            if (!$apiKey) {
                throw new \Exception('Selected API key not found');
            }

            $client = $this->getClientFromApiKey($apiKey);
            $accountId = $this->getAccountId($apiKey);

            $client->post("accounts/{$accountId}/email/routing/addresses", [
                'json' => [
                    'email' => $email
                ]
            ]);

            $this->loadDestinationEmails();

            Notification::make()
                ->title('Destination email added successfully')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error adding destination email')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function loadDestinationEmails(CloudflareDomain $record): array
    {
        try {
            $client = $this->getClient($record);
            $accountId = $this->getAccountId($record->apiKey);

            $response = $client->get("accounts/{$accountId}/email/routing/addresses");
            $data = json_decode($response->getBody(), true);

            if (!empty($data['result'])) {
                return collect($data['result'])
                    ->pluck('email')
                    ->toArray();
            }

            return [];
        } catch (\Exception $e) {
            \Log::error("Error loading destination emails: " . $e->getMessage());
            return [];
        }
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(CloudflareDomain::query()->when($this->domainSearch, function ($query) {
                $query->whereIn('name', $this->domainSearch);
            }))
            ->columns([
                TextColumn::make('name')
                    ->label('Domain')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'pending' => 'warning',
                        default => 'danger',
                    }),
                IconColumn::make('ssl_status')
                    ->boolean()
                    ->label('SSL'),
                IconColumn::make('email_enabled')
                    ->boolean()
                    ->label('Email'),
                TextColumn::make('nameservers')
                    ->label('Nameservers')
                    ->listWithLineBreaks()
                    ->bulleted(),
                TextInputColumn::make('notes')
                    ->label('Notes')

                    ->searchable()
                    ->sortable()
                    ->toggleable()

                    ->tooltip(function ($record): ?string {
                        return $record->notes ?? null;
                    }),
                TextColumn::make('apiKey.email')
                    ->label('Cloudflare Account')
                    ->searchable(),
            ])
            ->filters([
                SelectFilter::make('cloudflare_api_key_id')
                    ->label('Cloudflare Account')
                    ->options(CloudflareApiKey::pluck('email', 'id'))
            ])
            ->actions([
                ActionGroup::make([
                    \Filament\Tables\Actions\Action::make('viewEmailConfig')
                        ->label('Email Settings')
                        ->icon('heroicon-o-envelope')
                        ->modalWidth('xl')
                        ->modalContent(function (CloudflareDomain $record) {
                            try {
                                $client = $this->getClient($record);
                                
                                // Get routing status
                                $routingResponse = $client->get("zones/{$record->id}/email/routing");
                                $routingStatus = json_decode($routingResponse->getBody(), true);
                                
                                // Get rules
                                $rulesResponse = $client->get("zones/{$record->id}/email/routing/rules");
                                $rulesData = json_decode($rulesResponse->getBody(), true);
                                
                                // Get MX records
                                $dnsResponse = $client->get("zones/{$record->id}/dns_records", [
                                    'query' => ['type' => 'MX']
                                ]);
                                $mxRecords = json_decode($dnsResponse->getBody(), true);

                                return view('filament.pages.email-config', [
                                    'domain' => $record,
                                    'routing_enabled' => $routingStatus['result']['enabled'] ?? false,
                                    'rules' => $rulesData['result'] ?? [],
                                    'mx_records' => $mxRecords['result'] ?? []
                                ]);
                            } catch (\Exception $e) {
                                return view('filament.pages.email-config-error', [
                                    'error' => $e->getMessage()
                                ]);
                            }
                        })
                        ->modalActions([
                            \Filament\Tables\Actions\Action::make('enableRouting')
                                ->label('Enable Routing')
                                ->icon('heroicon-o-check')
                                ->requiresConfirmation()
                                ->visible(fn ($record) => !$record->email_enabled)
                                ->action(function (CloudflareDomain $record) {
                                    try {
                                        $client = $this->getClient($record);
                                        $client->post("zones/{$record->id}/email/routing/enable");
                                        
                                        $record->email_enabled = true;
                                        $record->save();
                                        
                                        Notification::make()
                                            ->title('Email routing enabled')
                                            ->success()
                                            ->send();
                                    } catch (\Exception $e) {
                                        Notification::make()
                                            ->title('Error enabling email routing')
                                            ->body($e->getMessage())
                                            ->danger()
                                            ->send();
                                    }
                                }),
                        ]),

                    \Filament\Tables\Actions\Action::make('configureForward')
                        ->label('Add Forward Rule')
                        ->icon('heroicon-o-arrow-right')
                        ->form([
                            TextInput::make('mailbox')
                                ->label('Mailbox')
                                ->placeholder('e.g. support')
                                ->helperText('Only enter the part before @domain.com')
                                ->required(),
                            TextInput::make('forward_to')
                                ->label('Forward To')
                                ->email()
                                ->required()
                                ->placeholder('e.g. <EMAIL>'),
                        ])
                        ->action(function (CloudflareDomain $record, array $data) {
                            try {
                                $client = $this->getClient($record);
                                
                                // Enable email routing if not enabled
                                $routingResponse = $client->get("zones/{$record->id}/email/routing");
                                $routingStatus = json_decode($routingResponse->getBody(), true);
                                
                                if (!$routingStatus['result']['enabled']) {
                                    $client->post("zones/{$record->id}/email/routing/enable");
                                    $client->post("zones/{$record->id}/email/routing/dns");
                                }

                                $fullMailbox = $data['mailbox'] . '@' . $record->name;

                                $ruleConfig = [
                                    'actions' => [
                                        [
                                            'type' => 'forward',
                                            'value' => [
                                                $data['forward_to']
                                            ]
                                        ]
                                    ],
                                    'matchers' => [
                                        [
                                            'type' => 'literal',
                                            'field' => 'to',
                                            'value' => $fullMailbox
                                        ]
                                    ],
                                    'enabled' => true,
                                    'priority' => 0,
                                    'name' => 'Rule created at ' . now()->toISOString()  // Format gi��ng Cloudflare
                                ];

                                $response = $client->post("zones/{$record->id}/email/routing/rules", [
                                    'json' => $ruleConfig
                                ]);

                                $record->email_enabled = true;
                                $record->save();

                                Notification::make()
                                    ->title('Email forward rule created successfully')
                                    ->success()
                                    ->send();
                            } catch (\Exception $e) {
                                \Log::error('Cloudflare error: ' . $e->getMessage());
                                if ($e->hasResponse()) {
                                    \Log::error('Response body: ' . $e->getResponse()->getBody());
                                }
                                
                                Notification::make()
                                    ->title('Error configuring email forward')
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        }),

                    \Filament\Tables\Actions\Action::make('configureCatchAll')
                        ->label('Configure Catch-all')
                        ->icon('heroicon-o-inbox-arrow-down')
                        ->form([
                            TextInput::make('forward_to')
                                ->label('Forward To')
                                ->email()
                                ->required()
                                ->placeholder('e.g. <EMAIL>')
                        ])
                        ->action(function (CloudflareDomain $record, array $data) {
                            // ... existing catch-all logic ...
                        }),
                ])->icon('heroicon-m-envelope')->label('Email Actions'),

                ActionGroup::make([
                    \Filament\Tables\Actions\Action::make('updateSSL')
                        ->label('Update SSL')
                        ->icon('heroicon-o-shield-check')
                        ->requiresConfirmation()
                        ->action(function (Collection $records) {
                            foreach ($records as $record) {
                                $this->updateSSL($record);
                            }
                        }),

                    \Filament\Tables\Actions\Action::make('purgeCache')
                        ->label('Purge Cache')
                        ->icon('heroicon-o-trash')
                        ->requiresConfirmation()
                        ->action(function (Collection $records) {
                            foreach ($records as $record) {
                                $this->purgeCache($record);
                            }
                        }),

                    \Filament\Tables\Actions\Action::make('setupDNS')
                        ->label('Setup DNS')
                        ->icon('heroicon-o-server')
                        ->form([
                            TextInput::make('ip_address')
                                ->label('IP Address')
                                ->default(config('services.cloudflare.default_ip'))
                                ->required()
                                ->regex('/^(\d{1,3}\.){3}\d{1,3}$/')
                                ->default('*************')
                                ->helperText('Enter the IPv4 address for the A record')
                        ])
                        ->action(function (Collection $records, array $data) {
                            foreach ($records as $record) {
                                $this->setupDNS($record, $data['ip_address']);
                            }
                        }),

                    \Filament\Tables\Actions\Action::make('disableEmail')
                        ->label('Disable Email')
                        ->icon('heroicon-o-no-symbol')
                        ->requiresConfirmation()
                        ->action(function (Collection $records) {
                            foreach ($records as $record) {
                                $this->disableEmail($record);
                            }
                        }),
                ])->icon('heroicon-m-server')->label('Server Actions'),
            ])
            ->bulkActions([
                BulkAction::make('configureCatchAll')
                    ->label('Configure Catch-all')
                    ->icon('heroicon-o-envelope')
                    ->form([
                        Select::make('catchAllEmail')
                            ->label('Destination Email')
                            ->options(array_combine($this->availableEmails, $this->availableEmails))
                            ->searchable()
                            ->required()
                    ])
                    ->action(function (Collection $records, array $data) {
                        $this->catchAllEmail = $data['catchAllEmail'];
                        $this->configureCatchAll($records);
                    }),

                BulkAction::make('updateSSL')
                    ->label('Update SSL')
                    ->icon('heroicon-o-shield-check')
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        foreach ($records as $record) {
                            $this->updateSSL($record);
                        }
                    }),

                BulkAction::make('purgeCache')
                    ->label('Purge Cache')
                    ->icon('heroicon-o-trash')
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        foreach ($records as $record) {
                            $this->purgeCache($record);
                        }
                    }),

                BulkAction::make('setupDNS')
                    ->label('Setup DNS')
                    ->icon('heroicon-o-server')
                    ->form([
                        TextInput::make('ip_address')
                            ->label('IP Address')
                            ->default(config('services.cloudflare.default_ip'))
                            ->required()
                            ->regex('/^(\d{1,3}\.){3}\d{1,3}$/')
                            ->default('*************')
                            ->helperText('Enter the IPv4 address for the A record')
                    ])
                    ->action(function (Collection $records, array $data) {
                        foreach ($records as $record) {
                            $this->setupDNS($record, $data['ip_address']);
                        }
                    }),

                BulkAction::make('disableEmail')
                    ->label('Disable Email')
                    ->icon('heroicon-o-no-symbol')
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        foreach ($records as $record) {
                            $this->disableEmail($record);
                        }
                    }),

                BulkAction::make('configureWildcard')
                    ->label('Configure Wildcard DNS')
                    ->icon('heroicon-o-globe-alt')
                    ->form([
                        TextInput::make('ip_address')
                            ->label('IP Address')
                            ->default(config('services.cloudflare.default_ip'))
                            ->required()
                            ->regex('/^(\d{1,3}\.){3}\d{1,3}$/')
                            ->default('*************')
                            ->helperText('This IP will be used for *.domain.com records')
                    ])
                    ->action(function (Collection $records, array $data) {
                        $successCount = 0;
                        $errorCount = 0;
                        $errors = [];

                        foreach ($records as $domain) {
                            try {
                                $client = $this->getClient($domain);
                                
                                // Get existing wildcard records
                                $response = $client->get("zones/{$domain->id}/dns_records", [
                                    'query' => [
                                        'type' => 'A',
                                        'name' => "*." . $domain->name
                                    ]
                                ]);
                                $existingRecords = json_decode($response->getBody(), true);

                                $recordData = [
                                    'type' => 'A',
                                    'name' => "*." . $domain->name,
                                    'content' => $data['ip_address'],
                                    'proxied' => true,
                                    'ttl' => 1,
                                    'comment' => 'Wildcard subdomain record'
                                ];

                                if (!empty($existingRecords['result'][0])) {
                                    // Update existing record
                                    $client->put(
                                        "zones/{$domain->id}/dns_records/{$existingRecords['result'][0]['id']}", 
                                        ['json' => $recordData]
                                    );
                                    \Log::info("Updated wildcard DNS for {$domain->name}");
                                } else {
                                    // Create new record
                                    $response = $client->post(
                                        "zones/{$domain->id}/dns_records", 
                                        ['json' => $recordData]
                                    );
                                    $result = json_decode($response->getBody(), true);
                                    
                                    if (!$result['success']) {
                                        throw new \Exception(
                                            "Failed to create DNS record: " . 
                                            json_encode($result['errors'] ?? 'Unknown error')
                                        );
                                    }
                                    \Log::info("Created new wildcard DNS for {$domain->name}");
                                }

                                $successCount++;
                            } catch (\Exception $e) {
                                $errorCount++;
                                $errorMessage = $e->getMessage();
                                if ($e->hasResponse()) {
                                    $errorBody = json_decode($e->getResponse()->getBody(), true);
                                    if (!empty($errorBody['errors'])) {
                                        $errorMessage = collect($errorBody['errors'])
                                            ->map(fn($error) => $error['message'])
                                            ->implode('; ');
                                    }
                                }
                                
                                $errors[] = "{$domain->name}: {$errorMessage}";
                                \Log::error("Wildcard DNS setup error for {$domain->name}: {$errorMessage}");
                            }
                        }

                        // Prepare notification message
                        $message = "Completed configuring wildcard DNS:\n";
                        $message .= "Success: {$successCount}\n";
                        $message .= "Failed: {$errorCount}\n";
                        
                        if (!empty($errors)) {
                            $message .= "\nErrors:\n" . implode("\n", $errors);
                        }

                        Notification::make()
                            ->title('Wildcard DNS Configuration')
                            ->body($message)
                            ->success($errorCount === 0)
                            ->danger($errorCount > 0)
                            ->persistent()
                            ->send();
                    }),
            ]);
    }

    protected function updateSSL(CloudflareDomain $record): void
    {
        try {
            $this->getClient($record)->patch("zones/{$record->id}/settings/ssl", [
                'json' => [
                    'value' => 'flexible'
                ]
            ]);

            $record->ssl_status = true;
            $record->save();

            Notification::make()
                ->title("SSL updated for {$record->name}")
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title("Error updating SSL")
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function purgeCache(CloudflareDomain $record): void
    {
        try {
            $this->getClient($record)->post("zones/{$record->id}/purge_cache", [
                'json' => [
                    'purge_everything' => true
                ]
            ]);

            Notification::make()
                ->title("Cache purged for {$record->name}")
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title("Error purging cache")
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
    protected function viewEmailConfig(CloudflareDomain $record): void
    {
        try {
            $client = $this->getClient($record);
            $data = [];

            // Get email routing status
            $routingResponse = $client->get("zones/{$record->id}/email/routing");
            $routingStatus = json_decode($routingResponse->getBody(), true);
            $data['routing_enabled'] = $routingStatus['result']['enabled'] ?? false;

            // Get catch-all rule
            try {
                $catchAllResponse = $client->get("zones/{$record->id}/email/routing/rules/catch_all");
                $catchAllRule = json_decode($catchAllResponse->getBody(), true);
                $data['catch_all'] = $catchAllRule['result'] ?? null;
            } catch (\Exception $e) {
                $data['catch_all'] = null;
            }

            // Get all email routing rules
            $rulesResponse = $client->get("zones/{$record->id}/email/routing/rules");
            $rules = json_decode($rulesResponse->getBody(), true);
            $data['rules'] = $rules['result'] ?? [];

            // Get DNS records related to email
            $dnsResponse = $client->get("zones/{$record->id}/dns_records", [
                'query' => [
                    'type' => 'MX',
                ]
            ]);
            $mxRecords = json_decode($dnsResponse->getBody(), true);
            $data['mx_records'] = $mxRecords['result'] ?? [];

            // Format message
            $message = "Email Configuration for {$record->name}\n";
            $message .= "=====================================\n\n";

            $message .= "Email Routing: " . ($data['routing_enabled'] ? 'Enabled' : 'Disabled') . "\n\n";

            if ($data['catch_all']) {
                $message .= "Catch-all Rule:\n";
                $message .= "- Name: " . ($data['catch_all']['name'] ?? 'N/A') . "\n";
                $message .= "- Enabled: " . (($data['catch_all']['enabled'] ?? false) ? 'Yes' : 'No') . "\n";
                if (!empty($data['catch_all']['actions'])) {
                    $message .= "- Actions:\n";
                    foreach ($data['catch_all']['actions'] as $action) {
                        $message .= "  • Type: {$action['type']}\n";
                        if ($action['type'] === 'forward') {
                            $message .= "    Destinations: " . implode(', ', $action['value']) . "\n";
                        }
                    }
                }
                $message .= "\n";
            }

            if (!empty($data['rules'])) {
                $message .= "Custom Rules:\n";
                foreach ($data['rules'] as $rule) {
                    if (empty($rule['tag']) || $rule['tag'] !== 'catch_all') {
                        $message .= "- {$rule['name']}\n";
                        $message .= "  • Enabled: " . ($rule['enabled'] ? 'Yes' : 'No') . "\n";
                        if (!empty($rule['matchers'])) {
                            $message .= "   Matches: \n";
                            foreach ($rule['matchers'] as $matcher) {
                                $message .= "    - Type: {$matcher['type']}\n";
                                if (!empty($matcher['value'])) {
                                    $message .= "      Value: {$matcher['value']}\n";
                                }
                            }
                        }
                        if (!empty($rule['actions'])) {
                            $message .= "  • Actions:\n";
                            foreach ($rule['actions'] as $action) {
                                $message .= "    - Type: {$action['type']}\n";
                                if (!empty($action['value'])) {
                                    $message .= "      Value: " . (is_array($action['value']) ? implode(', ', $action['value']) : $action['value']) . "\n";
                                }
                            }
                        }
                        $message .= "\n";
                    }
                }
            }

            if (!empty($data['mx_records'])) {
                $message .= "MX Records:\n";
                foreach ($data['mx_records'] as $record) {
                    $message .= "- {$record['content']} (Priority: {$record['priority']})\n";
                }
            }

            Notification::make()
                ->title('Email Configuration')
                ->body($message)
                ->success()
                ->persistent()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error fetching email configuration')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
    protected function setupDNS(CloudflareDomain $record, string $ipAddress): void
    {
        try {
            $client = $this->getClient($record);
            
            // Validate IP address
            if (!filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                throw new \Exception('Invalid IPv4 address provided');
            }

            // Get existing records
            $response = $client->get("zones/{$record->id}/dns_records");
            $existingRecords = json_decode($response->getBody(), true);

            // Define required records with all necessary fields
            $requiredRecords = [
                [
                    'type' => 'A',
                    'name' => $record->name,
                    'content' => $ipAddress,
                    'proxied' => true,
                    'ttl' => 1,
                    'comment' => 'Main A record'
                ],
                [
                    'type' => 'CNAME',
                    'name' => "www.{$record->name}",
                    'content' => $record->name,
                    'proxied' => true,
                    'ttl' => 1,
                    'comment' => 'WWW CNAME record'
                ],
                // Add wildcard record
                [
                    'type' => 'A',
                    'name' => "*.{$record->name}",
                    'content' => $ipAddress,
                    'proxied' => true,
                    'ttl' => 1,
                    'comment' => 'Wildcard subdomain record'
                ]
            ];

            // Create or update records
            foreach ($requiredRecords as $required) {
                $exists = false;
                if (!empty($existingRecords['result'])) {
                    foreach ($existingRecords['result'] as $existing) {
                        if ($existing['type'] === $required['type'] && $existing['name'] === $required['name']) {
                            $exists = true;
                            try {
                                // Update if different
                                if ($existing['content'] !== $required['content'] || 
                                    $existing['proxied'] !== $required['proxied']) {
                                    $client->put(
                                        "zones/{$record->id}/dns_records/{$existing['id']}", 
                                        ['json' => $required]
                                    );
                                    \Log::info("Updated DNS record: {$required['type']} {$required['name']}");
                                }
                            } catch (\Exception $e) {
                                \Log::error("Error updating DNS record: " . $e->getMessage());
                                throw $e;
                            }
                            break;
                        }
                    }
                }

                // Create if doesn't exist
                if (!$exists) {
                    try {
                        $response = $client->post(
                            "zones/{$record->id}/dns_records", 
                            ['json' => $required]
                        );
                        $result = json_decode($response->getBody(), true);
                        
                        if (!$result['success']) {
                            throw new \Exception(
                                "Failed to create DNS record: " . 
                                json_encode($result['errors'] ?? 'Unknown error')
                            );
                        }
                        
                        \Log::info("Created new DNS record: {$required['type']} {$required['name']}");
                    } catch (\Exception $e) {
                        \Log::error("Error creating DNS record: " . $e->getMessage());
                        throw $e;
                    }
                }
            }

            Notification::make()
                ->title("DNS setup completed for {$record->name}")
                ->success()
                ->send();
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            if ($e->hasResponse()) {
                $errorBody = json_decode($e->getResponse()->getBody(), true);
                if (!empty($errorBody['errors'])) {
                    $errorMessage = collect($errorBody['errors'])
                        ->map(fn($error) => $error['message'])
                        ->implode('; ');
                }
            }
            
            \Log::error("DNS setup error for {$record->name}: " . $errorMessage);
            
            Notification::make()
                ->title("Error setting up DNS")
                ->body($errorMessage)
                ->danger()
                ->send();
        }
    }

    protected function disableEmail(CloudflareDomain $record): void
    {
        try {
            $client = $this->getClient($record);
            
            // Delete catch-all rule if exists
            try {
                $client->delete("zones/{$record->id}/email/routing/rules/catch_all");
            } catch (\Exception $e) {
                \Log::info("No catch-all rule to delete for {$record->name}");
            }

            // Disable email routing
            $client->post("zones/{$record->id}/email/routing/disable");

            // Update local record
            $record->email_enabled = false;
            $record->save();

            Notification::make()
                ->title("Email disabled for {$record->name}")
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title("Error disabling email")
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function configureCatchAll(Collection $records): void
    {
        if (empty($this->availableEmails)) {
            $this->loadAvailableEmails();  // Load verified destinations for catch-all
        }

        foreach ($records as $record) {
            try {
                $client = $this->getClient($record);
                
                \Log::info("Configuring catch-all for domain: {$record->name}");

                // 1. Check current email routing status
                $routingResponse = $client->get("zones/{$record->id}/email/routing");
                $routingStatus = json_decode($routingResponse->getBody(), true);

                if (!$routingStatus['result']['enabled']) {
                    // Setup DNS records first
                    $client->post("zones/{$record->id}/email/routing/dns");
                    \Log::info("DNS records created for email routing");
                }

                // 2. Check existing catch-all rule
                $catchAllResponse = $client->get("zones/{$record->id}/email/routing/rules/catch_all");
                $catchAllRule = json_decode($catchAllResponse->getBody(), true);

                // 3. Create or update catch-all rule
                $ruleConfig = [
                    'actions' => [
                        [
                            'type' => 'forward',
                            'value' => [$this->catchAllEmail]
                        ]
                    ],
                    'matchers' => [
                        [
                            'type' => 'all'
                        ]
                    ],
                    'name' => 'Catch-all Rule',
                    'enabled' => true,
                    'priority' => 0
                ];

                if (empty($catchAllRule['result'])) {
                    // Create new rule
                    $client->post("zones/{$record->id}/email/routing/rules", [
                        'json' => $ruleConfig
                    ]);
                    \Log::info("Created new catch-all rule");
                } else {
                    // Update existing rule
                    $client->put("zones/{$record->id}/email/routing/rules/catch_all", [
                        'json' => $ruleConfig
                    ]);
                    \Log::info("Updated existing catch-all rule");
                }

                // 4. Update local record
                $record->email_enabled = true;
                $record->save();

                Notification::make()
                    ->title("Domain {$record->name} configured successfully")
                    ->success()
                    ->send();
            } catch (\Exception $e) {
                Notification::make()
                    ->title("Error configuring catch-all for {$record->name}")
                    ->body($e->getMessage())
                    ->danger()
                    ->send();
            }
        }
    }

    protected function getClient(CloudflareDomain $record): Client
    {
        $apiKey = $record->apiKey;
        if (!$apiKey) {
            throw new \Exception('No API key associated with this domain');
        }

        return $this->getClientFromApiKey($apiKey);
    }

    protected function getClientFromApiKey(CloudflareApiKey $apiKey): Client
    {
        return new Client([
            'base_uri' => 'https://api.cloudflare.com/client/v4/',
            'headers' => [
                'X-Auth-Email' => $apiKey->email,
                'X-Auth-Key' => $apiKey->api_key,
                'Content-Type' => 'application/json'
            ],
            'timeout' => 30,
        ]);
    }

    protected function refreshDomains(): void
    {
        try {
            // Chạy command trong background
            $process = new Process(['php', 'artisan', 'cloudflare:refresh-domains']);
            $process->start();

            Notification::make()
                ->title('Refresh Started')
                ->body('Domain refresh has been started in the background. This may take several minutes.')
                ->info()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error Starting Refresh')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function deleteRule(string $ruleId, CloudflareDomain $record): void
    {
        try {
            $client = $this->getClient($record);
            $client->delete("zones/{$record->id}/email/routing/rules/{$ruleId}");
            
            Notification::make()
                ->title('Rule deleted successfully')
                ->success()
                ->send();

            // Refresh view để cập nhật danh sách rules
            $this->dispatch('email-config-updated');
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error deleting rule')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function searchDomains(string $domains): void
    {
        $domainList = explode("\n", trim($domains));
        $domainList = array_map('trim', $domainList);

        // Use domainSearch instead of tableSearch
        $this->domainSearch = $domainList;

        $foundDomains = CloudflareDomain::whereIn('name', $domainList)->get();

        if ($foundDomains->isEmpty()) {
            Notification::make()
                ->title('No domains found')
                ->body('No domains were found in the database that match the search criteria.')
                ->warning()
                ->send();
        } else {
            $found = $foundDomains->pluck('name')->toArray();
            $notFound = array_diff($domainList, $found);
            
            $message = '';
            if (!empty($found)) {
                $message .= "Found domains:\n- " . implode("\n- ", $found) . "\n\n";
            }
            if (!empty($notFound)) {
                $message .= "Not found domains:\n- " . implode("\n- ", $notFound);
            }

            Notification::make()
                ->title(count($found) . ' domains found')
                ->body($message)
                ->success()
                ->persistent()
                ->send();
        }
    }

    protected function loadAvailableEmails(): void
    {
        try {
            $apiKey = CloudflareApiKey::find($this->selectedApiKey);
            if (!$apiKey) {
                throw new \Exception('No API key selected');
            }

            $client = $this->getClientFromApiKey($apiKey);
            $accountId = $this->getAccountId($apiKey);

            $response = $client->get("accounts/{$accountId}/email/routing/addresses");
            $data = json_decode($response->getBody(), true);

            if (!empty($data['result'])) {
                $this->availableEmails = collect($data['result'])
                    ->pluck('email')
                    ->toArray();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error loading email addresses')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
