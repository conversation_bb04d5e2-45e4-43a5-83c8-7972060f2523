<?php
 
namespace App\Filament\Pages;

use App\Filament\App\Widgets\IdeaStatisticsWidget;
use App\Filament\App\Widgets\MonthlyGoalsWidget;
use App\Filament\App\Widgets\MonthlyPayouts;
use App\Filament\App\Widgets\OrderFulfillmentStatsWidget;
use App\Filament\App\Widgets\TopHandlersWidget;
use App\Filament\App\Widgets\TopSellersWidget;
use App\Filament\App\Widgets\TopSellingProductsWidget;
use App\Filament\App\Widgets\UserActivityWidget;
use App\Filament\App\Widgets\NoticeWidget;
use App\Filament\App\Widgets\OrderStatusStats;
use App\Filament\App\Widgets\SellerMonthlyPayouts;
use App\Filament\App\Widgets\SellerPerformanceTable;
use App\Filament\App\Widgets\SellerStatsOverview;
use App\Filament\App\Widgets\StoreOrdersWidget;
use App\Filament\App\Widgets\StoreProductCountHistoryWidget;
use App\Filament\App\Widgets\StoreProductReminder;
use App\Filament\App\Widgets\OrderFulfillmentChart;
use App\Filament\App\Widgets\TikTokMonthlyPayouts;
use App\Filament\App\Widgets\LatestTikTokPaymentsWidget;
use App\Filament\App\Widgets\LatestSellerFinancesWidget;
use App\Filament\App\Widgets\StoreStatsOverview;
use App\Filament\App\Widgets\BankPayoutStatsWidget;
use App\Filament\App\Widgets\BankPayoutSummaryWidget;
use Illuminate\Support\Facades\Auth;

class Dashboard extends \Filament\Pages\Dashboard
{
    protected static ?string $title = '';
    protected static ?string $navigationLabel = 'Dashboard';

    /**
     * Override getColumns để đảm bảo dashboard có đủ columns cho responsive
     */
    public function getColumns(): int | string | array
    {
        return [
            'default' => 2,  // Mobile: 2 columns
            'sm' => 2,       // Small: 2 columns
            'md' => 3,       // Medium: 3 columns
            'lg' => 4,       // Large: 4 columns
            'xl' => 4,       // XL: 4 columns
        ];
    }

    public function getWidgets(): array
    {
        $user = Auth::user();  // Ensure you have imported the Auth facade
        $widgets = [];
  
        if ($user->hasAnyRole(['super_admin', 'User Manager', 'Fullfillment Manager','Leader', 'Seller'])) {
            $widgets[] = OrderFulfillmentStatsWidget::class; // Tạm disable để debug
            $widgets[] = StoreStatsOverview::class; // Tạm disable để debug
        }
    
        if ($user->hasAnyRole(['super_admin', 'User Manager', 'Fullfillment Manager'])) {
            $widgets[] = TopSellersWidget::class;
            $widgets[] = BankPayoutSummaryWidget::class;  // Widget thống kê
            $widgets[] = BankPayoutStatsWidget::class;    // Widget biểu đồ
            $widgets[] = OrderFulfillmentChart::class;
        }
       
        return $widgets;
    }
}