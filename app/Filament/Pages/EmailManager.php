<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Notifications\Notification;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Support\Collection;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class EmailManager extends Page implements HasTable
{
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';
    protected static ?string $navigationGroup = 'System';
    
    protected $mailbox;
    protected Collection $emails;

    protected static string $view = 'filament.pages.email-manager';

    public static function shouldRegisterNavigation(): bool
    {
        return true;
    }

    public function mount(): void
    {
        $this->emails = collect([]);
        $this->loadEmails();
    }

    protected function loadEmails(): void
    {
        try {
            // Thêm timeout và các options khác
            $opts = [
                'DISABLE_AUTHENTICATOR' => 'GSSAPI',
                'TIMEOUT' => 10  // timeout 10 giây
            ];
            
            $mailbox = '{imap.postmarkapp.com:993/imap/ssl/novalidate-cert}INBOX';
            
            // Thêm các options vào imap_open
            $this->mailbox = @imap_open(
                $mailbox,
                config('mail.username'),
                config('mail.password'),
                OP_READONLY | OP_HALFOPEN, // Thêm OP_HALFOPEN để kết nối nhanh hơn
                1,
                $opts
            );

            if (!$this->mailbox) {
                throw new \Exception('Cannot connect to mailbox: ' . imap_last_error());
            }

            // Chỉ lấy 50 email gần nhất
            $emails = imap_search($this->mailbox, 'ALL', SE_UID);
            if ($emails) {
                $emails = array_slice(array_reverse($emails), 0, 50);
                
                $this->emails = collect($emails)->map(function ($msgno) {
                    $overview = imap_fetch_overview($this->mailbox, $msgno, FT_UID)[0];
                    
                    return [
                        'id' => $msgno,
                        'from' => $overview->from,
                        'subject' => imap_utf8($overview->subject ?? '(No Subject)'),
                        'date' => Carbon::parse($overview->date),
                        'size' => $overview->size ?? 0,
                        'body' => '' // Chỉ load body khi cần xem
                    ];
                });
            } else {
                $this->emails = collect([]);
            }

        } catch (\Exception $e) {
            \Log::error('IMAP Error:', [
                'error' => $e->getMessage(),
                'last_error' => imap_last_error()
            ]);

            Notification::make()
                ->title('Error loading emails')
                ->body($e->getMessage())
                ->danger()
                ->persistent()
                ->send();

            $this->emails = collect([]);
        }

        // Đóng kết nối IMAP
        if ($this->mailbox) {
            imap_close($this->mailbox);
        }
    }

    // Thêm phương thức để load body khi cần
    protected function getEmailBody($uid): string
    {
        try {
            $mailbox = '{imap.postmarkapp.com:993/imap/ssl/novalidate-cert}INBOX';
            $mbox = imap_open($mailbox, config('mail.username'), config('mail.password'), OP_READONLY);
            
            if (!$mbox) {
                throw new \Exception('Cannot connect to mailbox');
            }

            $body = imap_fetchbody($mbox, $uid, 1, FT_UID);
            imap_close($mbox);
            
            return quoted_printable_decode($body);
        } catch (\Exception $e) {
            return 'Error loading email content: ' . $e->getMessage();
        }
    }

    public function getTableQuery(): Builder
    {
        // Tạo model ảo để xử lý collection
        $model = new class extends Model {
            protected $table = 'emails';
            public $timestamps = false;
        };

        // Tạo collection query
        return $model->newQuery()->setQuery(
            $model->newQuery()->getQuery()->fromSub(
                function ($query) {
                    $query->fromSub(
                        function ($query) {
                            $query->from('emails')->whereRaw('0=1');
                            
                            if ($this->emails->isNotEmpty()) {
                                foreach ($this->emails as $email) {
                                    $query->union(
                                        \DB::table(\DB::raw('(SELECT '.implode(',', array_map(function($value, $key) {
                                            return is_string($value) ? 
                                                \DB::getPdo()->quote($value)." as `$key`" : 
                                                "$value as `$key`";
                                        }, $email, array_keys($email))).')'))
                                    );
                                }
                            }
                        },
                        'emails'
                    );
                },
                'emails'
            )
        );
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('from')
                    ->label('From')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('subject')
                    ->label('Subject')
                    ->searchable()
                    ->sortable()
                    ->limit(50),
                TextColumn::make('date')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('size')
                    ->label('Size')
                    ->formatStateUsing(fn ($state) => $this->formatSize($state))
            ])
            ->actions([
                Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->action(fn ($record) => $this->viewEmail($record))
            ])
            ->defaultSort('date', 'desc');
    }

    // Sửa lại phương thức viewEmail để lazy load body
    protected function viewEmail($record): void
    {
        $body = $this->getEmailBody($record['id']);
        
        Notification::make()
            ->title($record['subject'])
            ->body($body)
            ->persistent()
            ->success()
            ->send();
    }

    protected function formatSize($bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}