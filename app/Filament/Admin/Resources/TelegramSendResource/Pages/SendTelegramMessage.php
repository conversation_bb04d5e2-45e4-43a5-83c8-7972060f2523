<?php

namespace App\Filament\Admin\Resources\TelegramSendResource\Pages;

use App\Filament\Admin\Resources\TelegramSendResource;
use App\Models\User;
use App\Services\TelegramService;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Auth;

class SendTelegramMessage extends Page
{
    use InteractsWithFormActions;

    protected static string $resource = TelegramSendResource::class;

    protected static string $view = 'filament.app.resources.telegram-send-resource.pages.send-telegram-message';

    protected static ?string $title = 'Send Telegram Message';

    protected static ?string $navigationLabel = 'Send Message';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Send Telegram Message')
                    ->description('Send a message to users via Telegram using the new bot system')
                    ->schema([
                        Forms\Components\Placeholder::make('info')
                            ->label('')
                            ->content('🚀 **New Bot System**: Users can now register by clicking the bot link and pressing Start. No manual configuration needed!')
                            ->columnSpanFull(),
                        Forms\Components\Select::make('recipient_type')
                            ->label('Send to')
                            ->options([
                                'single' => 'Single User',
                                'multiple' => 'Multiple Users',
                                'role' => 'Users by Role',
                            ])
                            ->default('single')
                            ->live()
                            ->required(),

                        Forms\Components\Select::make('recipient_user_id')
                            ->label('Select User')
                            ->options(function () {
                                return User::whereHas('telegramUsers', function ($query) {
                                    $query->active();
                                })
                                ->orWhereNotNull('telegram_chat_id')
                                ->orWhereNotNull('telegram_bot_config')
                                ->pluck('name', 'id');
                            })
                            ->searchable()
                            ->preload()
                            ->visible(fn (Forms\Get $get): bool => $get('recipient_type') === 'single')
                            ->required(fn (Forms\Get $get): bool => $get('recipient_type') === 'single')
                            ->helperText('Users with active Telegram registration or legacy configuration'),

                        Forms\Components\Select::make('recipient_user_ids')
                            ->label('Select Users')
                            ->options(function () {
                                return User::whereHas('telegramUsers', function ($query) {
                                    $query->active();
                                })
                                ->orWhereNotNull('telegram_chat_id')
                                ->orWhereNotNull('telegram_bot_config')
                                ->pluck('name', 'id');
                            })
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->visible(fn (Forms\Get $get): bool => $get('recipient_type') === 'multiple')
                            ->required(fn (Forms\Get $get): bool => $get('recipient_type') === 'multiple')
                            ->helperText('Users with active Telegram registration or legacy configuration'),

                        Forms\Components\Select::make('recipient_role')
                            ->label('Select Role')
                            ->options(function () {
                                return \Spatie\Permission\Models\Role::pluck('name', 'name');
                            })
                            ->searchable()
                            ->visible(fn (Forms\Get $get): bool => $get('recipient_type') === 'role')
                            ->required(fn (Forms\Get $get): bool => $get('recipient_type') === 'role')
                            ->helperText('Only users with active Telegram registration will receive messages'),

                        Forms\Components\Textarea::make('message')
                            ->label('Message')
                            ->required()
                            ->rows(6)
                            ->placeholder('Enter your message here...')
                            ->columnSpanFull(),

                        Forms\Components\Toggle::make('send_immediately')
                            ->label('Send Immediately')
                            ->default(true)
                            ->helperText('If disabled, messages will be queued for later processing'),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('send')
                ->label('Send Message')
                ->color('primary')
                ->icon('heroicon-o-paper-airplane')
                ->action('sendMessage'),

            Actions\Action::make('cancel')
                ->label('Cancel')
                ->color('gray')
                ->url($this->getResource()::getUrl('index')),
        ];
    }

    public function sendMessage(): void
    {
        $data = $this->form->getState();
        $telegramService = app(TelegramService::class);
        $sender = Auth::user();

        try {
            $recipients = $this->getRecipients($data);

            if (empty($recipients)) {
                Notification::make()
                    ->title('No Recipients Found')
                    ->body('No users found with Telegram configuration.')
                    ->warning()
                    ->send();
                return;
            }

            $results = [];
            foreach ($recipients as $recipient) {
                // Kiểm tra xem user có telegram user active hoặc cấu hình legacy
                $hasActiveTelegram = $recipient->activeTelegramUser() !== null;
                $hasLegacyConfig = $recipient->hasTelegramBotConfigured() || $recipient->telegram_chat_id;

                if ($hasActiveTelegram || $hasLegacyConfig) {
                    $result = $telegramService->sendMessage($sender, $recipient, $data['message']);
                    $results[] = $result;
                }
            }

            $successCount = collect($results)->where('status', 'sent')->count();
            $failedCount = collect($results)->where('status', 'failed')->count();

            if ($successCount > 0) {
                Notification::make()
                    ->title('Messages Sent Successfully')
                    ->body("Successfully sent {$successCount} messages" . ($failedCount > 0 ? ", {$failedCount} failed" : ""))
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Failed to Send Messages')
                    ->body('All messages failed to send. Please check the logs for details.')
                    ->danger()
                    ->send();
            }

            $this->redirect($this->getResource()::getUrl('index'));

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('An error occurred while sending messages: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    private function getRecipients(array $data): array
    {
        switch ($data['recipient_type']) {
            case 'single':
                $user = User::find($data['recipient_user_id']);
                return $user ? [$user] : [];

            case 'multiple':
                return User::whereIn('id', $data['recipient_user_ids'])->get()->all();

            case 'role':
                return User::role($data['recipient_role'])
                    ->where(function ($query) {
                        $query->whereHas('telegramUsers', function ($q) {
                            $q->active();
                        })
                        ->orWhereNotNull('telegram_chat_id')
                        ->orWhereNotNull('telegram_bot_config');
                    })
                    ->get()
                    ->all();

            default:
                return [];
        }
    }
}
