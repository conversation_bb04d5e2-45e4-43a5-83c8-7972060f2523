<?php

namespace App\Filament\Admin\Resources\TelegramSendResource\Pages;

use App\Filament\Admin\Resources\TelegramSendResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTelegramSends extends ListRecords
{
    protected static string $resource = TelegramSendResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('send_message')
                ->label('Send Message')
                ->icon('heroicon-o-paper-airplane')
                ->color('primary')
                ->url(fn (): string => TelegramSendResource::getUrl('send')),
            Actions\CreateAction::make(),
        ];
    }
}
