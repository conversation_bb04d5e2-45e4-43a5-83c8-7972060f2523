<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\SellerBonusResource\Pages;
use App\Models\SellerBonus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SellerBonusResource extends Resource
{
    protected static ?string $model = SellerBonus::class;
    protected static ?string $navigationIcon = 'heroicon-o-gift';
    protected static ?string $navigationGroup = 'Seller Management';
    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Card::make()
                ->schema([
                    Forms\Components\Grid::make()
                        ->schema([
                            Forms\Components\Select::make('seller_id')
                                ->relationship('seller', 'name')
                                ->required()
                                ->searchable()
                                ->preload(),

                            Forms\Components\Select::make('bonus_type')
                                ->required()
                                ->options([
                                    'revenue_bonus' => 'Revenue Bonus (%)',
                                    'profit_bonus' => 'Profit Bonus (%)',
                                    'fixed_bonus' => 'Fixed Bonus ($)',
                                ])
                                ->live(),

                            Forms\Components\TextInput::make('bonus_rate')
                                ->required()
                                ->numeric()
                                ->step(0.01)
                                ->suffix(fn (Forms\Get $get) => 
                                    match ($get('bonus_type')) {
                                        'fixed_bonus' => '$',
                                        default => '%',
                                    }
                                ),

                            Forms\Components\TextInput::make('reason')
                                ->required()
                                ->maxLength(255),

                            Forms\Components\DatePicker::make('start_date')
                                ->required(),

                            Forms\Components\DatePicker::make('end_date')
                                ->after('start_date')
                                ->helperText('Leave empty for permanent bonus'),

                            Forms\Components\Toggle::make('is_active')
                                ->required()
                                ->inline(false)
                                ->onColor('success')
                                ->offColor('danger'),

                            Forms\Components\Textarea::make('description')
                                ->maxLength(65535)
                                ->columnSpanFull(),
                        ])
                        ->columns(2),
                ])
                ->columnSpan(['lg' => fn (?SellerBonus $record) => $record === null ? 3 : 2]),

            Forms\Components\Card::make()
                ->schema([
                    Forms\Components\Placeholder::make('created_at')
                        ->label('Created at')
                        ->content(fn (SellerBonus $record): ?string => $record->created_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('created_by_name')
                        ->label('Created by')
                        ->content(fn (SellerBonus $record): ?string => $record->creator?->name),

                    Forms\Components\Placeholder::make('approved_at')
                        ->label('Approved at')
                        ->content(fn (SellerBonus $record): ?string => $record->approved_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('approved_by_name')
                        ->label('Approved by')
                        ->content(fn (SellerBonus $record): ?string => $record->approver?->name),
                ])
                ->columnSpan(['lg' => 1])
                ->hidden(fn (?SellerBonus $record) => $record === null),
        ])
        ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('seller.name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('bonus_type')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'revenue_bonus' => 'success',
                        'profit_bonus' => 'warning',
                        'fixed_bonus' => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('bonus_rate')
                    ->sortable()
                    ->alignRight()
                    ->formatStateUsing(fn ($record) => 
                        match ($record->bonus_type) {
                            'fixed_bonus' => '$' . number_format($record->bonus_rate, 2),
                            default => number_format($record->bonus_rate, 2) . '%',
                        }
                    ),

                Tables\Columns\TextColumn::make('reason')
                    ->searchable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('start_date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable()
                    ->placeholder('Permanent'),

                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('approver.name')
                    ->label('Approved By')
                    ->searchable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('seller')
                    ->relationship('seller', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('bonus_type')
                    ->options([
                        'revenue_bonus' => 'Revenue Bonus',
                        'profit_bonus' => 'Profit Bonus',
                        'fixed_bonus' => 'Fixed Bonus',
                    ]),

                Tables\Filters\SelectFilter::make('is_active')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ])
                    ->label('Status'),

                Tables\Filters\Filter::make('active_period')
                    ->form([
                        Forms\Components\DatePicker::make('from_date'),
                        Forms\Components\DatePicker::make('until_date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from_date'],
                                fn (Builder $query, $date): Builder => $query->where('end_date', '>=', $date),
                            )
                            ->when(
                                $data['until_date'],
                                fn (Builder $query, $date): Builder => $query->where('start_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSellerBonuses::route('/'),
            'create' => Pages\CreateSellerBonus::route('/create'),
            'edit' => Pages\EditSellerBonus::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return static::getNavigationBadge() > 0 ? 'success' : 'gray';
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['seller.name', 'reason', 'description'];
    }
}