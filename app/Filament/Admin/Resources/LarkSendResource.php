<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\LarkSendResource\Pages;
use App\Models\LarkSend;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Tables\Filters\SelectFilter;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class LarkSendResource extends Resource
{
    use HasPageShield;

    protected static ?string $model = LarkSend::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-ellipsis';

    protected static ?string $navigationLabel = 'Lark Messages';

    protected static ?string $navigationGroup = 'Communication';

    protected static ?int $navigationSort = 2;

    protected static ?string $pluralModelLabel = 'Lark Messages';

    protected static ?string $modelLabel = 'Lark Message';

    public static function canAccess(): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('Sender')
                    ->relationship('sender', 'name', function ($query) {
                        // Admin có thể chọn bất kỳ user nào, user thường chỉ chọn chính họ
                        if (!auth()->user()->hasAnyRole(['super_admin', 'user_manager'])) {
                            $query->where('id', auth()->id());
                        }
                        return $query;
                    })
                    ->searchable()
                    ->preload()
                    ->required()
                    ->default(auth()->id())
                    ->disabled(fn (string $operation): bool => $operation === 'edit'),

                Forms\Components\Select::make('recipient_user_id')
                    ->label('Recipient')
                    ->relationship('recipient', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->disabled(fn (string $operation): bool => $operation === 'edit'),

                Forms\Components\Textarea::make('message')
                    ->label('Message')
                    ->required()
                    ->rows(4)
                    ->columnSpanFull()
                    ->disabled(fn (string $operation): bool => $operation === 'edit'),

                Forms\Components\Select::make('status')
                    ->label('Status')
                    ->options([
                        'pending' => 'Pending',
                        'sent' => 'Sent',
                        'failed' => 'Failed',
                    ])
                    ->required()
                    ->disabled(),

                Forms\Components\DateTimePicker::make('sent_at')
                    ->label('Sent At')
                    ->disabled(),

                Forms\Components\Textarea::make('error_message')
                    ->label('Error Message')
                    ->rows(2)
                    ->disabled()
                    ->visible(fn ($record) => $record && $record->status === 'failed'),

                Forms\Components\KeyValue::make('lark_response')
                    ->label('Lark Response')
                    ->disabled()
                    ->visible(fn ($record) => $record && $record->status === 'sent'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('sender.name')
                    ->label('Sender')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('recipient.name')
                    ->label('Recipient')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('message')
                    ->label('Message')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'sent',
                        'danger' => 'failed',
                    ])
                    ->icons([
                        'heroicon-o-clock' => 'pending',
                        'heroicon-o-check-circle' => 'sent',
                        'heroicon-o-x-circle' => 'failed',
                    ]),

                Tables\Columns\TextColumn::make('sent_at')
                    ->label('Sent At')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Not sent'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('error_message')
                    ->label('Error')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (!$state || strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    })
                    ->visible(fn ($record) => $record && $record->status === 'failed')
                    ->color('danger'),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'pending' => 'Pending',
                        'sent' => 'Sent',
                        'failed' => 'Failed',
                    ]),

                SelectFilter::make('sender')
                    ->label('Sender')
                    ->relationship('sender', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('recipient')
                    ->label('Recipient')
                    ->relationship('recipient', 'name')
                    ->searchable()
                    ->preload(),

                DateRangeFilter::make('created_at')
                    ->label('Ngày tạo')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->displayFormat('d/m/Y')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ]),

                DateRangeFilter::make('sent_at')
                    ->label('Ngày gửi')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->displayFormat('d/m/Y')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->visible(fn ($record) => static::canView($record)),

                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => static::canEdit($record)),
            ])
            ->bulkActions([
                // Không có bulk actions
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLarkSends::route('/'),
            'create' => Pages\CreateLarkSend::route('/create'),
            'edit' => Pages\EditLarkSend::route('/{record}/edit'),
            'send' => Pages\SendLarkMessage::route('/send'),
        ];
    }

}
