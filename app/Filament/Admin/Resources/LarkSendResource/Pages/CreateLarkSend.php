<?php

namespace App\Filament\Admin\Resources\LarkSendResource\Pages;

use App\Filament\Admin\Resources\LarkSendResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateLarkSend extends CreateRecord
{
    protected static string $resource = LarkSendResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Đảm bảo user_id được set là user hiện tại nếu không phải admin
        if (!auth()->user()->hasAnyRole(['super_admin', 'user_manager'])) {
            $data['user_id'] = auth()->id();
        }

        // Set default status
        if (empty($data['status'])) {
            $data['status'] = 'pending';
        }

        return $data;
    }
}
