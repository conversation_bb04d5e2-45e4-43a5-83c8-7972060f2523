<?php

namespace App\Filament\Admin\Resources\LarkSendResource\Pages;

use App\Filament\Admin\Resources\LarkSendResource;
use App\Models\User;
use App\Services\LarkService;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Auth;

class SendLarkMessage extends Page
{
    use InteractsWithFormActions;

    protected static string $resource = LarkSendResource::class;

    protected static string $view = 'filament.app.resources.lark-send-resource.pages.send-lark-message';

    protected static ?string $title = 'Send Lark Message';

    protected static ?string $navigationLabel = 'Send Message';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Send Lark Message')
                    ->description('Send messages to users via Lark')
                    ->schema([
                        Forms\Components\Select::make('send_to')
                            ->label('Send To')
                            ->options([
                                'specific_users' => 'Specific Users',
                                'all_users' => 'All Users with Lark Configuration',
                                'role_based' => 'Users by Role',
                                'team_based' => 'Users by Team',
                            ])
                            ->required()
                            ->reactive()
                            ->default('specific_users'),

                        Forms\Components\Select::make('recipient_users')
                            ->label('Recipients')
                            ->multiple()
                            ->searchable()
                            ->preload()
                            ->options(function () {
                                return User::whereNotNull('lark_bot_config')
                                    ->where('lark_bot_config', '!=', '{}')
                                    ->pluck('name', 'id');
                            })
                            ->visible(fn (callable $get) => $get('send_to') === 'specific_users')
                            ->required(fn (callable $get) => $get('send_to') === 'specific_users'),

                        Forms\Components\Select::make('role')
                            ->label('Role')
                            ->options([
                                'seller' => 'Sellers',
                                'Leader' => 'Leaders',
                                'Accountant' => 'Accountants',
                                'Fulfillment' => 'Fulfillment',
                                'Designer' => 'Designers',
                                'super_admin' => 'Super Admins',
                            ])
                            ->visible(fn (callable $get) => $get('send_to') === 'role_based')
                            ->required(fn (callable $get) => $get('send_to') === 'role_based'),

                        Forms\Components\Select::make('team_id')
                            ->label('Team')
                            ->relationship('team', 'name')
                            ->searchable()
                            ->preload()
                            ->visible(fn (callable $get) => $get('send_to') === 'team_based')
                            ->required(fn (callable $get) => $get('send_to') === 'team_based'),

                        Forms\Components\Textarea::make('message')
                            ->label('Message')
                            ->required()
                            ->rows(6)
                            ->placeholder('Enter your message here...')
                            ->visible(fn (callable $get) => $get('message_type') !== 'html')
                            ->columnSpanFull(),

                        Forms\Components\RichEditor::make('message')
                            ->label('HTML Content')
                            ->required()
                            ->placeholder('Nhập nội dung HTML...')
                            ->toolbarButtons([
                                'attachFiles',
                                'blockquote',
                                'bold',
                                'bulletList',
                                'codeBlock',
                                'h2',
                                'h3',
                                'italic',
                                'link',
                                'orderedList',
                                'redo',
                                'strike',
                                'underline',
                                'undo',
                            ])
                            ->helperText('Sử dụng editor để tạo nội dung rich text. HTML sẽ được tự động chuyển đổi sang định dạng Lark Markdown.')
                            ->visible(fn (callable $get) => $get('message_type') === 'html')
                            ->columnSpanFull(),

                        Forms\Components\FileUpload::make('image')
                            ->label('Attach Image (Optional)')
                            ->image()
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->maxSize(10240) // 10MB
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
                            ->helperText('Maximum file size: 10MB. Supported formats: JPEG, PNG, GIF, WebP')
                            ->columnSpanFull(),

                        Forms\Components\Select::make('message_type')
                            ->label('Message Type')
                            ->options([
                                'text' => 'Text Only (Webhook)',
                                'text_with_image' => 'Text + Image Link (Webhook)',
                                'rich_card' => 'Rich Card with Image (Webhook)',
                                'html' => 'HTML/Rich Text (Webhook)',
                            ])
                            ->default('text')
                            ->reactive()
                            ->helperText('Tất cả đều sử dụng Webhook URL. Ảnh sẽ được upload lên server và gửi link.'),

                        Forms\Components\Toggle::make('send_immediately')
                            ->label('Send Immediately')
                            ->default(true)
                            ->helperText('If disabled, messages will be queued for later processing'),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('send')
                ->label('Send Message')
                ->color('primary')
                ->icon('heroicon-o-paper-airplane')
                ->action('sendMessage'),

            Actions\Action::make('cancel')
                ->label('Cancel')
                ->color('gray')
                ->url($this->getResource()::getUrl('index')),
        ];
    }

    public function sendMessage(): void
    {
        $data = $this->form->getState();
        $larkService = app(LarkService::class);
        $sender = Auth::user();

        try {
            $recipients = $this->getRecipients($data);

            if (empty($recipients)) {
                Notification::make()
                    ->title('No Recipients Found')
                    ->body('No users found with Lark configuration.')
                    ->warning()
                    ->send();
                return;
            }

            // Validate message and image based on message type
            $validationResult = $this->validateMessageData($data);
            if (!$validationResult['valid']) {
                Notification::make()
                    ->title('Validation Error')
                    ->body($validationResult['error'])
                    ->danger()
                    ->send();
                return;
            }

            $results = [];
            foreach ($recipients as $recipient) {
                if ($recipient->hasLarkBotConfigured()) {
                    $result = $this->sendMessageToRecipient($larkService, $sender, $recipient, $data);
                    $results[] = $result;
                }
            }

            $successCount = collect($results)->where('status', 'sent')->count();
            $failedCount = collect($results)->where('status', 'failed')->count();

            if ($successCount > 0) {
                Notification::make()
                    ->title('Messages Sent Successfully')
                    ->body("Successfully sent {$successCount} messages" . ($failedCount > 0 ? ", {$failedCount} failed" : ''))
                    ->success()
                    ->send();
            }

            if ($failedCount > 0 && $successCount === 0) {
                Notification::make()
                    ->title('Failed to Send Messages')
                    ->body("Failed to send {$failedCount} messages")
                    ->danger()
                    ->send();
            }

            $this->redirect($this->getResource()::getUrl('index'));

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('An error occurred while sending messages: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    private function validateMessageData(array $data): array
    {
        $messageType = $data['message_type'] ?? 'text';
        $message = $data['message'] ?? '';
        $image = $data['image'] ?? null;

        switch ($messageType) {
            case 'text':
                if (empty($message)) {
                    return ['valid' => false, 'error' => 'Nội dung tin nhắn là bắt buộc.'];
                }
                break;

            case 'text_with_image':
                if (empty($message)) {
                    return ['valid' => false, 'error' => 'Nội dung tin nhắn là bắt buộc khi gửi kèm ảnh.'];
                }
                if (empty($image)) {
                    return ['valid' => false, 'error' => 'Ảnh là bắt buộc cho loại tin nhắn này.'];
                }
                break;

            case 'rich_card':
                if (empty($message) && empty($image)) {
                    return ['valid' => false, 'error' => 'Cần có ít nhất nội dung tin nhắn hoặc ảnh cho Rich Card.'];
                }
                break;

            case 'html':
                if (empty($message)) {
                    return ['valid' => false, 'error' => 'HTML content là bắt buộc.'];
                }
                break;
        }

        return ['valid' => true];
    }

    private function sendMessageToRecipient(LarkService $larkService, $sender, $recipient, array $data)
    {
        $messageType = $data['message_type'] ?? 'text';
        $message = $data['message'] ?? '';
        $imagePath = $data['image'] ?? null;

        // Convert image path to UploadedFile if exists
        $imageFile = null;
        if ($imagePath) {
            $fullPath = storage_path('app/public/' . $imagePath);
            if (file_exists($fullPath)) {
                $imageFile = new \Illuminate\Http\UploadedFile(
                    $fullPath,
                    basename($imagePath),
                    mime_content_type($fullPath),
                    null,
                    true
                );
            }
        }

        switch ($messageType) {
            case 'text':
                return $larkService->sendMessage($sender, $recipient, $message);

            case 'text_with_image':
                return $larkService->sendMessage($sender, $recipient, $message, $imageFile);

            case 'rich_card':
                return $larkService->sendRichMessage($sender, $recipient, $message, $imageFile);

            case 'html':
                return $larkService->sendHtmlMessage($sender, $recipient, $message, $imageFile);
        }

        // Fallback to text message
        return $larkService->sendMessage($sender, $recipient, $message);
    }

    private function getRecipients(array $data): array
    {
        switch ($data['send_to']) {
            case 'specific_users':
                return User::whereIn('id', $data['recipient_users'] ?? [])->get()->all();

            case 'all_users':
                return User::whereNotNull('lark_bot_config')
                    ->where('lark_bot_config', '!=', '{}')
                    ->get()->all();

            case 'role_based':
                return User::role($data['role'])
                    ->whereNotNull('lark_bot_config')
                    ->where('lark_bot_config', '!=', '{}')
                    ->get()->all();

            case 'team_based':
                return User::whereHas('teams', function ($query) use ($data) {
                        $query->where('team_id', $data['team_id']);
                    })
                    ->whereNotNull('lark_bot_config')
                    ->where('lark_bot_config', '!=', '{}')
                    ->get()->all();

            default:
                return [];
        }
    }
}
