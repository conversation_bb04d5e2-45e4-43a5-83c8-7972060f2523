<?php

namespace App\Filament\Admin\Resources\LarkSendResource\Pages;

use App\Filament\Admin\Resources\LarkSendResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLarkSends extends ListRecords
{
    protected static string $resource = LarkSendResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('send_message')
                ->label('Send Message')
                ->icon('heroicon-o-paper-airplane')
                ->color('primary')
                ->url(fn (): string => LarkSendResource::getUrl('send')),
            Actions\CreateAction::make(),
        ];
    }
}
