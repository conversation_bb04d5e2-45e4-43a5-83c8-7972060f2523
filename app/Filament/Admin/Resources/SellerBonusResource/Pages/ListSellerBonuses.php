<?php

namespace App\Filament\Admin\Resources\SellerBonusResource\Pages;

use App\Filament\Admin\Resources\SellerBonusResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSellerBonuses extends ListRecords
{
    protected static string $resource = SellerBonusResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
