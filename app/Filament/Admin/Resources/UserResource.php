<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\UserResource\Pages;
use App\Models\User;
use App\Models\Team;
use App\Models\SellerLevel;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;
use Spatie\Permission\Models\Role;
use Filament\Facades\Filament;
use App\Services\TelegramService;
use App\Services\LarkService;
use Filament\Notifications\Notification;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    
    protected static ?int $navigationSort = 1;

    /**
     * Helper method to safely check roles with null checking and exception handling
     */
    private static function hasRoleSafely($record, $role): bool
    {
        if (!$record) {
            return false;
        }
        
        try {
            if (is_array($role)) {
                foreach ($role as $r) {
                    if ($record->hasRole($r)) {
                        return true;
                    }
                }
                return false;
            }
            
            return $record->hasRole($role);
        } catch (\Exception $e) {
            return false;
        }
    }

    public static function getNavigationGroup(): ?string
    {
        return __('filament-shield::filament-shield.nav.group');
    }
    
    public static function getNavigationLabel(): string
    {
        return __('Users');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('User Information')
                    ->tabs([
                        Tabs\Tab::make('Basic Information')
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('email')
                                    ->email()
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('password')
                                    ->password()
                                    ->dehydrated(fn($state) => filled($state))
                                    ->required(fn(string $context): bool => $context === 'create')
                                    ->maxLength(255),
                                Forms\Components\Select::make('roles')
                                    ->relationship('roles', 'name', function ($query) {
                                        if (Filament::auth()->id() === 1) {
                                            return $query;
                                        }
                                        return $query->whereNot('id', 1);
                                    })
                                    ->multiple()
                                    ->preload()
                                    ->searchable()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $record, $set) {
                                        if (!$record) return;
                                        
                                        $roles = collect($state)->map(fn($id) => Role::find($id)?->name);
                                        
                                        // Check if user is a seller
                                        $isSeller = $roles->contains('seller');
                                        
                                        // Set default seller level for new sellers
                                        if ($isSeller && $record && !$record->seller_level_id) {
                                            $defaultLevel = SellerLevel::where('code', 'JUNIOR')->first();
                                            if ($defaultLevel) {
                                                $record->seller_level_id = $defaultLevel->id;
                                                $record->save();
                                            }
                                        }
                                    }),
                            ])->columns(2),
                            
                        Tabs\Tab::make('Contact Information')
                            ->schema([
                                Forms\Components\TextInput::make('phone_number')
                                    ->tel()
                                    ->maxLength(20),
                                Forms\Components\TextInput::make('telegram_id')
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('telegram_chat_id')
                                    ->label('Telegram Chat ID')
                                    ->maxLength(255),
                                FileUpload::make('avatar_url')
                                    ->label('Avatar')
                                    ->image()
                                    ->disk('s3')
                                    ->directory('avatars')
                                    ->visibility('public')
                                    ->imageResizeMode('contain')
                                    ->imageResizeTargetWidth('400')
                                    ->imageResizeTargetHeight('400'),
                            ])->columns(2),

                        Tabs\Tab::make('API Token')
                            ->schema([
                                Section::make('API Token Management')
                                    ->description('Manage API token for this user to access API endpoints')
                                    ->schema([
                                        Forms\Components\TextInput::make('api_token')
                                            ->label('API Token')
                                            ->placeholder('Token will be generated automatically')
                                            ->disabled()
                                            ->helperText('This token is used for API authentication. Keep it secure!')
                                            ->suffixActions([
                                                Forms\Components\Actions\Action::make('generate_token')
                                                    ->label('Generate New Token')
                                                    ->icon('heroicon-o-arrow-path')
                                                    ->color('success')
                                                    ->requiresConfirmation()
                                                    ->modalHeading('Generate New API Token')
                                                    ->modalDescription('This will generate a new API token and invalidate the current one. Are you sure?')
                                                    ->modalSubmitActionLabel('Generate Token')
                                                    ->action(function ($record, $set) {
                                                        if ($record) {
                                                            $token = $record->generateApiToken();
                                                            $set('api_token', $token);

                                                            Notification::make()
                                                                ->success()
                                                                ->title('Token Generated')
                                                                ->body('New API token has been generated successfully.')
                                                                ->send();
                                                        }
                                                    })
                                                    ->visible(fn (string $context): bool => $context === 'edit'),

                                                Forms\Components\Actions\Action::make('copy_token')
                                                    ->label('Copy')
                                                    ->icon('heroicon-o-clipboard')
                                                    ->color('gray')
                                                    ->action(function ($record) {
                                                        if ($record && $record->api_token) {
                                                            // JavaScript will handle the copy action
                                                            Notification::make()
                                                                ->success()
                                                                ->title('Token Copied')
                                                                ->body('API token has been copied to clipboard.')
                                                                ->send();
                                                        }
                                                    })
                                                    ->extraAttributes([
                                                        'onclick' => 'navigator.clipboard.writeText(this.closest(".fi-input-wrp").querySelector("input").value)'
                                                    ])
                                                    ->visible(fn (string $context, $record): bool => $context === 'edit' && $record && $record->api_token),
                                            ])
                                            ->columnSpanFull(),

                                        Forms\Components\Placeholder::make('api_usage_info')
                                            ->label('API Usage Information')
                                            ->content(new HtmlString('
                                                <div class="space-y-2 text-sm">
                                                    <p><strong>How to use this token:</strong></p>
                                                    <ul class="list-disc list-inside space-y-1 ml-4">
                                                        <li>Include in Authorization header: <code class="bg-gray-100 px-1 rounded">Authorization: Bearer YOUR_TOKEN</code></li>
                                                        <li>Or as query parameter: <code class="bg-gray-100 px-1 rounded">?api_token=YOUR_TOKEN</code></li>
                                                    </ul>
                                                    <p class="text-amber-600"><strong>Security Note:</strong> Keep this token secure and do not share it publicly.</p>
                                                </div>
                                            '))
                                            ->columnSpanFull(),
                                    ])
                                    ->columns(1),
                            ])
                            ->hidden(fn (string $context): bool => $context === 'create'),

                        Tabs\Tab::make('Telegram Configuration')
                            ->schema([
                                Section::make('Telegram Bot Configuration')
                                    ->description('Configure Telegram bot settings for this user')
                                    ->schema([
                                        Forms\Components\TextInput::make('telegram_bot_config.bot_token')
                                            ->label('Bot Token')
                                            ->placeholder('123456789:ABCdefGHIjklMNOpqrsTUVwxyz')
                                            ->helperText('Get this from @BotFather on Telegram')
                                            ->password()
                                            ->revealable(),

                                        Forms\Components\TextInput::make('telegram_bot_config.chat_id')
                                            ->label('Chat ID')
                                            ->placeholder('-1001234567890 or 123456789')
                                            ->helperText('The chat ID where messages will be sent'),

                                        Forms\Components\Toggle::make('telegram_bot_config.enabled')
                                            ->label('Enable Telegram Bot')
                                            ->default(false)
                                            ->helperText('Enable or disable Telegram notifications for this user'),

                                        Forms\Components\Textarea::make('telegram_bot_config.notes')
                                            ->label('Notes')
                                            ->placeholder('Additional notes about this Telegram configuration')
                                            ->rows(3)
                                            ->columnSpanFull(),
                                    ])
                                    ->columns(2),

                                Section::make('Test Configuration')
                                    ->description('Test the Telegram bot configuration')
                                    ->schema([
                                        Forms\Components\Placeholder::make('test_info')
                                            ->label('Test Bot Configuration')
                                            ->content('Use the "Test Bot" action to verify that the bot token and chat ID are working correctly.')
                                            ->columnSpanFull(),
                                    ])
                                    ->hidden(fn (string $context): bool => $context === 'create'),
                            ]),

                        Tabs\Tab::make('Lark Configuration')
                            ->schema([
                                Section::make('Lark Bot Configuration')
                                    ->description('Configure Lark (Feishu) bot settings for this user')
                                    ->schema([
                                        Forms\Components\TextInput::make('lark_bot_config.webhook_url')
                                            ->label('Webhook URL (Recommended)')
                                            ->placeholder('https://open.larksuite.com/open-apis/bot/v2/hook/...')
                                            ->helperText('Simple method: Get webhook URL from Lark bot settings')
                                            ->url()
                                            ->columnSpanFull(),

                                        Forms\Components\TextInput::make('lark_bot_config.app_id')
                                            ->label('App ID')
                                            ->placeholder('cli_xxxxxxxxxxxxxxxxx')
                                            ->helperText('Get this from Lark Developer Console'),

                                        Forms\Components\TextInput::make('lark_bot_config.app_secret')
                                            ->label('App Secret')
                                            ->placeholder('xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
                                            ->helperText('Get this from Lark Developer Console')
                                            ->password()
                                            ->revealable(),

                                        Forms\Components\TextInput::make('lark_bot_config.chat_id')
                                            ->label('Chat ID')
                                            ->placeholder('oc_xxxxxxxxxxxxxxxxx')
                                            ->helperText('The chat ID where messages will be sent'),

                                        Forms\Components\Toggle::make('lark_bot_config.enabled')
                                            ->label('Enable Lark Bot')
                                            ->default(false)
                                            ->helperText('Enable or disable Lark notifications for this user'),

                                        Forms\Components\Textarea::make('lark_bot_config.notes')
                                            ->label('Notes')
                                            ->placeholder('Additional notes about this Lark configuration')
                                            ->rows(3)
                                            ->columnSpanFull(),
                                    ])
                                    ->columns(2),

                                Section::make('Test Configuration')
                                    ->description('Test the Lark bot configuration')
                                    ->schema([
                                        Forms\Components\Placeholder::make('test_lark_info')
                                            ->label('Test Bot Configuration')
                                            ->content('Use the "Test Lark Bot" action to verify that the app credentials and chat ID are working correctly.')
                                            ->columnSpanFull(),
                                    ])
                                    ->hidden(fn (string $context): bool => $context === 'create'),
                            ]),

                        Tabs\Tab::make('Commission Settings')
                            ->schema([
                                Section::make('Commission Type')
                                    ->description('Configure how commission is calculated for this seller')
                                    ->schema([
                                        Forms\Components\Group::make([
                                            Toggle::make('has_fixed_commission')
                                                ->label('Use Fixed Commission Rate')
                                                ->helperText('If enabled, the seller will receive a fixed percentage regardless of revenue tiers')
                                                ->default(false)
                                                ->live()
                                                ->onColor('success')
                                                ->offColor('danger'),
                                            
                                            Forms\Components\Card::make()
                                                ->schema([
                                                    Forms\Components\TextInput::make('fixed_commission_rate')
                                                        ->label('Fixed Commission Rate (%)')
                                                        ->numeric()
                                                        ->default(20.00)
                                                        ->required()
                                                        ->rules(['numeric', 'min:0', 'max:100'])
                                                        ->suffix('%')
                                                        ->step(0.01)
                                                        ->columnSpan('full'),
                                                    
                                                    Forms\Components\Placeholder::make('fixed_rate_explanation')
                                                        ->label('What is Fixed Commission?')
                                                        ->content('Fixed commission means the seller will receive a set percentage of sales regardless of their revenue tier or performance metrics.')
                                                        ->columnSpan('full'),
                                                ])
                                                ->visible(fn (callable $get): bool => (bool) $get('has_fixed_commission'))
                                                ->columns(1),
                                                
                                            Forms\Components\Placeholder::make('variable_rate_explanation')
                                                ->label('Variable Commission Information')
                                                ->content('When fixed commission is disabled, the seller\'s commission rate will be determined by their revenue tier and level.')
                                                ->hidden(fn (callable $get): bool => (bool) $get('has_fixed_commission')),
                                        ])->columnSpan('full'),
                                    ]),
                                    
                                Section::make('Salary Configuration')
                                    ->description('Configure whether this seller receives a base salary')
                                    ->schema([
                                        Forms\Components\Group::make([
                                            Toggle::make('has_base_salary')
                                                ->label('Receives Base Salary')
                                                ->helperText('If disabled, the seller will only receive commission without a base salary')
                                                ->default(true)
                                                ->live()
                                                ->onColor('success')
                                                ->offColor('danger'),
                                                
                                            Forms\Components\Placeholder::make('base_salary_explanation')
                                                ->label('What is Base Salary?')
                                                ->content('Base salary is a fixed amount paid to the seller regardless of their sales performance. When disabled, the seller will only earn from commissions.')
                                                ->columnSpan('full'),
                                        ])->columnSpan('full'),
                                    ]),
                                    
                                Section::make('Commission Summary')
                                    ->description('Summary of commission settings for this user')
                                    ->schema([
                                        Forms\Components\Placeholder::make('commission_summary')
                                            ->content(function ($record) {
                                                if (!$record) return 'No commission data available';
                                                
                                                $summary = '';
                                                
                                                // Seller Level
                                                if ($record->sellerLevel) {
                                                    $summary .= "• Level: {$record->sellerLevel->name}\n";
                                                }
                                                
                                                // Commission Type
                                                if ($record->has_fixed_commission) {
                                                    $summary .= "• Fixed commission rate: {$record->fixed_commission_rate}%\n";
                                                } else {
                                                    $summary .= "• Variable commission based on revenue tiers\n";
                                                }
                                                
                                                // Salary Information
                                                $summary .= "• Base salary: " . ($record->has_base_salary ? 'Yes' : 'No') . "\n";
                                                
                                                return $summary;
                                            }),
                                    ])
                                    ->hidden(fn (string $context): bool => $context === 'create'),
                                        ]),
                            
                        Tabs\Tab::make('Payment Information')
                            ->schema([
                                Forms\Components\TextInput::make('bank_account_number')
                                    ->label('Bank Account Number')
                                    ->maxLength(100),
                                Forms\Components\TextInput::make('bank_name')
                                    ->label('Bank Name')
                                    ->maxLength(100),
                                FileUpload::make('qr_code_image')
                                    ->label('Payment QR Code')
                                    ->image()
                                    ->disk('s3')
                                    ->directory('qr-codes')
                                    ->visibility('public'),
                            ])->columns(2),

                        Tabs\Tab::make('Team Settings')
                            ->schema([
                                Section::make('Team Management')
                                    ->description('Assign user to teams and manage team relationships')
                                    ->schema([
                                        Select::make('teams')
                                            ->label('Teams')
                                            ->relationship('teams', 'name')
                                            ->multiple()
                                            ->preload()
                                            ->searchable()
                                            ->helperText('Select teams this user belongs to')
                                            ->getOptionLabelFromRecordUsing(fn ($record) =>
                                                "{$record->name} (Commission: {$record->commission_rate}%)"
                                            )
                                            ->columnSpanFull(),


                                    ])
                                    ->columns(1),

                                Section::make('Team Information')
                                    ->description('Current team memberships and leadership roles')
                                    ->schema([
                                        Forms\Components\Placeholder::make('team_summary')
                                            ->label('Team Summary')
                                            ->content(function ($record) {
                                                if (!$record) return 'No team data available';

                                                $summary = '';

                                                // Team memberships
                                                if ($record->teams && $record->teams->count() > 0) {
                                                    $summary .= "• Member of teams: " . $record->teams->pluck('name')->implode(', ') . "\n";
                                                } else {
                                                    $summary .= "• Not a member of any team\n";
                                                }



                                                // Managed sellers (if leader)
                                                if (self::hasRoleSafely($record, 'Leader')) {
                                                    $managedSellers = $record->leaderManagedSellers()->count();
                                                    $summary .= "• Managing {$managedSellers} sellers\n";
                                                }

                                                return $summary ?: 'No team information available';
                                            })
                                            ->columnSpanFull(),
                                    ])
                                    ->hidden(fn (string $context): bool => $context === 'create'),
                            ])
                            ->visible(fn ($record) => self::hasRoleSafely($record, ['super_admin', 'Leader', 'Seller'])),
                            
                        Tabs\Tab::make('Seller Settings')
                            ->schema([
                                Section::make('Seller Level')
                                    ->description('Configure the seller level which affects commission rates')
                                    ->schema([
                                        Select::make('seller_level_id')
                                            ->label('Seller Level')
                                            ->relationship('sellerLevel', 'name')
                                            ->preload()
                                            ->searchable(),
                                    ]),
                            ])
                            ->visible(fn ($record) => self::hasRoleSafely($record, 'seller')),
                            
                        Tabs\Tab::make('Access Management')
                            ->schema([
                                Section::make('Leader Management')
                                    ->schema([
                                        Select::make('leader_managed_sellers')
                                            ->label('Managed Sellers')
                                            ->relationship('leaderManagedSellersRelation', 'name')
                                            ->multiple()
                                            ->preload()
                                            ->searchable()
                                            ->afterStateUpdated(function ($state, $record) {
                                                if (!$record) return;
                                                $record->leaderManagedSellersRelation()->detach();
                                                $record->leaderManagedSellersRelation()->attach($state, ['role_type' => 'Leader']);
                                            }),
                                    ])
                                    ->visible(fn ($record) => self::hasRoleSafely($record, 'Leader')),

                                Section::make('Fulfillment Management')
                                    ->schema([
                                        Select::make('fulfillment_managed_sellers')
                                            ->label('Managed Sellers')
                                            ->relationship('fulfillmentManagedSellers', 'name')
                                            ->multiple()
                                            ->preload()
                                            ->searchable()
                                            ->afterStateUpdated(function ($state, $record) {
                                                if (!$record) return;
                                                $record->fulfillmentManagedSellers()->detach();
                                                $record->fulfillmentManagedSellers()->attach($state, ['role_type' => 'Fulfillment']);
                                            }),
                                    ])
                                    ->visible(fn ($record) => self::hasRoleSafely($record, 'Fulfillment')),



                                Section::make('Accountant Management')
                                    ->schema([
                                        Select::make('accountant_managed_sellers')
                                            ->label('Managed Sellers')
                                            ->relationship('accountantManagedSellersRelation', 'name')
                                            ->multiple()
                                            ->preload()
                                            ->searchable()
                                            ->afterStateUpdated(function ($state, $record) {
                                                if (!$record) return;
                                                $record->accountantManagedSellersRelation()->detach();
                                                $record->accountantManagedSellersRelation()->attach($state, ['role_type' => 'Accountant']);
                                            }),
                                    ])
                                    ->visible(fn ($record) => self::hasRoleSafely($record, 'Accountant')),
                            ])
                            ->hidden(fn (string $context): bool => $context === 'create')
                            ->visible(fn ($record) => self::hasRoleSafely($record, ['super_admin', 'Leader', 'Fulfillment', 'Accountant'])),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->getStateUsing(function ($record) {
                        if (!$record) {
                            return null;
                        }
                        return $record->name;
                    })
                    ->searchable()
                    ->sortable(),
                    
                Tables\Columns\ImageColumn::make('avatar_url')
                    ->label('Avatar')
                    ->circular()
                    ->defaultImageUrl(fn ($record) => $record ? 'https://api.dicebear.com/9.x/lorelei-neutral/svg?seed=' . urlencode($record->name ?? '') : null)
                    ->toggleable(),
                    
                Tables\Columns\TextColumn::make('email')
                    ->getStateUsing(function ($record) {
                        if (!$record) {
                            return null;
                        }
                        return $record->email;
                    })
                    ->searchable()
                    ->sortable(),

                  Tables\Columns\TextColumn::make('last_activity')
                    ->label('Last Activity')
                    ->getStateUsing(function ($record) {
                        if (!$record || !$record->last_activity) {
                            return 'Never';
                        }
                        return $record->last_activity->diffForHumans();
                    })
                    ->badge()
                    ->color(function ($record) {
                        if (!$record || !$record->last_activity) {
                            return 'gray';
                        }

                        $lastActivity = $record->last_activity;
                        $now = now();

                        if ($lastActivity->diffInMinutes($now) <= 5) {
                            return 'success'; // Online (within 5 minutes)
                        } elseif ($lastActivity->diffInHours($now) <= 1) {
                            return 'warning'; // Recently active (within 1 hour)
                        } elseif ($lastActivity->diffInDays($now) <= 1) {
                            return 'info'; // Active today
                        } else {
                            return 'gray'; // Inactive
                        }
                    })
                    ->tooltip(function ($record) {
                        if (!$record || !$record->last_activity) {
                            return 'User has never been active';
                        }
                        return 'Last seen: ' . $record->last_activity->format('M j, Y \a\t g:i A');
                    })
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('roles.name')
                    ->badge()
                    ->label(__('Role'))
                    ->colors(['primary'])
                    ->getStateUsing(function ($record) {
                        if (!$record || !$record->roles || $record->roles->isEmpty()) {
                            return null;
                        }
                        return $record->roles->pluck('name')->toArray();
                    })
                    ->searchable(),
                    
                Tables\Columns\TextColumn::make('sellerLevel.name')
                    ->label('Level')
                    ->getStateUsing(function ($record) {
                        if (!$record || !$record->sellerLevel) {
                            return null;
                        }
                        return $record->sellerLevel->name;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('teams.name')
                    ->label('Teams')
                    ->badge()
                    ->getStateUsing(function ($record) {
                        if (!$record || !$record->teams || $record->teams->isEmpty()) {
                            return ['No Team'];
                        }
                        return $record->teams->pluck('name')->toArray();
                    })
                    ->color(function ($record) {
                        if (!$record || !$record->teams || $record->teams->isEmpty()) {
                            return 'danger';
                        }
                        return 'info';
                    })
                    ->searchable()
                    ->toggleable(),
                    
                Tables\Columns\TextColumn::make('phone_number')
                    ->label('Phone')
                    ->getStateUsing(function ($record) {
                        if (!$record) {
                            return null;
                        }
                        return $record->phone_number;
                    })
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                    
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->getStateUsing(function ($record) {
                        if (!$record) {
                            return null;
                        }
                        return $record->created_at;
                    })
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('no_team')
                    ->label('🚫 Users without team')
                    ->query(fn (Builder $query): Builder => $query->whereDoesntHave('teams'))
                    ->toggle(),

                Tables\Filters\SelectFilter::make('role')
                    ->label('Role')
                    ->relationship('roles', 'name')
                    ->preload()
                    ->multiple(),

                Tables\Filters\SelectFilter::make('seller_level')
                    ->label('Seller Level')
                    ->relationship('sellerLevel', 'name')
                    ->preload(),

                Tables\Filters\SelectFilter::make('team')
                    ->label('Team')
                    ->relationship('teams', 'name')
                    ->preload()
                    ->multiple(),

                Tables\Filters\TernaryFilter::make('has_team')
                    ->label('Team Status')
                    ->placeholder('All users')
                    ->trueLabel('Has team')
                    ->falseLabel('No team')
                    ->queries(
                        true: fn (Builder $query) => $query->whereHas('teams'),
                        false: fn (Builder $query) => $query->whereDoesntHave('teams'),
                    ),

                Tables\Filters\TernaryFilter::make('has_fixed_commission')
                    ->label('Fixed Commission')
                    ->queries(
                        true: fn (Builder $query) => $query->where('has_fixed_commission', true),
                        false: fn (Builder $query) => $query->where(function($q) {
                            $q->where('has_fixed_commission', false)
                              ->orWhereNull('has_fixed_commission');
                        })
                    ),
                    
                Tables\Filters\TernaryFilter::make('has_qr_code')
                    ->label('Has QR Code')
                    ->queries(
                        true: fn (Builder $query) => $query->whereNotNull('qr_code_image'),
                        false: fn (Builder $query) => $query->whereNull('qr_code_image'),
                    ),
                    
                Tables\Filters\SelectFilter::make('managed_by')
                    ->label('Managed By')
                    ->relationship('managers', 'name')
                    ->multiple()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Impersonate::make(),
                Tables\Actions\Action::make('test_telegram_bot')
                    ->label('Test Bot')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->color('info')
                    ->visible(fn ($record) => !empty($record->telegram_bot_config['bot_token']))
                    ->action(function ($record) {
                        $telegramService = app(TelegramService::class);
                        $botToken = $record->telegram_bot_config['bot_token'] ?? null;

                        if (!$botToken) {
                            Notification::make()
                                ->title('No Bot Token')
                                ->body('Please configure a bot token first.')
                                ->warning()
                                ->send();
                            return;
                        }

                        $result = $telegramService->testBotConnection($botToken);

                        if ($result['success']) {
                            $botInfo = $result['data']['result'];
                            Notification::make()
                                ->title('Bot Test Successful')
                                ->body("Bot '{$botInfo['first_name']}' (@{$botInfo['username']}) is working correctly!")
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Bot Test Failed')
                                ->body($result['error'])
                                ->danger()
                                ->send();
                        }
                    }),
                Tables\Actions\Action::make('test_lark_bot')
                    ->label('Test Lark Bot')
                    ->icon('heroicon-o-chat-bubble-left-ellipsis')
                    ->color('success')
                    ->visible(fn ($record) => !empty($record->lark_bot_config['app_id']) && !empty($record->lark_bot_config['app_secret']))
                    ->action(function ($record) {
                        $larkService = app(LarkService::class);
                        $appId = $record->lark_bot_config['app_id'] ?? null;
                        $appSecret = $record->lark_bot_config['app_secret'] ?? null;

                        if (!$appId || !$appSecret) {
                            Notification::make()
                                ->title('No App Credentials')
                                ->body('Please configure App ID and App Secret first.')
                                ->warning()
                                ->send();
                            return;
                        }

                        $result = $larkService->testBotConnection($appId, $appSecret);

                        if ($result['success']) {
                            $botInfo = $result['data']['bot'] ?? [];
                            $botName = $botInfo['app_name'] ?? 'Unknown Bot';
                            Notification::make()
                                ->title('Lark Bot Test Successful')
                                ->body("Bot '{$botName}' is working correctly!")
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Lark Bot Test Failed')
                                ->body($result['error'])
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Eager load relationships to avoid N+1 queries
        $query->with(['roles', 'sellerLevel', 'teams']);

        // Don't allow regular users to view or edit the super admin
        if (Filament::auth()->id() !== 1) {
            return $query->whereNot('id', 1);
        }

        return $query;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    /**
     * Mutate form data before it is saved to the database.
     */
    public static function mutateFormDataBeforeSave(array $data, $record = null): array
    {
        // Handle fixed commission rate
        if (isset($data['has_fixed_commission'])) {
            if ($data['has_fixed_commission'] && !isset($data['fixed_commission_rate'])) {
                // If fixed commission is enabled but no rate provided, use existing or default
                $data['fixed_commission_rate'] = $record?->fixed_commission_rate ?? 20.00;
            } elseif (!$data['has_fixed_commission']) {
                // If fixed commission is disabled, clear the rate (make it nullable)
                $data['fixed_commission_rate'] = null;
            }
        }
        
        // Ensure has_base_salary is properly set with default value if not provided
        if (!isset($data['has_base_salary'])) {
            $data['has_base_salary'] = $record?->has_base_salary ?? true;
        }
        
        return $data;
    }
}
