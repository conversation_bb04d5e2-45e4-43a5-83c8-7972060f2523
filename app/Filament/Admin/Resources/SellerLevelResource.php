<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\SellerLevelResource\Pages;
use App\Filament\Admin\Resources\SellerLevelResource\RelationManagers;
use App\Models\SellerLevel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Colors\Color;

class SellerLevelResource extends Resource
{
    protected static ?string $model = SellerLevel::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-trending-up';
    protected static ?string $navigationGroup = 'Seller Management';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Card::make()
                ->schema([
                    Forms\Components\Grid::make()
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->required()
                                ->maxLength(255),

                            Forms\Components\TextInput::make('code')
                                ->required()
                                ->maxLength(255)
                                ->unique(ignoreRecord: true),

                            Forms\Components\TextInput::make('base_salary')
                                ->required()
                                ->numeric()
                                ->prefix('$')
                                ->step(0.01),

                            Forms\Components\TextInput::make('bonus_rate')
                                ->required()
                                ->numeric()
                                ->suffix('%')
                                ->step(0.01),

                            Forms\Components\Toggle::make('is_active')
                                ->required()
                                ->inline(false)
                                ->onColor('success')
                                ->offColor('danger'),

                            Forms\Components\Textarea::make('description')
                                ->maxLength(65535)
                                ->columnSpanFull(),
                        ])
                        ->columns(2),
                ])
                ->columnSpan(['lg' => fn (?SellerLevel $record) => $record === null ? 3 : 2]),

            Forms\Components\Card::make()
                ->schema([
                    Forms\Components\Placeholder::make('created_at')
                        ->label('Created at')
                        ->content(fn (SellerLevel $record): ?string => $record->created_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('updated_at')
                        ->label('Last modified at')
                        ->content(fn (SellerLevel $record): ?string => $record->updated_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('sellers_count')
                        ->label('Number of Sellers')
                        ->content(fn (SellerLevel $record): string => $record->sellers()->count() . ' sellers'),
                ])
                ->columnSpan(['lg' => 1])
                ->hidden(fn (?SellerLevel $record) => $record === null),
        ])
        ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn (SellerLevel $record): string => match($record->code) {
                        'JUNIOR' => 'info',
                        'SENIOR' => 'warning',
                        'EXPERT' => 'success',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('base_salary')
                    ->money('USD')
                    ->sortable()
                    ->alignRight(),

                Tables\Columns\TextColumn::make('bonus_rate')
                    ->suffix('%')
                    ->sortable()
                    ->alignRight(),

                Tables\Columns\TextColumn::make('sellers_count')
                    ->counts('sellers')
                    ->label('Sellers')
                    ->sortable()
                    ->alignRight(),

                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable()
                    ->tooltip(fn (SellerLevel $record): string => $record->updated_at->format('Y-m-d H:i:s')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('is_active')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ])
                    ->label('Status'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading('Delete Level')
                    ->modalDescription('Are you sure you want to delete this level? This action cannot be undone if there are sellers assigned to this level.')
                    ->modalSubmitActionLabel('Yes, delete it')
                    ->disabled(fn (SellerLevel $record) => $record->sellers()->exists()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalDescription('Are you sure you want to delete these levels? This action cannot be undone for levels that have sellers assigned.')
                        ->deselectRecordsAfterCompletion(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSellerLevels::route('/'),
            'create' => Pages\CreateSellerLevel::route('/create'),
            'edit' => Pages\EditSellerLevel::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return static::getModel()::where('is_active', true)->count() > 0 
            ? 'success' 
            : 'danger';
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'code', 'description'];
    }
}
