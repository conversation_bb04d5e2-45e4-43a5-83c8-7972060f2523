<?php

namespace App\Filament\Admin\Resources\TelegramUserResource\Pages;

use App\Filament\Admin\Resources\TelegramUserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTelegramUsers extends ListRecords
{
    protected static string $resource = TelegramUserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
