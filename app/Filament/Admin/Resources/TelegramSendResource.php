<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TelegramSendResource\Pages;
use App\Filament\Admin\Resources\TelegramSendResource\RelationManagers;
use App\Models\TelegramSend;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class TelegramSendResource extends Resource{

    public static function canAccess(): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    protected static ?string $model = TelegramSend::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationLabel = 'Telegram Messages';

    protected static ?string $navigationGroup = 'Communication';

    protected static ?int $navigationSort = 1;

    public static function getNavigationItems(): array
    {
        return [
            ...parent::getNavigationItems(),
            \Filament\Navigation\NavigationItem::make('Bot Management')
                ->url(static::getUrl('bot-management'))
                ->icon('heroicon-o-cog-6-tooth')
                ->group(static::getNavigationGroup())
                ->sort(static::getNavigationSort() + 1)
                ->visible(fn (): bool => static::canAccess()),
        ];
    }

    protected static ?string $pluralModelLabel = 'Telegram Messages';

    protected static ?string $modelLabel = 'Telegram Message';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('Sender')
                    ->relationship('sender', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->disabled(fn (string $operation): bool => $operation === 'edit'),

                Forms\Components\Select::make('recipient_user_id')
                    ->label('Recipient')
                    ->relationship('recipient', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->disabled(fn (string $operation): bool => $operation === 'edit'),

                Forms\Components\Textarea::make('message')
                    ->label('Message')
                    ->required()
                    ->rows(4)
                    ->columnSpanFull()
                    ->disabled(fn (string $operation): bool => $operation === 'edit'),

                Forms\Components\Select::make('status')
                    ->label('Status')
                    ->options([
                        'pending' => 'Pending',
                        'sent' => 'Sent',
                        'failed' => 'Failed',
                    ])
                    ->required()
                    ->disabled(fn (string $operation): bool => $operation === 'create'),

                Forms\Components\DateTimePicker::make('sent_at')
                    ->label('Sent At')
                    ->disabled(),

                Forms\Components\Textarea::make('error_message')
                    ->label('Error Message')
                    ->rows(3)
                    ->columnSpanFull()
                    ->disabled()
                    ->visible(fn ($record) => $record && $record->status === 'failed'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('sender.name')
                    ->label('Sender')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('recipient.name')
                    ->label('Recipient')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('message')
                    ->label('Message')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'warning',
                        'sent' => 'success',
                        'failed' => 'danger',
                        default => 'gray',
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'pending' => 'heroicon-o-clock',
                        'sent' => 'heroicon-o-check-circle',
                        'failed' => 'heroicon-o-x-circle',
                        default => 'heroicon-o-question-mark-circle',
                    }),

                Tables\Columns\TextColumn::make('sent_at')
                    ->label('Sent At')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Not sent'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'sent' => 'Sent',
                        'failed' => 'Failed',
                    ]),

                SelectFilter::make('sender')
                    ->relationship('sender', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('recipient')
                    ->relationship('recipient', 'name')
                    ->searchable()
                    ->preload(),

                DateRangeFilter::make('created_at')
                    ->label('Ngày tạo')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->displayFormat('d/m/Y')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ]),

                DateRangeFilter::make('sent_at')
                    ->label('Ngày gửi')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->displayFormat('d/m/Y')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ]),

                DateRangeFilter::make('sent_at')
                    ->label('Ngày gửi')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->displayFormat('d/m/Y')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => $record->status === 'pending'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTelegramSends::route('/'),
            'create' => Pages\CreateTelegramSend::route('/create'),
            'edit' => Pages\EditTelegramSend::route('/{record}/edit'),
            'send' => Pages\SendTelegramMessage::route('/send'),
            'bot-management' => Pages\BotManagement::route('/bot-management'),
        ];
    }

}
