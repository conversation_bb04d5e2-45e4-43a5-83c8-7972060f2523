<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TeamResource\Pages;
use App\Filament\Admin\Resources\TeamResource\RelationManagers;
use App\Models\Team;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Novadaemon\FilamentCombobox\Combobox;

class TeamResource extends Resource
{
    protected static ?string $model = Team::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->required(),
                TextInput::make('commission_rate')
                    ->label(__('Tỉ lệ hoa hồng'))
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(100)
                    ->suffix('%')
                    ->default(0)
                    ->required()
                    ->helperText(__('Tỉ lệ hoa hồng của team (0-100%)')),
                Select::make('users')
                    ->label(__('Member'))
                    ->multiple()
                    ->relationship(
                        'users',
                        'name',
                        function ($query) {
                            return $query->whereHas('roles', function ($q) {
                                $q->where('name', 'Seller');
                            });
                        }
                    )
                    ->getOptionLabelFromRecordUsing(fn ($record) =>
                        "{$record->name} ({$record->roles->pluck('name')->implode(', ')})"
                    )
                    ->preload()
                    ->searchable()
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('commission_rate')
                    ->label(__('Tỉ lệ hoa hồng'))
                    ->suffix('%')
                    ->sortable(),
                TextColumn::make('team_leaders')
                    ->label('Leaders')
                    ->getStateUsing(function (Team $record): string {
                        $leaders = $record->users()
                            ->whereHas('roles', fn($q) => $q->where('name', 'Leader'))
                            ->get();

                        if ($leaders->isEmpty()) {
                            return '<span class="text-gray-400 text-sm">No leaders</span>';
                        }

                        $names = $leaders->pluck('name')->toArray();
                        return '<span class="text-sm font-medium text-blue-700 dark:text-blue-300">' . implode(', ', $names) . '</span>';
                    })
                    ->html()
                    ->searchable(
                        query: function (Builder $query, string $search): Builder {
                            return $query->whereHas('users', function ($q) use ($search) {
                                $q->whereHas('roles', fn($r) => $r->where('name', 'Leader'))
                                  ->where('name', 'like', "%{$search}%");
                            });
                        }
                    ),

              
                TextColumn::make('users_count')
                    ->label('Total Members')
                    ->counts('users')
                    ->badge()
                    ->color('info'),


            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('manage_members')
                    ->label('Manage Members')
                    ->icon('heroicon-o-users')
                    ->color('info')
                    ->form([
                        Forms\Components\Select::make('users')
                            ->label('Team Members')
                            ->multiple()
                            ->relationship(
                                'users',
                                'name',
                                function ($query) {
                                    return $query->whereHas('roles', function ($q) {
                                        $q->whereIn('name', ['Seller', 'Leader']);
                                    });
                                }
                            )
                            ->getOptionLabelFromRecordUsing(fn ($record) =>
                                "{$record->name} ({$record->roles->pluck('name')->implode(', ')}) - {$record->email}"
                            )
                            ->preload()
                            ->searchable()
                            ->columnSpanFull(),
                    ])
                    ->fillForm(function ($record) {
                        return [
                            'users' => $record->users->pluck('id')->toArray(),
                        ];
                    })
                    ->action(function ($record, array $data) {
                        $record->users()->sync($data['users']);

                        \Filament\Notifications\Notification::make()
                            ->success()
                            ->title('Team Members Updated')
                            ->body('Team members have been updated successfully.')
                            ->send();
                    })
                    ->modalHeading('Manage Team Members')
                    ->modalDescription('Add or remove members from this team.')
                    ->modalSubmitActionLabel('Update Members'),

                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                   // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTeams::route('/'),
            'create' => Pages\CreateTeam::route('/create'),
            'edit' => Pages\EditTeam::route('/{record}/edit'),
        ];
    }
}
