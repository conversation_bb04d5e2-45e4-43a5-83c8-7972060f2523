<?php

namespace App\Filament\Admin\Resources\SellerLevelResource\Pages;

use App\Filament\Admin\Resources\SellerLevelResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSellerLevels extends ListRecords
{
    protected static string $resource = SellerLevelResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
