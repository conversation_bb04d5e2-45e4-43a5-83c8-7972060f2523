<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;


use App\Filament\Admin\Resources\UserResource;
use App\Models\UserSeller;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Permission\Models\Role;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    // public function getTabs(): array
    // {
    //     $tabs = [
    //         'all' => Tab::make('All Users')
    //             ->icon('heroicon-o-users')
    //             ->badge(fn() => \App\Models\User::count()),
    //     ];

    //     // Thêm tab cho mỗi role từ database
    //     $roles = Role::all();
    //     foreach ($roles as $role) {
    //         $tabs[$role->name] = Tab::make(ucfirst($role->name))
    //             ->icon('heroicon-o-user-group')
    //             ->badge(fn() => \App\Models\User::role($role->name)->count())
    //             ->modifyQueryUsing(fn(Builder $query) => $query->role($role->name));
    //     }

       

    //     return $tabs;
    // }

    public function getDefaultActiveTab(): string
    {
        return 'all';
    }
}
