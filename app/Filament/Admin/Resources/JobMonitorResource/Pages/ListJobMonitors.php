<?php

namespace App\Filament\Admin\Resources\JobMonitorResource\Pages;

use App\Filament\Admin\Resources\JobMonitorResource;
use Filament\Actions\Action;
use Filament\Resources\Pages\ListRecords;

class ListJobMonitors extends ListRecords
{
    protected static string $resource = JobMonitorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('refresh')
                ->icon('heroicon-o-arrow-path')
                ->action(fn () => $this->refreshTable())
        ];
    }

    public function getPollingInterval(): ?string
    {
        return '10s';
    }
}
