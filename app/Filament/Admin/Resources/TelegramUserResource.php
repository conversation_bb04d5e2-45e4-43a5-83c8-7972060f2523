<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TelegramUserResource\Pages;
use App\Filament\Admin\Resources\TelegramUserResource\RelationManagers;
use App\Models\TelegramUser;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TelegramUserResource extends Resource
{
    protected static ?string $model = TelegramUser::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationLabel = 'Telegram Users';

    protected static ?string $navigationGroup = 'Communication';

    protected static ?int $navigationSort = 2;

    protected static ?string $pluralModelLabel = 'Telegram Users';

    protected static ?string $modelLabel = 'Telegram User';

    public static function canAccess(): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Telegram Information')
                    ->description('Basic Telegram user information')
                    ->schema([
                        Forms\Components\TextInput::make('telegram_id')
                            ->label('Telegram ID')
                            ->required()
                            ->disabled(fn (string $operation): bool => $operation === 'edit')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('chat_id')
                            ->label('Chat ID')
                            ->required()
                            ->disabled(fn (string $operation): bool => $operation === 'edit')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('username')
                            ->label('Username')
                            ->prefix('@')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('first_name')
                            ->label('First Name')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('last_name')
                            ->label('Last Name')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('language_code')
                            ->label('Language Code')
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('System Integration')
                    ->description('Link with system user and manage status')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('Linked System User')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->placeholder('Select a user to link'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->helperText('Enable or disable notifications for this user')
                            ->required(),

                        Forms\Components\DateTimePicker::make('last_interaction_at')
                            ->label('Last Interaction')
                            ->disabled(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Metadata')
                    ->description('Additional information')
                    ->schema([
                        Forms\Components\Textarea::make('metadata')
                            ->label('Metadata (JSON)')
                            ->rows(3)
                            ->columnSpanFull()
                            ->disabled(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('display_name')
                    ->label('Display Name')
                    ->getStateUsing(fn ($record) => $record->display_name)
                    ->searchable(['first_name', 'last_name', 'username'])
                    ->sortable(),

                Tables\Columns\TextColumn::make('username')
                    ->label('Username')
                    ->formatStateUsing(fn (string $state): string => '@' . $state)
                    ->searchable()
                    ->placeholder('No username'),

                Tables\Columns\TextColumn::make('telegram_id')
                    ->label('Telegram ID')
                    ->copyable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('chat_id')
                    ->label('Chat ID')
                    ->copyable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Linked User')
                    ->sortable()
                    ->placeholder('Not linked')
                    ->color(fn ($state) => $state ? 'success' : 'warning'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('last_interaction_at')
                    ->label('Last Interaction')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->placeholder('Never'),

                Tables\Columns\TextColumn::make('language_code')
                    ->label('Language')
                    ->badge()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Registered')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->placeholder('All users')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),

                Tables\Filters\TernaryFilter::make('user_id')
                    ->label('Link Status')
                    ->placeholder('All users')
                    ->trueLabel('Linked only')
                    ->falseLabel('Unlinked only')
                    ->queries(
                        true: fn ($query) => $query->whereNotNull('user_id'),
                        false: fn ($query) => $query->whereNull('user_id'),
                    ),

                Tables\Filters\SelectFilter::make('user')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->label('Linked User'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),

                Tables\Actions\Action::make('send_test_message')
                    ->label('Test Message')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->color('info')
                    ->visible(fn ($record) => $record->is_active)
                    ->form([
                        Forms\Components\Textarea::make('message')
                            ->label('Test Message')
                            ->required()
                            ->default('🧪 This is a test message from the admin panel.')
                            ->rows(3),
                    ])
                    ->action(function ($record, array $data) {
                        try {
                            \Telegram\Bot\Laravel\Facades\Telegram::bot('special_bot')->sendMessage([
                                'chat_id' => $record->chat_id,
                                'text' => $data['message'],
                                'parse_mode' => 'HTML',
                            ]);

                            \Filament\Notifications\Notification::make()
                                ->title('Test message sent successfully')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('Failed to send test message')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),

                Tables\Actions\Action::make('link_user')
                    ->label('Link User')
                    ->icon('heroicon-o-link')
                    ->color('warning')
                    ->visible(fn ($record) => !$record->isLinked())
                    ->form([
                        Forms\Components\Select::make('user_id')
                            ->label('Select User to Link')
                            ->options(\App\Models\User::pluck('name', 'id'))
                            ->searchable()
                            ->required(),
                    ])
                    ->action(function ($record, array $data) {
                        $botService = app(\App\Services\TelegramBotManagementService::class);
                        $success = $botService->linkTelegramUser($record->telegram_id, $data['user_id']);

                        if ($success) {
                            \Filament\Notifications\Notification::make()
                                ->title('User linked successfully')
                                ->success()
                                ->send();
                        } else {
                            \Filament\Notifications\Notification::make()
                                ->title('Failed to link user')
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each->activate();
                            \Filament\Notifications\Notification::make()
                                ->title('Users activated successfully')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each->deactivate();
                            \Filament\Notifications\Notification::make()
                                ->title('Users deactivated successfully')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('last_interaction_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTelegramUsers::route('/'),
            'create' => Pages\CreateTelegramUser::route('/create'),
            'edit' => Pages\EditTelegramUser::route('/{record}/edit'),
        ];
    }
}
