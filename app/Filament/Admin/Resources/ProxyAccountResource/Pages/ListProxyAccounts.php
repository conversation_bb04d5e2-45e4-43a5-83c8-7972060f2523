<?php

namespace App\Filament\Admin\Resources\ProxyAccountResource\Pages;

use App\Filament\Admin\Resources\ProxyAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListProxyAccounts extends ListRecords
{
    protected static string $resource = ProxyAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
