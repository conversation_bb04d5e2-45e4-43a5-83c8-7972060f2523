<?php

namespace App\Filament\Admin\Resources\ProxyAccountResource\Pages;

use App\Filament\Admin\Resources\ProxyAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditProxyAccount extends EditRecord
{
    protected static string $resource = ProxyAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Auto-load số dư khi vào trang edit
        if (!empty($data['api_key'])) {
            try {
                $result = $this->record->getAccountFunds();

                if ($result['success']) {
                    $data['balance'] = $result['amount'];

                    // Cập nhật vào database luôn
                    $this->record->update(['balance' => $result['amount']]);

                    Notification::make()
                        ->title('Đã cập nhật số dư tự động')
                        ->body('Số dư hiện tại: $' . number_format($result['amount'], 2))
                        ->success()
                        ->send();
                } else {
                    Notification::make()
                        ->title('Không thể lấy số dư tự động')
                        ->body('Lỗi: ' . $result['message'])
                        ->warning()
                        ->send();
                }
            } catch (\Exception $e) {
                Notification::make()
                    ->title('Lỗi khi lấy số dư')
                    ->body('Lỗi: ' . $e->getMessage())
                    ->warning()
                    ->send();
            }
        }

        return $data;
    }
}
