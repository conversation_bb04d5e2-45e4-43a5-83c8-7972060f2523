<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ProxyAccountResource\Pages;
use App\Models\ProxyAccount;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use App\Services\ProxyPurchaseService;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action as FormAction;

class ProxyAccountResource extends Resource
{
    protected static ?string $model = ProxyAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-globe-alt';

    protected static ?string $navigationLabel = 'Tài khoản Proxy';

    protected static ?string $modelLabel = 'Tài khoản Proxy';

    protected static ?string $pluralModelLabel = 'Tài khoản Proxy';

    protected static ?string $navigationGroup = 'Dịch vụ AI';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Thông tin cơ bản')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Tên tài khoản')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('VD: Proxies.com Account 1'),

                                Select::make('priority')
                                    ->label('Độ ưu tiên')
                                    ->options([
                                        100 => 'Cao nhất (100)',
                                        75 => 'Cao (75)',
                                        50 => 'Trung bình (50)',
                                        25 => 'Thấp (25)',
                                        10 => 'Thấp nhất (10)',
                                    ])
                                    ->default(50)
                                    ->required()
                                    ->helperText('Số càng cao càng được ưu tiên sử dụng'),
                            ]),

                        Grid::make(3)
                            ->schema([
                                Toggle::make('is_active')
                                    ->label('Kích hoạt')
                                    ->default(true)
                                    ->helperText('Chỉ tài khoản được kích hoạt mới được sử dụng'),

                                Toggle::make('is_default')
                                    ->label('Mặc định')
                                    ->default(false)
                                    ->helperText('Tài khoản được ưu tiên sử dụng đầu tiên'),

                                TextInput::make('daily_limit')
                                    ->label('Giới hạn hàng ngày')
                                    ->numeric()
                                    ->default(50)
                                    ->required()
                                    ->minValue(1)
                                    ->helperText('Số proxy tối đa có thể mua mỗi ngày'),
                            ]),
                    ]),

                Section::make('Phân tích Request')
                    ->description('Paste curl request để tự động phân tích cookies và CSRF token')
                    ->schema([
                        Textarea::make('raw_request')
                            ->label('Raw Request (cURL)')
                            ->rows(8)
                            ->placeholder("Paste cURL request ở đây, ví dụ:\ncurl 'https://www.proxies.com/proxy' \\\n  -H 'x-csrf-token: abc123' \\\n  -b 'cookie1=value1; cookie2=value2'")
                            ->helperText('Paste toàn bộ cURL request từ Developer Tools → Network → Copy as cURL')
                            ->dehydrated(false)
                            ->columnSpanFull(),

                        Actions::make([
                            FormAction::make('analyze_request')
                                ->label('🔍 Phân tích Request')
                                ->color('primary')
                                ->action(function ($livewire, $get, $set) {
                                    $rawRequest = $get('raw_request');
                                    if (empty($rawRequest)) {
                                        Notification::make()
                                            ->title('Lỗi')
                                            ->body('Vui lòng nhập raw request trước')
                                            ->danger()
                                            ->send();
                                        return;
                                    }

                                    try {
                                        $analyzed = static::analyzeRequest($rawRequest);

                                        // Set các giá trị đã phân tích
                                        $set('api_url', $analyzed['url']);
                                        $set('csrf_token', $analyzed['csrf_token']);
                                        $set('cookies', $analyzed['cookies']);
                                        $set('user_agent', $analyzed['user_agent']);

                                        Notification::make()
                                            ->title('Phân tích thành công!')
                                            ->body('Đã tự động điền URL, CSRF token, cookies và User Agent')
                                            ->success()
                                            ->send();
                                    } catch (\Exception $e) {
                                        Notification::make()
                                            ->title('Phân tích thất bại')
                                            ->body('Lỗi: ' . $e->getMessage())
                                            ->danger()
                                            ->send();
                                    }
                                }),
                        ])
                        ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(false),

                Section::make('Cấu hình API')
                    ->schema([
                        TextInput::make('api_url')
                            ->label('API URL')
                            ->url()
                            ->required()
                            ->default('https://www.proxies.com/proxy')
                            ->helperText('URL endpoint để mua proxy'),

                        TextInput::make('api_key')
                            ->label('API Key')
                            ->password()
                            ->placeholder('Bearer token từ https://www.proxies.com/account/profile')
                            ->helperText('API Key để sử dụng với Proxies.com API v2. Để trống nếu không muốn thay đổi.')
                            ->dehydrated(fn (?string $state): bool => filled($state))
                            ->columnSpanFull(),

                        TextInput::make('user_agent')
                            ->label('User Agent')
                            ->placeholder('Mozilla/5.0...')
                            ->helperText('User Agent để sử dụng cho requests')
                            ->columnSpanFull(),

                        // Ẩn nội dung CSRF token và cookies để bảo mật
                        TextInput::make('csrf_token')
                            ->label('CSRF Token')
                            ->password()
                            ->placeholder('x-csrf-token value')
                            ->helperText('Copy từ x-csrf-token header trong Developer Tools. Để trống nếu không muốn thay đổi.')
                            ->dehydrated(fn (?string $state): bool => filled($state)),

                        TextInput::make('cookies')
                            ->label('Cookies')
                            ->password()
                            ->placeholder('cookie1=value1; cookie2=value2; ...')
                            ->helperText('Copy từ Cookie header trong Developer Tools. Để trống nếu không muốn thay đổi.')
                            ->dehydrated(fn (?string $state): bool => filled($state))
                            ->columnSpanFull(),
                    ]),

                Section::make('Thông tin tài khoản')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('balance')
                                    ->label('Số dư')
                                    ->numeric()
                                    ->prefix('$')
                                    ->step(0.01)
                                    ->disabled()
                                    ->helperText('Số dư hiện tại của tài khoản (nếu có)')
                                    ->suffixAction(
                                        \Filament\Forms\Components\Actions\Action::make('refresh_balance')
                                            ->icon('heroicon-o-arrow-path')
                                            ->tooltip('Lấy số dư từ API')
                                            ->action(function ($livewire, $set, $get) {
                                                try {
                                                    $apiKey = $get('api_key');
                                                    if (empty($apiKey)) {
                                                        throw new \Exception('Vui lòng nhập API Key trước');
                                                    }

                                                    // Tạo temporary ProxyAccount để test
                                                    $tempAccount = new \App\Models\ProxyAccount();
                                                    $tempAccount->api_key = $apiKey;
                                                    $tempAccount->name = $get('name') ?? 'Temp';
                                                    $tempAccount->id = 0;

                                                    $result = $tempAccount->getAccountFunds();

                                                    if ($result['success']) {
                                                        $set('balance', $result['amount']);

                                                        \Filament\Notifications\Notification::make()
                                                            ->title('Cập nhật số dư thành công!')
                                                            ->body('Số dư: $' . number_format($result['amount'], 2))
                                                            ->success()
                                                            ->send();
                                                    } else {
                                                        throw new \Exception($result['message']);
                                                    }
                                                } catch (\Exception $e) {
                                                    \Filament\Notifications\Notification::make()
                                                        ->title('Không thể lấy số dư')
                                                        ->body('Lỗi: ' . $e->getMessage())
                                                        ->danger()
                                                        ->send();
                                                }
                                            })
                                    ),

                                TextInput::make('usage_count')
                                    ->label('Số lần đã sử dụng')
                                    ->numeric()
                                    ->default(0)
                                    ->disabled()
                                    ->helperText('Tổng số lần đã sử dụng tài khoản này'),
                            ]),
                    ])
                    ->collapsible(),

                Section::make('Ghi chú')
                    ->schema([
                        Textarea::make('notes')
                            ->label('Ghi chú')
                            ->rows(3)
                            ->placeholder('Ghi chú về tài khoản này...'),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Tên tài khoản')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),

                IconColumn::make('is_active')
                    ->label('Hoạt động')
                    ->boolean()
                    ->sortable(),

                IconColumn::make('is_default')
                    ->label('Mặc định')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('priority')
                    ->label('Ưu tiên')
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match (true) {
                        $state >= 75 => 'success',
                        $state >= 50 => 'warning',
                        default => 'gray',
                    }),

                BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'success' => 'available',
                        'warning' => 'limit_reached',
                        'danger' => 'inactive',
                    ])
                    ->icons([
                        'heroicon-o-check-circle' => 'available',
                        'heroicon-o-exclamation-triangle' => 'limit_reached',
                        'heroicon-o-x-circle' => 'inactive',
                    ]),

                TextColumn::make('usage_info')
                    ->label('Sử dụng hôm nay')
                    ->badge()
                    ->color(fn (ProxyAccount $record): string => 
                        $record->getRemainingDailyUsage() > 5 ? 'success' : 
                        ($record->getRemainingDailyUsage() > 0 ? 'warning' : 'danger')
                    ),

                TextColumn::make('balance')
                    ->label('Số dư')
                    ->money('USD')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('last_used_at')
                    ->label('Lần cuối sử dụng')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('is_active')
                    ->label('Trạng thái hoạt động')
                    ->options([
                        1 => 'Hoạt động',
                        0 => 'Không hoạt động',
                    ]),

                SelectFilter::make('is_default')
                    ->label('Tài khoản mặc định')
                    ->options([
                        1 => 'Mặc định',
                        0 => 'Không mặc định',
                    ]),

                SelectFilter::make('priority')
                    ->label('Độ ưu tiên')
                    ->options([
                        100 => 'Cao nhất (100)',
                        75 => 'Cao (75)',
                        50 => 'Trung bình (50)',
                        25 => 'Thấp (25)',
                        10 => 'Thấp nhất (10)',
                    ]),
            ])
            ->actions([
                Action::make('test_cookie')
                    ->label('Test Cookie')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function (ProxyAccount $record) {
                        $result = $record->testCookie();

                        if ($result['success']) {
                            $title = $result['is_logged_in'] ? '✅ Cookie hợp lệ!' : '⚠️ Cookie có vấn đề';
                            $color = $result['is_logged_in'] ? 'success' : 'warning';

                            $details = [];
                            $details[] = $result['message'];
                            $details[] = "📊 Status Code: " . $result['status_code'];
                            $details[] = "📦 Response Size: " . number_format($result['response_size']) . ' bytes';
                            $details[] = "🍪 Cookies: " . $result['cookies_count'] . ' cookies';

                            if ($result['username']) {
                                $details[] = "👤 Username: " . $result['username'];
                            }

                            Notification::make()
                                ->title($title)
                                ->body(implode("\n", $details))
                                ->color($color)
                                ->duration(10000)
                                ->send();
                        } else {
                            Notification::make()
                                ->title('❌ Test thất bại!')
                                ->body($result['message'])
                                ->danger()
                                ->duration(8000)
                                ->send();
                        }
                    }),

                Action::make('test_api_key')
                    ->label('Test API Key')
                    ->icon('heroicon-o-key')
                    ->color('warning')
                    ->action(function (ProxyAccount $record) {
                        try {
                            if (empty($record->api_key)) {
                                throw new \Exception('API Key chưa được cấu hình');
                            }

                            // Test API key bằng cách lấy account funds
                            $result = $record->getAccountFunds();

                            if ($result['success']) {
                                Notification::make()
                                    ->title('API Key hợp lệ!')
                                    ->body('Số dư tài khoản: $' . number_format($result['amount'], 2))
                                    ->success()
                                    ->send();
                            } else {
                                throw new \Exception($result['message']);
                            }
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('API Key không hợp lệ!')
                                ->body('Lỗi: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),

                Action::make('test_connection')
                    ->label('Test API')
                    ->icon('heroicon-o-wifi')
                    ->color('info')
                    ->action(function (ProxyAccount $record) {
                        try {
                            // Test connection logic here
                            $service = app(ProxyPurchaseService::class);

                            // Temporarily use this account for testing
                            $config = [
                                'api_url' => $record->api_url,
                                'cookies' => $record->getParsedCookies(),
                                'headers' => $record->getFullHeaders(),
                            ];

                            $service->updateConfiguration($config);

                            Notification::make()
                                ->title('Cấu hình API thành công!')
                                ->body('Tài khoản ' . $record->name . ' đã được áp dụng.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Cấu hình thất bại!')
                                ->body('Lỗi: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),

                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('priority', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProxyAccounts::route('/'),
            'create' => Pages\CreateProxyAccount::route('/create'),
            'edit' => Pages\EditProxyAccount::route('/{record}/edit'),
        ];
    }

    /**
     * Phân tích cURL request để extract thông tin
     */
    public static function analyzeRequest(string $rawRequest): array
    {
        $result = [
            'url' => '',
            'csrf_token' => '',
            'cookies' => '',
            'user_agent' => '',
        ];

        // Extract URL
        if (preg_match("/curl\s+'([^']+)'/", $rawRequest, $matches)) {
            $result['url'] = $matches[1];
        } elseif (preg_match('/curl\s+"([^"]+)"/', $rawRequest, $matches)) {
            $result['url'] = $matches[1];
        } elseif (preg_match('/curl\s+([^\s\\\\]+)/', $rawRequest, $matches)) {
            $result['url'] = $matches[1];
        }

        // Extract CSRF token từ header x-csrf-token
        if (preg_match("/-H\s+'x-csrf-token:\s*([^']+)'/i", $rawRequest, $matches)) {
            $result['csrf_token'] = trim($matches[1]);
        } elseif (preg_match('/-H\s+"x-csrf-token:\s*([^"]+)"/i', $rawRequest, $matches)) {
            $result['csrf_token'] = trim($matches[1]);
        }

        // Extract cookies từ -b flag
        if (preg_match("/-b\s+'([^']+)'/", $rawRequest, $matches)) {
            $result['cookies'] = $matches[1];
        } elseif (preg_match('/-b\s+"([^"]+)"/', $rawRequest, $matches)) {
            $result['cookies'] = $matches[1];
        }

        // Extract User Agent
        if (preg_match("/-H\s+'user-agent:\s*([^']+)'/i", $rawRequest, $matches)) {
            $result['user_agent'] = trim($matches[1]);
        } elseif (preg_match('/-H\s+"user-agent:\s*([^"]+)"/i', $rawRequest, $matches)) {
            $result['user_agent'] = trim($matches[1]);
        }

        // Validate kết quả
        if (empty($result['url'])) {
            throw new \Exception('Không tìm thấy URL trong request');
        }

        if (empty($result['cookies'])) {
            throw new \Exception('Không tìm thấy cookies trong request');
        }

        return $result;
    }
}
