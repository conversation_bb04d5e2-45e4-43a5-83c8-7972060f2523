<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\IdeogramAccountResource\Pages;
use App\Filament\Admin\Resources\IdeogramAccountResource\RelationManagers;
use App\Models\IdeogramAccount;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;

class IdeogramAccountResource extends Resource
{
    protected static ?string $model = IdeogramAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationLabel = 'Tài khoản Ideogram';

    protected static ?string $modelLabel = 'Tài khoản Ideogram';

    protected static ?string $pluralModelLabel = 'Tài khoản Ideogram';

    protected static ?string $navigationGroup = 'Dịch vụ AI';

    protected static ?string $slug = 'ideogram-accounts';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Thông tin cơ bản')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Tên tài khoản')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('VD: Account Primary'),

                                Select::make('priority')
                                    ->label('Độ ưu tiên')
                                    ->options([
                                        100 => 'Cao nhất (100)',
                                        75 => 'Cao (75)',
                                        50 => 'Trung bình (50)',
                                        25 => 'Thấp (25)',
                                        10 => 'Thấp nhất (10)',
                                    ])
                                    ->default(50)
                                    ->required()
                                    ->helperText('Số càng cao càng được ưu tiên sử dụng'),
                            ]),

                        Grid::make(3)
                            ->schema([
                                Toggle::make('is_active')
                                    ->label('Kích hoạt')
                                    ->default(true)
                                    ->helperText('Chỉ tài khoản được kích hoạt mới được sử dụng'),

                                Toggle::make('is_default')
                                    ->label('Mặc định')
                                    ->default(false)
                                    ->helperText('Tài khoản được ưu tiên sử dụng đầu tiên'),

                                TextInput::make('daily_limit')
                                    ->label('Giới hạn hàng ngày')
                                    ->numeric()
                                    ->default(100)
                                    ->required()
                                    ->minValue(1)
                                    ->helperText('Số lượng request tối đa mỗi ngày'),
                            ]),
                    ]),

                Section::make('Thông tin xác thực')
                    ->schema([
                        Textarea::make('authorization')
                            ->label('Authorization Token')
                            ->required()
                            ->rows(3)
                            ->placeholder('Bearer your-token-here')
                            ->helperText('Copy từ Authorization header trong Developer Tools')
                            ->rules(['required', 'string', 'min:10'])
                            ->dehydrateStateUsing(fn ($state) => trim($state))
                            ->afterStateUpdated(function ($state, $set) {
                                // Tự động làm sạch dữ liệu khi nhập
                                if ($state) {
                                    $cleaned = trim(str_replace(["\r", "\n", "\t"], '', $state));
                                    $set('authorization', $cleaned);
                                }
                            }),

                        Textarea::make('cookie')
                            ->label('Cookie')
                            ->required()
                            ->rows(3)
                            ->placeholder('your-cookie-string-here')
                            ->helperText('Copy từ Cookie header trong Developer Tools')
                            ->rule('min:10')
                            ->validationMessages([
                                'min' => 'Cookie phải có ít nhất 10 ký tự'
                            ]),

                        Grid::make(2)
                            ->schema([
                                TextInput::make('external_photo_url')
                                    ->label('External Photo URL')
                                    ->maxLength(500)
                                    ->placeholder('URL từ payload login request'),

                                TextInput::make('location')
                                    ->label('Location')
                                    ->maxLength(500)
                                    ->placeholder('Location từ payload submit request'),
                            ]),
                    ]),

                Section::make('Ghi chú')
                    ->schema([
                        Textarea::make('notes')
                            ->label('Ghi chú')
                            ->rows(3)
                            ->placeholder('Ghi chú về tài khoản này...'),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Tên tài khoản')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),

                IconColumn::make('is_active')
                    ->label('Hoạt động')
                    ->boolean()
                    ->sortable(),

                IconColumn::make('is_default')
                    ->label('Mặc định')
                    ->boolean()
                    ->sortable(),

                TextColumn::make('priority')
                    ->label('Ưu tiên')
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match (true) {
                        $state >= 75 => 'success',
                        $state >= 50 => 'warning',
                        default => 'gray',
                    }),

                TextColumn::make('daily_usage')
                    ->label('Sử dụng hôm nay')
                    ->formatStateUsing(fn ($record) => $record->daily_usage . '/' . $record->daily_limit)
                    ->badge()
                    ->color(fn ($record): string => match (true) {
                        $record->usage_percentage >= 90 => 'danger',
                        $record->usage_percentage >= 70 => 'warning',
                        default => 'success',
                    }),

                TextColumn::make('usage_count')
                    ->label('Tổng sử dụng')
                    ->sortable()
                    ->numeric(),

                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'available' => 'success',
                        'limit_reached' => 'warning',
                        'inactive' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'available' => 'Khả dụng',
                        'limit_reached' => 'Đạt giới hạn',
                        'inactive' => 'Không hoạt động',
                        default => 'Không xác định',
                    }),

                TextColumn::make('last_used_at')
                    ->label('Lần cuối sử dụng')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->placeholder('Chưa sử dụng'),

                TextColumn::make('updated_at')
                    ->label('Cập nhật lần cuối')
                    ->dateTime('d/m/Y H:i:s')
                    ->sortable()
                    ->toggleable()
                    ->description(fn ($record) => $record->updated_at ? $record->updated_at->diffForHumans() : null),

                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('is_active')
                    ->label('Trạng thái hoạt động')
                    ->options([
                        1 => 'Hoạt động',
                        0 => 'Không hoạt động',
                    ]),

                SelectFilter::make('is_default')
                    ->label('Tài khoản mặc định')
                    ->options([
                        1 => 'Mặc định',
                        0 => 'Không mặc định',
                    ]),

                SelectFilter::make('status')
                    ->label('Trạng thái sử dụng')
                    ->options([
                        'available' => 'Khả dụng',
                        'limit_reached' => 'Đạt giới hạn',
                        'inactive' => 'Không hoạt động',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (!$data['value']) {
                            return $query;
                        }

                        return match ($data['value']) {
                            'available' => $query->where('is_active', true)
                                                ->where(function ($q) {
                                                    $q->where('daily_usage_date', '!=', today())
                                                      ->orWhere('daily_usage', '<', \DB::raw('daily_limit'))
                                                      ->orWhereNull('daily_usage_date');
                                                }),
                            'limit_reached' => $query->where('is_active', true)
                                                    ->where('daily_usage_date', today())
                                                    ->where('daily_usage', '>=', \DB::raw('daily_limit')),
                            'inactive' => $query->where('is_active', false),
                            default => $query,
                        };
                    }),
            ])
            ->actions([
                Action::make('test_connection')
                    ->label('Test kết nối')
                    ->icon('heroicon-o-wifi')
                    ->color('info')
                    ->action(function (IdeogramAccount $record) {
                        try {
                            $service = new \App\Services\IdeogramService($record);
                            $service->login();

                            Notification::make()
                                ->title('Kết nối thành công!')
                                ->body('Tài khoản ' . $record->name . ' hoạt động bình thường.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Kết nối thất bại!')
                                ->body('Lỗi: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),

                Action::make('reset_daily_usage')
                    ->label('Reset sử dụng hàng ngày')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->action(function (IdeogramAccount $record) {
                        $record->resetDailyUsage();

                        Notification::make()
                            ->title('Đã reset!')
                            ->body('Đã reset số lượng sử dụng hàng ngày cho tài khoản ' . $record->name)
                            ->success()
                            ->send();
                    }),

                Action::make('clean_headers')
                    ->label('Làm sạch headers')
                    ->icon('heroicon-o-sparkles')
                    ->color('info')
                    ->action(function (IdeogramAccount $record) {
                        $oldAuth = $record->authorization;
                        $oldCookie = $record->cookie;

                        $record->authorization = $record->cleanHeaderValue($record->authorization);
                        $record->cookie = $record->cleanHeaderValue($record->cookie);
                        $record->save();

                        $changes = [];
                        if ($oldAuth !== $record->authorization) {
                            $changes[] = 'Authorization';
                        }
                        if ($oldCookie !== $record->cookie) {
                            $changes[] = 'Cookie';
                        }

                        if (empty($changes)) {
                            Notification::make()
                                ->title('Không có thay đổi')
                                ->body('Headers đã sạch rồi!')
                                ->info()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Đã làm sạch!')
                                ->body('Đã làm sạch: ' . implode(', ', $changes))
                                ->success()
                                ->send();
                        }
                    }),

                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('Kích hoạt')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => true]);

                            Notification::make()
                                ->title('Đã kích hoạt!')
                                ->body('Đã kích hoạt ' . $records->count() . ' tài khoản.')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Vô hiệu hóa')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => false]);

                            Notification::make()
                                ->title('Đã vô hiệu hóa!')
                                ->body('Đã vô hiệu hóa ' . $records->count() . ' tài khoản.')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\BulkAction::make('reset_daily_usage')
                        ->label('Reset sử dụng hàng ngày')
                        ->icon('heroicon-o-arrow-path')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->action(function ($records) {
                            $records->each->resetDailyUsage();

                            Notification::make()
                                ->title('Đã reset!')
                                ->body('Đã reset số lượng sử dụng hàng ngày cho ' . $records->count() . ' tài khoản.')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('priority', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIdeogramAccounts::route('/'),
            'create' => Pages\CreateIdeogramAccount::route('/create'),
            'edit' => Pages\EditIdeogramAccount::route('/{record}/edit'),
            'test-generation' => Pages\TestImageGeneration::route('/test-generation'),
        ];
    }
}
