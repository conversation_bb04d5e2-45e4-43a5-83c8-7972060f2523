<?php

namespace App\Filament\Admin\Resources\IdeogramAccountResource\Pages;

use App\Filament\Admin\Resources\IdeogramAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListIdeogramAccounts extends ListRecords
{
    protected static string $resource = IdeogramAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('testGeneration')
                ->label('🎨 Test Gen Ảnh')
                ->icon('heroicon-o-sparkles')
                ->color('success')
                ->url(static::getResource()::getUrl('test-generation')),
        ];
    }
}
