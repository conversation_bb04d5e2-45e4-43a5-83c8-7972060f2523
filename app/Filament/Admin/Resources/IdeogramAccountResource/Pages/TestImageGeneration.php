<?php

namespace App\Filament\Admin\Resources\IdeogramAccountResource\Pages;

use App\Filament\Admin\Resources\IdeogramAccountResource;
use App\Models\IdeogramAccount;
use App\Services\IdeogramService;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Filament\Support\Exceptions\Halt;
use Illuminate\Support\Facades\Log;

class TestImageGeneration extends Page
{
    protected static string $resource = IdeogramAccountResource::class;
    
    protected static string $view = 'filament.admin.pages.test-image-generation';
    
    protected static ?string $title = 'Test Tạo Ảnh Ideogram';
    
    protected static ?string $navigationLabel = 'Test Gen Ảnh';
    
    protected static ?string $navigationIcon = 'heroicon-o-photo';
    
    protected static ?int $navigationSort = 3;

    public ?array $data = [];
    
    public ?array $result = null;
    
    public bool $isGenerating = false;

    public function mount(): void
    {
        $this->form->fill([
            'prompt' => 'A beautiful sunset over mountains',
            'account_id' => IdeogramAccount::where('is_active', true)->first()?->id,
            'model_version' => 'V_3_1',
            'sampling_speed' => 2,
            'resolution_width' => 1024,
            'resolution_height' => 1024,
            'num_images' => 4,
            'use_autoprompt' => true,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('account_id')
                    ->label('Tài khoản Ideogram')
                    ->options(IdeogramAccount::where('is_active', true)->pluck('name', 'id'))
                    ->required()
                    ->searchable()
                    ->helperText('Chọn tài khoản để sử dụng tạo ảnh'),

                Textarea::make('prompt')
                    ->label('Prompt')
                    ->required()
                    ->rows(3)
                    ->placeholder('Mô tả ảnh bạn muốn tạo...')
                    ->helperText('Mô tả chi tiết ảnh bạn muốn tạo'),

                Select::make('model_version')
                    ->label('Phiên bản Model')
                    ->options([
                        'V_3_1' => 'V3.1 (Mới nhất)',
                        'V_2' => 'V2.0',
                        'V_1_5' => 'V1.5',
                    ])
                    ->default('V_3_1')
                    ->required(),

                Select::make('sampling_speed')
                    ->label('Tốc độ tạo')
                    ->options([
                        0 => 'Slow (Chất lượng cao)',
                        1 => 'Medium',
                        2 => 'Fast (Nhanh)',
                    ])
                    ->default(2)
                    ->required(),

                TextInput::make('resolution_width')
                    ->label('Chiều rộng')
                    ->numeric()
                    ->default(1024)
                    ->required()
                    ->minValue(512)
                    ->maxValue(2048),

                TextInput::make('resolution_height')
                    ->label('Chiều cao')
                    ->numeric()
                    ->default(1024)
                    ->required()
                    ->minValue(512)
                    ->maxValue(2048),

                TextInput::make('num_images')
                    ->label('Số lượng ảnh')
                    ->numeric()
                    ->default(4)
                    ->required()
                    ->minValue(1)
                    ->maxValue(8),

                Toggle::make('use_autoprompt')
                    ->label('Sử dụng Auto Prompt')
                    ->default(true)
                    ->helperText('Tự động cải thiện prompt'),
            ])
            ->statePath('data')
            ->columns(2);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generateImage')
                ->label('🎨 Tạo Ảnh')
                ->color('primary')
                ->size('lg')
                ->disabled(fn () => $this->isGenerating)
                ->action('generateImage'),
                
            Action::make('testConnection')
                ->label('🔗 Test Kết Nối')
                ->color('gray')
                ->action('testConnection'),
                
            Action::make('clearResult')
                ->label('🗑️ Xóa Kết Quả')
                ->color('danger')
                ->action('clearResult')
                ->visible(fn () => $this->result !== null),
        ];
    }

    public function generateImage(): void
    {
        try {
            $this->validate();
            
            $data = $this->form->getState();
            
            if (!$data['account_id']) {
                throw new Halt('Vui lòng chọn tài khoản Ideogram');
            }

            $account = IdeogramAccount::find($data['account_id']);
            if (!$account) {
                throw new Halt('Không tìm thấy tài khoản');
            }

            $this->isGenerating = true;
            
            // Tạo options từ form data
            $options = [
                'model_version' => $data['model_version'],
                'sampling_speed' => $data['sampling_speed'],
                'resolution' => [
                    'width' => $data['resolution_width'],
                    'height' => $data['resolution_height']
                ],
                'num_images' => $data['num_images'],
                'use_autoprompt_option' => $data['use_autoprompt'] ? 'AUTO' : 'OFF',
            ];

            // Khởi tạo service và tạo ảnh
            $service = new IdeogramService($account);
            $result = $service->createImage($data['prompt'], $options);

            $this->result = $result;
            $this->isGenerating = false;

            if ($result['success']) {
                Notification::make()
                    ->title('✅ Tạo ảnh thành công!')
                    ->body("Đã tạo {$result['images']['count']} ảnh với request ID: {$result['request_id']}")
                    ->success()
                    ->duration(5000)
                    ->send();
            } else {
                Notification::make()
                    ->title('❌ Tạo ảnh thất bại')
                    ->body($result['error'] ?? 'Lỗi không xác định')
                    ->danger()
                    ->duration(8000)
                    ->send();
            }

        } catch (\Exception $e) {
            $this->isGenerating = false;
            
            Log::channel('ideogram')->error('Test image generation failed', [
                'error' => $e->getMessage(),
                'data' => $data ?? null
            ]);

            Notification::make()
                ->title('❌ Lỗi hệ thống')
                ->body($e->getMessage())
                ->danger()
                ->duration(8000)
                ->send();
        }
    }

    public function testConnection(): void
    {
        try {
            $data = $this->form->getState();
            
            if (!$data['account_id']) {
                throw new Halt('Vui lòng chọn tài khoản Ideogram');
            }

            $account = IdeogramAccount::find($data['account_id']);
            if (!$account) {
                throw new Halt('Không tìm thấy tài khoản');
            }

            $service = new IdeogramService($account);
            $result = $service->testConnection();

            if ($result['success']) {
                Notification::make()
                    ->title('✅ Kết nối thành công!')
                    ->body('Tài khoản hoạt động bình thường')
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('❌ Kết nối thất bại')
                    ->body($result['error'] ?? 'Lỗi không xác định')
                    ->warning()
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('❌ Lỗi test kết nối')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function clearResult(): void
    {
        $this->result = null;
        
        Notification::make()
            ->title('🗑️ Đã xóa kết quả')
            ->success()
            ->send();
    }
}
