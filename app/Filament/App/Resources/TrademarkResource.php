<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\TrademarkResource\Pages;
use App\Filament\App\Resources\TrademarkResource\RelationManagers;
use App\Models\Trademark;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TrademarkResource extends Resource
{
    protected static ?string $model = Trademark::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationGroup = 'Products';
    protected static ?int $navigationSort = 1;

    public static function getNavigationLabel(): string
    {
        return 'Trademark Keywords';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('keyword')
                    ->required()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),
                Forms\Components\Textarea::make('notes')
                    ->maxLength(65535),
            ]);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('keyword')
                    ->searchable(),
                Tables\Columns\TextColumn::make('notes')
                    ->limit(50)
                    ->searchable(),
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['super_admin', 'admin', 'TM Leader']);
    }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTrademarks::route('/'),
            'create' => Pages\CreateTrademark::route('/create'),
            'edit' => Pages\EditTrademark::route('/{record}/edit'),
        ];
    }
}
