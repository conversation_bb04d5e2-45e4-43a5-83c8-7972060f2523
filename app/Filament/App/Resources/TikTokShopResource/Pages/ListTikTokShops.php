<?php

namespace App\Filament\App\Resources\TikTokShopResource\Pages;

use App\Enums\TiktokShopReviewStatus;
use App\Enums\TiktokShopStatus;
use App\Filament\App\Resources\TikTokShopResource;
use App\Models\TikTokShop;
use App\Services\Tiktok\PodShopService;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListTikTokShops extends ListRecords
{
    protected static string $resource = TikTokShopResource::class;
    //protected static ?string $title = '2';

    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('All Shop')
        ];

        $statusTabs = collect(TiktokShopReviewStatus::cases())->mapWithKeys(function ($status) {
            $statusName = $status->value;
            $statusLabel = $status->getLabel();
            $badgeColor = $status->getColor();

            return [
                strtolower($statusName) => Tab::make($statusLabel)
                    ->modifyQueryUsing(fn (Builder $query) => $query->where('review_status', $statusName))
                    ->badge(TikTokShop::where('review_status', $statusName)->count())
                    ->badgeColor($badgeColor),
            ];
        })->toArray();

        return $tabs + $statusTabs;
    }

    protected function getHeaderActions(): array
    {
        return [
             Actions\CreateAction::make(),


            Action::make('importShop')
                ->label('Import Shop')
                ->form([
                    TextInput::make('shop_id')
                        ->label('Shop ID')
                        ->required(),
                ])
                ->action(function (array $data) {
                    // Call the import logic here
                    $shopId = $data['shop_id'];
                    app(PodShopService::class)->importShop($shopId);
                })
     
                ->color('success')
                ->modalHeading('Import TikTok Shop')
                ->modalButton('Import'),
        ];
    }
}
