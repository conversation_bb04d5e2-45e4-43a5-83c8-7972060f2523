<?php

namespace App\Filament\App\Resources\SupplierProductResource\Pages;

use App\Filament\App\Resources\SupplierProductResource;
use App\Filament\Imports\SupplierProductImporter;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\ImportAction;

class ListSupplierProducts extends ListRecords
{
    protected static string $resource = SupplierProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\ImportAction::make()
            //     ->importer(SupplierProductImporter::class)
        
            //     ->icon('heroicon-o-arrow-up-tray')
            //     ->color('success')
            //     ->chunkSize(100)
            //     ->maxRows(1000)
            //     ->slideOver(),
         
        ];
    }
}