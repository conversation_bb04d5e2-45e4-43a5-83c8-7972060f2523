<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\PartnerAppResource\Pages;
use App\Filament\App\Resources\PartnerAppResource\RelationManagers;
use App\Models\PartnerApp;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PartnerAppResource extends Resource
{
    protected static ?string $model = PartnerApp::class;
    protected static ?string $navigationGroup = 'TikTok Shop';
    protected static ?string $navigationIcon = 'heroicon-o-cube';

    public static function getEloquentQuery(): Builder
    {
        if(auth()->user()->hasRole('super_admin')){
            return parent::getEloquentQuery();
        }
        return parent::getEloquentQuery()->where('seller_id', auth()->user()->id);
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // TextInput::make('app_name')
                //     ->required()
                //     ->label('App Name'),
                // TextInput::make('app_key')
                //     ->required()
                //     ->label('App Key'),
                // TextInput::make('app_secret')
                //     ->required()
                //     ->label('App Secret'),
                // TextInput::make('proxy')
                //     ->required()
                //     ->label('Proxy'),
                // TextInput::make('auth_link')
                //     ->required()
                //     ->label('Auth Link'),
                // Select::make('seller_id')
                //     ->searchable()
                //     ->preload()
                //     ->required()
                //     ->relationship(
                //         name: 'seller',
                //         titleAttribute: 'name',
                //         modifyQueryUsing: fn (Builder $query) => $query->role('Seller'),
                //     ),
                // ToggleButtons::make('status')
                //     ->options([
                //         'Active' => 'Active',
                //         'InActive' => 'InActive',
                //     ])->inline()->columnSpanFull()->required()->default('Active'),
                // TextInput::make('webhook_domain')
                //     ->label('Webhook Domain')
                //     ->unique(ignoreRecord: true)
                //     ->helperText(function ($state, $record) {
                //         if (!$state) return null;
                        
                //         return new \Illuminate\Support\HtmlString('<div class="space-y-2">
                //             <div class="font-medium">Link đăng ký App:</div>
                //             <div class="ml-4">• https://' . $state . '/tiktok-callback</div>
                //             <div class="ml-4">• https://' . $state . '/tiktok-webhook</div>
                //             ' . ($record ? '<div class="font-medium mt-3">Link cài app:</div>
                //             <div class="ml-4">• https://' . $state . '?app_id=' . $record->id . '</div>' : '') . '
                //         </div>');
                //     })
                //     ->columnSpanFull(),
        
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                
                TextColumn::make('app_name')->label('App Name')->searchable(),
                
                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn($state) => match ($state) {
                        'Active' => 'primary',
                        'InActive' => 'danger',
                        default => 'primary'
                    }),
                TextInputColumn::make('auth_link')->state(function ( $record) {
                    if($record->status === 'Active'){
                        return 'https://'.$record->webhook_domain.'?app_id='.$record->id;
                    }
                    return null;
                    //return route('tiktok.auth',['id' => $record->id ]);
                }),

                TextColumn::make('seller.name')->label('Seller ID'),
               
                TextColumn::make('webhook_domain')->label('Webhook Domain'),
                TextColumn::make('store_count')->counts('store')
                    ->label('Store Count'),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPartnerApps::route('/'),
            'create' => Pages\CreatePartnerApp::route('/create'),
            'edit' => Pages\EditPartnerApp::route('/{record}/edit'),
        ];
    }
}
