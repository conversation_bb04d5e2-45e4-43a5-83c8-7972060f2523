<?php

namespace App\Filament\App\Resources;

use App\Enums\KeywordTrackingStatus;
use App\Filament\App\Resources\KeywordTrackingResource\Pages;
use App\Filament\App\Resources\KeywordTrackingResource\RelationManagers;
use App\Filament\App\Widgets\KeywordTrackingGuideWidget;
use App\Models\KeywordTracking;
use App\Models\Store;
use App\Tables\Columns\TextAreaInputColumn;
use Filament\Forms;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Mokhosh\FilamentRating\Columns\RatingColumn;
use Mokhosh\FilamentRating\Components\Rating;

class KeywordTrackingResource extends Resource
{
    protected static ?string $model = KeywordTracking::class;
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin']) ;
    }
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    public static function getNavigationGroup(): ?string
    {
        return __('Ideas');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                ToggleButtons::make('status')
                    ->options(KeywordTrackingStatus::class)
                    ->inline()
                    ->columnSpanFull()
                    ->required()
                    ->hidden(fn($livewire) => $livewire instanceof Pages\CreateKeywordTracking),
                Forms\Components\Select::make('seller_id')
                    ->relationship(
                        'seller',
                        'name',
                        modifyQueryUsing: fn(Builder $query) =>
                        auth()->user()->hasRole('super_admin')
                            ? $query->withCount([
                                'keywordTrackings',
                                'keywordTrackings as pending_keywords_count' => fn(Builder $query) =>
                                $query->where('status', 'pending'),
                                'keywordTrackings as listing_keywords_count' => fn(Builder $query) =>
                                $query->where('status', 'listing'),
                            ])
                            : $query->whereIn('id', [
                                auth()->id(),
                                ...auth()->user()->managedSellers->pluck('id')
                            ])->withCount([
                                'keywordTrackings',
                                'keywordTrackings as pending_keywords_count' => fn(Builder $query) =>
                                $query->where('status', 'pending'),
                                'keywordTrackings as listing_keywords_count' => fn(Builder $query) =>
                                $query->where('status', 'listing'),
                            ])
                    )
                    ->getOptionLabelFromRecordUsing(fn($record) => view('filament.components.seller-select-option', [
                        'name' => $record->name,
                        'pending' => $record->pending_keywords_count,
                        'listing' => $record->listing_keywords_count,
                    ])->render())
                    ->preload()
                    ->allowHtml()
                    ->searchable()
                    ->afterStateUpdated(fn(Set $set) => $set('store_id', null))
                    ->required(),
                Forms\Components\Select::make('store_id')
                    ->options(function (Get $get) {
                        $sellerId = $get('seller_id');

                        return Store::query()
                            ->where('owner_id', $sellerId) // Thay đổi từ user_id sang owner_id
                            ->withCount([
                                'products',
                                'orders',
                                'keywordTrackings',
                                'keywordTrackings as pending_keywords_count' => fn(Builder $query) =>
                                $query->where('status', 'pending'),
                                'keywordTrackings as listing_keywords_count' => fn(Builder $query) =>
                                $query->where('status', 'listing'),
                            ])
                            ->get()
                            ->mapWithKeys(function ($store) {
                                return [$store->id => view('filament.components.store-select-option', [
                                    'name' => $store->name,
                                    'owner' => $store->owner->name,
                                    'products' => $store->products_count,
                                    'orders' => $store->orders_count,
                                    'pending' => $store->pending_keywords_count,
                                    'listing' => $store->listing_keywords_count,
                                ])->render()];
                            });
                    })
                    ->getOptionLabelUsing(function ($value) {
                        $store = Store::query()
                            ->withCount([
                                'products',
                                'orders',
                                'keywordTrackings',
                                'keywordTrackings as pending_keywords_count' => fn(Builder $query) =>
                                $query->where('status', 'pending'),
                                'keywordTrackings as listing_keywords_count' => fn(Builder $query) =>
                                $query->where('status', 'listing'),
                            ])
                            ->find($value);

                        if (!$store) return $value;

                        return new HtmlString(
                            view('filament.components.store-select-option', [
                                'name' => $store->name,
                                'owner' => $store->owner->name,
                                'products' => $store->products_count,
                                'orders' => $store->orders_count,
                                'pending' => $store->pending_keywords_count,
                                'listing' => $store->listing_keywords_count,
                            ])->render()
                        );
                    })
                    ->allowHtml()
                    ->searchable(['name', 'owner.name'])
                    ->preload()
                    ->reactive()
                    ->afterStateUpdated(function (Get $get, Set $set) {
                        if (! $get('seller_id')) {
                            $set('store_id', null);
                        }
                    })
                    ->columnSpanFull()
                    ->required(),

                Forms\Components\TextInput::make('seasonal')
                    ->label('Seasonal')
                    ->placeholder('e.g. Spring, Summer, etc'),
                Forms\Components\TextInput::make('keyword')
                    ->required()
                    ->maxLength(255),



                Textarea::make('note')
                    ->helperText('Ghi chú ,lưu ý về keyword')

                    ->rows(4),
                Textarea::make('result')

                    ->helperText('Kết quả đánh giá')
                    ->rows(4),
                Rating::make('rating')->allowZero(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                Tables\Columns\TextColumn::make('store.name')
                    ->searchable()
                    ->sortable()
                    ->description(fn($record) => $record->seller->name),

                Tables\Columns\TextColumn::make('keyword_info')
                    ->label('Keyword')
                    ->searchable(['keyword', 'seasonal'])
                    ->sortable('keyword')
                    ->view('filament.tables.columns.keyword-info')
                    ->html(),
                    Tables\Columns\TextColumn::make('ideas_count')
                    ->label('Ideas')
                    ->counts([
                        'ideas',
                        'ideas as ideas_new_count' => fn($query) => $query->where('status', 'new'),
                        'ideas as ideas_under_review_count' => fn($query) => $query->where('status', 'under_review'),
                        'ideas as ideas_approved_count' => fn($query) => $query->where('status', 'approved'),
                        'ideas as ideas_in_design_count' => fn($query) => $query->where('status', 'in_design'),
                        'ideas as ideas_completed_count' => fn($query) => $query->where('status', 'completed'),
                        'ideas as ideas_rejected_count' => fn($query) => $query->where('status', 'rejected'),
                    ])
                    ->sortable()
                    ->alignCenter()
                    ->view('filament.tables.columns.ideas-count')
                    ->tooltip(function ($record): string {
                        $details = [];
                        
                        if ($record->ideas_new_count > 0) 
                            $details[] = "New: {$record->ideas_new_count}";
                        if ($record->ideas_under_review_count > 0) 
                            $details[] = "Under Review: {$record->ideas_under_review_count}";
                        if ($record->ideas_approved_count > 0) 
                            $details[] = "Approved: {$record->ideas_approved_count}";
                        if ($record->ideas_in_design_count > 0) 
                            $details[] = "In Design: {$record->ideas_in_design_count}";
                        if ($record->ideas_completed_count > 0) 
                            $details[] = "Completed: {$record->ideas_completed_count}";
                        if ($record->ideas_rejected_count > 0) 
                            $details[] = "Rejected: {$record->ideas_rejected_count}";
                            
                        return implode("\n", $details);
                    }),


                RatingColumn::make('rating'),
                TextAreaInputColumn::make('note')->extraAttributes(['class' => 'w-[180px]']),
                TextAreaInputColumn::make('result')->extraAttributes(['class' => 'w-[180px]']),
            ])
            ->filters([
                SelectFilter::make('rating')
                    ->options([
                        '0' => '0 stars ☆☆☆☆☆',
                        '1' => '1 star ★☆☆☆☆',
                        '2' => '2 stars ★★☆☆☆',
                        '3' => '3 stars ★★★☆☆',
                        '4' => '4 stars ★★★★☆',
                        '5' => '5 stars ★★★★★',
                    ])
                    ->label('Rating')
                    ->multiple() // cho phép chọn nhiều rating cùng lúc
                    ->query(function ($query, array $data) {
                        if (!empty($data['values'])) {
                            $query->whereIn('rating', $data['values']);
                        }
                    }),
                Tables\Filters\SelectFilter::make('seller')
                    ->relationship(
                        'seller',
                        'name',
                        modifyQueryUsing: fn(Builder $query) =>
                        auth()->user()->hasRole('super_admin')
                            ? $query
                            : $query->whereIn('id', [
                                auth()->id(), // thêm chính user hiện tại
                                ...auth()->user()->managedSellers->pluck('id')
                            ])
                    )->searchable()->preload(),
            ])
            ->actions([
                Tables\Actions\EditAction::make('Add Ideas'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\IdeasRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKeywordTrackings::route('/'),
            'create' => Pages\CreateKeywordTracking::route('/create'),
            'edit' => Pages\EditKeywordTracking::route('/{record}/edit'),
        ];
    }
}
