<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\BlankResource\Pages;
use App\Models\Blank;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class BlankResource extends Resource
{
    protected static ?string $model = Blank::class;

    protected static ?string $navigationIcon = 'heroicon-o-square-3-stack-3d';

    protected static ?string $navigationGroup = 'Design & Media';

    protected static ?string $navigationLabel = 'Blanks (Phôi)';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->required()
                ->maxLength(255),

            Forms\Components\FileUpload::make('image')
                ->image()
                ->columnSpanFull(),

            Forms\Components\TextInput::make('sku')
                ->required()
                ->unique(ignoreRecord: true)
                ->maxLength(255),

            Forms\Components\Select::make('type')
                ->required()
                ->options([
                    'hoodie' => 'Hoodie',
                    't-shirt' => 'T-Shirt',
                    'sweatshirt' => 'Sweatshirt',
                    'tank-top' => 'Tank Top',
                    'tumbler' => 'Tumbler',
                ]),

            Forms\Components\Textarea::make('description')
                ->maxLength(65535)
                ->columnSpanFull(),

            Forms\Components\KeyValue::make('attributes')
                ->keyLabel('Attribute')
                ->valueLabel('Value')
                ->columnSpanFull(),

            Forms\Components\TextInput::make('stock')
                ->required()
                ->numeric()
                ->default(0),

            Forms\Components\TextInput::make('cost')
                ->required()
                ->numeric()
                ->prefix('$')
                ->default(0),

            Forms\Components\TextInput::make('processing_cost')
                ->required()
                ->numeric()
                ->prefix('$')
                ->default(0)
                ->label('Processing Cost'),

            Forms\Components\TextInput::make('supplier')
                ->maxLength(255),

            Forms\Components\Select::make('status')
                ->required()
                ->options([
                    'active' => 'Active',
                    'inactive' => 'Inactive',
                    'discontinued' => 'Discontinued',
                ])
                ->default('active'),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->circular(),

                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('sku')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('stock')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('cost')
                    ->money()
                    ->sortable(),

                Tables\Columns\TextColumn::make('processing_cost')
                    ->money()
                    ->sortable()
                    ->label('Processing Cost'),

                Tables\Columns\TextColumn::make('supplier')
                    ->searchable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'danger' => 'inactive',
                        'warning' => 'discontinued',
                        'success' => 'active',
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type'),
                Tables\Filters\SelectFilter::make('status'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBlanks::route('/'),
            'create' => Pages\CreateBlank::route('/create'),
            'edit' => Pages\EditBlank::route('/{record}/edit'),
        ];
    }
}