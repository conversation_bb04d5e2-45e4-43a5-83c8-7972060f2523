<?php

namespace App\Filament\App\Resources\DailyReportResource\Widgets;

use Filament\Widgets\Widget;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use App\Models\User;
use App\Models\DailyReport;
use App\Traits\HasUserFilter;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;

class MissingReportsWidget extends Widget
{
    use InteractsWithPageFilters, HasUserFilter;

    protected static string $view = 'filament.app.resources.daily-report-resource.widgets.missing-reports-widget';

    protected int | string | array $columnSpan = 'full';

    protected static ?string $pollingInterval = '30s';

    public ?string $dateRange = null;
    public array $missingReports = [];
    public int $totalSellers = 0;
    public int $sellersWithReports = 0;
    public int $sellersWithoutReports = 0;
    public int $totalReports = 0;
    public int $approvedReports = 0;
    public int $rejectedReports = 0;
    public int $pendingReports = 0;



    public function mount(): void
    {
        $this->loadMissingReports();
    }

    #[On('filtersChanged')]
    public function updateFilters($filters = null): void
    {
        if ($filters && isset($filters['report_date']['report_date'])) {
            $this->dateRange = $filters['report_date']['report_date'];

            // Reload data với filter mới
            $this->loadMissingReports();
            $this->dispatch('$refresh');
        }
    }

    public function refreshWidget(): void
    {
        $this->loadMissingReports();
    }



    public function loadMissingReports(): void
    {
        // Nếu chưa có dateRange, sử dụng mặc định là hôm nay
        if ($this->dateRange === null) {
            $this->dateRange = now()->format('d/m/Y') . ' - ' . now()->format('d/m/Y');
        }

        // Parse date range từ filter
        $dates = $this->parseDateRange($this->dateRange);

        // Lấy seller IDs mà user hiện tại có quyền truy cập
        $accessibleUserIds = static::getAccessibleUserIds();

        // Lấy tất cả seller mà user có quyền xem
        $sellers = User::role('Seller')
            ->whereIn('id', $accessibleUserIds)
            ->orderBy('name')
            ->get();

        $this->totalSellers = $sellers->count();
        $missingData = [];
        $sellersWithReports = 0;

        foreach ($sellers as $seller) {
            $missingDates = [];

            foreach ($dates as $date) {
                $hasReport = DailyReport::where('seller_id', $seller->id)
                    ->whereDate('report_date', $date)
                    ->exists();

                if (!$hasReport) {
                    $missingDates[] = $date->format('d/m/Y');
                }
            }

            if (!empty($missingDates)) {
                $missingData[] = [
                    'seller' => $seller,
                    'missing_dates' => $missingDates,
                    'missing_count' => count($missingDates),
                    'total_days' => count($dates),
                ];
            } else {
                $sellersWithReports++;
            }
        }

        $this->missingReports = $missingData;
        $this->sellersWithReports = $sellersWithReports;
        $this->sellersWithoutReports = count($missingData);

        // Thống kê báo cáo theo trạng thái trong khoảng thời gian
        $this->loadReportStatistics($dates);
    }

    private function loadReportStatistics(array $dates): void
    {
        if (empty($dates)) {
            $this->totalReports = 0;
            $this->approvedReports = 0;
            $this->rejectedReports = 0;
            $this->pendingReports = 0;
            return;
        }

        $startDate = $dates[0];
        $endDate = end($dates);

        // Lấy seller IDs mà user hiện tại có quyền truy cập
        $accessibleUserIds = static::getAccessibleUserIds();

        // Query báo cáo trong khoảng thời gian và chỉ của seller có quyền xem
        $reportsQuery = DailyReport::whereBetween('report_date', [$startDate, $endDate])
            ->whereIn('seller_id', $accessibleUserIds);

        $this->totalReports = $reportsQuery->count();
        $this->approvedReports = (clone $reportsQuery)->where('status', 'reviewed')->count();
        $this->rejectedReports = (clone $reportsQuery)->where('status', 'rejected')->count();
        $this->pendingReports = (clone $reportsQuery)->whereIn('status', ['draft', 'submitted'])->count();
    }

    private function parseDateRange(?string $dateRange): array
    {
        if (!$dateRange) {
            // Mặc định là hôm nay
            return [Carbon::today()];
        }

        // Parse date range format: "01/01/2024 - 31/01/2024"
        if (strpos($dateRange, ' - ') !== false) {
            [$startStr, $endStr] = explode(' - ', $dateRange);
            $startDate = Carbon::createFromFormat('d/m/Y', trim($startStr));
            $endDate = Carbon::createFromFormat('d/m/Y', trim($endStr));

            $dates = [];
            $current = $startDate->copy();

            while ($current->lte($endDate)) {
                $dates[] = $current->copy();
                $current->addDay();
            }

            return $dates;
        }

        // Single date
        try {
            return [Carbon::createFromFormat('d/m/Y', $dateRange)];
        } catch (\Exception $e) {
            return [Carbon::today()];
        }
    }

    public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Leader']);
    }
}
