<?php

namespace App\Filament\App\Resources\DailyReportResource\Pages;

use App\Filament\App\Resources\DailyReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Carbon\Carbon;

class ListDailyReports extends ListRecords
{
    use ExposesTableToWidgets, HasFiltersForm;
    protected static string $resource = DailyReportResource::class;

    /**
     * Mount method để set filter mặc định
     */
    public function mount(): void
    {
        parent::mount();

        // Set filter mặc định cho report_date là hôm nay nếu chưa có filter nào được set
        if (!isset($this->tableFilters['report_date']['report_date'])) {
            $today = now()->format('d/m/Y');
            $this->tableFilters['report_date'] = ['report_date' => $today . ' - ' . $today];
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            \App\Filament\App\Resources\DailyReportResource\Widgets\MissingReportsWidget::class,
        ];
    }

    public function filtersForm(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('report_date')
                    ->label('Ngày báo cáo')
                    ->default(now()),
            ]);
    }

    public function updatedTableFilters(): void
    {
        // Dispatch event để widget cập nhật khi filter thay đổi
        $this->dispatch('filtersChanged', filters: $this->tableFilters);
    }

    public function updated($propertyName): void
    {
        if (str_starts_with($propertyName, 'tableFilters')) {
            $this->dispatch('filtersChanged', filters: $this->tableFilters);
        }
    }


}
