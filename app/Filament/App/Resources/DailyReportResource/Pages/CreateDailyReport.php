<?php

namespace App\Filament\App\Resources\DailyReportResource\Pages;

use App\Filament\App\Resources\DailyReportResource;
use App\Models\DailyReport;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class CreateDailyReport extends CreateRecord
{
    protected static string $resource = DailyReportResource::class;



    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Đảm bảo seller_id luôn có giá trị
        $data['seller_id'] = Auth::id();

        // Đảm bảo report_date luôn có giá trị
        if (empty($data['report_date'])) {
            $data['report_date'] = today()->format('Y-m-d');
        }

        return $data;
    }

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        // Kiểm tra xem đã có báo cáo cho ngày này chưa
        $existingReport = DailyReport::where('seller_id', $data['seller_id'])
            ->where('report_date', $data['report_date'])
            ->first();

        if ($existingReport) {
            // Thông báo lỗi và dừng việc tạo record
            Notification::make()
                ->title('❌ Không thể tạo báo cáo')
                ->body('Bạn chỉ được tạo một báo cáo mỗi ngày. Báo cáo cho ngày ' . date('d/m/Y', strtotime($data['report_date'])) . ' đã tồn tại.')
                ->danger()
                ->persistent()
                ->send();

            // Dừng việc tạo record bằng cách throw exception
            $this->halt();
        }

        // Nếu chưa có, tạo mới
        return parent::handleRecordCreation($data);
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
