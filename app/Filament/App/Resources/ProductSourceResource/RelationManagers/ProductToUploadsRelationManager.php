<?php

namespace App\Filament\App\Resources\ProductSourceResource\RelationManagers;

use App\Enums\ProductUploadStatus;
use App\Enums\TiktokShopStatus;
use App\Filament\App\Resources\ProductToUploadResource;
use App\Forms\Components\ImageGalleryField;
use App\Models\ProductToUpload;
use App\Models\Store;
use App\Models\Template;
use App\Services\OpenAIService;
use App\Services\Tiktok\ProductUploadService;
use App\Services\Tiktok\TiktokShopService;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\On;

class ProductToUploadsRelationManager extends RelationManager
{
    protected static string $relationship = 'productToUploads';

    #[On('auto-select-warehouse')]
    public function autoSelectWarehouse($warehouseId)
    {
        try {
            $this->mountedTableActionsData[0]['warehouse_id'] = $warehouseId;
        } catch (\Exception $e) {
        }
    }


    public function hasCombinedRelationManagerTabsWithContent(): bool
    {
        return false;
    }


    public function form(Form $form): Form
    {
        $productSource = $this->getOwnerRecord();

        return $form
            ->schema([
                Forms\Components\TextInput::make('product_title')
                    ->live()
                    ->helperText(fn($state): string => strlen($state) . ' / 255 characters')
                    ->required()
                    ->maxLength(255)
                    ->default($productSource->title)
                    ->suffixAction(
                        Action::make('rewriteTitle')
                            ->icon('heroicon-m-sparkles')
                            ->label('AI Rewrite')
                            ->color('primary')
                            ->action(function (Get $get, Set $set) {
                                try {
                                    $openAI = new OpenAIService();
                                    $newTitle = $openAI->rewriteTitle($get('product_title'));
                                    $set('product_title', $newTitle);
                                    Notification::make()
                                        ->success()
                                        ->title('Title rewritten by AI')
                                        ->send();
                                } catch (\Exception $e) {
                                    Notification::make()
                                        ->danger()
                                        ->title('Error')
                                        ->body($e->getMessage())
                                        ->send();
                                }
                            })
                    )->columnSpanFull(),

                RichEditor::make('description')->columnSpanFull()
                    ->extraInputAttributes(['style' => 'min-height: 200px; max-height: 400px; overflow-y: auto;']),

                Select::make('template_id')
                    ->relationship('template', 'name')

                    ->afterStateUpdated(function (Get $get, Set $set) {
                        // Lấy template và cập nhật mô tả
                        $template = Template::find($get('template_id'));
                        if ($template && $template->description) {
                            $set('description', $template->description);
                            Notification::make()
                                ->success()
                                ->title('Description updated from template')
                                ->send();
                        }
                    })
                    ->default($productSource->template_id)
                    ->preload()
                    ->searchable()
                    ->live()

                    ->required()
                    ->columnSpan(2),

                Select::make('store_id')
                    ->label('Select Store')
                    ->options(function () {
                        return Store::where('app_partner_id', '!=', '')
                            ->whereIn('tiktok_shop_status', [TiktokShopStatus::Live, TiktokShopStatus::NotConnected])
                            ->orderBy('id', 'desc')
                            ->pluck('name', 'id');
                    })
                    ->afterStateUpdated(function (Get $get, Set $set) {
                        $set('warehouse_id', null);
                    })
                    ->searchable()
                    ->preload()

                    ->required()
                    ->live(),
                Select::make('warehouse_id')
                    ->options(function (Get $get, Set $set) {
                        if (!$get('store_id')) {
                            $set('warehouse_id', null);
                            return [];
                        }

                        $warehouses = $this->loadWarehouses($get('store_id'));
                        if (count($warehouses) === 1) {

                            if ($get('warehouse_id') == null) {
                                $warehouseId = array_key_first($warehouses);
                                $this->js('setTimeout(() => {
                                    $wire.autoSelectWarehouse("' . $warehouseId . '")
                                }, 100)');
                            }
                        }
                        return $warehouses;
                    })
                    ->live()
                    ->required(),

                Select::make('save_mode')

                    ->options([
                        'AS_DRAFT' => 'Draft',
                        'LISTING' => 'Published',
                    ])
                    ->default('LISTING')
                    ->required()
                    ->placeholder('Select status'),
                Forms\Components\DateTimePicker::make('scheduled_at')
                    ->default(now()),
                Actions::make([

                    Action::make('autoSchedule')
                        ->icon('heroicon-m-clock')
                        ->button()
                        ->label('Auto Schedule')
                        ->color('success')
                        ->size('sm')
                        ->tooltip('Tự động đặt lịch upload trong giờ làm việc của Mỹ (9 AM - 10 PM EST)')

                        ->extraAttributes([
                            'class' => 'flex items-center gap-1'
                        ])
                        ->action(function (Get $get, Set $set) {
                            // Convert current time to EST (US Eastern Time)
                            $now = now()->setTimezone('America/New_York');
                            $optimalTime = $now->copy();

                            // If current time in US is between 22:00 and 09:00 (sleeping hours)
                            if ($now->hour >= 22 || $now->hour < 9) {
                                // Set time to 9:00 AM the next day
                                $optimalTime = $now->hour >= 22
                                    ? $now->addDay()->setHour(9)->setMinute(rand(0, 30))->setSecond(rand(0, 59))
                                    : $now->setHour(9)->setMinute(rand(0, 30))->setSecond(rand(0, 59));
                            } else {
                                // During working hours, add a random delay between 5-30 minutes
                                $optimalTime = $now->addMinutes(rand(5, 30))->setSecond(rand(0, 59));
                            }

                            // Convert back to Vietnam timezone for display
                            $optimalTime = $optimalTime->setTimezone('Asia/Ho_Chi_Minh');

                            // Update form value
                            $set('scheduled_at', $optimalTime->format('Y-m-d H:i:s'));
                        })
                ])->columnSpanFull()->fullWidth(),
                ImageGalleryField::make('images')
                    ->default($productSource->images)
                    ->label('Product Images')
                    ->columnSpanFull(),
            ]);
    }
    public function loadWarehouses($storeId)
    {
        $store = Store::find($storeId);
        $tiktokService = new TiktokShopService($store);

        try {
            $warehouses = $tiktokService->getWarehouseList();
            return collect($warehouses)
                ->filter(function ($warehouse) {
                    return $warehouse['type'] === 'SALES_WAREHOUSE';
                })
                ->mapWithKeys(function ($warehouse) {
                    return [$warehouse['id'] => $warehouse['name']];
                })
                ->toArray();
        } catch (\Exception $e) {
            return [];
            Notification::make()
                ->title('Unable to load warehouses: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
    public function table(Table $table): Table
    {
        return ProductToUploadResource::table($table)
            ->recordTitleAttribute('product_title')

            ->headerActions([
                Tables\Actions\CreateAction::make()->slideOver()->icon('heroicon-m-plus')
                    ->label('Schedule Upload')->color('success'),
            ])
            ->actions([
                Tables\Actions\Action::make('upload_now')
                    ->label('Upload Now')
                    ->icon('heroicon-m-cloud-arrow-up')
                    ->color('primary')
                    ->button()
                    ->visible(
                        fn($record) => !$record->product_id && $record->status !== ProductUploadStatus::Processing
                    )
                    ->action(function (ProductToUpload $record) {
                        try {
                            $service = new ProductUploadService();
                            $response = $service->uploadProduct($record);

                            if (!isset($response['product_id'])) {
                                throw new \Exception($response['message'] ?? 'Unknown error');
                            }

                            $record->update([
                                'status' => ProductUploadStatus::Completed,
                                'uploaded_at' => now(),
                                'product_id' => $response['product_id'],
                                'error_message' => null,
                                'attempts' => $record->attempts + 1,
                                'last_attempt_at' => now(),
                            ]);

                            Notification::make()
                                ->success()
                                ->title('Success')
                                ->body(new HtmlString("Product uploaded successfully!<br>ID: {$response['product_id']}"))
                                ->duration(10000)
                                ->persistent()
                                ->send();
                        } catch (\Exception $e) {
                            $attempts = $record->attempts + 1;
                            $status = $attempts >= 5 ?
                                ProductUploadStatus::Failed :
                                ProductUploadStatus::Pending;

                            $record->update([
                                'status' => $status,
                                'error_message' => $e->getMessage(),
                                'attempts' => $attempts,
                                'last_attempt_at' => now(),
                            ]);

                            Notification::make()
                                ->danger()
                                ->title('Error')
                                ->body(new HtmlString("Upload failed (Attempt {$attempts}/5)<br>" . $e->getMessage()))
                                ->duration(10000)
                                ->persistent()
                                ->send();
                        }
                    }),
                Tables\Actions\EditAction::make()
                    ->slideOver(),
                // 
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
