<?php

namespace App\Filament\App\Resources\ProductSourceResource\Pages;

use App\Filament\App\Resources\ProductSourceResource;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Filament\Actions;

class ListProductSources extends ListRecords
{
    protected static string $resource = ProductSourceResource::class;


    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Sources')
                ->icon('heroicon-o-squares-2x2')  // Grid icon
                ->modifyQueryUsing(fn(Builder $query) => $query),
            'scheduled' => Tab::make('Scheduled')
                ->icon('heroicon-o-clock')
                ->modifyQueryUsing(function (Builder $query) {
                    return $query->has('productToUploads'); // Có quan hệ với productToUploads = đã hẹn giờ
                })
                ->badge(function () {
                    return static::getModel()::has('productToUploads')->count();
                })
                ->badgeColor('success'),

            'not_scheduled' => Tab::make('Not Scheduled')
                ->icon('heroicon-o-x-circle')
                ->modifyQueryUsing(function (Builder $query) {
                    return $query->doesntHave('productToUploads'); // Không có quan hệ với productToUploads = chưa hẹn giờ
                })
                ->badge(function () {
                    return static::getModel()::doesntHave('productToUploads')->count();
                })
                ->badgeColor('danger'),

            'uploaded' => Tab::make('Uploaded')
                ->icon('heroicon-o-check-circle')
                ->modifyQueryUsing(function (Builder $query) {
                    return $query->whereHas('productToUploads', function ($query) {
                        $query->whereNotNull('uploaded_at'); // Có productToUploads và đã upload
                    });
                })
                ->badge(function () {
                    return static::getModel()::whereHas('productToUploads', function ($query) {
                        $query->whereNotNull('uploaded_at');
                    })->count();
                })
                ->badgeColor('success'),
        ];
    }


    public function getDefaultActiveTab(): string | int | null
    {
        return 'all';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
