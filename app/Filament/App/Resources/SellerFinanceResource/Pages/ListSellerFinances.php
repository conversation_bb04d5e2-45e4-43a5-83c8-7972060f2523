<?php

namespace App\Filament\App\Resources\SellerFinanceResource\Pages;

use App\Filament\App\Resources\SellerFinanceResource;

use App\Models\SellerFinance;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListSellerFinances extends ListRecords
{
    protected static string $resource = SellerFinanceResource::class;

    public function getTabs(): array
    {
        $baseQuery = SellerFinance::query();

        return [
            'all' => Tab::make('Tất Cả')
                ->badge($baseQuery->count())
                ->badgeColor('primary'),
            
            'unpaid' => Tab::make('Chưa Thanh Toán')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereRaw('(base_salary + total_bonus) > (
                        SELECT COALESCE(SUM(amount), 0) 
                        FROM invoice_items 
                        WHERE seller_finance_id = seller_finances.id 
                        AND EXISTS (
                            SELECT 1 FROM invoices 
                            WHERE invoices.id = invoice_items.invoice_id 
                            AND invoices.status != "cancelled"
                        )
                    )')
                )
                ->badge($baseQuery->clone()
                    ->whereRaw('(base_salary + total_bonus) > (
                        SELECT COALESCE(SUM(amount), 0) 
                        FROM invoice_items 
                        WHERE seller_finance_id = seller_finances.id 
                        AND EXISTS (
                            SELECT 1 FROM invoices 
                            WHERE invoices.id = invoice_items.invoice_id 
                            AND invoices.status != "cancelled"
                        )
                    )')
                    ->count())
                ->badgeColor('danger'),

            'partial_paid' => Tab::make('Thanh Toán Một Phần')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereHas('invoiceItems', function ($q) {
                        $q->whereHas('invoice', fn($q) => $q->whereNotIn('status', ['cancelled']));
                    })
                    ->whereRaw('(base_salary + total_bonus) > (
                        SELECT COALESCE(SUM(amount), 0) 
                        FROM invoice_items 
                        WHERE seller_finance_id = seller_finances.id 
                        AND EXISTS (
                            SELECT 1 FROM invoices 
                            WHERE invoices.id = invoice_items.invoice_id 
                            AND invoices.status != "cancelled"
                        )
                    )')
                )
                ->badge($baseQuery->clone()
                    ->whereHas('invoiceItems')
                    ->whereRaw('(base_salary + total_bonus) > (
                        SELECT COALESCE(SUM(amount), 0) 
                        FROM invoice_items 
                        WHERE seller_finance_id = seller_finances.id 
                        AND EXISTS (
                            SELECT 1 FROM invoices 
                            WHERE invoices.id = invoice_items.invoice_id 
                            AND invoices.status != "cancelled"
                        )
                    )')
                    ->count())
                ->badgeColor('warning'),

            'fully_paid' => Tab::make('Đã Thanh Toán')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereRaw('(base_salary + total_bonus) <= (
                        SELECT COALESCE(SUM(amount), 0) 
                        FROM invoice_items 
                        WHERE seller_finance_id = seller_finances.id 
                        AND EXISTS (
                            SELECT 1 FROM invoices 
                            WHERE invoices.id = invoice_items.invoice_id 
                            AND invoices.status != "cancelled"
                        )
                    )')
                )
                ->badge($baseQuery->clone()
                    ->whereRaw('(base_salary + total_bonus) <= (
                        SELECT COALESCE(SUM(amount), 0) 
                        FROM invoice_items 
                        WHERE seller_finance_id = seller_finances.id 
                        AND EXISTS (
                            SELECT 1 FROM invoices 
                            WHERE invoices.id = invoice_items.invoice_id 
                            AND invoices.status != "cancelled"
                        )
                    )')
                    ->count())
                ->badgeColor('success'),

            'pending_invoice' => Tab::make('Chờ Duyệt Hoá Đơn')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereHas('invoiceItems', function ($q) {
                        $q->whereHas('invoice', fn($q) => $q->where('status', 'pending'));
                    })
                )
                ->badge($baseQuery->clone()
                    ->whereHas('invoiceItems', function ($q) {
                        $q->whereHas('invoice', fn($q) => $q->where('status', 'pending'));
                    })
                    ->count())
                ->badgeColor('warning'),

            'no_invoice' => Tab::make('Chưa Tạo Hoá Đơn')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereDoesntHave('invoiceItems')
                )
                ->badge($baseQuery->clone()
                    ->whereDoesntHave('invoiceItems')
                    ->count())
                ->badgeColor('danger'),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
          //  Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            
        ];
    }
}
