<?php

namespace App\Filament\App\Resources\MediaServicePricingResource\Pages;

use App\Filament\App\Resources\MediaServicePricingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use App\Enums\MediaRequestStatus;
use Illuminate\Database\Eloquent\Builder;

class ListMediaServicePricings extends ListRecords
{
    protected static string $resource = MediaServicePricingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Tạo dịch vụ mới')
                ->icon('heroicon-m-plus'),

            Actions\Action::make('pricing_info')
                ->label('Hướng dẫn sử dụng')
                ->icon('heroicon-m-information-circle')
                ->color('info')
                ->modalHeading('Hướng dẫn quản lý cấu hình giá dịch vụ Media')
                ->modalDescription('
                    • Trang này quản lý cấu hình giá cho các loại dịch vụ media
                    • Tạo mới, chỉnh sửa, và quản lý giá dịch vụ
                    • Cấu hình phí rush, phí revision, thời gian giao hàng
                    • Bulk actions để cập nhật giá hàng loạt
                    • Chỉ Admin và User Manager mới có quyền truy cập
                ')
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Đóng')
                ->action(fn () => null),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('Tất cả')
                ->icon('heroicon-o-rectangle-stack')
                ->badge(static::getModel()::count())
                ->badgeColor('primary'),

            'active' => Tab::make('Đang hoạt động')
                ->icon('heroicon-o-check-circle')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_active', true))
                ->badge(static::getModel()::where('is_active', true)->count())
                ->badgeColor('success'),

            'inactive' => Tab::make('Không hoạt động')
                ->icon('heroicon-o-x-circle')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_active', false))
                ->badge(static::getModel()::where('is_active', false)->count())
                ->badgeColor('danger'),

            'featured' => Tab::make('Nổi bật')
                ->icon('heroicon-o-star')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_featured', true))
                ->badge(static::getModel()::where('is_featured', true)->count())
                ->badgeColor('warning'),

            'low_price' => Tab::make('Giá thấp')
                ->icon('heroicon-o-currency-dollar')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('base_price', '<=', 15))
                ->badge(static::getModel()::where('base_price', '<=', 15)->count())
                ->badgeColor('info'),
        ];
    }

    public function getDefaultActiveTab(): string | int | null
    {
        return 'all';
    }
}
