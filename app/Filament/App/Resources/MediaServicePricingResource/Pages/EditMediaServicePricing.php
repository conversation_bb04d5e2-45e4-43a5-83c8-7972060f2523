<?php

namespace App\Filament\App\Resources\MediaServicePricingResource\Pages;

use App\Filament\App\Resources\MediaServicePricingResource;
use App\Models\SellerFundRequest;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditMediaServicePricing extends EditRecord
{
    protected static string $resource = MediaServicePricingResource::class;

    public function getTitle(): string
    {
        return 'Chỉnh sửa dịch vụ: ' . $this->record->name;
    }

    public function getSubheading(): string
    {
        return 'Mã dịch vụ: ' . $this->record->service_type;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('back')
                ->label('Quay lại danh sách')
                ->url(fn (): string => MediaServicePricingResource::getUrl('index'))
                ->icon('heroicon-m-arrow-left')
                ->color('gray'),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Set updated_by to current user
        $data['updated_by'] = auth()->id();

        // Update last_price_update if base_price changed
        if (isset($data['base_price']) && $data['base_price'] != $this->record->base_price) {
            $data['last_price_update'] = now();
        }

        return $data;
    }

    protected function afterSave(): void
    {
        Notification::make()
            ->title('Cập nhật thành công')
            ->body("Dịch vụ '{$this->record->name}' đã được cập nhật")
            ->success()
            ->send();
    }
}
