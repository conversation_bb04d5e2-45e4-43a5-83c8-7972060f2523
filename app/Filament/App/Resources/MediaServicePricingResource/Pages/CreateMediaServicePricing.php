<?php

namespace App\Filament\App\Resources\MediaServicePricingResource\Pages;

use App\Filament\App\Resources\MediaServicePricingResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateMediaServicePricing extends CreateRecord
{
    protected static string $resource = MediaServicePricingResource::class;

    public function getTitle(): string
    {
        return 'Tạo dịch vụ media mới';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('back')
                ->label('Quay lại danh sách')
                ->url(fn (): string => MediaServicePricingResource::getUrl('index'))
                ->icon('heroicon-m-arrow-left')
                ->color('gray'),
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set created_by to current user
        $data['created_by'] = auth()->id();
        $data['updated_by'] = auth()->id();
        
        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
