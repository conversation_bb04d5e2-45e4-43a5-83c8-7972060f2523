<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\GeneratedImageResource\Pages;
use App\Filament\App\Resources\GeneratedImageResource\RelationManagers;
use App\Models\GeneratedImage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Actions\Action;
use App\Jobs\ProcessImageUpload;
use Filament\Notifications\Notification;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\IconPosition;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class GeneratedImageResource extends Resource
{
    protected static ?string $model = GeneratedImage::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';
    protected static ?string $navigationLabel = 'Ảnh đã tạo';
    protected static ?string $modelLabel = 'Ảnh đã tạo';
    protected static ?string $pluralModelLabel = 'Ảnh đã tạo';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationGroup = 'Tools';


    public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->required(),
                Forms\Components\Textarea::make('prompt')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('original_url')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('s3_path')
                    ->maxLength(255),
                Forms\Components\TextInput::make('s3_url')
                    ->maxLength(255),
                Forms\Components\TextInput::make('filename')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('file_size')
                    ->numeric(),
                Forms\Components\TextInput::make('mime_type')
                    ->required()
                    ->maxLength(255)
                    ->default('image/jpeg'),
                Forms\Components\TextInput::make('width')
                    ->numeric(),
                Forms\Components\TextInput::make('height')
                    ->numeric(),
                Forms\Components\TextInput::make('metadata'),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\Textarea::make('error_message')
                    ->columnSpanFull(),
                Forms\Components\DateTimePicker::make('uploaded_at'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->contentGrid([
                'default' => 1,
                'sm' => 2,
                'md' => 3,
                'lg' => 4,
                'xl' => 5,
                '2xl' => 5,
            ])
            ->columns([
                Tables\Columns\Layout\Stack::make([
                    Tables\Columns\ViewColumn::make('card')
                        ->view('filament.tables.columns.generated-image-card')
                        ->label('')
                        ->searchable(false)
                        ->sortable(false),
                ])->space(0),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'pending' => 'Đang xử lý',
                        'uploaded' => 'Đã upload',
                        'failed' => 'Thất bại',
                    ]),

                Tables\Filters\SelectFilter::make('user')
                    ->label('Người tạo')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->visible(fn () => Auth::user()->hasRole('super_admin')),

                DateRangeFilter::make('created_at')
                    ->label('Ngày tạo')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->alwaysShowCalendar()
                    ->displayFormat('DD/MM/YYYY')
                    ->separator(' - '),
            ])
            ->emptyStateHeading('Chưa có ảnh nào')
            ->emptyStateDescription('Bạn chưa tạo ảnh nào. Hãy bắt đầu tạo ảnh mới.')
            ->emptyStateIcon('heroicon-o-photo')
            ->emptyStateActions([
                Tables\Actions\Action::make('create')
                    ->label('Tạo ảnh mới')
                    ->url(fn () => GeneratedImageResource::getUrl('create'))
                    ->icon('heroicon-o-plus')
                    ->button()
                    ->color('primary'),
            ])
            ->recordAction(null) // Disable row click
            ->recordUrl(null) // Disable row click
            ->actions([])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);

        // Chỉ hiển thị ảnh của user hiện tại (trừ super_admin)
        if (!Auth::user()->hasRole('super_admin')) {
            $query->where('user_id', Auth::id());
        }

        return $query;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGeneratedImages::route('/'),
            'create' => Pages\CreateGeneratedImage::route('/create'),
        ];
    }
}
