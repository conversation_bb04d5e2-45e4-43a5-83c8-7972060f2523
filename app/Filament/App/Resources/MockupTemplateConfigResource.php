<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\MockupTemplateConfigResource\Pages;
use App\Filament\Forms\Components\DesignPositionEditor;
use App\Models\MockupTemplateConfig;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\Facades\DB;

class MockupTemplateConfigResource extends Resource
{
    protected static ?string $model = MockupTemplateConfig::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static bool $shouldRegisterNavigation = false;


    public static function form(Form $form): Form
    {
        $isEdit = $form->getRecord() !== null;

        $schema = [
            Forms\Components\Group::make()
                ->schema([
                    Forms\Components\Grid::make(2)
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->required()
                                ->maxLength(255)
                                ->columnSpan(1),
                            Forms\Components\Toggle::make('has_back_view')
                                ->afterStateUpdated(function ($state, $livewire) {
                                    $livewire->dispatch('has-back-view-changed', value: $state);
                                })
                                ->label('Has Back View')
                                ->reactive()
                                ->inline(false)
                                ->columnSpan(1),
                        ]),
                    Forms\Components\FileUpload::make('apparel_image')
                        ->disk('s3')
                        ->directory('mockup')
                        ->visibility('public')
                        ->required()
                        ->image()
                        ->imageResizeMode('contain')
                        ->imageResizeTargetWidth('1200')
                        ->imageResizeTargetHeight('1200'),
                ])->columns(2)
        ];

        if (!$isEdit) {
            $schema[] = Forms\Components\Placeholder::make('design_position_note')
                ->content('⚡️ BƯỚC TIẾP THEO: Sau khi tạo mockup, bạn cần thiết lập vị trí của thiết kế trong trang chỉnh sửa')
                ->extraAttributes([
                    'class' => 'p-4 rounded-lg border-2 bg-primary-50/50 text-primary-600 border-primary-200/50 dark:bg-primary-400/10 dark:text-primary-400 dark:border-primary-500/20'
                ])
                ->columnSpanFull();
        } else {
            $schema[] = Forms\Components\Section::make('Design Position')
                ->schema([
                    Forms\Components\Group::make()
                        ->schema([
                            Forms\Components\Hidden::make('design_position_x')->default(0),
                            Forms\Components\Hidden::make('design_position_y')->default(0),
                            Forms\Components\Hidden::make('design_width')->default(200),
                            Forms\Components\Hidden::make('design_height')->default(200),
                            Forms\Components\Hidden::make('back_design_position_x')->default(0),
                            Forms\Components\Hidden::make('back_design_position_y')->default(0),
                            Forms\Components\Hidden::make('back_design_width')->default(200),
                            Forms\Components\Hidden::make('back_design_height')->default(200),
                        ])->columns(4),
                    DesignPositionEditor::make('design_position_editor'),
                ])->columnSpanFull();
        }

        return $form->schema($schema)->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                    Tables\Columns\ImageColumn::make('apparel_image')
                    ->disk('s3')
                    ->size(150),
                Tables\Columns\IconColumn::make('has_back_view')
                    ->alignCenter()
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
     
            ])
            ->defaultSort('mockup_template_configs.created_at', 'desc')  // Thêm default sort với table qualifier

            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMockupTemplateConfigs::route('/'),
            'create' => Pages\CreateMockupTemplateConfig::route('/create'),
            'edit' => Pages\EditMockupTemplateConfig::route('/{record}/edit'),
        ];
    }

    public static function getS3ImageUrl($path)
    {
        if ($path && Storage::disk('s3')->exists($path)) {
            return Storage::disk('s3')->url($path);
        }
        return null;
    }
}
