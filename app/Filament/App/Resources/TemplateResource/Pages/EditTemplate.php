<?php

namespace App\Filament\App\Resources\TemplateResource\Pages;

use App\Filament\App\Resources\TemplateResource;
use App\Livewire\SkusTable;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Filament\Resources\Components\Forms;
use App\Models\Template;

class EditTemplate extends EditRecord
{
    protected static string $resource = TemplateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave(): void
    {
        // Dispatch event sau khi save thành công
        $this->dispatch('attributes-updated');
    }

    protected function beforeSave(): void
    {
        // Dispatch event để AttributesManager có thể lưu data
        $this->dispatch('template-saving');
    }

    public function getViewData(): array 
    {
        return [
            'templateId' => $this->record->id,
        ];
    }
}