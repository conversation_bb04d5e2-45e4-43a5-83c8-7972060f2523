<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\SellerFundRequestResource\Pages;
use App\Filament\App\Resources\SellerFundRequestResource\RelationManagers;
use App\Models\SellerFundRequest;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SellerFundRequestResource extends Resource
{
    protected static ?string $model = SellerFundRequest::class;
    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';
    protected static ?string $navigationGroup = 'Finance';
    protected static ?int $navigationSort = 2;



    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'pending')->count();
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Request Information') ->schema([
                    Forms\Components\Select::make('seller_id')
                        ->label('Seller')
                        ->searchable()
                        ->preload()
                        ->relationship('seller', 'name'),
                        DatePicker::make('created_at')
                            ->label('Request Date')
                            ->default(now()),
                ])->columns(2)     
                ->visible(fn() => auth()->user()->hasRole(['super_admin', 'Accountant' ,'Super Accountant'])),
                Section::make('Request Details')
                    ->schema([
                        ToggleButtons::make('status')
                        ->options(function ($record) {
                            if (auth()->user()->hasRole(['super_admin', 'Accountant','Super Accountant'])) {
                                // Admin và Accountant thấy tất cả options
                                return [
                                    'pending' => 'Pending',
                                    'approved' => 'Approved',
                                    'rejected' => 'Rejected',
                                    'cancelled' => 'Cancelled'
                                ];
                            } else {
                                // Seller chỉ thấy pending và cancelled
                                return [
                                    'pending' => 'Pending',
                                    'cancelled' => 'Cancelled'
                                ];
                            }
                        })
                        ->colors([
                            'pending' => 'warning',
                            'approved' => 'success',
                            'rejected' => 'danger',
                            'cancelled' => 'gray'
                        ])
                        ->icons([
                            'pending' => 'heroicon-o-clock',
                            'approved' => 'heroicon-o-check-circle',
                            'rejected' => 'heroicon-o-x-circle',
                            'cancelled' => 'heroicon-o-archive-box-x-mark'
                        ])
                        ->inline()
                        ->default('pending')
                        ->disabled(fn ($record) => 
                            // Seller chỉ được hủy request của chính họ và đang pending
                            (auth()->user()->hasRole('Seller') && 
                                (!$record || 
                                $record->seller_id !== auth()->id() || 
                                $record->status !== 'pending'
                            )) ||
                            // Admin/Accountant chỉ duyệt được request đang pending
                            (auth()->user()->hasRole(['super_admin', 'Accountant']) && 
                                $record && 
                                $record->status !== 'pending'
                            )
                        )
                        ->columnSpanFull()
                        ->dehydrated(),
                        Forms\Components\Select::make('expense_type_id')
                            ->relationship('expenseType', 'name')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $expenseType = \App\Models\ExpenseType::find($state);
                                    if ($expenseType) {
                                        $set('required_fields', $expenseType->required_fields);
                                        $set('field_descriptions', $expenseType->field_descriptions);
                                    }
                                }
                            }),

                        Forms\Components\TextInput::make('amount')
                            ->required()
                            ->numeric()
                            ->default(0)
                            ->prefix('$')
                            ->minValue(0),

                        Forms\Components\Select::make('currency')
                            ->options([
                                'USD' => 'USD',
                        
                            ])
                            ->default('USD')
                            ->required(),

                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull()
                            ->required()
                            ->rows(3),
                    ])->columns(3),

                Section::make(function ($get) {
                    $expenseType = \App\Models\ExpenseType::find($get('expense_type_id'));
                    return 'Required Information for ' . ($expenseType?->name ?? 'Selected Type');
                })
                    ->description(function ($get) {
                        $expenseType = \App\Models\ExpenseType::find($get('expense_type_id'));
                        return $expenseType?->description ?? '';
                    })
                    ->schema(function ($get) {
                        $expenseType = \App\Models\ExpenseType::find($get('expense_type_id'));
                        if (!$expenseType) return [];

                        return collect($expenseType->required_fields)->map(function ($field) use ($expenseType) {
                            return Forms\Components\TextInput::make("additional_fields.{$field}")
                                ->label(ucwords(str_replace('_', ' ', $field)))
                               
                                ->helperText($expenseType->field_descriptions[$field] ?? '')
                                ->columnSpan(1);
                        })->toArray();
                    })
                    ->columns(2)
                    ->visible(fn($get) => !empty($get('expense_type_id')))
                    ->collapsible(),

                Section::make('Attachments')
                    ->schema([
                        Forms\Components\FileUpload::make('attachments')
                            ->multiple()
                            ->disk('s3')
                            ->directory('fund-requests')
                            ->preserveFilenames()
                            ->maxFiles(5)
                            ->acceptedFileTypes(['application/pdf', 'image/*'])
                            ->maxSize(5120)
                            ->columnSpanFull()
                            ->enableDownload()
                            ->enableOpen()
                            ->deletable(false)
                            ->deleteUploadedFileUsing(null)
                            ->helperText('Files cannot be deleted once uploaded')
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('seller.name')
                    ->searchable()
                    ->sortable()
                    ->description(fn($record) => $record->seller->email),

                TextColumn::make('expenseType.name')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn($state) => match ($state) {
                        'Quảng cáo Facebook' => 'info',
                        'Quảng cáo TikTok' => 'danger',
                        default => 'gray',
                    }),

                TextColumn::make('amount')
                    ->money('USD')
                    ->sortable()
                    ->alignRight(),

                TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('approved_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable()
                    ->placeholder('Not approved yet'),

                IconColumn::make('attachments')
                    ->boolean()
                    ->trueIcon('heroicon-o-paper-clip')
                    ->falseIcon('heroicon-o-x-mark')
                    ->label('Has Attachments'),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ]),

                SelectFilter::make('expense_type_id')
                    ->relationship('expenseType', 'name')
                    ->label('Expense Type'),

                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })->columnSpan(2)->columns(2),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('approve')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->visible(fn($record) => $record->status === 'pending')
                        ->action(fn($record) => $record->update([
                            'status' => 'approved',
                            'approved_by' => auth()->id(),
                            'approved_at' => now(),
                        ])),

                    Tables\Actions\Action::make('reject')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->form([
                            Forms\Components\Textarea::make('rejected_reason')
                                ->required()
                                ->label('Reason for Rejection'),
                        ])
                        ->visible(fn($record) => $record->status === 'pending')
                        ->action(function ($record, array $data) {
                            $record->update([
                                'status' => 'rejected',
                                'rejected_reason' => $data['rejected_reason'],
                            ]);
                        }),

                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                   // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSellerFundRequests::route('/'),
            'create' => Pages\CreateSellerFundRequest::route('/create'),
            'edit' => Pages\EditSellerFundRequest::route('/{record}/edit'),
        ];
    }
}
