<?php

namespace App\Filament\App\Resources\TikTokPaymentResource\Pages;

use App\Filament\App\Resources\TikTokPaymentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Carbon\Carbon;

class ListTikTokPayments extends ListRecords
{
    protected static string $resource = TikTokPaymentResource::class;

    public function mount(): void
    {
        parent::mount();

        // Set default filter for this month if no filter is set
        if (!isset($this->tableFilters['create_time']['create_time'])) {
            $startDate = now()->startOfMonth()->format('d/m/Y');
            $endDate = now()->endOfMonth()->format('d/m/Y');
            $this->tableFilters['create_time'] = ['create_time' => $startDate . ' - ' . $endDate];
        }
    }

    protected function getHeaderActions(): array
    {
        return [
           // Actions\CreateAction::make(),
        ];
    }
}
