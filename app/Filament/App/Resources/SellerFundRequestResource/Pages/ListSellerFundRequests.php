<?php

namespace App\Filament\App\Resources\SellerFundRequestResource\Pages;

use App\Filament\App\Resources\SellerFundRequestResource;
use App\Models\SellerFundRequest;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListSellerFundRequests extends ListRecords
{
    protected static string $resource = SellerFundRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Requests')
                ->icon('heroicon-o-clipboard-document-list')
                ->badge(fn() => SellerFundRequest::count()),

            'pending' => Tab::make('Pending')
                ->icon('heroicon-o-clock')
                ->badge(fn() => SellerFundRequest::where('status', 'Pending')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'Pending')),

            'approved' => Tab::make('Approved')
                ->icon('heroicon-o-check-circle')
                ->badge(fn() => SellerFundRequest::where('status', 'Approved')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'Approved')),

            'rejected' => Tab::make('Rejected')
                ->icon('heroicon-o-x-circle')
                ->badge(fn() => SellerFundRequest::where('status', 'Rejected')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'Rejected')),

            'cancelled' => Tab::make('Cancelled')
                ->icon('heroicon-o-archive-box-x-mark')
                ->badge(fn() => SellerFundRequest::where('status', 'Cancelled')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'Cancelled')),
        ];
    }

    public function getDefaultActiveTab(): string
    {
        return 'all';
    }
}