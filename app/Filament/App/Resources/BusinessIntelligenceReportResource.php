<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\BusinessIntelligenceReportResource\Pages;
use App\Models\BusinessIntelligenceReport;
use App\Services\BusinessIntelligenceService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class BusinessIntelligenceReportResource extends Resource
{
    protected static ?string $model = BusinessIntelligenceReport::class;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';

    protected static ?string $navigationLabel = 'Báo cáo tổng hợp';

    protected static ?string $modelLabel = 'Báo cáo BI';

    protected static ?string $pluralModelLabel = 'Báo cáo Business Intelligence';

    protected static ?string $navigationGroup = 'AI Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin báo cáo')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Tiêu đề')
                            ->required()
                            ->maxLength(255)
                            ->default(fn() => 'Báo cáo BI ' . now()->format('d/m/Y')),

                        Forms\Components\Select::make('period')
                            ->label('Loại báo cáo')
                            ->options([
                                'daily' => 'Hàng ngày',
                                'weekly' => 'Hàng tuần',
                                'monthly' => 'Hàng tháng',
                                'custom' => 'Tùy chỉnh'
                            ])
                            ->default('custom')
                            ->required()
                            ->disabled(fn($record) => $record),
                    ])->columns(2),

                Forms\Components\Section::make('Khoảng thời gian phân tích')
                    ->schema([
                        Forms\Components\DatePicker::make('from_date')
                            ->label('Từ ngày')
                            ->required()
                            ->default(now()->subDays(7))
                            ->maxDate(now())
                            ->disabled(fn($record) => $record),

                        Forms\Components\DatePicker::make('to_date')
                            ->label('Đến ngày')
                            ->required()
                            ->default(now())
                            ->maxDate(now())
                            ->afterOrEqual('from_date')
                            ->disabled(fn($record) => $record),
                    ])->columns(2),

                Forms\Components\Hidden::make('created_by')
                    ->default(fn() => Auth::id()),

                Forms\Components\Section::make('Kết quả phân tích')
                    ->schema([
                        Forms\Components\Textarea::make('ai_summary')
                            ->label('Tóm tắt AI')
                            ->rows(10)
                            ->columnSpanFull()
                            ->disabled(),

                        Forms\Components\TextInput::make('pdf_url')
                            ->label('Link PDF')
                            ->url()
                            ->disabled(),

                        Forms\Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'generating' => 'Đang tạo',
                                'completed' => 'Hoàn thành',
                                'failed' => 'Thất bại'
                            ])
                            ->disabled(),
                    ])->columns(2)
                    ->hidden(fn($record) => !$record),

                Forms\Components\Section::make('Raw Data gửi cho ChatGPT')
                    ->schema([
                        Forms\Components\Textarea::make('raw_prompt')
                            ->label('Text gốc gửi cho OpenAI')
                            ->rows(15)
                            ->columnSpanFull()
                            ->disabled()
                            ->helperText('Đây là text gốc được gửi cho ChatGPT để tạo báo cáo AI Summary'),
                    ])
                    ->collapsible()
                    ->collapsed()
                    ->hidden(fn($record) => !$record || !$record->raw_prompt),

                Forms\Components\Section::make('Raw Embedding Data từ Zilliz')
                    ->schema([
                        Forms\Components\Textarea::make('raw_embedding_data')
                            ->label('Dữ liệu embedding gốc từ Zilliz')
                            ->rows(20)
                            ->columnSpanFull()
                            ->disabled()
                            ->helperText('Đây là dữ liệu gốc được lấy từ Zilliz vector database, bao gồm metadata và vector embeddings'),
                    ])
                    ->collapsible()
                    ->collapsed()
                    ->hidden(fn($record) => !$record || !$record->raw_embedding_data),

                Forms\Components\Section::make('Raw Embedding Prompt')
                    ->schema([
                        Forms\Components\Textarea::make('raw_embedding_prompt')
                            ->label('Text gốc gửi cho ChatGPT tạo embedding')
                            ->rows(15)
                            ->columnSpanFull()
                            ->disabled()
                            ->helperText('Đây là text gốc đã được gửi cho ChatGPT để tạo vector embedding'),
                    ])
                    ->collapsible()
                    ->collapsed()
                    ->hidden(fn($record) => !$record || !$record->raw_embedding_prompt),

                Forms\Components\Section::make('Thống kê')
                    ->schema([
                        Forms\Components\TextInput::make('total_products')
                            ->label('Tổng sản phẩm')
                            ->numeric()
                            ->disabled(),

                        Forms\Components\TextInput::make('total_orders')
                            ->label('Tổng đơn hàng')
                            ->numeric()
                            ->disabled(),

                        Forms\Components\TextInput::make('total_revenue')
                            ->label('Tổng doanh thu')
                            ->numeric()
                            ->prefix('$')
                            ->disabled(),

                        Forms\Components\TextInput::make('avg_order_value')
                            ->label('AOV')
                            ->numeric()
                            ->prefix('$')
                            ->disabled(),
                    ])->columns(4)
                    ->hidden(fn($record) => !$record),

                Forms\Components\Section::make('Lỗi')
                    ->schema([
                        Forms\Components\Textarea::make('error_message')
                            ->label('Thông báo lỗi')
                            ->rows(3)
                            ->disabled(),
                    ])
                    ->hidden(fn($record) => !$record || !$record->error_message),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('Tiêu đề')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('period_text')
                    ->label('Loại')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Hàng ngày' => 'success',
                        'Hàng tuần' => 'info',
                        'Hàng tháng' => 'warning',
                        'Tùy chỉnh' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('date_range')
                    ->label('Khoảng thời gian')
                    ->sortable(['from_date']),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'warning' => 'generating',
                        'success' => 'completed',
                        'danger' => 'failed',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'generating' => 'Đang tạo',
                        'completed' => 'Hoàn thành',
                        'failed' => 'Thất bại',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('total_orders')
                    ->label('Đơn hàng')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_revenue')
                    ->label('Doanh thu')
                    ->money('USD')
                    ->sortable(),

                Tables\Columns\TextColumn::make('avg_order_value')
                    ->label('AOV')
                    ->money('USD')
                    ->sortable(),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Người tạo')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'generating' => 'Đang tạo',
                        'completed' => 'Hoàn thành',
                        'failed' => 'Thất bại',
                    ]),

                Tables\Filters\SelectFilter::make('period')
                    ->label('Loại báo cáo')
                    ->options([
                        'daily' => 'Hàng ngày',
                        'weekly' => 'Hàng tuần',
                        'monthly' => 'Hàng tháng',
                        'custom' => 'Tùy chỉnh'
                    ]),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Từ ngày'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Đến ngày'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
            ])
            ->actions([
                Tables\Actions\Action::make('view_pdf')
                    ->label('Xem PDF')
                    ->icon('heroicon-o-document-text')
                    ->url(fn (BusinessIntelligenceReport $record): string => route('bi-reports.pdf', $record))
                    ->openUrlInNewTab()
                    ->visible(fn (BusinessIntelligenceReport $record): bool => $record->isCompleted()),

                Tables\Actions\Action::make('preview_report')
                    ->label('Xem trên web')
                    ->icon('heroicon-o-eye')
                    ->url(fn (BusinessIntelligenceReport $record): string => route('bi-reports.preview', $record))
                    ->openUrlInNewTab()
                    ->visible(fn (BusinessIntelligenceReport $record): bool => $record->isCompleted()),

                Tables\Actions\Action::make('view_raw_prompt')
                    ->label('Xem Raw Prompt')
                    ->icon('heroicon-o-code-bracket')
                    ->color('info')
                    ->modalHeading('Raw Data gửi cho ChatGPT')
                    ->modalContent(function (BusinessIntelligenceReport $record) {
                        if (!$record->raw_prompt) {
                            return view('filament.components.no-raw-prompt');
                        }
                        return view('filament.components.raw-prompt-modal', [
                            'raw_prompt' => $record->raw_prompt,
                            'title' => $record->title,
                            'created_at' => $record->created_at
                        ]);
                    })
                    ->modalWidth('7xl')
                    ->visible(fn (BusinessIntelligenceReport $record): bool => $record->isCompleted() && !empty($record->raw_prompt)),

                Tables\Actions\Action::make('view_raw_embedding')
                    ->label('Xem Embedding Data')
                    ->icon('heroicon-o-circle-stack')
                    ->color('warning')
                    ->modalHeading('Raw Embedding Data từ Zilliz')
                    ->modalContent(function (BusinessIntelligenceReport $record) {
                        if (!$record->raw_embedding_data) {
                            return view('filament.components.no-raw-embedding');
                        }
                        return view('filament.components.raw-embedding-modal', [
                            'raw_embedding_data' => $record->raw_embedding_data,
                            'title' => $record->title,
                            'created_at' => $record->created_at
                        ]);
                    })
                    ->modalWidth('7xl')
                    ->visible(fn (BusinessIntelligenceReport $record): bool => $record->isCompleted() && !empty($record->raw_embedding_data)),

                Tables\Actions\Action::make('view_embedding_prompt')
                    ->label('Xem Embedding Prompt')
                    ->icon('heroicon-o-document-text')
                    ->color('success')
                    ->modalHeading('Raw Embedding Prompt gửi ChatGPT')
                    ->modalContent(function (BusinessIntelligenceReport $record) {
                        if (!$record->raw_embedding_prompt) {
                            return view('filament.components.no-raw-embedding-prompt');
                        }
                        return view('filament.components.raw-embedding-prompt-modal', [
                            'raw_embedding_prompt' => $record->raw_embedding_prompt,
                            'title' => $record->title,
                            'created_at' => $record->created_at
                        ]);
                    })
                    ->modalWidth('7xl')
                    ->visible(fn (BusinessIntelligenceReport $record): bool => $record->isCompleted() && !empty($record->raw_embedding_prompt)),

                Tables\Actions\ViewAction::make()
                    ->label('Chi tiết'),

                Tables\Actions\EditAction::make()
                    ->label('Sửa')
                    ->visible(fn (BusinessIntelligenceReport $record): bool => !$record->isCompleted()),

                Tables\Actions\DeleteAction::make()
                    ->label('Xóa'),
            ])
            ->headerActions([
                Tables\Actions\Action::make('create_report')
                    ->label('Tạo báo cáo mới')
                    ->icon('heroicon-o-plus')
                    ->color('primary')
                    ->form([
                        Forms\Components\TextInput::make('title')
                            ->label('Tiêu đề')
                            ->required()
                            ->default(fn() => 'Báo cáo BI ' . now()->format('d/m/Y')),

                        Forms\Components\DatePicker::make('from_date')
                            ->label('Từ ngày')
                            ->required()
                            ->default(now()->subDays(7))
                            ->maxDate(now()),

                        Forms\Components\DatePicker::make('to_date')
                            ->label('Đến ngày')
                            ->required()
                            ->default(now())
                            ->maxDate(now())
                            ->afterOrEqual('from_date'),
                    ])
                    ->action(function (array $data) {
                        $biService = app(BusinessIntelligenceService::class);

                        $result = $biService->generateReport(
                            $data['from_date'],
                            $data['to_date'],
                            Auth::id()
                        );

                        if ($result['success']) {
                            Notification::make()
                                ->title('Báo cáo đã được tạo thành công!')
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Lỗi tạo báo cáo')
                                ->body($result['error'])
                                ->danger()
                                ->send();
                        }
                    })
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Xóa đã chọn'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBusinessIntelligenceReports::route('/'),
            'create' => Pages\CreateBusinessIntelligenceReport::route('/create'),
            'view' => Pages\ViewBusinessIntelligenceReport::route('/{record}'),
            'edit' => Pages\EditBusinessIntelligenceReport::route('/{record}/edit'),
        ];
    }

    /**
     * Kiểm tra quyền truy cập - chỉ super_admin và User Manager
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Kiểm tra role super_admin hoặc User Manager
        return $user->hasRole('super_admin') || $user->hasRole('User Manager');
    }

    /**
     * Kiểm tra quyền tạo mới
     */
    public static function canCreate(): bool
    {
        return static::canAccess();
    }

    /**
     * Kiểm tra quyền xem
     */
    public static function canView($record): bool
    {
        return static::canAccess();
    }

    /**
     * Kiểm tra quyền sửa
     */
    public static function canEdit($record): bool
    {
        return static::canAccess() && !$record->isCompleted();
    }

    /**
     * Kiểm tra quyền xóa
     */
    public static function canDelete($record): bool
    {
        return static::canAccess();
    }
}
