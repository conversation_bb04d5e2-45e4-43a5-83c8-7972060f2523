<?php

namespace App\Filament\App\Resources;

use App\Enums\FeatureRequestStatus;
use App\Enums\FeatureRequestPriority;
use App\Enums\FeatureRequestCategory;
use App\Filament\App\Resources\FeatureRequestResource\Pages;
use App\Filament\App\Resources\FeatureRequestResource\RelationManagers;
use App\Models\FeatureRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use FilamentTiptapEditor\TiptapEditor;
use FilamentTiptapEditor\Enums\TiptapOutput;

class FeatureRequestResource extends Resource
{
    protected static ?string $model = FeatureRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-light-bulb';

    protected static ?string $navigationGroup = '<PERSON><PERSON> thống';

    protected static ?string $modelLabel = 'Yêu cầu tính năng';

    protected static ?string $pluralModelLabel = 'Yêu cầu tính năng';

    protected static ?int $navigationSort = 1;

    protected static bool $shouldRegisterNavigation = false;

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'pending')->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin yêu cầu')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Tiêu đề')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Nhập tiêu đề yêu cầu tính năng'),

                        Forms\Components\Select::make('category')
                            ->label('Danh mục')
                            ->options(FeatureRequestCategory::getOptions())
                            ->required()
                            ->default('functionality'),

                        Forms\Components\Select::make('priority')
                            ->label('Mức độ ưu tiên')
                            ->options(FeatureRequestPriority::getOptions())
                            ->required()
                            ->default('medium'),

                        TiptapEditor::make('description')
                            ->label('Mô tả chi tiết')
                            ->required()
                            ->profile('default')
                            ->placeholder('Mô tả chi tiết về tính năng bạn muốn yêu cầu... (Có thể paste ảnh trực tiếp Ctrl+V)')
                            ->output(TiptapOutput::Html)
                            ->disk('public')
                            ->directory('feature-requests')
                            ->acceptedFileTypes(['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/svg+xml', 'image/bmp'])
                            ->maxSize(10240) // 10MB
                            ->extraInputAttributes(['style' => 'min-height: 12rem;'])
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Quản lý (Chỉ dành cho Admin)')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options(FeatureRequestStatus::getOptions())
                            ->default('pending'),

                        TiptapEditor::make('admin_notes')
                            ->label('Ghi chú của Admin')
                            ->profile('default')
                            ->placeholder('Ghi chú từ phía quản trị viên... (Có thể paste ảnh trực tiếp Ctrl+V)')
                            ->output(TiptapOutput::Html)
                            ->disk('public')
                            ->directory('feature-requests/admin-notes')
                            ->acceptedFileTypes(['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/svg+xml', 'image/bmp'])
                            ->maxSize(10240) // 10MB
                            ->extraInputAttributes(['style' => 'min-height: 8rem;'])
                            ->columnSpanFull(),

                        Forms\Components\DateTimePicker::make('completed_at')
                            ->label('Ngày hoàn thành'),
                    ])
                    ->columns(2)
                    ->visible(fn () => Auth::user()?->hasRole(['super_admin', 'admin'])),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('Tiêu đề')
                    ->searchable()
                    ->sortable()
                    ->limit(50)
                    ->tooltip(function (FeatureRequest $record): ?string {
                        return strip_tags($record->description);
                    }),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Người yêu cầu')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('category')
                    ->label('Danh mục')
                    ->badge()
                    ->color(fn (FeatureRequestCategory $state): string => $state->getColor())
                    ->formatStateUsing(fn (FeatureRequestCategory $state): string => $state->getLabel()),

                Tables\Columns\TextColumn::make('priority')
                    ->label('Ưu tiên')
                    ->badge()
                    ->color(fn (FeatureRequestPriority $state): string => $state->getColor())
                    ->formatStateUsing(fn (FeatureRequestPriority $state): string => $state->getLabel()),

                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->color(fn (FeatureRequestStatus $state): string => $state->getColor())
                    ->formatStateUsing(fn (FeatureRequestStatus $state): string => $state->getLabel()),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('completed_at')
                    ->label('Ngày hoàn thành')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options(FeatureRequestStatus::getOptions()),

                Tables\Filters\SelectFilter::make('priority')
                    ->label('Mức độ ưu tiên')
                    ->options(FeatureRequestPriority::getOptions()),

                Tables\Filters\SelectFilter::make('category')
                    ->label('Danh mục')
                    ->options(FeatureRequestCategory::getOptions()),

                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Người yêu cầu')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Xem'),
                Tables\Actions\EditAction::make()
                    ->label('Sửa'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Xóa'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFeatureRequests::route('/'),
            'create' => Pages\CreateFeatureRequest::route('/create'),
            'view' => Pages\ViewFeatureRequest::route('/{record}'),
            'edit' => Pages\EditFeatureRequest::route('/{record}/edit'),
        ];
    }

    public static function canAccess(): bool
    {
        // Tất cả user đã đăng nhập đều có thể truy cập
        return auth()->check();
    }

    public static function canCreate(): bool
    {
        // Tất cả user đều có thể tạo yêu cầu tính năng
        return auth()->check();
    }

    public static function canEdit($record): bool
    {
        // Admin có thể sửa tất cả, user chỉ sửa yêu cầu của mình (và chỉ khi chưa hoàn thành)
        if (auth()->user()->hasAnyRole(['super_admin', 'admin', 'User Manager'])) {
            return true;
        }

        return $record->user_id === auth()->id() &&
               $record->status->value !== 'completed';
    }

    public static function canView($record): bool
    {
        // Admin có thể xem tất cả, user chỉ xem yêu cầu của mình
        return auth()->user()->hasAnyRole(['super_admin', 'admin', 'User Manager']) ||
               $record->user_id === auth()->id();
    }

    public static function canDelete($record): bool
    {
        // Chỉ admin mới có thể xóa yêu cầu tính năng
        return auth()->user()->hasAnyRole(['super_admin', 'admin', 'User Manager']);
    }
}
