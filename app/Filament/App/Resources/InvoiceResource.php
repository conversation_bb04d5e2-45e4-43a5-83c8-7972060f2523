<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\InvoiceResource\Pages;
use App\Models\Invoice;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Carbon;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\Collection;
use App\Forms\Components\FinanceBreakdown;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Tables\Actions\ViewAction;
use Illuminate\Contracts\View\View;
use App\Models\SellerFinance;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Notification;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Finance';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make()
                    ->columns(12)
                    ->schema([
                        // Thông tin cơ bản - 8 cột
                        Forms\Components\Section::make('Chi tiết hoá đơn')
                            ->columnSpan(8)
                            ->schema([
                                // Phần thanh toán báo cáo tài chính
                                Repeater::make('financeItems')
                                    ->label('Thanh toán báo cáo tài chính')
                                    ->relationship('financeItems')
                                    ->schema([
                                        Select::make('seller_finance_id')
                                            ->label('Báo cáo tài chính')
                                            ->default(fn () => request('finance_id'))
                                            ->options(function (Get $get) {
                                                $userId = $get('../../user_id') ?? request('seller_id');
                                                if (!$userId) return [];
                                                
                                                $currentItems = collect($get('../../financeItems') ?? []);
                                                
                                                return SellerFinance::query()
                                                    ->where('seller_id', $userId)
                                                    ->get()
                                                    ->mapWithKeys(function ($finance) use ($currentItems) {
                                                        $remainingAmount = $finance->getRemainingAmount();
                                                        $currentAmount = $currentItems
                                                            ->where('seller_finance_id', $finance->id)
                                                            ->sum('amount');
                                                        
                                                        $actualRemaining = $remainingAmount - $currentAmount;
                                                        
                                                        return [
                                                            $finance->id => "Tháng {$finance->month->format('m/Y')} - Còn lại: $" . number_format($actualRemaining, 2)
                                                        ];
                                                    });
                                            })
                                            ->searchable()
                                            ->preload()
                                            ->live()
                                            ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                if (!$state) {
                                                    $set('..', [
                                                        'seller_finance_id' => null,
                                                        'amount' => null,
                                                        'type' => 'finance',
                                                        'description' => null,
                                                    ]);
                                                    return;
                                                }
                                                
                                                $finance = SellerFinance::find($state);
                                                if (!$finance) return;
                                                
                                                $remainingAmount = $finance->getRemainingAmount();
                                                $currentItems = collect($get('../../../financeItems') ?? [])
                                                    ->where('seller_finance_id', $state)
                                                    ->where('amount', '!=', $get('../amount'))
                                                    ->sum('amount');
                                                
                                                $actualRemaining = $remainingAmount - $currentItems;
                                                
                                                $set('..', [
                                                    'seller_finance_id' => $state,
                                                    'amount' => $actualRemaining,
                                                    'type' => 'finance',
                                                    'description' => "Thanh toán báo cáo tài chính tháng " . $finance->month->format('m/Y'),
                                                ]);
                                            })
                                            ->required(),

                                        TextInput::make('amount')
                                            ->label('Số tiền')
                                            ->required()
                                            ->numeric()
                                            ->prefix('$')
                                            ->live()
                                            ->afterStateUpdated(function (Get $get, Set $set) {
                                                $set('../../', $get('../../'));
                                            })
                                            ->disabled(fn (Get $get) => !$get('seller_finance_id')),

                                        Forms\Components\Hidden::make('type')
                                            ->default('finance'),

                                        Forms\Components\Hidden::make('description'),
                                    ])
                                    ->columns(2)
                                    ->defaultItems(0)
                                    ->live()
                                    ->addActionLabel('Thêm thanh toán báo cáo tài chính'),

                                // Các khoản mục khác
                                Repeater::make('regularItems')
                                    ->label('Các khoản mục khác')
                                    ->relationship('regularItems')
                                    ->schema([
                                        TextInput::make('description')
                                            ->label('Mô tả')
                                            ->required(),
                                        TextInput::make('amount')
                                            ->label('Số tiền')
                                            ->numeric()
                                            ->required()
                                            ->prefix('$'),
                                        Forms\Components\Hidden::make('type')
                                            ->default('regular'),
                                    ])
                                    ->columns(2)
                                    ->defaultItems(0)
                                    ->addActionLabel('Thêm khoản mục khác'),
                            ]),

                        // Thông tin bên phải - 4 cột
                        Forms\Components\Section::make('Thông tin')
                            ->columnSpan(4)
                            ->schema([
                                Select::make('user_id')
                                    ->label('Seller')
                                    ->default(fn () => request('seller_id'))
                                    ->relationship('user', 'name')
                                    ->searchable()
                                    ->live()
                                    ->preload()
                                    ->required(),
                                    
                                TextInput::make('invoice_number')
                                    ->label('Số hoá đơn')
                                    ->default(fn () => 'INV-' . now()->format('YmdHis'))
                                    ->disabled()
                                    ->dehydrated(),
                                    
                                DatePicker::make('issue_date')
                                    ->label('Ngày phát hành')
                                    ->default(now())
                                    ->required(),
                                
                                DatePicker::make('billing_month')
                                    ->label('Tháng tính doanh số')
                                    ->default(fn () => now()->subMonth()->startOfMonth())
                                    ->format('Y-m')
                                    ->displayFormat('m/Y')
                                    ->required()
                                    ->native(false)
                                    ->closeOnDateSelection(),
                                    
                                DatePicker::make('due_date')
                                    ->label('Ngày đến hạn')
                                    ->default(now()->addDays(7))
                                    ->required(),

                                Forms\Components\Select::make('payment_method')
                                    ->label('Phương thức thanh toán')
                                    ->options([
                                        'bank_transfer' => 'Chuyển khoản',
                                        'cash' => 'Tiền mặt',
                                    ])
                                    ->visible(fn (Get $get) => in_array($get('status'), ['approved', 'paid'])),

                                Forms\Components\TextInput::make('reference_number')
                                    ->label('Số tham chiếu')
                                    ->visible(fn (Get $get) => $get('status') === 'paid'),

                                Forms\Components\FileUpload::make('evidence_url')
                                    ->label('Chứng từ thanh toán')
                                    ->image()
                                    ->directory('payment-evidence')
                                    ->visible(fn (Get $get) => $get('status') === 'paid'),
                                    
                                Forms\Components\ToggleButtons::make('status')
                                    ->label('Trạng thái')
                                    ->options([
                                        'pending' => 'Chờ xử lý',
                                        'approved' => 'Đã duyệt',
                                        'confirmed' => 'Đã xác nhận',
                                        'paid' => 'Đã thanh toán',
                                        'cancelled' => 'Đã huỷ',
                                    ])
                                    ->colors([
                                        'pending' => 'warning',
                                        'approved' => 'info',
                                        'confirmed' => 'success',
                                        'paid' => 'primary',
                                        'cancelled' => 'danger',
                                    ])
                                    ->icons([
                                        'pending' => 'heroicon-m-clock',
                                        'approved' => 'heroicon-m-check',
                                        'confirmed' => 'heroicon-m-check-circle',
                                        'paid' => 'heroicon-m-banknotes',
                                        'cancelled' => 'heroicon-m-x-circle',
                                    ])
                                    ->inline()
                                    ->required(),
                                    
                                Forms\Components\Textarea::make('notes')
                                    ->label('Ghi chú')
                                    ->nullable()
                                    ->columnSpanFull(),

                                Forms\Components\Textarea::make('payment_notes')
                                    ->label('Ghi chú thanh toán')
                                    ->visible(fn (Get $get) => in_array($get('status'), ['approved', 'paid']))
                                    ->columnSpanFull(),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['items', 'approver', 'creator']))
            ->columns([
                Tables\Columns\TextColumn::make('invoice_number')
                    ->label('Số hoá đơn')
                    ->sortable()
                    ->searchable(),
       
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Seller')
                    ->sortable(),
                Tables\Columns\TextColumn::make('issue_date')
                    ->label('Ngày phát hành')
                    ->date()
                    ->sortable(),
      
                Tables\Columns\TextColumn::make('billing_month')
                    ->label('Tháng tính doanh số')
                    ->date('m/Y')
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Tổng tiền')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->money('USD')
                            ->query(function ($query) {
                                return $query
                                    ->where('status', '!=', 'cancelled');
                            }),
                    ])
                    ->alignRight(),

                Tables\Columns\TextColumn::make('paid_amount')
                    ->label('Đã thanh toán')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->money('USD')
                            ->query(function ($query) {
                                return $query
                                    ->where('status', '!=', 'cancelled');
                            }),
                    ])
                    ->alignRight(),

                Tables\Columns\TextColumn::make('payment_status')
                    ->label('Trạng thái thanh toán')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'paid' => 'success',
                        'partial' => 'warning',
                        'unpaid' => 'danger',
                        'cancelled' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'paid' => 'Đã thanh toán',
                        'partial' => 'Thanh toán một phần',
                        'unpaid' => 'Chưa thanh toán',
                        'cancelled' => 'Đã hủy',
                        default => $state,
                    }),
      
                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'info',
                        'confirmed' => 'success',
                        'paid' => 'primary',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
        
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Người tạo')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('approver.name')
                    ->label('Người duyệt')
                    ->sortable()
                    ->toggleable()
                    ->placeholder('Chưa duyệt')
                    ->description(fn ($record) => $record->approved_at ? 
                        'Duyệt lúc: ' . $record->approved_at->format('d/m/Y H:i') : ''),
        
                Tables\Columns\TextColumn::make('payment_date')
                    ->label('Ngày thanh toán')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable()
                    ->placeholder('Chưa thanh toán'),
            ])
            ->filters([
                SelectFilter::make('user_id')
                    ->label('Seller')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->visible(fn () => Invoice::canManageInvoices()),

                Filter::make('billing_month_filter')
                    ->form([
                        Forms\Components\DatePicker::make('billing_month')
                            ->label('Tháng tính doanh số')
                            ->displayFormat('m/Y')
                            ->format('Y-m')
                            ->closeOnDateSelection(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['billing_month'],
                            fn (Builder $query, $date): Builder => $query
                                ->whereMonth('billing_month', Carbon::parse($date)->month)
                                ->whereYear('billing_month', Carbon::parse($date)->year)
                        );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        
                        if ($data['billing_month'] ?? null) {
                            $indicators['billing_month'] = 'Tháng tính doanh số: ' . Carbon::parse($data['billing_month'])->format('m/Y');
                        }
                        
                        return $indicators;
                    }),

                Filter::make('amount_range')
                    ->form([
                        Forms\Components\TextInput::make('amount_from')
                            ->label('Từ số tiền')
                            ->numeric()
                            ->prefix('$'),
                        Forms\Components\TextInput::make('amount_to')
                            ->label('Đến số tiền')
                            ->numeric()
                            ->prefix('$'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['amount_from'],
                                fn (Builder $query, $amount): Builder => $query->where('total_amount', '>=', $amount),
                            )
                            ->when(
                                $data['amount_to'],
                                fn (Builder $query, $amount): Builder => $query->where('total_amount', '<=', $amount),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        
                        if ($data['amount_from'] ?? null) {
                            $indicators['amount_from'] = 'Từ $' . number_format($data['amount_from'], 2);
                        }
                        
                        if ($data['amount_to'] ?? null) {
                            $indicators['amount_to'] = 'Đến $' . number_format($data['amount_to'], 2);
                        }
                        
                        return $indicators;
                    })->columns(2),

                SelectFilter::make('approved_by')
                    ->label('Người duyệt')
                    ->relationship('approver', 'name')
                    ->searchable()
                    ->preload()
                    ->visible(fn () => Invoice::canManageInvoices()),

                SelectFilter::make('created_by')
                    ->label('Người tạo')
                    ->relationship('creator', 'name')
                    ->searchable()
                    ->preload()
                    ->visible(fn () => Invoice::canManageInvoices()),

                DateRangeFilter::make('created_at')
                    ->label('Ngày phát hành')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->alwaysShowCalendar()
                    ->displayFormat('D MMM YYYY')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ])
                    ->modifyQueryUsing(function (Builder $query, ?Carbon $startDate, ?Carbon $endDate) {
                        if ($startDate && $endDate) {
                            return $query->whereDate('issue_date', '>=', $startDate)
                                        ->whereDate('issue_date', '<=', $endDate);
                        }
                        return $query;
                    })
                    ->visible(fn () => Invoice::canManageInvoices()),

                DateRangeFilter::make('approval_date')
                    ->label('Ngày duyệt')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->alwaysShowCalendar()
                    ->displayFormat('D MMM YYYY')
                    ->modifyQueryUsing(function (Builder $query, ?Carbon $startDate, ?Carbon $endDate) {
                        if ($startDate && $endDate) {
                            return $query->whereDate('approved_at', '>=', $startDate)
                                        ->whereDate('approved_at', '<=', $endDate);
                        }
                        return $query;
                    })
                    ->visible(fn () => Invoice::canManageInvoices()),
            ])
            ->filtersFormColumns(3)
            ->actions([
                Action::make('approve')
                    ->label('Duyệt')
                    ->icon('heroicon-m-check')
                    ->button()
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Duyệt hóa đơn')
                    ->modalDescription('Bạn có chắc chắn muốn duyệt hóa đơn này?')
                    ->action(function (Invoice $record) {
                        $record->update([
                            'status' => 'approved',
                            'approved_by' => auth()->id(),
                            'approved_at' => now()
                        ]);
                    })
                    ->visible(fn () => Invoice::canManageInvoices()),
                Action::make('confirm')
                    ->label('Xác nhận')
                    ->icon('heroicon-m-check-circle')
                    ->button()
                    ->color('info')
                    ->requiresConfirmation()
                    ->action(fn (Invoice $record) => $record->update(['status' => 'confirmed']))
                    ->visible(fn (Invoice $record) => $record->canConfirm()),
                Action::make('pay')
                    ->label('Thanh toán')
                    ->icon('heroicon-m-banknotes')
                    ->button()
                    ->color('primary')
                    ->form([
                        Forms\Components\Select::make('payment_method')
                            ->label('Phương thức thanh toán')
                            ->options([
                                'bank_transfer' => 'Chuyển khoản',
                                'cash' => 'Tiền mặt',
                            ])
                            ->required(),
                        Forms\Components\TextInput::make('reference_number')
                            ->label('Số tham chiếu'),
                        Forms\Components\FileUpload::make('evidence_url')
                            ->label('Chứng từ thanh toán')
                            ->image()
                            ->directory('payment-evidence'),
                        Forms\Components\Textarea::make('payment_notes')
                            ->label('Ghi chú thanh toán'),
                    ])
                    ->action(function (Invoice $record, array $data) {
                        $record->update([
                            'status' => 'paid',
                            'payment_method' => $data['payment_method'],
                            'reference_number' => $data['reference_number'],
                            'evidence_url' => $data['evidence_url'],
                            'payment_notes' => $data['payment_notes'],
                            'payment_date' => now(),
                            'paid_amount' => $record->total_amount
                        ]);
                    })
                    ->visible(fn (Invoice $record) => $record->canPay()),
                ViewAction::make()
                    ->label('Xem hoá đơn')
                    ->icon('heroicon-m-eye')
                    ->modalWidth('4xl')
                    ->modalFooterActions(fn (Invoice $record) => [
                        Action::make('print')
                            ->label('In hoá đơn')
                            ->icon('heroicon-m-printer')
                            ->color('primary')
                            ->url(fn () => route('invoice.print', $record))
                            ->openUrlInNewTab(),
                    ]),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    BulkAction::make('approve')
                        ->label('Duyệt hóa đơn')
                        ->icon('heroicon-m-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('Duyệt hóa đơn')
                        ->modalDescription('Bạn có chắc chắn muốn duyệt các hóa đơn đã chọn?')
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                if ($record->status === 'pending') {
                                    $record->update([
                                        'status' => 'approved',
                                        'approved_by' => auth()->id(),
                                        'approved_at' => now(),
                                    ]);
                                }
                            });
                        })
                        ->visible(fn () => Invoice::canManageInvoices()),

                    BulkAction::make('confirm')
                        ->label('Xác nhận hóa đơn')
                        ->icon('heroicon-m-check-circle')
                        ->color('info')
                        ->requiresConfirmation()
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                if ($record->status === 'approved' && $record->user_id === auth()->id()) {
                                    $record->update(['status' => 'confirmed']);
                                }
                            });
                        })
                        ->visible(fn () => auth()->user()->hasRole('seller')),

                    BulkAction::make('pay')
                        ->label('Thanh toán hóa đơn')
                        ->icon('heroicon-m-banknotes')
                        ->color('primary')
                        ->form([
                            Forms\Components\Select::make('payment_method')
                                ->label('Phương thức thanh toán')
                                ->options([
                                    'bank_transfer' => 'Chuyển khoản',
                                    'cash' => 'Tiền mặt',
                                ])
                                ->required(),
                            Forms\Components\TextInput::make('reference_number')
                                ->label('Số tham chiếu'),
                            Forms\Components\FileUpload::make('evidence_url')
                                ->label('Chứng từ thanh toán')
                                ->image()
                                ->directory('payment-evidence'),
                            Forms\Components\Textarea::make('payment_notes')
                                ->label('Ghi chú thanh toán'),
                        ])
                        ->action(function (Collection $records, array $data) {
                            $records->each(function ($record) use ($data) {
                                if ($record->status === 'confirmed') {
                                    $record->update([
                                        'status' => 'paid',
                                        'payment_method' => $data['payment_method'],
                                        'reference_number' => $data['reference_number'],
                                        'evidence_url' => $data['evidence_url'],
                                        'payment_notes' => $data['payment_notes'],
                                        'payment_date' => now(),
                                        'paid_amount' => $record->total_amount
                                    ]);
                                }
                            });
                        })
                        ->visible(fn () => Invoice::canManageInvoices()),

                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                Action::make('recalculateTotal')
                    ->label('Tính lại tổng tiền')
                    ->icon('heroicon-m-calculator')
                    ->requiresConfirmation()
                    ->action(function () {
                        $updated = 0;
                        Invoice::chunk(100, function ($invoices) use (&$updated) {
                            foreach ($invoices as $invoice) {
                                $total = $invoice->items()->sum('amount');
                                if ($total != $invoice->total_amount) {
                                    $invoice->forceFill(['total_amount' => $total])->saveQuietly();
                                    $updated++;
                                }
                            }
                        });
                        
                        Notification::make()
                            ->title('Đã tính lại tổng tiền')
                            ->body("Đã cập nhật {$updated} hóa đơn")
                            ->success()
                            ->send();
                    }),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'edit' => Pages\EditInvoice::route('/{record}/edit'),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Grid::make()
                    ->columns(12)
                    ->schema([
                        Section::make('Chi tiết hoá đơn')
                            ->columnSpan(8)
                            ->schema([
                                // Hiển thị finance items
                                RepeatableEntry::make('financeItems')
                                    ->label('Thanh toán báo cáo tài chính')
                                    ->schema([
                                        TextEntry::make('description')
                                            ->label('Mô tả'),
                                        TextEntry::make('amount')
                                            ->label('Số tiền')
                                            ->money('USD'),
                                    ])
                                    ->columns(2),

                                // Hiển thị regular items
                                RepeatableEntry::make('regularItems')
                                    ->label('Các khoản mục khác')
                                    ->schema([
                                        TextEntry::make('description')
                                            ->label('Mô tả'),
                                        TextEntry::make('amount')
                                            ->label('Số tiền')
                                            ->money('USD'),
                                    ])
                                    ->columns(2),

                                TextEntry::make('total_amount')
                                    ->label('Tổng tiền')
                                    ->money('USD')
                                    ->color('success')
                                    ->size('lg'),
                            ]),

                        Section::make('Thông tin')
                            ->columnSpan(4)
                            ->schema([
                                TextEntry::make('invoice_number')
                                    ->label('Số hoá đơn'),

                                TextEntry::make('user.name')
                                    ->label('Seller'),

                                TextEntry::make('issue_date')
                                    ->label('Ngày phát hành')
                                    ->date(),

                                TextEntry::make('billing_month')
                                    ->label('Tháng tính doanh số')
                                    ->date('m/Y'),

                                TextEntry::make('due_date') 
                                    ->label('Ngày đến hạn')
                                    ->date(),

                                TextEntry::make('status')
                                    ->label('Trạng thái')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'pending' => 'warning',
                                        'approved' => 'info', 
                                        'confirmed' => 'success',
                                        'paid' => 'primary',
                                        'cancelled' => 'danger',
                                        default => 'gray',
                                    })
                                    ->formatStateUsing(fn (string $state): string => match ($state) {
                                        'pending' => 'Chờ xử lý',
                                        'approved' => 'Đã duyệt',
                                        'confirmed' => 'Đã xác nhận', 
                                        'paid' => 'Đã thanh toán',
                                        'cancelled' => 'Đã huỷ',
                                        default => $state,
                                    }),

                                TextEntry::make('notes')
                                    ->label('Ghi chú')
                                    ->columnSpanFull(),
                            ]),

                        // Hiển thị chi tiết từng báo cáo tài chính
                        RepeatableEntry::make('financeItems')
                            ->columnSpan(12)
                            ->label('Chi tiết báo cáo tài chính')
                            ->schema([
                                Grid::make()
                                    ->columns(4)
                                    ->schema([
                                        TextEntry::make('sellerFinance.base_salary')
                                            ->label('Lương cơ bản')
                                            ->money('USD'),
                                        TextEntry::make('sellerFinance.total_bonus')
                                            ->label('Tổng thưởng')
                                            ->money('USD'),
                                        TextEntry::make('sellerFinance.total_income')
                                            ->label('Tổng thu nhập')
                                            ->money('USD'),
                                        TextEntry::make('sellerFinance.month')
                                            ->label('Tháng báo cáo')
                                            ->date('m/Y'),
                                        TextEntry::make('amount')
                                            ->label('Thanh toán')
                                            ->money('USD'),
                                    ]),
                            ]),
                    ]),
            ]);
    }


}
