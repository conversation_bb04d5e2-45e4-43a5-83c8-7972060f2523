<?php

namespace App\Filament\App\Resources\BusinessIntelligenceReportResource\Pages;

use App\Filament\App\Resources\BusinessIntelligenceReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewBusinessIntelligenceReport extends ViewRecord
{
    protected static string $resource = BusinessIntelligenceReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview_report')
                ->label('Xem trên web')
                ->icon('heroicon-o-eye')
                ->url(fn (): string => route('bi-reports.preview', $this->record))
                ->openUrlInNewTab()
                ->visible(fn (): bool => $this->record->isCompleted()),

            Actions\Action::make('view_pdf')
                ->label('Xem PDF')
                ->icon('heroicon-o-document-text')
                ->url(fn (): string => route('bi-reports.pdf', $this->record))
                ->openUrlInNewTab()
                ->visible(fn (): bool => $this->record->isCompleted()),

            Actions\Action::make('download_pdf')
                ->label('Tải PDF')
                ->icon('heroicon-o-arrow-down-tray')
                ->url(fn (): string => route('bi-reports.download', $this->record))
                ->visible(fn (): bool => $this->record->isCompleted()),

            Actions\EditAction::make()
                ->visible(fn (): bool => !$this->record->isCompleted()),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Thông tin báo cáo')
                    ->schema([
                        Infolists\Components\TextEntry::make('title')
                            ->label('Tiêu đề'),
                        
                        Infolists\Components\TextEntry::make('period_text')
                            ->label('Loại báo cáo')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'Hàng ngày' => 'success',
                                'Hàng tuần' => 'info',
                                'Hàng tháng' => 'warning',
                                'Tùy chỉnh' => 'gray',
                                default => 'gray',
                            }),
                        
                        Infolists\Components\TextEntry::make('date_range')
                            ->label('Khoảng thời gian'),
                        
                        Infolists\Components\TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'generating' => 'warning',
                                'completed' => 'success',
                                'failed' => 'danger',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn (string $state): string => match ($state) {
                                'generating' => 'Đang tạo',
                                'completed' => 'Hoàn thành',
                                'failed' => 'Thất bại',
                                default => $state,
                            }),
                        
                        Infolists\Components\TextEntry::make('creator.name')
                            ->label('Người tạo'),
                        
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Ngày tạo')
                            ->dateTime('d/m/Y H:i'),
                    ])->columns(3),

                Infolists\Components\Section::make('Thống kê tổng quan')
                    ->schema([
                        Infolists\Components\TextEntry::make('total_products')
                            ->label('Tổng sản phẩm')
                            ->numeric(),
                        
                        Infolists\Components\TextEntry::make('total_orders')
                            ->label('Tổng đơn hàng')
                            ->numeric(),
                        
                        Infolists\Components\TextEntry::make('total_suppliers')
                            ->label('Supplier orders')
                            ->numeric(),
                        
                        Infolists\Components\TextEntry::make('total_stores')
                            ->label('Tổng stores')
                            ->numeric(),
                        
                        Infolists\Components\TextEntry::make('total_reports')
                            ->label('Daily reports')
                            ->numeric(),
                        
                        Infolists\Components\TextEntry::make('total_revenue')
                            ->label('Tổng doanh thu')
                            ->money('USD'),
                        
                        Infolists\Components\TextEntry::make('avg_order_value')
                            ->label('AOV')
                            ->money('USD'),
                    ])->columns(4)
                    ->visible(fn (): bool => $this->record->isCompleted()),

                Infolists\Components\Section::make('Phân tích AI')
                    ->schema([
                        Infolists\Components\TextEntry::make('ai_summary')
                            ->label('Tóm tắt AI')
                            ->markdown()
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (): bool => $this->record->isCompleted() && $this->record->ai_summary),

                Infolists\Components\Section::make('File báo cáo')
                    ->schema([
                        Infolists\Components\TextEntry::make('pdf_filename')
                            ->label('Tên file'),
                        
                        Infolists\Components\TextEntry::make('pdf_url')
                            ->label('Link PDF')
                            ->url(fn (): string => $this->record->pdf_url)
                            ->openUrlInNewTab(),
                    ])->columns(2)
                    ->visible(fn (): bool => $this->record->isCompleted()),

                Infolists\Components\Section::make('Lỗi')
                    ->schema([
                        Infolists\Components\TextEntry::make('error_message')
                            ->label('Thông báo lỗi')
                            ->color('danger'),
                    ])
                    ->visible(fn (): bool => $this->record->isFailed() && $this->record->error_message),
            ]);
    }
}
