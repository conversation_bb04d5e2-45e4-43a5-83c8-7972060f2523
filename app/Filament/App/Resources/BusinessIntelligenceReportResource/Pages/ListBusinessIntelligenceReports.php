<?php

namespace App\Filament\App\Resources\BusinessIntelligenceReportResource\Pages;

use App\Filament\App\Resources\BusinessIntelligenceReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBusinessIntelligenceReports extends ListRecords
{
    protected static string $resource = BusinessIntelligenceReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
