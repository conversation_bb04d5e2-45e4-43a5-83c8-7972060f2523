<?php

namespace App\Filament\App\Resources\BusinessIntelligenceReportResource\Pages;

use App\Filament\App\Resources\BusinessIntelligenceReportResource;
use App\Services\BusinessIntelligenceService;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class CreateBusinessIntelligenceReport extends CreateRecord
{
    protected static string $resource = BusinessIntelligenceReportResource::class;

    protected static ?string $title = 'Tạo báo cáo Business Intelligence';

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Tự động tạo báo cáo khi submit form
        $biService = app(BusinessIntelligenceService::class);

        $result = $biService->generateReport(
            $data['from_date'],
            $data['to_date'],
            Auth::id()
        );

        if ($result['success']) {
            Notification::make()
                ->title('Báo cáo đang được tạo!')
                ->body('Báo cáo sẽ sẵn sàng trong vài phút.')
                ->success()
                ->send();

            // Redirect về trang index thay vì tạo record mới
            $this->redirect($this->getRedirectUrl());
            return [];
        } else {
            Notification::make()
                ->title('Lỗi tạo báo cáo')
                ->body($result['error'])
                ->danger()
                ->send();

            // Ngăn việc tạo record
            $this->halt();
            return [];
        }
    }
}
