<?php

namespace App\Filament\App\Resources\TikTokProductResource\Pages;

use App\Filament\App\Resources\TikTokProductResource;
use App\Models\TikTokProduct;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListTikTokProducts extends ListRecords
{
    protected static string $resource = TikTokProductResource::class;

    protected static ?string $title = '';

    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('All Shop')
        ];
    
        $statusTabs = collect(['Shirt','Hoodie','Sweatshirt','Unknown'])->mapWithKeys(function ($status) {

    
            return [
                strtolower($status) => Tab::make($status)
                    ->modifyQueryUsing(fn (Builder $query) => $query->where('product_type', $status))
                    ->badge(TikTokProduct::where('product_type', $status)->count()),
            ];
        })->toArray();
    
        return $tabs + $statusTabs;
    }
}
