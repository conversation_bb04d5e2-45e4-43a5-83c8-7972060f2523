<?php

namespace App\Filament\App\Resources\TikTokProductResource\Pages;

use App\Filament\App\Resources\TikTokProductResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTikTokProduct extends EditRecord
{
    protected static string $resource = TikTokProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
