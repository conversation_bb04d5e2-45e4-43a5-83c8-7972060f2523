<?php

namespace App\Filament\App\Resources;

use App\Exports\SellerFinanceExport;
use App\Filament\App\Resources\SellerFinanceResource\Pages;
use App\Models\SellerFinance;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Enums\ActionsPosition;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Carbon;
use Illuminate\Support\HtmlString;
use Maatwebsite\Excel\Facades\Excel;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use App\Traits\HasUserFilter;
use Filament\Tables\Actions\ActionGroup;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Tables\Actions\ExportAction;
use App\Models\PayoutTransaction;
use App\Services\SellerService;


class SellerFinanceResource extends Resource
{
    use HasUserFilter;
    protected static ?string $model = SellerFinance::class;
    protected static ?string $navigationIcon = 'heroicon-o-calculator';
    protected static ?string $navigationGroup = 'Finance';
    protected static ?string $navigationLabel = 'Finances';
    protected static ?int $navigationSort = 1;

    // protected static $record = SellerFinance::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('seller_id')
                    ->label('Seller')
                    ->options(User::role('seller')->pluck('name', 'id'))
                    ->required()
                    ->searchable()
                    ->disabled(),

                Forms\Components\DatePicker::make('month')
                    ->label('Tháng')
                    ->required()
                    ->format('m/Y')
                    ->displayFormat('m/Y')
                    ->closeOnDateSelection()
                    ->disabled(),

                Forms\Components\Section::make('Doanh Thu')
                    ->schema([
                        Forms\Components\TextInput::make('gross_revenue')
                            ->disabled()
                            ->label('Doanh Thu Gộp')
                            ->required()
                            ->numeric()
                            ->prefix('$'),

                        Forms\Components\TextInput::make('platform_fees')
                            ->disabled()
                            ->label('Phí Nền Tảng')
                            ->required()
                            ->numeric()
                            ->prefix('$'),

                        Forms\Components\TextInput::make('net_revenue')
                            ->label('Doanh Thu Ròng')
                            ->required()
                            ->numeric()
                            ->prefix('$')
                            ->disabled()
                            ->dehydrated(),
                    ])->columns(3),

                Forms\Components\Section::make('Chi Phí')
                    ->schema([
                        KeyValue::make('costs')
                            ->disabled()
                            ->label('Chi Phí')
                            ->keyLabel('Loại Chi Phí')
                            ->valueLabel('Số Tiền')

                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Thu Nhập')
                    ->schema([
                        Forms\Components\TextInput::make('base_salary')
                            ->label('Lương Cơ Bản')
                            ->disabled()
                            ->required()
                            ->numeric()
                            ->prefix('$'),

                        Forms\Components\TextInput::make('total_bonus')
                            ->label('Tổng Hoa Hồng')
                            ->numeric()
                            ->prefix('$')
                            ->disabled()
                            ->dehydrated(),

                        Forms\Components\TextInput::make('total_salary')
                            ->label('Tổng Thu Nhập')
                            ->numeric()
                            ->prefix('$')
                            ->disabled()
                            ->dehydrated(),

                    ])->columns(3),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('seller.name')
                    ->label('Seller')
                    ->searchable()
                    ->sortable()
                    ->description(fn (SellerFinance $record): string => $record->seller->email ?? ''),
                Tables\Columns\TextColumn::make('month')
                    ->label('Tháng')
                    ->date('m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('gross_revenue')
                    ->label('Doanh thu')
                    ->money('USD')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_cost')
                    ->label('Chi phí')
                    ->money('USD')
                    ->sortable()
                    ->description(function ($record) {
                        return \App\Services\SellerService::formatCostDetails($record->costs);
                    }),
                Tables\Columns\TextColumn::make('previous_loss')
                    ->label('Lỗ tháng trước')
                    ->money('USD')
                    ->sortable()
                    ->color(fn ($state): string => $state > 0 ? 'danger' : 'gray')
                    ->formatStateUsing(fn ($state): string => $state > 0 ? '$' . number_format($state, 2) : '-'),
                Tables\Columns\TextColumn::make('gross_profit')
                    ->label('Lợi nhuận gộp')
                    ->money('USD')
                    ->sortable()
                    ->color(fn ($state): string => $state >= 0 ? 'success' : 'danger'),
                Tables\Columns\TextColumn::make('net_profit')
                    ->label('Lợi nhuận rồng')
                    ->money('USD')
                    ->sortable()
                    ->color(fn ($state): string => $state >= 0 ? 'success' : 'danger')
                    ->description('Lợi nhụân gộp - Lỗ tháng trước'),
                Tables\Columns\TextColumn::make('adjusted_profit')
                    ->label('Lợi nhuận điều chỉnh (Lãi thực tế)')
                    ->money('USD')
                    ->sortable()
                    ->color(fn ($state): string => $state >= 0 ? 'success' : 'danger')
                    ->description('Bank Payout - Chi phí - Luơng cơ bản'),
                Tables\Columns\TextColumn::make('bank_payout')
                    ->label('Bank Payout')
                    ->money('USD')
                    ->sortable()
                    ->state(function ($record) {
                        return SellerService::getBankPayoutFromRecord($record);
                    })
                    ->color('info'),
                Tables\Columns\TextColumn::make('total_salary')
                    ->label('Thu nhập của seller')
                    ->money('USD')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'pending' => 'Chờ xử lý',
                        'completed' => 'Hoàn thành',
                        'cancelled' => 'Đã hủy',
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'completed' => 'success',
                        'pending' => 'warning',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('payout')
                    ->label('Thanh Toán')
                    ->getStateUsing(function ($record) {
                        return view('components.finance.payment-column', [
                            'record' => $record
                        ]);
                    })
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('teams')
                    ->relationship('seller.teams', 'name')
                    ->label('Teams'),
                static::getUserFilter('seller_id', 'User'),

                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Chờ Xử Lý',
                        'partial' => 'Thanh Toán Một Phần',
                        'completed' => 'Hoàn Thành',
                    ])
                    ->label('Trạng Thái'),

                DateRangeFilter::make('created_at')
                    ->label('Ngày tạo')
                    ->displayFormat('D/M/Y')
                       ->ranges([
                        'Tháng này' => [now()->startOfMonth(), now()->endOfMonth()],
                        'Tháng trước' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                        '2 tháng trước' => [
                            now()->subMonths(2)->startOfMonth(),
                            now()->subMonths(2)->endOfMonth(),
                        ],
                        '3 tháng trước' => [
                            now()->subMonths(3)->startOfMonth(),
                            now()->subMonths(3)->endOfMonth(),
                        ],
                        'Quý này' => [
                            now()->startOfQuarter(),
                            now()->endOfQuarter(),
                        ],
                        'Quý trước' => [
                            now()->subQuarter()->startOfQuarter(),
                            now()->subQuarter()->endOfQuarter(),
                        ],
                        'Năm nay' => [now()->startOfYear(), now()->endOfYear()],
                        'Tất cả' => [now()->subYear(2), now()],
                    ])
            ])
            ->headerActions([
                ExportAction::make()
                    ->label('Xuất Excel')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->exporter(SellerFinanceExport::class)
                    ->formats([
                        ExportFormat::Xlsx,
                    ])
                    ->fileName(fn (): string => 'bao-cao-tai-chinh-' . now()->format('Y-m-d-H-i-s'))
                    ->maxRows(10000) // Giới hạn số dòng để tránh timeout
                    ->chunkSize(1000) // Giảm chunk size
                    ->fileDisk('local'), // Luôn sử dụng local disk

                ExportAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->exporter(SellerFinanceExport::class)
                    ->formats([
                        ExportFormat::Csv,
                    ])
                    ->fileName(fn (): string => 'bao-cao-tai-chinh-csv-' . now()->format('Y-m-d-H-i-s'))
                    ->maxRows(10000)
                    ->chunkSize(1000)
                    ->fileDisk('local'),
            ])
            ->actions([], position: ActionsPosition::BeforeColumns)
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('month', 'desc')
            ->emptyStateHeading('Không có báo cáo tài chính nào')
            ->emptyStateDescription('Không tìm thấy báo cáo tài chính nào trong khoảng thời gian bạn đã chọn')
            ->emptyStateIcon('heroicon-o-chart-bar');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSellerFinances::route('/'),
            //'create' => Pages\CreateSellerFinance::route('/create'),
            //'edit' => Pages\EditSellerFinance::route('/{record}/edit'),
        ];
    }




}
