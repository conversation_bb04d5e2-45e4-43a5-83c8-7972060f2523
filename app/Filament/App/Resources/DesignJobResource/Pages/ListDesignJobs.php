<?php

namespace App\Filament\App\Resources\DesignJobResource\Pages;

use App\Filament\App\Resources\DesignJobResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use App\Enums\DesignJobStatus;
use App\Models\DesignJob;
use App\Services\LarkService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class ListDesignJobs extends ListRecords
{
    protected static string $resource = DesignJobResource::class;
    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Jobs')
                ->badge(static::getModel()::count())
                ->badgeColor('gray'),
            
            ...array_map(
                fn (DesignJobStatus $status) => Tab::make($status->getLabel())
                    ->badge(static::getModel()::where('status', $status)->count())
                    ->badgeColor($status->getColor())
                    ->modifyQueryUsing(fn ($query) => $query->where('status', $status)),
                DesignJobStatus::cases()
            ),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    /**
     * Kiểm tra xem designer đã được gửi thông báo cho job này trong 1 tiếng qua chưa
     */
    private function canSendNotification(int $jobId, int $designerId): bool
    {
        $cacheKey = "lark_notification_job_{$jobId}_designer_{$designerId}";
        return !Cache::has($cacheKey);
    }

    /**
     * Đánh dấu đã gửi thông báo cho designer về job này
     */
    private function markNotificationSent(int $jobId, int $designerId): void
    {
        $cacheKey = "lark_notification_job_{$jobId}_designer_{$designerId}";
        Cache::put($cacheKey, true, now()->addHour()); // Cache 1 tiếng
    }

    /**
     * Gửi thông báo Lark cho designer về job quá hạn
     */
    public function sendLarkNotification(int $jobId): void
    {
        $record = DesignJob::find($jobId);

        if (!$record) {
            Notification::make()
                ->title('Lỗi')
                ->body('Không tìm thấy job.')
                ->danger()
                ->send();
            return;
        }

        // Kiểm tra điều kiện
        $daysDiff = (int) Carbon::parse($record->created_at)->diffInDays(now());
        if ($daysDiff < 2 || !$record->designer_id || $record->status === DesignJobStatus::COMPLETED) {
            Notification::make()
                ->title('Không thể gửi thông báo')
                ->body('Job này không đủ điều kiện để gửi thông báo.')
                ->warning()
                ->send();
            return;
        }

        $larkService = app(LarkService::class);
        $sender = Auth::user();
        $designer = $record->designer;

        if (!$designer) {
            Notification::make()
                ->title('Lỗi')
                ->body('Job này chưa được assign cho designer nào.')
                ->danger()
                ->send();
            return;
        }

        if (!$designer->hasLarkBotConfigured()) {
            Notification::make()
                ->title('Không thể gửi thông báo')
                ->body("Designer {$designer->name} chưa cấu hình Lark bot.")
                ->warning()
                ->send();
            return;
        }

        // Kiểm tra cache để tránh spam
        if (!$this->canSendNotification($record->id, $designer->id)) {
            Notification::make()
                ->title('Thông báo đã được gửi')
                ->body("Thông báo cho {$designer->name} về job này đã được gửi trong vòng 1 tiếng qua. Vui lòng chờ trước khi gửi lại.")
                ->warning()
                ->send();
            return;
        }

        $daysDiff = (int) Carbon::parse($record->created_at)->diffInDays(now());
        $message = "🔔 THÔNG BÁO JOB QUÁ HẠN\n\n";
        $message .= "📋 Job: {$record->title}\n";
        $message .= "⏰ Đã quá hạn: {$daysDiff} ngày\n";
        $message .= "📅 Ngày tạo: " . Carbon::parse($record->created_at)->format('d/m/Y H:i') . "\n";
        $message .= "💰 Giá: $" . number_format($record->price, 2) . "\n";
        $message .= "🏷️ Loại: " . $record->job_type->getLabel() . "\n";
        $message .= "📝 Trạng thái: " . $record->status->getLabel() . "\n\n";
        $message .= "Vui lòng xử lý job này sớm nhất có thể!";

        try {
            $larkSend = $larkService->sendMessage($sender, $designer, $message);

            if ($larkSend->isSent()) {
                // Đánh dấu đã gửi thông báo
                $this->markNotificationSent($record->id, $designer->id);

                Notification::make()
                    ->title('Gửi thông báo thành công')
                    ->body("Đã gửi thông báo cho {$designer->name} qua Lark.")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Gửi thông báo thất bại')
                    ->body($larkSend->error_message ?? 'Có lỗi xảy ra khi gửi thông báo.')
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi hệ thống')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
