<?php

namespace App\Filament\App\Resources\TiktokSettlementResource\Pages;

use App\Filament\App\Resources\TiktokSettlementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTiktokSettlements extends ListRecords
{
    protected static string $resource = TiktokSettlementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
