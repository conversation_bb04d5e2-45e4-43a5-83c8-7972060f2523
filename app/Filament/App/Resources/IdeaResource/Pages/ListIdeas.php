<?php

namespace App\Filament\App\Resources\IdeaResource\Pages;

use App\Enums\IdeaStatus;
use App\Filament\App\Resources\IdeaResource;
use App\Models\Idea;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListIdeas extends ListRecords
{
    protected static string $resource = IdeaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->slideOver()->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('All')
                ->modifyQueryUsing(fn (Builder $query) => $query)
        ];
    
        $statusTabs = collect(IdeaStatus::cases())->mapWithKeys(function ($status) {
            $statusName = $status->value;
            $statusLabel = $status->getLabel();
            $badgeColor = $status->getColor();
    
            return [
                strtolower($statusName) => Tab::make($statusLabel)
                    ->modifyQueryUsing(fn (Builder $query) => $query->where('status', $statusName))
                    ->badge(Idea::where('status', $statusName)->count())
                    ->badgeColor($badgeColor),
            ];
        })->toArray();
    
        return $tabs + $statusTabs;
    }
    public function getDefaultActiveTab(): string | int | null
    {
        return 'processing';
    }
}
