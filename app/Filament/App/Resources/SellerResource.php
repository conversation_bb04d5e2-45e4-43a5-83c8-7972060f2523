<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\SellerResource\Pages;
use App\Filament\App\Resources\SellerResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Collection;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Indicator;
use Filament\Resources\Components\Tab;
use Filament\Notifications\Notification;
use Filament\Resources\Components\Actions;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class SellerResource extends Resource
{
    // use HasPageShield; // Comment để tránh xung đột permissions

    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Seller Management';
    protected static ?string $navigationGroup = 'Seller Management';
    protected static ?string $pluralModelLabel = 'Sellers';
    protected static ?string $modelLabel = 'Seller';
    protected static ?int $navigationSort = 1;

    public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Seller Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter your name')
                            ->label('Name'),

                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->placeholder('Enter your email')
                            ->label('Email'),

                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->placeholder('Enter your password')
                            ->dehydrateStateUsing(fn ($state) => filled($state) ? Hash::make($state) : null)
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (string $context): bool => $context === 'create')
                            ->label('Password')
                            ->hiddenOn('view'),

                        Forms\Components\TextInput::make('password_confirmation')
                            ->password()
                            ->placeholder('Enter your password again')
                            ->required(fn (string $context): bool => $context === 'create')
                            ->label('Confirm Password')
                            ->hiddenOn(['view', 'edit']),

                        Forms\Components\TextInput::make('salary_amount')
                            ->label('Lương cơ bản')
                            ->numeric()
                            ->prefix('$')
                            ->placeholder('0.00')
                            ->step(0.01)
                            ->hint('Lương cơ bản của seller'),

                        Forms\Components\TextInput::make('commission_rate')
                            ->label('Tỷ lệ hoa hồng (%)')
                            ->numeric()
                            ->suffix('%')
                            ->placeholder('0')
                            ->step(0.01)
                            ->hint('Tỷ lệ hoa hồng của seller'),

                        Forms\Components\CheckboxList::make('teams')
                            ->relationship('teams', 'name')
                            ->columns(2)
                            ->searchable()
                            ->gridDirection('row')
                            ->label('Teams')
                            ->live()
                            ->afterStateUpdated(function ($state, $set) {
                                // Nếu có nhiều hơn 1 giá trị được chọn, chỉ giữ lại giá trị cuối cùng
                                if (is_array($state) && count($state) > 1) {
                                    $set('teams', [end($state)]);
                                }
                            })
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('avatar_url')
                    ->state(function (User $record) {
                        return $record->getFilamentAvatarUrl();
                    })
                    ->circular()
                    ->label('Avatar'),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Enter your name')
                    ->label('Name')
                    ->description(function (User $record) {
                        $color = match(true) {
                            !$record->last_activity => 'danger',
                            $record->last_activity->diffInHours() < 24 => 'success',
                            $record->last_activity->diffInDays() < 7 => 'warning',
                            default => 'danger'
                        };

                        $status = !$record->last_activity
                            ? 'Never active'
                            : 'Last active: ' . $record->last_activity->diffForHumans();

                        return new \Illuminate\Support\HtmlString('<span class="font-medium text-' . $color . '-600">' . $status . '</span>');
                    })
                    ->html(),

                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Enter your email')
                    ->label('Email'),

                Tables\Columns\TagsColumn::make('teams.name')
                    ->label('Teams')
                    ->limit(2)
                    ->separator(', ')
                    ->action(
                        Tables\Actions\Action::make('view_teams')
                            ->icon('heroicon-s-user-group')
                            ->color('primary')
                            ->mountUsing(fn (Forms\ComponentContainer $form, User $record) => $form->fill([
                                'user_name' => $record->name,
                            ]))
                            ->form([
                                Forms\Components\Hidden::make('user_name'),
                                Forms\Components\Placeholder::make('teams_list')
                                    ->label('Team Memberships')
                                    ->content(function (User $record) {
                                        $teams = $record->teams;
                                        if ($teams->isEmpty()) {
                                            return new \Illuminate\Support\HtmlString('<div class="text-gray-500 italic">No teams assigned</div>');
                                        }

                                        $content = '<div class="space-y-1">';
                                        foreach ($teams as $team) {
                                            $content .= '<div class="flex items-center gap-2 p-2 bg-primary-50 dark:bg-primary-800/20 rounded-lg">';
                                            $content .= '<div class="flex-shrink-0 w-8 h-8 rounded-full bg-primary-500/10 flex items-center justify-center text-primary-500">';
                                            $content .= '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg>';
                                            $content .= '</div>';
                                            $content .= '<div class="text-sm font-medium">' . e($team->name) . '</div>';
                                            $content .= '</div>';
                                        }
                                        $content .= '</div>';

                                        return new \Illuminate\Support\HtmlString($content);
                                    })
                                    ->columnSpanFull(),
                            ])
                            ->modalWidth('md')
                            ->modalHeading(fn (User $record) => 'Teams for ' . $record->name)
                            ->modalIcon('heroicon-o-user-group')
                            ->modalSubmitAction(false)
                            ->modalCancelAction(false)
                            ->modalCloseButton()
                    ),

                Tables\Columns\TagsColumn::make('roles.name')
                    ->label('Roles')
                    ->limit(2)
                    ->separator(', ')
                    ->action(
                        Tables\Actions\Action::make('view_roles')
                            ->icon('heroicon-s-shield-check')
                            ->color('warning')
                            ->mountUsing(fn (Forms\ComponentContainer $form, User $record) => $form->fill([
                                'user_name' => $record->name,
                            ]))
                            ->form([
                                Forms\Components\Hidden::make('user_name'),
                                Forms\Components\Placeholder::make('roles_list')
                                    ->label('User Roles')
                                    ->content(function (User $record) {
                                        $roles = $record->roles;
                                        if ($roles->isEmpty()) {
                                            return new \Illuminate\Support\HtmlString('<div class="text-gray-500 italic">No roles assigned</div>');
                                        }

                                        $content = '<div class="space-y-1">';
                                        foreach ($roles as $role) {
                                            $content .= '<div class="flex items-center gap-2 p-2 bg-warning-50 dark:bg-warning-800/20 rounded-lg">';
                                            $content .= '<div class="flex-shrink-0 w-8 h-8 rounded-full bg-warning-500/10 flex items-center justify-center text-warning-500">';
                                            $content .= '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>';
                                            $content .= '</div>';
                                            $content .= '<div class="text-sm font-medium">' . e($role->name) . '</div>';
                                            $content .= '</div>';
                                        }
                                        $content .= '</div>';

                                        return new \Illuminate\Support\HtmlString($content);
                                    })
                                    ->columnSpanFull(),
                            ])
                            ->modalWidth('md')
                            ->modalHeading(fn (User $record) => 'Roles for ' . $record->name)
                            ->modalIcon('heroicon-o-shield-check')
                            ->modalSubmitAction(false)
                            ->modalCancelAction(false)
                            ->modalCloseButton()
                    ),

                Tables\Columns\TextColumn::make('created_at')
                    ->date('d/m/Y')
                    ->sortable()
                    ->label('Created Date'),


            ])
            ->filters([
                // Add advanced search filters
                Tables\Filters\SelectFilter::make('roles')
                    ->relationship('roles', 'name')
                    ->multiple()
                    ->preload()
                    ->label('Filter by Roles'),

                Tables\Filters\SelectFilter::make('teams')
                    ->relationship('teams', 'name')
                    ->multiple()
                    ->preload()
                    ->label('Filter by Teams'),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Created From'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Created Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['created_from'] ?? null) {
                            $indicators['created_from'] = 'Created from ' . Carbon::parse($data['created_from'])->toFormattedDateString();
                        }

                        if ($data['created_until'] ?? null) {
                            $indicators['created_until'] = 'Created until ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                        }

                        return $indicators;
                    })->columnSpan(2) ->columns(2),
            ])
            ->actions([
                Tables\Actions\Action::make('manage_seller_role')
                    ->label(fn (User $record): string => $record->hasRole('Seller') ? 'Remove Seller Role' : 'Assign Seller Role')
                    ->icon(fn (User $record): string => $record->hasRole('Seller') ? 'heroicon-o-x-circle' : 'heroicon-o-check-badge')
                    ->color(fn (User $record): string => $record->hasRole('Seller') ? 'danger' : 'success')
                    ->requiresConfirmation()
                    ->modalHeading(fn (User $record): string => $record->hasRole('Seller') ? 'Remove Seller Role' : 'Assign Seller Role')
                    ->modalDescription(fn (User $record): string => $record->hasRole('Seller')
                        ? "Are you sure you want to remove the Seller role from {$record->name}?"
                        : "Are you sure you want to assign the Seller role to {$record->name}?")
                    ->visible(fn (): bool => Auth::user()->hasAnyRole(['super_admin', 'User Manager']))
                    ->action(function (User $record) {
                        try {
                            if ($record->hasRole('Seller')) {
                                $record->removeRole('Seller');
                                $message = 'Seller role removed';
                            } else {
                                $record->assignRole('Seller');
                                $message = 'Seller role assigned';
                            }

                            Notification::make()
                                ->title($message)
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Error: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),

                // Manage teams - for all users
                // Tables\Actions\Action::make('manage_teams')
                //     ->label('Manage Teams')
                //     ->icon('heroicon-o-user-group')
                //     ->color('primary')
                //     ->modalWidth('md')
                //     ->modalHeading(fn (User $record): string => "Manage Teams for {$record->name}")
                //     ->mountUsing(fn (Forms\ComponentContainer $form, User $record) => $form->fill([
                //         'teams' => $record->teams()->pluck('teams.id')->toArray(),
                //     ]))
                //     ->visible(fn (): bool => Auth::user()->hasAnyRole(['super_admin', 'User Manager']))
                //     ->action(function (User $record, array $data): void {
                //         // Only sync if teams data is provided, otherwise do nothing
                //         if (isset($data['teams'])) {
                //             $record->teams()->sync($data['teams']);

                //             Notification::make()
                //                 ->title('Teams updated successfully')
                //                 ->success()
                //                 ->send();
                //         }
                //     })
                //     ->form([
                //         Forms\Components\CheckboxList::make('teams')
                //             ->label('Teams')
                //             ->relationship('teams', 'name', fn (Builder $query) => $query->select('teams.*'))
                //             ->columns(2)
                //             ->bulkToggleable()
                //             ->searchable()
                //             ->gridDirection('row')
                //             ->columnSpanFull(),
                //     ]),
            ])
            ->bulkActions([
                // Bulk assign seller role
                Tables\Actions\BulkAction::make('assign_seller_bulk')
                    ->label('Assign Seller Role')
                    ->icon('heroicon-o-check-badge')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Assign Seller Role')
                    ->modalDescription('Are you sure you want to assign the Seller role to the selected users?')
                    ->visible(fn (): bool => Auth::user()->hasAnyRole(['super_admin', 'User Manager']))
                    ->action(function (Collection $records) {
                        $sellerRole = Role::findById(4); // ID 4 is seller
                        foreach ($records as $record) {
                            if (!$record->hasRole('seller')) {
                                $record->assignRole($sellerRole);
                            }
                        }

                        Notification::make()
                            ->title('Seller roles assigned')
                            ->success()
                            ->send();
                    })
                    ->deselectRecordsAfterCompletion(),

                // Bulk remove seller role
                Tables\Actions\BulkAction::make('remove_seller_bulk')
                    ->label('Remove Seller Role')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Remove Seller Role')
                    ->modalDescription('Are you sure you want to remove the Seller role from the selected users?')
                    ->visible(fn (): bool => Auth::user()->hasAnyRole(['super_admin', 'User Manager']))
                    ->action(function (Collection $records) {
                        foreach ($records as $record) {
                            if ($record->hasRole('seller')) {
                                $record->removeRole('seller');
                            }
                        }

                        Notification::make()
                            ->title('Seller roles removed')
                            ->success()
                            ->send();
                    })
                    ->deselectRecordsAfterCompletion(),

                // Bulk add to teams
                Tables\Actions\BulkAction::make('add_to_teams')
                    ->label('Add to Teams')
                    ->icon('heroicon-o-user-group')
                    ->color('primary')
                    ->requiresConfirmation()
                    ->modalHeading('Add to Teams')
                    ->modalWidth('md')
                    ->visible(fn (): bool => Auth::user()->hasAnyRole(['super_admin', 'User Manager']))
                    ->action(function (Collection $records, array $data) {
                        foreach ($records as $record) {
                            $record->teams()->syncWithoutDetaching($data['teams']);
                        }

                        Notification::make()
                            ->title('Users added to teams')
                            ->success()
                            ->send();
                    })
                    ->form([
                        Forms\Components\CheckboxList::make('teams')
                            ->label('Teams')
                            ->relationship('teams', 'name', fn (Builder $query) => $query->select('teams.*'))
                            ->columns(2)
                            ->bulkToggleable()
                            ->searchable()
                            ->gridDirection('row')
                            ->required()
                            ->columnSpanFull(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSellers::route('/'),
            'create' => Pages\CreateSeller::route('/create'),
            'view' => Pages\ViewSeller::route('/{record}'),
            'edit' => Pages\EditSeller::route('/{record}/edit'),
        ];
    }
}
