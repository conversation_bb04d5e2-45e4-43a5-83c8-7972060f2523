<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\FlashSaleResource\Pages;
use App\Models\FlashSale;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

class FlashSaleResource extends Resource
{
    protected static ?string $model = FlashSale::class;
    protected static ?string $navigationIcon = 'heroicon-o-bolt';
    protected static ?string $navigationLabel = 'Flash Sales';
    protected static ?int $navigationSort = 3;

    public static function getNavigationGroup(): ?string
    {
        return 'TikTok Shop';
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->heading('Flash Sales Management')
            ->description('Quản lý và tự động hóa Flash Sales trên TikTok Shop')
            ->columns([
                TextColumn::make('deal_name')
                    ->label('Flash Sale Title')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('store.name')
                    ->label('Store')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('start')
                    ->label('Start Time')
                    ->formatStateUsing(function ($state) {
                        $time = Carbon::parse($state)->setTimezone('America/Phoenix');
                        return   $time->diffForHumans();
                    })
                    ->sortable(),
                
                TextColumn::make('expire')
                    ->label('End Time')
                    ->formatStateUsing(function ($state) {
                        $time = Carbon::parse($state)->setTimezone('America/Phoenix');
                        $textColor = $time->isFuture() && $time->diffInHours(now()) <= 24 ? 'text-warning-500' : '';
                        
                        return "<div class='{$textColor}'>" . 
                 
                            $time->diffForHumans() . 
                        "</div>";
                    })
                    ->html()
                    ->sortable(),
                
                TextColumn::make('deal_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'ONGOING' => 'success',
                        'PRODUCT UPDATE' => 'warning',
                        'DUPLICATED' => 'info',
                        'DEACTIVATED' => 'gray',
                        default => 'warning',
                    }),
                TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->since()
                    ->sortable(),
                TextColumn::make('action')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        default => 'gray',
                    }),
                
          
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('store_id')
                    ->relationship('store', 'name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('deal_status')
                    ->options([
                        'ONGOING' => 'Ongoing',
                        'PRODUCT UPDATE' => 'Product Update',
                        'DUPLICATED' => 'Duplicated',
                        'DEACTIVATED' => 'Deactivated'
                    ]),
                SelectFilter::make('action')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                    ]),
                TernaryFilter::make('auto_renew')
                    ->label('Auto Renew'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
             
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFlashSales::route('/'),
            'view' => Pages\ViewFlashSale::route('/{record}'),
        ];
    }
}