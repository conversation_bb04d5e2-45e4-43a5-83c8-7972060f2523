<?php

namespace App\Filament\App\Resources\TelegramSendResource\Pages;

use App\Filament\App\Resources\TelegramSendResource;
use App\Services\TelegramBotManagementService;
use Filament\Actions;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Resources\Pages\Page;
use Telegram\Bot\Laravel\Facades\Telegram;
use Illuminate\Support\Facades\Log;

class BotManagement extends Page
{
    use InteractsWithFormActions;

    protected static string $resource = TelegramSendResource::class;

    protected static string $view = 'filament.app.resources.telegram-send-resource.pages.bot-management';

    protected static ?string $title = 'Bot Management';

    protected static ?string $navigationLabel = 'Bot Management';

    public ?array $data = [];

    public function mount(): void
    {
        $this->loadBotStatus();
    }

    protected function getBotManagementService()
    {
        try {
            return app(TelegramBotManagementService::class);
        } catch (\Exception $e) {
            Log::error('Failed to initialize TelegramBotManagementService', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    protected function loadBotStatus(): void
    {
        try {
            $botManagementService = $this->getBotManagementService();
            if (!$botManagementService) {
                throw new \Exception('Bot management service is not initialized');
            }

            $status = $botManagementService->getBotStatus();
            $this->data = $status;
        } catch (\Exception $e) {
            Log::error('Failed to load bot status', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->data = ['error' => $e->getMessage()];
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('refresh')
                ->label('Refresh Status')
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->action(function () {
                    $this->loadBotStatus();
                    Notification::make()
                        ->title('Status refreshed')
                        ->success()
                        ->send();
                }),



            Actions\Action::make('get_invite_link')
                ->label('Get Invite Link')
                ->icon('heroicon-o-link')
                ->color('success')
                ->action(function () {
                    try {
                        $botInfo = Telegram::bot('special_bot')->getMe();
                        $botUsername = $botInfo->getUsername();
                        $inviteLink = "https://t.me/{$botUsername}";

                        Notification::make()
                            ->title('Invite Link')
                            ->body("Share this link with sellers: {$inviteLink}")
                            ->success()
                            ->persistent()
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Error getting invite link')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Actions\Action::make('send_test_broadcast')
                ->label('Test Broadcast')
                ->icon('heroicon-o-megaphone')
                ->color('warning')
                ->form([
                    Forms\Components\Textarea::make('message')
                        ->label('Test Message')
                        ->required()
                        ->default('🧪 This is a test broadcast message from the admin.')
                        ->rows(3),
                    Forms\Components\Toggle::make('confirm')
                        ->label('I confirm to send this message to all active users')
                        ->required(),
                ])
                ->action(function (array $data) {
                    if (!$data['confirm']) {
                        return;
                    }

                    try {
                        $botManagementService = $this->getBotManagementService();
                        if (!$botManagementService) {
                            throw new \Exception('Bot management service is not available');
                        }

                        $results = $botManagementService->sendSystemNotification($data['message']);
                        $successCount = count($results);

                        Notification::make()
                            ->title('Test broadcast sent')
                            ->body("Message sent to {$successCount} users")
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Log::error('Error sending test broadcast', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        Notification::make()
                            ->title('Error sending broadcast')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }

    public function getBotInfo(): ?array
    {
        return $this->data['bot_info'] ?? null;
    }

    public function getWebhookInfo(): ?array
    {
        return $this->data['webhook_info'] ?? null;
    }

    public function getStatistics(): ?array
    {
        return $this->data['statistics'] ?? null;
    }

    public function hasError(): bool
    {
        return isset($this->data['error']);
    }

    public function getError(): ?string
    {
        return $this->data['error'] ?? null;
    }

    public function getInviteLink(): ?string
    {
        try {
            $botInfo = $this->getBotInfo();
            if ($botInfo && isset($botInfo['username'])) {
                return "https://t.me/{$botInfo['username']}";
            }
        } catch (\Exception $e) {
            Log::error('Error getting invite link', ['error' => $e->getMessage()]);
        }
        return null;
    }
}
