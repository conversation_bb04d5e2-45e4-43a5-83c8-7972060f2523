<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\MockupTemplateResource\Pages;
use App\Models\MockupTemplate;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\Facades\DB;

class MockupTemplateResource extends Resource
{
    protected static ?string $model = MockupTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static bool $shouldRegisterNavigation = false;
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()->columnSpanFull()
                    ->maxLength(255),

                Forms\Components\FileUpload::make('apparel_image')
                    ->disk('s3')->directory('mockup')
                    ->visibility('public')
                    ->required()
                    ->image()
                    ->imageResizeMode('contain')
                    ->imageResizeTargetWidth('1200')
                    ->imageResizeTargetHeight('1200'),

                Forms\Components\Hidden::make('design_position_x'),
                Forms\Components\Hidden::make('design_position_y'),
                Forms\Components\Hidden::make('design_width'),
                Forms\Components\Hidden::make('design_height'),

                Forms\Components\Toggle::make('has_back_view')
                    ->label('Has Back View')
                    ->reactive(),

                Forms\Components\Section::make('Design Position')
                    ->schema([
                        Forms\Components\Hidden::make('design_position_x'),
                        Forms\Components\Hidden::make('design_position_y'),
                        Forms\Components\Hidden::make('design_width'),
                        Forms\Components\Hidden::make('design_height'),
                        Forms\Components\Hidden::make('back_design_position_x'),
                        Forms\Components\Hidden::make('back_design_position_y'),
                        Forms\Components\Hidden::make('back_design_width'),
                        Forms\Components\Hidden::make('back_design_height'),
                        Forms\Components\Hidden::make('design_position_data')
                            ->afterStateUpdated(function ($state, $set) {

                                try {
                                    foreach ($state as $key => $value) {
                                        $set($key, $value);
                                    }
                                } catch (\Exception $e) {
                                }
                            }),
                        Forms\Components\View::make('filament.app.pages.components.design-position-editor')
                            ->statePath('design_position_data'),
                        Actions::make([
                            Action::make('updateDesignPosition2')
                                ->label('Update Design Position')
                                ->action(function (Get $get, Set $set, $record) {
                                    DB::beginTransaction();
                                    try {
                                        $designData = $get('design_position_data');
                                       
                                        $decodedData = is_string($designData) ? json_decode($designData, true) : $designData;

                                        if (!is_array($decodedData)) {
                                            throw new \Exception("Invalid design data format");
                                        }

                                        foreach ($decodedData as $key => $value) {
                                            $set($key, $value);
                                            $record->$key = $value;
                                        }

                                        $record->save();

                                        DB::commit();

                                        \Filament\Notifications\Notification::make()
                                            ->success()
                                            ->title('Design position updated and saved successfully')
                                            ->send();
                                    } catch (\Exception $e) {
                                        DB::rollBack();

                                        \Filament\Notifications\Notification::make()
                                            ->danger()
                                            ->title('Error updating design position')
                                            ->body('Please try again or contact support if the problem persists.')
                                            ->send();
                                    }
                                })
                        ])
                    ])
                    ->columnSpan('full'),
            ]);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\ImageColumn::make('apparel_image')
                    ->disk('s3'),
                Tables\Columns\ImageColumn::make('design_image')
                    ->disk('s3'),
                Tables\Columns\TextColumn::make('design_position_x')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('design_position_y')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('design_width')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('design_height')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->url(fn ($record): string => static::getUrl('edit', ['record' => $record]))
                    ->openUrlInNewTab() // Thêm dòng này
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMockupTemplates::route('/'),
            'create' => Pages\CreateMockupTemplate::route('/create'),
            'edit' => Pages\EditMockupTemplate::route('/{record}/edit'),
        ];
    }

    public static function getS3ImageUrl($path)
    {
        if ($path && Storage::disk('s3')->exists($path)) {
            return Storage::disk('s3')->url($path);
        }
        return null;
    }
}
