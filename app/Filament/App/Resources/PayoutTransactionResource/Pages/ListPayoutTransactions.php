<?php

namespace App\Filament\App\Resources\PayoutTransactionResource\Pages;

use App\Filament\App\Resources\PayoutTransactionResource;
use App\Filament\Imports\PayoutTransactionImporter;
use Filament\Actions;
use Filament\Actions\ImportAction;
use Filament\Resources\Pages\ListRecords;

class ListPayoutTransactions extends ListRecords
{
    protected static string $resource = PayoutTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
           // Actions\CreateAction::make(),
           ImportAction::make()
           ->importer(PayoutTransactionImporter::class)->hidden(function(){
                return auth()->user()->hasRole('super_admin') ? false : true;
           })
           ->label('Import Payout Transactions'),
        ];
    }
}
