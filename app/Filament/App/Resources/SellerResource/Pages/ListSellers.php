<?php

namespace App\Filament\App\Resources\SellerResource\Pages;

use App\Filament\App\Resources\SellerResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class ListSellers extends ListRecords
{
    // use HasPageShield; // Comment để tránh xung đột permissions

    protected static string $resource = SellerResource::class;

    public ?string $activeTab = 'all';

    protected function getHeaderActions(): array
    {
        $actions = [];

        if (Auth::user()->hasAnyRole(['super_admin', 'Leader'])) {
            $actions[] = CreateAction::make()
                ->label('Create New Seller')
                ->icon('heroicon-o-plus');
        }

        return $actions;
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // SellerOverview widget removed
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Users')
                ->badge(User::count())
                ->icon('heroicon-o-users'),

            'sellers' => Tab::make('Sellers')
                ->icon('heroicon-o-user-group')
                ->badge(User::role('seller')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->role('seller')),

            'non_sellers' => Tab::make('Non-Sellers')
                ->icon('heroicon-o-user')
                ->badge(User::whereDoesntHave('roles', function ($q) {
                    $q->where('name', 'seller');
                })->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->whereDoesntHave('roles', function ($q) {
                    $q->where('name', 'seller');
                })),
        ];
    }
}
