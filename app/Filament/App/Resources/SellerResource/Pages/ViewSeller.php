<?php

namespace App\Filament\App\Resources\SellerResource\Pages;

use App\Filament\App\Resources\SellerResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Spatie\Permission\Models\Role;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Auth;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class ViewSeller extends ViewRecord
{
    // use HasPageShield; // Comment để tránh xung đột permissions

    protected static string $resource = SellerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->modalCancelAction(false)
                ->successNotificationTitle(''),

            Actions\DeleteAction::make()
                ->modalCancelAction(false)
                ->successNotificationTitle(''),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // SellerOverview widget removed
        ];
    }
}
