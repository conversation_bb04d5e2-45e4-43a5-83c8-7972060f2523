<?php

namespace App\Filament\App\Resources\SellerResource\Pages;

use App\Filament\App\Resources\SellerResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Auth;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class CreateSeller extends CreateRecord
{
    // use HasPageShield; // Comment để tránh xung đột permissions

    protected static string $resource = SellerResource::class;



    // Redirect back to list view after creation
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    // Customize "Create" button
    // protected function getCreateFormAction(): Actions\CreateAction
    // {
    //     return parent::getCreateFormAction();
    // }

    // Disable "Create and Create Another" button
    protected function getCreateAnotherFormAction(): Actions\Action
    {
        return parent::getCreateAnotherFormAction()
            ->visible(false);
    }


    // Validate password confirmation
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Remove password_confirmation as it doesn't exist in the database
        unset($data['password_confirmation']);

        return $data;
    }

    // Always assign seller role after creation
    protected function afterCreate(): void
    {
        $sellerRole = Role::findById(4); // ID 4 is seller
        if ($sellerRole) {
            $this->record->assignRole($sellerRole);
        }

        // Remove the custom notification to avoid duplicate notifications
        // Filament already shows a default notification after creation
    }
}
