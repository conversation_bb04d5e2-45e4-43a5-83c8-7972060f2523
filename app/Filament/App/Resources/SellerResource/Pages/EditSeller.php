<?php

namespace App\Filament\App\Resources\SellerResource\Pages;

use App\Filament\App\Resources\SellerResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Auth;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;

class EditSeller extends EditRecord
{
    // use HasPageShield; // Comment để tránh xung đột permissions

    protected static string $resource = SellerResource::class;



    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
