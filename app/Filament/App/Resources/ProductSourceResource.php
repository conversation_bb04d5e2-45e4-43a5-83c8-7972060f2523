<?php

namespace App\Filament\App\Resources;

use App\Enums\TiktokShopStatus;
use App\Filament\App\Resources\ProductSourceResource\Pages;
use App\Filament\App\Resources\ProductSourceResource\Pages\ManageProductToUploads;
use App\Filament\App\Resources\ProductSourceResource\RelationManagers\ProductToUploadsRelationManager;
use App\Forms\Components\ImageGalleryField;
use App\Models\ProductSource;
use App\Models\Store;
use App\Models\Template;
use App\Models\Team;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\RichEditor;
use Filament\Notifications\Notification;
use App\Services\ProductImportService;
use App\Services\OpenAIService;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\HtmlString;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use App\Services\Tiktok\TiktokShopService;
use Filament\Forms\Get;
use Exception;
use Filament\Support\Enums\IconPosition;
use App\Models\User;

class ProductSourceResource extends Resource
{
    protected static ?string $model = ProductSource::class;
    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';
    protected static ?string $navigationGroup = 'Products';
    protected static ?string $navigationLabel = 'Product Sources';
    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make()
                ->schema([
                    // Phần thông tin sản phẩm - 2/3 chiều rộng
                    Section::make('Product Information')
                        ->description('Enter detailed information about the product')
                        ->icon('heroicon-o-tag')
                        ->schema([
                            TextInput::make('title')
                                ->label('Product Name')
                                ->required()
                                ->live()
                                ->helperText(fn($state): string => strlen($state) . ' / 255 characters')
                                ->maxLength(255)
                                ->placeholder('Enter product name...')
                                ->columnSpanFull(),

                            Grid::make()->schema([
                                TextInput::make('trademark')
                                    ->label('Trademark')
                                    ->helperText('Trademark check result')
                                    ->maxLength(255)
                                    ->dehydrated(true)
                                    ->disabled()
                                    ->placeholder('Not checked yet'),
                                
                                Forms\Components\Actions::make([
                                    Forms\Components\Actions\Action::make('checkTrademark')
                                        ->label('Check Trademark')
                                        ->color('gray')
                                        ->icon('heroicon-o-shield-check')
                                        ->iconPosition(IconPosition::After)
                                        ->action(function (Get $get, Set $set) {
                                            self::checkTrademark($get, $set);
                                        }),
                                ])->alignEnd(),
                            ])->columns(2)->columnSpanFull(),
                            
                            TextInput::make('store_note')
                                ->label('Uploaded to Store')
                                ->helperText('Note which stores this product has been uploaded to')
                                ->placeholder('Not uploaded to any store yet')
                                ->maxLength(255)
                                ->columnSpanFull(),

                            // Team filter instead of direct relationship
                            Forms\Components\Select::make('team_filter')
                                ->label('Team Filter')
                                ->options(function() {
                                    $user = auth()->user();
                                    return $user->teams->pluck('name', 'id')->toArray();
                                })
                                ->helperText('This is just a visual indicator, not a relationship field')
                                ->dehydrated(false) // Don't save this to the database
                                ->disabled(true) // Just for display
                                ->columnSpanFull(),

                            // Thêm tải ảnh và mô tả
                            ImageGalleryField::make('images')
                                ->label('Product Images')
                                ->helperText('Maximum 8 images, optimal size 1000x1000px')
                                ->columnSpanFull(),

                            RichEditor::make('description')
                                ->label('Product Description')
                                ->toolbarButtons([
                                    'bold', 'italic', 'underline', 'strike', 'bulletList', 'orderedList',
                                    'blockquote', 'codeBlock', 'link', 'undo', 'redo',
                                ])
                                ->placeholder('Enter detailed product description...')
                                ->extraInputAttributes(['style' => 'min-height: 150px; max-height: 300px; overflow-y: auto;'])
                                ->columnSpanFull(),

                            Select::make('template_id')
                                ->relationship('template', 'name')
                                ->label('Template')
                                ->placeholder('Choose a design template...')
                                ->searchable()
                                ->preload()
                                ->nullable()
                                ->columnSpanFull(),
                        ])
                        ->columns(2)
                        ->columnSpan(['lg' => 2]),

                    // Phần công cụ - 1/3 chiều rộng
                    Section::make('Tools')
                        ->description('Import tools and options')
                        ->icon('heroicon-o-wrench-screwdriver')
                        ->schema([
                            Section::make('Import from URL')
                                ->collapsible()
                                ->schema([
                                    TextInput::make('source_url')
                                        ->label('Product URL')
                                        ->placeholder('https://...')
                                        ->url()
                                        ->helperText(new HtmlString('
                                        <div style="line-height: 1.4; font-size: 0.9em;">
                                            <div style="margin-bottom: 6px">
                                                <strong>✓ Supported platforms:</strong>
                                            </div>
                                            <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 6px;">
                                                <span class="inline-flex items-center justify-center min-w-6 px-2 py-0.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Amazon</span>
                                                <span class="inline-flex items-center justify-center min-w-6 px-2 py-0.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Etsy</span>
                                                <span class="inline-flex items-center justify-center min-w-6 px-2 py-0.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800">eBay</span>
                                                <span class="inline-flex items-center justify-center min-w-6 px-2 py-0.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800">TikTok Shop</span>
                                                <span class="inline-flex items-center justify-center min-w-6 px-2 py-0.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Shopify</span>
                                            </div>
                                        </div>
                                        ')),

                                    Forms\Components\Actions::make([
                                        Forms\Components\Actions\Action::make('Import')
                                            ->label('Import Data')
                                            ->color('primary')
                                            ->icon('heroicon-o-arrow-down-tray')
                                            ->action(function (Forms\Get $get, Forms\Set $set) {
                                                try {
                                                    $state = $get('source_url');
                                                    if (empty($state)) {
                                                        Notification::make()
                                                            ->title('Please enter a valid URL')
                                                            ->danger()
                                                            ->send();
                                                        return;
                                                    }
                                                    $set('title', '');
                                                    $set('description', '');
                                                    $set('images', []);
                                                    $set('source_type', '');
                                                    $set('external_id', '');
                                                    $set('source_data', []);
                                                    $set('metadata', []);

                                                    $importService = new ProductImportService();
                                                    $data = $importService->importFromUrl($state);

                                                    // Set basic fields
                                                    $set('title', $data['title'] ?? '');
                                                    $set('description', $data['description'] ?? '');
                                                    $set('images', $data['images'] ?? []);
                                                    $set('source_type', $data['source_type'] ?? '');
                                                    $set('external_id', $data['external_id'] ?? '');
                                                    $set('source_url', $data['url'] ?? $state);

                                                    // Set source_data và metadata với dữ liệu mặc định nếu trống
                                                    $sourceData = $data['source_data'] ?? [];
                                                    if (empty($sourceData)) {
                                                        $sourceData = [
                                                            'url' => $state,
                                                            'platform' => $data['source_type'] ?? 'unknown',
                                                            'scraped_at' => now()->toIso8601String()
                                                        ];
                                                    }

                                                    // Convert arrays to key-value pairs
                                                    $formattedSourceData = collect($sourceData)
                                                        ->map(function ($value) {
                                                            return is_array($value) ? json_encode($value) : (string) $value;
                                                        })
                                                        ->toArray();

                                                    $formattedMetadata = collect($data['metadata'] ?? [])
                                                        ->map(function ($value) {
                                                            return is_array($value) ? json_encode($value) : (string) $value;
                                                        })
                                                        ->toArray();

                                                    // Set the formatted data
                                                    $set('source_data', $formattedSourceData);
                                                    $set('metadata', $formattedMetadata);

                                                    // Tự động kiểm tra trademark
                                                    self::checkTrademark($get, $set);

                                                    Notification::make()
                                                        ->title('Data imported successfully')
                                                        ->success()
                                                        ->send();
                                                } catch (\Exception $e) {
                                                    Notification::make()->title('Error importing data: ' . $e->getMessage())->danger()->send();
                                                }
                                            }),
                                    ])->fullWidth(),
                                ]),

                            // Ẩn các trường không cần thiết
                            TextInput::make('source_type')
                                ->label('Product Source')
                                ->disabled()
                                ->dehydrated()
                                ->hidden(),

                            TextInput::make('external_id')
                                ->label('External ID')
                                ->disabled()
                                ->dehydrated()
                                ->hidden(),
                        ])
                        ->columnSpan(['lg' => 1]),
                ])
                ->columns(3),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                ImageColumn::make('images')
                    ->label('Image')
                    ->circular(false)
                    ->stacked()
                    ->limit(1)
                    ->defaultImageUrl(url('/img/placeholder.png')),
                
                // Combined title and source type
                Tables\Columns\TextColumn::make('title')
                    ->label('Product Title')
                    ->description(function ($record) {
                        $sourceType = ucfirst($record->source_type ?? 'Unknown');
                        $badge = match(strtolower($sourceType)) {
                            'amazon', 'tiktok' => '<span class="text-xs px-1.5 py-0.5 rounded bg-primary-100 text-primary-700">' . $sourceType . '</span>',
                            'etsy', 'shopify' => '<span class="text-xs px-1.5 py-0.5 rounded bg-success-100 text-success-700">' . $sourceType . '</span>',
                            'ebay' => '<span class="text-xs px-1.5 py-0.5 rounded bg-warning-100 text-warning-700">' . $sourceType . '</span>',
                            default => '<span class="text-xs px-1.5 py-0.5 rounded bg-gray-100 text-gray-700">' . $sourceType . '</span>',
                        };
                        return new HtmlString($badge);
                    })
                    ->searchable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    })
                    ->sortable(),
                
                // Product URL column
                Tables\Columns\TextColumn::make('source_url')
                    ->label('Product URL')
                    ->formatStateUsing(function ($state) {
                        if (empty($state)) return null;
                        
                        // Extract domain for display
                        $domain = parse_url($state, PHP_URL_HOST);
                        $domain = str_replace('www.', '', $domain);
                        
                        // Create a link that opens in a new tab
                        return new HtmlString(
                            '<a href="' . $state . '" target="_blank" class="inline-flex items-center gap-1 text-primary-600 hover:underline">
                                <span>' . $domain . '</span>
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                            </a>'
                        );
                    })
                    ->searchable(),
                
                // Combined user and created date
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Created By')
                    ->description(function ($record) {
                        return $record->created_at?->format('M d, Y');
                    })
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->color('gray'),
                
                Tables\Columns\TextColumn::make('trademark')
                    ->label('Trademark')
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        if (empty($state)) return 'Not checked';
                        if ($state === 'Không tìm thấy từ khóa Trademark') return 'Clear';
                        return 'Found: ' . $state;
                    })
                    ->colors([
                        'success' => fn ($state) => $state === 'Không tìm thấy từ khóa Trademark' || $state === 'Clear',
                        'danger' => fn ($state) => $state && $state !== 'Không tìm thấy từ khóa Trademark' && $state !== 'Clear' && $state !== 'Not checked',
                        'gray' => fn ($state) => empty($state) || $state === 'Not checked',
                    ]),
                
                Tables\Columns\TextColumn::make('store_note')
                    ->label('Uploaded to Store')
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        if (empty($state)) return 'None';
                        return $state;
                    })
                    ->colors([
                        'success' => fn ($state) => !empty($state),
                        'gray' => fn ($state) => empty($state),
                    ]),
                
                Tables\Columns\TextColumn::make('products')
                    ->label('Products')
                    ->alignCenter()
                    ->view('filament.tables.columns.product-status-list'),
            ])
            ->filters([
                DateRangeFilter::make('created_at')
                    ->label('Created Date'),
                
                Tables\Filters\SelectFilter::make('source_type')
                    ->label('Source Platform')
                    ->options([
                        'amazon' => 'Amazon',
                        'etsy' => 'Etsy',
                        'ebay' => 'eBay',
                        'tiktok' => 'TikTok Shop',
                        'shopify' => 'Shopify',
                        'manual' => 'Manual',
                    ])
                    ->multiple(),
                
                Tables\Filters\SelectFilter::make('trademark_status')
                    ->label('Trademark Status')
                    ->options([
                        'clear' => 'Clear (No trademark)',
                        'found' => 'Found (Has trademark)',
                        'not_checked' => 'Not checked',
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (!$data['value']) {
                            return $query;
                        }
                        
                        if (in_array('clear', $data['value'])) {
                            $query->where('trademark', 'Không tìm thấy từ khóa Trademark');
                        }
                        
                        if (in_array('found', $data['value'])) {
                            $query->where('trademark', '!=', 'Không tìm thấy từ khóa Trademark')
                                 ->whereNotNull('trademark');
                        }
                        
                        if (in_array('not_checked', $data['value'])) {
                            $query->whereNull('trademark');
                        }
                        
                        return $query;
                    }),
                
                Tables\Filters\Filter::make('has_store_note')
                    ->label('Store Upload Status')
                    ->form([
                        Forms\Components\Select::make('uploaded_status')
                            ->options([
                                'uploaded' => 'Uploaded to store',
                                'not_uploaded' => 'Not uploaded',
                            ])
                            ->placeholder('Select status')
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['uploaded_status'] === 'uploaded',
                                fn (Builder $query): Builder => $query->whereNotNull('store_note')->where('store_note', '!=', ''),
                            )
                            ->when(
                                $data['uploaded_status'] === 'not_uploaded',
                                fn (Builder $query): Builder => $query->where(function ($query) {
                                    $query->whereNull('store_note')->orWhere('store_note', '');
                                }),
                            );
                    }),
                
                Tables\Filters\SelectFilter::make('teams')
                    ->options(function () {
                        // Super Admin có thể xem tất cả teams trong hệ thống
                        if (auth()->user()->hasRole('super_admin')) {
                            return Team::pluck('name', 'id')->toArray();
                        }

                        // Người dùng khác chỉ thấy teams của họ
                        return auth()->user()->teams->pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data) {
                        if (!$data['value']) {
                            return $query;
                        }
                        
                        $teamId = $data['value'];
                        $teamUserIds = User::whereHas('teams', function($query) use ($teamId) {
                            $query->where('teams.id', $teamId);
                        })->pluck('id')->toArray();
                        
                        return $query->whereIn('user_id', $teamUserIds);
                    }),
                
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Created By')
                    ->searchable()
                    ->options(function () {
                        $user = auth()->user();
                        
                        // Super Admin thấy tất cả users
                        if ($user->hasRole('super_admin')) {
                            return User::whereHas('roles', function($q) {
                                $q->whereIn('name', ['Leader', 'Seller', 'super_admin']);
                            })->pluck('name', 'id')->toArray();
                        }
                        
                        // Leader thấy các seller được quản lý và chính họ
                        if ($user->hasRole('Leader')) {
                            $managedSellerIds = $user->leaderManagedSellers()->pluck('id')->toArray();
                            return User::whereIn('id', array_merge([$user->id], $managedSellerIds))
                                ->pluck('name', 'id')
                                ->toArray();
                        }
                        
                        // Các user thông thường thấy thành viên trong team và chính họ
                        $teamUserIds = User::whereHas('teams', function($query) use ($user) {
                            $query->whereIn('teams.id', $user->teams->pluck('id'));
                        })->pluck('id')->toArray();
                        
                        // Include current user
                        $allUserIds = array_unique(array_merge([$user->id], $teamUserIds));
                        
                        return User::whereIn('id', $allUserIds)
                            ->pluck('name', 'id')
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['value'],
                                fn (Builder $query, $userId): Builder => $query->where('user_id', $userId),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('upload_product')
                        ->label('Upload Product')
                        ->url(fn (ProductSource $record): string => ProductSourceResource::getUrl('upload-product', ['record' => $record]))
                        ->icon('heroicon-o-arrow-up-tray')
                        ->color('success'),
                    
                    Tables\Actions\EditAction::make(),
                    
                    Tables\Actions\DeleteAction::make(),
                ])
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    
                    Tables\Actions\BulkAction::make('upload_products')
                        ->label('Upload Products')
                        ->icon('heroicon-o-arrow-up-tray')
                        ->form([
                            Forms\Components\Select::make('template_id')
                                ->label('Template')
                                ->relationship('template', 'name')
                                ->searchable()
                                ->required(),
                                
                            Forms\Components\Select::make('store_id')
                                ->label('Store')
                                ->options(function () {
                                    $user = auth()->user();
                                    if ($user->hasRole('super_admin')) {
                                        return Store::all()->pluck('name', 'id');
                                    }
                                    
                                    return Store::where('owner_id', $user->id)->pluck('name', 'id');
                                })
                                ->required()
                                ->reactive()
                                ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                    $set('warehouse_id', null);
                                    $set('warehouses', self::loadWarehouses($state));
                                }),
                                
                            Forms\Components\Select::make('warehouse_id')
                                ->label('Warehouse')
                                ->options(fn ($get) => $get('warehouses') ?? [])
                                ->required()
                                ->afterStateHydrated(function ($state, Set $set, Get $get) {
                                    $warehouses = $get('warehouses') ?? [];
                                    if (!array_key_exists($state, $warehouses)) {
                                        $set('warehouse_id', null);
                                    }
                                }),
                                
                            Forms\Components\Select::make('save_mode')
                                ->label('Save Mode')
                                ->options([
                                    'AS_DRAFT' => 'Draft',
                                    'LISTING' => 'Published',
                                ])
                                ->default('LISTING')
                                ->required(),
                                
                            Forms\Components\DateTimePicker::make('scheduled_at')
                                ->label('Schedule Time')
                                ->default(now()),
                        ])
                        ->action(function (array $data, Collection $records) {
                            $records->each(function ($productSource) use ($data) {
                                $productSource->productToUploads()->create([
                                    'product_title' => $productSource->title,
                                    'description' => $productSource->description,
                                    'images' => $productSource->images,
                                    'template_id' => $data['template_id'],
                                    'store_id' => $data['store_id'],
                                    'warehouse_id' => $data['warehouse_id'],
                                    'save_mode' => $data['save_mode'],
                                    'scheduled_at' => $data['scheduled_at'],
                                ]);
                                
                                // Cập nhật store_note khi tạo sản phẩm mới
                                $store = Store::find($data['store_id']);
                                if ($store) {
                                    $currentNote = $productSource->store_note ?? '';
                                    $newNote = trim($currentNote . ', ' . $store->name, ', ');
                                    $productSource->update(['store_note' => $newNote]);
                                }
                            });

                            Notification::make()
                                ->success()
                                ->title('Product uploads created successfully')
                                ->send();
                        }),
                        
                    Tables\Actions\BulkAction::make('share_with_team')
                        ->label('Share with Team')
                        ->icon('heroicon-o-user-group')
                        ->form([
                            Forms\Components\Select::make('team_ids')
                                ->label('Select Teams')
                                ->options(fn () => Team::whereIn('id', auth()->user()->teams->pluck('id'))->pluck('name', 'id'))
                                ->multiple()
                                ->required(),
                        ])
                        ->action(function (array $data, Collection $records) {
                            foreach ($records as $record) {
                                $record->teams()->sync($data['team_ids']);
                            }
                            
                            Notification::make()
                                ->success()
                                ->title('Products shared with selected teams')
                                ->send();
                        }),
                ]),
            ]);
    }

    protected static function checkTrademark(Get $get, Set $set): void
    {
        if (!$get('title')) {
            Notification::make()
                ->title('Error')
                ->body('Please enter a product title')
                ->danger()
                ->send();
            return;
        }

        try {
            $title = $get('title');
            $trademarks = \App\Models\Trademark::all()->pluck('keyword')->toArray();
            $foundTrademarks = [];
            
            foreach ($trademarks as $trademark) {
                $pattern = '/\b' . preg_quote($trademark, '/') . '\b/i';
                
                if (preg_match($pattern, $title)) {
                    $foundTrademarks[] = $trademark;
                }
            }
            
            if (empty($foundTrademarks)) {
                $set('trademark', 'Không tìm thấy từ khóa Trademark');
                Notification::make()
                    ->title('No trademark found')
                    ->success()
                    ->send();
            } else {
                $set('trademark', implode(', ', $foundTrademarks));
                Notification::make()
                    ->title('Found ' . count($foundTrademarks) . ' trademark keywords')
                    ->warning()
                    ->body('Trademark: ' . implode(', ', $foundTrademarks))
                    ->send();
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('Error checking trademark: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public static function getRelations(): array
    {
        return [
            ProductToUploadsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductSources::route('/'),
            'create' => Pages\CreateProductSource::route('/create'),
            'edit' => Pages\EditProductSource::route('/{record}/edit'),
            'upload-product' => Pages\ManageProductToUploads::route('/{record}/upload-product'),
        ];
    }

    protected static function loadWarehouses($storeId)
    {
        $store = Store::find($storeId);
        $tiktokService = new TiktokShopService($store);

        try {
            $warehouses = $tiktokService->getWarehouseList();
            return collect($warehouses)
                ->filter(fn($warehouse) => $warehouse['type'] === 'SALES_WAREHOUSE')
                ->mapWithKeys(fn($warehouse) => [$warehouse['id'] => $warehouse['name']])
                ->toArray();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Unable to load warehouses: ' . $e->getMessage())
                ->danger()
                ->send();
            return [];
        }
    }
}
