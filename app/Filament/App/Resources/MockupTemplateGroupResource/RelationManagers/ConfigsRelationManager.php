<?php

namespace App\Filament\App\Resources\MockupTemplateGroupResource\RelationManagers;

use App\Filament\App\Resources\MockupTemplateConfigResource;
use App\Filament\Forms\Components\DesignPositionEditor;
use App\Models\MockupTemplateConfig;
use App\Models\MockupTemplateGroup;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\EditAction;
use Illuminate\Support\Facades\DB;

class ConfigsRelationManager extends RelationManager
{
    use InteractsWithForms;

    protected static string $relationship = 'configs';
    protected static ?string $title = 'Mockup Templates';
    protected static ?string $recordTitleAttribute = 'name';

    public function updateDesignPosition(array $data, $recordId): void
    {
        $record = $this->getRelationship()->getModel()::find($recordId);

        if (!$record) {
            Notification::make()
                ->danger()
                ->title('Record not found')
                ->send();
            return;
        }

        $record->update([
            'design_position_x' => $data['design_position_x'],
            'design_position_y' => $data['design_position_y'],
            'design_width' => $data['design_width'],
            'design_height' => $data['design_height'],
            'back_design_position_x' => $data['back_design_position_x'] ?? null,
            'back_design_position_y' => $data['back_design_position_y'] ?? null,
            'back_design_width' => $data['back_design_width'] ?? null,
            'back_design_height' => $data['back_design_height'] ?? null,
        ]);

        Notification::make()
            ->success()
            ->title('Design position updated successfully')
            ->send();
    }

    public function form(Form $form): Form
    {
        return MockupTemplateConfigResource::form($form);
    }

    public function table(Table $table): Table
    {
        return MockupTemplateConfigResource::table($table)
            ->actions([
                Tables\Actions\Action::make('duplicate')
                    ->button()
                    ->color('info')
                    ->icon('heroicon-m-square-2-stack')
                    ->label('Duplicate')
                    ->requiresConfirmation()
                    ->modalHeading('Duplicate Mockup Template')
                    ->modalDescription('Are you sure you want to duplicate this mockup template? A new copy will be created with draft status.')
                    ->modalSubmitActionLabel('Yes, duplicate it')
                    ->action(function (MockupTemplateConfig $record): void {
                        // Tạo bản sao
                        $duplicate = new MockupTemplateConfig();
                        $duplicate->name = $record->name . ' (Copy)';
                        $duplicate->user_id = auth()->id();
                        $duplicate->apparel_image = $record->apparel_image;
                        $duplicate->has_back_view = $record->has_back_view;
                        $duplicate->design_position_x = $record->design_position_x;
                        $duplicate->design_position_y = $record->design_position_y;
                        $duplicate->design_width = $record->design_width;
                        $duplicate->design_height = $record->design_height;
                        $duplicate->back_design_position_x = $record->back_design_position_x;
                        $duplicate->back_design_position_y = $record->back_design_position_y;
                        $duplicate->back_design_width = $record->back_design_width;
                        $duplicate->back_design_height = $record->back_design_height;
                        $duplicate->status = 'draft';
                        $duplicate->save();

                        // Copy groups
                        $groupIds = DB::table('mockup_template_mockup_template_group')
                            ->where('mockup_template_id', $record->id)
                            ->pluck('mockup_template_group_id')
                            ->toArray();

                        $accessibleGroupIds = MockupTemplateGroup::whereIn('id', $groupIds)
                            ->where(function ($query) {
                                $query->where('is_public', true)
                                    ->orWhere('user_id', auth()->id());
                            })
                            ->pluck('id')
                            ->toArray();

                        foreach ($accessibleGroupIds as $groupId) {
                            DB::table('mockup_template_mockup_template_group')->insert([
                                'mockup_template_id' => $duplicate->id,
                                'mockup_template_group_id' => $groupId,
                                'created_at' => now(),
                                'updated_at' => now()
                            ]);
                        }

                        Notification::make()
                            ->title('Mockup duplicated successfully')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\DeleteAction::make()
                    ->button()
                    ->color('danger')
                    ->icon('heroicon-m-trash')
                    ->requiresConfirmation()
                    ->modalHeading('Delete Mockup Template')
                    ->modalDescription('Are you sure you want to delete this mockup template? This action cannot be undone.')
                    ->modalSubmitActionLabel('Yes, delete it'),
                Tables\Actions\EditAction::make()
                    ->slideOver()
                    ->modalWidth('full')
                    ->button()
                    ->color('warning')
                    ->icon('heroicon-m-pencil-square')
                    ->mutateFormDataUsing(function (array $data): array {
                        return $data;
                    })
                    ->before(function ($record, EditAction $action) {
                        $this->dispatch('save-design-position', recordId: $record->id);
                    })
                    ->afterFormFilled(function () {
                        $this->dispatch('refresh-design-editor');
                    })
                    ->action(function (MockupTemplateConfig $record, array $data, Tables\Actions\EditAction $action): void {
                        $record->update($data);
                        $this->dispatch('refresh-design-editor');
                        Notification::make()
                            ->success()
                            ->title('Saved successfully')
                            ->send();
                        $action->halt();
                    })
                    ->closeModalByClickingAway(false)
                    ->modalCancelAction(false)
                    ->successNotification(null),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->slideOver()
                    ->modalWidth('full')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['user_id'] = auth()->id();
                        return $data;
                    })
                    ->after(function (MockupTemplateConfig $record) {
                        Notification::make()
                            ->success()
                            ->title('🎨 Đã Tạo Mockup Thành Công!')
                            ->body('Bạn có thể thiết lập vị trí của thiết kế trong trang chỉnh sửa.')
                            ->duration(5000)
                            ->send();
                    })
                    ->closeModalByClickingAway(false)
                    ->modalCancelAction(false)
                    ->successNotification(null),
                Tables\Actions\AttachAction::make(),
            ]);
    }

    public function saveDesignPosition(array $data): void
    {
        session()->put('temp_design_data', $data);
    }
}
