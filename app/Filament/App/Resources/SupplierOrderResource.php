<?php

namespace App\Filament\App\Resources;

use App\Enums\OrderStatus;
use App\Enums\SupplierOrderStatus;
use App\Filament\App\Resources\SupplierOrderResource\Pages;
use App\Models\Product;
use App\Models\SupplierOrder;
use App\Tables\Columns\OrderItemsColumn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\Summarizers\Count;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use App\Fullfillment\FlashShip;
use Filament\Notifications\Notification;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Exception;
use Illuminate\Support\Facades\Auth;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use App\Traits\HasUserFilter;
use App\Models\Team;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class SupplierOrderResource extends Resource
{
    use HasUserFilter;
    protected static ?string $model = SupplierOrder::class;

    

    public static function getNavigationGroup(): ?string
    {
        return __('Order & Supplier');
    }

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';

    public static function getNavigationSort(): ?int
    {
        return 2;
    }

    public static function getNavigationBadge(): ?string
    {
        return \Illuminate\Support\Facades\Cache::remember('supplier_orders_pending_count', 300, function () {
            return static::getModel()::where('status', 'Pending')->count();
        });
    }

    public static function getNavigationBadgeColor(): ?string
    {
        $count = \Illuminate\Support\Facades\Cache::remember('supplier_orders_total_count', 300, function () {
            return static::getModel()::count();
        });
        return $count > 20 ? 'warning' : 'primary';
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                // Load seller with only necessary fields
                'seller:id,name',
                // Load supplier with only necessary fields
                'supplier:id,name',
                // Load handler with only necessary fields
                'handler:id,name,email',
                // Load order with minimal fields needed for OrderItemsColumn
                'order:id,order_code,store_id,fulfillment_type,store_order_status',
                'order.store:id,name',
                // Load order items for OrderItemsColumn display
                'order.orderItems:id,order_id,quantity,price,status,image,name,link,sku,product_id',
                'order.orderItems.product:id,image,name',
                'order.orderItems.product.design:id,product_id,status',
            ]);
    }

    public static function infolist(Infolists\Infolist $infolist): Infolists\Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make()
                    ->schema([
                        Infolists\Components\ViewEntry::make('fulfillment_details')
                            ->view('filament.supplier-order-details')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Hidden searchable column for order codes
                Tables\Columns\TextColumn::make('order_code')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Order Code'),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(fn ($state) => $state?->value ?? $state)
                    ->badge()
                    ->color(fn ($state) => match ($state instanceof SupplierOrderStatus ? $state->value : $state) {
                        'Pending' => 'gray',
                        'InProducing' => 'warning',
                        'Completed' => 'success',
                        'Cancelled' => 'danger',
                        'Refunded' => 'danger',
                        default => 'gray',
                    }),
                OrderItemsColumn::make('order')->disabledClick(),
                Tables\Columns\TextColumn::make('base_cost')
                    ->summarize([
                        Sum::make()->money('USD')->label('Total Amount'),
                        Count::make()->label('Count'),
                    ])

                    ->sortable()
                    ->label('Cost'),
                Tables\Columns\TextColumn::make('seller.name')
                    ->description(fn (SupplierOrder $record): string => $record->order?->store?->name ?? 'N/A')
                    ->label('Seller')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('seller', function ($query) use ($search) {
                            $query->where('users.name', 'like', "%{$search}%");
                        })->orWhereHas('order.store', function ($query) use ($search) {
                            $query->where('stores.name', 'like', "%{$search}%");
                        });
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('supplier.name')
                    ->description(fn (SupplierOrder $record): string => $record->supplier_order_id ?? 'N/A')
                    ->label('Supplier')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('supplier', function ($query) use ($search) {
                            $query->where('suppliers.name', 'like', "%{$search}%");
                        })->orWhere('supplier_order_id', 'like', "%{$search}%");
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('fulfillment_type')
                    ->label('Fulfillment Type')
                    ->badge()
                    ->color(fn ($state) => match ($state) {
                        'auto' => 'success',
                        'manual' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn ($state) => match ($state) {
                        'auto' => 'Auto',
                        'manual' => 'Manual',
                        default => 'Unknown',
                    })
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('handler.name')
                    ->label('Handler')
                    ->description(fn (SupplierOrder $record): string => $record->handler?->email ?? 'N/A')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('handler', function ($query) use ($search) {
                            $query->where('users.name', 'like', "%{$search}%")
                                  ->orWhere('users.email', 'like', "%{$search}%");
                        });
                    })
                    ->sortable()
                    ->placeholder('No handler')
                    ->toggleable(),
              
                
                Tables\Columns\TextColumn::make('supplier_order_id')
                    ->label('Supplier Order ID')
                    ->searchable()
                    ->sortable()
                    ->placeholder('No supplier order ID')
                    ->copyable()
                    ->copyMessage('Supplier Order ID copied!')
                    ->copyMessageDuration(1500),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Thời gian tạo')
                    ->dateTime('d/m/Y H:i')
                    ->description(function ($record): string {
                        return Carbon::parse($record->created_at)->diffForHumans();
                    })
                   
                    ->sortable(),
                  

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật cuối')
                    ->dateTime('d/m/Y H:i')
                    ->description(function ($record): string {
                        return Carbon::parse($record->updated_at)->diffForHumans();
                    })
                  
                    ->sortable()
                  
            ])
            ->filters([
                Filter::make('order_code')
                    ->form([
                        Forms\Components\TextInput::make('order_code')
                            ->label('Order Code')
                            ->placeholder('Search order code...')
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['order_code'],
                            fn (Builder $query, $code): Builder => $query->whereHas('order', function ($query) use ($code) {
                                $query->where('order_code', 'like', "%{$code}%");
                            })
                        );
                    }),
                SelectFilter::make('status')
                    ->multiple()
                    ->options([
                        'AwaitingShipment' => 'Awaiting Shipment',
                        'Completed' => 'Completed',
                        'Cancelled' => 'Cancelled',
                    ])
                    ->placeholder('All Statuses')
                    ->label('Order Status'),
                static::getStoreFilter('store_id', 'Store', 'order.store')
                    ->multiple(),
                static::getUserFilter('seller_id', 'User'),

                SelectFilter::make('fulfillment_type')
                    ->label('Fulfillment Type')
                    ->options([
                        'auto' => 'Auto Fulfill',
                        'manual' => 'Manual Fulfill',
                    ])
                    ->placeholder('All Types'),

                SelectFilter::make('handler_id')
                    ->label('Handler')
                    ->relationship('handler', 'name')
                    ->searchable()
                    ->preload()
                    ->placeholder('All Handlers'),
                // SelectFilter::make('seller_id')
                //     ->relationship('seller', 'name')
                //     ->multiple()
                //     ->preload()
                //     ->label('Seller'),
                DateRangeFilter::make('created_at')

                    ->displayFormat('D/M/Y')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ]),

                // Bộ lọc theo Team - chỉ hiển thị cho Fullfillment Manager
                SelectFilter::make('team')
                    ->label('Team')
                    ->options(function () {
                        return Team::pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn (Builder $query, $teamId): Builder => $query->whereHas('seller.teams', function ($query) use ($teamId) {
                                $query->where('teams.id', $teamId);
                            })
                        );
                    })
                    ->visible(fn (): bool => Auth::check() && Auth::user()->hasAnyRole(['Fullfillment Manager', 'User Manager', 'super_admin']))
                    ->searchable()
                    ->preload(),

                // Bộ lọc theo Design Status - hiển thị tất cả status
                SelectFilter::make('design_status')
                    ->label('Design Status')
                    ->options([
                        'Uploaded' => 'Uploaded',
                        'Design' => 'Design',
                        'Archived' => 'Archived',
                        'Clone' => 'Clone',
                        'Spy' => 'Spy',
                        'Cancelled' => 'Cancelled',
                        'no_design' => 'No Design',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            function (Builder $query, $status): Builder {
                                if ($status === 'no_design') {
                                    // Không có design
                                    return $query->whereDoesntHave('order.orderItems.product.design');
                                } else {
                                    // Có design với status cụ thể
                                    return $query->whereHas('order.orderItems.product.design', function ($query) use ($status) {
                                        $query->where('status', $status);
                                    });
                                }
                            }
                        );
                    })
                    ->visible(fn (): bool => Auth::check() && Auth::user()->hasAnyRole(['Fullfillment Manager', 'User Manager', 'super_admin']))
                    ->searchable()
                    ->preload(),

            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->button()
                    ->url(fn (SupplierOrder $record): string => static::getUrl('view', ['record' => $record])),
                Tables\Actions\Action::make('sync_status')
                ->icon('heroicon-m-arrow-path')
                ->color('warning')
                ->button()
                ->action(function (SupplierOrder $record) {
                    try {
                        $record->updateStatus();

                        Notification::make()
                            ->success()
                            ->title('Status synced successfully')
                            ->send();
                    } catch (\Exception $e) {
                        Log::error('Failed to sync status', [
                            'supplier_order_id' => $record->id,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        Notification::make()
                            ->danger()
                            ->title('Failed to sync status')
                            ->body($e->getMessage())
                            ->send();
                    }
                }),

            Tables\Actions\Action::make('cancel_order')
                ->icon('heroicon-m-x-mark')
                ->color('danger')
                ->button()
                ->visible(function (SupplierOrder $record) {
                    return static::canCancelOrder($record);
                })
                ->requiresConfirmation()
                ->modalHeading('Cancel Order')
                ->modalDescription(function (SupplierOrder $record) {
                    return "Are you sure you want to cancel order {$record->supplier_order_id} from {$record->supplier->name}?";
                })
                ->action(function (SupplierOrder $record) {
                    try {
                        $response = null;
                        $supplierName = $record->supplier->name ?? 'Unknown';

                        // Handle different suppliers
                        switch ($supplierName) {
                            case 'Flashship':
                                $flashship = new FlashShip();
                                $response = $flashship->cancelOrder($record->supplier_order_id);

                                // Logging
                                Log::channel('flashship_cancel_orders')->info('Cancel order', [
                                    'supplier_order_id_db' => $record->id,
                                    'order_code' => $record->order_code,
                                    'supplier_order_id' => $record->supplier_order_id,
                                    'response' => $response
                                ]);


                                break;

                            case 'Pressify':
                                $response = \App\Fullfillment\Pressify::cancelOrderSafe($record->supplier_order_id);
                                break;

                            default:
                                throw new \Exception("Supplier '{$supplierName}' is not supported for cancellation");
                        }

                        if ($response && $response['success']) {
                            // Update status from supplier API
                            $record->updateStatus();

                            // Build success message based on supplier
                            $message = static::buildCancelSuccessMessage($response, $supplierName);

                            Notification::make()
                                ->success()
                                ->title('Order cancelled successfully')
                                ->body($message)
                                ->send();
                        } else {
                            $errorMessage = $response['message'] ?? 'Unknown error occurred';

                            Notification::make()
                                ->danger()
                                ->title('Failed to cancel order')
                                ->body("Supplier: {$supplierName} - {$errorMessage}")
                                ->send();
                        }

                    } catch (\Exception $e) {
                        Notification::make()
                            ->danger()
                            ->title('Failed to cancel order')
                            ->body("Error: {$e->getMessage()}")
                            ->send();
                    }
                }),
            ])
            ->defaultSort('created_at', 'desc')
            ->persistSortInSession()
            ->persistSearchInSession()
            ->persistFiltersInSession();
    }

    /**
     * Build success message for cancel operation based on supplier response
     */
    private static function buildCancelSuccessMessage(array $response, string $supplierName): string
    {
        $message = "Supplier: {$supplierName}";

        switch ($supplierName) {
            case 'Pressify':
                if (isset($response['operation_type'])) {
                    $operationType = ucfirst($response['operation_type']);
                    $message .= " - {$operationType} operation completed";
                }

                if (isset($response['order_codes']) && !empty($response['order_codes'])) {
                    $codes = implode(', ', $response['order_codes']);
                    $message .= " (Order codes: {$codes})";
                }

                if (isset($response['reject_note']) && !empty($response['reject_note'])) {
                    $message .= " - Reason: {$response['reject_note']}";
                }

                if (isset($response['processed_count'])) {
                    $message .= " - Processed: {$response['processed_count']} order(s)";
                }
                break;

            case 'Flashship':
                // FlashShip specific message formatting
                if (isset($response['message'])) {
                    $message .= " - {$response['message']}";
                } else {
                    $message .= " - Order cancelled successfully";
                }
                break;

            default:
                $message .= " - Order cancelled successfully";
                break;
        }

        return $message;
    }

    /**
     * Check if order can be cancelled based on supplier and status
     */
    private static function canCancelOrder(SupplierOrder $record): bool
    {
        $supplierName = $record->supplier->name ?? '';

        // Check if supplier supports cancellation
        if (!in_array($supplierName, ['Flashship', 'Pressify'])) {
            return false;
        }

        // Check if order is already cancelled or completed
        if (in_array($record->status, [
            \App\Enums\SupplierOrderStatus::Cancelled,
            \App\Enums\SupplierOrderStatus::Completed,
            \App\Enums\SupplierOrderStatus::Refunded
        ])) {
            return false;
        }

        // Supplier-specific checks
        switch ($supplierName) {
            case 'Pressify':
                // For Pressify, we could check the actual order status from API
                // but for performance, we'll use local status as indicator
                return in_array($record->status, [
                    \App\Enums\SupplierOrderStatus::AwaitingShipment,
                    \App\Enums\SupplierOrderStatus::OnHold
                ]);

            case 'Flashship':
                // FlashShip allows cancellation in more states
                return in_array($record->status, [
                    \App\Enums\SupplierOrderStatus::AwaitingShipment,
                    \App\Enums\SupplierOrderStatus::OnHold,
                    \App\Enums\SupplierOrderStatus::InProducing
                ]);

            default:
                return false;
        }
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Order Details')
                            ->relationship('order')
                            ->schema([
                                Forms\Components\ToggleButtons::make('status')
                                    ->label('Update Order Status')
                                    ->options(OrderStatus::class)
                                    ->inline(),
                                Forms\Components\Repeater::make('orderItems')
                                    ->relationship('orderItems')
                                    ->label('')
                                    ->schema([
                                        Forms\Components\TextInput::make('sku')
                                            ->label('Item SKU')
                                            ->disabled(),
                                        Forms\Components\TextInput::make('price')
                                            ->disabled(),
                                        Forms\Components\Fieldset::make('Product')
                                            ->relationship('product')
                                            ->schema([
                                                Forms\Components\TextInput::make('name')
                                                    ->disabled(),
                                            ]),
                                    ])
                                    ->columns(3),
                            ]),
                        Forms\Components\Section::make('Supplier Order Details')
                            ->schema([
                                Forms\Components\ToggleButtons::make('status')
                                    ->options(SupplierOrderStatus::class)
                                    ->inline(),
                                Forms\Components\Select::make('supplier_id')
                                    ->relationship('supplier', 'name')
                                    ->searchable()
                                    ->preload(),
                                Forms\Components\Repeater::make('supplierOrderItems')
                                    ->relationship('supplierOrderItems')
                                    ->label('')
                                    ->schema([
                                        Forms\Components\Select::make('product_id')
                                            ->label('Product')
                                            ->relationship('product', 'name')
                                            ->searchable()
                                            ->preload()
                                            ->getSearchResultsUsing(fn (string $search): array =>
                                                Product::where('name', 'like', "%{$search}%")
                                                    ->limit(50)
                                                    ->pluck('name', 'id')
                                                    ->toArray()
                                            ),
                                        Forms\Components\TextInput::make('quantity')
                                            ->numeric()
                                            ->default(1)
                                            ->minValue(1),
                                        Forms\Components\TextInput::make('price')
                                            ->numeric()
                                            ->prefix('$'),
                                    ])
                                    ->orderColumn()
                                    ->defaultItems(1)
                                    ->columns(3)
                                    ->collapsible(),
                            ]),
                    ])
                    ->columnSpan(['lg' => fn (?SupplierOrder $record) => $record === null ? 3 : 2]),
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Actions')
                            ->schema([
                                Forms\Components\Actions::make([
                                    Forms\Components\Actions\Action::make('flashship')
                                        ->label('Create FlashShip Order')
                                        ->icon('heroicon-o-truck')
                                        ->action(function (SupplierOrder $record) {
                                            try {
                                                $flashship = new FlashShip();
                                                $flashship->createOrder($record);
                                                Notification::make()
                                                    ->title('Task Executed Successfully')
                                                    ->success()
                                                    ->send();
                                            } catch (Exception $e) {
                                                Log::error($e->getMessage());
                                                Notification::make()
                                                    ->title('Task Execution Failed')
                                                    ->body($e->getMessage())
                                                    ->danger()
                                                    ->send();
                                            }
                                        })
                                        ->visible(fn (?SupplierOrder $record): bool => $record !== null),
                                ]),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?SupplierOrder $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => SupplierOrderResource\Pages\ListSupplierOrders::route('/'),
            'view' => SupplierOrderResource\Pages\ViewSupplierOrder::route('/{record}'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [
            'order_code',
            'supplier_order_id',
            'tracking_number',
        ];
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()->with(['seller', 'supplier', 'order']);
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        /** @var SupplierOrder $record */
        return [
            'Order Code' => $record->order?->order_code ?? 'N/A',
            'Supplier Order ID' => $record->supplier_order_id ?? 'N/A',
            'Tracking Number' => $record->tracking_number ?? 'N/A',
            'Seller' => $record->seller?->name ?? 'N/A',
            'Supplier' => $record->supplier?->name ?? 'N/A',
        ];
    }
}
