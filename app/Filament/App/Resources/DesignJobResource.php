<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\DesignJobResource\Pages;
use App\Models\DesignJob;
use App\Enums\DesignJobStatus;
use App\Enums\DesignJobType;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ViewColumn;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;
use Illuminate\Support\Facades\Storage;
use App\Services\LarkService;
use Filament\Notifications\Notification;

class DesignJobResource extends Resource
{
    protected static ?string $model = DesignJob::class;

    /**
     * Kiểm tra xem designer đã được gửi thông báo cho job này trong 1 tiếng qua chưa
     */
    private static function canSendNotification(int $jobId, int $designerId): bool
    {
        $cacheKey = "lark_notification_job_{$jobId}_designer_{$designerId}";
        return !Cache::has($cacheKey);
    }

    /**
     * Đánh dấu đã gửi thông báo cho designer về job này
     */
    private static function markNotificationSent(int $jobId, int $designerId): void
    {
        $cacheKey = "lark_notification_job_{$jobId}_designer_{$designerId}";
        Cache::put($cacheKey, true, now()->addHour()); // Cache 1 tiếng
    }
    protected static ?string $navigationIcon = 'heroicon-o-photo';          // Icon hình ảnh
    protected static ?string $navigationGroup = 'Design & Media';

    protected static ?string $modelLabel = 'Design Job';
    protected static ?string $navigationLabel = 'Design Jobs';
    protected static ?string $slug = 'design-jobs';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
         
                Grid::make(2)
                    ->schema([
                        // Cột trái
                        Section::make('Basic Information')
                            ->columnSpan(1)
                            ->schema([
                                // Status Toggle
                                Forms\Components\TextInput::make('title')
                                    //->required()
                                    ->columnSpanFull(),
                                    
                                Forms\Components\ToggleButtons::make('status')
                                    ->inline()
                                    ->dehydrated()
                                    ->required()
                                    ->default(DesignJobStatus::PENDING->value)
                                    ->options(function (?DesignJob $record) {
                                        $user = Auth::user();
                                        $currentStatus = $record?->status;

                                        // Nếu là form tạo mới hoặc chưa có status
                                        if (!$record || !$currentStatus) {
                                            return [DesignJobStatus::PENDING->value => 'Pending'];
                                        }

                                        // Lấy roles từ database
                                        $userRoles = $user->getRoleNames()->toArray(); // Sử dụng getRoleNames() từ Spatie

                                        // Debug để kiểm tra
                                        // dd($userRoles);

                                        $availableStatuses = $currentStatus->canTransitionTo($userRoles);

                                        return collect($availableStatuses)
                                            ->mapWithKeys(fn($status) => [
                                                $status->value => $status->getLabel()
                                            ])
                                            ->toArray();
                                    })

                                    ->inline()
                                    ->icons([
                                        'pending' => 'heroicon-o-clock',
                                        'assigned' => 'heroicon-o-user-plus',
                                        'in_progress' => 'heroicon-o-arrow-path',
                                        'under_review' => 'heroicon-o-eye',
                                        'needs_revision' => 'heroicon-o-pencil',
                                        'completed' => 'heroicon-o-check-circle',
                                        'cancelled' => 'heroicon-o-x-circle',
                                    ]),

                                // Designer Selection (Only for Seller)
                                Forms\Components\Radio::make('designer_id')
                                    ->label('Designer')
                                    ->options(function () {
                                        return User::role('Designer')
                                            ->withCount(['designJobs' => function ($query) {
                                                $query->whereIn('status', [
                                                    DesignJobStatus::PENDING,
                                                    DesignJobStatus::IN_PROGRESS,
                                                    DesignJobStatus::ASSIGNED
                                                ]);
                                            }])
                                            ->orderBy('design_jobs_count')
                                            ->get()
                                            ->mapWithKeys(function ($designer) {
                                                $statusColor = match (true) {
                                                    $designer->design_jobs_count > 10 => 'text-danger-600',
                                                    $designer->design_jobs_count > 5 => 'text-warning-600',
                                                    default => 'text-success-600',
                                                };

                                                return [
                                                    $designer->id => new HtmlString("
                                                        <div class='flex items-center gap-2'>
                                                            <span>{$designer->name}</span>
                                                            <span class='{$statusColor}'>
                                                                ({$designer->design_jobs_count} pending jobs)
                                                            </span>
                                                        </div>
                                                    ")
                                                ];
                                            });
                                    })
                                    ->visible(fn() => Auth::user()->hasRole(['super_admin','Fullfillment Manager']))
                                    ->columns(2),

                                ToggleButtons::make('job_type')
                                    ->label('Job Type')
                                    ->options(array_combine(
                                        array_column(DesignJobType::cases(), 'value'),
                                        array_map(fn($case) => $case->getLabel(), DesignJobType::cases())
                                    ))
                                    ->colors([
                                        DesignJobType::ORIGINAL->value => 'primary',
                                        DesignJobType::CLONE->value => 'success',
                                        DesignJobType::EDIT->value => 'warning',
                                        DesignJobType::BACKGROUND_REMOVE->value => 'danger',
                                        DesignJobType::MOCKUP->value => 'info',
                                    ])
                                    ->icons([
                                        DesignJobType::ORIGINAL->value => 'heroicon-m-sparkles',
                                        DesignJobType::CLONE->value => 'heroicon-m-document-duplicate',
                                        DesignJobType::EDIT->value => 'heroicon-m-pencil',
                                        DesignJobType::BACKGROUND_REMOVE->value => 'heroicon-m-scissors',
                                        DesignJobType::MOCKUP->value => 'heroicon-m-square-3-stack-3d',
                                    ])
                                    ->required()
                                    ->inline(),




                            ]),

                        // Cột phải
                        Section::make('Pricing')
                            ->columnSpan(1)
                            ->schema([
                                Select::make('created_by')
                               
                                    ->label('Seller')
                                    ->options(function () {
                                        return User::role('Seller')->get()->mapWithKeys(fn($seller) => [$seller->id => $seller->name]);
                                    })
                                    ->searchable()
                                    ->visible(fn() => Auth::user()->hasRole([ 'super_admin','Fullfillment Manager']))
                                    ->required(),
                                Forms\Components\TextInput::make('price')
                                    ->numeric()
                                    ->prefix('$')
                                    ->default(0)
                                    ->required(fn() => Auth::user()->hasRole(['super_admin', 'Fullfillment Manager','Fullfillment' , 'Designer']))
                                    ->minValue(0)
                                    ->prefixIcon('heroicon-m-currency-dollar')
                                    ->visible(fn() => Auth::user()->hasRole(['super_admin',
                                    'Fullfillment Manager','Fullfillment' , 'Designer']))
                                    ->dehydrated(), // Đảm bảo giá trị được lưu ngay cả khi không visible

                                Forms\Components\Toggle::make('is_rush')
                                    ->label('Rush Order')
                                    ->onIcon('heroicon-m-bolt')
                                    ->offIcon('heroicon-m-clock')
                                    ->onColor('danger')
                                    ->inline(),


                                Forms\Components\TextInput::make('rush_fee')
                                    ->numeric()
                                    ->prefix('$')
                                    ->default(0)
                                    ->minValue(0)
                                    ->visible(fn(Forms\Get $get) => $get('is_rush'))
                                    ->prefixIcon('heroicon-m-currency-dollar'),

                                FileUpload::make('final_files')
                                    ->label('Final Design Files')
                                    ->multiple()
                                    ->reorderable()
                                    ->downloadable()
                                    ->disk('s3')
                                    ->directory('design-jobs/finals'),
                            ]),
                    ]),
                // Forms\Components\DateTimePicker::make('deadline')

                // ->native(false)
                // ->icon('heroicon-m-calendar'),

                TinyEditor::make('description')
                    ->label('Description')
                    ->profile('simple2')
                    ->placeholder('Enter job description')
                    ->fileAttachmentsDisk('s3')
                    ->fileAttachmentsDirectory('design-jobs/descriptions')

                    ->columnSpanFull(),
            ]);
    }
    public static function table(Table $table): Table
    {
        $user = Auth::user();
        $totalJobs = DesignJob::withoutGlobalScopes()->count();
        $visibleJobs = DesignJob::count();

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('Title')
                    ->limit(20)
                    ->searchable(),

                Tables\Columns\TextColumn::make('designer.name')
                    ->label('Designer')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ViewColumn::make('description')
                    ->label('Description')
                    ->view('filament.tables.columns.description-with-images')
                    ->searchable(),

                Tables\Columns\TextColumn::make('creator.name')  // Thêm người tạo
                    ->label('Created By')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('price')
                    ->money('USD')
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->money('USD')
                    ]),
                
                    

                Tables\Columns\IconColumn::make('is_rush')
                    ->boolean()
                    ->label('Rush'),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(
                        fn($state) =>
                        $state instanceof DesignJobStatus
                            ? $state->getLabel()
                            : DesignJobStatus::from($state)->getLabel()
                    )
                    ->color(
                        fn($state) =>
                        $state instanceof DesignJobStatus
                            ? $state->getColor()
                            : DesignJobStatus::from($state)->getColor()
                    ),

                    Tables\Columns\ImageColumn::make('final_files')
                    
                    ->getStateUsing(function ($record) {
                        if (!$record->final_files || empty($record->final_files)) {
                            return null;
                        }
                        return array_map(function($url) {
                            $url = Storage::disk('s3')->url($url);
                            // Chỉ sử dụng photon_url nếu URL chứa 'kmediaz'
                            return str_contains($url, 'kmediaz') 
                                ? \App\Models\DesignFile::photon_url($url, 30)
                                : $url;
                        }, $record->final_files);
                    })
                    ->width('30')
                    ->height('30')
                    ->square()
                    ->limit(3),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Time')
                    ->dateTime('d/m/Y H:i')
                    ->description(function ($record): string {
                        return Carbon::parse($record->created_at)->diffForHumans();
                    })
                    ->color(function ($record): string {
                        // Chỉ áp dụng màu cảnh báo cho status pending
                        if ($record->status !== DesignJobStatus::PENDING) {
                            return 'gray'; // Màu bình thường cho các status khác
                        }

                        $daysDiff = (int) Carbon::parse($record->created_at)->diffInDays(now());
                        return match (true) {
                            $daysDiff >= 2 => 'danger',    // Trên 2 ngày - đỏ
                            $daysDiff >= 1 => 'warning',   // Trên 1 ngày - vàng
                            default => 'gray',             // Dưới 1 ngày - xám
                        };
                    })
                    ->sortable()
                    ->tooltip(function ($record): string {
                        $timeInfo = Carbon::parse($record->created_at)->format('d/m/Y H:i:s');

                        // Chỉ hiển thị cảnh báo cho status pending
                        if ($record->status !== DesignJobStatus::PENDING) {
                            return $timeInfo;
                        }

                        $daysDiff = (int) Carbon::parse($record->created_at)->diffInDays(now());

                        if ($daysDiff >= 2) {
                            return $timeInfo . ' - Cảnh báo: Job pending đã hơn 2 ngày!';
                        } elseif ($daysDiff >= 1) {
                            return $timeInfo . ' - Chú ý: Job pending đã hơn 1 ngày';
                        }

                        return $timeInfo;
                    }),

                Tables\Columns\ViewColumn::make('overdue_alert')
                    ->label('⚠️ Cảnh báo')
                    ->view('filament.tables.columns.overdue-alert-column')
                    ->alignCenter(),
            ])
            ->defaultSort('created_at', 'desc')
            ->persistSortInSession()
            ->persistFiltersInSession()
            ->filters([

                // Bộ lọc trạng thái xử lý
                TernaryFilter::make('processing_status')
                    ->label('Trạng thái xử lý')
                    ->placeholder('Tất cả job')
                    ->trueLabel('Chưa xử lý')
                    ->falseLabel('Đã xử lý')
                    ->queries(
                        true: fn (Builder $query) => $query->where('status', DesignJobStatus::PENDING),
                        false: fn (Builder $query) => $query->where('status', '!=', DesignJobStatus::PENDING),
                        blank: fn (Builder $query) => $query,
                    )
                    ->indicator('Xử lý'),

                // Bộ lọc theo thời gian chưa xử lý
                SelectFilter::make('pending_days')
                    ->label('Job chưa xử lý')
                    ->options([
                        '1' => 'Trên 1 ngày',
                        '2' => 'Trên 2 ngày (Khẩn cấp)',
                        '3' => 'Trên 3 ngày (Rất khẩn cấp)',
                        '7' => 'Trên 1 tuần',
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (!isset($data['value']) || $data['value'] === null) {
                            return $query;
                        }

                        $days = (int) $data['value'];
                        $cutoffDate = now()->subDays($days);

                        return $query->where('status', DesignJobStatus::PENDING)
                            ->where('created_at', '<=', $cutoffDate);
                    })
                    ->indicator('Chưa xử lý'),

                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options(array_combine(
                        array_column(DesignJobStatus::cases(), 'value'),
                        array_map(fn($case) => $case->getLabel(), DesignJobStatus::cases())
                    ))
                    ->indicator('Status'),

                SelectFilter::make('job_type')
                    ->options(array_combine(
                        array_column(DesignJobType::cases(), 'value'),
                        array_map(fn($case) => $case->getLabel(), DesignJobType::cases())
                    ))
                    ->indicator('Job Type'),

                    

                SelectFilter::make('designer')
                    ->relationship('designer', 'name')
                    ->indicator('Designer'),

                SelectFilter::make('created_by')  // Thêm filter người tạo
                    ->relationship('creator', 'name')
                    ->label('Created By')
                    ->searchable()
                    ->preload() 
                    ->indicator('Creator'),

                DateRangeFilter::make('created_at'),

                Filter::make('is_rush')
                    ->toggle()
                    ->label('Rush Orders Only')
                    ->indicator('Rush Orders'),

                Filter::make('overdue')
                    ->toggle()
                    ->label('Overdue Jobs')
                    ->query(
                        fn(Builder $query): Builder =>
                        $query->where('deadline', '<', now())
                            ->whereNotIn('status', [
                                DesignJobStatus::COMPLETED->value,
                                DesignJobStatus::CANCELLED->value
                            ])
                    )
                    ->indicator('Overdue'),
            ])
            ->headerActions([
                Tables\Actions\Action::make('show_oldest_pending')
                    ->label('Job lâu nhất chưa xử lý')
                    ->icon('heroicon-o-clock')
                    ->color('warning')
                    ->url(fn (): string => static::getUrl('index', [
                        'tableFilters' => [
                            'processing_status' => ['value' => true],
                        ],
                        'tableSortColumn' => 'created_at',
                        'tableSortDirection' => 'asc',
                    ]))
                    ->tooltip('Hiển thị các job pending được sắp xếp từ lâu nhất'),

                Tables\Actions\Action::make('show_urgent_jobs')
                    ->label('Job khẩn cấp')
                    ->icon('heroicon-o-exclamation-triangle')
                    ->color('danger')
                    ->url(fn (): string => static::getUrl('index', [
                        'tableFilters' => [
                            'pending_days' => ['value' => '2'],
                        ],
                        'tableSortColumn' => 'created_at',
                        'tableSortDirection' => 'asc',
                    ]))
                    ->tooltip('Hiển thị các job pending trên 2 ngày'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('complete')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->visible(fn($record) => $record->status !== DesignJobStatus::COMPLETED)
                    ->action(fn($record) => $record->markAsCompleted()),


            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('assignDesigner')
                        ->icon('heroicon-o-user-plus')
                        ->form([
                            Forms\Components\Select::make('designer_id')
                                ->label('Select Designer')
                                ->options(function () {
                                    return User::role('Designer')
                                        ->withCount(['designJobs' => function ($query) {
                                            $query->whereIn('status', [
                                                DesignJobStatus::PENDING,
                                                DesignJobStatus::IN_PROGRESS,
                                                DesignJobStatus::ASSIGNED
                                            ]);
                                        }])
                                        ->get()
                                        ->mapWithKeys(fn($designer) => [
                                            $designer->id => "{$designer->name} ({$designer->design_jobs_count} pending jobs)"
                                        ]);
                                })
                                ->required(),
                        ])
                        ->action(function (Collection $records, array $data): void {
                            $records->each(function ($record) use ($data) {
                                $record->update([
                                    'designer_id' => $data['designer_id'],
                                    'status' => DesignJobStatus::ASSIGNED,
                                ]);
                            });
                        }),

                    Tables\Actions\BulkAction::make('sendLarkNotifications')
                        ->label('Gửi thông báo cho Designer hàng loạt')
                        ->icon('heroicon-o-bell')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalHeading('Gửi thông báo cho Designer về các job quá hạn')
                        ->modalDescription('Bạn có muốn gửi thông báo nhắc nhở đến tất cả designer của các job đã chọn không?')
                        ->modalSubmitActionLabel('Gửi thông báo cho Designer')
                        ->deselectRecordsAfterCompletion()
                        ->action(function (Collection $records): void {
                            $larkService = app(LarkService::class);
                            $sender = Auth::user();
                            $successCount = 0;
                            $failedCount = 0;
                            $skippedCount = 0;

                            foreach ($records as $record) {
                                // Kiểm tra điều kiện
                                $daysDiff = (int) Carbon::parse($record->created_at)->diffInDays(now());
                                if ($daysDiff < 2 || !$record->designer_id || $record->status === DesignJobStatus::COMPLETED) {
                                    $skippedCount++;
                                    continue;
                                }

                                $designer = $record->designer;
                                if (!$designer || !$designer->hasLarkBotConfigured()) {
                                    $skippedCount++;
                                    continue;
                                }

                                // Kiểm tra cache để tránh spam
                                if (!self::canSendNotification($record->id, $designer->id)) {
                                    $skippedCount++;
                                    continue;
                                }

                                $message = "🔔 THÔNG BÁO JOB QUÁ HẠN\n\n";
                                $message .= "📋 Job: {$record->title}\n";
                                $message .= "⏰ Đã quá hạn: {$daysDiff} ngày\n";
                                $message .= "📅 Ngày tạo: " . Carbon::parse($record->created_at)->format('d/m/Y H:i') . "\n";
                                $message .= "💰 Giá: $" . number_format($record->price, 2) . "\n";
                                $message .= "🏷️ Loại: " . $record->job_type->getLabel() . "\n";
                                $message .= "📝 Trạng thái: " . $record->status->getLabel() . "\n\n";
                                $message .= "Vui lòng xử lý job này sớm nhất có thể!";

                                try {
                                    $larkSend = $larkService->sendMessage($sender, $designer, $message);

                                    if ($larkSend->isSent()) {
                                        // Đánh dấu đã gửi thông báo
                                        self::markNotificationSent($record->id, $designer->id);
                                        $successCount++;
                                    } else {
                                        $failedCount++;
                                    }
                                } catch (\Exception $e) {
                                    $failedCount++;
                                }
                            }

                            $message = "Kết quả gửi thông báo:\n";
                            $message .= "✅ Thành công: {$successCount}\n";
                            if ($failedCount > 0) {
                                $message .= "❌ Thất bại: {$failedCount}\n";
                            }
                            if ($skippedCount > 0) {
                                $message .= "⏭️ Bỏ qua: {$skippedCount} (không đủ điều kiện hoặc đã gửi trong 1h qua)";
                            }

                            Notification::make()
                                ->title('Hoàn thành gửi thông báo cho Designer')
                                ->body($message)
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDesignJobs::route('/'),
            'create' => Pages\CreateDesignJob::route('/create'),
            'edit' => Pages\EditDesignJob::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', DesignJobStatus::PENDING)->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return static::getModel()::where('status', DesignJobStatus::PENDING)->count() > 0
            ? 'warning'
            : 'primary';
    }
}
