<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\TiktokChannelResource\Pages;
use App\Filament\App\Resources\TiktokChannelResource\RelationManagers;
use App\Models\TiktokChannel;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TiktokChannelResource extends Resource
{
    protected static ?string $model = TiktokChannel::class;

    protected static ?string $navigationIcon = 'heroicon-o-video-camera';
    
    protected static ?string $navigationLabel = 'TikTok Channels';
    
    protected static ?string $modelLabel = 'TikTok Channel';
    
    protected static ?string $pluralModelLabel = 'TikTok Channels';
    
    protected static ?string $navigationGroup = 'TikTok';
    
    
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user && ($user->hasRole('super_admin') || $user->hasRole('User Manager'));
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('User')
                    ->options(User::pluck('name', 'id'))
                    ->required()
                    ->searchable(),
                
                Forms\Components\TextInput::make('username')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255),
                
                Forms\Components\TextInput::make('display_name')
                    ->maxLength(255),
                
                Forms\Components\Textarea::make('description')
                    ->rows(3),
                
                Forms\Components\TextInput::make('avatar_url')
                    ->url()
                    ->maxLength(255),
                
                Forms\Components\TextInput::make('follower_count')
                    ->numeric()
                    ->default(0),
                
                Forms\Components\TextInput::make('following_count')
                    ->numeric()
                    ->default(0),
                
                Forms\Components\TextInput::make('video_count')
                    ->numeric()
                    ->default(0),
                
                Forms\Components\TextInput::make('likes_count')
                    ->numeric()
                    ->default(0),
                
                Forms\Components\Toggle::make('is_verified')
                    ->default(false),
                
                Forms\Components\Toggle::make('is_active')
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->sortable()
                    ->searchable(),
                
                Tables\Columns\TextColumn::make('username')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('display_name')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('follower_count')
                    ->numeric()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('video_count')
                    ->numeric()
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_verified')
                    ->boolean(),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('last_synced_at')
                    ->dateTime()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('videos_count')
                    ->counts('videos')
                    ->label('Videos')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('User')
                    ->options(User::pluck('name', 'id')),
                
                Tables\Filters\TernaryFilter::make('is_verified'),
                
                Tables\Filters\TernaryFilter::make('is_active'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('view_videos')
                    ->label('View Videos')
                    ->icon('heroicon-o-play')
                    ->modalContent(fn (TiktokChannel $record) => view('filament.modals.tiktok-videos', ['channel' => $record]))
                    ->modalWidth('7xl'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTiktokChannels::route('/'),
            'create' => Pages\CreateTiktokChannel::route('/create'),
            'edit' => Pages\EditTiktokChannel::route('/{record}/edit'),
        ];
    }
}
