<?php

namespace App\Filament\App\Resources;

use App\Enums\ColorMap;
use App\Filament\App\Resources\ProductVariantResource\Pages;
use App\Filament\App\Resources\ProductVariantResource\RelationManagers;
use App\Models\ProductVariant;
use App\Models\Supplier;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use App\Models\DesignFile;
use Filament\Notifications\Notification;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;

class ProductVariantResource extends Resource
{
    protected static ?string $model = ProductVariant::class;


    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;
    public static function getNavigationGroup(): ?string
    {
        return ProductResource::getNavigationLabel();
    }
    public static function getParentResource(): string
    {
        return ProductResource::class;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_id')->disabled()
                    ->relationship('product', 'name')
                    ->required(),
                Forms\Components\TextInput::make('variant_id')
                    ->required()
                    ->disabled(),
                Forms\Components\TextInput::make('variant_name')
                    ->required()
                    ->disabled(),
                Forms\Components\TextInput::make('variant_fulfill_id')->label('Variant Fulfill ID'),
                Forms\Components\TextInput::make('variant_fulfill_name')->disabled(),
                Forms\Components\Select::make('supplier_id')
                    ->relationship('supplier', 'name'),
                Forms\Components\TextInput::make('design_front_url')
                    ->url(),
                Forms\Components\TextInput::make('design_back_url')
                    ->url(),
                Forms\Components\TextInput::make('sleeve_right_design_url')
                    ->url(),
                Forms\Components\TextInput::make('sleeve_left_design_url')
                    ->url(),
                Forms\Components\TextInput::make('mockup_front_url')
                    ->url(),
                Forms\Components\TextInput::make('mockup_back_url')
                    ->url(),
                // Forms\Components\KeyValue::make('specifications'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ToggleColumn::make('auto_fulfill')
                ->label('Auto Fulfill')
                ->beforeStateUpdated(function (ProductVariant $record, bool $state) {
                    if ($state && !$record->canAutoFulfill()) {
                        Notification::make()
                            ->title('Cannot enable Auto Fulfill')
                            ->body('This variant is missing required design files.')
                            ->danger()
                            ->send();

                        return false; // Prevents the toggle from being updated
                    }

                    return true; // Allows the toggle to be updated
                })
                ->afterStateUpdated(function (ProductVariant $record, bool $state) {
                    if ($state) {
                        // Chỉ track khi bật auto fulfill
                        $record->auto_fulfill_enabled_by = auth()->id();
                        $record->auto_fulfill_enabled_at = now();
                        $record->save();
                    }
                })
                ->disabled(fn (?ProductVariant $record) => !$record?->canAutoFulfill())
                ->tooltip(fn (?ProductVariant $record) => $record?->canAutoFulfill()
                    ? null
                    : 'Missing required design files'
                ),
                Tables\Columns\TextColumn::make('autoFulfillEnabledBy.name')
                    ->label('Auto Fulfill Enabled By')
                    ->description(fn (?ProductVariant $record) => $record?->auto_fulfill_enabled_at?->format('d/m/Y H:i'))
                    ->placeholder('-')
                    ->getStateUsing(function (?ProductVariant $record) {
                        if (!$record || !$record->auto_fulfill) {
                            return '-';
                        }
                        return $record->autoFulfillEnabledBy?->name ?? 'Not tracked';
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.name')
                    ->visible(fn($livewire) => !$livewire instanceof \Filament\Resources\RelationManagers\RelationManager),
                //  Tables\Columns\TextColumn::make('variant_id'),
                Tables\Columns\TextColumn::make('variant_name')->searchable()->description(fn($record) => $record?->variant_fulfill_name)->searchable(),
                Tables\Columns\TextColumn::make('supplier.name')->description(fn($record) => $record?->variant_fulfill_id),
                Tables\Columns\ImageColumn::make('design_front_url')
                    ->defaultImageUrl('https://placehold.co/800x800')
                    ->getStateUsing(function ($record) {
                        return $record?->design_front_url
                            ? DesignFile::photon_url($record->design_front_url, 200)
                            : null;
                    })
                    ->square()
                    ->width(200)
                    ->height(200)
                    ->extraAttributes(function ($record) {
                        return ['style' => 'background-color: ' . self::getColorCode($record?->variant_fulfill_name)];
                    }),
                Tables\Columns\ImageColumn::make('design_back_url')
                    ->defaultImageUrl('https://placehold.co/800x800')
                    ->getStateUsing(function ($record) {
                        return $record?->design_back_url
                            ? DesignFile::photon_url($record->design_back_url, 200)
                            : null;
                    })
                    ->square()
                    ->width(200)
                    ->height(200)
                    ->extraAttributes(function ($record) {
                        return ['style' => 'background-color: ' . self::getColorCode($record?->variant_fulfill_name)];
                    }),
                Tables\Columns\ImageColumn::make('mockup_front_url')
                    ->defaultImageUrl('https://placehold.co/800x800')
                    ->getStateUsing(function ($record) {
                        return $record?->mockup_front_url
                            ? DesignFile::photon_url($record->mockup_front_url, 200)
                            : null;
                    })
                    ->square()
                    ->width(200)
                    ->height(200),
                Tables\Columns\ImageColumn::make('mockup_back_url')
                    ->defaultImageUrl('https://placehold.co/800x800')
                    ->getStateUsing(function ($record) {
                        return $record?->mockup_back_url
                            ? DesignFile::photon_url($record->mockup_back_url, 200)
                            : null;
                    })
                    ->square()
                    ->width(200)
                    ->height(200),

            ])
            ->filters([
                Filter::make('missing_front_design')
                    ->label('Missing Front Design')
                    ->query(fn(Builder $query): Builder => $query->whereNull('design_front_url'))
                    ->toggle(),

                Filter::make('missing_back_design')
                    ->label('Missing Back Design')
                    ->query(fn(Builder $query): Builder => $query->whereNull('design_back_url'))
                    ->toggle(),

                Filter::make('missing_right_sleeve_design')
                    ->label('Missing Right Sleeve Design')
                    ->query(fn(Builder $query): Builder => $query->whereNull('sleeve_right_design_url'))
                    ->toggle(),

                Filter::make('missing_left_sleeve_design')
                    ->label('Missing Left Sleeve Design')
                    ->query(fn(Builder $query): Builder => $query->whereNull('sleeve_left_design_url'))
                    ->toggle(),
   
                Tables\Filters\TernaryFilter::make('auto_fulfill')
                    ->label('Auto Fulfill')
                    ->trueLabel('Yes')
                    ->falseLabel('No')
                    ->placeholder('All'),
            ])
            ->filtersLayout(FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\EditAction::make(),
            ]);
    }

    private static function getColorCode($variantName)
    {
        $colorMap = ColorMap::toArray();

        foreach ($colorMap as $colorName => $colorCode) {
            if (stripos($variantName, $colorName) !== false) {
                return $colorCode;
            }
        }

        return '#FFFFFF'; // Default to white if no color match
    }
    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with('autoFulfillEnabledBy')
            ->when(request()->input('product_id'), fn($query, $productId) => $query->where('product_id', $productId));
    }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductVariants::route('/'),
            'create' => Pages\CreateProductVariant::route('/create'),
            'edit' => Pages\EditProductVariant::route('/{record}/edit'),
        ];
    }
}
