<?php

namespace App\Filament\App\Resources;

use App\Models\MockupTemplateGroup;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Filament\App\Resources\MockupTemplateGroupResource\Pages;
use App\Filament\App\Resources\MockupTemplateGroupResource\RelationManagers;

class MockupTemplateGroupResource extends Resource
{
    protected static ?string $model = MockupTemplateGroup::class;
    protected static ?string $navigationGroup = 'Mockup Tools';  // Thay đổi này
    protected static ?string $navigationLabel = 'Mockup Template Groups';
    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';
    protected static ?int $navigationSort = 3;  // Thêm thứ tự
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên nhóm')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->columnSpanFull()
                    ->label('<PERSON>ô tả')
                    ->maxLength(1000),
                Forms\Components\Toggle::make('is_public')->columnSpanFull()
                    ->helperText('Nếu công khai, tất cả người dùng có thể sử dụng nhóm này')
                    ->label('Công khai')
                    ->default(false),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên nhóm')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ViewColumn::make('configs')
                    ->label('Mockup')
                    ->view('filament.app.tables.columns.configs-gallery'),

                Tables\Columns\IconColumn::make('is_public')
                    ->label('Public')
                    ->boolean(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Người tạo')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
       
                    ->since()
                    ->sortable(),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),

            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ConfigsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMockupTemplateGroups::route('/'),
            'create' => Pages\CreateMockupTemplateGroup::route('/create'),
            'edit' => Pages\EditMockupTemplateGroup::route('/{record}/edit'),
        ];
    }
}
