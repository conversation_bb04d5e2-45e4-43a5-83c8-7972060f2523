<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\BankResource\Pages;
use App\Filament\App\Resources\BankResource\RelationManagers;
use App\Models\Bank;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Models\Store;
use Filament\Notifications\Notification;
use Illuminate\Support\HtmlString;
use App\Models\TikTokPayment;
use App\Models\PayoutTransaction;

class BankResource extends Resource
{
    protected static ?string $model = Bank::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Finance';
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Account Information')
                    ->schema([
                        Forms\Components\TextInput::make('account_email')
                            ->email()
                            ->label('Account Email'),
                        Forms\Components\Select::make('owner_id')
                            ->relationship('owner', 'name')
                            ->required()
                            ->searchable(),
                        Forms\Components\TextInput::make('account_nickname'),
                        Forms\Components\Toggle::make('account_status')
                            ->default(true),
                        Forms\Components\TextInput::make('currency')
                            ->default('USD')
                            ->length(3),
                        Forms\Components\TextInput::make('usage'),
                    ])->columns(2),

                Forms\Components\Section::make('Bank Details')
                    ->schema([
                        Forms\Components\TextInput::make('bank_account_number')
                            ->required(),
                        Forms\Components\TextInput::make('iban'),
                        Forms\Components\TextInput::make('bank_name')
                            ->required(),
                        Forms\Components\TextInput::make('bank_location')
                            ->required(),
                        Forms\Components\TextInput::make('account_holders_name')
                            ->required(),
                        Forms\Components\Select::make('account_type')
                            ->options([
                                'personal' => 'Personal',
                                'business' => 'Business',
                            ])
                            ->default('personal'),
                    ])->columns(2),

                Forms\Components\Section::make('Additional Details')
                    ->schema([
                        Forms\Components\TextInput::make('swift_code'),
                        Forms\Components\TextInput::make('routing_number')
                            ->length(9),
                        Forms\Components\TextInput::make('sort_code'),
                        Forms\Components\TextInput::make('institution_number'),
                        Forms\Components\TextInput::make('transit_number'),
                        Forms\Components\TextInput::make('zip_code'),
                        Forms\Components\TextInput::make('city'),
                        Forms\Components\Textarea::make('bank_address'),
                        Forms\Components\Textarea::make('note')
                            ->columnSpanFull(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('bank_account_number')
                    ->label('Bank Information')
                    ->description(function ($record) {
                        return new HtmlString(
                            'Bank: ' . $record->bank_name . '<br>' .
                                'Currency: ' . $record->currency
                        );
                    })
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query
                            ->where('bank_name', 'like', "%{$search}%")
                            ->orWhere('bank_account_number', 'like', "%{$search}%");
                    })
                    ->sortable()
                    ->html(),
                Tables\Columns\TextColumn::make('store.name')
                    ->label('Linked Store')
                    ->description(
                        fn($record) =>
                        $record->store
                            ? new HtmlString(
                                'Shop Code: ' . ($record->store->tiktok_shop_code ?? 'Chưa Cài App TikTok') . '<br>' .
                                    'Card: <span style="color: ' .
                                    ($record->store->card && substr($record->store->card, -4) === substr($record->bank_account_number, -4)
                                        ? 'green'
                                        : ($record->store->card
                                            ? 'red'
                                            : 'orange')
                                    ) . ';">' .
                                    ($record->store->card ?? 'No Card') . '</span>'
                            )
                            : 'Not linked'
                    )
                    ->html()
                    ->url(fn($record) => $record->store
                        ? StoreResource::getUrl('edit', ['record' => $record->store])
                        : null)
                    ->openUrlInNewTab()->searchable(),
                Tables\Columns\TextColumn::make('store')
                    ->label('This Month Received')
                    ->description(function ($record) {
                        if (!$record->store) return null;

                        $tiktokTotal = TikTokPayment::where('store_id', $record->store->id)
                            ->currentMonth()
                            ->sum('settlement_amount');

                        $payoutTotal = PayoutTransaction::where('card_no', $record->bank_account_number)
                            ->where('type', 'Receive')
                            ->where('status', 'Success')
                            ->whereRaw("DATE_FORMAT(time, '%Y-%m') = ?", [now()->format('Y-m')])
                            ->sum('amount');

                        return new HtmlString(
                            'Payout: $' . number_format($payoutTotal, 2) . '<br>' .
                                'TikTok: $' . number_format($tiktokTotal, 2)
                        );
                    })
                    ->formatStateUsing(function ($record) {
                        if (!$record->store) return 'N/A';

                        $tiktokTotal = TikTokPayment::where('store_id', $record->store->id)
                            ->currentMonth()
                            ->sum('settlement_amount');

                        $payoutTotal = PayoutTransaction::where('card_no', $record->bank_account_number)
                            ->where('type', 'Receive')
                            ->where('status', 'Success')
                            ->whereRaw("DATE_FORMAT(time, '%Y-%m') = ?", [now()->format('Y-m')])
                            ->sum('amount');

                        return '$' . number_format($payoutTotal + $tiktokTotal, 2);
                    })
                    ->html()
                    ->sortable()
                    ->color(fn($record) => $record->store ? 'success' : 'gray'),
                Tables\Columns\TextColumn::make('store.id')
                    ->label('Total Received')
                    ->description(function ($record) {
                        if (!$record->store) return null;

                        $payoutTotal = PayoutTransaction::where('card_no', $record->bank_account_number)
                            ->where('type', 'Receive')
                            ->where('status', 'Success')
                            ->sum('amount');

                        $tiktokTotal = TikTokPayment::where('store_id', $record->store->id)
                            ->where('status', 'PAID')
                            ->sum('settlement_amount');

                        return new HtmlString(
                            'Payout: $' . number_format($payoutTotal, 2) . '<br>' .
                                'TikTok: $' . number_format($tiktokTotal, 2)
                        );
                    })
                    ->formatStateUsing(function ($record) {
                        if (!$record->store) return 'N/A';

                        $payoutTotal = PayoutTransaction::where('card_no', $record->bank_account_number)
                            ->where('type', 'Receive')
                            ->where('status', 'Success')
                            ->sum('amount');

                        $tiktokTotal = TikTokPayment::where('store_id', $record->store->id)
                            ->where('status', 'PAID')
                            ->sum('settlement_amount');

                        return '$' . number_format($payoutTotal + $tiktokTotal, 2);
                    })
                    ->html()
                    ->sortable()
                    ->color(fn($record) => $record->store ? 'success' : 'gray'),
            ])
            ->actions([
                Tables\Actions\Action::make('link_store')
                    ->label('Link to Store')
                    ->icon('heroicon-m-link')
                    ->form([
                        Forms\Components\Section::make('Bank Information')
                            ->schema([
                                Forms\Components\TextInput::make('bank_account_number')
                                    ->label('Bank Account Number')
                                    ->default(fn (Bank $record) => $record->bank_account_number)
                                    ->disabled()
                                    ->dehydrated(false),
                            ]),
                        
                        Forms\Components\Section::make('Store Selection')
                            ->schema([
                                Forms\Components\Select::make('store_id')
                                    ->label('Select Store')
                                    ->options(function () {
                                        return Store::whereNull('bank_account')
                                            ->pluck('name', 'id');
                                    })
                                    ->getSearchResultsUsing(function (string $search) {
                                        return Store::whereNull('bank_account')
                                            ->where(function ($query) use ($search) {
                                                $query
                                                    ->where('name', 'like', "%{$search}%")
                                                    ->orWhere('card', 'like', "%{$search}%")
                                                    ->orWhere('tiktok_shop_code', 'like', "%{$search}%");
                                            })
                                            ->pluck('name', 'id');
                                    })
                                    ->searchable()
                                    ->required()
                                    ->helperText('Search by store name, card number or shop code')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $store = Store::find($state);
                                            $set('store_card', $store->card ?? 'No Card');
                                            $set('store_shop_code', $store->tiktok_shop_code ?? 'N/A');
                                        }
                                    }),

                                Forms\Components\TextInput::make('store_card')
                                    ->label('Store Card Number')
                                    ->disabled()
                                    ->dehydrated(false)
                                    ->helperText('The card number associated with selected store'),

                                Forms\Components\TextInput::make('store_shop_code')
                                    ->label('TikTok Shop Code')
                                    ->disabled()
                                    ->dehydrated(false),
                            ]),
                    ])
                    ->button()
                    ->action(function (Bank $record, array $data): void {
                        $store = Store::find($data['store_id']);
                        $store->update([
                            'bank_account' => $record->bank_account_number
                        ]);

                        Notification::make()
                            ->title('Store linked successfully')
                            ->success()
                            ->send();
                    })
                    ->visible(fn(Bank $record) => $record->store === null),
                Tables\Actions\Action::make('unlink_store')
                    ->label('Unlink Store')
                    ->icon('heroicon-m-x-mark')
                    ->color('danger')
                    ->button()
                    ->requiresConfirmation()
                    ->action(function (Bank $record): void {
                        if ($record->store) {
                            $record->store->update([
                                'bank_account' => null
                            ]);

                            Notification::make()
                                ->title('Store unlinked successfully')
                                ->success()
                                ->send();
                        }
                    })
                    ->visible(fn(Bank $record) => $record->store !== null),
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),



                ]),
            ])
            ->filters([])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                    // Tables\Actions\ForceDeleteBulkAction::make(),
                    // Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBanks::route('/'),
            'create' => Pages\CreateBank::route('/create'),
            'edit' => Pages\EditBank::route('/{record}/edit'),
        ];
    }
}
