<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\ProductionResource\Pages;
use App\Filament\App\Resources\ProductionResource\RelationManagers\MediaRequestsRelationManager;
use App\Forms\Components\ImageGalleryField;
use App\Models\Production;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Wizard;
use Filament\Support\Enums\MaxWidth;
use App\Models\Product;
use App\Forms\Components\ProductDesignGalleryField;
use App\Tables\Columns\DesignJobSummary;
use Filament\Notifications\Notification;

class ProductionResource extends Resource
{
    protected static ?string $model = Production::class;

    protected static ?string $navigationIcon = 'heroicon-o-beaker';

    protected static ?string $navigationGroup = 'Design & Media';

    protected static ?string $navigationLabel = 'Production';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Wizard::make([
                // Bước 1: Chọn sản phẩm và phôi
                Wizard\Step::make('Product & Blank')
                    ->icon('heroicon-o-shopping-bag')
                    ->schema([
                        Forms\Components\Select::make('product_id')
                            ->relationship('product', 'name')
                            ->searchable()
                            ->preload(false)
                            ->live()
                            ->label('Sản phẩm')
                            ->placeholder('Tìm kiếm sản phẩm...')
                            ->helperText('Chọn sản phẩm cần sản xuất (nếu có)')
                            ->getSearchResultsUsing(function (string $search) {
                                return Product::query()
                                    ->where(function ($query) use ($search) {
                                        $query->where('name', 'like', "%{$search}%")
                                            ->orWhere('sku', 'like', "%{$search}%");
                                    })
                                    ->limit(10)
                                    ->get()
                                    ->mapWithKeys(fn ($product) => [
                                        $product->id => view('filament.components.product-option', [
                                            'name' => $product->name,
                                            'image' => $product->image,
                                            'sku' => $product->sku,
                                            'store' => $product->store?->name
                                        ])->render()
                                    ]);
                            })
                            ->allowHtml()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state) {
                                    $product = Product::find($state);
                                    if ($product) {
                                        $set('name', $product->name);
                                    }
                                }
                            }),

                        Forms\Components\TextInput::make('name')
                            ->label('Tên sản phẩm')
                            ->helperText('Tên sản phẩm sản xuất')
                            ->required()
                            ->columnSpanFull(),

                        Forms\Components\Select::make('blank_id')
                            ->helperText('Chọn phôi sản xuất')
                            ->relationship('blank', 'name')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->live(),

                        Forms\Components\Radio::make('assigned_to')
                            ->label('Người thực hiện')
                            ->options(fn() => User::role('production')->pluck('name', 'id'))
                            ->helperText('Chọn người sẽ thực hiện sản xuất sản phẩm này')
                            ->required(),
                    ]),

                // Bước 2: Upload thiết kế
                Wizard\Step::make('Design Files')
                    ->icon('heroicon-o-photo')
                    ->schema([
                        ProductDesignGalleryField::make('product_id')
                            ->label('Design Files')
                            ->columnSpanFull()
                            ->visible(fn ($get) => $get('product_id')),
                            ImageGalleryField::make('design_files')
                            ->required()
                            ->maxFiles(5)
                            ->columnSpanFull(),
                    ]),

                // Bước 3: Trạng thái và ghi chú
                Wizard\Step::make('Status & Notes')
                    ->icon('heroicon-o-clipboard-document-list')
                    ->schema([
                        Forms\Components\ToggleButtons::make('status')
                            ->inline()
                            ->options([
                                'pending' => 'Chờ xử lý',
                                'in_production' => 'Đang sản xuất',
                                'completed' => 'Hoàn thành',
                                'rejected' => 'Từ chối',
                            ])
                            ->colors([
                                'pending' => 'warning',
                                'in_production' => 'info',
                                'completed' => 'success',
                                'rejected' => 'danger',
                            ])
                            ->icons([
                                'pending' => 'heroicon-o-clock',
                                'in_production' => 'heroicon-o-play',
                                'completed' => 'heroicon-o-check-circle',
                                'rejected' => 'heroicon-o-x-mark',
                            ])
                            ->required()
                            ->live()
                            ->helperText('Chọn trạng thái của sản xuất')
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state === 'completed') {
                                    $set('is_ready_for_video', true);
                                }
                            }),

                        Forms\Components\Toggle::make('is_ready_for_video')
                            ->label('Sẵn sàng quay video')
                            ->helperText('Bật khi sản phẩm đã sẵn sàng để quay video')
                            ->disabled(fn($get) => $get('status') !== 'completed'),

                        Forms\Components\RichEditor::make('notes')
                            ->label('Ghi chú')
                            ->helperText('Ghi chú về quá trình sản xuất, thời gian, vấn đề phát sinh...')
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'bulletList',
                                'orderedList',
                            ])
                            ->columnSpanFull(),
                    ]),
            ])
            ->persistStepInQueryString()
            ->startOnStep(function ($record) {
               
                if (!$record) return 1; // New record starts at step 1
                if ($record->status) return 3;
                // Logic to determine which step to show based on record state
                if ($record->blank_id) return 2;
               
                return 2; // Default to Media Processing step for existing records
            })->columnSpanFull()->extraAttributes(['class' => 'w-full']),
        ])->extraAttributes(['class' => 'max-w-4xl mx-auto w-full'])
       ;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                DesignJobSummary::make('summary')
                    ->disableClick()
                    ->searchable(['name', 'notes'])
                    ->sortable('created_at'),
  
                Tables\Columns\ImageColumn::make('design_files')
                    ->getStateUsing(function ($record) {
                        if (!$record->design_files || empty($record->design_files)) {
                            return null;
                        }
                        return array_map(function($url) {
                            // Chỉ sử dụng photon_url nếu URL chứa 'kmediaz'
                            return str_contains($url, 'kmediaz') 
                                ? \App\Models\DesignFile::photon_url($url, 110)
                                : $url;
                        }, $record->design_files);
                    })
                    ->width('110px')
                    ->height('110px')
                    ->square()
                    ->limit(3),

                Tables\Columns\TextColumn::make('blank.cost')
                    ->label('Chi phí')
                    ->formatStateUsing(function ($record) {
                        if (!$record->blank) {
                            return '-';
                        }

                        $blankCost = round(floatval($record->blank->cost), 2);
                        $processingCost = round(floatval($record->blank->processing_cost), 2);
                        $quantity = intval($record->quantity) ?: 1;
                        $total = ($blankCost + $processingCost) * $quantity;
                        
                        return view('filament.tables.columns.production-costs', [
                            'record' => $record,
                            'blankCost' => number_format($blankCost, 2),
                            'processingCost' => number_format($processingCost, 2),
                            'quantity' => $quantity,
                            'total' => number_format($total, 2),
                        ]);
                    })
                    ->html(),

                Tables\Columns\TextColumn::make('mediaRequests')
                    ->label('Video Requests')
                    ->formatStateUsing(function ($record) {
                      
                        return view('filament.tables.columns.video-requests', [
                            'requests' => $record->mediaRequests
                        ]);
                    })
                    ->html()
                    ->disabledClick()
                    ->alignLeft(),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->formatStateUsing(function ($state) {
                        return match($state) {
                            'pending' => 'Chờ xử lý',
                            'in_production' => 'Đang sản xuất',
                            'completed' => 'Hoàn thành',
                            'rejected' => 'Từ chối',
                            default => $state
                        };
                    })
                    ->colors([
                        'warning' => 'pending',
                        'info' => 'in_production',
                        'success' => 'completed',
                        'danger' => 'rejected',
                    ])
                    ->icons([
                        'pending' => 'heroicon-o-clock',
                        'in_production' => 'heroicon-o-play',
                        'completed' => 'heroicon-o-check-circle',
                        'rejected' => 'heroicon-o-x-mark',
                    ])
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'in_production' => 'In Production',
                        'completed' => 'Completed',
                        'rejected' => 'Rejected',
                    ]),
                Tables\Filters\SelectFilter::make('assigned_to')
                    ->relationship('assignedUser', 'name')
                    ->label('Người thực hiện'),
                Tables\Filters\SelectFilter::make('seller_id')
                    ->relationship('seller', 'name')
                    ->label('Người gửi job')
                    ->searchable()
                    ->preload(),
                Tables\Filters\TernaryFilter::make('is_ready_for_video')
                    ->label('Video Ready'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
             
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    Tables\Actions\BulkAction::make('markAsCompleted')
                        ->label('Đánh dấu Hoàn thành')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('Đánh dấu các Production đã chọn là Hoàn thành')
                        ->modalDescription('Bạn có chắc chắn muốn chuyển các production đã chọn thành trạng thái "Completed"?')
                        ->modalSubmitActionLabel('Xác nhận')
                        ->modalCancelActionLabel('Hủy')
                        ->action(function ($records) {
                            $updatedCount = 0;
                            foreach ($records as $record) {
                                if ($record->status !== 'completed') {
                                    $record->update([
                                        'status' => 'completed',
                                        'is_ready_for_video' => true,
                                    ]);
                                    $updatedCount++;
                                }
                            }

                            Notification::make()
                                ->title('Thành công!')
                                ->body("Đã cập nhật {$updatedCount} production thành trạng thái Completed.")
                                ->success()
                                ->send();
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
           MediaRequestsRelationManager::class,

        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductions::route('/'),
            'create' => Pages\CreateProduction::route('/create'),
            'edit' => Pages\EditProduction::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with(['mediaRequests', 'assignedUser', 'seller', 'product', 'blank']);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [
            'name',
            'notes',
            'assignedUser.name',
            'seller.name',
            'product.name',
            'blank.name'
        ];
    }

}
