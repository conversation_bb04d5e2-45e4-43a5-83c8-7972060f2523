<?php

namespace App\Filament\App\Resources\VideoJobResource\Pages;

use App\Filament\App\Resources\VideoJobResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateVideoJob extends CreateRecord
{
    protected static string $resource = VideoJobResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Tự động set created_by nếu chưa có
        if (!isset($data['created_by']) || empty($data['created_by'])) {
            $data['created_by'] = Auth::id();
        }

        return $data;
    }
}
