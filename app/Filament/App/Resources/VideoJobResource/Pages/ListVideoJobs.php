<?php

namespace App\Filament\App\Resources\VideoJobResource\Pages;

use App\Filament\App\Resources\VideoJobResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListVideoJobs extends ListRecords
{
    protected static string $resource = VideoJobResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
