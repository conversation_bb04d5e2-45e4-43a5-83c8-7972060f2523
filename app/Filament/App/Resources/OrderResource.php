<?php

namespace App\Filament\App\Resources;

use App\Enums\DesignStatus;
use App\Enums\FileLocation;
use App\Enums\OrderItemStatus;
use App\Enums\OrderStatus;
use App\Filament\App\Resources\OrderResource\Pages;
use App\Filament\App\Resources\OrderResource\Pages\FulfillmentOrder;
use App\Filament\App\Resources\OrderResource\Pages\ViewOrderActivities;
use App\Filament\App\Resources\OrderResource\RelationManagers;
use App\Filament\Imports\OrderImporter;
use App\Forms\Components\CustomUserSelect;
use App\Livewire\FulfillModal;
use App\Models\Customer;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Store;
use App\Models\SupplierOrder;
use App\Models\Team;
use App\Models\User;
use App\Services\Tiktok\TiktokShopService;
use App\Services\Tiktok\TiktokOrderSyncService;
use App\Tables\Columns\FulfillColumn;
use App\Tables\Columns\OrderItemsColumn;
use App\Tables\Columns\TextAreaInputColumn;
use App\Tables\Columns\TextCustomColumn;
use App\Tables\Columns\FulfillmentTimeColumn;
use App\Tables\Columns\TotalProfitColumn;
use App\Tables\Columns\OrderStoreColumn;
use App\Tables\Columns\OrderFulfillmentColumn;
use App\Tables\Columns\BankStatusColumn;
use App\Traits\HasUserFilter;
use Attribute;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Closure;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Livewire;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Set;
use Filament\Tables\Columns\TextColumn;
use Parfaitementweb\FilamentCountryField\Forms\Components\Country;
use Filament\Forms\Components\ViewField;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action as ActionsAction;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkAction;

use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\MultiSelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Filament\Tables\Columns\Column;
use Carbon\Carbon;
use Filament\Tables\Columns\Summarizers\Sum;
use NumberFormatter;

use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use App\Services\LarkService;
use Illuminate\Support\Facades\Cache;

class OrderResource extends Resource
{
    use HasUserFilter;
    protected static ?string $model = Order::class;

    /**
     * Kiểm tra xem seller đã được gửi thông báo cho order này trong 1 tiếng qua chưa
     */
    private static function canSendNotification(int $orderId, int $sellerId): bool
    {
        $cacheKey = "lark_notification_order_{$orderId}_seller_{$sellerId}";
        return !Cache::has($cacheKey);
    }

    /**
     * Đánh dấu đã gửi thông báo cho seller về order này
     */
    private static function markNotificationSent(int $orderId, int $sellerId): void
    {
        $cacheKey = "lark_notification_order_{$orderId}_seller_{$sellerId}";
        Cache::put($cacheKey, true, now()->addHour()); // Cache 1 tiếng
    }

    /**
     * Kiểm tra user có quyền xem và gửi thông báo không
     */
    private static function canUserSendNotification(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole(['super_admin', 'User Manager', 'Fullfillment Manager']);
    }
    public static function getNavigationGroup(): ?string
    {
        return __('Order & Supplier');
    }


    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';

    public static function getNavigationSort(): ?int
    {
        return 2;
    }
    public static function getNavigationBadge(): ?string
    {
        return \Illuminate\Support\Facades\Cache::remember('orders_processing_count', 300, function () {
            return static::getModel()::where('status', 'Processing')->count();
        });
    }

    public static function getNavigationBadgeColor(): ?string
    {
        $count = \Illuminate\Support\Facades\Cache::remember('orders_total_count', 300, function () {
            return static::getModel()::count();
        });
        return $count > 10 ? 'warning' : 'primary';
    }
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->select([
                'orders.*',
                // Pre-calculate base cost to avoid N+1 queries
                DB::raw('(SELECT COALESCE(SUM(base_cost), 0) FROM supplier_orders WHERE supplier_orders.order_id = orders.id) as total_base_cost'),
                // Pre-calculate supplier order count
                DB::raw('(SELECT COUNT(*) FROM supplier_orders WHERE supplier_orders.order_id = orders.id) as supplier_orders_count'),
                // Get latest supplier order created_at for fulfillment time
                DB::raw('(SELECT MAX(created_at) FROM supplier_orders WHERE supplier_orders.order_id = orders.id) as latest_supplier_order_at')
            ])
            ->with([
                // Only load necessary fields for relationships
                'store:id,name,owner_id,last_sync_tiktok,bank_account,card,tiktok_shop_name,tiktok_payout_on_hold,tiktok_payout_day',
                'store.owner:id,name',
                'seller:id,name',
                // Load order items with all fields needed for display including image, name, link, sku
                'orderItems:id,order_id,quantity,price,status,image,name,link,sku,product_id,product_variant_id',
                // Load product relationship for order items to get product image and design info
                'orderItems.product:id,image,name',
                'orderItems.product.design:id,product_id,status',
                'orderItems.productVariant:variant_id,auto_fulfill',
                // Load supplier orders with minimal fields for display
                'SupplierOrders:id,order_id,base_cost,created_at,status'
            ]);
    }
    public static function form(Form $form): Form
    {


        return $form
            ->schema([


                // Cột 1 - Chiếm 2/3 chiều rộng
                Group::make()
                    ->schema([

                        Section::make('Customer')->schema([
                            Forms\Components\TextInput::make('shipping_first_name'),
                            Forms\Components\TextInput::make('shipping_last_name'),
                            Forms\Components\TextInput::make('shipping_email')
                                ->email(),
                        ])->columns(2),
                        Section::make('Shipping')->schema([
                            Forms\Components\TextInput::make('shipping_address_line1')->reactive()
                                ->required(),
                            Forms\Components\TextInput::make('shipping_address_line2'),

                            Forms\Components\TextInput::make('shipping_city')
                                ->required(),
                            Forms\Components\TextInput::make('shipping_region')
                                ->required(),
                            Forms\Components\TextInput::make('shipping_zip')
                                ->required(),
                            Forms\Components\TextInput::make('shipping_phone'),

                            Country::make('shipping_country')->searchable()
                                ->required(),
                        ])->columns(2),
                        // Quan hệ với OrderItems
                        Repeater::make('orderItems')->minItems(1)->deletable(function (?Order $record) {
                            if ($record) {
                                if ($record->id) {
                                    return false;
                                }
                            }
                            return true;
                        })

                            ->relationship()


                            ->schema([
                                Grid::make()->schema([
                                    ViewField::make('QrCode')->dehydrated(false)
                                        ->view('filament.forms.components.order-item')->columnSpanFull(),
                                ])->hiddenOn('create')->columnSpanFull(),

                                Grid::make()->schema([



                                    Forms\Components\TextInput::make('quantity')->minValue(1)
                                        ->numeric()->disabledOn('edit')
                                        ->required(),
                                    Forms\Components\TextInput::make('price')
                                        ->numeric()
                                        ->disabled() // Giá sẽ được cập nhật tự động dựa trên variant, nên không cho phép chỉnh sửa
                                        ->required(),
                                ])->hiddenOn('edit'),

                                Forms\Components\Textarea::make('note')->label("Note")->columnSpanFull()
                                    ->label('Note'),





                            ])->mutateRelationshipDataBeforeCreateUsing(function (array $data): array {
                                unset($data['attribute_values']);
                                unset($data['product_id']);
                                return $data;
                            })->mutateRelationshipDataBeforeSaveUsing(function (array $data): array {

                                unset($data['attribute_values']);
                                unset($data['product_id']);
                                return $data;
                            })->columns(3)->addable(false)




                    ])->columnSpan([
                        'sm' => 2, // trên mobile chiếm toàn bộ
                        'lg' => 2, // trên desktop chiếm 2/3
                    ]),



                // Cột 2 - Chiếm 1/3 chiều rộng
                Group::make()
                    ->schema([
                        Section::make()->schema([

                            Forms\Components\Select::make('store_id')
                                ->relationship(name: 'store', titleAttribute: 'name')
                                ->preload()


                                ->validationAttribute('order number')
                                ->searchable()
                                ->required()->disabledOn('edit')
                                ->createOptionForm([
                                    Forms\Components\TextInput::make('name')
                                        ->required()
                                        ->maxLength(255),
                                    ToggleButtons::make('status')
                                        ->options([
                                            'Active' => 'Active',
                                            'InActive' => 'InActive',
                                        ])->inline()->columnSpanFull()->required()->default('Active'),


                                    RichEditor::make('note')
                                        ->toolbarButtons([
                                            'bold',
                                            'bulletList',
                                            'codeBlock',
                                            'h2',
                                            'h3',
                                        ])
                                        ->maxLength(255),
                                ])
                                ->editOptionForm([
                                    Forms\Components\TextInput::make('name')
                                        ->required()
                                        ->maxLength(255),
                                    ToggleButtons::make('status')
                                        ->options([
                                            'Active' => 'Active',
                                            'InActive' => 'InActive',
                                        ])->inline()->columnSpanFull()->required()->default('Active'),


                                    RichEditor::make('note')
                                        ->toolbarButtons([
                                            'bold',
                                            'bulletList',
                                            'codeBlock',
                                            'h2',
                                            'h3',
                                        ])
                                        ->maxLength(255),
                                ]),
                            // CustomUserSelect::make('handler_id')

                            //     ->label('Fulfillment User')
                            //     ->helperText('Chọn người xử lý đơn.'),
                            Forms\Components\ToggleButtons::make('status')
                                ->inline()
                                ->options(OrderStatus::class)->default("New")
                                ->required(),
                            Forms\Components\TextInput::make('order_number')->disabled()

                                ->required(),
                            Forms\Components\TextInput::make('total')->disabled()->default(0)->live()->readOnly()
                                ->numeric()
                                ->required(),
                            // Forms\Components\TextInput::make('shipping_cost')->disabled()->default(0)->live()
                            //     ->numeric()
                            //     ->required(),
                        ])
                    ])
                    ->columnSpan([
                        'sm' => 2, // trên mobile chiếm toàn bộ
                        'lg' => 1, // trên desktop chiếm 1/3
                    ]),



            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table

            ->columns([
                // Hidden searchable column for order codes
                TextColumn::make('order_code')
                    ->searchable(['order_code', 'order_number'])
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Order Code'),

                // TextColumn::make('status')->description(function ($record) {
                //     if ($record->label) {
                //         return new HtmlString('<span style="color: darkgreen; font-size: 0.875rem;"><svg class="w-3 h-3 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg> Labeled</span>');
                //     }
                //     return new HtmlString('<span style="color: darkorange; font-size: 0.875rem;"><svg class="w-3 h-3 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg> Print Label</span>');
                // })->badge()->action(
                //     ActionsAction::make('select_status')
                //         ->form(function ($record) {
                //             return [
                //                 Actions::make([
                //                     Action::make('printLabel')->action(function (Order $order, Set $set) {
                //                         try {
                //                             // Load store với partnerApp relationship để tránh lỗi
                //                             $store = $order->store()->with('partnerApp')->first();
                //                             if (!$store) {
                //                                 throw new Exception('Store not found for order: ' . $order->id);
                //                             }
                //                             if (!$store->partnerApp) {
                //                                 throw new Exception('Partner App not configured for store: ' . $store->name);
                //                             }

                //                             $tiktok = new TiktokShopService($store);
                //                             $label = $tiktok->syncLabelTracking($order);
                //                             $set('label', $label);
                //                             Notification::make()
                //                                 ->title('Task Executed Successfully')
                //                                 ->success()
                //                                 ->send();
                //                         } catch (Exception $e) {

                //                             Notification::make()
                //                                 ->title('Task Execution Failed')
                //                                 ->body($e->getMessage())
                //                                 ->danger()
                //                                 ->send();
                //                         }
                //                     }),
                //                 ]),
                //                 TextInput::make('label')->reactive()->default($record->label ?? ''),
                //                 Forms\Components\Select::make('status')
                //                     ->label('New Status')
                //                     ->options(OrderStatus::class),
                //             ];
                //         })
                //         ->action(function (Order $order, array $data) {

                //             $order->update(['status' => $data['status'], 'label' => $data['label']]);
                //         })->modalSubmitActionLabel("Update order")
                // )->alignCenter()
                //     ->badge(),

                OrderItemsColumn::make('orderItems')->disabledClick(),

                // TextColumn::make('handler.name')
                //     ->label('Handler')->alignCenter()
                //     ->view('filament.tables.columns.handler-column')->action(
                //         ActionsAction::make('select_handler')

                //             ->form([
                //                 CustomUserSelect::make('handler_id')

                //                     ->required()
                //                     ->placeholder('Select a handler'),
                //             ])
                //             ->action(function ($record, array $data) {
                //                 $record->update(['handler_id' => $data['handler_id']]);
                //             }),

                //     ),
                OrderStoreColumn::make()->disabledClick(),
                BankStatusColumn::make()->disabledClick(),
                TotalProfitColumn::make(),
                OrderFulfillmentColumn::make(),
                TextAreaInputColumn::make('note')->extraAttributes(['class' => 'w-[180px]']),
                FulfillmentTimeColumn::make(),

         
                   
            ])
            ->filters([

                // Bộ lọc đơn chưa fulfill
                TernaryFilter::make('fulfillment_status')
                    ->label('Trạng thái Fulfill')
                    ->placeholder('Tất cả đơn hàng')
                    ->trueLabel('Chưa fulfill')
                    ->falseLabel('Đã fulfill')
                    ->queries(
                        true: fn (Builder $query) => $query->whereDoesntHave('supplierOrders'),
                        false: fn (Builder $query) => $query->whereHas('supplierOrders'),
                        blank: fn (Builder $query) => $query,
                    )
                    ->indicator('Fulfill'),

                // Bộ lọc theo thời gian chưa xử lý
                SelectFilter::make('pending_days')
                    ->label('Đơn chưa xử lý')
                    ->options([
                        '1' => 'Trên 1 ngày',
                        '2' => 'Trên 2 ngày (Quá hạn)',
                        '3' => 'Trên 3 ngày (Khẩn cấp)',
                        '7' => 'Trên 1 tuần',
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (!isset($data['value']) || $data['value'] === null) {
                            return $query;
                        }

                        $days = (int) $data['value'];
                        $cutoffDate = now()->subDays($days);

                        return $query->whereDoesntHave('supplierOrders')
                            ->where('created_at', '<=', $cutoffDate);
                    })
                    ->indicator('Chưa xử lý'),

                static::getStoreFilter('store_id', 'Store', 'store'),

                static::getUserFilter('seller_id', 'User'),

                DateRangeFilter::make('created_at')
                    ->label('Order Date')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->alwaysShowCalendar()
                    ->displayFormat('DD/MM/YYYY')
                    ->separator(' - '),

                // Bộ lọc theo Team - chỉ hiển thị cho super_admin, User Manager, Fullfillment Manager
                SelectFilter::make('team')
                    ->label('Team')
                    ->options(function () {
                        return Team::pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['values'] ?? null,
                            fn (Builder $query, $teamIds): Builder => $query->whereHas('seller.teams', function ($query) use ($teamIds) {
                                $query->whereIn('teams.id', $teamIds);
                            })
                        );
                    })
                    ->multiple()
                    ->visible(fn (): bool => Auth::check() && Auth::user()->hasAnyRole(['Fullfillment Manager', 'User Manager', 'super_admin']))
                    ->searchable()
                    ->preload(),

                // Bộ lọc theo Design Status - hiển thị tất cả status
                SelectFilter::make('design_status')
                    ->label('Design Status')
                    ->options([
                        'Uploaded' => 'Uploaded',
                        'Design' => 'Design',
                        'Archived' => 'Archived',
                        'Clone' => 'Clone',
                        'no_design' => 'No Design',
                    ])
                    ->multiple()
                    ->query(
                        function (Builder $query, array $data): Builder {
                            $statuses = $data['values'] ?? null;

                            if (!$statuses || empty($statuses)) {
                                return $query;
                            }

                            // Kiểm tra xem có 'no_design' trong danh sách không
                            $hasNoDesign = in_array('no_design', $statuses);
                            $designStatuses = array_filter($statuses, fn($status) => $status !== 'no_design');

                            if ($hasNoDesign && !empty($designStatuses)) {
                                // Cả hai: có design với status cụ thể VÀ không có design
                                return $query->where(function ($query) use ($designStatuses) {
                                    $query->whereDoesntHave('orderItems.product.design')
                                          ->orWhereHas('orderItems.product.design', function ($query) use ($designStatuses) {
                                              $query->whereIn('status', $designStatuses);
                                          });
                                });
                            } elseif ($hasNoDesign) {
                                // Chỉ không có design
                                return $query->whereDoesntHave('orderItems.product.design');
                            } else {
                                // Chỉ có design với status cụ thể
                                return $query->whereHas('orderItems.product.design', function ($query) use ($designStatuses) {
                                    $query->whereIn('status', $designStatuses);
                                });
                            }
                        }
                    ),



            ])
            ->headerActions([
                // Action để hiển thị đơn chưa fulfill
                Tables\Actions\Action::make('unfulfilled_orders')
                    ->label('Đơn chưa Fulfill')
                    ->icon('heroicon-o-exclamation-triangle')
                    ->color('warning')
                    ->button()
                    ->action(function ($livewire) {
                        // Reset tất cả filters trước
                        $livewire->tableFilters = [];

                        // Áp dụng filter để hiển thị chỉ những đơn chưa fulfill
                        $livewire->tableFilters['fulfillment_status'] = ['value' => true];

                        // Sắp xếp theo thời gian tạo cũ nhất lên trước
                        $livewire->tableSortColumn = 'created_at';
                        $livewire->tableSortDirection = 'asc';

                        // Thông báo cho người dùng
                        Notification::make()
                            ->title('Đã áp dụng bộ lọc')
                            ->body('Hiển thị các đơn hàng chưa fulfill, sắp xếp theo thời gian tạo cũ nhất')
                            ->success()
                            ->send();
                    })
                    ->tooltip('Hiển thị các đơn hàng chưa fulfill, sắp xếp theo thời gian tạo cũ nhất lên trước'),
            ])
            ->actions([
                // Action Edit với quyền hạn chế - chuyển đến trang edit riêng
                Tables\Actions\EditAction::make()
                    ->label('Edit')
                    ->icon('heroicon-o-pencil')
                    ->color('primary')
                    ->visible(fn (): bool => Auth::check() && Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Fullfillment Manager']))
                    ->url(fn (Order $record): string => OrderResource::getUrl('edit', ['record' => $record]))
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('fulfillment')
                    ->button()
                    ->label('Fulfill')
                    ->icon('heroicon-o-truck')
                    ->color('success')
                    ->url(fn(Order $record): string => OrderResource::getUrl('fulfillment', ['record' => $record]))
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('sync_order')
                    ->label('Sync Order')
                    ->icon('heroicon-m-arrow-path')
                    ->color('warning')
                    ->button()
                    ->visible(fn(Order $record) => !$record->SupplierOrders || $record->SupplierOrders->count() === 0)
                    ->action(function (Order $record) {
                        try {
                            // Load store với partnerApp relationship để tránh lỗi
                            $store = $record->store()->with('partnerApp')->first();
                            if (!$store) {
                                throw new Exception('Store not found for order: ' . $record->order_code);
                            }
                            if (!$store->partnerApp) {
                                throw new Exception('Partner App not configured for store: ' . $store->name);
                            }

                            $tiktok = new TiktokOrderSyncService($store);
                            $tiktok->syncTiktokOrders();

                            Notification::make()
                                ->title('Sync Order thành công')
                                ->body('Đơn hàng đã được đồng bộ từ TikTok Shop')
                                ->success()
                                ->send();
                        } catch (Exception $e) {
                            Notification::make()
                                ->title('Sync Order thất bại')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                // ActionGroup::make([


                //     Tables\Actions\Action::make('view_activities')
                //         ->label('Log')
                //         ->icon('heroicon-m-bolt')->slideOver()
                //         ->color('purple')
                //         ->url(fn ($record) => OrderResource::getUrl('activities', ['record' => $record])),
                // ])
            ])
            ->bulkActions([
                self::updateOrderStatusBulkAction(),
                BulkAction::make('assignHandler')
                    ->label('Assign Handler')
                    ->action(function ($records, array $data) {
                        $records->each(function ($record) use ($data) {
                            $record->update(['handler_id' => $data['handler_id']]);
                        });
                    })
                    ->form([
                        CustomUserSelect::make('handler_id')

                            ->required()
                            ->placeholder('Select a handler')
                    ])
                    ->size('xs')
                    ->icon('heroicon-o-user-plus')
                    ->color('gray'),

                // Bulk action gửi thông báo Lark cho seller
                BulkAction::make('sendLarkNotificationsToSellers')
                    ->label('Gửi thông báo cho Seller hàng loạt')
                    ->icon('heroicon-o-bell')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->modalHeading('Gửi thông báo cho Seller về các đơn hàng quá hạn')
                    ->modalDescription('Bạn có muốn gửi thông báo nhắc nhở đến tất cả seller của các đơn hàng đã chọn không?')
                    ->modalSubmitActionLabel('Gửi thông báo cho Seller')
                    ->deselectRecordsAfterCompletion()
                    ->visible(fn() => self::canUserSendNotification())
                    ->action(function ($records): void {
                        $larkService = app(LarkService::class);
                        $sender = Auth::user();
                        $successCount = 0;
                        $failedCount = 0;
                        $skippedCount = 0;

                        foreach ($records as $record) {
                            // Kiểm tra điều kiện
                            $daysDiff = (int) Carbon::parse($record->created_at)->diffInDays(now());
                            $hasSupplierOrders = $record->SupplierOrders && $record->SupplierOrders->count() > 0;
                            $isCompletedOrCancelled = in_array($record->status, [
                                OrderStatus::Completed,
                                OrderStatus::Cancelled,
                                OrderStatus::Refunded
                            ]);

                            if ($daysDiff < 2 || $hasSupplierOrders || $isCompletedOrCancelled || !$record->seller_id) {
                                $skippedCount++;
                                continue;
                            }

                            $seller = $record->seller;
                            if (!$seller || !$seller->hasLarkBotConfigured()) {
                                $skippedCount++;
                                continue;
                            }

                            // Kiểm tra cache để tránh spam
                            if (!self::canSendNotification($record->id, $seller->id)) {
                                $skippedCount++;
                                continue;
                            }

                            $message = "🔔 THÔNG BÁO ĐÔN HÀNG QUÁ HẠN CHƯA FULFILL\n\n";
                            $message .= "📦 Đơn hàng: {$record->order_code}\n";
                            $message .= "⏰ Đã quá hạn: {$daysDiff} ngày\n";
                            $message .= "📅 Ngày tạo: " . Carbon::parse($record->created_at)->format('d/m/Y H:i') . "\n";
                            $message .= "💰 Tổng tiền: $" . number_format($record->total, 2) . "\n";
                            $message .= "🏪 Store: " . ($record->store ? $record->store->name : 'N/A') . "\n";
                            $message .= "📝 Trạng thái: " . $record->status->getLabel() . "\n\n";
                            $message .= "Vui lòng xử lý fulfill đơn hàng này sớm nhất có thể!";

                            try {
                                $larkSend = $larkService->sendMessage($sender, $seller, $message);

                                if ($larkSend->isSent()) {
                                    // Đánh dấu đã gửi thông báo
                                    self::markNotificationSent($record->id, $seller->id);
                                    $successCount++;
                                } else {
                                    $failedCount++;
                                }
                            } catch (\Exception $e) {
                                $failedCount++;
                            }
                        }

                        $message = "Kết quả gửi thông báo:\n";
                        $message .= "✅ Thành công: {$successCount}\n";
                        if ($failedCount > 0) {
                            $message .= "❌ Thất bại: {$failedCount}\n";
                        }
                        if ($skippedCount > 0) {
                            $message .= "⏭️ Bỏ qua: {$skippedCount} (không đủ điều kiện hoặc đã gửi trong 1h qua)";
                        }

                        Notification::make()
                            ->title('Hoàn thành gửi thông báo cho Seller')
                            ->body($message)
                            ->success()
                            ->send();
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->searchable();
    }
    protected static function updateOrderStatusBulkAction(): BulkAction
    {
        return BulkAction::make('updateStatus')
            ->icon('heroicon-o-arrow-path')
            ->color('gray')
            ->size('xs')
            ->label('Update Status')
            ->action(function ($records, $data) {  // Note: Removed the type hint for $records
                foreach ($records as $record) {
                    $record->status = $data['status'];
                    $record->save();
                }
            })
            ->form([
                Forms\Components\Select::make('status')
                    ->label('New Status')
                    ->options(OrderStatus::class),
            ]);
    }
    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
            'fulfillment' => FulfillmentOrder::route('/order/{record}/fulfillment'),
            'activities' => ViewOrderActivities::route('/order/{record}/activities'),
        ];
    }
}