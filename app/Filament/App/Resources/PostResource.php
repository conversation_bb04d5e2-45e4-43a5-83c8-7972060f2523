<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\PostResource\Pages;
use App\Models\Post;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Tables\Filters\SelectFilter;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Illuminate\Support\Str;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Forms\Set;
use Filament\Forms\Get;

class PostResource extends Resource
{
    use HasPageShield;

    protected static ?string $model = Post::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Bài viết';

    protected static ?string $navigationGroup = 'Nội dung';

    protected static ?int $navigationSort = 1;

    protected static ?string $pluralModelLabel = 'Bài viết';

    protected static ?string $modelLabel = 'Bài viết';
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin cơ bản')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Tiêu đề')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Set $set, ?string $state, ?string $operation, ?string $old) {
                                if ($operation !== 'create') {
                                    return;
                                }

                                if (filled($state)) {
                                    $set('slug', Str::slug($state));
                                }
                            }),

                        Forms\Components\TextInput::make('slug')
                            ->label('Slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(Post::class, 'slug', ignoreRecord: true)
                            ->helperText('URL thân thiện cho bài viết. Sẽ tự động tạo từ tiêu đề.')
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Set $set, ?string $state) {
                                if (filled($state)) {
                                    $set('slug', Str::slug($state));
                                }
                            }),

                        Forms\Components\Textarea::make('excerpt')
                            ->label('Tóm tắt')
                            ->rows(3)
                            ->maxLength(500)
                            ->helperText('Mô tả ngắn gọn về bài viết (tối đa 500 ký tự)'),

                        Forms\Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'draft' => 'Bản nháp',
                                'published' => 'Đã xuất bản',
                            ])
                            ->required()
                            ->default('draft')
                            ->live(),

                        Forms\Components\Select::make('author_id')
                            ->label('Tác giả')
                            ->relationship('author', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->default(auth()->id()),

                        Forms\Components\DateTimePicker::make('published_at')
                            ->label('Thời gian xuất bản')
                            ->visible(fn (Get $get): bool => $get('status') === 'published')
                            ->default(now()),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Nội dung')
                    ->schema([
                        Forms\Components\RichEditor::make('content')
                            ->label('Nội dung')
                            ->required()
                            ->columnSpanFull()
                            ->toolbarButtons([
                                'attachFiles',
                                'blockquote',
                                'bold',
                                'bulletList',
                                'codeBlock',
                                'h2',
                                'h3',
                                'italic',
                                'link',
                                'orderedList',
                                'redo',
                                'strike',
                                'underline',
                                'undo',
                            ]),
                    ]),

                Forms\Components\Section::make('Hình ảnh & SEO')
                    ->schema([
                        Forms\Components\FileUpload::make('featured_image')
                            ->label('Hình ảnh đại diện')
                            ->image()
                            ->disk('s3') // Sử dụng S3 disk
                            ->directory('posts/featured-images')
                            ->visibility('public')
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('meta_description')
                            ->label('Meta Description')
                            ->maxLength(160)
                            ->helperText('Mô tả cho SEO (tối đa 160 ký tự)')
                            ->columnSpanFull(),

                        Forms\Components\TagsInput::make('tags')
                            ->label('Tags')
                            ->placeholder('Nhập tags và nhấn Enter')
                            ->helperText('Các từ khóa liên quan đến bài viết')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('featured_image')
                    ->label('Hình ảnh')
                    ->disk('s3') // Sử dụng S3 disk
                    ->circular()
                    ->defaultImageUrl(url('/images/placeholder.png'))
                    ->size(50),

                Tables\Columns\TextColumn::make('title')
                    ->label('Tiêu đề')
                    ->searchable()
                    ->sortable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('author.name')
                    ->label('Tác giả')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'warning' => 'draft',
                        'success' => 'published',
                    ])
                    ->icons([
                        'heroicon-o-pencil' => 'draft',
                        'heroicon-o-check-circle' => 'published',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'draft' => 'Bản nháp',
                        'published' => 'Đã xuất bản',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('views_count')
                    ->label('Lượt xem')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('published_at')
                    ->label('Ngày xuất bản')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->placeholder('Chưa xuất bản'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('reading_time')
                    ->label('Thời gian đọc')
                    ->formatStateUsing(fn (int $state): string => $state . ' phút')
                    ->alignCenter()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'draft' => 'Bản nháp',
                        'published' => 'Đã xuất bản',
                    ]),

                SelectFilter::make('author')
                    ->label('Tác giả')
                    ->relationship('author', 'name')
                    ->searchable()
                    ->preload(),

                DateRangeFilter::make('created_at')
                    ->label('Ngày tạo')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->displayFormat('d/m/Y')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ]),

                DateRangeFilter::make('published_at')
                    ->label('Ngày xuất bản')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->displayFormat('d/m/Y')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ]),
            ])
            ->actions([
                Tables\Actions\Action::make('preview')
                    ->label('Xem trước')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->modalHeading(fn (Post $record): string => 'Xem trước: ' . $record->title)
                    ->modalContent(fn (Post $record): \Illuminate\Contracts\Support\Htmlable => view('filament.app.modals.post-preview', ['post' => $record]))
                    ->modalWidth('7xl')
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Đóng')
                    ->slideOver(),

                Tables\Actions\Action::make('view_public')
                    ->label('Xem công khai')
                    ->icon('heroicon-o-globe-alt')
                    ->color('success')
                    ->visible(fn (Post $record): bool => $record->isPublished())
                    ->url(fn (Post $record): string => route('posts.show', $record->slug))
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('publish')
                    ->label('Xuất bản')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn (Post $record): bool => $record->status === 'draft' && auth()->user()->hasAnyRole(['super_admin', 'user_manager']))
                    ->requiresConfirmation()
                    ->action(fn (Post $record) => $record->publish()),

                Tables\Actions\Action::make('unpublish')
                    ->label('Hủy xuất bản')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->visible(fn (Post $record): bool => $record->status === 'published' && auth()->user()->hasAnyRole(['super_admin', 'user_manager']))
                    ->requiresConfirmation()
                    ->action(fn (Post $record) => $record->unpublish()),

                Tables\Actions\ViewAction::make()
                    ->visible(fn (): bool => auth()->user()->hasAnyRole(['super_admin', 'user_manager'])),

                Tables\Actions\EditAction::make()
                    ->visible(fn (): bool => auth()->user()->hasAnyRole(['super_admin', 'user_manager'])),

                Tables\Actions\DeleteAction::make()
                    ->visible(fn (): bool => auth()->user()->hasAnyRole(['super_admin', 'user_manager'])),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn (): bool => auth()->user()->hasAnyRole(['super_admin', 'user_manager'])),

                    Tables\Actions\BulkAction::make('publish')
                        ->label('Xuất bản')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->visible(fn (): bool => auth()->user()->hasAnyRole(['super_admin', 'user_manager']))
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->publish();
                            }
                        }),

                    Tables\Actions\BulkAction::make('unpublish')
                        ->label('Hủy xuất bản')
                        ->icon('heroicon-o-x-circle')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->visible(fn (): bool => auth()->user()->hasAnyRole(['super_admin', 'user_manager']))
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->unpublish();
                            }
                        }),
                ])->visible(fn (): bool => auth()->user()->hasAnyRole(['super_admin', 'user_manager'])),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPosts::route('/'),
            'create' => Pages\CreatePost::route('/create'),
            'view' => Pages\ViewPost::route('/{record}'),
            'edit' => Pages\EditPost::route('/{record}/edit'),
        ];
    }

    public static function canAccess(): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'user_manager', 'Seller']);
    }

    public static function canCreate(): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'user_manager']);
    }

    public static function canEdit($record): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'user_manager']);
    }

    public static function canDelete($record): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'user_manager']);
    }

    public static function canView($record): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'user_manager']);
    }
}
