<?php

namespace App\Filament\App\Resources\PostResource\Pages;

use App\Filament\App\Resources\PostResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreatePost extends CreateRecord
{
    protected static string $resource = PostResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Bài viết đã được tạo')
            ->body('Bài viết mới đã được tạo thành công.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Kiểm tra permissions
        if (!auth()->user()->hasAnyRole(['super_admin', 'user_manager'])) {
            abort(403, 'Bạn không có quyền tạo bài viết.');
        }

        // Đảm bảo author_id được set
        if (empty($data['author_id'])) {
            $data['author_id'] = auth()->id();
        }

        // Nếu status là published nhưng chưa có published_at, set thời gian hiện tại
        if ($data['status'] === 'published' && empty($data['published_at'])) {
            $data['published_at'] = now();
        }

        // Tự động tạo slug nếu chưa có
        if (empty($data['slug']) && !empty($data['title'])) {
            $data['slug'] = \Illuminate\Support\Str::slug($data['title']);
        }

        return $data;
    }
}
