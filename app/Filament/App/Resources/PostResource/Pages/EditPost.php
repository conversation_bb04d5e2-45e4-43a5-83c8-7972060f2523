<?php

namespace App\Filament\App\Resources\PostResource\Pages;

use App\Filament\App\Resources\PostResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Support\Htmlable;

class EditPost extends EditRecord
{
    protected static string $resource = PostResource::class;

    protected function getHeaderActions(): array
    {
        $canManage = auth()->user()->hasAnyRole(['super_admin', 'user_manager']);

        return [
            Actions\Action::make('preview')
                ->label('Xem trước')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->modalHeading(fn (): string => 'Xem trước: ' . $this->record->title)
                ->modalContent(fn (): \Illuminate\Contracts\Support\Htmlable => view('filament.app.modals.post-preview', ['post' => $this->record]))
                ->modalWidth('7xl')
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Đóng')
                ->slideOver(),

            Actions\Action::make('view_public')
                ->label('Xem công khai')
                ->icon('heroicon-o-globe-alt')
                ->color('success')
                ->visible(fn (): bool => $this->record->isPublished())
                ->url(fn (): string => route('posts.show', $this->record->slug))
                ->openUrlInNewTab(),

            Actions\Action::make('publish')
                ->label('Xuất bản')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->visible(fn (): bool => $this->record->status === 'draft' && $canManage)
                ->requiresConfirmation()
                ->action(function () {
                    $this->record->publish();
                    Notification::make()
                        ->success()
                        ->title('Đã xuất bản')
                        ->body('Bài viết đã được xuất bản thành công.')
                        ->send();
                }),

            Actions\Action::make('unpublish')
                ->label('Hủy xuất bản')
                ->icon('heroicon-o-x-circle')
                ->color('warning')
                ->visible(fn (): bool => $this->record->status === 'published' && $canManage)
                ->requiresConfirmation()
                ->action(function () {
                    $this->record->unpublish();
                    Notification::make()
                        ->success()
                        ->title('Đã hủy xuất bản')
                        ->body('Bài viết đã được chuyển về bản nháp.')
                        ->send();
                }),

            Actions\ViewAction::make()
                ->visible(fn (): bool => $canManage),

            Actions\DeleteAction::make()
                ->visible(fn (): bool => $canManage),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Bài viết đã được cập nhật')
            ->body('Thay đổi đã được lưu thành công.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Kiểm tra permissions
        if (!auth()->user()->hasAnyRole(['super_admin', 'user_manager'])) {
            abort(403, 'Bạn không có quyền chỉnh sửa bài viết.');
        }

        // Nếu chuyển từ draft sang published mà chưa có published_at
        if ($data['status'] === 'published' &&
            $this->record->status === 'draft' &&
            empty($data['published_at'])) {
            $data['published_at'] = now();
        }

        // Nếu chuyển từ published về draft, xóa published_at
        if ($data['status'] === 'draft' && $this->record->status === 'published') {
            $data['published_at'] = null;
        }

        return $data;
    }
}
