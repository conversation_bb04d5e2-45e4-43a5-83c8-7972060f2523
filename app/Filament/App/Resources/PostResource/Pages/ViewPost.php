<?php

namespace App\Filament\App\Resources\PostResource\Pages;

use App\Filament\App\Resources\PostResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewPost extends ViewRecord
{
    protected static string $resource = PostResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('Xem trước')
                ->icon('heroicon-o-eye')
                ->color('info')
                ->url(fn (): string => route('posts.preview', $this->record))
                ->openUrlInNewTab(),

            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Thông tin bài viết')
                    ->schema([
                        Infolists\Components\TextEntry::make('title')
                            ->label('Tiêu đề'),

                        Infolists\Components\TextEntry::make('slug')
                            ->label('Slug')
                            ->copyable(),

                        Infolists\Components\TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'draft' => 'warning',
                                'published' => 'success',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn (string $state): string => match ($state) {
                                'draft' => 'Bản nháp',
                                'published' => 'Đã xuất bản',
                                default => $state,
                            }),

                        Infolists\Components\TextEntry::make('author.name')
                            ->label('Tác giả'),

                        Infolists\Components\TextEntry::make('views_count')
                            ->label('Lượt xem')
                            ->numeric(),

                        Infolists\Components\TextEntry::make('reading_time')
                            ->label('Thời gian đọc')
                            ->formatStateUsing(fn (int $state): string => $state . ' phút'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Nội dung')
                    ->schema([
                        Infolists\Components\ImageEntry::make('featured_image')
                            ->label('Hình ảnh đại diện')
                            ->disk('s3') // Sử dụng S3 disk
                            ->height(200)
                            ->width(300),

                        Infolists\Components\TextEntry::make('excerpt')
                            ->label('Tóm tắt')
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('content')
                            ->label('Nội dung')
                            ->html()
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('SEO & Metadata')
                    ->schema([
                        Infolists\Components\TextEntry::make('meta_description')
                            ->label('Meta Description'),

                        Infolists\Components\TextEntry::make('tags')
                            ->label('Tags')
                            ->badge()
                            ->separator(','),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Thời gian')
                    ->schema([
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Ngày tạo')
                            ->dateTime('d/m/Y H:i'),

                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Cập nhật lần cuối')
                            ->dateTime('d/m/Y H:i'),

                        Infolists\Components\TextEntry::make('published_at')
                            ->label('Ngày xuất bản')
                            ->dateTime('d/m/Y H:i')
                            ->placeholder('Chưa xuất bản'),
                    ])
                    ->columns(3),
            ]);
    }
}
