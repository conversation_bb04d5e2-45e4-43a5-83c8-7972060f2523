<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\UserAiPromptResource\Pages;
use App\Filament\App\Resources\UserAiPromptResource\RelationManagers;
use App\Models\UserAiPrompt;
use App\Models\AiPrompt;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;

class UserAiPromptResource extends Resource
{
    protected static ?string $model = UserAiPrompt::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationLabel = 'User AI Prompts';

    protected static ?string $modelLabel = 'User AI Prompt';

    protected static ?string $pluralModelLabel = 'User AI Prompts';

    protected static ?string $navigationGroup = 'AI Management';

    /**
     * Chỉ cho phép super_admin và User Manager truy cập
     */
    public static function canAccess(): bool
    {
        $user = auth()->user();

        if (!$user) {
            return false;
        }

        // Kiểm tra role super_admin hoặc User Manager
        return $user->hasRole('super_admin');
    }

    /**
     * Kiểm tra quyền tạo mới
     */
    public static function canCreate(): bool
    {
        return static::canAccess();
    }

    /**
     * Kiểm tra quyền chỉnh sửa
     */
    public static function canEdit($record): bool
    {
        return static::canAccess();
    }

    /**
     * Kiểm tra quyền xóa
     */
    public static function canDelete($record): bool
    {
        return static::canAccess();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Assignment Information')
                    ->description('Assign AI Prompt to User - Only for Super Admin and User Manager')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('Người dùng')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->columnSpan(2),

                        Forms\Components\Select::make('ai_prompt_id')
                            ->label('AI Prompt Template')
                            ->relationship('aiPrompt', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->getOptionLabelFromRecordUsing(fn ($record) => "{$record->name} ({$record->type})")
                            ->columnSpan(2),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Kích hoạt')
                            ->default(true)
                            ->columnSpan(1),

                        Forms\Components\Placeholder::make('assignment_info')
                            ->label('Thông tin')
                            ->content('Chỉ Super Admin và User Manager mới có thể assign AI prompt cho user. User không thể tự customize.')
                            ->columnSpan(3),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Customization for User')
                    ->description('Customize prompt variables and requirements for this specific user')
                    ->schema([
                        Forms\Components\KeyValue::make('custom_variables')
                            ->label('Biến tùy chỉnh cho user này')
                            ->keyLabel('Tên biến')
                            ->valueLabel('Giá trị')
                            ->helperText('Override các biến mặc định trong template cho user này')
                            ->columnSpanFull(),

                        Forms\Components\KeyValue::make('custom_requirements')
                            ->label('Yêu cầu riêng cho user này')
                            ->keyLabel('Yêu cầu')
                            ->valueLabel('Mô tả')
                            ->helperText('Thêm yêu cầu đặc biệt cho user này')
                            ->columnSpanFull(),

                        Forms\Components\KeyValue::make('custom_target_metrics')
                            ->label('Chỉ tiêu riêng cho user này')
                            ->keyLabel('Chỉ tiêu')
                            ->valueLabel('Giá trị')
                            ->helperText('Override target metrics cho user này')
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Assignment Log')
                    ->schema([
                        Forms\Components\Placeholder::make('assigned_by_info')
                            ->label('Được assign bởi')
                            ->content(function ($record) {
                                if (!$record) return 'Chưa có thông tin';
                                return auth()->user()->name . ' (' . auth()->user()->roles->pluck('name')->join(', ') . ')';
                            }),

                        Forms\Components\Placeholder::make('assignment_date')
                            ->label('Ngày assign')
                            ->content(function ($record) {
                                return $record ? $record->created_at?->format('d/m/Y H:i') : 'Chưa có';
                            }),
                    ])
                    ->columns(2)
                    ->hiddenOn('create'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Người dùng')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Medium),

                Tables\Columns\TextColumn::make('aiPrompt.name')
                    ->label('AI Prompt')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Medium),

                Tables\Columns\TextColumn::make('aiPrompt.type')
                    ->label('Loại')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => AiPrompt::getTypes()[$state] ?? $state)
                    ->color(fn (string $state): string => match ($state) {
                        'report' => 'success',
                        'qa' => 'info',
                        'analysis' => 'warning',
                        'summary' => 'primary',
                        'recommendation' => 'danger',
                        'automation' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Kích hoạt')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Người dùng')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('ai_prompt_id')
                    ->label('AI Prompt')
                    ->relationship('aiPrompt', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Trạng thái')
                    ->placeholder('Tất cả')
                    ->trueLabel('Kích hoạt')
                    ->falseLabel('Không kích hoạt'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Xem'),
                Tables\Actions\EditAction::make()
                    ->label('Sửa'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Unassign đã chọn')
                        ->modalHeading('Unassign AI Prompts')
                        ->modalDescription('Bạn có chắc muốn unassign AI prompts từ các users đã chọn?')
                        ->modalSubmitActionLabel('Unassign'),

                    Tables\Actions\BulkAction::make('bulkAssignPrompt')
                        ->label('Bulk Assign Prompt')
                        ->icon('heroicon-o-cpu-chip')
                        ->color('success')
                        ->form([
                            Forms\Components\Select::make('ai_prompt_id')
                                ->label('AI Prompt Template')
                                ->options(function () {
                                    return \App\Models\AiPrompt::where('is_active', true)
                                        ->pluck('name', 'id')
                                        ->toArray();
                                })
                                ->required(),

                            Forms\Components\KeyValue::make('custom_variables')
                                ->label('Custom Variables (áp dụng cho tất cả)')
                                ->keyLabel('Variable')
                                ->valueLabel('Value'),
                        ])
                        ->action(function (array $data, $records) {
                            $aiPromptService = app(\App\Services\AiPromptService::class);
                            $userIds = $records->pluck('user_id')->toArray();

                            try {
                                $results = $aiPromptService->bulkAssignPromptToUsers(
                                    $userIds,
                                    $data['ai_prompt_id'],
                                    $data['custom_variables'] ?? []
                                );

                                $successful = count(array_filter($results, fn($r) => $r['success']));
                                $failed = count(array_filter($results, fn($r) => !$r['success']));

                                if ($failed === 0) {
                                    \Filament\Notifications\Notification::make()
                                        ->title('Bulk Assignment Successful')
                                        ->body("Đã assign prompt cho {$successful} users thành công")
                                        ->success()
                                        ->send();
                                } else {
                                    \Filament\Notifications\Notification::make()
                                        ->title('Bulk Assignment Completed with Errors')
                                        ->body("Thành công: {$successful}, Thất bại: {$failed}")
                                        ->warning()
                                        ->send();
                                }
                            } catch (\Exception $e) {
                                \Filament\Notifications\Notification::make()
                                    ->title('Bulk Assignment Failed')
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Bulk Assign AI Prompt')
                        ->modalDescription('Assign AI prompt cho tất cả users đã chọn'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserAiPrompts::route('/'),
            'create' => Pages\CreateUserAiPrompt::route('/create'),
            'view' => Pages\ViewUserAiPrompt::route('/{record}'),
            'edit' => Pages\EditUserAiPrompt::route('/{record}/edit'),
        ];
    }
}
