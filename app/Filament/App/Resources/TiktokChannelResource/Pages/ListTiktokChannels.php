<?php

namespace App\Filament\App\Resources\TiktokChannelResource\Pages;

use App\Filament\App\Resources\TiktokChannelResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTiktokChannels extends ListRecords
{
    protected static string $resource = TiktokChannelResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
