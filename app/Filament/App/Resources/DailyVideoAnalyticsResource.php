<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\DailyVideoAnalyticsResource\Pages;
use App\Models\TiktokChannel;
use App\Models\TiktokVideo;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class DailyVideoAnalyticsResource extends Resource
{
    protected static ?string $model = TiktokVideo::class;

    protected static ?string $navigationIcon = 'heroicon-o-play';
    
    protected static ?string $navigationLabel = 'TikTok Videos';
    
    protected static ?string $modelLabel = 'TikTok Video';
    
    protected static ?string $pluralModelLabel = 'TikTok Videos';
    
    protected static ?int $navigationSort = 10;
    
    protected static ?string $navigationGroup = 'TikTok';
    
    
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user && ($user->hasRole('super_admin') || $user->hasRole('User Manager'));
    }

    public static function getEloquentQuery(): Builder
    {
        return TiktokVideo::query()
            ->with(['tiktokChannel.user'])
            ->orderBy('create_time', 'desc');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('cover_url')
                    ->label('Thumbnail')
                    ->size(60)
                    ->square(),
                
                Tables\Columns\TextColumn::make('tiktokChannel.username')
                    ->label('Channel')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->copyMessage('Username copied!')
                    ->copyMessageDuration(1500),

                Tables\Columns\TextColumn::make('channel_link')
                    ->label('Link')
                    ->getStateUsing(fn ($record) => $record->tiktokChannel ? 'https://www.tiktok.com/@' . $record->tiktokChannel->username : '')
                    ->url(fn ($record) => $record->tiktokChannel ? 'https://www.tiktok.com/@' . $record->tiktokChannel->username : '')
                    ->openUrlInNewTab()
                    ->icon('heroicon-o-arrow-top-right-on-square')
                    ->iconPosition('after')
                    ->formatStateUsing(fn ($state) => 'Open TikTok')
                    ->color('primary'),
                
                Tables\Columns\TextColumn::make('tiktokChannel.user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->limit(50)
                    ->searchable()
                    ->tooltip(function (TiktokVideo $record): ?string {
                        return $record->description;
                    }),
                
                Tables\Columns\TextColumn::make('create_time')
                    ->label('Posted')
                    ->dateTime('d/m/Y H:i')
                    ->timezone('Asia/Ho_Chi_Minh')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('number_of_plays')
                    ->label('Views')
                    ->numeric()
                    ->sortable()
                    ->formatStateUsing(fn ($state) => number_format($state)),
                
                Tables\Columns\TextColumn::make('number_of_hearts')
                    ->label('Hearts')
                    ->numeric()
                    ->sortable()
                    ->formatStateUsing(fn ($state) => number_format($state)),
                
                Tables\Columns\TextColumn::make('number_of_comments')
                    ->label('Comments')
                    ->numeric()
                    ->sortable()
                    ->formatStateUsing(fn ($state) => number_format($state)),
                
                Tables\Columns\TextColumn::make('duration')
                    ->label('Duration')
                    ->formatStateUsing(fn ($state) => $state ? $state . 's' : '-')
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('tiktok_channel_id')
                    ->label('Channel')
                    ->options(TiktokChannel::pluck('username', 'id'))
                    ->searchable(),
                
                Tables\Filters\SelectFilter::make('user')
                    ->label('User')
                    ->relationship('tiktokChannel.user', 'name')
                    ->searchable(),
                
                DateRangeFilter::make('create_time')
                    ->label('Date Range')
                    ->withIndicator()
                    ->timezone('Asia/Ho_Chi_Minh')
                    ->alwaysShowCalendar()
                    ->displayFormat('DD/MM/YYYY')
                    ->separator(' - ')
                    ->ranges([
                        'Yesterday' => [now()->yesterday()->startOfDay(), now()->yesterday()->endOfDay()],
                        'Today' => [now()->startOfDay(), now()->endOfDay()],
                        'This Week' => [now()->startOfWeek(), now()->endOfWeek()],
                        'Last Week' => [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()],
                        'This Month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'Last Month' => [now()->subMonth()->startOfMonth(), now()->subMonth()->endOfMonth()],
                    ])
            ])
            ->actions([
                Tables\Actions\Action::make('view_details')
                    ->label('View URLs')
                    ->icon('heroicon-o-link')
                    ->modalContent(function (TiktokVideo $record) {
                        return view('filament.modals.video-details', [
                            'video' => $record
                        ]);
                    })
                    ->modalWidth('4xl'),
            ])
            ->defaultSort('create_time', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDailyVideoAnalytics::route('/'),
        ];
    }
    
    public static function canCreate(): bool
    {
        return false;
    }
    
    public static function canEdit($record): bool
    {
        return false;
    }
}
