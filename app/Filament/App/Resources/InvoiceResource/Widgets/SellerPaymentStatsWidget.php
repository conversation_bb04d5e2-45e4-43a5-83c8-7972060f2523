<?php

namespace App\Filament\App\Resources\InvoiceResource\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Invoice;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SellerPaymentStatsWidget extends BaseWidget
{
    protected static ?int $sort = 2;
    
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'this_month';

    protected function getStats(): array
    {
        $period = $this->filter;
        
        // Xác định kho<PERSON>ng thời gian
        switch ($period) {
            case 'today':
                $startDate = now()->startOfDay();
                $endDate = now()->endOfDay();
                $previousStart = now()->subDay()->startOfDay();
                $previousEnd = now()->subDay()->endOfDay();
                break;
            case 'this_week':
                $startDate = now()->startOfWeek();
                $endDate = now()->endOfWeek();
                $previousStart = now()->subWeek()->startOfWeek();
                $previousEnd = now()->subWeek()->endOfWeek();
                break;
            case 'this_month':
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
                $previousStart = now()->subMonth()->startOfMonth();
                $previousEnd = now()->subMonth()->endOfMonth();
                break;
            case 'this_quarter':
                $startDate = now()->startOfQuarter();
                $endDate = now()->endOfQuarter();
                $previousStart = now()->subQuarter()->startOfQuarter();
                $previousEnd = now()->subQuarter()->endOfQuarter();
                break;
            default:
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
                $previousStart = now()->subMonth()->startOfMonth();
                $previousEnd = now()->subMonth()->endOfMonth();
        }

        // Thống kê tổng thanh toán
        $currentTotal = Invoice::whereBetween('billing_month', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->sum('paid_amount');

        $previousTotal = Invoice::whereBetween('billing_month', [$previousStart, $previousEnd])
            ->where('status', '!=', 'cancelled')
            ->sum('paid_amount');

        $totalChange = $previousTotal > 0 ? (($currentTotal - $previousTotal) / $previousTotal) * 100 : 0;

        // Thống kê số hóa đơn đã thanh toán
        $currentPaidCount = Invoice::whereBetween('billing_month', [$startDate, $endDate])
            ->where('status', 'paid')
            ->count();

        $previousPaidCount = Invoice::whereBetween('billing_month', [$previousStart, $previousEnd])
            ->where('status', 'paid')
            ->count();

        $paidChange = $previousPaidCount > 0 ? (($currentPaidCount - $previousPaidCount) / $previousPaidCount) * 100 : 0;

        // Thống kê số seller có thanh toán
        $currentSellersWithPayment = Invoice::whereBetween('billing_month', [$startDate, $endDate])
            ->where('status', 'paid')
            ->distinct('user_id')
            ->count('user_id');

        $previousSellersWithPayment = Invoice::whereBetween('billing_month', [$previousStart, $previousEnd])
            ->where('status', 'paid')
            ->distinct('user_id')
            ->count('user_id');

        $sellersChange = $previousSellersWithPayment > 0 ? (($currentSellersWithPayment - $previousSellersWithPayment) / $previousSellersWithPayment) * 100 : 0;

        // Thống kê trung bình thanh toán mỗi seller
        $avgPaymentPerSeller = $currentSellersWithPayment > 0 ? $currentTotal / $currentSellersWithPayment : 0;
        $previousAvgPayment = $previousSellersWithPayment > 0 ? $previousTotal / $previousSellersWithPayment : 0;
        $avgChange = $previousAvgPayment > 0 ? (($avgPaymentPerSeller - $previousAvgPayment) / $previousAvgPayment) * 100 : 0;

        return [
            Stat::make('Tổng thanh toán', '$' . number_format($currentTotal, 2))
                ->description($this->getChangeDescription($totalChange))
                ->descriptionIcon($totalChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($totalChange >= 0 ? 'success' : 'danger')
                ->chart($this->getChartData($startDate, $endDate, 'paid_amount')),

            Stat::make('Hóa đơn đã thanh toán', $currentPaidCount)
                ->description($this->getChangeDescription($paidChange))
                ->descriptionIcon($paidChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($paidChange >= 0 ? 'success' : 'danger')
                ->chart($this->getChartData($startDate, $endDate, 'count')),

            Stat::make('Sellers có thanh toán', $currentSellersWithPayment)
                ->description($this->getChangeDescription($sellersChange))
                ->descriptionIcon($sellersChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($sellersChange >= 0 ? 'success' : 'danger'),

            Stat::make('TB thanh toán/Seller', '$' . number_format($avgPaymentPerSeller, 2))
                ->description($this->getChangeDescription($avgChange))
                ->descriptionIcon($avgChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($avgChange >= 0 ? 'success' : 'danger'),
        ];
    }

    private function getChangeDescription(float $change): string
    {
        if ($change == 0) {
            return 'Không thay đổi';
        }
        
        $sign = $change >= 0 ? '+' : '';
        return $sign . number_format($change, 1) . '% so với kỳ trước';
    }

    private function getChartData(Carbon $startDate, Carbon $endDate, string $type): array
    {
        $days = [];
        $current = $startDate->copy();
        
        while ($current <= $endDate) {
            if ($type === 'paid_amount') {
                $value = Invoice::whereDate('billing_month', $current)
                    ->where('status', '!=', 'cancelled')
                    ->sum('paid_amount');
            } else {
                $value = Invoice::whereDate('billing_month', $current)
                    ->where('status', 'paid')
                    ->count();
            }
            
            $days[] = (float) $value;
            $current->addDay();
        }

        return $days;
    }

    protected function getFilters(): ?array
    {
        return [
            'today' => 'Hôm nay',
            'this_week' => 'Tuần này',
            'this_month' => 'Tháng này',
            'this_quarter' => 'Quý này',
        ];
    }

    public static function canView(): bool
    {
        return Invoice::canManageInvoices();
    }
}