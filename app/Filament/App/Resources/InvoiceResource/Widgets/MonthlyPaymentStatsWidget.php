<?php

namespace App\Filament\App\Resources\InvoiceResource\Widgets;

use Filament\Widgets\Widget;
use App\Models\Invoice;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Filament\Widgets\Concerns\InteractsWithPageFilters;

class MonthlyPaymentStatsWidget extends Widget
{
    use InteractsWithPageFilters;
    
    protected static string $view = 'filament.app.resources.invoice-resource.widgets.monthly-payment-stats';
    
    protected static ?int $sort = 1;
    
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'last_6_months';

    public function getMonthlyData(): array
    {
        $period = $this->filter;
        
        // <PERSON>ác đ<PERSON>nh kho<PERSON>ng thời gian
        switch ($period) {
            case 'last_3_months':
                $months = 3;
                break;
            case 'last_6_months':
                $months = 6;
                break;
            case 'last_12_months':
                $months = 12;
                break;
            default:
                $months = 6;
        }

        $startDate = now()->subMonths($months - 1)->startOfMonth();
        $endDate = now()->endOfMonth();

        // Lấy dữ liệu thanh toán theo tháng
        $monthlyData = Invoice::select(
            DB::raw('DATE_FORMAT(billing_month, "%Y-%m") as month'),
            DB::raw('SUM(paid_amount) as total_paid'),
            DB::raw('SUM(total_amount) as total_amount'),
            DB::raw('COUNT(*) as total_invoices'),
            DB::raw('SUM(CASE WHEN status = "paid" THEN 1 ELSE 0 END) as paid_invoices'),
            DB::raw('COUNT(DISTINCT user_id) as unique_sellers')
        )
        ->whereBetween('billing_month', [$startDate, $endDate])
        ->where('status', '!=', 'cancelled')
        ->groupBy('month')
        ->orderBy('month', 'desc')
        ->get();

        $result = [];
        
        foreach ($monthlyData as $data) {
            $monthDate = Carbon::createFromFormat('Y-m', $data->month);
            $result[] = [
                'month' => $monthDate->format('m/Y'),
                'month_name' => 'Tháng ' . $monthDate->format('m/Y'),
                'total_paid' => (float) $data->total_paid,
                'total_amount' => (float) $data->total_amount,
                'total_invoices' => $data->total_invoices,
                'paid_invoices' => $data->paid_invoices,
                'unique_sellers' => $data->unique_sellers,
                'avg_per_seller' => $data->unique_sellers > 0 ? $data->total_paid / $data->unique_sellers : 0,
                'payment_rate' => $data->total_amount > 0 ? ($data->total_paid / $data->total_amount) * 100 : 0,
            ];
        }

        return $result;
    }

    public function getGrandTotals(): array
    {
        $period = $this->filter;
        
        // Xác định khoảng thời gian
        switch ($period) {
            case 'last_3_months':
                $months = 3;
                break;
            case 'last_6_months':
                $months = 6;
                break;
            case 'last_12_months':
                $months = 12;
                break;
            default:
                $months = 6;
        }

        $startDate = now()->subMonths($months - 1)->startOfMonth();
        $endDate = now()->endOfMonth();

        // Tính tổng cộng thực tế cho toàn bộ khoảng thời gian
        $grandTotals = Invoice::select(
            DB::raw('SUM(paid_amount) as total_paid'),
            DB::raw('SUM(total_amount) as total_amount'),
            DB::raw('COUNT(*) as total_invoices'),
            DB::raw('SUM(CASE WHEN status = "paid" THEN 1 ELSE 0 END) as paid_invoices'),
            DB::raw('COUNT(DISTINCT user_id) as unique_sellers')
        )
        ->whereBetween('billing_month', [$startDate, $endDate])
        ->where('status', '!=', 'cancelled')
        ->first();

        return [
            'total_paid' => (float) $grandTotals->total_paid,
            'total_amount' => (float) $grandTotals->total_amount,
            'total_invoices' => $grandTotals->total_invoices,
            'paid_invoices' => $grandTotals->paid_invoices,
            'unique_sellers' => $grandTotals->unique_sellers,
            'payment_rate' => $grandTotals->total_amount > 0 ? ($grandTotals->total_paid / $grandTotals->total_amount) * 100 : 0,
            'avg_per_seller' => $grandTotals->unique_sellers > 0 ? $grandTotals->total_paid / $grandTotals->unique_sellers : 0,
        ];
    }

    public static function canView(): bool
    {
        return Invoice::canManageInvoices();
    }
}