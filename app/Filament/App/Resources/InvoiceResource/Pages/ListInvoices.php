<?php

namespace App\Filament\App\Resources\InvoiceResource\Pages;

use App\Filament\App\Resources\InvoiceResource;
use App\Models\Invoice;
use App\Models\Team;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Filament\Actions\Action;
use Illuminate\Support\Facades\Auth;
use Filament\Pages\Concerns\ExposesTableToWidgets;

class ListInvoices extends ListRecords
{
    use ExposesTableToWidgets;
    
    protected static string $resource = InvoiceResource::class;

    public function getTabs(): array
    {
        $baseQuery = Invoice::query();

        return [
            'all' => Tab::make('Tất Cả')
                ->badge($baseQuery->count())
                ->badgeColor('primary'),
            
            'pending' => Tab::make('Chờ Xử Lý')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'pending'))
                ->badge($baseQuery->clone()->where('status', 'pending')->count())
                ->badgeColor('warning'),

            'approved' => Tab::make('Đã Duyệt')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'approved'))
                ->badge($baseQuery->clone()->where('status', 'approved')->count())
                ->badgeColor('info'),

            'confirmed' => Tab::make('Đã Xác Nhận')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'confirmed'))
                ->badge($baseQuery->clone()->where('status', 'confirmed')->count())
                ->badgeColor('success'),

            'paid' => Tab::make('Đã Thanh Toán')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'paid'))
                ->badge($baseQuery->clone()->where('status', 'paid')->count())
                ->badgeColor('primary'),

            'cancelled' => Tab::make('Đã Huỷ')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'cancelled'))
                ->badge($baseQuery->clone()->where('status', 'cancelled')->count())
                ->badgeColor('danger'),

            'overdue' => Tab::make('Quá Hạn')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->where('due_date', '<', now())
                    ->whereNotIn('status', ['paid', 'cancelled'])
                )
                ->badge($baseQuery->clone()
                    ->where('due_date', '<', now())
                    ->whereNotIn('status', ['paid', 'cancelled'])
                    ->count()
                )
                ->badgeColor('danger'),

            'this_month' => Tab::make('Tháng Này')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereMonth('issue_date', now()->month)
                    ->whereYear('issue_date', now()->year)
                )
                ->badge($baseQuery->clone()
                    ->whereMonth('issue_date', now()->month)
                    ->whereYear('issue_date', now()->year)
                    ->count()
                )
                ->badgeColor('gray'),
        ];
    }

    protected function getHeaderActions(): array
    {
        $actions = [
            Actions\CreateAction::make(),
        ];
        
        // Only add Team Invoice button for Leaders or Admins
        $user = Auth::user();
        if ($user->hasRole(['super_admin', 'Leader'])) {
            // Get user's teams
            $teams = $user->teams;
            
            // Only add if user has teams
            if ($teams->isNotEmpty()) {
                // Create a team invoice button for each team
                foreach ($teams as $team) {
                    $actions[] = Action::make('printTeamInvoice' . $team->id)
                        ->label("Print {$team->name} Invoice")
                        ->icon('heroicon-o-printer')
                        ->color('gray')
                        ->url(route('team.invoice.print', ['team' => $team->id]))
                        ->openUrlInNewTab();
                }
            }
        }
        
        return $actions;
    }

    protected function getHeaderWidgets(): array
    {
        return [
            \App\Filament\App\Resources\InvoiceResource\Widgets\MonthlyPaymentStatsWidget::class,
        ];
    }
}
