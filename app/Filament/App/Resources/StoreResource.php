<?php

namespace App\Filament\App\Resources;

use App\Enums\StoreType;
use App\Enums\TiktokShopStatus;
use App\Filament\App\Resources\StoreResource\Pages;
use App\Filament\App\Resources\StoreResource\Pages\CreateProduct;
use App\Filament\App\Resources\StoreResource\Pages\EditProduct;
use App\Filament\App\Resources\StoreResource\Pages\Payout;
use App\Filament\App\Resources\StoreResource\Pages\Promotion;
use App\Filament\App\Resources\StoreResource\Pages\StoreOverview;
use App\Filament\App\Resources\StoreResource\RelationManagers;
use App\Filament\App\Resources\StoreResource\RelationManagers\PayoutRelationManager;
use App\Filament\App\Resources\StoreResource\RelationManagers\SupplierOrderRelationManager;
use App\Filament\App\Resources\StoreResource\RelationManagers\TiktokPayoutRelationManager;
use App\Forms\Components\SelectProxy;
use App\Models\Store;
use App\Models\User;
use App\Services\Tiktok\TiktokOrderSyncService;
use App\Services\Tiktok\TiktokShopService;
use App\Tables\Columns\MetricsSlider;
use App\Tables\Columns\StoreNameWithStatus;
use App\Traits\HasUserFilter;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\HeaderActionsPosition;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Enums\ActionsPosition;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use App\Services\StoreRevenueService;
use App\Tables\Columns\StoreMetrics;
use Filament\Tables\Columns\Summarizers\Sum;
use Illuminate\Support\Str;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Columns\ViewColumn;



class StoreResource extends Resource
{
    use HasUserFilter;
    protected static ?string $model = Store::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';


    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with('storeMetric');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make('')->schema([
                    Forms\Components\Section::make("Store Info")
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->required()
                                ->maxLength(255),
                            Forms\Components\TextInput::make('niche')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('email')
                                ->email()
                                ->maxLength(255),
                            Textarea::make('note')
                                ->maxLength(255)->columnSpanFull(),
                            Select::make('proxy_id')
                                ->searchable()
                                ->preload()
                                ->relationship(
                                    name: 'proxy',
                                    titleAttribute: 'proxy',
                                    modifyQueryUsing: fn(Builder $query) => $query->whereDoesntHave('store'),
                                ),
                            Forms\Components\TextInput::make('token')->maxLength(255)->hiddenOn('create')->disabled(),
                            // Textarea::make('tiktok_cookie')
                            //     ->dehydrated(fn($state) => $state !== null)  // Ensure value is only considered if not null
                            //     ->visible(fn(callable $get) => $get('type') === 'Tiktok'),  // Immediately react to 'type' changes

                            Forms\Components\TextInput::make('api_key')
                                ->maxLength(255),
                            Forms\Components\TextInput::make('api_secret')
                                ->maxLength(255),


                        ])->columns(2),
                    Forms\Components\Section::make("Bank & Info")
                        ->schema([
                            Forms\Components\TextInput::make('card')->disabled()
                                ->maxLength(255),
                            // Forms\Components\TextInput::make('bank_account')->disabled()
                            //     ->maxLength(255),
                            Forms\Components\TextInput::make('bank_code')->disabled()
                                ->maxLength(255),
                            Forms\Components\Textarea::make('info')
                                ->maxLength(255),
                        ])

                ])->columnSpan([
                    'sm' => 2, // trên mobile chiếm toàn bộ
                    'lg' => 2, // trên desktop chiếm 2/3
                ]),



                Grid::make('')->schema([
                    Forms\Components\Section::make("Setting")
                        ->schema([
                            Select::make('owner_id')
                                ->searchable()
                                ->preload()
                                ->relationship(
                                    name: 'seller',
                                    titleAttribute: 'name',
                                    modifyQueryUsing: fn(Builder $query) => $query->role('Seller'),
                                ),
                            ToggleButtons::make('status')
                                ->options([
                                    'Active' => 'Active',
                                    'InActive' => 'InActive',
                                ])->inline()->columnSpanFull()->required()->default('Active'),
                            ToggleButtons::make('type')->live()
                                ->options(StoreType::class)->inline()->columnSpanFull()->required()->default('None'),

                            Toggle::make('auto_add_design')
                                ->onIcon('heroicon-m-bolt')->default(true)
                                ->offIcon('heroicon-m-user')->inline(),

                        ]),
                    // Forms\Components\Section::make("Tiktok Shop Info")
                    //     ->schema([
                    //         TextInput::make('tiktok_seller_id')->disabled(),
                    //         TextInput::make('tiktok_payout_day')->disabled(),
                    //         TextInput::make('tiktok_shop_name')->disabled(),
                    //         TextInput::make('tiktok_shop_status')->disabled(),
                    //         TextInput::make('tiktok_seller_type')->disabled(),
                    //         TextInput::make('tiktok_business_type')->disabled(),
                    //         TextInput::make('tiktok_shop_code')->disabled(),
                    //         TextInput::make('user_agent')->disabled(),




                    //     ])->visible(fn(callable $get) => $get('type') === 'Tiktok'),
                ])
                    ->columnSpan([
                        'sm' => 2, // trên mobile chiếm toàn bộ
                        'lg' => 1, // trên desktop chiếm 1/3
                    ]),
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('name', 'asc')

            ->columns([
                StoreNameWithStatus::make()
                    ->label('Store & Status')
                    ->searchable(['name', 'tiktok_shop_name', 'tiktok_shop_code', 'bank_account']),

                TextInputColumn::make('niche')
                    ->width('120px'),

                ViewColumn::make('tiktok_status')
                    ->label('TikTok Status')
                    ->view('tables.columns.tiktok-status')
                    ->toggleable(),


                ViewColumn::make('verification_codes')
                    ->label('Verification Codes')
                    ->view('tables.columns.verification-codes')
                    ->toggleable()
                    ->searchable(['email'])
                    ->extraAttributes([
                        'class' => 'verification-codes-column'
                    ])
                    ->action(
                        Tables\Actions\Action::make('refresh_verification_code')
                            ->label('Get Code')
                            ->icon('heroicon-o-arrow-path')
                            ->color('primary')
                            ->button()
                            ->size('sm')
                            ->visible()
                            ->action(fn () => Notification::make()
                                ->title('Refreshing verification codes')
                                ->success()
                                ->send())
                            ->extraAttributes([
                                'x-on:click' => 'setTimeout(() => { $wire.$refresh() }, 300)',
                            ])
                    )
                    ->alignCenter(),

                StoreMetrics::make('sotre_metrics')
                    ->label('Thống Kê Store'),
                    

                ViewColumn::make('tiktok_payout_on_hold')
                    ->label('Thanh Toán & Vi Phạm')
                    ->view('tables.columns.settlement-info')
                    ->toggleable()
                    ->width('200px')
                    ->searchable(['tiktok_payout_on_hold'])
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderByRaw("CAST(COALESCE(tiktok_payout_on_hold, 0) AS DECIMAL(15,2)) {$direction}");
                    })
                    ->summarize([
                        Sum::make('tiktok_payout_on_hold')
                            ->money('USD')
                            ->label('Tổng Tiền Hold'),
                    ]),

                //Tables\Columns\TextColumn::make('binded_bank_account')->sortable()->alignCenter()->label('Binded Bank Account'),


                // Tables\Columns\TextColumn::make('production_day')->sortable()->alignCenter()->label('Production Day'),
                // Tables\Columns\TextColumn::make('shop_health')->sortable()->alignCenter()->label('Shop Health'),
                // Tables\Columns\ToggleColumn::make('auto_renew_flash_sale')->label('Auto Renew Flash Sale')->searchable(['tiktok_shop_code']),







            ])
            ->filters([
                static::getStoreFilter('id', 'Store Name'),

                SelectFilter::make('status')
                    ->options([
                        'Active' => 'Active',
                        'InActive' => 'InActive',
                    ])
                    ->searchable()
                    ->preload(),

                SelectFilter::make('violation_risk')
                    ->label('Mức Độ Vi Phạm')
                    ->options([
                        'low' => 'Thấp (0 điểm)',
                        'medium' => 'Trung Bình (1-11 điểm)',
                        'high' => 'Cao (12+ điểm)',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return match ($data['value'] ?? null) {
                            'low' => $query->where('violation_score', '=', 0),
                            'medium' => $query->whereBetween('violation_score', [1, 11]),
                            'high' => $query->where('violation_score', '>=', 12),
                            default => $query,
                        };
                    }),

                SelectFilter::make('bank_status')
                    ->label('Trạng thái Bank')
                    ->options([
                        'no_bank' => 'Chưa add bank',
                        'no_tiktok_bank' => 'Chưa add bank TikTok',
                        'bank_mismatch' => 'Bank không khớp',
                        'bank_ok' => 'Bank OK',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return match ($data['value'] ?? null) {
                            'no_bank' => $query->whereNull('bank_account'),
                            'no_tiktok_bank' => $query->whereNotNull('bank_account')->whereNull('card'),
                            'bank_mismatch' => $query->whereNotNull('bank_account')
                                ->whereNotNull('card')
                                ->whereRaw('RIGHT(bank_account, 4) != RIGHT(card, 4)'),
                            'bank_ok' => $query->whereNotNull('bank_account')
                                ->whereNotNull('card')
                                ->whereRaw('RIGHT(bank_account, 4) = RIGHT(card, 4)'),
                            default => $query,
                        };
                    })
                    ->searchable()
                    ->preload(),



                static::getUserFilter(),

                Tables\Filters\TernaryFilter::make('card')
                    ->label('TikTok Bank')
                    ->placeholder('All stores')
                    ->trueLabel('Linked')
                    ->falseLabel('Not Linked')
                    ->queries(
                        true: fn (Builder $query) => $query->whereNotNull('card'),
                        false: fn (Builder $query) => $query->whereNull('card'),
                    ),

                Tables\Filters\TernaryFilter::make('has_orders')
                    ->label('Trạng thái đơn hàng')
                    ->placeholder('Tất cả shop')
                    ->trueLabel('Có đơn hàng')
                    ->falseLabel('Không có đơn hàng')
                    ->queries(
                        true: fn (Builder $query) => $query->whereHas('orders'),
                        false: fn (Builder $query) => $query->whereDoesntHave('orders'),
                    ),



                Filter::make('high_hold_amount')
                    ->label('Tiền Hold Cao (>$1000)')
                    ->query(fn (Builder $query): Builder => $query->where('tiktok_payout_on_hold', '>', 1000))
                    ->toggle(),

                Filter::make('has_violations')
                    ->label('Có Vi Phạm')
                    ->query(fn (Builder $query): Builder => $query->where('violation_score', '>', 0))
                    ->toggle(),

            

                Filter::make('sync_outdated')
                    ->label('Chưa Đồng Bộ Trên 3 Ngày')
                    ->query(fn (Builder $query): Builder => $query
                        ->where(function ($query) {
                            $query->whereNull('last_sync_tiktok')
                                ->orWhere('last_sync_tiktok', '<', now()->subDays(3));
                        })
                    )
                    ->toggle(),

                Filter::make('incomplete')
                    ->label('Bank Incomplete')
                    ->query(fn (Builder $query): Builder => $query
                        ->whereHas('orders')
                        ->whereNull('bank_account')
                    )
                    ->toggle(),

                Filter::make('has_tiktok_bank_no_system_bank')
                    ->label('Có Bank TikTok - Chưa Có Bank Hệ Thống')
                    ->query(fn (Builder $query): Builder => $query
                        ->whereNotNull('card')  // Có bank TikTok
                        ->whereNull('bank_account')  // Chưa có bank trên hệ thống
                    )
                    ->toggle(),



            ])

            ->actions([

                Tables\Actions\DeleteAction::make()
                ->visible(fn ($record) => $record->orders()->count() === 0),
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\Action::make('sync_order')
                        ->label('Sync Order')
                        ->icon('heroicon-m-bolt')->slideOver()
                        ->color('success')
                        ->visible(fn($record) => !empty($record->app_partner_id))
                        ->action(function (Store $record) {
                            try {
                                $tiktok = new TiktokOrderSyncService($record);
                                $tiktok->syncTiktokOrders();
                                Notification::make()
                                    ->title('Task Executed Successfully')
                                    ->success()
                                    ->send();
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Task Execution Failed')
                                    ->body($e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        }),


                    Tables\Actions\Action::make('view_products')
                        ->label('Products List')
                        ->icon('heroicon-m-rectangle-stack')
                        ->color('blue')
                        ->url(fn($record) => ProductResource::getUrl('index') . "?tableFilters[store][value]={$record->id}&tableFilters[has_orders][isActive]=false")
                        ->openUrlInNewTab(),

                    // Tables\Actions\Action::make('add_product')
                    //     ->label('Add Product')
                    //     ->icon('heroicon-m-plus')
                    //     ->color('success')
                    //     ->url(fn($record) => StoreResource::getUrl('add-product', [
                    //         'record' => $record
                    //     ])),
                    Tables\Actions\Action::make('view_ideas')
                        ->label('Ideas List')
                        ->icon('heroicon-m-light-bulb')
                        ->color('warning')
                        ->url(fn($record) => IdeaResource::getUrl('index', [
                            'tableFilters[stores]' => [$record->id]
                        ]))
                        ->openUrlInNewTab(),
                    Tables\Actions\Action::make('set_bank')
                        ->label('Set Bank')
                        ->icon('heroicon-m-building-library')
                        ->color('info')
                        ->form([
                            Forms\Components\Section::make('Current Information')
                                ->schema([
                                    Forms\Components\TextInput::make('current_bank')
                                        ->label('Current Bank')
                                        ->default(function (Store $record) {
                                            if (!$record->bank_account) return 'No bank linked';
                                            $bank = \App\Models\Bank::where('bank_account_number', $record->bank_account)->first();
                                            return $bank ? "{$bank->bank_name} - {$bank->bank_account_number}" : 'No bank linked';
                                        })
                                        ->disabled()
                                        ->dehydrated(false),

                                    Forms\Components\TextInput::make('store_card')
                                        ->label('Store Card')
                                        ->default(fn(Store $record) => $record->card ?? 'No Card')
                                        ->disabled()
                                        ->dehydrated(false)
                                        ->helperText('The card number associated with this store'),
                                ]),

                            Forms\Components\Section::make('Select New Bank')
                                ->schema([
                                    Forms\Components\Select::make('bank_account')
                                        ->label('Available Banks')
                                        ->options(function (Store $record) {
                                            $query = \App\Models\Bank::whereDoesntHave('store');

                                            // Nếu store có card, chỉ cho phép chọn bank có 4 số cuối khớp với card
                                            if ($record->card) {
                                                $lastFourDigits = substr($record->card, -4);
                                                $query->where('bank_account_number', 'like', "%{$lastFourDigits}");
                                            }

                                            return $query->get()
                                                ->mapWithKeys(function ($bank) {
                                                    $label = "{$bank->bank_name} - {$bank->bank_account_number}";
                                                    if ($bank->account_holders_name) {
                                                        $label .= " ({$bank->account_holders_name})";
                                                    }
                                                    return [$bank->bank_account_number => $label];
                                                });
                                        })
                                        ->searchable()
                                        ->getSearchResultsUsing(function (string $search, Store $record) {
                                            $query = \App\Models\Bank::whereDoesntHave('store')
                                                ->where(function ($query) use ($search) {
                                                    $query->where('bank_account_number', 'like', "%{$search}%")
                                                        ->orWhere('bank_name', 'like', "%{$search}%")
                                                        ->orWhere('account_holders_name', 'like', "%{$search}%");
                                                });

                                            // Nếu store có card, chỉ cho phép chọn bank có 4 số cuối khớp với card
                                            if ($record->card) {
                                                $lastFourDigits = substr($record->card, -4);
                                                $query->where('bank_account_number', 'like', "%{$lastFourDigits}");
                                            }

                                            return $query->get()
                                                ->mapWithKeys(function ($bank) {
                                                    $label = "{$bank->bank_name} - {$bank->bank_account_number}";
                                                    if ($bank->account_holders_name) {
                                                        $label .= " ({$bank->account_holders_name})";
                                                    }
                                                    return [$bank->bank_account_number => $label];
                                                });
                                        })
                                        ->required()
                                        ->helperText(
                                            fn(Store $record) =>
                                            $record->card
                                                ? 'Only showing banks matching the last 4 digits of your card (' . substr($record->card, -4) . ')'
                                                : 'Only showing banks that are not linked to any store'
                                        ),
                                ]),
                        ])
                        ->action(function (Store $record, array $data): void {
                            // Kiểm tra lại một lần nữa để đảm bảo bank vẫn còn trống
                            $bankIsAvailable = \App\Models\Bank::where('bank_account_number', $data['bank_account'])
                                ->whereDoesntHave('store')
                                ->exists();

                            if (!$bankIsAvailable) {
                                Notification::make()
                                    ->title('Error')
                                    ->body('This bank is no longer available. Please select another bank.')
                                    ->danger()
                                    ->send();
                                return;
                            }

                            // Kiểm tra matching của 4 số cuối
                            if ($record->card) {
                                $cardLastFour = substr($record->card, -4);
                                $bankLastFour = substr($data['bank_account'], -4);

                                if ($cardLastFour !== $bankLastFour) {
                                    Notification::make()
                                        ->title('Error')
                                        ->body('Bank account number must match the last 4 digits of your card.')
                                        ->danger()
                                        ->send();
                                    return;
                                }
                            }

                            $record->update([
                                'bank_account' => $data['bank_account']
                            ]);

                            Notification::make()
                                ->title('Bank account linked successfully')
                                ->success()
                                ->send();
                        })
                ])
                    ->label('Actions')
                    ->icon('heroicon-o-adjustments-vertical')
                    ->button()
                    ->iconButton()
                    ->color('gray'),
            ], position: ActionsPosition::BeforeColumns)



            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->paginationPageOptions([15]);
    }

    public static function getRelations(): array
    {
        return [
            PayoutRelationManager::class,
            TiktokPayoutRelationManager::class,
            SupplierOrderRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStores::route('/'),
            'create' => Pages\CreateStore::route('/create'),
            'edit' => Pages\EditStore::route('/{record}/edit'),
            // 'add-product' => CreateProduct::route('/{record}/add-product'),
            'edit-product' => EditProduct::route('/{record}/edit-product'),
            'promotion' => Promotion::route('/{record}/promotion'),
            'payout' => Payout::route('/{record}/payout'),
        ];
    }
}
