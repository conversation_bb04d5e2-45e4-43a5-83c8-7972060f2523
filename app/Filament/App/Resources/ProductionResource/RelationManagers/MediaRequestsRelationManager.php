<?php

namespace App\Filament\App\Resources\ProductionResource\RelationManagers;

use App\Filament\App\Resources\MediaRequestResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MediaRequestsRelationManager extends RelationManager
{
    protected static string $relationship = 'mediaRequests';

    public function form(Form $form): Form
    {
        return MediaRequestResource::form($form);
    }

    public function table(Table $table): Table
    {
        return MediaRequestResource::table($table)->headerActions(
           [
            Tables\Actions\CreateAction::make()->label('Media Request')->icon('heroicon-o-plus'),
           ]
        );
        
    }
}
