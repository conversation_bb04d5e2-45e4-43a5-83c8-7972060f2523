<?php

namespace App\Filament\App\Resources\ProductionResource\Pages;

use App\Filament\App\Resources\ProductionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Pages\ListRecords\Tab;
use Illuminate\Database\Eloquent\Builder;
use Filament\Notifications\Notification;
use App\Models\Production;

class ListProductions extends ListRecords
{
    protected static string $resource = ProductionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),

            Actions\Action::make('completeAll')
                ->label('Completed All')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Hoàn thành tất cả Production')
                ->modalDescription('Bạn có chắc chắn muốn chuyển tất cả các production chưa hoàn thành thành trạng thái "Completed"?')
                ->modalSubmitActionLabel('Xác nhận')
                ->modalCancelActionLabel('Hủy')
                ->action(function () {
                    $updatedCount = Production::whereNotIn('status', ['completed'])
                        ->update([
                            'status' => 'completed',
                            'is_ready_for_video' => true,
                        ]);

                    Notification::make()
                        ->title('Thành công!')
                        ->body("Đã cập nhật {$updatedCount} production thành trạng thái Completed.")
                        ->success()
                        ->send();

                    // Refresh the page to update the data
                    $this->redirect(request()->header('Referer'));
                }),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Productions')
                ->icon('heroicon-o-rectangle-stack')
                ->badge(static::getModel()::count()),

            'pending' => Tab::make('Pending')
                ->icon('heroicon-o-clock')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'pending'))
                ->badge(fn () => static::getModel()::where('status', 'pending')->count())
                ->badgeColor('warning'),

            'in_production' => Tab::make('In Production')
                ->icon('heroicon-o-play')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'in_production'))
                ->badge(fn () => static::getModel()::where('status', 'in_production')->count())
                ->badgeColor('info'),

            'completed' => Tab::make('Completed')
                ->icon('heroicon-o-check-circle')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'completed'))
                ->badge(fn () => static::getModel()::where('status', 'completed')->count())
                ->badgeColor('success'),

            'pending_media' => Tab::make('Pending Media')
                ->icon('heroicon-o-video-camera')
                ->modifyQueryUsing(function (Builder $query) {
                    return $query->whereHas('mediaRequests', function ($query) {
                        $query->whereIn('status', ['pending', 'in_progress', 'review']);
                    });
                })
                ->badge(function () {
                    return static::getModel()::whereHas('mediaRequests', function ($query) {
                        $query->whereIn('status', ['pending', 'in_progress', 'review']);
                    })->count();
                })
                ->badgeColor('warning'),
        ];
    }
}
