<?php

namespace App\Filament\App\Resources\ProductStatisticsResource\Pages;

use App\Exports\ProductStatisticsExport;
use App\Filament\App\Resources\ProductStatisticsResource;
use App\Filament\App\Resources\ProductStatisticsResource\Widgets\TopOrderedProductsChart;
use Filament\Resources\Pages\ListRecords;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Actions\ExportAction;

class ListProductStatistics extends ListRecords
{
    protected static string $resource = ProductStatisticsResource::class;

    // Biến lưu trữ bộ lọc thời gian mặc định
    public ?string $timeFilter = 'week';
    
    // Cờ để theo dõi trạng thái cập nhật
    public bool $isUpdatingCharts = false;

    public function mount(): void
    {
        parent::mount();
        
        // Thiết lập bộ lọc mặc định 7 ngày gần đây nếu chưa có bộ lọc
        if (!isset($this->tableFilters['date']['date'])) {
            $sevenDaysAgo = Carbon::now()->subDays(7)->startOfDay()->format('d/m/Y');
            $today = Carbon::now()->endOfDay()->format('d/m/Y');
            $this->tableFilters['date'] = ['date' => $sevenDaysAgo . ' - ' . $today];

        }
    }

    public function getTitle(): string
    {
        return 'Top 100 sản phẩm được đặt hàng nhiều nhất';
    }

    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [10, 25, 50, 100];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            TopOrderedProductsChart::make([
                'productRank' => 1,
                'filter' => $this->timeFilter,
            ]),

            TopOrderedProductsChart::make([
                'productRank' => 2,
                'filter' => $this->timeFilter,
            ]),

            TopOrderedProductsChart::make([
                'productRank' => 3,
                'filter' => $this->timeFilter,
            ]),
            
            TopOrderedProductsChart::make([
                'productRank' => 4,
                'filter' => $this->timeFilter,
            ]),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            ExportAction::make()
                ->label('Xuất dữ liệu')
                ->color('success')
                ->icon('heroicon-o-arrow-down-tray')
                ->exporter(ProductStatisticsExport::class)
                ->formats([
                    ExportFormat::Xlsx,
                    ExportFormat::Csv,
                ]),

            \Filament\Actions\Action::make('timeRange')
                ->label('Khoảng thời gian biểu đồ')
                ->icon('heroicon-o-calendar')
                ->disabled(fn () => $this->isUpdatingCharts)
                ->form([
                    \Filament\Forms\Components\Select::make('timeFilter')
                        ->label('Khoảng thời gian')
                        ->options([
                            'day' => '24 giờ qua',
                            'three_days' => '3 ngày qua',
                            'week' => '7 ngày qua',
                            'month' => '30 ngày qua',
                            'sixty_days' => '60 ngày qua',
                        ])
                        ->default($this->timeFilter)
                        ->required(),
                ])
                ->action(function (array $data): void {
                    try {
                        // Đánh dấu đang cập nhật
                        $this->isUpdatingCharts = true;

                        // Lưu thời gian mới
                        $this->timeFilter = $data['timeFilter'];

                        // Thông báo cho người dùng
                        Notification::make()
                            ->title('Đang cập nhật biểu đồ...')
                            ->info()
                            ->send();

                        // Phát sự kiện để cập nhật biểu đồ
                        $this->dispatch('filter-changed', timeFilter: $this->timeFilter);

                        // Refresh các widget
                        $this->refreshHeaderWidgets();

                        // Hiển thị thông báo thành công
                        Notification::make()
                            ->title('Đã cập nhật khoảng thời gian')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        // Xử lý lỗi nếu có
                        Notification::make()
                            ->title('Có lỗi xảy ra khi cập nhật')
                            ->danger()
                            ->send();
                    } finally {
                        // Đặt lại cờ để có thể ấn nút lần tiếp theo (luôn chạy kể cả khi có lỗi)
                        $this->isUpdatingCharts = false;
                    }
                }),
        ];
    }

    /**
     * Khi bộ lọc ngày thay đổi, cập nhật biểu đồ
     */
    public function filterTable(): void
    {
        // Gọi phương thức gốc
        parent::filterTable();
        
        // Phát sự kiện để cập nhật biểu đồ
        $this->dispatch('filter-changed', timeFilter: $this->timeFilter);
        
        // Làm mới các widget
        $this->refreshHeaderWidgets();
        
    }
    
    // Phương thức mới để làm mới các widget
    public function refreshHeaderWidgets(): void
    {
        // Thay vì gọi phương thức reset, chỉ cần phát sự kiện refresh để các widget tự cập nhật
        $this->dispatch('refresh');
    }
   
} 