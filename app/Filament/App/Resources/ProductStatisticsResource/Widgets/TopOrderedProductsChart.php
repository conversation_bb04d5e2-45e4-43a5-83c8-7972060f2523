<?php

namespace App\Filament\App\Resources\ProductStatisticsResource\Widgets;

use App\Models\OrderItem;
use App\Models\Product;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Livewire\Attributes\On;

class TopOrderedProductsChart extends ChartWidget
{
    // ID của sản phẩm cần hiển thị
    public ?int $productId = null;

    // Vị trí của sản phẩm trong danh sách top
    public int $productRank = 1;

    // Phương thức để thiết lập productRank
    public function productRank(int $rank): static
    {
        $this->productRank = $rank;
        return $this;
    }

    // Cập nhật dữ liệu mỗi 5 giây nếu đang hiển thị
    protected static ?string $pollingInterval = null; // Tắt tự động cập nhật

    // Cờ để theo dõi trạng thái cập nhật biểu đồ
    public bool $isUpdatingCharts = false;

    // Phương thức này được gọi khi sự kiện filter-changed được phát ra
    #[On('filter-changed')]
    public function updateChart($timeFilter = null): void
    {
        try {
            $this->isUpdatingCharts = true;
            
            if ($timeFilter !== null) {
                $this->filter = $timeFilter;
            }

            $this->updateChartData();
        } catch (\Exception $e) {
            // Log lỗi nếu xảy ra
            logger()->error('Error updating chart: ' . $e->getMessage());
        } finally {
            $this->isUpdatingCharts = false;
        }
    }

    protected static ?string $heading = '';

    protected static ?string $maxHeight = '300px';

    protected static string $color = 'success';

    // Thiết lập chiều rộng
    protected int | string | array $columnSpan = [
        'default' => 'full',
        'md' => 'full',
        'lg' => '1/2',
    ];

    // Thêm bộ lọc thời gian
    public ?string $filter = 'week'; // Giá trị mặc định là 7 ngày gần đây

    public function getDescription(): ?string
    {
        $description = '';

        switch ($this->filter) {
            case 'day':
                $description .= ' hôm nay';
                break;
            case 'three_days':
                $description .= ' trong 3 ngày qua';
                break;
            case 'week':
                $description .= ' trong 7 ngày qua';
                break;
            case 'month':
                $description .= ' trong 1 tháng qua';
                break;
            case 'quarter':
                $description .= ' trong 3 tháng qua';
                break;
        }

        return $description;
    }

    // Lưu trữ dữ liệu sản phẩm và khoảng thời gian
    protected array $productData = [];
    protected array $dateLabels = [];
    protected array $dateRange = [];
    protected $startDate;
    protected $endDate;
    protected $colors = [
        ['rgba(75, 192, 192, 0.7)', 'rgb(75, 192, 192)'],
        ['rgba(54, 162, 235, 0.7)', 'rgb(54, 162, 235)'],
        ['rgba(153, 102, 255, 0.7)', 'rgb(153, 102, 255)'],
        ['rgba(255, 159, 64, 0.7)', 'rgb(255, 159, 64)'],
        ['rgba(255, 99, 132, 0.7)', 'rgb(255, 99, 132)']
    ];

    // Phương thức để xác định khoảng thời gian
    protected function prepareTimeRange(): void
    {
        // Xác định khoảng thời gian dựa trên bộ lọc
        $this->startDate = now();
        $this->endDate = now();

        switch ($this->filter) {
            case 'day':
                // 24 giờ qua
                $this->startDate = now()->subHours(24);
                $this->endDate = now();
                break;
            case 'three_days':
                // 3 ngày qua
                $this->startDate = now()->subDays(3);
                $this->endDate = now();
                break;
            case 'week':
                // 1 tuần qua
                $this->startDate = now()->subDays(7);
                $this->endDate = now();
                break;
            case 'month':
                // 30 ngày qua
                $this->startDate = now()->subDays(30);
                $this->endDate = now();
                break;
            case 'sixty_days':
                // 60 ngày qua
                $this->startDate = now()->subDays(60);
                $this->endDate = now();
                break;
            default:
                // Mặc định là 7 ngày qua
                $this->startDate = now()->subDays(7);
                $this->endDate = now();
                break;
        }

        // Tạo mảng ngày để hiển thị trên trục x
        $this->dateRange = [];
        $this->dateLabels = [];

        // Xác định khoảng thời gian để hiển thị trên biểu đồ
        $period = $this->getPeriodByFilter($this->startDate, $this->endDate);

        foreach ($period as $date) {
            $this->dateLabels[] = $date->format('d/m/Y');
            $this->dateRange[] = $date->format('Y-m-d');
        }
    }

    // Phương thức để lấy top 5 sản phẩm có lượt mua cao nhất
    protected function getTopProducts(): array
    {
        if (empty($this->startDate) || empty($this->endDate)) {
            $this->prepareTimeRange();
        }

        $query = OrderItem::select(
                'products.id',
                'products.name as product_name',
                'products.image',
                DB::raw('SUM(order_items.quantity) as total_ordered')
            )
            ->join('products', 'order_items.product_id', '=', 'products.id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate])
            ->whereNotNull('order_items.product_id')
            ->whereNotIn('orders.status', ['canceled', 'returned']) // Loại bỏ đơn hàng đã hủy hoặc trả lại
            ->groupBy('products.id', 'products.name', 'products.image')
            ->orderByDesc('total_ordered')
            ->limit(5);
            
        $result = $query->get()->toArray();
        
        return $result;
    }

    // Phương thức để lấy dữ liệu cho biểu đồ
    protected function getData(): array
    {
        // Nếu không có product ID, lấy top sản phẩm và sử dụng sản phẩm theo rank
        if (!$this->productId) {
            $topProducts = $this->getTopProducts();
            
            // Đảm bảo có đủ màu sắc cho biểu đồ
            $colorIndex = min($this->productRank - 1, count($this->colors) - 1);
            
            if (empty($topProducts) || !isset($topProducts[$this->productRank - 1])) {
                return [
                    'datasets' => [
                        [
                            'label' => 'Không có dữ liệu',
                            'data' => [0],
                            'backgroundColor' => [$this->colors[$colorIndex][0]],
                            'borderColor' => [$this->colors[$colorIndex][1]],
                            'borderWidth' => 1
                        ],
                    ],
                    'labels' => ['Không có dữ liệu'],
                ];
            }

            $this->productId = $topProducts[$this->productRank - 1]['id'];
        }

        // Lấy thông tin sản phẩm
        $productInfo = $this->getProductInfo($this->productId);
        
        // Đảm bảo có đủ màu sắc cho biểu đồ
        $colorIndex = min($this->productRank - 1, count($this->colors) - 1);

        if (!$productInfo) {
            return [
                'datasets' => [
                    [
                        'label' => 'Không có dữ liệu',
                        'data' => [0],
                        'backgroundColor' => [$this->colors[$colorIndex][0]],
                        'borderColor' => [$this->colors[$colorIndex][1]],
                        'borderWidth' => 1
                    ],
                ],
                'labels' => ['Không có dữ liệu'],
            ];
        }

        // Lấy dữ liệu sản phẩm
        $productData = $this->getProductData($this->productId);

        return [
            'datasets' => [
                [
                    'label' => $productInfo['name'],
                    'data' => $productData,
                    'backgroundColor' => $this->colors[$colorIndex][0],
                    'borderColor' => $this->colors[$colorIndex][1],
                    'borderWidth' => 2,
                    'tension' => 0.3,
                    'fill' => true
                ],
            ],
            'labels' => $this->dateLabels,
        ];
    }

    // Phương thức để lấy dữ liệu cho một sản phẩm cụ thể
    protected function getProductData($productId): array
    {
        if (empty($this->dateRange)) {
            $this->prepareTimeRange();
        }

        $productData = [];

        // Tìm tổng số lượng bán của sản phẩm theo khoảng thời gian
        $totalQuery = OrderItem::select(
                DB::raw('SUM(order_items.quantity) as total_quantity')
            )
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('order_items.product_id', $productId)
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate])
            ->whereNotIn('orders.status', ['canceled', 'returned']); // Loại bỏ đơn hàng đã hủy hoặc trả lại
            
        $totalOrdered = $totalQuery->first()->total_quantity ?? 0;

        // Tạo query lấy dữ liệu đặt hàng theo ngày cho sản phẩm
        $query = OrderItem::select(
                DB::raw('DATE(orders.created_at) as order_date'),
                DB::raw('SUM(order_items.quantity) as daily_quantity')
            )
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('order_items.product_id', $productId)
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate])
            ->whereNotIn('orders.status', ['canceled', 'returned']) // Loại bỏ đơn hàng đã hủy hoặc trả lại
            ->groupBy('order_date');

        // Thực hiện query
        $dailyOrders = $query->get()->keyBy('order_date');

        // Lưu trữ tất cả dữ liệu ngày đã có
        $availableDates = $dailyOrders->keys()->toArray();

        // Nếu không có ngày nào có dữ liệu, trả về 1 điểm dữ liệu duy nhất với tổng số lượng
        if (empty($availableDates)) {
            // Hiển thị tổng số lượng ở ngày đầu tiên
            $this->dateLabels = ['Tổng cộng'];
            $this->dateRange = ['total'];
            return [$totalOrdered];
        }
        
        // Điền dữ liệu cho từng ngày
        foreach ($this->dateRange as $index => $date) {
            if (isset($dailyOrders[$date])) {
                $productData[] = $dailyOrders[$date]->daily_quantity;
            } else {
                // Nếu là ngày cuối cùng và không có dữ liệu trước đó, hiển thị tổng số lượng
                if ($index === 0 && empty($productData)) {
                    $productData[] = $totalOrdered;
                } else {
                    $productData[] = 0;
                }
            }
        }

        return $productData;
    }

    // Phương thức để lấy thông tin sản phẩm
    protected function getProductInfo($productId): ?array
    {
        $product = Product::find($productId);

        if (!$product) {
            return null;
        }

        // Hiển thị đầy đủ tên sản phẩm không cắt ngắn
        $productName = $product->name ? mb_convert_encoding($product->name, 'UTF-8', 'UTF-8') : 'Unknown';

        return [
            'id' => $product->id,
            'name' => '#' . $this->productRank . ': ' . $productName,
            'image' => $product->image,
        ];
    }

    // Ghi đè phương thức getHeading để hiển thị tiêu đề động cho biểu đồ
    public function getHeading(): string
    {
        // Nếu không có product ID, lấy top 3 sản phẩm và sử dụng sản phẩm theo rank
        if (!$this->productId) {
            $topProducts = $this->getTopProducts();

            if (empty($topProducts) || !isset($topProducts[$this->productRank - 1])) {
                return '#' . $this->productRank;
            }

            $this->productId = $topProducts[$this->productRank - 1]['id'];
        }

        // Lấy thông tin sản phẩm
        $productInfo = $this->getProductInfo($this->productId);

        if (!$productInfo) {
            return '#' . $this->productRank;
        }

        return $productInfo['name'];
    }

    /**
     * Tạo khoảng thời gian dựa trên bộ lọc đã chọn
     */
    private function getPeriodByFilter($startDate, $endDate): \Carbon\CarbonPeriod
    {
        // Trước tiên, kiểm tra xem có dữ liệu trong khoảng thời gian này không
        $hasData = OrderItem::join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->exists();
        
        if (!$hasData) {
            // Nếu không có dữ liệu, vẫn hiển thị khoảng thời gian ngắn để không bị trống
            return \Carbon\CarbonPeriod::create(
                $startDate->copy(),
                '1 day', 
                min($endDate->copy(), $startDate->copy()->addDays(6))
            );
        }
        
        // Tìm ngày có dữ liệu mới nhất
        $latestDate = OrderItem::join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->max('orders.created_at');
        
        if ($latestDate) {
            $latestDate = Carbon::parse($latestDate);
            // Nếu ngày có dữ liệu mới nhất cách thời điểm hiện tại hơn 7 ngày, điều chỉnh khoảng thời gian
            if ($endDate->diffInDays($latestDate) > 7) {
                // Giới hạn khoảng thời gian đến 5 ngày sau ngày có dữ liệu mới nhất
                $endDate = $latestDate->copy()->addDays(5);
            }
        }
        
        $daysDiff = $endDate->diffInDays($startDate);
        $interval = 1; // Mặc định mỗi 1 ngày
        
        // Điều chỉnh khoảng thời gian dựa trên số ngày
        if ($daysDiff > 60) {
            $interval = 6; // Mỗi 6 ngày nếu khoảng thời gian trên 60 ngày
        } elseif ($daysDiff > 30) {
            $interval = 3; // Mỗi 3 ngày nếu khoảng thời gian trên 30 ngày
        } elseif ($daysDiff > 14) {
            $interval = 2; // Mỗi 2 ngày nếu khoảng thời gian trên 14 ngày
        }

        switch ($this->filter) {
            case 'day':
                // 24 giờ, hiển thị theo từng giờ
                return \Carbon\CarbonPeriod::create($startDate, '2 hours', $endDate);
            case 'three_days':
                // 3 ngày, hiển thị mỗi 6 giờ
                return \Carbon\CarbonPeriod::create($startDate, '6 hours', $endDate);
            case 'week':
                // 1 tuần, hiển thị mỗi ngày
                return \Carbon\CarbonPeriod::create($startDate, '1 day', $endDate);
            case 'month':
                // 30 ngày, điều chỉnh khoảng thời gian dựa trên dữ liệu
                return \Carbon\CarbonPeriod::create($startDate, "{$interval} days", $endDate);
            case 'sixty_days':
                // 60 ngày, điều chỉnh khoảng thời gian dựa trên dữ liệu
                return \Carbon\CarbonPeriod::create($startDate, "{$interval} days", $endDate);
            default:
                // Mặc định chia thành các khoảng 1 ngày
                return \Carbon\CarbonPeriod::create($startDate, '1 day', $endDate);
        }
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        // Lấy màu cho biểu đồ dựa trên productRank
        $colorIndex = min($this->productRank - 1, count($this->colors) - 1);
        $color = $this->colors[$colorIndex];

        return [
            'plugins' => [
                'legend' => [
                    'display' => false, // Ẩn legend vì mỗi biểu đồ chỉ hiển thị 1 sản phẩm
                ],
                'tooltip' => [
                    'enabled' => true,
                    'displayColors' => true,
                    'padding' => 10,
                    'intersect' => false,
                    'mode' => 'index',
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'precision' => 0,
                    ],
                    'title' => [
                        'display' => true,
                        'text' => 'Số lượng đặt hàng',
                        'font' => [
                            'weight' => 'bold',
                            'size' => 11,
                        ],
                    ],
                    'grid' => [
                        'drawBorder' => false,
                    ],
                ],
                'x' => [
                    'ticks' => [
                        'maxRotation' => 45,
                        'minRotation' => 45,
                        'font' => [
                            'size' => 10,
                        ],
                        'autoSkip' => true,
                        'maxTicksLimit' => 8,
                    ],
                    'title' => [
                        'display' => true,
                        'text' => 'Thời gian',
                        'font' => [
                            'weight' => 'bold',
                            'size' => 11,
                        ],
                    ],
                    'grid' => [
                        'display' => false,
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'elements' => [
                'line' => [
                    'fill' => true,
                    'backgroundColor' => $color[0],
                    'borderColor' => $color[1],
                ],
                'point' => [
                    'radius' => 3,
                    'hoverRadius' => 5,
                    'backgroundColor' => $color[1],
                ],
            ],
        ];
    }
}
