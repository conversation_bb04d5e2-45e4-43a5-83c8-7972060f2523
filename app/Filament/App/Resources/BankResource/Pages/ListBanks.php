<?php

namespace App\Filament\App\Resources\BankResource\Pages;

use App\Filament\App\Resources\BankResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\ImportAction;
use App\Filament\Imports\BankImporter;
use Illuminate\Validation\Rules\File;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Bank;

class ListBanks extends ListRecords
{
    protected static string $resource = BankResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            ImportAction::make()
                ->importer(BankImporter::class)
                ->fileRules([
                    File::types(['csv', 'xlsx', 'xls'])
                        ->max(5120)
                ])
                ->chunkSize(100)
                ->label('Import Banks')
                ->modalHeading('Import Bank Accounts')
                ->modalDescription('Upload a CSV or Excel file to import bank accounts. Please ensure your file matches the required format.')
        ];
    }

    public function getTabs(): array
    {
        $baseQuery = Bank::query();

        return [
            'all' => Tab::make('All Banks')
                ->badge($baseQuery->count())
                ->badgeColor('primary'),

  

            'verified' => Tab::make('Store Verified')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereHas('store', function (Builder $query) {
                        $query->whereNotNull('card')
                            ->whereRaw('RIGHT(stores.card, 4) = RIGHT(banks.bank_account_number, 4)');
                    }))
                ->badge($baseQuery->clone()
                    ->whereHas('store', function (Builder $query) {
                        $query->whereNotNull('card')
                            ->whereRaw('RIGHT(stores.card, 4) = RIGHT(banks.bank_account_number, 4)');
                    })->count())
                ->badgeColor('success')
                ->icon('heroicon-m-check-badge'),

            'mismatch' => Tab::make('Store Mismatch')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereHas('store', function (Builder $query) {
                        $query->whereNotNull('card')
                            ->whereRaw('RIGHT(stores.card, 4) != RIGHT(banks.bank_account_number, 4)');
                    }))
                ->badge($baseQuery->clone()
                    ->whereHas('store', function (Builder $query) {
                        $query->whereNotNull('card')
                            ->whereRaw('RIGHT(stores.card, 4) != RIGHT(banks.bank_account_number, 4)');
                    })->count())
                ->badgeColor('danger')
                ->icon('heroicon-m-exclamation-triangle'),

            'linked_no_card' => Tab::make('Linked No Card')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereHas('store', function (Builder $query) {
                        $query->whereNull('card');
                    }))
                ->badge($baseQuery->clone()
                    ->whereHas('store', function (Builder $query) {
                        $query->whereNull('card');
                    })->count())
                ->badgeColor('warning')
                ->icon('heroicon-m-question-mark-circle'),

            'unlinked' => Tab::make('Not Linked')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereDoesntHave('store'))
                ->badge($baseQuery->clone()->whereDoesntHave('store')->count())
                ->badgeColor('gray')
                ->icon('heroicon-m-x-mark'),
        ];
    }
}
