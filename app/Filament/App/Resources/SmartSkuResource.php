<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\SmartSkuResource\Pages;
use App\Filament\App\Resources\SmartSkuResource\RelationManagers;
use App\Models\SmartSku;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;

class SmartSkuResource extends Resource
{
    protected static ?string $model = SmartSku::class;
    protected static ?string $navigationIcon = 'heroicon-o-tag';
    public static function getNavigationGroup(): ?string
    {
        return __('Order & Supplier');
    }
    protected static ?string $modelLabel = 'Smart SKU';
    protected static ?string $navigationLabel = 'Smart SKUs';
    protected static ?string $slug = 'smart-skus';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('style')
                                    ->required()
                                    ->maxLength(191),

                                Forms\Components\TextInput::make('color')
                                    ->required()
                                    ->maxLength(191),

                                Forms\Components\TextInput::make('size')
                                    ->required()
                                    ->maxLength(191),

                                Forms\Components\Toggle::make('active')
                                    ->required()
                                    ->default(true),
                            ]),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('style')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('color')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('size')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\IconColumn::make('active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('supplier_products_count')
                    ->counts('supplier_products')
                    ->label('Products')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('style')
                    ->multiple()
                    ->options(fn () => SmartSku::distinct()->pluck('style', 'style')),

                Tables\Filters\SelectFilter::make('color')
                    ->multiple()
                    ->options(fn () => SmartSku::distinct()->pluck('color', 'color')),

                Tables\Filters\SelectFilter::make('size')
                    ->multiple()
                    ->options(fn () => SmartSku::distinct()->pluck('size', 'size')),

                Tables\Filters\TernaryFilter::make('active'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->action(fn (Collection $records) => $records->each->update(['active' => true]))
                        ->icon('heroicon-o-check'),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->action(fn (Collection $records) => $records->each->update(['active' => false]))
                        ->icon('heroicon-o-x-mark')
                        ->color('danger'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SupplierProductsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSmartSkus::route('/'),
            // 'create' => Pages\CreateSmartSku::route('/create'),
            // 'edit' => Pages\EditSmartSku::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('active', true)->count();
    }
}