<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\AiPromptResource\Pages;
use App\Filament\App\Resources\AiPromptResource\RelationManagers;
use App\Models\AiPrompt;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;

class AiPromptResource extends Resource
{
    protected static ?string $model = AiPrompt::class;

    protected static ?string $navigationIcon = 'heroicon-o-cpu-chip';

    protected static ?string $navigationLabel = 'AI Prompts';

    protected static ?string $modelLabel = 'AI Prompt';

    protected static ?string $pluralModelLabel = 'AI Prompts';

    protected static ?string $navigationGroup = 'AI Management';

    /**
     * Chỉ cho phép super_admin quản lý AI Prompt templates
     */
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user && $user->hasRole('super_admin');
    }

    /**
     * Chỉ super_admin mới có thể tạo template
     */
    public static function canCreate(): bool
    {
        return static::canAccess();
    }

    /**
     * Chỉ super_admin mới có thể chỉnh sửa template
     */
    public static function canEdit($record): bool
    {
        return static::canAccess();
    }

    /**
     * Chỉ super_admin mới có thể xóa template
     */
    public static function canDelete($record): bool
    {
        return static::canAccess();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin cơ bản')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Tên prompt')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),

                        Forms\Components\Select::make('type')
                            ->label('Loại prompt')
                            ->required()
                            ->options(AiPrompt::getTypes())
                            ->columnSpan(1),

                        Forms\Components\Textarea::make('description')
                            ->label('Mô tả')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Nội dung prompt')
                    ->schema([
                        Forms\Components\Textarea::make('system_prompt')
                            ->label('System prompt')
                            ->required()
                            ->rows(10)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Cấu hình nâng cao')
                    ->schema([
                        Forms\Components\KeyValue::make('variables')
                            ->label('Biến thay thế')
                            ->keyLabel('Tên biến')
                            ->valueLabel('Giá trị mặc định')
                            ->columnSpan(2),

                        Forms\Components\KeyValue::make('requirements')
                            ->label('Yêu cầu đặc biệt')
                            ->keyLabel('Yêu cầu')
                            ->valueLabel('Mô tả')
                            ->columnSpan(2),

                        Forms\Components\KeyValue::make('target_metrics')
                            ->label('Chỉ tiêu mục tiêu')
                            ->keyLabel('Chỉ tiêu')
                            ->valueLabel('Giá trị')
                            ->columnSpan(2),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Trạng thái')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Kích hoạt')
                            ->default(true)
                            ->columnSpan(1),

                        Forms\Components\Toggle::make('is_default')
                            ->label('Mặc định cho loại này')
                            ->columnSpan(1),

                        Forms\Components\Select::make('created_by')
                            ->label('Người tạo')
                            ->relationship('creator', 'name')
                            ->searchable()
                            ->preload()
                            ->default(auth()->id())
                            ->columnSpan(1),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên prompt')
                    ->searchable()
                    ->weight(FontWeight::Medium),

                Tables\Columns\TextColumn::make('type')
                    ->label('Loại')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => AiPrompt::getTypes()[$state] ?? $state)
                    ->color(fn (string $state): string => match ($state) {
                        'report' => 'success',
                        'qa' => 'info',
                        'analysis' => 'warning',
                        'summary' => 'primary',
                        'recommendation' => 'danger',
                        'automation' => 'gray',
                        default => 'gray',
                    })
                    ->searchable(),

                Tables\Columns\TextColumn::make('description')
                    ->label('Mô tả')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Kích hoạt')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\IconColumn::make('is_default')
                    ->label('Mặc định')
                    ->boolean()
                    ->trueIcon('heroicon-o-star')
                    ->falseIcon('heroicon-o-star')
                    ->trueColor('warning')
                    ->falseColor('gray'),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Người tạo')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Loại prompt')
                    ->options(AiPrompt::getTypes()),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Trạng thái')
                    ->placeholder('Tất cả')
                    ->trueLabel('Kích hoạt')
                    ->falseLabel('Không kích hoạt'),

                Tables\Filters\TernaryFilter::make('is_default')
                    ->label('Mặc định')
                    ->placeholder('Tất cả')
                    ->trueLabel('Mặc định')
                    ->falseLabel('Không mặc định'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Xem'),
                Tables\Actions\EditAction::make()
                    ->label('Sửa'),
                Tables\Actions\Action::make('setDefault')
                    ->label('Đặt mặc định')
                    ->icon('heroicon-o-star')
                    ->color('warning')
                    ->action(function (AiPrompt $record) {
                        $record->setAsDefault();
                    })
                    ->requiresConfirmation()
                    ->visible(fn (AiPrompt $record) => !$record->is_default),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Xóa đã chọn'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAiPrompts::route('/'),
            'create' => Pages\CreateAiPrompt::route('/create'),
            'view' => Pages\ViewAiPrompt::route('/{record}'),
            'edit' => Pages\EditAiPrompt::route('/{record}/edit'),
        ];
    }
}
