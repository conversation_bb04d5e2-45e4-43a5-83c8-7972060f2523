<?php

namespace App\Filament\App\Resources\ProxyResource\Pages;

use App\Filament\App\Resources\ProxyResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;


use Filament\Forms\Components\FileUpload;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use App\Models\Proxy;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;

class ListProxies extends ListRecords
{
    protected static string $resource = ProxyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('importProxies')
            ->label('Import Proxies')
            ->form([
                Select::make('proxy_type')
                ->options([
                    'http' => 'HTTP',
                    'socks5' => 'Socks5',
                ])
                ->required()
                ->label('Proxy Type'),
                Textarea::make('proxies')
                    ->label('Proxies')->rows(15)
                    ->helperText('Định dạng IP:Port:User:Password')
                    ->placeholder("Enter each proxy on a new line.")
                    ->required(),
                    Textarea::make('note')
                    ->label('Note')->rows(2),
            ])
            ->action(function (array $data) {
                $lines = explode("\n", $data['proxies']);

                foreach ($lines as $line) {
                    $line = trim($line);
                    if (!empty($line)) {
                        Proxy::create([
                            'proxy' => $line,
                            'proxy_type' => $data['proxy_type'], // Hoặc lấy từ form import nếu cần
                            'user_id' => Auth::id(),
                            'note' => $data['note']
                        ]);
                    }
                }

                Notification::make()
                    ->title('Proxies Imported')
                    ->success()
                    ->body('The proxies have been imported successfully.')
                    ->send();
            }),
        ];
    }
}
