<?php

namespace App\Filament\App\Resources\DailyVideoAnalyticsResource\Pages;

use App\Filament\App\Resources\DailyVideoAnalyticsResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewDailyVideoAnalytics extends ViewRecord
{
    protected static string $resource = DailyVideoAnalyticsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
