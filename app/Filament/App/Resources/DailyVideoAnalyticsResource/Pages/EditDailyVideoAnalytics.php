<?php

namespace App\Filament\App\Resources\DailyVideoAnalyticsResource\Pages;

use App\Filament\App\Resources\DailyVideoAnalyticsResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDailyVideoAnalytics extends EditRecord
{
    protected static string $resource = DailyVideoAnalyticsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
