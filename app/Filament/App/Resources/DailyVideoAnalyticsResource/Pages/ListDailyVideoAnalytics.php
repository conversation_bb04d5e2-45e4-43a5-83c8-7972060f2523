<?php

namespace App\Filament\App\Resources\DailyVideoAnalyticsResource\Pages;

use App\Filament\App\Resources\DailyVideoAnalyticsResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Carbon\Carbon;

class ListDailyVideoAnalytics extends ListRecords
{
    protected static string $resource = DailyVideoAnalyticsResource::class;

    /**
     * Mount method để set filter mặc định
     */
    public function mount(): void
    {
        parent::mount();

        // Set filter mặc định cho create_time là hôm qua nếu chưa có filter nào được set
        if (!isset($this->tableFilters['create_time']['create_time'])) {
            $yesterday = now()->yesterday()->format('d/m/Y');
            $this->tableFilters['create_time'] = ['create_time' => $yesterday . ' - ' . $yesterday];
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            // Remove CreateAction since we can't create videos manually
        ];
    }
}
