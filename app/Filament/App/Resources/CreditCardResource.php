<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\CreditCardResource\Pages;
use Filament\Resources\Resource;
use App\Models\CreditCard;
use App\Enums\CardStatus;
use App\Enums\CardResult;
use App\Enums\CardBonStatus;
use App\Tables\Columns\TextAreaInputColumn;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\Layout\Panel;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Enums\ActionsPosition;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class CreditCardResource extends Resource
{
    protected static ?string $model = CreditCard::class;
    protected static ?string $navigationGroup = 'Tools';
    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('')->schema([
                    Forms\Components\Select::make('status')
                        ->options(CardStatus::class),
                    Forms\Components\Select::make('result')
                        ->options(CardResult::class),
                    Forms\Components\Select::make('status_bon')
                        ->options(CardBonStatus::class),

                    Select::make('user_id')
                        ->relationship('user', 'name')
                        ->searchable()
                        ->visible(function () {
                            $user = Auth::user();
                            return $user->hasRole(['super_admin']);
                        }),
                    Forms\Components\TextInput::make('price')
                        ->label('Price')
                        ->numeric()
                        ->default(0),
                    Toggle::make('checked'),
                    Forms\Components\TagsInput::make('tags')->label('Tags') ->suggestions([
                        'Tiktok',
                        'Etsy',
                        'Zazzle',
                    ])->required(),
                    Forms\Components\TagsInput::make('groups')->label('groups'),

                    Forms\Components\Textarea::make('card')->columnSpanFull()
                        ->label('Card')
                        ->required(),
                    Forms\Components\Textarea::make('note')->nullable()->columnSpanFull(),
                ])->columns(3)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            // ->recordClasses(fn (Model $record) => match ($record->status) {
            //     'add' => 'border-s-2 border-green-600 dark:border-green-300',
            //     'not_add' => 'border-s-2 border-red-600 dark:border-red-300',
            //     'sms' => 'border-s-2 border-blue-600 dark:border-blue-300',
            //     'unused' => 'opacity-30 bg-red-800',
            //     default => null,
            // })
            ->columns([
                // TextColumn::make('id')->searchable(),
                // TextColumn::make('status'),
                // TextColumn::make('result'),
                // TextColumn::make('status_bon'),
                // TextColumn::make('tags')->label('Tags')->badge(),
               
                // Tables\Columns\TextColumn::make('bin_base')->label('BIN')->searchable()->copyable(),
                // IconColumn::make('checked')->boolean(),
                // TextColumn::make('note'),
                // TextColumn::make('price')->money(),
                // TextColumn::make('created_at')->since(),


                Split::make([


                    SelectColumn::make('status')
                    
                    ->rules(['required'])->options(CardStatus::class)->label('Trạng thái')
                   // ->extraAttributes(['class' => 'w-[150px]'])
                    ->extraAttributes(function ($record) {
                        return [
                            'class' => ' w-[150px] '.$record->status->getCss(),
                        ];
                    }),
                    
                    SelectColumn::make('result')
                    ->extraAttributes(function ($record) {
                        $color = $record->result?->getCss() ?? 'text-gray-500';
                        return [
                            'class' => ' w-[150px] '.$color,
                        ];
                    })
                    ->options(CardResult::class)->label('Kết quả'),
                    SelectColumn::make('status_bon') ->rules(['required'])->options(CardBonStatus::class)->label('Trạng thái bòn')->extraAttributes(['class' => 'w-[150px]']),


                    Tables\Columns\TextColumn::make('user.name')->label('Người sử dụng'),
                    // Tables\Columns\TextColumn::make('bin')->label('BIN')->searchable(),

                    Tables\Columns\TagsColumn::make('tags')->label('Tags'),
                    TextColumn::make('price'),
                ]),
                Panel::make([
                    Split::make([
                        TextAreaInputColumn::make('card')->label('Card')->extraAttributes(['class' => 'w-full'])->searchable(),
                        TextAreaInputColumn::make('note')->label('Ghi chú')->extraAttributes(['class' => 'w-full'])->searchable(),

                    ]),
                ])->collapsed(false),
            ])
            ->filters([
                SelectFilter::make('status')->label('Trạng thái')
                    ->options(CardStatus::class),
                SelectFilter::make('result')->label('Kết quả')
                    ->options(CardResult::class),
                SelectFilter::make('status_bon')->label('Trạng thái bòn')
                    ->options(CardBonStatus::class),
            
                Filter::make('created_at')
                    ->form([
                        TextInput::make('tags'),
                    ])
                    ->query(function ( $query, array $data) {
                        if($data['tags']){
                            return $query->where('tags', 'like', '%' .   $data['tags'] . '%');
                        }
                        
                    })
            ])
            ->actions([
                EditAction::make()->slideOver()->label('')
            ])->selectable()
            //->actionsPosition(ActionsPosition::BeforeCells)
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCreditCards::route('/'),
            // 'create' => Pages\CreateCreditCard::route('/create'),
            // 'edit' => Pages\EditCreditCard::route('/{record}/edit'),
        ];
    }
}
