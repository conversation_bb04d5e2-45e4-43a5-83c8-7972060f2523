<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\TiktokSettlementResource\Pages;
use App\Models\TiktokSettlement;
use App\Tables\Columns\OrderItemsColumn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Count;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Filters\SelectFilter;
use NumberFormatter;
use Illuminate\Support\HtmlString;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class TiktokSettlementResource extends Resource
{
    protected static ?string $model = TiktokSettlement::class;
    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';
    protected static ?string $navigationGroup = 'TikTok Shop';
    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('store_id')
                    ->relationship('store', 'name')
                    ->required(),
                Forms\Components\TextInput::make('statement_id')
                    ->required(),
                Forms\Components\TextInput::make('transaction_id')
                    ->required(),
                Forms\Components\TextInput::make('tiktok_order_id')
                    ->required(),
                Forms\Components\TextInput::make('currency')
                    ->required(),
                Forms\Components\DateTimePicker::make('statement_time')
                    ->required(),
                Forms\Components\TextInput::make('revenue_amount')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('net_sales_amount')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('settlement_amount')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('fee_amount')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('shipping_cost_amount')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('adjustment_amount')
                    ->numeric()
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('store.name')
                    ->searchable()
                    ->sortable(),

                OrderItemsColumn::make('order')
                    ->disabledClick()
                    ->label('Order Items'),

                ViewColumn::make('revenue_amount')
                    ->label('Financial Summary')
                    ->view('tables.columns.financial-summary')
                    ->getStateUsing(function (TiktokSettlement $record) {
                        $revenue = $record->revenue_amount;
                        $fulfillmentCost = $record->order?->SupplierOrders->sum('base_cost') ?? 0;
                        $profit = $revenue - $fulfillmentCost;

                        $formatter = new NumberFormatter('en_US', NumberFormatter::CURRENCY);
                        $revenueFormatted = $formatter->formatCurrency($revenue, $record->currency);
                        $fulfillmentCostFormatted = $formatter->formatCurrency($fulfillmentCost, 'USD');
                        $profitFormatted = $formatter->formatCurrency($profit, $record->currency);

                        $profitColor = $profit >= 0 ? 'text-success-600' : 'text-danger-600';
                        $profitBg = $profit >= 0 ? 'bg-success-50' : 'bg-danger-50';
                        $profitIcon = $profit >= 0
                            ? '<svg class="w-3 h-3" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12 7a1 1 0 01-1 1H4a1 1 0 010-2h7a1 1 0 011 1zM4 11h7a1 1 0 110 2H4a1 1 0 110-2zm0 4h7a1 1 0 110 2H4a1 1 0 110-2z" clip-rule="evenodd"/></svg>'
                            : '<svg class="w-3 h-3" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12 13a1 1 0 01-1 1H4a1 1 0 110-2h7a1 1 0 011 1zM4 7h7a1 1 0 110 2H4a1 1 0 110-2zm0-4h7a1 1 0 110 2H4a1 1 0 010-2z" clip-rule="evenodd"/></svg>';

                        return new HtmlString("
                            <div class='flex flex-col gap-1'>
                                <div class='text-sm font-medium text-gray-900 dark:text-gray-100'>
                                    {$revenueFormatted}
                                </div>
                                
                                <div class='text-xs text-gray-500 dark:text-gray-400'>
                                    FF: {$fulfillmentCostFormatted}
                                </div>

                                <div class='inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full {$profitColor} {$profitBg}'>
                                    {$profitIcon}
                                    {$profitFormatted}
                                </div>
                            </div>
                        ");
                    })->summarize([
                        Sum::make()->money('USD')->label('Total Amount'),
                        Count::make()->label('Count'),
                    ]),

                Tables\Columns\TextColumn::make('statement_time')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('fee_amount')
                    ->money(fn ($record) => $record->currency)
                    ->sortable(),

                Tables\Columns\TextColumn::make('shipping_cost_amount')
                    ->money(fn ($record) => $record->currency)
                    ->sortable(),

                Tables\Columns\TextColumn::make('adjustment_amount')
                    ->money(fn ($record) => $record->currency)
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('seller_id')
                ->relationship(
                    'store.owner',
                    'name',
                    modifyQueryUsing: function (Builder $query) {
                        $user = auth()->user();
                        
                        if ($user->hasRole('super_admin')) {
                            return $query->role('Seller');
                        }
                        
                        if ($user->hasRole('Leader')) {
                            return $query->where(function ($query) use ($user) {
                                $query->role('Seller')
                                    ->where(function ($query) use ($user) {
                                        $query->where('id', $user->id)
                                            ->orWhereIn('id', $user->leaderManagedSellers()->pluck('users.id'));
                                    });
                            });
                        }
                        
                        if ($user->hasRole('Seller')) {
                            return $query->where('id', $user->id);
                        }
                        
                        return $query->whereRaw('1 = 0'); // Return empty if no matching role
                    }
                )
                ->multiple()
                ->preload()
                ->label('Seller'),

                SelectFilter::make('store_id')
                    ->relationship('store', 'name')
                    ->multiple()
                    ->preload()
                    ->label('Store'),

                SelectFilter::make('supplier')
                    ->relationship('order.SupplierOrders.supplier', 'name')
                    ->multiple()
                    ->preload()
                    ->label('Supplier'),

                DateRangeFilter::make('statement_time')
                    ->timezone('UTC')
                    ->displayFormat('D/M/Y')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->defaultSort('statement_time', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTiktokSettlements::route('/'),
            'create' => Pages\CreateTiktokSettlement::route('/create'),
            'edit' => Pages\EditTiktokSettlement::route('/{record}/edit'),
        ];
    }
}
