<?php

namespace App\Filament\App\Resources\MockupTemplateConfigResource\Pages;

use App\Filament\App\Resources\MockupTemplateConfigResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMockupTemplateConfigs extends ListRecords
{
    protected static string $resource = MockupTemplateConfigResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->slideOver(),
        ];
    }
}