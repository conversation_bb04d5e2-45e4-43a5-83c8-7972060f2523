<?php

namespace App\Filament\App\Resources\CreditCardResource\Pages;

use App\Enums\CardResult;
use App\Enums\CardStatus;
use App\Filament\App\Resources\CreditCardResource;
use App\Models\CreditCard;
use App\Models\User;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ListCreditCards extends ListRecords
{
    protected static string $resource = CreditCardResource::class;
    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('All Cards')
                ->modifyQueryUsing(fn (Builder $query) => $query)
        ];

        $statusTabs = collect(CardStatus::cases())->mapWithKeys(function ($status) {
            $statusName = $status->value;
            $statusLabel = $status->getLabel();
            $badgeColor = $status->getColor();

            return [
                strtolower($statusName) => Tab::make($statusLabel)
                    ->modifyQueryUsing(fn (Builder $query) => $query->where('status', $statusName))
                    ->badge(CreditCard::where('status', $statusName)->count())
                    ->badgeColor($badgeColor),
            ];
        })->toArray();

        return $tabs + $statusTabs;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('viewStats')
                ->label('View BIN Stats')
                ->color('warning')
                ->modalWidth('screen')
                ->modalHeading('BIN Base Statistics')
                ->modalFooterActions(null)
                ->modalSubmitAction(false)
                ->modalCancelAction(false)
                ->modalContent(function () {
                    return $this->getBinBaseStats();
                })->slideOver(),
            Actions\CreateAction::make()->slideOver()->color('gray'),
            Actions\Action::make('import')
                ->visible(function () {

                    return auth()->user()->hasRole(['super_admin']);
                })
                ->label('Import Cards')

                ->modalWidth('lg')
                ->modalHeading('Import Cards')
                ->form([
                    Textarea::make('cards')
                        ->label('Card Data')
                        ->rows('15')
                        ->placeholder('Paste your card data here, one card per line')
                        ->required(),
                    Select::make('user_id')
                        ->label('User')
                        ->options(User::all()->pluck('name', 'id'))
                        ->searchable()
                        ->required(),
                    Checkbox::make('active')
                        ->label('active')
                        ->default(false),
                    Checkbox::make('checked')
                        ->label('Checked')
                        ->default(false),
                ])->action(function (array $data) {
                    $this->importCards($data);
                })->color('gray'),
            Actions\Action::make('assignCards')
                ->visible(function () {

                    return auth()->user()->hasRole(['super_admin']);
                })
                ->label('Assign Cards')
                ->modalWidth('lg')
                ->modalHeading('Assign Cards to User')
                ->form([
                    Toggle::make('skip_check_bin')->reactive()->afterStateUpdated(function ($state, callable $set) {
                        $set('bin_base', null); // Reset bin_base when user changes
                        $set('available_quantity', 0); // Reset available quantity
                        $set('user_id', null);
                    }),
                    Select::make('user_id')
                        ->label('User')
                        ->options(User::role(['DarkMember', 'super_admin'])->get()->pluck('name', 'id'))
                        ->searchable()
                        ->required()
                        ->reactive()
                        ->afterStateUpdated(function ($state, callable $set) {
                            $set('bin_base', null); // Reset bin_base when user changes
                            $set('available_quantity', 0); // Reset available quantity

                        }),
                    Select::make('bin_base')
                        ->label('BIN Base')
                        ->options(function ($get) {
                            $skip_check_bin = $get('skip_check_bin');
                            if (!$skip_check_bin) {
                                $userId = $get('user_id');
                                if ($userId) {
                                    return CreditCard::where('user_id', $userId)
                                        ->distinct()->pluck('bin_base', 'bin_base');
                                }
                            }

                            return CreditCard::distinct()->pluck('bin_base', 'bin_base');
                        })
                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                            $userId = $get('user_id');
                            $availableCards = CreditCard::where(function ($query) use ($userId) {
                                $query->where('user_id', 1)
                                    ->orWhere('user_id', $userId);
                            })
                                ->where('active', false)
                                ->where('bin_base', $state)
                                ->count();

                            $set('available_quantity', $availableCards);
                        })
                        ->reactive()
                        ->searchable()
                        ->required(),
                    TextInput::make('available_quantity')
                        ->label('Available Quantity')
                        ->disabled()
                        ->reactive(),
                    TextInput::make('quantity')->numeric()
                        ->label('Quantity')->default(100)
                        ->required(),
                ])
                ->action(function (array $data) {
                    $this->assignCardsToUser($data);
                }),
        ];
    }

    public function importCards(array $data)
    {
        // Lấy thông tin từ form
        $cards = $data['cards'];
        $userId = $data['user_id'];
        $checked = $data['checked'];
        $active = $data['active'];
        // Chia nhỏ các dòng trong text area
        $lines = explode("\n", $cards);

        // Lặp qua từng dòng và thêm vào cơ sở dữ liệu
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line)) {
                // Giả định rằng mỗi dòng là thông tin thẻ đầy đủ
                CreditCard::create([
                    'card' => $line,
                    'user_id' => $userId,
                    'checked' => $checked,
                    'active' => $active,
       
                    'status' => 'unused',
                    'bin' => substr($line, 0, 6),
                    'bin_base' => substr($line, 0, 12),
                ]);
            }
        }

        // Thông báo thành công
        Notification::make()
            ->title('Cards imported successfully!')
            ->success()
            ->send();
    }
    protected function getBinBaseStats()
    {
        // Thống kê tất cả thời gian
        $statsAllTime = CreditCard::select('bin_base', CreditCard::raw('count(*) as total'))
            ->groupBy('bin_base')
            ->orderBy('bin_base', 'desc')
            ->get()
            ->map(function ($item) {
                $statusCounts = CreditCard::select('status', CreditCard::raw('count(*) as count'))
                    ->where('bin_base', $item->bin_base)
                    ->groupBy('status')
                    ->get()
                    ->mapWithKeys(function ($statusCount) {
                        return [$statusCount->status->getLabel() => $statusCount->count];
                    })
                    ->toArray();

                $resultCounts = CreditCard::select('result', CreditCard::raw('count(*) as count'))
                    ->whereNotNull('result')
                    ->where('bin_base', $item->bin_base)
                    ->groupBy('result')
                    ->get()
                    ->mapWithKeys(function ($resultCount) {
                        return [$resultCount->result->getLabel() => $resultCount->count];
                    })
                    ->toArray();

                $item->status_counts = $statusCounts;
                $item->result_counts = $resultCounts;

                return $item;
            });

        // Thống kê 7 ngày gần đây
        $oneWeekAgo = Carbon::now()->subDays(3);
        $statsLastWeek = CreditCard::select('bin_base', DB::raw('count(*) as total'))
            ->where('created_at', '>=', $oneWeekAgo)
            ->groupBy('bin_base')
            ->orderBy('bin_base', 'desc')
            ->get()
            ->map(function ($item) {
                $statusCounts = CreditCard::select('status', DB::raw('count(*) as count'))
                    ->where('bin_base', $item->bin_base)
                    ->where('created_at', '>=', Carbon::now()->subDays(3))
                    ->groupBy('status')
                    ->get()
                    ->mapWithKeys(function ($statusCount) {
                        return [$statusCount->status->getLabel() => $statusCount->count];
                    })
                    ->toArray();

                $resultCounts = CreditCard::select('result', DB::raw('count(*) as count'))
                    ->whereNotNull('result')
                    ->where('bin_base', $item->bin_base)
                    ->where('created_at', '>=', Carbon::now()->subDays(3))
                    ->groupBy('result')
                    ->get()
                    ->mapWithKeys(function ($resultCount) {
                        return [$resultCount->result->getLabel() => $resultCount->count];
                    })
                    ->toArray();

                $item->status_counts = $statusCounts;
                $item->result_counts = $resultCounts;

                return $item;
            });

        return view('filament.pages.bin-base-stats', ['statsAllTime' => $statsAllTime, 'statsLastWeek' => $statsLastWeek]);
    }

    public function assignCardsToUser(array $data)
    {
        $quantity = $data['quantity'];
        $binBase = $data['bin_base'];
        $userId = $data['user_id'];
        $availableCards = CreditCard::where(function ($query) use ($userId, $binBase) {
            $query->where('user_id', 1)
                ->where('bin_base', $binBase)
                ->orWhere(function ($query) use ($userId, $binBase) {
                    $query->where('user_id', $userId)
                        ->where('bin_base', $binBase);
                });
        })
            ->where('active', false)
            ->where('bin_base', $binBase)
            ->inRandomOrder()
            ->take($quantity)
            ->get();

        if ($availableCards->count() < $quantity) {
            $assignedCount = $availableCards->count();
            Notification::make()
                ->title('Not enough cards available')
                ->body("Only $assignedCount cards were assigned.")
                ->warning()
                ->send();
        } else {
            $assignedCount = $quantity;
            Notification::make()
                ->title('Cards assigned successfully!')
                ->success()
                ->send();
        }

        foreach ($availableCards as $card) {
            $card->update([
                'user_id' => $userId,
                'active' => true,
            ]);
        }

        if ($assignedCount < $quantity) {
            Notification::make()
                ->title('Not enough cards available')
                ->body("Only $assignedCount cards were assigned.")
                ->warning()
                ->send();
        } else {
            Notification::make()
                ->title('Cards assigned successfully!')
                ->success()
                ->send();
        }
    }
}
