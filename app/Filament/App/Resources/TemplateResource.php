<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\TemplateResource\Pages;
use App\Models\Template;
use Filament\Forms;
use Filament\Forms\Components\Livewire;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Icetalker\FilamentTableRepeater\Forms\Components\TableRepeater;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Filament\Forms\Components\View;
use Filament\Tables\Actions\Action;
use App\Livewire\AttributesManager;
use App\Livewire\SkusTable;
use Filament\Forms\Get;

class TemplateResource extends Resource
{
    protected static ?string $model = Template::class;
    protected static ?string $navigationIcon = 'heroicon-o-calculator';
    protected static ?string $navigationGroup = 'Products';
    protected static ?string $navigationLabel = 'Templates';
    public static function form(Form $form): Form
    {
        $json = File::get(public_path('categories.json'));
        $categories = json_decode($json, true)['data']['categories'];

        // Tổ chức categories thành cấu trúc của
        $categoryTree = [];
        $categoryMap = [];

        // Đầu tiên, tạo map của tất cả categories
        foreach ($categories as $category) {
            // Lấy tất cả categories có status AVAILABLE
            if (in_array('AVAILABLE', $category['permission_statuses'])) {
                $categoryMap[$category['id']] = [
                    'id' => $category['id'],
                    'name' => $category['local_name'],
                    'parent_id' => $category['parent_id'],
                    'is_leaf' => $category['is_leaf'],
                    'children' => [],
                ];
            }
        }

        // Sau đó, xây dựng cây
        foreach ($categoryMap as $id => $category) {
            if ($category['parent_id'] === "0") {
                // Nếu là root category, thêm vào categoryTree
                $categoryTree[] = &$categoryMap[$id];
            } else if (isset($categoryMap[$category['parent_id']])) {
                // Nếu có parent, thêm vào children của parent
                $categoryMap[$category['parent_id']]['children'][] = &$categoryMap[$id];
            }
        }

        // Hàm đệ quy để tạo options với indent, chỉ cho phép chọn leaf categories
        function buildSelectOptions($categories, $level = 0)
        {
            $options = [];
            $indent = str_repeat('─ ', $level);

            foreach ($categories as $category) {
                if ($category['is_leaf']) {
                    // Nếu là leaf category, cho phép chọn
                    $options[$category['id']] = $indent . $category['name'];
                } else {
                    // Nếu không phải leaf, chỉ hiển thị tên nhưng không cho chọn
                    $options[''] = $indent . $category['name'] . ' ▼';
                }

                if (!empty($category['children'])) {
                    $options += buildSelectOptions($category['children'], $level + 1);
                }
            }

            return $options;
        }

        $categoryOptions = buildSelectOptions($categoryTree);

        return $form->schema([
            Forms\Components\Tabs::make('Template Information')
                ->tabs([
                    Forms\Components\Tabs\Tab::make('General')
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->label('Template Name')
                                ->required(),
                            Forms\Components\Select::make('category_id')
                                ->label('Category')
                                ->options($categoryOptions)
                                ->searchable()
                                ->required(),
                            ToggleButtons::make('share')
                                ->label('Share this Template ?')
                                ->boolean()
                                ->inline(),
                                Forms\Components\FileUpload::make('size_chart')
                                ->label('Size Chart')
                                ->image()
                                ->imageEditor()
                                ->disk('s3')
                                ->required()
                                ->directory('templates/size-charts')
                                ->visibility('public')
                                ->imageResizeMode('cover')
                                ->imageResizeTargetWidth('1000')
                                ->imageResizeTargetHeight('1000')
                                ->helperText('Upload size chart image or enter URL')
                                ->columnSpanFull(),
                            Forms\Components\RichEditor::make('description')
                                ->toolbarButtons([
                                    'attachFiles',
                                    'blockquote',
                                    'bold',
                                    'bulletList',
                                    'codeBlock',
                                    'h2',
                                    'h3',
                                    'italic',
                                    'link',
                                    'orderedList',
                                    'redo',
                                    'strike',
                                    'underline',
                                    'undo',
                                ])
                                ->columnSpanFull()
                                ->required()
                                ->extraInputAttributes(['style' => 'min-height: 150px; max-height: 500px;']),
                      
                        ])->columns(3),

                    Forms\Components\Tabs\Tab::make('Attributes')
                        ->schema([
                            Forms\Components\Section::make()
                                ->schema([
                                    Forms\Components\Livewire::make(AttributesManager::class, fn (Get $get) => [
                                        'templateId' => $get('id')
                                    ])
                                    ->key(fn (Get $get): string => 'attributes-manager-' . $get('id'))
                                   
                                    ->columnSpanFull(),
                                ])
                        ])
                        ->reactive() ->visible(fn($record) => $record?->id),

                    Forms\Components\Tabs\Tab::make('Variants')
                        ->schema([
                            Forms\Components\Section::make()
                                ->schema([
                                    Forms\Components\Livewire::make(SkusTable::class, fn (Get $get) => [
                                        'templateId' => $get('id')
                                    ])
                                    ->key(fn (Get $get): string => 'skus-table-' . $get('id'))
                                    ->columnSpanFull()
                                  
                                    ->dehydrated(false),
                                ])->dehydrated(false) ,
                        ]) ->visible(fn($record) => $record?->id),

                    Forms\Components\Tabs\Tab::make('Shipping')
                        ->schema([
                            Forms\Components\TextInput::make('brand_id')
                                ->label('Brand ID')
                                ->default('7184176719711028997')
                                ->required(),
                            
                            Forms\Components\Section::make('Package Weight')
                                ->schema([
                                    Forms\Components\Select::make('package_weight.unit')
                                        ->label('Unit')
                                        ->options([
                                            'KILOGRAM' => 'Kilogram',
                                            'POUND' => 'Pound',
                                        ])
                                        ->default('KILOGRAM')
                                        ->required(),
                                    
                                    Forms\Components\TextInput::make('package_weight.value')
                                        ->label('Weight')
                                        ->default('0.4')
                                        
                                        ->required(),
                                ])->columns(2),

                            Forms\Components\Section::make('Package Dimensions')
                                ->schema([
                                    Forms\Components\Select::make('package_dimensions.unit')
                                        ->label('Unit')
                                        ->options([
                                            'CENTIMETER' => 'Centimeter',
                                            'INCH' => 'Inch',
                                        ])
                                        ->default('CENTIMETER')
                                        ->required(),
                                    
                                    Forms\Components\TextInput::make('package_dimensions.height')
                                        ->label('Height')
                                        ->default('5')
                                        
                                        ->required(),
                                        
                                    Forms\Components\TextInput::make('package_dimensions.length')
                                        ->label('Length')
                                        ->default('20')
                                        
                                        ->required(),
                                        
                                    Forms\Components\TextInput::make('package_dimensions.width')
                                        ->label('Width')
                                        ->default('20')
                                       
                                        ->required(),
                                ])->columns(2),
                        ]),
                ])->columnSpanFull(),
        ]);
    }

    public static function page(Pages\EditTemplate $page): Pages\EditTemplate
    {
        return $page
            ->afterForm([
                Forms\Components\Section::make('Variants')
                    ->schema([
                        Forms\Components\Placeholder::make('variants_table')
                            ->content(fn(?Template $record) => view(
                                'filament.templates.variants-table',
                                ['templateId' => $record?->id]
                            ))
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('name'),

            Tables\Columns\TextColumn::make('category_id'),
            Tables\Columns\TextColumn::make('user.name')->label('Created By'),
        ])
            ->actions([
                Tables\Actions\EditAction::make(),

                Tables\Actions\Action::make('duplicate')
                    ->label('Duplicate')
                    ->requiresConfirmation()
                    ->modalHeading('Confirm Duplication')
                    ->modalSubheading('Are you sure you want to duplicate this template?')
                    ->action(function (Template $record) {
                        $newRecord = $record->replicate();
                        $newRecord->name = $record->name . ' (Copy)';
                        $newRecord->save();
                    }),
            ])
            ->filters([
                // Add filters here if needed
            ]);
    }
    public function duplicateTemplate() {}
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTemplates::route('/'),
            'create' => Pages\CreateTemplate::route('/create'),
            'edit' => Pages\EditTemplate::route('/{record}/edit'),
        ];
    }

    protected static function generateCombinations($arrays)
    {
        if (empty($arrays)) {
            return [[]];
        }

        $result = [];
        $firstArray = array_shift($arrays);
        $combinationsOfRest = static::generateCombinations($arrays);

        foreach ($firstArray['values'] as $value) {
            foreach ($combinationsOfRest as $combination) {
                array_unshift($combination, ['attribute_name' => $firstArray['attribute_name'], 'value' => $value['value']]);
                $result[] = $combination;
            }
        }

        return $result;
    }

    protected static function generateVariants(Template $record, $productType = 'All', $priceAdjustment = 0, $includeSticker = false, $stickerPrice = 10)
    {
        // Define the product attributes and prices
        $types = [
            'Unisex T-Shirt' => [
                'colors' => ['BLACK', 'WHITE', 'SPORT GREY', 'SAND', 'FOREST GREEN', 'LIGHT PINK', 'NAVY', 'MILITARY GREEN', 'MAROON', 'RED', 'ROYAL BLUE', 'ASH GREY', 'LIGHT BLUE', 'CHARCOAL', 'DARK HEATHER', 'PURPLE', 'ORANGE'],
                'sizes' => ['S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL'],
                'prices' => ['S' => 25.68, 'M' => 25.68, 'L' => 25.68, 'XL' => 25.68, '2XL' => 27.68, '3XL' => 28.68, '4XL' => 30.18, '5XL' => 30.18],
            ],
            'Unisex Sweatshirt' => [
                'colors' => ['BLACK', 'WHITE', 'SPORT GREY', 'SAND', 'FOREST GREEN', 'LIGHT PINK', 'MILITARY GREEN', 'MAROON', 'RED', 'ROYAL BLUE', 'ASH GREY', 'ORANGE'],
                'sizes' => ['S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL'],
                'prices' => ['S' => 45.68, 'M' => 45.68, 'L' => 45.68, 'XL' => 45.68, '2XL' => 47.68, '3XL' => 49.68, '4XL' => 49.68, '5XL' => 51.68],
            ],
            'Unisex Hoodie' => [
                'colors' => ['BLACK', 'WHITE', 'SPORT GREY', 'SAND', 'FOREST GREEN', 'LIGHT PINK', 'NAVY'],
                'sizes' => ['S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL'],
                'prices' => ['S' => 47.68, 'M' => 47.68, 'L' => 47.68, 'XL' => 47.68, '2XL' => 49.68, '3XL' => 51.68, '4XL' => 51.68, '5XL' => 53.68],
            ],

        ];

        // Generate attributes based on the selected product type
        $attributes = [];
        if ($productType === 'All') {
            $attributes[] = [
                'attribute_name' => 'Style',
                'values' => array_map(fn($type) => ['value' => $type], array_keys($types)),
            ];
            $allColors = [];
            $allSizes = [];
            foreach ($types as $details) {
                $allColors = array_merge($allColors, $details['colors']);
                $allSizes = array_merge($allSizes, $details['sizes']);
            }
            $allColors = array_unique($allColors);
            $allSizes = array_unique($allSizes);
            $attributes[] = [
                'attribute_name' => 'Color',
                'values' => array_map(fn($color) => ['value' => $color], $allColors),
            ];
            $attributes[] = [
                'attribute_name' => 'Size',
                'values' => array_map(fn($size) => ['value' => $size], $allSizes),
            ];
        } else {
            $attributes[] = [
                'attribute_name' => 'Color',
                'values' => array_map(fn($color) => ['value' => $color], $types[$productType]['colors']),
            ];
            $attributes[] = [
                'attribute_name' => 'Size',
                'values' => array_map(fn($size) => ['value' => $size], $types[$productType]['sizes']),
            ];
        }

        // Generate combinations of attributes
        $combinations = static::generateCombinations($attributes);

        // Create SKUs based on combinations
        $skus = [];
        foreach ($combinations as $combination) {
            $type = $productType;
            $size = '';
            $color = '';

            foreach ($combination as $item) {
                if ($productType === 'All' && isset($types[$item['value']])) {
                    $type = $item['value'];
                } elseif (in_array($item['value'], $types[$type]['colors'])) {
                    $color = $item['value'];
                } elseif (in_array($item['value'], $types[$type]['sizes'])) {
                    $size = $item['value'];
                }
            }

            if ($type && $color && $size) {
                $price = $types[$type]['prices'][$size] ?? null;
                if ($price === null) {
                    continue;
                }

                // Adjust price
                $price += $priceAdjustment;

                $sku = $type . "-" . $color . "-" . $size;
                $salesAttributes = array_map(fn($item) => [
                    'name' => $item['attribute_name'],
                    'value_name' => $item['value'],
                ], $combination);

                $skus[] = [
                    'sku_code' => $sku,
                    'seller_sku' => $sku,
                    'price' => $price,
                    'stock' => 300,
                    'sales_attributes' => $salesAttributes,
                ];
            }
        }

        // Add the "Sticker" SKU if required
        if ($includeSticker) {
            if ($productType === 'All') {

                $skus[] = [
                    'sku_code' => 'Sticker-S',
                    'seller_sku' => 'Sticker-S',
                    'price' => $stickerPrice,
                    'stock' => 300,
                    'sales_attributes' => [
                        [
                            'name' => 'Style',
                            'value_name' => 'Unisex T-Shirt',
                        ],
                        [
                            'name' => 'Color',
                            'value_name' => 'Sticker',
                        ],
                        [
                            'name' => 'Size',
                            'value_name' => 'S',
                        ],
                    ],

                ];
            } else {
                $skus[] = [
                    'sku_code' => 'Sticker-S',
                    'seller_sku' => 'Sticker-S',
                    'price' => $stickerPrice,
                    'stock' => 300,
                    'sales_attributes' => [
                        [
                            'name' => 'Color',
                            'value_name' => 'Sticker',
                        ],
                        [
                            'name' => 'Size',
                            'value_name' => 'S',
                        ],
                    ],
                ];
            }
        }

        // Save attributes and update SKUs in the record
        $record->update(['attributes' => $attributes, 'skus' => $skus]);

        Notification::make()
            ->title('SKUs generated successfully!')
            ->success()
            ->send();
    }





    protected static function generateAttributes($productType = 'All')
    {
        $attributes = [];

        $types = [
            'T-shirt' => [
                'colors' => ['BLACK', 'WHITE', 'SPORT GREY', 'SAND', 'FOREST GREEN', 'LIGHT PINK', 'NAVY', 'MILITARY GREEN', 'MAROON', 'RED', 'ROYAL BLUE', 'ASH GREY', 'LIGHT BLUE', 'CHARCOAL', 'DARK HEATHER', 'PURPLE', 'ORANGE'],
                'sizes' => ['S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL'],
            ],
            'Hoodie' => [
                'colors' => ['BLACK', 'WHITE', 'SPORT GREY', 'SAND', 'FOREST GREEN', 'LIGHT PINK', 'NAVY'],
                'sizes' => ['S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL'],
            ],
            'Sweatshirt' => [
                'colors' => ['BLACK', 'WHITE', 'SPORT GREY', 'SAND', 'FOREST GREEN', 'LIGHT PINK', 'MILITARY GREEN', 'MAROON', 'RED', 'ROYAL BLUE', 'ASH GREY', 'ORANGE'],
                'sizes' => ['S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL'],
            ],
        ];

        if ($productType === 'All') {
            foreach ($types as $type => $details) {
                $attributes[] = [
                    'attribute_name' => $type,
                    'values' => array_map(fn($color) => ['value' => $color], $details['colors']),
                ];
                $attributes[] = [
                    'attribute_name' => 'Size',
                    'values' => array_map(fn($size) => ['value' => $size], $details['sizes']),
                ];
            }
        } else {
            $attributes[] = [
                'attribute_name' => $productType,
                'values' => array_map(fn($color) => ['value' => $color], $types[$productType]['colors']),
            ];
            $attributes[] = [
                'attribute_name' => 'Size',
                'values' => array_map(fn($size) => ['value' => $size], $types[$productType]['sizes']),
            ];
        }

        return $attributes;
    }
}
