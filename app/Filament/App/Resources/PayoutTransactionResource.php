<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\PayoutTransactionResource\Pages;
use App\Filament\App\Resources\PayoutTransactionResource\RelationManagers;
use App\Imports\PayoutTransactionsImport;
use App\Models\PayoutTransaction;
use App\Models\Store;
use Filament\Forms;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ImportAction;
use Filament\Tables\Columns\Summarizers\Count;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use App\Traits\HasUserFilter;

class PayoutTransactionResource extends Resource
{
    use HasUserFilter;
    protected static ?string $model = PayoutTransaction::class;
    public static function getNavigationGroup(): ?string
    {
        return __('Finance');
    }
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['super_admin', 'Super Accountant', 'Developer', 'Accountant']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                DateTimePicker::make('time')
                    ->required(),
                TextInput::make('currency')
                    ->required()
                    ->maxLength(3),
                TextInput::make('amount')
                    ->required()
                    ->numeric(),
                TextInput::make('transaction_id')
                    ->required()
                    ->unique(PayoutTransaction::class, 'transaction_id')
                    ->maxLength(255),
                TextInput::make('card_no')
                    ->required()
                    ->maxLength(255),
                TextInput::make('fee')
                    ->numeric()
                    ->nullable(),
                TextInput::make('net')
                    ->required()
                    ->numeric(),
                TextInput::make('type')
                    ->required()
                    ->maxLength(50),
                TextInput::make('from_to')
                    ->required()
                    ->maxLength(255),
                TextInput::make('status')
                    ->required()
                    ->maxLength(50),
                Textarea::make('note')
                    ->nullable(),
            ]);
    }


    public static function table(Table $table): Table
    {
        return $table->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('time')
                    ->label('Thời gian')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                TextColumn::make('currency')
                    ->label('Tiền tệ')
                    ->sortable(),
                TextColumn::make('amount')
                    ->label('Số tiền')
                    ->sortable()
                    ->summarize([
                        Sum::make()->money('USD')->label('Tổng tiền'),
                        Count::make()->label('Số lượng'),
                    ]),
                TextColumn::make('transaction_id')
                    ->label('Mã giao dịch')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('card_no')
                    ->label("Tài khoản ngân hàng")
                    ->searchable(),

                TextColumn::make('type')
                    ->label('Loại')
                    ->sortable(),
                TextColumn::make('store.name')
                    ->label('Cửa hàng'),
                TextColumn::make('store.owner.name')
                    ->label('Chủ cửa hàng'),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(),

            ])
            ->filters([
                Tables\Filters\SelectFilter::make('store')
                    ->relationship('store', 'name')->preload()
                    ->searchable(),
                    Tables\Filters\SelectFilter::make('store.owner.id')
                    ->relationship('store.owner', 'name')->preload()
                    ->searchable(),
                        // Date Range Filter for Create Time
                DateRangeFilter::make('created_at')
                    ->label('Created Date Range')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->alwaysShowCalendar()
                    ->displayFormat('DD/MM/YYYY')
                    ->separator(' - '),

                     DateRangeFilter::make('time')
                    ->label('Time Range')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->alwaysShowCalendar()
                    ->displayFormat('DD/MM/YYYY')
                    ->separator(' - '),
            ])
            ->actions([
              //  Tables\Actions\EditAction::make(),
            ])

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                   // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayoutTransactions::route('/'),
            'create' => Pages\CreatePayoutTransaction::route('/create'),
           // 'edit' => Pages\EditPayoutTransaction::route('/{record}/edit'),
        ];
    }
}
