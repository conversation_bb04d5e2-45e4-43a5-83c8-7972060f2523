<?php

namespace App\Filament\App\Resources\KeywordTrackingResource\Pages;

use App\Filament\App\Resources\KeywordTrackingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditKeywordTracking extends EditRecord
{
    protected static string $resource = KeywordTrackingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
