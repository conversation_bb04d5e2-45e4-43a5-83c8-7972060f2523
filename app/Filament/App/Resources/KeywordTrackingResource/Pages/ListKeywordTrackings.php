<?php

namespace App\Filament\App\Resources\KeywordTrackingResource\Pages;

use App\Enums\KeywordTrackingResult;
use App\Enums\KeywordTrackingStatus;
use App\Filament\App\Resources\KeywordTrackingResource;
use App\Filament\App\Widgets\KeywordTrackingGuideWidget;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListKeywordTrackings extends ListRecords
{
    protected static string $resource = KeywordTrackingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->slideOver(),
        ];
    }
    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('All')
                ->icon('heroicon-o-list-bullet')
                ->badge(KeywordTrackingResource::getModel()::count()),
        ];

        // Thêm tabs cho mỗi status
        foreach (KeywordTrackingStatus::cases() as $status) {
            $tabs[$status->value] = Tab::make($status->getLabel())
                ->icon($status->getIcon())
                ->badge(KeywordTrackingResource::getModel()::where('status', $status->value)->count())
                ->badgeColor($status->getColor())
                ->modifyQueryUsing(fn(Builder $query) => $query->where('status', $status->value));
        }

        // Thêm tabs cho mỗi result
        foreach (KeywordTrackingResult::cases() as $result) {
            $tabs[$result->value] = Tab::make($result->getLabel())
                ->icon($result->getIcon())
                ->badge(KeywordTrackingResource::getModel()::where('result', $result->value)->count())
                ->badgeColor($result->getColor())
                ->modifyQueryUsing(fn(Builder $query) => $query->where('result', $result->value));
        }

        return $tabs;
    }

    public function getDefaultActiveTab(): string | int | null
    {
        return 'pending';
    }
    protected function getHeaderWidgets(): array
    {
        return [
            KeywordTrackingGuideWidget::class,
        ];
    }
}
