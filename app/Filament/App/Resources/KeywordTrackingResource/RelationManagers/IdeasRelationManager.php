<?php

namespace App\Filament\App\Resources\KeywordTrackingResource\RelationManagers;

use App\Filament\App\Resources\IdeaResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class IdeasRelationManager extends RelationManager
{
    protected static string $relationship = 'ideas';

    public function form(Form $form): Form
    {
        return IdeaResource::form($form);
    }

    public function table(Table $table): Table
    {
        return IdeaResource::table($table)->headerActions([
            Tables\Actions\CreateAction::make()->slideOver(),
        ]);
    }
}
