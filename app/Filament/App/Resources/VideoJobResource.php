<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\VideoJobResource\Pages;
use App\Models\VideoJob;
use App\Enums\VideoJobStatus;
use App\Enums\VideoJobType;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\IconColumn;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Mohamedsabil83\FilamentFormsTinyeditor\Components\TinyEditor;
use Illuminate\Support\Facades\Storage;

class VideoJobResource extends Resource
{
    protected static ?string $model = VideoJob::class;
    protected static ?string $navigationIcon = 'heroicon-o-video-camera';
    protected static ?string $navigationGroup = 'Design & Media';

    protected static ?string $modelLabel = 'Video Job';
    protected static ?string $navigationLabel = 'Video Jobs';
    protected static ?string $slug = 'video-jobs';
    protected static ?int $navigationSort = 3;


    // hide in navbar
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        // Cột trái
                        Section::make('Basic Information')
                            ->columnSpan(1)
                            ->schema([
                                Forms\Components\TextInput::make('title')
                                    ->required()
                                    ->columnSpanFull(),
                                    
                                Forms\Components\ToggleButtons::make('status')
                                    ->inline()
                                    ->dehydrated()
                                    ->required()
                                    ->default(VideoJobStatus::PENDING->value)
                                    ->options(function (?VideoJob $record) {
                                        $user = Auth::user();
                                        $currentStatus = $record?->status;

                                        // Nếu là form tạo mới hoặc chưa có status
                                        if (!$record || !$currentStatus) {
                                            return [VideoJobStatus::PENDING->value => 'Pending'];
                                        }

                                        // Lấy roles từ database
                                        $userRoles = $user->getRoleNames()->toArray();

                                        $availableStatuses = $currentStatus->canTransitionTo($userRoles);

                                        return collect($availableStatuses)
                                            ->mapWithKeys(fn($status) => [
                                                $status->value => $status->getLabel()
                                            ])
                                            ->toArray();
                                    })
                                    ->inline()
                                    ->icons([
                                        'pending' => 'heroicon-o-clock',
                                        'assigned' => 'heroicon-o-user-plus',
                                        'in_progress' => 'heroicon-o-arrow-path',
                                        'under_review' => 'heroicon-o-eye',
                                        'needs_revision' => 'heroicon-o-pencil',
                                        'completed' => 'heroicon-o-check-circle',
                                        'cancelled' => 'heroicon-o-x-circle',
                                    ]),

                                // Video Editor Selection (Only for Seller/Manager)
                                Forms\Components\Radio::make('video_editor_id')
                                    ->label('Video Editor')
                                    ->options(function () {
                                        return User::role('Media')
                                            ->withCount(['videoJobs' => function ($query) {
                                                $query->whereIn('status', [
                                                    VideoJobStatus::PENDING,
                                                    VideoJobStatus::IN_PROGRESS,
                                                    VideoJobStatus::ASSIGNED
                                                ]);
                                            }])
                                            ->orderBy('video_jobs_count')
                                            ->get()
                                            ->mapWithKeys(function ($editor) {
                                                $statusColor = match (true) {
                                                    $editor->video_jobs_count > 10 => 'text-danger-600',
                                                    $editor->video_jobs_count > 5 => 'text-warning-600',
                                                    default => 'text-success-600',
                                                };

                                                return [
                                                    $editor->id => new HtmlString("
                                                        <div class='flex items-center gap-2'>
                                                            <span>{$editor->name}</span>
                                                            <span class='{$statusColor}'>
                                                                ({$editor->video_jobs_count} pending jobs)
                                                            </span>
                                                        </div>
                                                    ")
                                                ];
                                            });
                                    })
                                    ->visible(fn() => Auth::user()->hasRole(['Seller', 'super_admin','Fullfillment Manager']))
                                    ->columns(2)
                                    ->required(),

                                ToggleButtons::make('job_type')
                                    ->label('Job Type')
                                    ->options(array_combine(
                                        array_column(VideoJobType::cases(), 'value'),
                                        array_map(fn($case) => $case->getLabel(), VideoJobType::cases())
                                    ))
                                    ->colors([
                                        VideoJobType::PRODUCT_VIDEO->value => 'primary',
                                        VideoJobType::PROMOTIONAL_VIDEO->value => 'success',
                                        VideoJobType::TUTORIAL_VIDEO->value => 'warning',
                                        VideoJobType::UNBOXING_VIDEO->value => 'info',
                                        VideoJobType::TESTIMONIAL_VIDEO->value => 'purple',
                                        VideoJobType::ANIMATION->value => 'danger',
                                        VideoJobType::MOTION_GRAPHICS->value => 'orange',
                                        VideoJobType::VIDEO_EDITING->value => 'gray',
                                        VideoJobType::SLIDESHOW->value => 'cyan',
                                        VideoJobType::SOCIAL_MEDIA_VIDEO->value => 'pink',
                                    ])
                                    ->icons([
                                        VideoJobType::PRODUCT_VIDEO->value => 'heroicon-m-cube',
                                        VideoJobType::PROMOTIONAL_VIDEO->value => 'heroicon-m-megaphone',
                                        VideoJobType::TUTORIAL_VIDEO->value => 'heroicon-m-academic-cap',
                                        VideoJobType::UNBOXING_VIDEO->value => 'heroicon-m-gift',
                                        VideoJobType::TESTIMONIAL_VIDEO->value => 'heroicon-m-chat-bubble-left-right',
                                        VideoJobType::ANIMATION->value => 'heroicon-m-sparkles',
                                        VideoJobType::MOTION_GRAPHICS->value => 'heroicon-m-bolt',
                                        VideoJobType::VIDEO_EDITING->value => 'heroicon-m-scissors',
                                        VideoJobType::SLIDESHOW->value => 'heroicon-m-photo',
                                        VideoJobType::SOCIAL_MEDIA_VIDEO->value => 'heroicon-m-share',
                                    ])
                                    ->required()
                                    ->inline(),
                            ]),

                        // Cột phải
                        Section::make('Pricing & Details')
                            ->columnSpan(1)
                            ->schema([
                                Select::make('created_by')
                                    ->label('Seller')
                                    ->options(function () {
                                        return User::role('Seller')->get()->mapWithKeys(fn($seller) => [$seller->id => $seller->name]);
                                    })
                                    ->searchable()
                                    ->visible(fn() => Auth::user()->hasRole(['super_admin','Fullfillment Manager']))
                                    ->required(),
                                    
                                Forms\Components\TextInput::make('price')
                                    ->numeric()
                                    ->prefix('$')
                                    ->default(0)
                                    ->prefixIcon('heroicon-m-currency-dollar')
                                    ->visible(fn() => Auth::user()->hasRole(['super_admin', 
                                    'Fullfillment Manager','Fullfillment', 'Media'])),

                                Forms\Components\Toggle::make('is_rush')
                                    ->label('Rush Order')
                                    ->onIcon('heroicon-m-bolt')
                                    ->offIcon('heroicon-m-clock')
                                    ->onColor('danger')
                                    ->inline(),

                                Forms\Components\TextInput::make('rush_fee')
                                    ->numeric()
                                    ->prefix('$')
                                    ->visible(fn(Forms\Get $get) => $get('is_rush'))
                                    ->prefixIcon('heroicon-m-currency-dollar'),

                                // Video specific fields
                                Forms\Components\TextInput::make('duration')
                                    ->label('Duration (seconds)')
                                    ->numeric()
                                    ->suffix('sec')
                                    ->helperText('Estimated or actual video duration'),

                                Forms\Components\TextInput::make('resolution')
                                    ->label('Resolution')
                                    ->placeholder('1920x1080')
                                    ->helperText('Target video resolution'),

                                Forms\Components\Select::make('format')
                                    ->label('Video Format')
                                    ->options([
                                        'mp4' => 'MP4',
                                        'mov' => 'MOV',
                                        'avi' => 'AVI',
                                        'mkv' => 'MKV',
                                        'webm' => 'WebM',
                                    ])
                                    ->default('mp4'),

                                FileUpload::make('final_videos')
                                    ->label('Final Video Files')
                                    ->multiple()
                                    ->reorderable()
                                    ->downloadable()
                                    ->disk('s3')
                                    ->directory('video-jobs/finals')
                                    ->acceptedFileTypes(['video/*'])
                                    ->maxSize(100 * 1024) // 100MB
                                    ->maxFiles(5)
                                    ->helperText('Maximum file size: 100MB per file. Supported formats: MP4, MOV, AVI, MKV, WebM'),
                            ]),
                    ]),

                TinyEditor::make('description')
                    ->label('Description')
                    ->profile('simple2')
                    ->placeholder('Enter video job description')
                    ->fileAttachmentsDisk('s3')
                    ->fileAttachmentsDirectory('video-jobs/descriptions')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')->limit(20)
                    ->searchable(),

                Tables\Columns\TextColumn::make('videoEditor.name')
                    ->label('Video Editor')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('price')
                    ->money('USD')
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->money('USD')
                    ]),

                Tables\Columns\IconColumn::make('is_rush')
                    ->boolean()
                    ->label('Rush'),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(
                        fn($state) =>
                        $state instanceof VideoJobStatus
                            ? $state->getLabel()
                            : VideoJobStatus::from($state)->getLabel()
                    )
                    ->color(
                        fn($state) =>
                        $state instanceof VideoJobStatus
                            ? $state->getColor()
                            : VideoJobStatus::from($state)->getColor()
                    ),

                Tables\Columns\TextColumn::make('job_type')
                    ->badge()
                    ->formatStateUsing(
                        fn($state) =>
                        $state instanceof VideoJobType
                            ? $state->getLabel()
                            : VideoJobType::from($state)->getLabel()
                    ),

                Tables\Columns\TextColumn::make('duration')
                    ->label('Duration')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->getDurationFormatted();
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Time')
                    ->sortable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('job_type')
                    ->options(array_combine(
                        array_column(VideoJobType::cases(), 'value'),
                        array_map(fn($case) => $case->getLabel(), VideoJobType::cases())
                    ))
                    ->indicator('Job Type'),

                SelectFilter::make('video_editor')
                    ->relationship('videoEditor', 'name')
                    ->indicator('Video Editor'),

                SelectFilter::make('created_by')
                    ->relationship('creator', 'name')
                    ->label('Created By')
                    ->indicator('Creator'),

                DateRangeFilter::make('created_at'),

                Filter::make('is_rush')
                    ->toggle()
                    ->label('Rush Orders Only')
                    ->indicator('Rush Orders'),

                Filter::make('overdue')
                    ->toggle()
                    ->label('Overdue Jobs')
                    ->query(
                        fn(Builder $query): Builder =>
                        $query->where('deadline', '<', now())
                            ->whereNotIn('status', [
                                VideoJobStatus::COMPLETED->value,
                                VideoJobStatus::CANCELLED->value
                            ])
                    )
                    ->indicator('Overdue'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('complete')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->visible(fn($record) => $record->status !== VideoJobStatus::COMPLETED)
                    ->action(fn($record) => $record->markAsCompleted()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('assignVideoEditor')
                        ->icon('heroicon-o-user-plus')
                        ->form([
                            Forms\Components\Select::make('video_editor_id')
                                ->label('Select Video Editor')
                                ->options(function () {
                                    return User::role('Media')
                                        ->withCount(['videoJobs' => function ($query) {
                                            $query->whereIn('status', [
                                                VideoJobStatus::PENDING,
                                                VideoJobStatus::IN_PROGRESS,
                                                VideoJobStatus::ASSIGNED
                                            ]);
                                        }])
                                        ->get()
                                        ->mapWithKeys(fn($editor) => [
                                            $editor->id => "{$editor->name} ({$editor->video_jobs_count} pending jobs)"
                                        ]);
                                })
                                ->required(),
                        ])
                        ->action(function (Collection $records, array $data): void {
                            $records->each(function ($record) use ($data) {
                                $record->update([
                                    'video_editor_id' => $data['video_editor_id'],
                                    'status' => VideoJobStatus::ASSIGNED,
                                ]);
                            });
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVideoJobs::route('/'),
            'create' => Pages\CreateVideoJob::route('/create'),
            'edit' => Pages\EditVideoJob::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', VideoJobStatus::PENDING)->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return static::getModel()::where('status', VideoJobStatus::PENDING)->count() > 0
            ? 'warning'
            : 'primary';
    }
}
