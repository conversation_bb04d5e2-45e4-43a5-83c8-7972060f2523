<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\ProxyResource\Pages;
use App\Filament\App\Resources\ProxyResource\RelationManagers;
use App\Models\Proxy;
use App\Tables\Columns\TextAreaInputColumn;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Http;

class ProxyResource extends Resource
{
    protected static ?string $model = Proxy::class;
    protected static ?string $navigationGroup = 'Resources';
    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static bool $shouldRegisterNavigation = false;

    public static function getNavigationGroup(): ?string
    {
        return null;
    }

    public static function getNavigationIcon(): ?string
    {
        return null;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('proxy')->placeholder('IP:PORT:USER:PASSWORD')->helperText(' Nhập đúng định dạng IP:PORT:USER:PASSWORD')
                    ->required()
                    ->label('Proxy'),
                Forms\Components\Select::make('proxy_type')
                    ->options([
                        'http' => 'HTTP',
                        'socks5' => 'Socks5',
                    ])
                    ->required()
                    ->label('Proxy Type'),
                Forms\Components\Select::make('status')
                    ->options([
                        'Active' => 'Active',
                        'Inactive' => 'Inactive',
                    ])
                    ->default('Active')
                    ->required(),
                // Forms\Components\Textarea::make('proxy_info')
                //     ->label('Proxy Info'),
                Forms\Components\Textarea::make('note')
                    ->label('Note'),
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->required()
                    ->label('User')
                    ->default(auth()->id())
                    ->disabled(),
                Forms\Components\Select::make('share_id')
                    ->relationship('share', 'name')
                    ->label('Shared User'),
                Forms\Components\Select::make('store_id')
                    ->relationship('store', 'name')->disabled()
                    ->label('Store'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('status')->label('status')->badge(),
                TextInputColumn::make('proxy')->label('Proxy')->disabled(),
                Tables\Columns\TextColumn::make('proxy_type')->label('Type'),
                TextAreaInputColumn::make('proxy_info')->label('Proxy Info')->disabled()->extraAttributes(['class' => 'w-[180px]']),
               
                TextAreaInputColumn::make('note')->label('Note')->extraAttributes(['class' => 'w-[180px]']),
                // Tables\Columns\TextColumn::make('class_note')->label('Private Check')->limit(50),
                Tables\Columns\TextColumn::make('share.name')->label('Shared User'),
                Tables\Columns\TextColumn::make('store.name')->label('Store'),
                Tables\Columns\TextColumn::make('created_at')->label('Created At')->dateTime(),
            ])
            ->filters([
                Filter::make('shared')
                    ->query(fn (Builder $query): Builder => $query->where('share_id', auth()->id())),
                Filter::make('created')
                    ->query(fn (Builder $query): Builder => $query->where('user_id', auth()->id())),
            ])
            ->actions([
                Tables\Actions\Action::make('checkProxy')
                    ->label('Check Proxy')
                    ->action(function (Proxy $record) {
                        $proxy = $record->proxy;
                        $proxyType = $record->proxy_type;

                        // Extract IP, port, username, and password from proxy string
                        list($ip, $port, $user, $pass) = explode(':', $proxy);

                        $proxyUrl = ($proxyType == 'http' ? 'http' : 'socks5') . "://$user:$pass@$ip:$port";

                        try {
                            $response = Http::withOptions([
                                'proxy' => $proxyUrl,
                            ])->get('https://ip.smartproxy.com/json');

                            if ($response->successful()) {
                                $record->update([
                                    'proxy_info' => $response->body(),
                                    'status' => 'Active',
                                ]);
                                Notification::make()
                                    ->title('Proxy Check Successful')
                                    ->success()
                                    ->body('The proxy is active and information has been saved.')
                                    ->send();
                            } else {
                                $record->update(['status' => 'Inactive']);
                                Notification::make()
                                    ->title('Proxy Check Failed')
                                    ->danger()
                                    ->body('The proxy is inactive.')
                                    ->send();
                            }
                        } catch (\Exception $e) {
                            $record->update(['status' => 'Inactive']);
                            Notification::make()
                                ->title('Proxy Check Error')
                                ->danger()
                                ->body('An error occurred while checking the proxy.')
                                ->send();
                        }
                    })
                    ->icon('heroicon-o-check'),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // Define any relationships
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProxies::route('/'),
            // 'create' => Pages\CreateProxy::route('/create'),
            // 'edit' => Pages\EditProxy::route('/{record}/edit'),
        ];
    }
}
