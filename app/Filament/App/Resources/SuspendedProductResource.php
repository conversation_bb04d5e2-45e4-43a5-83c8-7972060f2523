<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\SuspendedProductResource\Pages;
use App\Models\SuspendedProduct;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;

class SuspendedProductResource extends Resource
{
    protected static ?string $model = SuspendedProduct::class;

    public static function getNavigationGroup(): ?string
    {
        return 'TikTok Shop';
    }

    protected static ?string $navigationLabel = 'Suspended Products';
    protected static ?string $navigationIcon = 'heroicon-o-exclamation-triangle';
    protected static ?int $navigationSort = 4;

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('product_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('store_id')
                    ->relationship('store', 'name')
                    ->required(),
                Forms\Components\Select::make('owner_id')
                    ->relationship('owner', 'name')
                    ->required(),
            ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('product_name')->sortable()->searchable()->limit(150),
                Tables\Columns\TextColumn::make('store.name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('owner.name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('created_at')->since()->sortable(),
            ])
            ->filters([
                // Add any filters if needed
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
               /// Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // Define any relations if needed
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSuspendedProducts::route('/'),
            // 'create' => Pages\CreateSuspendedProduct::route('/create'),
            // 'edit' => Pages\EditSuspendedProduct::route('/{record}/edit'),
        ];
    }
}