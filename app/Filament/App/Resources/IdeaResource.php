<?php

namespace App\Filament\App\Resources;

use App\Enums\IdeaStatus;
use App\Filament\App\Resources\IdeaResource\Pages;
use App\Models\Idea;
use App\Models\User;
use App\Services\OpenAIService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use App\Traits\HasUserFilter;
use Exception;

class IdeaResource extends Resource
{
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin']) ;
    }
    protected static bool $shouldRegisterNavigation = false;
    use HasUserFilter;
    protected static ?string $model = Idea::class;
    
    public static function getNavigationSort(): int
    {
        return 1;
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Ideas');
    }

    protected static ?string $navigationIcon = 'heroicon-o-light-bulb';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\ToggleButtons::make('status')
                    ->options(IdeaStatus::class)
                    ->default(IdeaStatus::New)
                    ->grouped()
                    ->columnSpanFull(),
                
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->label('Tiêu đề sản phẩm')
                    ->helperText('Nhập tiêu đề sản phẩm')
                    ->maxLength(255)
                    ->columnSpanFull(),
                
                Forms\Components\TextInput::make('link')
                    ->label('Link sản phẩm')
                    ->helperText('Nhập link gốc của sản phẩm (nếu có)')
                    ->url()
                    ->maxLength(255)
                    ->columnSpanFull(),
                
                Forms\Components\ToggleButtons::make('product_type')
                    ->label('Loại sản phẩm')
                    ->grouped()
                    ->options([
                        'hoodie' => 'Hoodie',
                        't-shirt' => 'T-Shirt',
                        'sweatshirt' => 'Sweatshirt',
                        'custom' => 'Custom',
                    ])
                    ->required()
                    ->columnSpanFull(),
                
                Forms\Components\TextInput::make('trademark')
                    ->label('Trademark')
                    ->helperText('Kết quả kiểm tra trademark')
                    ->maxLength(255)
                    ->dehydrated(false)
                    ->disabled()
                    ->columnSpanFull(),
                
                Forms\Components\Actions::make([
                    Action::make('checkTrademark')
                        ->label('Kiểm tra Trademark')
                        ->color('gray')
                        ->icon('heroicon-o-check-circle')
                        ->action(function (Get $get, Set $set) {
                            static::checkTrademark($get, $set);
                        }),
                ])->columnSpanFull(),
                
                Forms\Components\TextInput::make('note')
                    ->label('Đã đăng lên Store')
                    ->helperText('Ghi chú tên store đã đăng sản phẩm này')
                    ->maxLength(255)
                    ->columnSpanFull(),
                
                Forms\Components\TagsInput::make('tags')
                    ->label('Từ khóa')
                    ->helperText('Nhập các từ khóa liên quan đến sản phẩm')
                    ->separator(',')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('status')
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->description(function (Idea $record): string {
                        return $record->created_at->diffForHumans();
                    })
                    ->formatStateUsing(fn(IdeaStatus $state): string => $state->value),
                
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->wrap()
                    ->weight('medium')
                    ->description(function (Idea $record): string {
                        return $record->user->name;
                    }),
                
                Tables\Columns\TextColumn::make('product_type')
                    ->searchable()
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        $labels = [
                            'hoodie' => 'Hoodie',
                            't-shirt' => 'T-Shirt',
                            'sweatshirt' => 'Sweatshirt',
                            'custom' => 'Custom',
                        ];
                        
                        return $labels[$state] ?? $state;
                    }),
                
                Tables\Columns\TextColumn::make('link')
                    ->searchable()
                    ->url(fn ($record) => $record->link)
                    ->openUrlInNewTab()
                    ->wrap(),
                
                Tables\Columns\TextColumn::make('note')
                    ->label('Đã đăng ở')
                    ->searchable()
                    ->placeholder('-')
                    ->wrap(),
                
                Tables\Columns\TagsColumn::make('tags')
                    ->label('Từ khóa')
                    ->separator(',')
                    ->limit(3),
                
       
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(IdeaStatus::class)
                    ->label('Trạng thái'),
                
                SelectFilter::make('product_type')
                    ->options([
                        'hoodie' => 'Hoodie',
                        't-shirt' => 'T-Shirt',
                        'sweatshirt' => 'Sweatshirt',
                        'custom' => 'Custom',
                    ])
                    ->label('Loại sản phẩm'),
                
                Tables\Filters\Filter::make('has_store_note')
                    ->label('Đã đăng lên store')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('note')->where('note', '!=', '')),
                
                static::getUserFilter('user_id', 'Người tạo'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->label('')->slideOver(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    BulkAction::make('updateStatus')
                        ->label('Cập nhật trạng thái')
                        ->icon('heroicon-o-flag')
                        ->color('success')
                        ->form([
                            Forms\Components\Select::make('status')
                                ->label('Trạng thái')
                                ->options(IdeaStatus::class)
                                ->required()
                        ])
                        ->action(function (Collection $records, array $data) {
                            $records->each(function ($record) use ($data) {
                                $record->update([
                                    'status' => $data['status']
                                ]);
                            });

                            Notification::make()
                                ->title('Cập nhật thành công')
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    protected static function checkTrademark(Get $get, Set $set): void
    {
        if (!$get('title')) {
            static::notifyError('Vui lòng nhập tiêu đề');
            return;
        }

        try {
            $title = $get('title');
            $trademarks = \App\Models\Trademark::all()->pluck('keyword')->toArray();
            $foundTrademarks = [];
            
            foreach ($trademarks as $trademark) {
                $pattern = '/\b' . preg_quote($trademark, '/') . '\b/i';
                
                if (preg_match($pattern, $title)) {
                    $foundTrademarks[] = $trademark;
                }
            }
            
            if (empty($foundTrademarks)) {
                $set('trademark', 'Không tìm thấy từ khóa Trademark');
                Notification::make()
                    ->title('Không tìm thấy từ khóa Trademark')
                    ->success()
                    ->send();
            } else {
                $set('trademark', implode(', ', $foundTrademarks));
                Notification::make()
                    ->title('Tìm thấy ' . count($foundTrademarks) . ' từ khóa Trademark')
                    ->warning()
                    ->body('Trademark: ' . implode(', ', $foundTrademarks))
                    ->send();
            }
        } catch (Exception $e) {
            static::notifyError('Lỗi kiểm tra trademark: ' . $e->getMessage());
        }
    }

    protected static function notifyError(string $message): void
    {
        Notification::make()
            ->title('Lỗi')
            ->body($message)
            ->danger()
            ->send();
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIdeas::route('/'),
        ];
    }
}
