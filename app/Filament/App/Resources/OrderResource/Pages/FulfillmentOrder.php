<?php

namespace App\Filament\App\Resources\OrderResource\Pages;

use App\Filament\App\Resources\{OrderResource, SupplierOrderResource};
use App\Models\{Supplier, SupplierOrder};
use Filament\Forms\Components\{Grid, Section, TextInput};
use Filament\Forms\{Concerns\InteractsWithForms, Contracts\HasForms, Form};
use Filament\Notifications\Notification;
use Filament\Resources\Pages\{Page, Concerns\InteractsWithRecord};
use Filament\Tables\Actions\Action as ActionsAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\{Concerns\InteractsWithTable, Contracts\HasTable, Table};
use Livewire\Attributes\On;
use Parfaitementweb\FilamentCountryField\Forms\Components\Country;
use App\Fullfillment\FlashShip;
use App\Enums\SupplierOrderStatus;
use Illuminate\Support\Facades\Log;

class FulfillmentOrder extends Page implements HasForms, HasTable
{
    use InteractsWithRecord, InteractsWithForms, InteractsWithTable;

    protected static string $resource = OrderResource::class;
    protected static ?string $navigationGroup = 'Orders';
    protected static ?string $navigationParentItem = 'Orders';
    protected static bool $hasTitleCaseModelLabel = true;
    protected static string $view = 'filament.app.resources.order-resource.pages.fulfillment-order';

    public $suppliers;
    public string $selected_supplier = "Flashship";
    public ?array $data = [];

    private const SAVE_THROTTLE_MS = 2000;
    private const CACHE_TTL = 24 * 60 * 60;
    private const NOTIFICATION_DURATION = 1500;

    private static float $lastSaveTime = 0;

    #[On('updated-fulfill')]
    public function refreshForm(): void
    {
        try {
            // KHÔNG reset table để tránh refresh FlashshipForm
            // $this->resetTable();

            // Chỉ refresh record data trong background
            $this->record = $this->record->fresh();

            // Dispatch browser event để chỉ refresh table, KHÔNG touch FlashshipForm
            $this->dispatch('refresh-supplier-table');

            // Log để debug
            \Log::info('FulfillmentOrder refreshed without touching FlashshipForm', [
                'order_id' => $this->record->id ?? null
            ]);

        } catch (\Exception $e) {
            \Log::error('Error refreshing form in FulfillmentOrder', [
                'error' => $e->getMessage(),
                'order_id' => $this->record->id ?? null
            ]);
        }
    }

    public function updatedSelectedSupplier(): void
    {
        try {
            // Validate supplier exists
            if (!$this->hasSupplierForm($this->selected_supplier)) {
                \Log::warning("Supplier form not found for: {$this->selected_supplier}");
                $this->selected_supplier = 'Flashship';
            }

            // Không dispatch $refresh để tránh lỗi snapshot missing
            // Component sẽ tự động re-render khi selected_supplier thay đổi

        } catch (\Exception $e) {
            \Log::error('Error updating selected supplier', [
                'error' => $e->getMessage(),
                'supplier' => $this->selected_supplier,
                'order_id' => $this->record->id ?? null
            ]);

            // Fallback to Flashship
            $this->selected_supplier = 'Flashship';
        }
    }

    public function mount(int | string $record): void
    {
        $this->record = $this->resolveRecord($record);
        $this->suppliers = $this->getAvailableSuppliers();

        // Set default supplier to Flashship if available, otherwise first available
        $this->selected_supplier = $this->suppliers->contains('name', 'Flashship')
            ? 'Flashship'
            : $this->suppliers->first()?->name ?? 'Flashship';

        $this->form->fill($this->getRecord()->toArray());
    }

    protected function getEloquentQuery()
    {
        return static::getResource()::getEloquentQuery();
    }

    private function getAvailableSuppliers()
    {
        $availableForms = $this->getAvailableForms();

        return Supplier::select('id', 'name')
            ->whereIn('name', array_keys($availableForms))
            ->orderByRaw("FIELD(name, 'Flashship', 'Pressify')") // Flashship first, then Pressify, then others
            ->get();
    }

    private function getAvailableForms(): array
    {
        return [
            'Flashship' => 'FlashshipForm',
            'Pressify' => 'PressifyForm',
            // Thêm các supplier khác ở đây khi có form
        ];
    }

    public function getSuppliers()
    {
        if (!$this->suppliers) {
            $this->suppliers = $this->getAvailableSuppliers();
        }
        return $this->suppliers;
    }

    // Computed property để đảm bảo suppliers luôn có sẵn cho view
    public function getSuppliersProperty()
    {
        return $this->getSuppliers();
    }

    public function hasSupplierForm(string $supplierName): bool
    {
        $availableForms = $this->getAvailableForms();

        return array_key_exists($supplierName, $availableForms) &&
               class_exists("App\\Livewire\\{$availableForms[$supplierName]}");
    }
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Order Details')
                    ->icon('heroicon-o-shopping-bag')
                    ->schema([
                        Grid::make('Shipping')
                            ->schema($this->buildShippingFields())
                            ->columns(3)
                            ->columnSpan(['md' => 2]),
                    ])
            ])
            ->statePath('data')
            ->model($this->record);
    }

    private function buildShippingFields(): array
    {
        $fields = [
            'shipping_address_line1' => ['required' => true],
            'shipping_address_line2' => ['required' => false],
            'shipping_city' => ['required' => true],
            'shipping_region' => ['required' => true],
            'shipping_zip' => ['required' => true],
            'shipping_phone' => ['required' => false],
            'label' => ['required' => false],
            'shipping_first_name' => ['required' => false],
            'shipping_last_name' => ['required' => false],
            'shipping_email' => ['required' => false],
        ];

        $components = [];

        foreach ($fields as $fieldName => $config) {
            $component = TextInput::make($fieldName)
                ->live(onBlur: true)
                ->afterStateUpdated(fn () => $this->autoSaveOrder());

            if ($config['required']) {
                $component->required();
            }

            $components[] = $component;
        }

        // Add country field separately as it's different
        $components[] = Country::make('shipping_country')
            ->searchable()
            ->required()
            ->live()
            ->afterStateUpdated(fn () => $this->autoSaveOrder());

        return $components;
    }

    public function autoSaveOrder(): void
    {
        if (!$this->shouldSave()) return;

        try {
            $filteredData = $this->getValidOrderData();

            if (!$this->hasChanges($filteredData)) return;

            $this->record->fill($filteredData);
            $this->record->save();

            $this->showSuccessNotification();

        } catch (\Exception $e) {
            $this->handleSaveError($e);
        }
    }

    private function shouldSave(): bool
    {
        $currentTime = microtime(true) * 1000;
        if ($currentTime - self::$lastSaveTime < self::SAVE_THROTTLE_MS) {
            return false;
        }
        self::$lastSaveTime = $currentTime;
        return true;
    }

    private function getValidOrderData(): array
    {
        $validFields = [
            'seller_id', 'store_id', 'order_number', 'total', 'shipping_cost', 'status',
            'shipping_first_name', 'shipping_last_name', 'shipping_email', 'shipping_phone',
            'fulfillment_type', 'shipping_country', 'shipping_region', 'shipping_address_line1',
            'shipping_address_line2', 'shipping_city', 'shipping_zip', 'total_revenue',
            'store_order_status', 'label', 'note', 'order_code', 'handler_id', 'buyer_note'
        ];

        return array_intersect_key($this->data, array_flip($validFields));
    }

    private function hasChanges(array $filteredData): bool
    {
        foreach ($filteredData as $key => $value) {
            if ($this->record->getAttribute($key) != $value) {
                return true;
            }
        }
        return false;
    }

    private function showSuccessNotification(): void
    {
        Notification::make()
            ->success()
            ->title('✓ Đã tự động lưu')
            ->body('Thông tin đơn hàng đã được cập nhật')
            ->duration(self::NOTIFICATION_DURATION)
            ->send();
    }

    private function handleSaveError(\Exception $e): void
    {
        \Log::error('Failed to auto-save order in FulfillmentOrder', [
            'order_id' => $this->record->id,
            'error' => $e->getMessage(),
            'data_keys' => array_keys($this->data ?? [])
        ]);

        Notification::make()
            ->danger()
            ->title('⚠ Lỗi tự động lưu')
            ->body('Không thể lưu thay đổi. Vui lòng thử lại.')
            ->duration(4000)
            ->send();
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(SupplierOrder::where('order_id', $this->record->id))
            ->columns($this->getTableColumns())
            ->actions($this->getTableActions());
            // Tắt polling để tránh gây snapshot missing
    }

    private function getTableColumns(): array
    {
        return [
            TextColumn::make('order_code')
                ->label('Order Code')
                ->searchable()
                ->sortable()
                ->copyable()
                ->copyMessage('Order code copied!')
                ->copyMessageDuration(1500),
            TextColumn::make('supplier.name')
                ->label('Supplier')
                ->sortable(),
            TextColumn::make('status')
                ->badge()
                ->color(fn ($state) => match ($state) {
                    'Pending' => 'gray',
                    'InProducing' => 'warning',
                    'Completed' => 'success',
                    'Cancelled' => 'danger',
                    default => 'gray',
                }),
            TextColumn::make('base_cost')
                ->label('Cost')
                ->money('USD')
                ->sortable(),
            TextColumn::make('tracking_number')
                ->placeholder('No tracking')
                ->copyable(),
            TextColumn::make('supplier_order_id')
                ->label('Supplier Order ID')
                    ->searchable()
                    ->sortable()
                    ->placeholder('No supplier order ID')
                    ->copyable()
                    ->copyMessage('copied!')
                    ->copyMessageDuration(1500),
            TextColumn::make('created_at')
                ->label('Created')
                ->dateTime('d/m/Y H:i')
                ->description(fn ($record) => $record->created_at?->diffForHumans())
                ->sortable()
                ->toggleable(),
            TextColumn::make('updated_at')
                ->label('Updated')
                ->dateTime('d/m/Y H:i')
                ->description(fn ($record) => $record->updated_at?->diffForHumans())
                ->sortable()
                ->toggleable(),
        ];
    }

    private function getTableActions(): array
    {
        return [
            ActionsAction::make('edit_order_code')
                ->label('Edit Order Code')
                ->icon('heroicon-o-pencil')
                ->color('primary')
                ->button()
                ->visible(fn () => auth()->user()->hasRole('super_admin'))
                ->form([
                    TextInput::make('order_code')
                        ->label('Order Code')
                        ->required()
                        ->maxLength(255)
                        ->helperText('Chỉ Super Admin mới có thể chỉnh sửa Order Code')
                ])
                ->fillForm(fn (SupplierOrder $record): array => [
                    'order_code' => $record->order_code,
                ])
                ->action(function (array $data, SupplierOrder $record): void {
                    try {
                        $oldOrderCode = $record->order_code;
                        $record->update([
                            'order_code' => $data['order_code']
                        ]);

                        Notification::make()
                            ->success()
                            ->title('Order Code updated successfully')
                            ->body("Changed from '{$oldOrderCode}' to '{$data['order_code']}'")
                            ->send();

                        // Refresh table
                        $this->dispatch('updated-fulfill');
                    } catch (\Exception $e) {
                        Notification::make()
                            ->danger()
                            ->title('Failed to update Order Code')
                            ->body($e->getMessage())
                            ->send();
                    }
                }),
            ActionsAction::make('view')
                ->label('View')
                ->icon('heroicon-o-eye')
                ->button()
                ->url(fn (SupplierOrder $record): string =>
                    SupplierOrderResource::getUrl('view', ['record' => $record])
                ),
            ActionsAction::make('sync_status')
                ->icon('heroicon-m-arrow-path')
                ->color('warning')
                ->button()
                ->action(function (SupplierOrder $record) {
                    try {
                        $record->updateStatus();

                        Notification::make()
                            ->success()
                            ->title('Status synced successfully')
                            ->send();
                    } catch (\Exception $e) {
                        Log::error('Failed to sync status', [
                            'supplier_order_id' => $record->id,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        Notification::make()
                            ->danger()
                            ->title('Failed to sync status')
                            ->body($e->getMessage())
                            ->send();
                    }
                }),
            ActionsAction::make('cancel_order')
                ->icon('heroicon-m-x-mark')
                ->color('danger')
                ->button()
                ->visible(function (SupplierOrder $record) {
                    return $this->canCancelOrder($record);
                })
                ->requiresConfirmation()
                ->modalHeading('Cancel Order')
                ->modalDescription(function (SupplierOrder $record) {
                    return "Are you sure you want to cancel order {$record->supplier_order_id} from {$record->supplier->name}?";
                })
                ->action(function (SupplierOrder $record) {
                    try {
                        $response = null;
                        $supplierName = $record->supplier->name ?? 'Unknown';

                        // Handle different suppliers
                        switch ($supplierName) {
                            case 'Flashship':
                                $flashship = new FlashShip();
                                $response = $flashship->cancelOrder($record->supplier_order_id);

                                // Logging
                                Log::channel('flashship_cancel_orders')->info('Cancel order', [
                                    'supplier_order_id_db' => $record->id,
                                    'order_code' => $record->order_code,
                                    'supplier_order_id' => $record->supplier_order_id,
                                    'response' => $response
                                ]);

                                break;

                            case 'Pressify':
                                $response = \App\Fullfillment\Pressify::cancelOrderSafe($record->supplier_order_id);
                                break;

                            default:
                                throw new \Exception("Supplier '{$supplierName}' is not supported for cancellation");
                        }

                        if ($response && $response['success']) {
                            // Update status from supplier API
                            $record->updateStatus();

                            // Build success message based on supplier
                            $message = $this->buildCancelSuccessMessage($response, $supplierName);

                            Notification::make()
                                ->success()
                                ->title('Order cancelled successfully')
                                ->body($message)
                                ->send();

                            // Refresh table
                            $this->dispatch('updated-fulfill');
                        } else {
                            $errorMessage = $response['message'] ?? 'Unknown error occurred';

                            Notification::make()
                                ->danger()
                                ->title('Failed to cancel order')
                                ->body("Supplier: {$supplierName} - {$errorMessage}")
                                ->send();
                        }

                    } catch (\Exception $e) {
                        Notification::make()
                            ->danger()
                            ->title('Failed to cancel order')
                            ->body("Error: {$e->getMessage()}")
                            ->send();
                    }
                }),
        ];
    }

    public function updateStatus(SupplierOrder $record): void
    {
        $record->updateStatus();
    }

    /**
     * Build success message for cancel operation based on supplier response
     */
    private function buildCancelSuccessMessage(array $response, string $supplierName): string
    {
        $message = "Supplier: {$supplierName}";

        switch ($supplierName) {
            case 'Pressify':
                if (isset($response['operation_type'])) {
                    $operationType = ucfirst($response['operation_type']);
                    $message .= " - {$operationType} operation completed";
                }

                if (isset($response['order_codes']) && !empty($response['order_codes'])) {
                    $codes = implode(', ', $response['order_codes']);
                    $message .= " (Order codes: {$codes})";
                }

                if (isset($response['reject_note']) && !empty($response['reject_note'])) {
                    $message .= " - Reason: {$response['reject_note']}";
                }

                if (isset($response['processed_count'])) {
                    $message .= " - Processed: {$response['processed_count']} order(s)";
                }
                break;

            case 'Flashship':
                // FlashShip specific message formatting
                if (isset($response['message'])) {
                    $message .= " - {$response['message']}";
                } else {
                    $message .= " - Order cancelled successfully";
                }
                break;

            default:
                $message .= " - Order cancelled successfully";
                break;
        }

        return $message;
    }

    /**
     * Check if order can be cancelled based on supplier and status
     */
    private function canCancelOrder(SupplierOrder $record): bool
    {
        $supplierName = $record->supplier->name ?? '';

        // Check if supplier supports cancellation
        if (!in_array($supplierName, ['Flashship', 'Pressify'])) {
            return false;
        }

        // Check if order is already cancelled or completed
        if (in_array($record->status, [
            SupplierOrderStatus::Cancelled,
            SupplierOrderStatus::Completed,
            SupplierOrderStatus::Refunded
        ])) {
            return false;
        }

        // Supplier-specific checks
        switch ($supplierName) {
            case 'Pressify':
                // For Pressify, we could check the actual order status from API
                // but for performance, we'll use local status as indicator
                return in_array($record->status, [
                    SupplierOrderStatus::AwaitingShipment,
                    SupplierOrderStatus::OnHold
                ]);

            case 'Flashship':
                // FlashShip allows cancellation in more states
                return in_array($record->status, [
                    SupplierOrderStatus::AwaitingShipment,
                    SupplierOrderStatus::OnHold,
                    SupplierOrderStatus::InProducing
                ]);

            default:
                return false;
        }
    }
}
