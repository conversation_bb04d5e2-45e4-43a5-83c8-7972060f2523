<?php

namespace App\Filament\App\Resources\OrderResource\Pages;

use App\Enums\OrderStatus;
use App\Filament\App\Resources\OrderResource;
use App\Filament\Imports\OrderImporter;

use App\Models\Order;
use Filament\Actions;
use Filament\Actions\ImportAction;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use App\Services\LarkService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class ListOrders extends ListRecords
{
    protected static ?string $title = '';


    protected static string $resource = OrderResource::class;
    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('All Orders')
                ->modifyQueryUsing(fn (Builder $query) => $query->filterByRole()),
            
            'settled' => Tab::make('Settled')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->filterByRole()
                    ->where('settlement_amount', '>', 0)
                )
                ->badge(Order::filterByRole()->where('settlement_amount', '>', 0)->count())
                ->badgeColor('success'),

            'unsettled' => Tab::make('Unsettled')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->filterByRole()
                    ->where(function ($query) {
                        $query->whereNull('settlement_amount')
                            ->orWhere('settlement_amount', '=', 0);
                    })
                    ->where('status', '!=', OrderStatus::Cancelled)
                )
                ->badge(Order::filterByRole()
                    ->where(function ($query) {
                        $query->whereNull('settlement_amount')
                            ->orWhere('settlement_amount', '=', 0);
                    })
                    ->where('status', '!=', OrderStatus::Cancelled)
                    ->count())
                ->badgeColor('danger'),
        ];
    
        $statusTabs = collect(OrderStatus::cases())
            ->filter(function ($status) {
                // Exclude unwanted statuses
                return !in_array($status, [
                    OrderStatus::AwaitingShipment,
                    OrderStatus::OnHold,
                    OrderStatus::Refunded
                ]);
            })
            ->mapWithKeys(function ($status) {
                $statusName = $status->value;
                $statusLabel = $status->getLabel();
                $badgeColor = $status->getColor();
    
                return [
                    strtolower($statusName) => Tab::make($statusLabel)
                        ->modifyQueryUsing(fn (Builder $query) => $query->filterByRole()->where('status', $statusName))
                        ->badge(Order::filterByRole()->where('status', $statusName)->count())
                        ->badgeColor($badgeColor),
                ];
            })->toArray();
    
        return $tabs + $statusTabs;
    }
    public function getDefaultActiveTab(): string | int | null
    {
        return 'processing';
    }
    protected function getHeaderActions(): array
    {
        return [

        ];
    }

    /**
     * Kiểm tra xem seller đã được gửi thông báo cho order này trong 1 tiếng qua chưa
     */
    private function canSendNotification(int $orderId, int $sellerId): bool
    {
        $cacheKey = "lark_notification_order_{$orderId}_seller_{$sellerId}";
        return !Cache::has($cacheKey);
    }

    /**
     * Đánh dấu đã gửi thông báo cho seller về order này
     */
    private function markNotificationSent(int $orderId, int $sellerId): void
    {
        $cacheKey = "lark_notification_order_{$orderId}_seller_{$sellerId}";
        Cache::put($cacheKey, true, now()->addHour()); // Cache 1 tiếng
    }

    /**
     * Kiểm tra user có quyền gửi thông báo không
     */
    private function canUserSendNotification(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole(['super_admin', 'User Manager', 'Fullfillment Manager']);
    }

    /**
     * Gửi thông báo Lark cho seller về đơn hàng quá hạn chưa fulfill
     */
    public function sendLarkNotificationToSeller(int $orderId): void
    {
        // Kiểm tra quyền
        if (!$this->canUserSendNotification()) {
            Notification::make()
                ->title('Không có quyền')
                ->body('Bạn không có quyền gửi thông báo.')
                ->danger()
                ->send();
            return;
        }

        $record = Order::find($orderId);

        if (!$record) {
            Notification::make()
                ->title('Lỗi')
                ->body('Không tìm thấy đơn hàng.')
                ->danger()
                ->send();
            return;
        }

        // Kiểm tra điều kiện
        $daysDiff = (int) Carbon::parse($record->created_at)->diffInDays(now());
        $hasSupplierOrders = $record->SupplierOrders && $record->SupplierOrders->count() > 0;
        $isCompletedOrCancelled = in_array($record->status, [
            OrderStatus::Completed,
            OrderStatus::Cancelled,
            OrderStatus::Refunded
        ]);

        if ($daysDiff < 2 || $hasSupplierOrders || $isCompletedOrCancelled || !$record->seller_id) {
            Notification::make()
                ->title('Không thể gửi thông báo')
                ->body('Đơn hàng này không đủ điều kiện để gửi thông báo.')
                ->warning()
                ->send();
            return;
        }

        $larkService = app(LarkService::class);
        $sender = Auth::user();
        $seller = $record->seller;

        if (!$seller) {
            Notification::make()
                ->title('Lỗi')
                ->body('Đơn hàng này chưa có seller.')
                ->danger()
                ->send();
            return;
        }

        if (!$seller->hasLarkBotConfigured()) {
            Notification::make()
                ->title('Không thể gửi thông báo')
                ->body("Seller {$seller->name} chưa cấu hình Lark bot.")
                ->warning()
                ->send();
            return;
        }

        // Kiểm tra cache để tránh spam
        if (!$this->canSendNotification($record->id, $seller->id)) {
            Notification::make()
                ->title('Thông báo đã được gửi')
                ->body("Thông báo cho {$seller->name} về đơn hàng này đã được gửi trong vòng 1 tiếng qua. Vui lòng chờ trước khi gửi lại.")
                ->warning()
                ->send();
            return;
        }

        $daysDiff = (int) Carbon::parse($record->created_at)->diffInDays(now());
        $message = "🔔 THÔNG BÁO ĐÔN HÀNG QUÁ HẠN CHƯA FULFILL\n\n";
        $message .= "📦 Đơn hàng: {$record->order_code}\n";
        $message .= "⏰ Đã quá hạn: {$daysDiff} ngày\n";
        $message .= "📅 Ngày tạo: " . Carbon::parse($record->created_at)->format('d/m/Y H:i') . "\n";
        $message .= "💰 Tổng tiền: $" . number_format($record->total, 2) . "\n";
        $message .= "🏪 Store: " . ($record->store ? $record->store->name : 'N/A') . "\n";
        $message .= "📝 Trạng thái: " . $record->status->getLabel() . "\n\n";
        $message .= "Vui lòng xử lý fulfill đơn hàng này sớm nhất có thể!";

        try {
            $larkSend = $larkService->sendMessage($sender, $seller, $message);

            if ($larkSend->isSent()) {
                // Đánh dấu đã gửi thông báo
                $this->markNotificationSent($record->id, $seller->id);

                Notification::make()
                    ->title('Gửi thông báo thành công')
                    ->body("Đã gửi thông báo cho {$seller->name} qua Lark.")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Gửi thông báo thất bại')
                    ->body($larkSend->error_message ?? 'Có lỗi xảy ra khi gửi thông báo.')
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi hệ thống')
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
