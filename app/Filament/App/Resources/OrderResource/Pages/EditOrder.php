<?php

namespace App\Filament\App\Resources\OrderResource\Pages;

use App\Enums\DesignStatus;
use App\Enums\OrderItemStatus;
use App\Filament\App\Resources\OrderResource;
use App\Models\Order;
use App\Services\Tiktok\TiktokOrderSyncService;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class EditOrder extends EditRecord
{
    protected static string $resource = OrderResource::class;


    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load relationships để hiển thị thông tin chi tiết
        $this->record->load([
            'orderItems.product.design',
            'orderItems.productVariant',
            'store',
            'seller',
            'SupplierOrders'
        ]);

        return $data;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()
                    ->schema([
                        // Thông tin Order cơ bản
                        Section::make('Thông tin Order')
                            ->schema([
                                Grid::make(2)->schema([
                                    Forms\Components\TextInput::make('order_code')
                                        ->label('Mã đơn hàng')
                                        ->disabled(),
                                    Forms\Components\TextInput::make('order_number')
                                        ->label('Số đơn hàng')
                                        ->disabled(),
                                ]),
                                Grid::make(3)->schema([
                                    Forms\Components\Select::make('status')
                                        ->label('Trạng thái')
                                        ->options(\App\Enums\OrderStatus::class)
                                        ->required()
                                        ->disabled(),
                                    Forms\Components\TextInput::make('total')
                                        ->label('Tổng tiền')
                                        ->numeric()
                                        ->prefix('$')
                                        ->disabled()
                                        ->live()
                                        ->afterStateHydrated(function (Forms\Components\TextInput $component, $state) {
                                            // Tính toán tổng tiền từ order items khi load form
                                            $record = $component->getRecord();
                                            if ($record && $record->orderItems) {
                                                $total = $record->orderItems->sum(function ($item) {
                                                    return $item->quantity * $item->price;
                                                });
                                                $component->state($total);
                                            }
                                        }),
                                    Forms\Components\TextInput::make('shipping_cost')
                                        ->label('Phí ship')
                                        ->numeric()
                                        ->prefix('$')
                                        ->disabled(),
                                ]),
                                Forms\Components\Textarea::make('note')
                                    ->label('Ghi chú đơn hàng')
                                    ->rows(3)
                                    ->disabled(),

                                // Thông tin thời gian
                                Grid::make(2)->schema([
                                    Forms\Components\Placeholder::make('created_at')
                                        ->label('Thời gian tạo')
                                        ->content(function ($record) {
                                            if (!$record || !$record->created_at) {
                                                return 'Chưa có thông tin';
                                            }
                                            return $record->created_at->format('d/m/Y H:i:s') .
                                                   ' (' . $record->created_at->diffForHumans() . ')';
                                        }),
                                    Forms\Components\Placeholder::make('updated_at')
                                        ->label('Thời gian cập nhật')
                                        ->content(function ($record) {
                                            if (!$record || !$record->updated_at) {
                                                return 'Chưa có thông tin';
                                            }
                                            return $record->updated_at->format('d/m/Y H:i:s') .
                                                   ' (' . $record->updated_at->diffForHumans() . ')';
                                        }),
                                ]),
                            ]),

                        // Thông tin khách hàng
                        Section::make('Thông tin khách hàng')
                            ->schema([
                                Grid::make(2)->schema([
                                    Forms\Components\TextInput::make('shipping_first_name')
                                        ->label('Họ')
                                        ->disabled(),
                                    Forms\Components\TextInput::make('shipping_last_name')
                                        ->label('Tên')
                                        ->disabled(),
                                ]),
                                Grid::make(2)->schema([
                                    Forms\Components\TextInput::make('shipping_email')
                                        ->label('Email')
                                        ->email()
                                        ->disabled(),
                                    Forms\Components\TextInput::make('shipping_phone')
                                        ->label('Số điện thoại')
                                        ->disabled(),
                                ]),
                                Forms\Components\TextInput::make('shipping_address_line1')
                                    ->label('Địa chỉ 1')
                                    ->disabled(),
                                Forms\Components\TextInput::make('shipping_address_line2')
                                    ->label('Địa chỉ 2')
                                    ->disabled(),
                                Grid::make(3)->schema([
                                    Forms\Components\TextInput::make('shipping_city')
                                        ->label('Thành phố')
                                        ->disabled(),
                                    Forms\Components\TextInput::make('shipping_region')
                                        ->label('Tỉnh/Bang')
                                        ->disabled(),
                                    Forms\Components\TextInput::make('shipping_zip')
                                        ->label('Mã bưu điện')
                                        ->disabled(),
                                ]),
                                Forms\Components\TextInput::make('shipping_country')
                                    ->label('Quốc gia')
                                    ->disabled(),
                            ])
                            ->collapsible(),
                    ])
                    ->columnSpan(['lg' => 2]),

                Group::make()
                    ->schema([
                        Section::make('Thông tin Store & Seller')
                            ->schema([
                                Forms\Components\Select::make('store_id')
                                    ->label('Store')
                                    ->relationship('store', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->disabled(),
                                Forms\Components\Select::make('seller_id')
                                    ->label('Seller')
                                    ->relationship('seller', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->disabled(),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),

                // Order Items - Giao diện nhỏ gọn
                Section::make('Order Items')
                    ->schema([
                        Repeater::make('orderItems')
                            ->relationship()
                            ->schema([
                                // Layout nhỏ gọn trong 1 card
                                Grid::make(12)->schema([
                                    // Cột 1: Thông tin cơ bản (6 columns)
                                    Group::make([
                                        Grid::make(2)->schema([
                                            Forms\Components\TextInput::make('name')
                                                ->label('Tên sản phẩm')
                                                ->disabled()
                                                ->columnSpan(2),
                                            Forms\Components\TextInput::make('sku')
                                                ->label('SKU')
                                                ->disabled(),
                                            Forms\Components\Select::make('status')
                                                ->label('Trạng thái')
                                                ->options(OrderItemStatus::class)
                                                ->disabled(),
                                        ]),
                                        Grid::make(3)->schema([
                                            Forms\Components\TextInput::make('quantity')
                                                ->label('SL')
                                                ->numeric()
                                                ->disabled(),
                                            Forms\Components\TextInput::make('price')
                                                ->label('Giá')
                                                ->numeric()
                                                ->prefix('$')
                                                ->disabled(),
                                            Forms\Components\Placeholder::make('total_item')
                                                ->label('Tổng')
                                                ->content(function ($record) {
                                                    if ($record && $record->quantity && $record->price) {
                                                        return '$' . number_format($record->quantity * $record->price, 2);
                                                    }
                                                    return '$0.00';
                                                }),
                                        ]),
                                    ])->columnSpan(6),

                                    // Cột 2: Thông tin Product & Hình ảnh (6 columns)
                                    Group::make([
                                        Grid::make(2)->schema([
                                            Forms\Components\Placeholder::make('product_info')
                                                ->label('Product Info')
                                                ->content(function ($record) {
                                                    if (!$record || !$record->product) {
                                                        return 'Không có product';
                                                    }
                                                    $html = '<div class="space-y-1">';
                                                    $html .= '<div class="font-medium text-sm">' . htmlspecialchars($record->product->name) . '</div>';
                                                    $html .= '<div class="text-xs text-gray-500">ID: ' . $record->product->id . '</div>';

                                                    // Design status
                                                    if ($record->product->design) {
                                                        $design = $record->product->design;
                                                        $statusColor = $design->status->getColor();
                                                        $statusLabel = $design->status->getLabel();
                                                        $html .= '<span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-' . htmlspecialchars($statusColor) . '-100 text-' . htmlspecialchars($statusColor) . '-800">';
                                                        $html .= htmlspecialchars($statusLabel);
                                                        $html .= '</span>';
                                                    }
                                                    $html .= '</div>';
                                                    return new HtmlString($html);
                                                }),

                                            // Preview hình ảnh nhỏ
                                            Forms\Components\Placeholder::make('product_image_small')
                                                ->label('Hình ảnh')
                                                ->content(function ($record) {
                                                    if (!$record || !$record->product || !$record->product->image) {
                                                        return new HtmlString('<div class="w-16 h-16 bg-gray-100 rounded border flex items-center justify-center">
                                                            <span class="text-xs text-gray-400">No Image</span>
                                                        </div>');
                                                    }

                                                    $imageUrl = $record->product->image;
                                                    return new HtmlString('<img src="' . htmlspecialchars($imageUrl) . '"
                                                        alt="Product"
                                                        class="w-16 h-16 object-cover rounded border"
                                                        onerror="this.src=\'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=\';">');
                                                }),
                                        ]),

                                        // URL hình ảnh Product - chỉ trường này có thể chỉnh sửa
                                        Forms\Components\TextInput::make('product_image_url')
                                            ->label('URL Hình ảnh Product')
                                            ->url()
                                            ->suffixAction(
                                                Forms\Components\Actions\Action::make('viewImage')
                                                    ->icon('heroicon-o-eye')
                                                    ->url(fn ($state) => $state)
                                                    ->openUrlInNewTab()
                                                    ->visible(fn ($state) => !empty($state))
                                            )
                                            ->afterStateHydrated(function (Forms\Components\TextInput $component, $state, $record) {
                                                if ($record && $record->product && $record->product->image) {
                                                    $component->state($record->product->image);
                                                }
                                            })
                                            ->afterStateUpdated(function ($state, $record) {
                                                if ($record && $record->product && $state) {
                                                    $record->product->update(['image' => $state]);
                                                }
                                            })
                                            ->live()
                                            ->dehydrated(false),
                                    ])->columnSpan(6),
                                ]),
                            ])
                            ->itemLabel(fn (array $state): ?string => $state['name'] ?? 'Order Item')
                            ->addable(false)
                            ->deletable(false)
                            ->reorderable(false)
                            ->cloneable(false)
                            ->grid(2), // 2 items per row
                    ])
                    ->columnSpanFull(),
            ])
            ->columns(3);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Tính toán lại tổng tiền dựa trên order items
        if (isset($data['orderItems']) && is_array($data['orderItems'])) {
            $total = 0;
            foreach ($data['orderItems'] as $item) {
                if (isset($item['quantity']) && isset($item['price'])) {
                    $total += $item['quantity'] * $item['price'];
                }
            }
            $data['total'] = $total;
        }

        return $data;
    }

    protected function afterSave(): void
    {
        // Cập nhật lại tổng tiền sau khi save
        $this->record->updateTotal();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('sync_order')
                ->label('Sync Order')
                ->icon('heroicon-m-arrow-path')
                ->color('warning')
                ->button()
                ->visible(fn(Order $record) => !empty($record->order_number))
                ->action(function (Order $record) {
                    try {
                        // Load store với partnerApp relationship để tránh lỗi
                        $store = $record->store()->with('partnerApp')->first();
                        if (!$store) {
                            throw new \Exception('Store not found for order: ' . $record->order_code);
                        }
                        if (!$store->partnerApp) {
                            throw new \Exception('Partner App not configured for store: ' . $store->name);
                        }
                        if (empty($record->order_number)) {
                            throw new \Exception('Order number is required for syncing: ' . $record->order_code);
                        }

                        $tiktok = new TiktokOrderSyncService($store);
                        // Sync order cụ thể thay vì tất cả orders
                        $updatedOrder = $tiktok->syncSpecificOrder($record->order_number);

                        Notification::make()
                            ->title('Sync Order thành công')
                            ->body("Đơn hàng {$record->order_code} (#{$record->order_number}) đã được đồng bộ từ TikTok Shop")
                            ->success()
                            ->send();

                        // Refresh trang để hiển thị dữ liệu mới
                        redirect()->to(static::getResource()::getUrl('edit', ['record' => $record]));
                    } catch (\Exception $e) {
                        \Log::error('Sync order failed', [
                            'order_code' => $record->order_code ?? 'unknown',
                            'order_number' => $record->order_number ?? 'unknown',
                            'error' => $e->getMessage()
                        ]);

                        Notification::make()
                            ->title('Sync Order thất bại')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Action::make('Logs')
                ->label('Xem Logs')
                ->icon('heroicon-o-document-text')
                ->url(fn ($record) => OrderResource::getUrl('activities', ['record' => $record]))
                ->openUrlInNewTab(),

            Action::make('fulfillment')
                ->label('Fulfill Order')
                ->icon('heroicon-o-truck')
                ->color('success')
                ->url(fn ($record) => OrderResource::getUrl('fulfillment', ['record' => $record]))
                ->openUrlInNewTab(),

            Actions\DeleteAction::make()
                ->visible(fn (): bool => Auth::check() && Auth::user()->hasAnyRole(['super_admin', 'User Manager'])),
        ];
    }

    

    /**
     * Cập nhật tổng tiền order khi order items thay đổi
     */
    private function updateOrderTotal(Forms\Set $set, Forms\Get $get): void
    {
        $orderItems = $get('orderItems') ?? [];
        $total = 0;

        foreach ($orderItems as $item) {
            $quantity = $item['quantity'] ?? 0;
            $price = $item['price'] ?? 0;
            $total += $quantity * $price;
        }

        $set('total', $total);
    }
}
