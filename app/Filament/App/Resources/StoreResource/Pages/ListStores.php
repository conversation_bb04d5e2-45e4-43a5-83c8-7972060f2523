<?php

namespace App\Filament\App\Resources\StoreResource\Pages;

use App\Filament\App\Resources\StoreResource;
use App\Filament\App\Widgets\StoreManagementGuideWidget;
use App\Models\Store;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Enums\TiktokShopStatus;

class ListStores extends ListRecords
{
    protected static string $resource = StoreResource::class;

    public function getTabs(): array
    {
        $baseQuery = Store::query();

        return [
            
            'all' => Tab::make('All Stores')
                ->badge($baseQuery->count())
                ->badgeColor('primary'),
            
             'no_bank' => Tab::make('Có đơn hàng nhưn chưa add bank')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereHas('orders')
                    ->where(function($q) {
                        $q->whereNull('bank_account')
                          ->orWhere('bank_account', '');
                    })
                    ->where(function($q) {
                        $q->whereNull('card')
                          ->orWhere('card', '');
                    }))
                ->badge($baseQuery->clone()
                    ->whereHas('orders')
                    ->where(function($q) {
                        $q->whereNull('bank_account')
                          ->orWhere('bank_account', '');
                    })
                    ->where(function($q) {
                        $q->whereNull('card')
                          ->orWhere('card', '');
                    })
                    ->count())
                ->badgeColor('warning'),

            
            // TiktokShop Status Tabs - không cần check orders
            'live' => Tab::make('Live')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('tiktok_shop_status', TiktokShopStatus::Live))
                ->badge($baseQuery->clone()->where('tiktok_shop_status', TiktokShopStatus::Live)->count())
                ->badgeColor('success'),
            
            'not_connected' => Tab::make('Not Connected')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('tiktok_shop_status', TiktokShopStatus::NotConnected))
                ->badge($baseQuery->clone()->where('tiktok_shop_status', TiktokShopStatus::NotConnected)->count())
                ->badgeColor('warning'),
            
            'suspended' => Tab::make('Suspended')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('tiktok_shop_status', TiktokShopStatus::Suspended))
                ->badge($baseQuery->clone()->where('tiktok_shop_status', TiktokShopStatus::Suspended)->count())
                ->badgeColor('danger'),
            
            // Bank Status Tabs - cần check có orders
            'bank_verified' => Tab::make('Bank Verified')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereHas('orders')
                    ->whereNotNull('bank_account')
                    ->whereNotNull('card')
                    ->whereRaw('RIGHT(bank_account, 4) = RIGHT(card, 4)'))
                ->badge($baseQuery->clone()
                    ->whereHas('orders')
                    ->whereNotNull('bank_account')
                    ->whereNotNull('card')
                    ->whereRaw('RIGHT(bank_account, 4) = RIGHT(card, 4)')
                    ->count())
                ->badgeColor('success'),
                
            'bank_mismatch' => Tab::make('Bank Mismatch')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereHas('orders')
                    ->whereNotNull('bank_account')
                    ->whereNotNull('card')
                    ->whereRaw('RIGHT(bank_account, 4) != RIGHT(card, 4)'))
                ->badge($baseQuery->clone()
                    ->whereHas('orders')
                    ->whereNotNull('bank_account')
                    ->whereNotNull('card')
                    ->whereRaw('RIGHT(bank_account, 4) != RIGHT(card, 4)')
                    ->count())
                ->badgeColor('danger'),
                
            'Incomplete' => Tab::make('Bank Incomplete')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereHas('orders')
                    ->whereNull('bank_account'))
                ->badge($baseQuery->clone()
                    ->whereHas('orders')
                    ->whereNull('bank_account')
                    ->count())
                ->badgeColor('danger'),

           
            'no_tiktok_bank' => Tab::make('No TikTok Bank')
                ->modifyQueryUsing(fn (Builder $query) => $query
                    ->whereHas('orders')
                    ->whereNull('card'))
                ->badge($baseQuery->clone()
                    ->whereHas('orders')
                    ->whereNull('card')
                    ->count())
                ->badgeColor('danger'),

          
        ];
    }

 

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus'),
        ];
    }
}
