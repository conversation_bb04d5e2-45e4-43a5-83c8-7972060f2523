<?php

namespace App\Filament\App\Resources\StoreResource\Pages;

use App\Filament\App\Resources\StoreResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\Page;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use App\Models\Store;
use App\Models\Template;
use App\Services\Tiktok\TiktokShopService;
use EcomPHP\TiktokShop\Client;
use Filament\Forms\Components\Livewire;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;
use Icetalker\FilamentTableRepeater\Forms\Components\TableRepeater;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Imagick\Driver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class EditProduct extends Page implements Forms\Contracts\HasForms
{
    use Forms\Concerns\InteractsWithForms;
    use InteractsWithRecord;

    protected static string $resource = StoreResource::class;
    protected static string $view = 'filament.app.resources.store-resource.pages.edit-product';

    public $templateId;
    public $skus = [];
    public $warehouseId;
    public $warehouses = [];
    public $saveMode;
    public $totalProducts = 0;
    public $productStatus;
    public $productDescription;
    public $products = [];
    public $pageToken = null;
    public $nextPageToken = null;
    public $previousPageTokens = [];
    public $selectedProducts = [];
    public $selectAll = false;

    protected function getFormSchema(): array
    {
        return [
            Tabs::make('Product')
                ->tabs([
                    Tabs\Tab::make('General Information')
                        ->schema([
                            Select::make('templateId')
                                ->label('Chọn Template')
                                ->options(Template::all()->pluck('name', 'id'))
                                ->live()
                                ->afterStateUpdated(function (Set $set, $state) {
                                    $template = Template::find($state);

                                    if ($template) {
                                        $set('productDescription', $template->description);
                                        $set('productCategoryId', $template->category_id);
                                        $set('skus', $template->skus);
                                        $set('sizeChartImage', $template->size_chart);
                                    }
                                })
                                ->required(),
                            Select::make('warehouseId')
                                ->label('Kho Hàng')
                                ->options($this->warehouses)
                                ->required(),
                            Select::make('productStatus')
                                ->label('Product Status')
                                ->options([
                                    'ALL' => 'All',
                                    'DRAFT' => 'Draft',
                                    'PENDING' => 'Pending',
                                    'FAILED' => 'Failed',
                                    'ACTIVATE' => 'Activate',
                                    'SELLER_DEACTIVATED' => 'Seller Deactivated',
                                    'PLATFORM_DEACTIVATED' => 'Platform Deactivated',
                                    'FREEZE' => 'Freeze',
                                    'DELETED' => 'Deleted',
                                ])
                                ->required()
                                ->live()
                                ->afterStateUpdated(function (Set $set, $state) {
                                    $this->productStatus = $state;
                                    $this->loadTotalProducts();
                                }),
                            Select::make('saveMode')
                                ->label('Save Mode')
                                ->options([
                                    'AS_DRAFT' => 'As Draft',
                                    'LISTING' => 'Published',
                                ])
                                ->required(),
                                RichEditor::make('productDescription')
                                ->extraInputAttributes(['style' => 'max-height: 300px; overflow: scroll'])
                                ->toolbarButtons([
                                    'attachFiles', 'blockquote', 'bold', 'bulletList', 'codeBlock', 'h2', 'h3',
                                    'italic', 'link', 'orderedList', 'redo', 'strike', 'underline', 'undo',
                                ])->columnSpanFull()
                        ])->columns(3),
                ]),
        ];
    }

    public function mount(int | string $record): void
    {
        $this->record = $this->resolveRecord($record);
        $this->loadWarehouses($this->record);
        $this->form->fill([
            'templateId' => null,
            'productDescription' => '',
            'productCategoryId' => '',
            'skus' => [],
            'saveMode' => 'AS_DRAFT',
        ]);
    }

    public function loadTotalProducts()
    {
        $tiktokService = new TiktokShopService($this->record);
        try {
            $response = $tiktokService->getProductList(null, $this->productStatus);
            $this->totalProducts = $response['total_count'] ?? 0;
            $this->products = $response['products'] ?? [];
            $this->nextPageToken = $response['next_page_token'] ?? null;
            $this->previousPageTokens = [];
            $this->selectedProducts = [];
        } catch (\Exception $e) {
            Notification::make()
                ->title('Unable to load total products: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function loadWarehouses($store, $isEdit = false)
    {
        if ($store) {
            $tiktokService = new TiktokShopService($store);
            try {
                $warehouses = $tiktokService->getWarehouseList();
                $filteredWarehouses = array_filter($warehouses, function ($warehouse) {
                    return $warehouse['type'] === 'SALES_WAREHOUSE';
                });
                $warehouseOptions = collect($filteredWarehouses)->mapWithKeys(function ($warehouse) {
                    return [$warehouse['id'] => $warehouse['name']];
                })->toArray();
                $this->warehouses = $warehouseOptions;
            } catch (\Exception $e) {
                Notification::make()
                    ->title('Unable to load warehouses: ' . $e->getMessage())
                    ->danger()
                    ->send();
            }
        }
    }

    public function updateTemplate() {}

    public function loadProducts()
    {
        $tiktokService = new TiktokShopService($this->record);
        try {
            $response = $tiktokService->getProductList($this->pageToken, $this->productStatus);

            // Thêm logging để kiểm tra phản hồi từ API
            Log::info('Product List Response:', $response);

            // Loại bỏ danh sách SKU từ kết quả trả về
            $products = $response['products'] ?? [];
            foreach ($products as &$product) {
                unset($product['skus']);
            }

            $this->products = $products;
            $this->nextPageToken = $response['next_page_token'] ?? null;
            $this->selectedProducts = [];
            $this->selectAll = false;
        } catch (\Exception $e) {
            Notification::make()
                ->title('Unable to load products: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function previousPage()
    {
        if (!empty($this->previousPageTokens)) {
            $this->pageToken = array_pop($this->previousPageTokens);
            $this->loadProducts();
        }
    }

    public function nextPage()
    {
        if ($this->nextPageToken) {
            $this->previousPageTokens[] = $this->pageToken;
            $this->pageToken = $this->nextPageToken;
            $this->loadProducts();
        }
    }

    public function fixSku($productId)
    {
        if (!$this->templateId) {
            Notification::make()
                ->title('Please select a template first.')
                ->danger()
                ->send();
            return;
        }

        if (!$this->warehouseId) {
            Notification::make()
                ->title('Please select a warehouse first.')
                ->danger()
                ->send();
            return;
        }
        if (!$this->saveMode) {
            Notification::make()
                ->title('Please select a save mode first.')
                ->danger()
                ->send();
            return;
        }
        if (!$this->productDescription) {
            Notification::make()
                ->title('Please enter a product description first.')
                ->danger()
                ->send();
            return;
        }
        $template = Template::find($this->templateId);
        if (!$template) {
            Notification::make()
                ->title('Template not found.')
                ->danger()
                ->send();
            return;
        }

        $tiktokService = new TiktokShopService($this->record);
     
        try {
            $response = $tiktokService->updateProductSku($productId, $template->skus, $this->warehouseId, $this->productDescription ,$this->saveMode);
            Notification::make()
                ->title('SKU updated successfully for product ID: ' . $productId)
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Unable to update SKU: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function fixSelectedSkus()
    {
        if (!$this->templateId) {
            Notification::make()
                ->title('Please select a template first.')
                ->danger()
                ->send();
            return;
        }

        if (!$this->warehouseId) {
            Notification::make()
                ->title('Please select a warehouse first.')
                ->danger()
                ->send();
            return;
        }

        foreach ($this->selectedProducts as $productId) {
            $this->fixSku($productId);
        }
    }

    public function toggleProductSelection($productId)
    {
        if (in_array($productId, $this->selectedProducts)) {
            $this->selectedProducts = array_diff($this->selectedProducts, [$productId]);
        } else {
            $this->selectedProducts[] = $productId;
        }
    }

    public function toggleSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedProducts = array_column($this->products, 'id');
        } else {
            $this->selectedProducts = [];
        }
    }

    function replacePlaceholders($template, $replacements)
    {
        foreach ($replacements as $key => $value) {
            $template = str_replace('{{' . $key . '}}', $value, $template);
        }
        return $template;
    }
}