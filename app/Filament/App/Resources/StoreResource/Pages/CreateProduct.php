<?php

namespace App\Filament\App\Resources\StoreResource\Pages;

use App\Filament\App\Resources\StoreResource;
use Filament\Pages\Actions;
use Filament\Resources\Pages\Page;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use App\Models\Store;
use App\Models\Template;
use App\Services\Tiktok\TiktokShopService;
use EcomPHP\TiktokShop\Client;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;
use Icetalker\FilamentTableRepeater\Forms\Components\TableRepeater;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Imagick\Driver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Models\ProductToUpload;
use App\Enums\ProductUploadStatus;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Get;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\DateTimePicker;
use App\Services\Tiktok\ProductUploadService;
use Illuminate\Support\Str;
use App\Forms\Components\ImageGalleryField;

class CreateProduct extends Page implements Forms\Contracts\HasForms
{
    use Forms\Concerns\InteractsWithForms;
    use InteractsWithRecord;

    protected static string $resource = StoreResource::class;
    protected static string $view = 'filament.app.resources.store-resource.pages.create-product';

    public $templateId;
    public $productTitle;
    public $productDescription;
    public $productImages = [];
    public $sizeChartImage;
    public $productCategoryId;
    public $skuInfo = []; // Thông tin SKU
    public $skus = [];
    public $warehouseId; // ID kho hàng
    public $warehouses = [];
    public $saveMode; // Save mode
    public $total_skus = 0;
    public $price_range = '-';
    public $variants = '-';
    public $scheduled_at;
    public $productImageUrls = [];
    public $etsyUrl = '';

    protected function getFormSchema(): array
    {
        return [
            Forms\Components\Grid::make()
                ->schema([
                    // Left Column - Main Information (2/3 width)
                    Forms\Components\Section::make()
                        ->schema([
                            Forms\Components\Grid::make()
                                ->schema([
                                    Select::make('templateId')
                                        ->label('Template')
                                        ->options(function () {
                                            return Template::orderBy('created_at', 'desc')
                                                ->get()
                                                ->mapWithKeys(function ($template) {
                                                    // Đếm số SKU có stock > 0
                                                    $activeSkus = collect($template->skus)->filter(function ($sku) {
                                                        return ($sku['stock'] ?? 0) > 0;
                                                    })->count();

                                                    // Lấy product types và số lượng ca từng loại
                                                    $typeStats = collect($template->skus)
                                                        ->flatMap(function ($sku) {
                                                            return collect($sku['sales_attributes'] ?? [])->filter(function ($attr) {
                                                                return in_array($attr['value_name'], [
                                                                    'T-shirt',
                                                                    'Hoodie',
                                                                    'Sweatshirt',
                                                                    'Unisex T-Shirt',
                                                                    'Unisex Hoodie',
                                                                    'Unisex Sweatshirt'
                                                                ]);
                                                            });
                                                        })
                                                        ->groupBy('value_name')
                                                        ->map(function ($group) {
                                                            return $group->count();
                                                        })
                                                        ->map(function ($count, $type) {
                                                            return "{$type} ({$count})";
                                                        })
                                                        ->implode(', ');

                                                    // Tính price range
                                                    $prices = collect($template->skus)->pluck('price');
                                                    $priceRange = $prices->min() === $prices->max()
                                                        ? "\${$prices->min()}"
                                                        : "\${$prices->min()} - \${$prices->max()}";

                                                    // Format template name với thông tin chi tiết
                                                    $warningClass = $activeSkus > 300 ? 'text-danger-600' : '';

                                                    return [
                                                        $template->id => view('filament.components.template-option', [
                                                            'name' => $template->name,
                                                            'typeStats' => $typeStats,
                                                            'activeSkus' => $activeSkus,
                                                            'priceRange' => $priceRange,
                                                            'warningClass' => $warningClass,
                                                        ])->render()
                                                    ];
                                                });
                                        })
                                        ->allowHtml()
                                        ->searchable()
                                        ->live()
                                        ->afterStateUpdated(function ($state) {
                                            $template = Template::find($state);
                                            if ($template) {
                                                $this->form->fill([
                                                    'productDescription' => $template->description,
                                                    'productCategoryId' => $template->category_id,
                                                    'skus' => $template->skus,
                                                    'sizeChartImage' => $template->size_chart,
                                                ]);
                                                $this->updateSkuSummary($template->skus ?? []);

                                                // Hiển thị cảnh báo nếu quá 300 SKU
                                                $activeSkus = collect($template->skus)->filter(function ($sku) {
                                                    return ($sku['stock'] ?? 0) > 0;
                                                })->count();

                                                if ($activeSkus > 300) {
                                                    Notification::make()
                                                        ->title('Warning')
                                                        ->body("This template has {$activeSkus} SKUs. TikTok Shop only allows up to 300 SKUs per product.")
                                                        ->danger()
                                                        ->duration(10000)
                                                        ->persistent()
                                                        ->send();
                                                }
                                            }
                                        })
                                        ->required()
                                        ->columnSpan(2),

                                    TextInput::make('productTitle')
                                        ->label('Product Title')
                                        ->required()
                                        ->maxLength(255)
                                        ->columnSpan(2),

                                    // Thêm field import Etsy
                                    Forms\Components\TextInput::make('etsyUrl')
                                        ->label('Import from Etsy URL')
                                        ->placeholder('https://www.etsy.com/listing/...')
                                        ->suffixAction(
                                            Forms\Components\Actions\Action::make('importFromEtsy')
                                                ->icon('heroicon-m-arrow-down-tray')
                                                ->action('importFromEtsy')
                                        )
                                        ->columnSpan(2),
                                ])
                                ->columns(2),

                            RichEditor::make('productDescription')
                                ->label('Description')
                                ->toolbarButtons([
                                    'bold',
                                    'italic',
                                    'underline',
                                    'bulletList',
                                    'orderedList',
                                    'h2',
                                    'h3',
                                    'link',
                                ])
                                ->required()
                                ->columnSpanFull()
                                ->extraInputAttributes(['style' => 'max-height: 300px; overflow: scroll']),

                        ])
                        ->columnSpan(['lg' => 2]),

                    // Right Column - Summary (1/3 width)
                    Forms\Components\Section::make('Product Information')
                        ->schema([
                            Select::make('warehouseId')
                                ->label('Warehouse')
                                ->options($this->warehouses)
                                ->required(),

                            Select::make('saveMode')
                                ->label('Status')
                                ->options([
                                    'AS_DRAFT' => 'Draft',
                                    'LISTING' => 'Published',
                                ])
                                ->required()
                                ->placeholder('Select status'),

                            DateTimePicker::make('scheduled_at')
                                ->label('Schedule Upload Time')
                                ->timezone('Asia/Ho_Chi_Minh')
                                ->default(now()->addMinutes(5))
                                ->minDate(now()),
                            Placeholder::make('sizeChart')
                                ->label('Size Chart')
                                ->live()
                                ->content(function (Get $get) {
                                    $template = Template::find($get('templateId'));
                                    if (!$template || !$template->size_chart) {
                                        return 'No size chart available';
                                    }
                                    return new HtmlString(
                                        '<img src="' . Storage::disk('s3')->url($template->size_chart) . '" 
alt="Size Chart" class="max-w-full h-auto rounded-lg">'
                                    );
                                })
                        ])
                        ->columnSpan(['lg' => 1]),

                    ImageGalleryField::make('productImages')
                        ->label('Product Images')
                        ->columnSpanFull(),

                    Forms\Components\Hidden::make('skus'),
                    Forms\Components\Hidden::make('productCategoryId'),
                ])
                ->columns(['lg' => 3]),

            // Action Buttons
            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Actions::make([
                        Forms\Components\Actions\Action::make('uploadNow')
                            ->label('Upload Now')
                            ->action(function (array $data) {
                                $this->form->validate([
                                    'templateId' => ['required'],
                                    'productTitle' => ['required', 'string', 'max:255'],
                                    'productDescription' => ['required'],
                                    'warehouseId' => ['required'],
                                    'saveMode' => ['required'],
                                    //'scheduled_at' => ['nullable', 'date'],
                                ]);

                                $store = Store::find($this->record->id);

                                try {
                                    $data = $this->form->getState();

                                    // Create ProductToUpload record
                                    $uploadService = new ProductUploadService();
                            
                                    $response = $uploadService->uploadDirectly($store, [
                                        'template_id' => $data['templateId'],
                                        'product_title' => $data['productTitle'],
                                        'description' => $data['productDescription'],
                                        'warehouse_id' => $data['warehouseId'],
                                        'save_mode' => $data['saveMode'],
                                        'images' => $data['productImages'],
                                    ]);


                                    if ($response['product_id']) {
                                        $this->resetForm();
                                        Notification::make()
                                            ->success()
                                            ->body("Product uploaded successfully! Product ID: {$response['product_id']}")
                                            ->send();
                                    } else {
                                        throw new \Exception(isset($response['message']) ? $response['message'] : 'Unknown error');
                                    }
                                } catch (\Exception $e) {
                                    Notification::make()
                                        ->danger()
                                        ->title('Error')
                                        ->body('Unable to create product: ' . $e->getMessage())
                                        ->send();
                                }
                            })
                            ->color('primary')
                            ->size('lg')
                            ->icon('heroicon-o-cloud-arrow-up')
                            ->extraAttributes(['class' => 'px-6']),

                        Forms\Components\Actions\Action::make('scheduleUpload')
                            ->label('Schedule Upload')
                            ->action(function () {
                                $formData = $this->form->getState();

                                // Validate required fields
                                $this->form->validate([
                                    'templateId' => ['required'],
                                    'productTitle' => ['required', 'string', 'max:255'],
                                    'productDescription' => ['required'],
                                    'warehouseId' => ['required'],
                                    'saveMode' => ['required'],
                                    'scheduled_at' => ['required', 'date', 'after:now'],
                                ]);

                                // Gọi createProduct với isScheduled = true
                                $this->createProduct(true);
                            })
                            ->color('success')
                            ->size('lg')
                            ->icon('heroicon-o-clock')
                            ->extraAttributes(['class' => 'px-6']),
                    ])
                        ->alignment(\Filament\Support\Enums\Alignment::Center)
                        ->verticalAlignment(\Filament\Support\Enums\VerticalAlignment::Center),
                ])
                ->columnSpan('full'),
        ];
    }

    public function mount(int | string $record): void
    {
        $this->record = $this->resolveRecord($record);
        $this->loadWarehouses();
        $this->form->fill([
            'templateId' => null,
            'productTitle' => '',
            'productDescription' => '',
            'productCategoryId' => '',
            'skus' => [],
            'saveMode' => null,
            'productImageUrls' => [],
            'scheduled_at' => now()->addMinutes(5),
        ]);
    }

    public function loadWarehouses()
    {
        $store = Store::find($this->record->id);
        $tiktokService = new TiktokShopService($store);

        try {
            $warehouses = $tiktokService->getWarehouseList();
            $this->warehouses = collect($warehouses)
                ->filter(function ($warehouse) {
                    return $warehouse['type'] === 'SALES_WAREHOUSE';
                })
                ->mapWithKeys(function ($warehouse) {
                    return [$warehouse['id'] => $warehouse['name']];
                })
                ->toArray();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Unable to load warehouses: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function createProduct($isScheduled = false)
    {
        $data = $this->form->getState();
        $store = Store::find($this->record->id);

        try {
            // Validate images
            if (empty($data['productImages'])) {
                throw new \Exception('At least one product image is required');
            }
            // if (count($data['productImages']) > 9) {
            //     throw new \Exception('Maximum 9 images allowed in total');
            // }

            // Get size chart URL from template
            $template = Template::find($data['templateId']);
            $sizeChartUrl = null;
            if ($template && $template->size_chart) {
                $sizeChartUrl = Storage::disk('s3')->url($template->size_chart);
            }

            // Nếu là schedule thì tạo ProductToUpload
            if ($isScheduled) {
                $productToUpload = ProductToUpload::create([
                    'store_id' => $store->id,
                    'template_id' => $data['templateId'],
                    'product_title' => $data['productTitle'],
                    'description' => $data['productDescription'],
                    'warehouse_id' => $data['warehouseId'],
                    'save_mode' => $data['saveMode'],
                    'size_chart_url' => $sizeChartUrl,
                    'status' => ProductUploadStatus::Pending->value,
                    'scheduled_at' => $data['scheduled_at'],
                    'user_id' => $store->owner_id,
                    'images' => $data['productImages'],
                ]);

                Notification::make()
                    ->success()
                    ->body('Product will be uploaded at ' . $productToUpload->scheduled_at->format('Y-m-d H:i:s'))
                    ->send();

                $this->resetForm();
                return;
            }

            // Upload trực tiếp không qua ProductToUpload
            $uploadService = new ProductUploadService();
            $response = $uploadService->uploadDirectly($store, [
                'template_id' => $data['templateId'],
                'product_title' => $data['productTitle'],
                'description' => $data['productDescription'],
                'warehouse_id' => $data['warehouseId'],
                'save_mode' => $data['saveMode'],
                'images' => $data['productImages'],
            ]);

            if ($response['product_id']) {
                $this->resetForm();
                Notification::make()
                    ->success()
                    ->body("Product uploaded successfully! Product ID: {$response['product_id']}")
                    ->duration(10000)
                    ->persistent()
                    ->send();
            } else {
                throw new \Exception(isset($response['message']) ? $response['message'] : 'Unknown error');
            }
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Unable to create product: ' . $e->getMessage())
                ->duration(10000)
                ->persistent()
                ->send();
        }
    }

    public function updateSkuSummary($skus)
    {
        $activeSkus = array_filter($skus, function ($sku) {
            return $sku['stock'] > 0;
        });

        $this->total_skus = count($activeSkus);

        if ($this->total_skus > 300) {
            Notification::make()
                ->title('Warning')
                ->body('This product has ' . $this->total_skus . ' SKUs. TikTok Shop only allows up to 300 SKUs per product.')
                ->warning()
                ->persistent()
                ->send();
        }

        // Tính price range
        if (!empty($skus)) {
            $prices = array_map(function ($sku) {
                return is_array($sku['price']) ? $sku['price']['amount'] : $sku['price'];
            }, $skus);
            $min = min($prices);
            $max = max($prices);
            $this->price_range = $min === $max ? "\${$min}" : "\${$min} - \${$max}";
        } else {
            $this->price_range = '-';
        }

        // Lấy các loại sản phẩm
        $types = [];
        foreach ($skus as $sku) {
            foreach ($sku['sales_attributes'] as $attr) {
                if (in_array($attr['value_name'], ['T-shirt', 'Hoodie', 'Sweatshirt', 'Unisex T-Shirt', 'Unisex Hoodie', 'Unisex Sweatshirt'])) {
                    $types[$attr['value_name']] = true;
                }
            }
        }
        $this->variants = !empty($types) ? implode(', ', array_keys($types)) : '-';
    }

    protected function resetForm()
    {
        $this->form->fill([
            //'templateId' => null,
            'productTitle' => '',
            //'productDescription' => '',
            //'productCategoryId' => '',
            //'skus' => [],
            //'saveMode' => null,
            'productImages' => [],
        ]);
    }

    public function importFromEtsy()
    {
        try {
            // Chuẩn hóa URL - loại bỏ query params và chuyển về dạng chuẩn
            $url = $this->etsyUrl;
            $url = preg_replace('/\?.*/', '', $url); // Xóa tất cả query params
            $url = preg_replace('#^https?://(www\.)?etsy\.com/[a-z-]+/listing/#', 'https://www.etsy.com/listing/', $url);

            // Validate URL
            if (!Str::contains($url, 'etsy.com/listing/')) {
                throw new \Exception('Invalid Etsy URL. Please provide a valid listing URL');
            }

            // Extract listing ID
            if (!preg_match('/listing\/(\d+)/', $url, $matches)) {
                throw new \Exception('Could not find Etsy listing ID from URL');
            }

            $listingId = $matches[1];

            // Call Etsy API
            $response = Http::withHeaders([
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ])->get("https://www.etsy.com/api/v3/ajax/public/listings/{$listingId}");

            if (!$response->successful()) {
                throw new \Exception('Failed to fetch Etsy listing');
            }

            $data = $response->json();

            // Update form data
            $this->productTitle = $data['title'];
            //$this->productDescription = $data['description'] . "\n\nTags: " . implode(', ', $data['tags']);

            // Format image URLs correctly
            $this->productImages = collect($data['images'])->take(25)->toArray();

            Notification::make()
                ->success()
                ->title('Import Successful')
                ->body(sprintf('Imported product details with %d images', count($data['images'])))
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Import Failed')
                ->body($e->getMessage())
                ->send();

            \Log::error('Etsy import error: ' . $e->getMessage());
            \Log::error('Response data: ' . json_encode($data ?? null));
        }
    }
}
