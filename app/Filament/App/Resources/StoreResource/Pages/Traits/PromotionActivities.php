<?php

namespace App\Filament\App\Resources\StoreResource\Pages\Traits;

use App\Services\Tiktok\TiktokPromotion;
use Carbon\Carbon;

trait PromotionActivities
{
    public function loadActivities(array $params = []): void
    {
        $this->isLoadingActivities = true;
        $tiktokService = new TiktokPromotion($this->record);

        if ($this->pageToken > 0) {
            $params['page_token'] = (string)$this->pageToken;
        }

        $params['page_size'] = $this->pageSize;

        if ($this->status) {
            $params['status'] = $this->status;
        }

        if ($this->activityTitle) {
            $params['activity_title'] = $this->activityTitle;
        }

        try {
            $response = $tiktokService->searchActivities($params);
            $this->activities = $response['activities'];
            $this->totalResults = $response['total_count'];
        } catch (\Exception $e) {
            // Handle exception
            $this->errorMessage = 'Error loading activities: ' . $e->getMessage();
        } finally {
            $this->isLoadingActivities = false;
        }
    }

    public function getActivities(): array
    {
        return $this->activities;
    }
    public function deactivateActivity($activityId)
    {
        $this->isLoadingActivities = true;
        $tiktokService = new TiktokPromotion($this->record);

        try {
            $response = $tiktokService->deactivateActivity($activityId);
            
            if ($response && isset($response['activity_id'])) {
                $this->notification = [
                    'type' => 'success',
                    'message' => 'Activity deactivated successfully.'
                ];
                $this->loadActivities(); // Refresh the activities list
            } else {
                throw new \Exception('Failed to deactivate activity.');
            }
        } catch (\Exception $e) {
            $this->notification = [
                'type' => 'error',
                'message' => 'Error deactivating activity: ' . $e->getMessage()
            ];
        } finally {
            $this->isLoadingActivities = false;
        }
    }
    public function searchActivities(): void
    {
        $this->pageToken = 0;
        $this->loadActivities();
    }
    public function changePage($token){
        $this->pageToken = $token;
        $this->currentPage = $token === '' ? 1 : (intval($token) / $this->pageSize) + 1;
        $this->loadActivities();
    }
    public function createActivity(): void
    {
        $this->validate([
            'newActivity.title' => 'required|string',
            'newActivity.activity_type' => 'required|in:FIXED_PRICE,DIRECT_DISCOUNT,FLASHSALE',
            'newActivity.product_level' => 'required|in:PRODUCT,VARIATION',
            'newActivity.begin_time' => 'required|date',
            'newActivity.end_time' => 'required|date|after:newActivity.begin_time',
        ]);
        $this->isLoadingActivities = true;

        $tiktokService = new TiktokPromotion($this->record);

        $tiktokTimezone = 'America/Los_Angeles';
        $beginTime = Carbon::parse($this->newActivity['begin_time'])->timezone($tiktokTimezone);
        $endTime = Carbon::parse($this->newActivity['end_time'])->timezone($tiktokTimezone);

        try {
            $tiktokService->createActivity(
                $this->newActivity['title'],
                $this->newActivity['activity_type'],
                $beginTime->timestamp,
                $endTime->timestamp,
                $this->newActivity['product_level']
            );
            $this->reset('newActivity');
            $this->loadActivities();
        } catch (\Exception $e) {
            $this->errorMessage = 'An error occurred while creating the activity: ' . $e->getMessage();
        } finally {
            $this->isLoadingActivities = false;
        }
    }

    public function getActivityType()
    {
        $activityInfo = $this->getActivityInfo($this->selectedActivity);
        return $activityInfo['activity_type'] ?? 'FLASHSALE';
    }

    private function getActivityInfo($activityId)
    {
        $selectedActivity = collect($this->activities)->firstWhere('id', $activityId);

        if (!$selectedActivity) {
            return null;
        }

        return [
            'activity_type' => $selectedActivity['activity_type'],
            'begin_time' => $selectedActivity['begin_time'],
            'end_time' => $selectedActivity['end_time'],
            'product_level' => $selectedActivity['product_level'],
            'status' => $selectedActivity['status'],
            'title' => $selectedActivity['title'],
        ];
    }
}