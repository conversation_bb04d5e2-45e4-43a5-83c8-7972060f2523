<?php

namespace App\Filament\App\Resources\StoreResource\Pages\Traits;

use App\Services\Tiktok\TiktokPromotion;
use App\Services\Tiktok\TiktokShopService;
use Filament\Notifications\Notification;
use Livewire\Attributes\On;

trait PromotionProducts
{
    public function loadProducts()
    {
        $this->isLoadingProducts = true;
        $this->loadNextBatch();
    }
    
              
    public function createProductActivity($title, $type, $begin_time, $end_time, $product_level)
    {
        try {
            $tiktokService = new TiktokPromotion($this->record);
            $response = $tiktokService->createActivity($title, $type, $begin_time, $end_time, $product_level);
            
            if (isset($response['activity_id'])) {
                return $response['activity_id'];
            } else {
                throw new \Exception('Activity ID not found in response');
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error creating activity')
                ->body($e->getMessage())
                ->danger()
                ->send();
            return null;
        }
    }

    #[On('load-next-batch')]
    public function loadNextBatch()
    {
        if ($this->allProductsLoaded) {
            $this->isLoadingProducts = false;
            return;
        }

        $tiktokService = new TiktokShopService($this->record);
        try {
            $response = $tiktokService->getProductList($this->pageToken, 'ACTIVATE', 30);

            $newProducts = $response['products'] ?? [];

            $filteredProducts = array_map(function ($product) {
                return [
                    'id' => $product['id'],
                    'title' => $product['product_name'] ?? $product['title'] ?? '',
                    'image' => $product['images'][0] ?? null,
                    'price' => $product['price']['original_price'] ?? null,
                    'stock' => $product['stock_info']['available_stock'] ?? 0,
                    'selected' => in_array($product['id'], $this->selectedProducts),
                ];
            }, $newProducts);

            $this->products = array_merge($this->products, $filteredProducts);
            $this->updateProductSelectionStatus(); // Thêm dòng này
            $this->pageToken = $response['next_page_token'] ?? null;

            if (!$this->pageToken) {
                $this->allProductsLoaded = true;
                $this->isLoadingProducts = false;
            } else {
                $this->dispatch('load-next-batch');
            }
        } catch (\Exception $e) {
            $this->isLoadingProducts = false;
            Notification::make()
                ->title('Error loading products')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function previousProductPage()
    {
        if (!empty($this->previousPageTokens)) {
            $this->pageToken = array_pop($this->previousPageTokens);
            $this->loadProducts();
        }
    }

    public function nextProductPage()
    {
        if ($this->nextPageToken) {
            $this->previousPageTokens[] = $this->pageToken;
            $this->pageToken = $this->nextPageToken;
            $this->loadProducts();
        }
    }

    public function toggleProductSelection($productId)
    {
        if (in_array($productId, $this->selectedProducts)) {
            $this->selectedProducts = array_diff($this->selectedProducts, [$productId]);
        } else {
            $this->selectedProducts[] = $productId;
        }
    }

    private function updateTotalSelectedProducts()
    {
        $this->totalSelectedProducts = count($this->selectedProducts);
    }

    public function toggleSelectAll()
    {
       
        if ($this->selectAll) {
            $this->selectedProducts = array_column($this->products, 'id');
        } else {
            $this->selectedProducts = [];
        }
    }

    private function updateProductResult($productId, $result)
    {
        foreach ($this->products as &$product) {
            if ($product['id'] == $productId) {
                $product['result'] = $result['result'];
                $product['message'] = $result['message'];
                break;
            }
        }
    }
    private function updateProductSelectionStatus()
    {
        foreach ($this->products as &$product) {
            $product['selected'] = in_array($product['id'], $this->selectedProducts);
        }
    }
}