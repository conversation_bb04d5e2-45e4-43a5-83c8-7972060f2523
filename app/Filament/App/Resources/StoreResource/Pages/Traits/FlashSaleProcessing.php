<?php

namespace App\Filament\App\Resources\StoreResource\Pages\Traits;

use App\Services\Tiktok\TiktokPromotion;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On;
use Illuminate\Support\Str;

trait FlashSaleProcessing
{
    public function startProcessing()
    {
        $this->initializeProcessing();
        if (!empty($this->selectedProducts)) {
            $this->processBatch();
        } else {
            $this->finishProcessing('No products selected', 'warning');
        }
    }

    public function stopProcessing()
    {
        $this->isStopped = true;
        $this->finishProcessing('Processing stopped', 'warning');
    }

    public function createAutoFlashSale()
    {
        $this->validate([
            'flashSaleStartDate' => 'required|date',
            'flashSaleDiscountPercentage' => 'required|numeric|min:1|max:99',
        ]);

        $this->initializeProcessing();
        $this->processNextBatchForFlashSale();
    }

    #[On('process-next-batch')]
    public function onProcessNextBatch()
    {
        $this->processBatch();
    }

    #[On('process-next-batch-flash-sale')]
    public function onProcessNextBatchFlashSale()
    {
        $this->processNextBatchForFlashSale();
    }

    private function processBatch()
    {
        if ($this->shouldStopProcessing()) return;

        $batchProducts = $this->getNextBatch();

        foreach ($batchProducts as $productId) {
            if ($this->isStopped) break;
            $this->processProduct($productId);
            $this->updateProgress();
        }

        $this->currentBatchIndex++;

        if ($this->currentBatchIndex * $this->batchSize < count($this->selectedProducts)) {
            $this->dispatch('process-next-batch');
        } else {
            $this->finishProcessing('Processing completed', 'success');
        }
    }

    private function processNextBatchForFlashSale()
    {
        if ($this->shouldStopProcessing()) {
            $this->finishProcessing('Flash Sale processing completed', 'success');
            return;
        }

        $tiktokService = new TiktokPromotion($this->record);
        $batchProducts = $this->getNextBatch();

        try {
            $this->ensureValidFlashSaleActivity($tiktokService);

            foreach ($batchProducts as $productId) {
                if ($this->isStopped) break;
                $this->processFlashSaleProduct($productId, $tiktokService);
                $this->updateProgress();
            }

            $this->currentBatchIndex++;
            $this->dispatch('process-next-batch-flash-sale');
        } catch (\Exception $e) {
            $this->handleProcessingError($e);
        }
    }

    private function processProduct($productId)
    {
        $result = $this->addProductToPromotion($productId);
        $this->updateProductResult($productId, $result);
    }

    private function processFlashSaleProduct($productId, $tiktokService)
    {
        try {
            $result = $this->addProductToFlashSale($productId, $this->currentActivityId);
            $this->updateProductResult($productId, $result);

            if ($result['result'] === 'Success') {
                $this->productsInCurrentActivity++;
            } elseif ($this->isActivityFullError($result)) {
                $this->createNewFlashSaleActivity($tiktokService);
                $result = $this->addProductToFlashSale($productId, $this->currentActivityId);
                $this->updateProductResult($productId, $result);
                if ($result['result'] === 'Success') {
                    $this->productsInCurrentActivity++;
                }
            }
        } catch (\Exception $e) {
            $this->updateProductResult($productId, ['result' => 'Failed', 'message' => $e->getMessage()]);
        }
    }

    private function addProductToPromotion($productId)
    {
        $tiktokService = new TiktokPromotion($this->record);
        try {
            $response = $tiktokService->addProductToPromotion(
                $this->selectedActivity,
                $productId,
                $this->getActivityType(),
                $this->discountPercentage,
                -1,
                -1
            );

            if ($this->isSuccessResponse($response)) {
                $this->removeProductFromSelection($productId);
                return ['result' => 'Success', 'message' => $response['message'] ?? 'Product added successfully'];
            } else {
                return ['result' => 'Failed', 'message' => "API Error: " . $this->formatErrorMessage($response)];
            }
        } catch (\Exception $e) {
            Log::error('Error adding product to promotion: ' . $e->getMessage());
            return ['result' => 'Failed', 'message' => "Exception: {$e->getMessage()}"];
        }
    }

    private function addProductToFlashSale($productId, $activityId)
    {
        $tiktokService = new TiktokPromotion($this->record);
        try {
            $response = $tiktokService->addProductToPromotion(
                $activityId,
                $productId,
                'FLASHSALE',
                $this->flashSaleDiscountPercentage,
                -1,
                -1
            );

            if ($this->isSuccessResponse($response)) {
                return ['result' => 'Success', 'message' => $response['message'] ?? 'Product added successfully to Flash Sale'];
            } else {
                return ['result' => 'Failed', 'message' => "API Error: " . $this->formatErrorMessage($response)];
            }
        } catch (\Exception $e) {
            Log::error('Error adding product to Flash Sale: ' . $e->getMessage());
            return ['result' => 'Failed', 'message' => "Exception: {$e->getMessage()}"];
        }
    }

    private function createNewFlashSaleActivity($tiktokService)
    {
        try {
            $startDate = Carbon::parse($this->flashSaleStartDate)->setTimezone('America/Los_Angeles');
            $endDate = $startDate->copy()->addHours(72);

            $maxAttempts = 5;
            $attempt = 0;

            do {
                $uniqueIdentifier = Str::random(6);
                $activityName = 'Flash Sale ' . $startDate->format('Y-m-d H:i') . ' ' . $uniqueIdentifier;

                $response = $tiktokService->createActivity(
                    $activityName,
                    'FLASHSALE',
                    $startDate->timestamp,
                    $endDate->timestamp,
                    'VARIATION'
                );


                if (isset($response['activity_id'])) {
                    $this->currentActivityId = $response['activity_id'];
                    $this->productsInCurrentActivity = 0;
                    $this->activityName = $response['activity_name'] ?? $activityName;
                    $this->activityStatus = $response['activity_status'] ?? 'Unknown';
                    $this->loadActivities();
                    return;
                }

                $attempt++;
                sleep(2);
            } while ($attempt < $maxAttempts);

            throw new \Exception('Unable to create Flash Sale activity after multiple attempts');
        } catch (\Exception $e) {
            $this->handleProcessingError($e);
        }
    }

    private function initializeProcessing()
    {
        $this->processing = true;
        $this->progress = 0;
        $this->resetResults();
        $this->currentBatchIndex = 0;
        $this->isStopped = false;
        $this->currentActivityId = null;
        $this->productsInCurrentActivity = 0;
    }

    private function shouldStopProcessing()
    {
        return $this->isStopped || empty($this->selectedProducts);
    }

    private function updateProgress()
    {
        $totalProducts = count($this->selectedProducts);
        $processedProducts = $this->currentBatchIndex * $this->batchSize + $this->productsInCurrentActivity;
        $this->progress = min(100, ($processedProducts / $totalProducts) * 100);
    }

    private function finishProcessing($message, $type)
    {
        $this->processing = false;
        Notification::make()->title($message)->$type()->send();
    }

    private function getNextBatch()
    {
        $start = $this->currentBatchIndex * $this->batchSize;
        $end = min(($this->currentBatchIndex + 1) * $this->batchSize, count($this->selectedProducts));
        return array_slice($this->selectedProducts, $start, $end - $start);
    }

    private function ensureValidFlashSaleActivity($tiktokService)
    {
        if ($this->currentActivityId === null || $this->productsInCurrentActivity >= 30) {
            $this->createNewFlashSaleActivity($tiktokService);
        }
    }

    private function isSuccessResponse($response)
    {
        return is_array($response) && isset($response['result']) && $response['result'] === 'Success';
    }

    private function formatErrorMessage($response)
    {
        return is_array($response) ? json_encode($response) : 'Unexpected response format';
    }

    private function removeProductFromSelection($productId)
    {
        $this->selectedProducts = array_diff($this->selectedProducts, [$productId]);
        $this->totalSelectedProducts = count($this->selectedProducts);
    }

    private function isActivityFullError($result)
    {
        return strpos($result['message'], "The quantity of the products or the SKUs included in the activity exceeds") !== false;
    }

    private function handleProcessingError(\Exception $e)
    {
        Log::error('Error during Flash Sale processing:', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
        $this->isStopped = true;
        Notification::make()
            ->title('Processing Error')
            ->body('An error occurred during processing. Please try again or contact support.')
            ->danger()
            ->send();
    }
}