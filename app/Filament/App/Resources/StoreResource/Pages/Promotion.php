<?php

namespace App\Filament\App\Resources\StoreResource\Pages;

use App\Filament\App\Resources\StoreResource;
use Filament\Resources\Pages\Page;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Filament\App\Resources\StoreResource\Pages\Traits\PromotionActivities;
use App\Filament\App\Resources\StoreResource\Pages\Traits\PromotionProducts;
use App\Filament\App\Resources\StoreResource\Pages\Traits\FlashSaleProcessing;

class Promotion extends Page implements HasForms
{
    use InteractsWithForms;
    use InteractsWithRecord;
    use PromotionActivities;
    use PromotionProducts;
    use FlashSaleProcessing;

    protected static string $resource = StoreResource::class;
    protected static string $view = 'filament.app.resources.store-resource.pages.promotion';

    // Properties
    public $activities = [];
    public $pageToken = 0;
    public $status = '';
    public $activityTitle = '';
    public $totalResults = 0;
    public $pageSize = 20;
    public $errorMessage = '';
    public $selectedActivity = null;
    public $discountPercentage = 20;
    public $isLoadingActivities = false;
    public $isLoadingProducts = false;
    public $newActivity = [
        'title' => '',
        'activity_type' => '',
        'begin_time' => '',
        'end_time' => '',
        'product_level' => '',
    ];

    public $products = [];
    public $nextPageToken = null;
    public $previousPageTokens = [];
    public $selectedProducts = [];
    public $selectAll = false;
    public $processing = false;
    public $progress = 0;
    public $totalSelectedProducts = 0;
    public $batchSize = 2;
    public $currentBatchIndex = 0;
    public $notification = [];
    public $currentPage = 1;
    public $perPage = 30;
    public $isStopped = false;
    public $allProductsLoaded = false;
    public $flashSaleStartDate;
    public $flashSaleDiscountPercentage = 20;
    public $totalPages = 1;
    public $currentActivityId = null;
    public $productsInCurrentActivity = 0;

    public function mount(int | string $record): void
    {
        $this->record = $this->resolveRecord($record);
        $this->loadActivities();
        $this->selectedProducts = [];
        $this->flashSaleStartDate = Carbon::now()->addDay()->format('Y-m-d\TH:i');
    }

    public function getProductsProperty()
    {
        $collection = collect($this->products);
        $total = $collection->count();
        $currentPageItems = $collection->slice(($this->currentPage - 1) * $this->perPage, $this->perPage)->all();

        return new LengthAwarePaginator(
            $currentPageItems,
            $total,
            $this->perPage,
            $this->currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );
    }

    public function updatedCurrentPage()
    {
        $this->dispatch('pageChanged');
    }

    public function updatedSelectedActivity()
    {
        $this->reset(['pageToken', 'allProductsLoaded']);
    }

    private function resetResults()
    {
        foreach ($this->products as &$product) {
            $product['result'] = null;
        }
    }
}