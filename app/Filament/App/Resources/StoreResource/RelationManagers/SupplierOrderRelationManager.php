<?php

namespace App\Filament\App\Resources\StoreResource\RelationManagers;

use App\Filament\App\Resources\SupplierOrderResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SupplierOrderRelationManager extends RelationManager
{
    protected static string $relationship = 'supplierOrder';

    public function form(Form $form): Form
    {
        return SupplierOrderResource::form($form);
    }

    public function table(Table $table): Table
    {
        return SupplierOrderResource::table($table);
    }
}