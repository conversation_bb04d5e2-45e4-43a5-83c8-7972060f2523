<?php

namespace App\Filament\App\Resources\StoreResource\RelationManagers;

use App\Filament\App\Resources\PayoutTransactionResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PayoutRelationManager extends RelationManager
{
    protected static string $relationship = 'payout';

    public function form(Form $form): Form
    {
        return PayoutTransactionResource::form($form);
        return $form
            ->schema([
                Forms\Components\TextInput::make('bank_account')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return PayoutTransactionResource::table($table);

    }
}
