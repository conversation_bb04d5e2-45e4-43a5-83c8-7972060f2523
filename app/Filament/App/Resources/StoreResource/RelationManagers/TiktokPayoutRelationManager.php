<?php

namespace App\Filament\App\Resources\StoreResource\RelationManagers;

use App\Filament\App\Resources\TikTokPaymentResource;
use App\Filament\App\Resources\TiktokPayoutTransactionResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TiktokPayoutRelationManager extends RelationManager
{
    protected static string $relationship = 'tiktokPayout';

    public function form(Form $form): Form
    {
        return TikTokPaymentResource::form($form);
    }

    public function table(Table $table): Table
    {
        return TikTokPaymentResource::table($table);
    }
}