<?php

namespace App\Filament\App\Resources\FlashSaleResource\Pages;

use App\Filament\App\Resources\FlashSaleResource;
use App\Models\FlashSale;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Notifications\Notification;
use Filament\Resources\Components\Tab;

class ListFlashSales extends ListRecords
{
    protected static string $resource = FlashSaleResource::class;

    public function getTabs(): array
    {
        $statuses = [
            'ONGOING' => [
                'label' => 'Ongoing',
                'icon' => 'heroicon-o-play',
                'color' => 'success'
            ],
            'PRODUCT UPDATE' => [
                'label' => 'Product Update',
                'icon' => 'heroicon-o-arrow-path',
                'color' => 'warning'
            ],
            'DUPLICATED' => [
                'label' => 'Duplicated',
                'icon' => 'heroicon-o-document-duplicate',
                'color' => 'danger'
            ],
            'DEACTIVATED' => [
                'label' => 'Deactivated',
                'icon' => 'heroicon-o-x-circle',
                'color' => 'gray'
            ],
        ];

        $tabs = [
            'all' => Tab::make('All')
                ->icon('heroicon-o-list-bullet')
                ->badge(FlashSale::count())
        ];

        foreach ($statuses as $status => $config) {
            $tabs[strtolower(str_replace(' ', '_', $status))] = Tab::make($config['label'])
                ->icon($config['icon'])
                ->modifyQueryUsing(fn ($query) => $query->where('deal_status', $status))
                ->badge(FlashSale::where('deal_status', $status)->count())
                ->badgeColor($config['color']);
        }

        return $tabs;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action(function () {
                    $this->mount();
                    Notification::make()
                        ->title('Data refreshed successfully')
                        ->success()
                        ->send();
                }),
        ];
    }
}