<?php

namespace App\Filament\App\Resources\FlashSaleResource\Pages;

use App\Filament\App\Resources\FlashSaleResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use App\Jobs\TiktokProductFlashSale;
use App\Jobs\TiktokSyncStoreFlashSales;
use Carbon\Carbon;

class ViewFlashSale extends ViewRecord
{
    protected static string $resource = FlashSaleResource::class;

    protected function getHeaderActions(): array
    {
        $record = $this->getRecord();
        $actions = [];

        if ($record->deal_status === 'ONGOING') {
            $actions[] = Actions\Action::make('forceRenew')
                ->label('Force Renew')
                ->icon('heroicon-o-exclamation-triangle')
                ->color('warning')
                ->requiresConfirmation()
                ->modalHeading('Force Renew Flash Sale')
                ->modalDescription('This will mark the flash sale for renewal in the next sync cycle.')
                ->modalSubmitActionLabel('Yes, force renew')
                ->action(function () use ($record) {
                    try {
                        $record->update([
                            'deal_status' => 'DUPLICATED',
                            'action' => 'active'
                        ]);

                        // Dispatch job to handle the renewal
                        TiktokProductFlashSale::dispatch($record, $record->store)
                            ->onQueue('flash_sales');

                        Notification::make()
                            ->success()
                            ->title('Flash sale marked for renewal')
                            ->send();

                    } catch (\Exception $e) {
                        Notification::make()
                            ->danger()
                            ->title('Failed to mark flash sale for renewal')
                            ->body($e->getMessage())
                            ->send();
                    }
                });
        }

        return $actions;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('deal_name')
                                    ->label('Title'),
                                TextEntry::make('deal_status')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'ONGOING' => 'success',
                                        'PRODUCT UPDATE' => 'warning',
                                        'DUPLICATED' => 'info',
                                        'DEACTIVATED' => 'gray',
                                        default => 'warning',
                                    }),
                                TextEntry::make('action')
                                    ->badge()
                                    ->color(fn ($state) => $state === 'active' ? 'success' : 'gray'),
                                TextEntry::make('auto_renew')
                                    ->label('Auto Renew')
                                    ->badge()
                                    ->color(fn ($state) => $state ? 'success' : 'gray')
                                    ->formatStateUsing(fn ($state) => $state ? 'Yes' : 'No'),
                                TextEntry::make('start')
                                    ->label('Start Time')
                                    ->formatStateUsing(fn ($state) => Carbon::parse($state)
                                        ->setTimezone('America/Phoenix')
                                        ->format('M d, H:i') . ' ' . 
                                        Carbon::parse($state)->diffForHumans()),
                                TextEntry::make('expire')
                                    ->label('End Time')
                                    ->formatStateUsing(fn ($state) => Carbon::parse($state)
                                        ->setTimezone('America/Phoenix')
                                        ->format('M d, H:i') . ' ' . 
                                        Carbon::parse($state)->diffForHumans()),
                                TextEntry::make('store.name')
                                    ->label('Store'),
                                TextEntry::make('deal_id')
                                    ->label('TikTok Deal ID'),
                            ]),
                    ]),
            ]);
    }
}