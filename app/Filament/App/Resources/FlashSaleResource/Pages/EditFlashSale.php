<?php

namespace App\Filament\App\Resources\FlashSaleResource\Pages;

use App\Filament\App\Resources\FlashSaleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFlashSale extends EditRecord
{
    protected static string $resource = FlashSaleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\Action::make('forceRenew')
                ->label('Force Renew')
                ->color('warning')
                ->icon('heroicon-o-arrow-path')
                ->requiresConfirmation()
                ->visible(fn ($record) => $record->status === 'ONGOING')
                ->action(function ($record) {
                    // Reset renewal status and attempt count
                    $record->update([
                        'needs_renewal' => true,
                        'renewal_status' => null,
                        'renewal_attempted_at' => null,
                        'meta_data' => array_merge($record->meta_data ?? [], [
                            'renewal_retry_count' => 0,
                            'retry_history' => [],
                            'last_renewal_error' => null,
                            'next_retry_at' => null
                        ])
                    ]);

                    // Show success notification
                    $this->notify('success', 'Flash sale will be renewed in the next cycle');
                }),
        ];
    }
}
