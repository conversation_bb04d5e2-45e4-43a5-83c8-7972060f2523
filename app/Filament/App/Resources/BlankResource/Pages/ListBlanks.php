<?php

namespace App\Filament\App\Resources\BlankResource\Pages;

use App\Filament\App\Resources\BlankResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBlanks extends ListRecords
{
    protected static string $resource = BlankResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
