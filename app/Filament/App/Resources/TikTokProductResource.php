<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\TikTokProductResource\Pages;
use App\Filament\App\Resources\TikTokProductResource\RelationManagers;
use App\Models\TikTokProduct;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TikTokProductResource extends Resource
{
    protected static ?string $model = TikTokProduct::class;
    protected static ?string $navigationGroup = 'Spy Idea';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $label = "Tiktok Product";

    protected static bool $shouldRegisterNavigation = false;

    public static function getNavigationGroup(): ?string
    {
        return null;
    }

    public static function getNavigationIcon(): ?string
    {
        return null;
    }
    
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('images')
                    ->required(),
                Forms\Components\TextInput::make('primary_image')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('sold_count')
                    ->required(),
                Forms\Components\TextInput::make('original_price')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('real_price')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('discount')
                    ->maxLength(255),
                Forms\Components\TextInput::make('print_type')
                    ->maxLength(255),
                Forms\Components\TextInput::make('product_type')
                    ->maxLength(255),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('primary_image')
                    ->label('Image')->width(85)->height(85)
                    ->sortable(),
                TextInputColumn::make('title')->disabled()->searchable()
                    ->searchable()
                    ->sortable(),
                    TextColumn::make('shop.shop_name')->searchable()
                    ->label('Shop')
                    ->sortable(),
                TextColumn::make('sold_count')
                    ->label('Sold Count')
                    ->sortable(),
                TextColumn::make('original_price')
                    ->label('Original Price')
                    ->sortable(),
                TextColumn::make('real_price')
                    ->label('Real Price')
                    ->sortable(),
                TextColumn::make('discount')
                    ->label('Discount')
                    ->sortable(),
                TextColumn::make('print_type')
                    ->label('Print Type')
                    ->sortable(),
                TextColumn::make('product_type')
                    ->label('Product Type')
                    ->sortable(),
                TextColumn::make('created_at')->sortable()->since(),
            ])
            ->filters([
                Filter::make('sold_count')
                    ->label('Sold Count')
                    ->form([
                        Forms\Components\TextInput::make('sold_count')
                            ->numeric()
                            ->label('Sold Count Greater Than')
                    ])
                    ->query(function ($query, $data) {
                        if (!empty($data['sold_count'])) {
                            return $query->where('sold_count', '>=', $data['sold_count']);
                        }
                        return $query;
                    }),
                Filter::make('discount')
                    ->label('Has Discount')
                    ->query(fn ($query) => $query->whereNotNull('discount')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                //Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                //   Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTikTokProducts::route('/'),
            'create' => Pages\CreateTikTokProduct::route('/create'),
            // 'edit' => Pages\EditTikTokProduct::route('/{record}/edit'),
        ];
    }
}
