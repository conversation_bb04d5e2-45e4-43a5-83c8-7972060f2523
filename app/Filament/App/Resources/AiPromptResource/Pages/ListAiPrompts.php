<?php

namespace App\Filament\App\Resources\AiPromptResource\Pages;

use App\Filament\App\Resources\AiPromptResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAiPrompts extends ListRecords
{
    protected static string $resource = AiPromptResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
