<?php

namespace App\Filament\App\Resources;

use App\Enums\TiktokShopReviewStatus;
use App\Filament\App\Resources\TikTokShopResource\Pages;
use App\Filament\App\Resources\TikTokShopResource\RelationManagers;
use App\Models\TikTokShop;
use Filament\Forms;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TikTokShopResource extends Resource
{
    protected static ?string $model = TikTokShop::class;
    protected static bool $shouldRegisterNavigation = false;

    public static function getNavigationGroup(): ?string
    {
        return null;
    }

    public static function getNavigationIcon(): ?string
    {
        return null;
    }
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Spy Idea';
    protected static ?string $label = "Tiktok Shop";
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('seller_id')
                    ->required()
                    ->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('shop_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('shop_rating')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('sold_count')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('on_sell_product_count')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('review_count')
                    ->required()
                    ->numeric(),
                Forms\Components\Toggle::make('is_on_holiday')
                    ->required(),
                Forms\Components\TextInput::make('display_on_sell_product_count')
                    ->numeric(),
                Forms\Components\TextInput::make('biz_type')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('pod_type')
                    ->required(),
                Forms\Components\TextInput::make('niche')
                    ->required(),
                Forms\Components\Toggle::make('review_status')
                    ->required(),
                Forms\Components\TextInput::make('logo_url')
                    ->url()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('logo_url')
                    ->label('Logo'),

                SelectColumn::make('review_status')->options(TiktokShopReviewStatus::class)
                    ->sortable(),
                TextColumn::make('shop_name')->url(function ($record) {
                    return route('filament.app.pages.tiktok-research', [
                        'page_view' => 'tiktok-search',
                        'searchQuery' => $record->seller_id,
                        'searchType' => 'user_id'
                    ]);
                })->openUrlInNewTab()->color('success')
                    ->searchable()
                    ->sortable(),


                TextColumn::make('sold_count')->alignCenter()->weight('semibold')
                    ->sortable(),
                    
                TextColumn::make('sales_day_1')->alignCenter()->weight('semibold')->label("Today Sales")
                    ->sortable(),

                TextColumn::make('sales_day_2')->alignCenter()->weight('semibold')->label("Yesterday Sales")
                    ->sortable(),

                TextColumn::make('growth_1_day')->label('Growth (1 Day)')->alignCenter()
                    ->sortable()->color('success')->size('medium'),
                TextColumn::make('growth_7_days')->label('Growth (7 Days)')->alignCenter()
                    ->sortable(),
                TextColumn::make('growth_15_days')->label('Growth (15 Days)')->alignCenter()
                    ->sortable(),
                TextColumn::make('display_on_sell_product_count')->alignCenter(),
                TextColumn::make('last_scanned_at')->label("Last Scanned")->alignCenter()->since()
                    ->sortable(),
                TextColumn::make('last_updated_sales')->alignCenter()->since()
                    ->sortable(),

                TextColumn::make('shop_rating')->label('Rating')->alignCenter()
                    ->sortable(),
                TextColumn::make('pod_type')->alignCenter()
                    ->sortable()
                    ->searchable(),
                TextColumn::make('seller_id')->alignCenter()
                    ->sortable()
                    ->searchable(),



            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                //Tables\Actions\EditAction::make(),
            ])
            ->filters([
                Filter::make('is_on_holiday')
                    ->label('On Holiday')
                    ->query(fn ($query) => $query->where('is_on_holiday', true)),
                // Filter::make('requires_review')
                //     ->label('Requires Review')
                //     ->query(fn ($query) => $query->where('requires_review', true)),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTikTokShops::route('/'),
            'create' => Pages\CreateTikTokShop::route('/create'),
            //'edit' => Pages\EditTikTokShop::route('/{record}/edit'),
        ];
    }
}
