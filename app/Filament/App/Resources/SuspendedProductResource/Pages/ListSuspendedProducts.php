<?php

namespace App\Filament\App\Resources\SuspendedProductResource\Pages;

use App\Filament\App\Resources\SuspendedProductResource;
use App\Filament\App\Widgets\SuspendedProductGuideWidget;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSuspendedProducts extends ListRecords
{
    protected static string $resource = SuspendedProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
          //  Actions\CreateAction::make(),
        ];
    }
    protected function getHeaderWidgets(): array
    {
        return [
          SuspendedProductGuideWidget::class,
        ];
    }
}
