<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\ProductResource\Pages;
use App\Filament\App\Resources\ProductResource\RelationManagers;
use App\Filament\App\Resources\DesignResource;

use App\Models\AttributeValue;
use App\Models\Product;
use App\Models\Attribute;
use App\Models\User;
use App\Traits\HasUserFilter;
use Filament\Forms;
use Filament\Forms\Components\BelongsToManyCheckboxList;
use Filament\Forms\Components\BelongsToManyMultiSelect;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\HasManyRepeater;
use Filament\Forms\Components\ViewField;
use App\Forms\Components\ProductVariantsField;
use App\Models\ProductVariant;
use App\Rules\UniqueAttributes;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Closure;

use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Actions\ResetStars;
use App\Enums\DesignStatus;
use App\Enums\FileLocation;
use App\Filament\App\Resources\ProductResource\RelationManagers\OrdersRelationManager;
use App\Filament\App\Resources\ProductResource\RelationManagers\VariantsRelationManager;
use App\Livewire\SearchDesign;
use Carbon\Carbon;
use Exception;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Livewire;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Get;
use Filament\Support\Enums\VerticalAlignment;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
// use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class ProductResource extends Resource
{
    use HasUserFilter;
    // use HasPageShield;

    protected static ?string $model = Product::class;
    protected static ?string $navigationGroup = 'Products';
    protected static ?string $title = 'Published Products';
    protected static ?string $label = 'Published Products';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationSort(): ?int
    {
        return 3;
    }

    public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'Fullfillment Manager', 'Fulfillment', 'User Manager', 'Developer', 'Seller', 'Leader']);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                // Load store with only necessary fields
                'store:id,name',
                // Load seller with only necessary fields
                'seller:id,name',
                // Load order items count for display
                'orderItems:id,product_id'
            ])
            ->withCount('orderItems as order_items_count')
            ->addSelect([
                'orders_count' => DB::table('order_items')
                    ->selectRaw('COALESCE(COUNT(DISTINCT order_id), 0)')
                    ->whereColumn('order_items.product_id', 'products.id')
                    ->whereNotNull('order_items.product_id')
            ]);
    }
    public static function form(Form $form): Form
    {
        return $form->schema([
            // Cột 1 - Chiếm 2/3 chiều rộng
            Section::make()
                ->schema([
                    TextInput::make('name')->required(),
                    RichEditor::make('description')->extraInputAttributes(['style' => 'max-height: 300px; overflow: scroll'])->columnSpanFull(),
                ])->columnSpan([
                    'sm' => 2, // trên mobile chiếm toàn bộ
                    'lg' => 2, // trên desktop chiếm 2/3
                ]),



            // Cột 2 - Chiếm 1/3 chiều rộng
            Section::make()
                ->schema([
                    Forms\Components\Select::make('store_id')
                        ->relationship(name: 'store', titleAttribute: 'name', modifyQueryUsing: fn(Builder $query) => static::applyStoreFilterQuery($query))
                        ->preload()
                        ->searchable()
                        ->required()->disabledOn('edit'),
                    Forms\Components\Select::make('seller_id')
                        ->relationship(name: 'seller', titleAttribute: 'name', modifyQueryUsing: fn(Builder $query) => static::applyUserFilterQuery($query))
                        ->preload()
                        ->searchable()
                        ->required()->disabledOn('edit'),
                    TextInput::make('image'),
                    TextInput::make('link'),
                    TextInput::make('sku'),
                    TextInput::make('tiktok_product_id'),
                ])->columnSpan([
                    'sm' => 2, // trên mobile chiếm toàn bộ
                    'lg' => 1, // trên desktop chiếm 1/3
                ]),
            Section::make('Design')->relationship('design')->icon('heroicon-o-puzzle-piece')
                ->schema(DesignResource::getFormSchema())->columns(3)
                ->collapsed(),

        ])->columns(3);
    }

    public static function  generateCombinations($arrays)
    {
        if (empty($arrays)) {
            return [[]];
        }

        $result = [];

        $firstArray = array_shift($arrays);
        $combinationsOfRest = static::generateCombinations($arrays);

        foreach ($firstArray as $value) {
            foreach ($combinationsOfRest as $combination) {
                array_unshift($combination, $value);
                $result[] = $combination;
            }
        }

        return $result;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Stack::make([
                    ViewColumn::make('product_info')
                        ->label('Product Info')
                        ->view('filament.tables.columns.product-info'),
                ]),
                // Debug column to see orders_count value
                // Tables\Columns\TextColumn::make('orders_count')
                //     ->label('Số đơn')
                //     ->toggleable(isToggledHiddenByDefault: false),
            ])
            ->contentGrid([
                'sm' => 1,
                'md' => 3,
                'lg' => 4,
                'xl' => 5,
                '2xl' => 5,
            ])
            ->defaultPaginationPageOption(25)
            ->paginationPageOptions([10, 25, 50, 100])
            ->filters([
                static::getStoreFilter('store_id', 'Store', 'store'),


                Tables\Filters\SelectFilter::make('auto_fulfill_status')
                    ->options([
                        'has_auto_fulfill' => 'Has Auto Fulfill Variant',
                        'no_auto_fulfill' => 'No Auto Fulfill Variant',
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query->when($data['value'] === 'has_auto_fulfill', function ($query) {
                            $query->whereHas('variants', function ($query) {
                                $query->where('auto_fulfill', true);
                            });
                        })->when($data['value'] === 'no_auto_fulfill', function ($query) {
                            $query->whereDoesntHave('variants', function ($query) {
                                $query->where('auto_fulfill', true);
                            });
                        });
                    })
                    ->label('Auto Fulfill Status'),

                static::getUserFilter('seller_id', 'Người bán'),

              

                DateRangeFilter::make('created_at')
                    ->label('Ngày tạo')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->displayFormat('D/M/Y')
                    ->ranges([
                        'today' => [now(), now()],
                        'yesterday' => [now()->subDay(), now()->subDay()],
                        'last_7_days' => [now()->subDays(6), now()],
                        'last_30_days' => [now()->subDays(29), now()],
                        'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                        'last_month' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                    ]),
                Filter::make('has_orders')
                    ->toggle()
                    ->query(fn(Builder $query): Builder => $query->has('orderItems'))
                    ->label('Có đơn hàng')
                    ->indicator('Có đơn hàng'),
                Filter::make('sort_by')
                    ->form([
                        Forms\Components\Select::make('sort_by')
                            ->label('Sắp xếp theo')
                            ->options([
                                'orders_desc' => 'Nhiều đơn hàng nhất',
                                'created_at_desc' => 'Mới nhất',
                                'created_at_asc' => 'Cũ nhất',
                            ])
                            ->placeholder('Chọn cách sắp xếp')
                            ->live(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // This filter doesn't actually filter data, just used for sorting
                        // The actual sorting is handled in getTableQuery()
                        return $query;
                    })
                    ->indicateUsing(function (array $data): ?string {
                        $sortBy = $data['sort_by'] ?? null;
                        if (!$sortBy) return null;

                        $options = [
                            'orders_desc' => 'Nhiều đơn hàng nhất',
                            'created_at_desc' => 'Mới nhất',
                            'created_at_asc' => 'Cũ nhất',
                        ];

                        return 'Sắp xếp: ' . ($options[$sortBy] ?? $sortBy);
                    }),
            ])


            ->actions([
                Tables\Actions\Action::make('view_tiktok')
                    ->label('Xem trên TikTok')
                    ->icon('heroicon-o-shopping-bag')
                    ->url(fn($record) => $record->tiktok_product_id
                        ? "https://shop.tiktok.com/view/product/{$record->tiktok_product_id}?region=US&locale=en"
                        : null)
                    ->openUrlInNewTab()
                    ->visible(fn($record) => !empty($record->tiktok_product_id))
                    ->color('gray'),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])

            ->defaultSort('created_at', 'desc')
            ->persistSortInSession()
            ->persistFiltersInSession();
    }

    public static function getRelations(): array
    {
        return [
            VariantsRelationManager::class,
            OrdersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
            'variants' => ProductVariantResource\Pages\ListProductVariants::route('/{record}/variants'),
        ];
    }
}
