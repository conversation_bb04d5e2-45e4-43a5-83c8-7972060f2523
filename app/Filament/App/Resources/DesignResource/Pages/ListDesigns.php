<?php

namespace App\Filament\App\Resources\DesignResource\Pages;

use App\Enums\DesignStatus;
use App\Filament\App\Resources\DesignResource;
use App\Models\Design;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListDesigns extends ListRecords
{
    protected static string $resource = DesignResource::class;
    protected static ?string $title = '';

    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('All Orders')
        ];
    
        $statusTabs = collect(DesignStatus::cases())->mapWithKeys(function ($status) {
            $statusName = $status->value;
            $statusLabel = $status->getLabel();
            $badgeColor = $status->getColor();  // Assuming getColor method returns a color string
    
            return [
                strtolower($statusName) => Tab::make($statusLabel)
                    ->modifyQueryUsing(fn (Builder $query) => $query->where('status', $statusName))
                    ->badge(Design::where('status', $statusName)->count())
                    ->badgeColor($badgeColor),
            ];
        })->toArray();
    
        return $tabs + $statusTabs;
    }
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    
}
