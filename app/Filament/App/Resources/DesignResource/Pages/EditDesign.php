<?php

namespace App\Filament\App\Resources\DesignResource\Pages;

use App\Filament\App\Resources\DesignResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Parallax\FilamentComments\Actions\CommentsAction;
use Livewire\Attributes\On;

class EditDesign extends EditRecord
{
    protected static string $resource = DesignResource::class;

    #[On('clone-design')]
    public function refreshForm(): void
    {
        $this->record->load('designFiles');   
        parent::fillForm();
        //parent::refreshFormData(array_keys($this->record->toArray()));
    }

    protected function getHeaderActions(): array
    {
        return [
            CommentsAction::make(),
           
        ];
    }
}
