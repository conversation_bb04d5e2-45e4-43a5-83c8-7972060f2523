<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\MediaServicePricingResource\Pages;
use App\Filament\App\Resources\MediaServicePricingResource\RelationManagers;
use App\Models\MediaServicePricing;
use App\Models\MediaRequest;
use App\Models\SellerFundRequest;
use App\Enums\MediaRequestStatus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Collection;

class MediaServicePricingResource extends Resource
{
    protected static ?string $model = MediaServicePricing::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationGroup = 'Media Manager';

    protected static ?string $navigationLabel = 'Quản lý giá dịch vụ';

    protected static ?string $modelLabel = 'Media Service Pricing';

    protected static ?string $pluralModelLabel = 'Media Service Pricing';

    protected static ?int $navigationSort = 1;

    public static function canAccess(): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'User Manager', 'Media Manager']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin cơ bản')
                    ->schema([
                        Forms\Components\TextInput::make('service_type')
                            ->label('Mã dịch vụ')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->helperText('Mã định danh duy nhất cho loại dịch vụ (vd: basic_product_video)'),

                        Forms\Components\TextInput::make('name')
                            ->label('Tên dịch vụ')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('description')
                            ->label('Mô tả dịch vụ')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Cấu hình giá')
                    ->schema([
                        Forms\Components\TextInput::make('base_price')
                            ->label('Giá cơ bản (USD)')
                            ->numeric()
                            ->prefix('$')
                            ->step(0.01)
                            ->required(),

                        Forms\Components\TextInput::make('min_price')
                            ->label('Giá tối thiểu (USD)')
                            ->numeric()
                            ->prefix('$')
                            ->step(0.01)
                            ->default(0),

                        Forms\Components\TextInput::make('max_price')
                            ->label('Giá tối đa (USD)')
                            ->numeric()
                            ->prefix('$')
                            ->step(0.01)
                            ->helperText('Để trống nếu không giới hạn'),

                        Forms\Components\TextInput::make('rush_fee_percentage')
                            ->label('Phí gấp (%)')
                            ->numeric()
                            ->suffix('%')
                            ->step(0.01)
                            ->default(50.00)
                            ->helperText('Phần trăm phí bổ sung cho đơn gấp'),

                        Forms\Components\TextInput::make('revision_fee')
                            ->label('Phí sửa thêm (USD)')
                            ->numeric()
                            ->prefix('$')
                            ->step(0.01)
                            ->default(5.00),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Cấu hình dịch vụ')
                    ->schema([
                        Forms\Components\TextInput::make('estimated_hours')
                            ->label('Thời gian ước tính (giờ)')
                            ->numeric()
                            ->default(1)
                            ->required(),

                        Forms\Components\TextInput::make('max_revisions')
                            ->label('Số lần sửa tối đa')
                            ->numeric()
                            ->default(2)
                            ->required(),

                        Forms\Components\TextInput::make('delivery_days')
                            ->label('Thời gian giao hàng (ngày)')
                            ->numeric()
                            ->default(3)
                            ->required(),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Hiển thị và trạng thái')
                    ->schema([
                        Forms\Components\Select::make('icon')
                            ->label('Icon')
                            ->options([
                                'heroicon-m-play' => 'Play',
                                'heroicon-m-star' => 'Star',
                                'heroicon-m-share' => 'Share',
                                'heroicon-m-megaphone' => 'Megaphone',
                                'heroicon-m-photo' => 'Photo',
                                'heroicon-m-film' => 'Film',
                                'heroicon-m-camera' => 'Camera',
                            ])
                            ->default('heroicon-m-play'),

                        Forms\Components\Select::make('color')
                            ->label('Màu badge')
                            ->options([
                                'primary' => 'Primary',
                                'secondary' => 'Secondary',
                                'success' => 'Success',
                                'warning' => 'Warning',
                                'danger' => 'Danger',
                                'info' => 'Info',
                            ])
                            ->default('primary'),

                        Forms\Components\TextInput::make('sort_order')
                            ->label('Thứ tự sắp xếp')
                            ->numeric()
                            ->default(0),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Đang hoạt động')
                            ->default(true),

                        Forms\Components\Toggle::make('is_featured')
                            ->label('Dịch vụ nổi bật')
                            ->default(false),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Chi tiết dịch vụ')
                    ->schema([
                        Forms\Components\TagsInput::make('features')
                            ->label('Tính năng')
                            ->placeholder('Nhập tính năng và nhấn Enter')
                            ->helperText('Danh sách các tính năng của dịch vụ'),

                        Forms\Components\TagsInput::make('requirements')
                            ->label('Yêu cầu đầu vào')
                            ->placeholder('Nhập yêu cầu và nhấn Enter')
                            ->helperText('Những gì khách hàng cần cung cấp'),

                        Forms\Components\TagsInput::make('deliverables')
                            ->label('Sản phẩm đầu ra')
                            ->placeholder('Nhập sản phẩm và nhấn Enter')
                            ->helperText('Những gì khách hàng sẽ nhận được'),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                Tables\Columns\TextColumn::make('service_type')
                    ->label('Mã dịch vụ')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('name')
                    ->label('Tên dịch vụ')
                    ->searchable()
                    ->sortable()
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('base_price')
                    ->label('Giá cơ bản')
                    ->money('USD')
                    ->sortable(),

                Tables\Columns\TextColumn::make('min_price')
                    ->label('Giá tối thiểu')
                    ->money('USD')
                    ->sortable(),

                Tables\Columns\TextColumn::make('max_price')
                    ->label('Giá tối đa')
                    ->money('USD')
                    ->sortable()
                    ->placeholder('Không giới hạn'),

                Tables\Columns\TextColumn::make('estimated_hours')
                    ->label('Thời gian (h)')
                    ->suffix(' giờ')
                    ->sortable(),

                Tables\Columns\TextColumn::make('delivery_days')
                    ->label('Giao hàng')
                    ->suffix(' ngày')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Hoạt động')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_featured')
                    ->label('Nổi bật')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Thứ tự')
                    ->sortable(),

                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Tạo bởi')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('last_price_update')
                    ->label('Cập nhật giá')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Trạng thái hoạt động')
                    ->boolean()
                    ->trueLabel('Đang hoạt động')
                    ->falseLabel('Không hoạt động')
                    ->native(false),

                Tables\Filters\TernaryFilter::make('is_featured')
                    ->label('Dịch vụ nổi bật')
                    ->boolean()
                    ->trueLabel('Nổi bật')
                    ->falseLabel('Không nổi bật')
                    ->native(false),

                Tables\Filters\Filter::make('price_range')
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('price_from')
                                    ->label('Giá từ')
                                    ->numeric()
                                    ->prefix('$'),
                                Forms\Components\TextInput::make('price_to')
                                    ->label('Giá đến')
                                    ->numeric()
                                    ->prefix('$'),
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['price_from'],
                                fn (Builder $query, $price): Builder => $query->where('base_price', '>=', $price),
                            )
                            ->when(
                                $data['price_to'],
                                fn (Builder $query, $price): Builder => $query->where('base_price', '<=', $price),
                            );
                    }),

                Tables\Filters\Filter::make('delivery_time')
                    ->form([
                        Forms\Components\Select::make('delivery_days')
                            ->label('Thời gian giao hàng')
                            ->options([
                                1 => '1 ngày',
                                2 => '2 ngày',
                                3 => '3 ngày',
                                5 => '5 ngày',
                                7 => '7 ngày',
                            ])
                            ->placeholder('Chọn thời gian'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['delivery_days'],
                            fn (Builder $query, $days): Builder => $query->where('delivery_days', '<=', $days),
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Chỉnh sửa')
                    ->icon('heroicon-m-pencil-square')
                    ->color('primary'),

                Tables\Actions\Action::make('duplicate')
                    ->label('Nhân bản')
                    ->icon('heroicon-m-document-duplicate')
                    ->color('secondary')
                    ->action(function (MediaServicePricing $record): void {
                        $newRecord = $record->replicate();
                        $newRecord->service_type = $record->service_type . '_copy';
                        $newRecord->name = $record->name . ' (Copy)';
                        $newRecord->is_active = false;
                        $newRecord->created_by = auth()->id();
                        $newRecord->save();

                        Notification::make()
                            ->title('Nhân bản thành công')
                            ->body("Đã tạo bản sao: {$newRecord->name}")
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('toggle_status')
                    ->label(fn (MediaServicePricing $record): string => $record->is_active ? 'Tắt' : 'Bật')
                    ->icon(fn (MediaServicePricing $record): string => $record->is_active ? 'heroicon-m-eye-slash' : 'heroicon-m-eye')
                    ->color(fn (MediaServicePricing $record): string => $record->is_active ? 'danger' : 'success')
                    ->action(function (MediaServicePricing $record): void {
                        $record->update([
                            'is_active' => !$record->is_active,
                            'updated_by' => auth()->id(),
                        ]);

                        $status = $record->is_active ? 'kích hoạt' : 'tắt';
                        Notification::make()
                            ->title('Cập nhật trạng thái')
                            ->body("Đã {$status} dịch vụ: {$record->name}")
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('bulk_activate')
                        ->label('Kích hoạt')
                        ->icon('heroicon-m-eye')
                        ->color('success')
                        ->action(function (Collection $records): void {
                            $records->each(function (MediaServicePricing $record) {
                                $record->update([
                                    'is_active' => true,
                                    'updated_by' => auth()->id(),
                                ]);
                            });

                            Notification::make()
                                ->title('Kích hoạt thành công')
                                ->body("Đã kích hoạt {$records->count()} dịch vụ")
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\BulkAction::make('bulk_deactivate')
                        ->label('Tắt')
                        ->icon('heroicon-m-eye-slash')
                        ->color('danger')
                        ->action(function (Collection $records): void {
                            $records->each(function (MediaServicePricing $record) {
                                $record->update([
                                    'is_active' => false,
                                    'updated_by' => auth()->id(),
                                ]);
                            });

                            Notification::make()
                                ->title('Tắt thành công')
                                ->body("Đã tắt {$records->count()} dịch vụ")
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\BulkAction::make('bulk_update_pricing')
                        ->label('Cập nhật giá hàng loạt')
                        ->icon('heroicon-m-currency-dollar')
                        ->color('warning')
                        ->form([
                            Forms\Components\TextInput::make('price_adjustment')
                                ->label('Điều chỉnh giá (%)')
                                ->numeric()
                                ->suffix('%')
                                ->helperText('Nhập % tăng/giảm giá (vd: 10 = tăng 10%, -5 = giảm 5%)')
                                ->required(),

                            Forms\Components\Textarea::make('update_reason')
                                ->label('Lý do cập nhật')
                                ->placeholder('Nhập lý do điều chỉnh giá...')
                                ->required(),
                        ])
                        ->action(function (Collection $records, array $data): void {
                            $adjustment = $data['price_adjustment'] / 100;
                            $updatedCount = 0;

                            foreach ($records as $record) {
                                $oldPrice = $record->base_price;
                                $newPrice = round($oldPrice * (1 + $adjustment), 2);

                                $record->update([
                                    'base_price' => $newPrice,
                                    'last_price_update' => now(),
                                    'updated_by' => auth()->id(),
                                ]);

                                $updatedCount++;
                            }

                            Notification::make()
                                ->title('Cập nhật giá thành công')
                                ->body("Đã cập nhật giá cho {$updatedCount} dịch vụ với mức điều chỉnh {$data['price_adjustment']}%")
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMediaServicePricings::route('/'),
            'create' => Pages\CreateMediaServicePricing::route('/create'),
            'edit' => Pages\EditMediaServicePricing::route('/{record}/edit'),
        ];
    }


}
