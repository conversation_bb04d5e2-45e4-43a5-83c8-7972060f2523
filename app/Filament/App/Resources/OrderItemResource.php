<?php

namespace App\Filament\App\Resources;

use App\Enums\DesignStatus;
use App\Enums\FileLocation;
use App\Enums\OrderItemStatus;
use App\Filament\App\Resources\OrderItemResource\Pages;
use App\Filament\App\Resources\OrderItemResource\RelationManagers;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ProductVariant;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class OrderItemResource extends Resource
{
    protected static ?string $model = OrderItem::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static bool $shouldRegisterNavigation = false;
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()->schema([
                    Section::make('Design')->schema([

                        ViewField::make('QrCode')->dehydrated(false)
                            ->view('filament.forms.components.order-item')->columnSpanFull(),

                    ]),
                    Section::make('Design')

                        ->relationship('design')
                        ->schema([
                            ToggleButtons::make('status')->inline()->columnSpanFull()
                                ->options(DesignStatus::class)->default("New"),
                            Select::make('designer_id')
                                ->relationship('designer', 'name'), // Giả sử 'name' là trường bạn muốn hiển thị từ model 'designer'
                            Forms\Components\TextInput::make('design_fee')
                                ->required()
                                ->numeric()
                                ->default(0)
                                ->suffix('$'),


                            TextInput::make('mockup')->label('Mockup')->hintAction(
                                Action::make('previewlink')->label('Open link')
                                    ->icon('heroicon-m-clipboard')
                                    ->url(function (Set $set, Get $get, $state) {
                                        $url = $get('preview');
                                        if ($url) {
                                            return $url;
                                        }
                                        return null;
                                    })->openUrlInNewTab()
                            ),

                            TextInput::make('source')->hintAction(
                                Action::make('source_link')->label('Open link')
                                    ->icon('heroicon-m-clipboard')
                                    ->url(function (Set $set, Get $get, $state) {
                                        $url = $get('source');
                                        if ($url) {
                                            return $url;
                                        }
                                        return null;
                                    })->openUrlInNewTab()
                            ),
                            Repeater::make('designFiles')
                                ->relationship()->collapsed()->itemLabel(function($state){
                                    if($state['location']){
                                        return FileLocation::from($state['location'])->getLabel() ;
                                    }
                                    return null;
                                })

                                ->schema([
                                    Select::make('location')->options(FileLocation::class)->required(),
                                    Select::make('file_type')->options([
                                        "DTS" => "DTS",
                                        "EMB" => "EMB",
                                        "PNG" => "PNG",
                                        "Color Step(Bước Màu)" => "Color Step(Bước Màu)",
                                        "CUSTOM" => "CUSTOM",
                                    ])->required(),
                                    TextInput::make('file_url'),
                                    FileUpload::make('file_path')->downloadable()->columnSpanFull(),

                                ])->columnSpanFull()->columns(3),




                        ])->columns(4),

                ])->columnSpan([
                    'sm' => 3, // trên mobile chiếm toàn bộ
                    'lg' => 2, // trên desktop chiếm 1/3
                ]),



                Section::make('Order Item')->schema([
                    Radio::make('status')->columnSpanFull()
                    ->options(OrderItemStatus::class)->default("New"),





               

                    Forms\Components\Textarea::make('note')->label("Note")->columnSpanFull()
                        ->label('Note'),
                ])->columns(2)->columnSpan([
                    'sm' => 3, // trên mobile chiếm toàn bộ
                    'lg' => 1, // trên desktop chiếm 1/3
                ]),
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([


                ImageColumn::make('image')->size(60)->square()->defaultImageUrl('https://upload.wikimedia.org/wikipedia/commons/6/65/No-Image-Placeholder.svg')->alignCenter(),
                TextColumn::make('status'),
                Tables\Columns\TextColumn::make('name')->label('Product Name')->limit(40),
                Tables\Columns\TextColumn::make('quantity')->label('Quantity')->alignCenter(),
                TextColumn::make('order.status'),
                TextColumn::make('design.status'),
                TextColumn::make('revenue')
                    ->money('usd'), // Giả sử bạn sử dụng USD
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrderItems::route('/'),
            'create' => Pages\CreateOrderItem::route('/create'),
            'edit' => Pages\EditOrderItem::route('/{record}/edit'),
        ];
    }
}
