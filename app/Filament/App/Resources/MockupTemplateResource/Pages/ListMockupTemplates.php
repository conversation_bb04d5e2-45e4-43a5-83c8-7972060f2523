<?php

namespace App\Filament\App\Resources\MockupTemplateResource\Pages;

use App\Filament\App\Resources\MockupTemplateResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMockupTemplates extends ListRecords
{
    protected static string $resource = MockupTemplateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
