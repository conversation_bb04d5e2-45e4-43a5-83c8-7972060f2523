<?php

namespace App\Filament\App\Resources;

use App\Enums\ProductUploadStatus;
use App\Filament\App\Resources\ProductToUploadResource\Pages;
use App\Forms\Components\ImageGalleryField;
use App\Models\ProductToUpload;
use App\Models\Store;
use App\Models\Template;
use App\Services\Tiktok\ProductUploadService;
use App\Services\Tiktok\TiktokShopService;
use Filament\Forms;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Notifications\Notification;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Filament\Infolists;
use Illuminate\Support\HtmlString;
use Filament\Tables\Columns\ImageColumn;

class ProductToUploadResource extends Resource
{
    protected static ?string $model = ProductToUpload::class;
    protected static ?string $navigationIcon = 'heroicon-o-cloud-arrow-up';
    protected static ?string $navigationGroup = 'Products';
    protected static ?string $modelLabel = 'Scheduled Products';
    protected static ?int $navigationSort = 2;

    const MAX_ATTEMPTS = 3; // Đồng bộ với Command

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Section::make('Product Details')
                        ->schema([
                            Select::make('store_id')
                                ->label('Select Store')
                                ->relationship('store', 'name')
                                ->searchable()
                                ->preload()
                                ->required()
                                ->live()
                                ->afterStateUpdated(function ($state, $set) {
                                    if ($state) {
                                        $set('warehouses', ProductToUploadResource::loadWarehouses($state));
                                    }
                                })
                                ->afterStateHydrated(function ($state, $set) {
                                    if ($state) {
                                        $set('warehouses', ProductToUploadResource::loadWarehouses($state));
                                    }
                                })
                                ->columnSpan(2),

                            Select::make('template_id')
                                ->relationship('template', 'name')
                                ->label('Template')
                                ->placeholder('Chọn template...')
                                ->searchable()
                                ->preload(false)
                                ->getSearchResultsUsing(function (string $search) {
                                    return Template::query()
                                        ->where('name', 'like', "%{$search}%")
                                        ->limit(10)
                                        ->get()
                                        ->mapWithKeys(function ($template) {
                                            // Tính toán các thống kê
                                            $skus = collect($template->skus ?? []);
                                            $activeSkus = $skus->where('status', 'active')->count();

                                            // Tính price range
                                            $prices = $skus->pluck('price')->filter();
                                            $minPrice = $prices->min();
                                            $maxPrice = $prices->max();
                                            $priceRange = $minPrice === $maxPrice
                                                ? "$" . number_format($minPrice, 2)
                                                : "$" . number_format($minPrice, 2) . " - $" . number_format($maxPrice, 2);

                                            // Lấy types từ attributes
                                            $types = collect($template->attributes ?? [])->pluck('type')->unique();
                                            $typeStats = $types->join(', ') ?: 'No types defined';

                                            // Warning class nếu không có SKUs
                                            $warningClass = $activeSkus === 0 ? 'text-warning-500' : '';

                                            return [$template->id => view('filament.components.template-option', [
                                                'name' => $template->name,
                                                'typeStats' => $typeStats,
                                                'activeSkus' => $activeSkus,
                                                'priceRange' => $priceRange,
                                                'warningClass' => $warningClass,
                                            ])->render()];
                                        });
                                })
                                ->allowHtml()
                                ->nullable()
                                ->afterStateUpdated(function ($state, $set) {
                                    $template = Template::find($state);
                                    if ($template) {
                                        $set('productDescription', $template->description);
                                        $set('productCategoryId', $template->category_id);
                                        $set('skus', $template->skus);
                                        $set('sizeChartImage', $template->size_chart);
                                    }
                                })

                                ->columnSpan(2),

                            TextInput::make('product_title')
                                ->live()
                                ->helperText(fn($state): string => strlen($state) . ' / 255 characters')
                                ->label('Product Title')
                                ->required()
                                ->maxLength(255)
                                ->columnSpan(2),

                            RichEditor::make('description')
                                ->extraInputAttributes(['style' => 'max-height: 300px; overflow: scroll'])
                                ->label('Description')
                                ->required()
                                ->columnSpanFull(),
                        ])
                        ->columns(2),

                    Forms\Components\Section::make('Images & Scheduling')
                        ->schema([
                            ImageGalleryField::make('images')
                                ->label('Product Images')
                                ->columnSpanFull(),

                            DateTimePicker::make('scheduled_at')
                                ->label('Schedule Upload Time')
                                ->timezone('Asia/Ho_Chi_Minh')
                                ->default(now()->addMinutes(5))
                                ->minDate(now())
                                ->columnSpan(1),

                            Select::make('warehouse_id')
                                ->label('Warehouse')
                                ->options(fn($get) => $get('warehouses') ?? [])
                                ->required()
                                ->afterStateHydrated(function ($state, $set, $get) {
                                    // Ensure the current warehouse is selected
                                    $warehouses = $get('warehouses') ?? [];
                                    if (!array_key_exists($state, $warehouses)) {
                                        $set('warehouse_id', null); // Reset if not available
                                    }
                                })
                                ->columnSpan(1),

                            Select::make('save_mode')
                                ->label('Status')
                                ->options([
                                    'AS_DRAFT' => 'Draft',
                                    'LISTING' => 'Published',
                                ])
                                ->required()
                                ->placeholder('Select status')
                                ->columnSpan(1),
                        ])
                        ->columns(3),
                ])
                ->columns(1),
        ]);
    }

    protected static function loadWarehouses($storeId)
    {
        $store = Store::find($storeId);
        $tiktokService = new TiktokShopService($store);

        try {
            $warehouses = $tiktokService->getWarehouseList();
            return collect($warehouses)
                ->filter(fn($warehouse) => $warehouse['type'] === 'SALES_WAREHOUSE')
                ->mapWithKeys(fn($warehouse) => [$warehouse['id'] => $warehouse['name']])
                ->toArray();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Unable to load warehouses: ' . $e->getMessage())
                ->danger()
                ->send();
            return [];
        }
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('product_title')
                    ->label('Title')
                    ->searchable()
                    ->sortable()
                    ->limit(30)
                    ->tooltip(function ($record): string {
                        return $record->product_title;
                    }),

                Tables\Columns\TextColumn::make('store.name')
                    ->label('Store')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('attempts')
                    ->label('Attempts')
                    ->formatStateUsing(fn($state) => "{$state}/" . self::MAX_ATTEMPTS)
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')->badge()

                    ->sortable(),

                Tables\Columns\TextColumn::make('product_id')
                    ->label('TikTok ID')
                    ->copyable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('scheduled_at')
                    ->label('Scheduled')
                    ->dateTime()
                    ->timezone('Asia/Ho_Chi_Minh')
                    ->sortable(),

                Tables\Columns\TextColumn::make('error_message')
                    ->label('Error')
                    ->visible(function ($record) {
                        if (!$record) return false;
                        return $record->status === ProductUploadStatus::Failed;
                    })
                    ->tooltip(function ($record) {
                        if (!$record) return null;
                        return $record->error_message;
                    })
                    ->limit(50),

                ImageColumn::make('images')
                    ->label('Images')
                    ->getStateUsing(fn($record) => $record->images)
                    ->stacked()

                    ->limit(5)
                    ->limitedRemainingText(),
            ])
            ->defaultSort('scheduled_at', 'desc')
            ->actions([
                Tables\Actions\Action::make('upload_now')
                    ->label('Upload Now')
                    ->icon('heroicon-m-cloud-arrow-up')
                    ->color('primary')
                    ->button()
                    ->visible(
                        fn($record) => !$record->product_id && $record->status !== ProductUploadStatus::Processing
                    )
                    ->action(function (ProductToUpload $record) {
                        try {
                            $service = new ProductUploadService();
                            $response = $service->uploadProduct($record);

                            if (!isset($response['product_id'])) {
                                throw new \Exception($response['message'] ?? 'Unknown error');
                            }

                            $record->update([
                                'status' => ProductUploadStatus::Completed,
                                'uploaded_at' => now(),
                                'product_id' => $response['product_id'],
                                'error_message' => null,
                                'attempts' => $record->attempts + 1,
                                'last_attempt_at' => now(),
                            ]);

                            Notification::make()
                                ->success()
                                ->title('Success')
                                ->body(new HtmlString("Product uploaded successfully!<br>ID: {$response['product_id']}"))
                                ->duration(10000)
                                ->persistent()
                                ->send();
                        } catch (\Exception $e) {
                            $attempts = $record->attempts + 1;
                            $status = $attempts >= self::MAX_ATTEMPTS ?
                                ProductUploadStatus::Failed :
                                ProductUploadStatus::Pending;

                            $record->update([
                                'status' => $status,
                                'error_message' => $e->getMessage(),
                                'attempts' => $attempts,
                                'last_attempt_at' => now(),
                            ]);

                            Notification::make()
                                ->danger()
                                ->title('Error')
                                ->body(new HtmlString("Upload failed (Attempt {$attempts}/" . self::MAX_ATTEMPTS . ")<br>" . $e->getMessage()))
                                ->duration(10000)
                                ->persistent()
                                ->send();
                        }
                    }),
                Tables\Actions\ActionGroup::make([


                    Tables\Actions\ViewAction::make()->slideOver(),
                    Tables\Actions\DeleteAction::make(),
                ])
            ])
            ->filters([
                SelectFilter::make('store')
                    ->relationship('store', 'name')
                    ->searchable()
                    ->preload()
                    ->multiple()
                    ->label('Store'),

                SelectFilter::make('status')
                    ->options(ProductUploadStatus::class)
                    ->multiple()
                    ->label('Status'),

                DateRangeFilter::make('scheduled_at')
                    ->timezone('Asia/Ho_Chi_Minh')
                    ->displayFormat('D/M/Y')
                    ->label('Schedule Date')
            ]);
    }

    public static function infolist(Infolists\Infolist $infolist): Infolists\Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make()
                    ->schema([
                        Infolists\Components\TextEntry::make('store.name')
                            ->label('Store'),

                        Infolists\Components\TextEntry::make('template.name')
                            ->label('Template'),

                        Infolists\Components\TextEntry::make('product_title')
                            ->label('Product Title'),

                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->html()
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('warehouse_id')
                            ->label('Warehouse ID'),

                        Infolists\Components\TextEntry::make('save_mode')
                            ->label('Save Mode')
                            ->badge(),

                        Infolists\Components\TextEntry::make('status')
                            ->badge()
                            ->color(fn($state): string => match ($state) {
                                ProductUploadStatus::Pending => 'warning',
                                ProductUploadStatus::Completed => 'success',
                                ProductUploadStatus::Failed => 'danger',
                                default => 'secondary'
                            }),

                        Infolists\Components\TextEntry::make('product_id')
                            ->label('TikTok Product ID')
                            ->copyable()
                            ->visible(fn($record) => $record->status === ProductUploadStatus::Completed),

                        Infolists\Components\TextEntry::make('error_message')
                            ->label('Error Message')
                            ->color('danger')
                            ->visible(fn($record) => $record->status === ProductUploadStatus::Failed)
                            ->columnSpanFull(),

                        Infolists\Components\TextEntry::make('scheduled_at')
                            ->label('Scheduled At')
                            ->dateTime(),

                        Infolists\Components\TextEntry::make('uploaded_at')
                            ->label('Uploaded At')
                            ->dateTime()
                            ->visible(fn($record) => $record->status === ProductUploadStatus::Completed),

                        Infolists\Components\ImageEntry::make('images')
                            ->label('Images')
                            ->getStateUsing(fn($record) => $record->images)
                            ->stacked()
                            ->circular()
                            ->limit(3)
                            ->limitedRemainingText(),
                    ])
                    ->columns(2)
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductToUploads::route('/'),
            'create' => Pages\CreateProductToUpload::route('/create'),
            'edit' => Pages\EditProductToUpload::route('/{record}/edit'),
        ];
    }
}
