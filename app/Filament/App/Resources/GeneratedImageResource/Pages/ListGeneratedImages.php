<?php

namespace App\Filament\App\Resources\GeneratedImageResource\Pages;

use App\Filament\App\Resources\GeneratedImageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListGeneratedImages extends ListRecords
{
    protected static string $resource = GeneratedImageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
