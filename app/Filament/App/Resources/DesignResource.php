<?php

namespace App\Filament\App\Resources;

use App\Enums\ColorMap;
use App\Enums\DesignStatus;
use App\Enums\FileLocation;
use App\Filament\App\Resources\DesignResource\Pages;
use App\Filament\App\Resources\DesignResource\Pages\ViewDesignActivities;
use App\Filament\App\Resources\DesignResource\RelationManagers;

use App\Forms\Components\RelatedDesign;
use App\Livewire\ColorPreview;
use App\Livewire\ProductImages;
use App\Livewire\SearchDesign;
use App\Models\Design;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Livewire;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Infolist;
use Filament\Tables\Columns\TextColumn;
use Parallax\FilamentComments\Infolists\Components\CommentsEntry;
use Parallax\FilamentComments\Tables\Actions\CommentsAction;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TagsColumn;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class DesignResource extends Resource
{
  
    protected static ?string $model = Design::class;
    protected static ?string $navigationGroup = 'Design & Media';
    protected static ?string $navigationIcon = 'heroicon-o-paint-brush';
    public static function getNavigationSort(): ?int
    {
        return 3;
    }

    public static function getFormSchema(): array
    {
        return [
            Forms\Components\Group::make()
                ->schema([
                    Forms\Components\Section::make("")
                        ->schema([
                            ToggleButtons::make('status')
                                ->options(DesignStatus::class)->inline(),
                            Forms\Components\TextInput::make('name')->visible(function () {
                                $user = Auth::user();
                                return $user->hasRole(['super_admin', 'Leader', 'Seller', 'Fulfillment']);
                            })
                                ->required()
                                ->default(fn() => 'Design #' . time())
                                ->maxLength(255),

                            Grid::make('')->schema([
                                Forms\Components\Radio::make('designer_id')
                                    ->label('Designer')
                                    ->options(function () {
                                        return \App\Models\User::whereHas('roles', function ($query) {
                                            $query->where('name', 'Designer');
                                        })
                                            ->withCount(['designs' => function ($query) {
                                                $query->where('status', 'design');
                                            }])
                                            ->orderBy('name')
                                            ->get()
                                            ->mapWithKeys(function ($designer) {
                                                return [$designer->id => $designer->name . ' (' . $designer->designs_count . ' designs)'];
                                            })
                                            ->toArray();
                                    })
                                    ->columnSpanFull(),


                                Forms\Components\TextInput::make('design_fee')->minValue(0)
                                    ->required()
                                    ->numeric()
                                    ->default("0")
                                    ->suffix('$'),
                            ])->columns(2),

                            // Livewire::make(SearchDesign::class)->lazy()->visible(function () {
                            //     $user = Auth::user();
                            //     return $user->hasRole(['super_admin', 'Leader', 'Seller', 'Fulfillment']);
                            // })->hidden(fn(?Model $record): bool => $record === null)->key('search-design'),

                            Textarea::make('desc')->label("Note"),
                            Select::make('required_locations')
                                ->multiple()
                                ->label('Required Design Locations')
                                ->options(function () {
                                    return collect(FileLocation::cases())
                                        ->reject(fn($location) => $location === FileLocation::no_select)
                                        ->mapWithKeys(fn($location) => [$location->value => $location->getLabel()])
                                        ->toArray();
                                })
                                ->helperText('Select the locations that are required for this design')
                                ->required(),
                            Repeater::make('designFiles')
                                ->relationship()
                                ->schema([

                                    Select::make('location')->options(FileLocation::class)->required()->default("printer_design_front_url"),
                                    Select::make('file_style')
                                    ->label('File Style')
                                   
                                    ->options([
                                        'T-SHIRT' => 'T-Shirt',
                                        'HOODIE' => 'Hoodie', 
                                        'SWEATSHIRT' => 'Sweatshirt'
                                    ]),

                                    TextInput::make('file_url')->hintAction(
                                        Action::make('file_url_link')->label('Open')
                                            ->icon('heroicon-m-clipboard')
                                            ->url(function (Set $set, Get $get, $state) {

                                                return  $state;
                                            })->openUrlInNewTab()
                                    )->label("From Url")->url(),
                                    FileUpload::make('file_path')

                                        ->directory('file-design')->label("Upload From PC")->disk('s3')->downloadable()->columnSpanFull()
                                        ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file, Get $get, $state, $record): string {
                                            $designName = $get('../../name'); // Lấy tên design từ form chính
                                            $designId = $record ? $record->id : null; // Lấy ID của design nếu có
                                            $extension = $file->getClientOriginalExtension();
                                    
                                            if (!empty($designName)) {
                                                $safeFileName = Str::slug($designName);
                                            } elseif ($designId) {
                                                $safeFileName = "design-{$designId}";
                                            } else {
                                                // Fallback: sử dụng tên file gốc nếu cả tên và ID đều không có
                                                $fileNameWithoutExtension = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                                                $safeFileName = Str::slug($fileNameWithoutExtension);
                                            }
                                    
                                            // Thêm một chuỗi ngẫu nhiên để đảm bảo tính duy nhất
                                            return "{$safeFileName}-" . Str::random(10) . ".{$extension}";
                                        }),
                                    Select::make('suitable_colors')
                                        ->multiple()
                                        ->label('Suitable Colors')
                                        ->options(array_combine(
                                            array_keys(ColorMap::toArray()),
                                            array_keys(ColorMap::toArray())
                                        ))
                                        ->searchable()
                                        ->placeholder('Select suitable colors')
                                        ->helperText('Choose the colors this design is suitable for')
                                        ->live()
                                        ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                            static::updateColorPreview($state, $set, $get);
                                        })
                                        ->hintActions([
                                            Action::make('selectDarkColors')
                                                ->label('Select Dark Colors')
                                                ->action(function (Set $set, Get $get) {
                                                    static::selectColorGroup(ColorMap::getDarkColors(), $set, $get);
                                                }),
                                            Action::make('selectLightColors')
                                                ->label('Select Light Colors')
                                                ->action(function (Set $set, Get $get) {
                                                    static::selectColorGroup(ColorMap::getLightColors(), $set, $get);
                                                }),
                                            Action::make('selectNeutralColors')
                                                ->label('Select Neutral Colors')
                                                ->action(function (Set $set, Get $get) {
                                                    static::addColorGroup(ColorMap::getNeutralColors(), $set, $get);
                                                }),
                                            Action::make('selectAllColors')
                                                ->label('Select All Colors')
                                                ->action(function (Set $set, Get $get) {
                                                    static::selectColorGroup(ColorMap::getAllColors(), $set, $get);
                                                }),
                                        ])
                                        ->columnSpanFull(),

                                    ViewField::make('color_preview')
                                        ->view('filament.forms.components.color-preview')
                                        ->afterStateHydrated(function (Get $get, Set $set, $record) {
                                            if (!$get('color_preview')) {
                                                $set('color_preview', [
                                                    'colors' => $record ? $record->suitable_colors : [],
                                                    'filePath' => $record ? ($record->file_path ?: $record->file_url) : '',
                                                ]);
                                            }
                                        })
                                        ->columnSpanFull()
                                        ->reactive()->dehydrated(false),
                                ])->columnSpanFull()->columns(3),
                        ])
                ])
                ->columnSpan(['lg' => 2]),
            Forms\Components\Group::make()
                ->schema([


                    Forms\Components\Section::make("")
                        ->schema([

                            Forms\Components\TextInput::make('mockup')->url()
                                ->suffixIcon('heroicon-m-globe-alt')->hintAction(
                                    Action::make('openUrl')
                                        ->icon('heroicon-m-clipboard')
                                        ->url(function (Set $set, Get $get, $state) {

                                            return $get('mockup');
                                        })->openUrlInNewTab()
                                )
                                ->maxLength(255),
                            Grid::make('')->schema([
                                Forms\Components\TextInput::make('mockup_front')->reactive()

                                    ->helperText(function (Get $get, $record) {
                                        if (!$record) {
                                            return;
                                        }
                                        return new HtmlString('<a  target="_blank" href="' . $get('mockup_front') . '"><img class="rounded max-w-[140px] mx-auto"  src="' . $get('mockup_front') . '" /></a>');
                                    })->url()

                                    ->maxLength(255),
                                Forms\Components\TextInput::make('mockup_back')->url()->reactive()

                                    ->helperText(function (Get $get, $record) {
                                        if (!$record) {
                                            return;
                                        }
                                        return new HtmlString('<a  target="_blank" href="' . $get('mockup_back') . '"><img class="rounded max-w-[140px] mx-auto"  src="' . $get('mockup_back') . '" /></a>');
                                    })->url()

                                    ->maxLength(255),
                            ])->columns(2),
                            Forms\Components\TextInput::make('source')->url()->hintAction(
                                Action::make('openUrlInNewTab')
                                    ->icon('heroicon-m-clipboard')
                                    ->url(function (Set $set, Get $get, $state) {

                                        return $get('source');
                                    })->openUrlInNewTab()
                            )
                                ->suffixIcon('heroicon-m-globe-alt')
                                ->maxLength(255),

                            //Livewire::make(ProductImages::class),
                            Forms\Components\TextInput::make('sku')->visible(function () {
                                $user = Auth::user();
                                return $user->hasRole(['super_admin', 'Leader', 'Seller', 'Fulfillment']);
                            })
                        ])
                        ->columnSpan(['lg' => 1]),

                ])

        ];
    }
    private static function updateColorPreview($colors, Set $set, Get $get): void
    {
        $set('color_preview', [
            'colors' => $colors,
            'filePath' => $get('file_path') ?: $get('file_url'),
        ]);
    }

    private static function selectColorGroup(array $colors, Set $set, Get $get): void
    {
        $colorNames = array_map(fn($color) => str_replace('_', ' ', $color->name), $colors);
        $set('suitable_colors', $colorNames);
        static::updateColorPreview($colorNames, $set, $get);
    }

    private static function addColorGroup(array $colors, Set $set, Get $get): void
    {
        $newColorNames = array_map(fn($color) => str_replace('_', ' ', $color->name), $colors);
        $currentColors = $get('suitable_colors') ?? [];
        $updatedColors = array_unique(array_merge($currentColors, $newColorNames));
        $set('suitable_colors', $updatedColors);
        static::updateColorPreview($updatedColors, $set, $get);
    }
    public static function form(Form $form): Form
    {
        return $form->schema(static::getFormSchema())->columns([
            'sm' => 1,
            'lg' => 3,
        ]);
    }

    public static function form2(Form $form): Form
    {

        return $form
            ->schema(static::getFormSchema())
            ->columns([
                'sm' => 3,
                'lg' => null,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn(Builder $query) => $query->take(10))
            ->columns([

                ImageColumn::make('mockup')->size(60)->square()->defaultImageUrl('https://upload.wikimedia.org/wikipedia/commons/6/65/No-Image-Placeholder.svg')->alignCenter(),
                ImageColumn::make('designFiles.design_thumb')

                    ->square()->defaultImageUrl('https://upload.wikimedia.org/wikipedia/commons/6/65/No-Image-Placeholder.svg')->alignCenter(),

                TextColumn::make('status')->alignLeft(),

                BadgeableColumn::make('name')->sortable()->limit(30)->alignLeft()->searchable(),
                TextColumn::make('orders_count')
                    ->label('Orders')
                    ->sortable()
                    ->alignCenter()
                    ->counts('orders'),

                TextColumn::make('design_fee')->alignCenter()->money()
                    ->description(function (?Design $record) {
                        if (isset($record->designer)) {
                            return $record->designer->name;
                        } else {
                            return "No Designer";
                        }
                    })
                    ->sortable(),
                TagsColumn::make('required_locations')
                    ->label('Required Locations')
                    ->getStateUsing(function ($record) {
                        return collect($record->required_locations)
                            ->map(fn($location) => FileLocation::from($location)->getLabel())
                            ->toArray();
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                CommentsAction::make(),
                Tables\Actions\Action::make('view_activities')
                    ->label('Activities')
                    ->icon('heroicon-m-bolt')
                    ->color('purple')
                    ->url(fn($record) => DesignResource::getUrl('activities', ['record' => $record]))->slideOver(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([

                Tables\Actions\BulkActionGroup::make([
                    BulkAction::make('updateStatus')
                        ->label('Update Status')
                        ->action(function ($records, $data) {  // Note: Removed the type hint for $records
                            foreach ($records as $record) {
                                $record->status = $data['status'];
                                $record->save();
                            }
                        })
                        ->form([
                            Forms\Components\Select::make('status')
                                ->label('New Status')
                                ->options(DesignStatus::class),
                        ])
                ]),
            ])->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                CommentsEntry::make('filament_comments'),
            ]);
    }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDesigns::route('/'),
            'create' => Pages\CreateDesign::route('/create'),
            'edit' => Pages\EditDesign::route('/{record}/edit'),
            'activities' => ViewDesignActivities::route('/{record}/activities'),

        ];
    }
}
