<?php

namespace App\Filament\App\Resources\FeatureRequestResource\Pages;

use App\Filament\App\Resources\FeatureRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateFeatureRequest extends CreateRecord
{
    protected static string $resource = FeatureRequestResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Yêu cầu tính năng đã được tạo thành công!';
    }
}
