<?php

namespace App\Filament\App\Resources\FeatureRequestResource\Pages;

use App\Filament\App\Resources\FeatureRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFeatureRequest extends EditRecord
{
    protected static string $resource = FeatureRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('Xem'),
            Actions\DeleteAction::make()
                ->label('Xóa'),
        ];
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Yêu cầu tính năng đã được cập nhật thành công!';
    }
}
