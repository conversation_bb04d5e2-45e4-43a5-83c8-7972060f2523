<?php

namespace App\Filament\App\Resources\FeatureRequestResource\Pages;

use App\Filament\App\Resources\FeatureRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewFeatureRequest extends ViewRecord
{
    protected static string $resource = FeatureRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Chỉnh sửa'),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Thông tin yêu cầu')
                    ->schema([
                        Infolists\Components\TextEntry::make('title')
                            ->label('Tiêu đề')
                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large)
                            ->weight('bold'),

                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Người yêu cầu')
                            ->badge()
                            ->color('primary'),

                        Infolists\Components\TextEntry::make('category')
                            ->label('Danh mục')
                            ->badge()
                            ->color(fn ($record) => $record->category->getColor())
                            ->formatStateUsing(fn ($record) => $record->category->getLabel()),

                        Infolists\Components\TextEntry::make('priority')
                            ->label('Mức độ ưu tiên')
                            ->badge()
                            ->color(fn ($record) => $record->priority->getColor())
                            ->formatStateUsing(fn ($record) => $record->priority->getLabel()),

                        Infolists\Components\TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge()
                            ->color(fn ($record) => $record->status->getColor())
                            ->formatStateUsing(fn ($record) => $record->status->getLabel()),

                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Ngày tạo')
                            ->dateTime('d/m/Y H:i'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Mô tả chi tiết')
                    ->schema([
                        Infolists\Components\TextEntry::make('description')
                            ->label('')
                            ->html()
                            ->prose(),
                    ]),

                Infolists\Components\Section::make('Quản lý')
                    ->schema([
                        Infolists\Components\TextEntry::make('admin_notes')
                            ->label('Ghi chú của Admin')
                            ->html()
                            ->prose()
                            ->placeholder('Chưa có ghi chú từ admin'),

                        Infolists\Components\TextEntry::make('completed_at')
                            ->label('Ngày hoàn thành')
                            ->dateTime('d/m/Y H:i')
                            ->placeholder('Chưa hoàn thành'),
                    ])
                    ->visible(fn ($record) => $record->admin_notes || $record->completed_at),
            ]);
    }
}
