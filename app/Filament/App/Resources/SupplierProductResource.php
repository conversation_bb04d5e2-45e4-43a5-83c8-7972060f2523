<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\SupplierProductResource\Pages;
use App\Models\SupplierProduct;
use App\Models\Supplier;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Filament\Tables\Actions\ImportAction;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Actions\ExportBulkAction;
use App\Filament\Imports\SupplierProductImporter;

class SupplierProductResource extends Resource
{
    protected static ?string $model = SupplierProduct::class;
    protected static ?string $navigationIcon = 'heroicon-o-cube';
    protected static ?int $navigationSort = 2;
    
    public static function getNavigationGroup(): ?string
    {
        return __('Order & Supplier');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('supplier_id')
                            ->relationship('supplier', 'name')
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\TextInput::make('price')
                            ->required()
                            ->numeric()
                            ->prefix('$'),

                        Forms\Components\TextInput::make('sku')
                            ->required()
                            ->maxLength(191)
                            ->unique(ignoreRecord: true),

                        Forms\Components\TextInput::make('variant_id')
                            ->required()
                            ->maxLength(191),

                        Forms\Components\TextInput::make('style')
                            ->required()
                            ->maxLength(191),

                        Forms\Components\TextInput::make('color')
                            ->required()
                            ->maxLength(191),

                        Forms\Components\TextInput::make('size')
                            ->required()
                            ->maxLength(191),

                        Forms\Components\Toggle::make('active')
                            ->required()
                            ->default(true),

                        Forms\Components\Toggle::make('tiktok')
                            ->default(false),

                        Forms\Components\Select::make('smart_sku_id')
                            ->relationship('smart_sku', 'style')
                            ->searchable()
                            ->preload(),
                    ])
                    ->columns(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('supplier.name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('price')
                    ->money()
                    ->sortable(),

                Tables\Columns\TextColumn::make('sku')
                    ->searchable()
                    ->copyable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('variant_id')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('style')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('color')
                    ->searchable(),

                Tables\Columns\TextColumn::make('size')
                    ->searchable(),

                Tables\Columns\IconColumn::make('active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('tiktok')
                    ->boolean(),

                Tables\Columns\TextColumn::make('smart_sku.style')
                    ->label('Smart SKU')
                    ->searchable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('supplier')
                    ->relationship('supplier', 'name')
                    ->multiple()
                    ->preload(),

                Tables\Filters\SelectFilter::make('style')
                    ->options(fn () => SupplierProduct::distinct()->pluck('style', 'style')),

                Tables\Filters\SelectFilter::make('color')
                    ->options(fn () => SupplierProduct::distinct()->pluck('color', 'color')),

                Tables\Filters\SelectFilter::make('size')
                    ->options(fn () => SupplierProduct::distinct()->pluck('size', 'size')),

                Tables\Filters\TernaryFilter::make('active'),
                Tables\Filters\TernaryFilter::make('tiktok'),
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                $record->update(['active' => true]);
                            });
                        })
                        ->icon('heroicon-o-check'),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                $record->update(['active' => false]);
                            });
                        })
                        ->icon('heroicon-o-x-mark')
                        ->color('danger'),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSupplierProducts::route('/'),
            // 'create' => Pages\CreateSupplierProduct::route('/create'),
            // 'edit' => Pages\EditSupplierProduct::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('active', true)->count();
    }
}