<?php

namespace App\Filament\App\Resources\ProductResource\Pages;

use App\Filament\App\Resources\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Auth;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;

class EditProduct extends EditRecord
{
    // use HasPageShield; // Comment để tránh xung đột permissions

    protected static string $resource = ProductResource::class;

    public static function canAccess(array $parameters = []): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'Fullfillment Manager', 'Fulfillment', 'User Manager', 'Developer', 'Seller', 'Leader']);
    }

    protected function getHeaderActions(): array
    {
        return [

        ];
    }
}
