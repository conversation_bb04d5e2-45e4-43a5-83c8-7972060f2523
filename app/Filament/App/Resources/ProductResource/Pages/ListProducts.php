<?php

namespace App\Filament\App\Resources\ProductResource\Pages;

use App\Filament\App\Resources\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListProducts extends ListRecords
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getTableQuery(): Builder
    {
        $query = parent::getTableQuery();

        // Check sort_by filter value from custom filter form
        $tableFilters = $this->tableFilters ?? [];
        $sortBy = $tableFilters['sort_by']['sort_by'] ?? null;

        // // Debug logging
        // \Log::info('Filter state:', [
        //     'sortBy' => $sortBy,
        //     'tableFilters' => $tableFilters,
        //     'query_before' => $query->toSql(),
        // ]);

        if ($sortBy) {
            // Clear existing orders first
            $query->getQuery()->orders = null;

            // Apply sorting based on selection
            switch ($sortBy) {
                case 'orders_desc':
                    $query->orderByRaw('CAST(orders_count AS UNSIGNED) DESC');
                    break;
                case 'created_at_desc':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'created_at_asc':
                    $query->orderBy('created_at', 'asc');
                    break;
            }

            \Log::info('Applied sorting:', [
                'sortBy' => $sortBy,
                'query_after' => $query->toSql(),
                'orders' => $query->getQuery()->orders
            ]);
        }

        return $query;
    }
}
