<?php

namespace App\Filament\App\Resources\ProductResource\RelationManagers;

use App\Enums\FileLocation;
use App\Filament\App\Resources\ProductVariantResource;
use App\Forms\Components\DesignField;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Supplier;
use App\Models\SupplierProduct;
use App\Services\Tiktok\TiktokShopService;
use Filament\Forms;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Collection;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\BulkAction;

class VariantsRelationManager extends RelationManager
{
    protected static string $relationship = 'variants';

    public function form(Form $form): Form
    {
        return ProductVariantResource::form($form);
    }

    public function table(Table $table): Table
    {
        return ProductVariantResource::table($table)
            ->modifyQueryUsing(fn ($query) => $query->with('autoFulfillEnabledBy'))
            ->headerActions([

            // Chỉ cho phép quyền fulfillment managẻ được bật auto fulfill cho tất cả variant
            Action::make('enable_auto_fulfill_all')
                ->label('Enable Auto Fulfill (All Eligible)')
                ->icon('heroicon-o-check-circle')
                ->color('info')
                ->visible(fn () => auth()->user()->hasRole(['Fullfillment Manager', 'super_admin', 'Fulfillment']))
                ->requiresConfirmation()
                ->action(function () {
                    $product = $this->getOwnerRecord();
                    $variants = $product->variants()->withoutGlobalScope('access')->get();

                    $enabledCount = 0;
                    $ineligibleCount = 0;

                    foreach ($variants as $variant) {
                        if ($variant->canAutoFulfill()) {
                            $variant->auto_fulfill = true;
                            $variant->auto_fulfill_enabled_by = auth()->id();
                            $variant->auto_fulfill_enabled_at = now();
                            $variant->save();
                            $enabledCount++;
                        } else {
                            $ineligibleCount++;
                        }
                    }

                    Notification::make()
                        ->title('Auto Fulfill Update')
                        ->body("Enabled for {$enabledCount} variants. {$ineligibleCount} variants were ineligible.")
                        ->success()
                        ->send();
                })
                ->requiresConfirmation()
                ->modalHeading('Enable Auto Fulfill for All Eligible Variants')
                ->modalDescription('This action will enable auto fulfill for all eligible variants of this product. Are you sure you want to continue?'),

            Action::make('disable_auto_fulfill_all')
                ->label('Disable Auto Fulfill (All)')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->visible(fn () => auth()->user()->hasRole(['Fullfillment Manager', 'super_admin', 'Fulfillment']))

                ->action(function () {
                    $product = $this->getOwnerRecord();
                    $variants = $product->variants()->withoutGlobalScope('access')->get();

                    $disabledCount = $variants->where('auto_fulfill', true)->count();

                    $variants->each(function ($variant) {
                        $variant->auto_fulfill = false;
                        $variant->save();
                    });

                    Notification::make()
                        ->title('Auto Fulfill Update')
                        ->body("Disabled for {$disabledCount} variants.")
                        ->success()
                        ->send();
                })
                ->requiresConfirmation()
                ->modalHeading('Disable Auto Fulfill for All Variants')
                ->modalDescription('This action will disable auto fulfill for all variants of this product. Are you sure you want to continue?'),
            Action::make('createProductVariants')
                ->label('Tạo Product Variants')
                ->color('gray')
                
                ->form([
                    Select::make('supplier_id')
                        ->label('Supplier')
                        ->options(function () {
                            return Supplier::pluck('name', 'id')->toArray();
                        })
                        ->default(function () {
                            // Giả sử Flashship có id là 1, hãy thay đổi nếu khác
                            return Supplier::where('name', 'Flashship')->first()->id ?? null;
                        })
                        ->required(),

                    Select::make('type_prefix')
                        ->label('Product Type Prefix')
                        ->options([
                            '' => 'None (Use existing)',
                            'T-SHIRT' => 'T-SHIRT',
                            'HOODIE' => 'HOODIE',
                            'SWEATSHIRT' => 'SWEATSHIRT',
                        ])
                        ->default('')
                ])->modalWidth('lg')
                ->action(function (array $data) {
                    $product = $this->getOwnerRecord();
                    $product->variants()->delete();
                    $tiktokService = new TiktokShopService($product->store);
                    $variants = $tiktokService->getProductVariants($product->tiktok_product_id);


                    $supplier = Supplier::find($data['supplier_id']);
                    $typePrefix = $data['type_prefix'] ?? '';




                    foreach ($variants as $variant) {
                        $variantName = [];
                        foreach ($variant['sales_attributes'] as $attribute) {
                            // Chuẩn hóa tên thuộc tính từ 'Colour' thành 'Color'
                            $attributeName = $attribute['name'] === 'Colour' ? 'Color' : $attribute['name'];
                            $variantName[] = "{$attributeName} : {$attribute['value_name']}";
                        }

                        $variantNameString = implode(' | ', $variantName);

                        $new_variant = $product->variants()->updateOrCreate(
                            ['variant_id' => $variant['id']],
                            [
                                'product_id' => $product->id,
                                'seller_id' => $product->store->owner_id, // Lấy seller_id từ store của sản phẩm
                                'variant_name' => $variantNameString,

                            ]
                        );
                        self::processVariant($new_variant, $supplier, $typePrefix);
                    }

                    Notification::make()
                        ->success()
                        ->title('Success')
                        ->body('Product variants đã được tạo thành công.')
                        ->send();
                })
                ->requiresConfirmation()
        ])->bulkActions([


            Tables\Actions\BulkAction::make('SetDesignURLs')
                ->label('Set Design URLs')

                ->form([
                    Group::make()->schema([
                        Group::make()->schema([
                            Forms\Components\TextInput::make('design_front_url')
                                ->label('Design Front URL')
                                ->url()
                                ->afterStateHydrated(function (Get $get, Set $set, RelationManager $livewire) {
                                    $design = $livewire->getOwnerRecord()->design;
                                    $frontDesignFile = $design?->designFiles()->firstWhere('location', 'printer_design_front_url');
                                    $set('design_front_url', $frontDesignFile?->design_link ?? '');
                                }),
                            Forms\Components\TextInput::make('design_back_url')
                                ->label('Design Back URL')
                                ->url()
                                ->afterStateHydrated(function (Get $get, Set $set, RelationManager $livewire) {
                                    $design = $livewire->getOwnerRecord()->design;
                                    $backDesignFile = $design?->designFiles()->firstWhere('location', 'printer_design_back_url');
                                    $set('design_back_url', $backDesignFile?->design_link ?? '');
                                }),
                            Forms\Components\TextInput::make('sleeve_right_design_url')
                                ->label('Sleeve Right Design URL')
                                ->url(),
                            Forms\Components\TextInput::make('sleeve_left_design_url')
                                ->label('Sleeve Left Design URL')
                                ->url(),
                            Forms\Components\TextInput::make('mockup_front_url')
                                ->label('Mockup Front URL')
                                ->url()
                                ->afterStateHydrated(function (Get $get, Set $set, RelationManager $livewire) {
                                    $design = $livewire->getOwnerRecord()->design;
                                    $set('mockup_front_url', $design?->mockup_front ?? '');
                                }),
                            Forms\Components\TextInput::make('mockup_back_url')
                                ->label('Mockup Back URL')
                                ->url()
                                ->afterStateHydrated(function (Get $get, Set $set, RelationManager $livewire) {
                                    $design = $livewire->getOwnerRecord()->design;
                                    $set('mockup_back_url', $design?->mockup_back ?? '');
                                }),
                        ])->columns(2)->columnSpan(['md' => 2]),
                        Group::make()->schema([
                            DesignField::make('design')
                                ->afterStateHydrated(function (Get $get, Set $set, RelationManager $livewire) {
                                    $design = $livewire->getOwnerRecord()->design;
                                    if ($design) {
                                        $set('design', $design->id);
                                    }
                                }),

                        ])->columnSpan(['md' => 1]),
                    ])->columns(3)


                ])
                ->slideOver()->modalWidth(MaxWidth::SixExtraLarge)
                ->action(function (Collection $records, array $data) {
                    foreach ($records as $record) {
                        unset($data['design']);
                        $record->update(array_filter($data));
                    }
                })->color('gray')
                ->deselectRecordsAfterCompletion(),

            Tables\Actions\BulkAction::make('process_variants')
                ->label('Process Variants')
                ->form([
                    Forms\Components\Select::make('supplier_id')
                        ->label('Supplier')
                        ->options(function () {
                            return Supplier::pluck('name', 'id')->toArray();
                        })
                        ->required(),
                    Forms\Components\Select::make('type_prefix')
                        ->label('Product Type Prefix')
                        ->options([
                            '' => 'None (Use existing)',
                            'T-SHIRT' => 'T-SHIRT',
                            'HOODIE' => 'HOODIE',
                            'SWEATSHIRT' => 'SWEATSHIRT',
                        ]),
                ])
                ->action(function (Collection $records, array $data) {
                    $supplier = Supplier::find($data['supplier_id']);
                    $typePrefix = $data['type_prefix'] ?? '';

                    foreach ($records as $record) {
                        try {
                            self::processVariant($record, $supplier, $typePrefix);
                        } catch (\Exception $e) {
                            dd($e->getMessage());
                        }
                    }
                })->color('gray')
                ->deselectRecordsAfterCompletion()
                ->requiresConfirmation(),
            BulkAction::make('enable_auto_fulfill')
                ->label('Enable Auto Fulfill')
                ->action(function (Collection $records) {
                    $enabledCount = 0;
                    $failedCount = 0;

                    foreach ($records as $record) {
                        if ($record->canAutoFulfill()) {
                            $record->auto_fulfill = true;
                            $record->auto_fulfill_enabled_by = auth()->id();
                            $record->auto_fulfill_enabled_at = now();
                            $record->save();
                            $enabledCount++;
                        } else {
                            $failedCount++;
                        }
                    }

                    Notification::make()
                        ->title("Auto Fulfill Update")
                        ->body("Enabled for {$enabledCount} variants. Failed for {$failedCount} variants.")
                        ->send();
                })
                ->deselectRecordsAfterCompletion()
                ->requiresConfirmation()
                ->hidden(function (?Collection $records) {
                    if ($records === null || $records->isEmpty()) {
                        return true;
                    }
                    return $records->every(fn($record) => $record->auto_fulfill);
                }),

            BulkAction::make('disable_auto_fulfill')
                ->label('Disable Auto Fulfill')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->action(function (Collection $records) {
                    $records->each(function ($record) {
                        $record->update(['auto_fulfill' => false]);
                    });
                })
                ->deselectRecordsAfterCompletion()
                ->successNotificationTitle('Auto fulfill disabled successfully'),
        ]);
    }

    private static function processVariant(ProductVariant $record, Supplier $supplier, string $typePrefix): void
    {
        $variantName = $record->variant_name;
        $type = $typePrefix ?: self::extractTypeFromVariantName($variantName);

        $variantParts = array_map('trim', explode('|', $variantName));
        $variantData = [];
        foreach ($variantParts as $part) {
            list($key, $value) = array_map('trim', explode(':', $part));
            $variantData[$key] = $value;
        }

        // Xác định loại sản phẩm từ Style hoặc Type nếu có
        if (isset($variantData['Style'])) {
            $type = self::extractTypeFromVariantName($variantData['Style']);
        } elseif (isset($variantData['Type'])) {
            $type = self::extractTypeFromVariantName($variantData['Type']);
        } elseif (isset($variantData['Tybe'])) { // Handle typo case
            $type = self::extractTypeFromVariantName($variantData['Tybe']);
        }

        // Log if no type could be extracted
        if (empty($type)) {
            \Log::channel('variant_mapping')->warning('No product type extracted from variant', [
                'variant_id' => $record->id,
                'variant_name' => $record->variant_name,
                'parsed_data' => $variantData,
                'reason' => 'No Type/Style field found or type extraction failed',
                'available_fields' => array_keys($variantData)
            ]);
        }

        if (isset($variantData['Size']) && isset($variantData['Color'])) {
            $size = self::normalizeSize($variantData['Size']);
            $color = strtoupper($variantData['Color']); // Chuẩn hóa color
            $style = strtoupper($type); // Chuẩn hóa style/type


            if ($supplier->name === 'Flashship') {
                $flashshipId = Supplier::where('name', 'Flashship')->value('id');

                // Tìm variant trong supplier_products
                $supplierProduct = SupplierProduct::where('supplier_id', $flashshipId)
                    ->where('active', true)
                    ->where('size', $size)
                    ->where('color', $color)
                    ->where('style', $style)
                    ->first();

                if ($supplierProduct) {
                    $record->update([
                        'supplier_id' => $supplier->id,
                        'variant_fulfill_name' => "{$supplierProduct->style} {$supplierProduct->color} [{$supplierProduct->size}]",
                        'variant_fulfill_id' => $supplierProduct->variant_id,
                    ]);

                  
                } else {
                    // Log khi không tìm thấy variant
                    \Log::channel('variant_mapping')->warning('Variant mapping failed - No supplier product found', [
                        'variant_id' => $record->id,
                        'variant_name' => $record->variant_name,
                        'supplier' => $supplier->name,
                        'search_criteria' => [
                            'size' => $size,
                            'color' => $color,
                            'style' => $style,
                            'extracted_type' => $type
                        ],
                        'parsed_data' => $variantData,
                        'reason' => 'No matching supplier product found with exact criteria'
                    ]);
                }
            } else {
                // Xử lý các supplier khác nếu cần
                $record->update([
                    'supplier_id' => $supplier->id,
                ]);
            }

            // Get and update design URLs
            $designUrls = self::getDesignUrls($record, $color, $style);
            $record->update([
                'design_front_url' => $designUrls['design_front_url'],
                'design_back_url' => $designUrls['design_back_url'],
                'sleeve_right_design_url' => $designUrls['sleeve_right_design_url'],
                'sleeve_left_design_url' => $designUrls['sleeve_left_design_url'],
                'mockup_front_url' => $designUrls['mockup_front_url'],
                'mockup_back_url' => $designUrls['mockup_back_url'],
            ]);
        }
    }
    private static function normalizeSize($size)
    {
        $sizeMap = [
            'XXS' => 'XXS',
            'XS' => 'XS',
            'S' => 'S',
            'M' => 'M',
            'L' => 'L',
            'XL' => 'XL',
            'XXL' => '2XL',
            '2XL' => '2XL',
            'XXXL' => '3XL',
            '3XL' => '3XL',
            '4XL' => '4XL',
            '5XL' => '5XL',
        ];

        $upperSize = strtoupper(trim($size));
        return $sizeMap[$upperSize] ?? $upperSize;
    }
    private static function getDesignUrls(ProductVariant $variant, string $color, string $style): array
    {
        $product = $variant->product;
        $design = $product->design;

        if (!$design) {
            return [
                'design_front_url' => null,
                'design_back_url' => null,
                'sleeve_right_design_url' => null,
                'sleeve_left_design_url' => null,
                'mockup_front_url' => null,
                'mockup_back_url' => null,
            ];
        }

        $designFiles = $design->designFiles;

        // Đảm bảo $designFiles là một collection
        if (!$designFiles instanceof \Illuminate\Support\Collection) {
            $designFiles = collect($designFiles);
        }
        // Filter designFiles theo style và color
        $designFiles = $designFiles->filter(function ($file) use ($style, $color) {
            // File style trống hoặc khớp với style của variant
            $styleMatch = $file->file_style === null || $file->file_style === $style;
            
            // Check color match như cũ
            $colorMatch = is_array($file->suitable_colors) &&
                in_array(strtoupper($color), array_map('strtoupper', $file->suitable_colors));
        
            return $styleMatch && $colorMatch;
        });
        $frontDesign = self::getDesignFileForPosition($designFiles, 'printer_design_front_url', $color);

        $backDesign = self::getDesignFileForPosition($designFiles, 'printer_design_back_url', $color);
        $rightSleeveDesign = self::getDesignFileForPosition($designFiles, 'printer_design_right_sleeve_url', $color);
        $leftSleeveDesign = self::getDesignFileForPosition($designFiles, 'printer_design_left_sleeve_url', $color);

        return [
            'design_front_url' => $frontDesign ? $frontDesign->getDesignLinkAttribute() : null,
            'design_back_url' => $backDesign ? $backDesign->getDesignLinkAttribute() : null,
            'sleeve_right_design_url' => $rightSleeveDesign ? $rightSleeveDesign->getDesignLinkAttribute() : null,
            'sleeve_left_design_url' => $leftSleeveDesign ? $leftSleeveDesign->getDesignLinkAttribute() : null,
            'mockup_front_url' => $product->mockup_front ?? $design->mockup_front ?? $design->mockup,
            'mockup_back_url' => $product->mockup_back ?? $design->mockup_back,
        ];
    }



    private static function getDesignFileForPosition($designFiles, $position, $color)
    {
        if (!$designFiles instanceof \Illuminate\Support\Collection) {
            $designFiles = collect($designFiles);
        }

        return $designFiles->first(function ($file) use ($position, $color) {
            $colorMatch = is_array($file->suitable_colors) &&
                in_array(strtoupper($color), array_map('strtoupper', $file->suitable_colors));

            $positionEnum = is_string($position) ? FileLocation::tryFrom($position) : $position;
            $locationMatch = $file->location === $positionEnum;

            return $locationMatch && $colorMatch;
        });
    }
    private static function loadFlashShipCsvData()
    {
        $flashshipId = Supplier::where('name', 'Flashship')->value('id');

        return SupplierProduct::where('supplier_id', $flashshipId)
            ->where('active', true)
            ->get()
            ->map(function ($product) {
                return [
                    'variant_id' => $product->variant_id,
                    'sku' => $product->sku,
                    'style' => $product->style,
                    'color' => $product->color,
                    'size' => $product->size
                ];
            })
            ->toArray();
    }

    private static function findFlashShipData($products, $sku, $type)
    {
        foreach ($products as $product) {
            if ($product['sku'] === $sku || $product['variant_id'] === $sku) {
                return [
                    'variant_id' => $product['variant_id'],
                    'sku' => $product['sku'],
                    'style' => $product['style'],
                    'color' => $product['color'],
                    'size' => $product['size']
                ];
            }
        }
        return null;
    }

    private static function normalizeProductType(string $type): string
    {
        $type = strtolower($type);
        if (strpos($type, 'hoodie') !== false) {
            return 'hoodie';
        }
        if (strpos($type, 'sweatshirt') !== false) {
            return 'sweatshirt';
        }
        if (strpos($type, 't-shirt') !== false || strpos($type, 'tshirt') !== false || $type === 'shirt') {
            return 'shirt';
        }

        // Add more product type normalizations as needed
        return $type;
    }
    private static function extractTypeFromVariantName(string $variantName): string
    {
        $lowercaseName = strtolower($variantName);

        // ✅ Check specific patterns first to avoid false positives
        if (strpos($lowercaseName, 'sweatshirt') !== false) {
            return 'SWEATSHIRT';
        } elseif (strpos($lowercaseName, 'hoodie') !== false) {
            return 'HOODIE';
        } elseif (strpos($lowercaseName, 't-shirt') !== false || strpos($lowercaseName, 'tshirt') !== false) {
            return 'T-SHIRT';
        } elseif (strpos($lowercaseName, 'tank top') !== false || strpos($lowercaseName, 'tank') !== false) {
            return 'TANK-TOP';
        } elseif (strpos($lowercaseName, 'tote bag') !== false || strpos($lowercaseName, 'bag') !== false) {
            return 'TOTE-BAG';
        } elseif (strpos($lowercaseName, 'shirt') !== false) {
            // ✅ USER'S LOGIC: Map any remaining 'shirt' to T-SHIRT
            return 'T-SHIRT';
        }

        // ✅ FAIL-SAFE: No match → empty → manual processing
        return '';
    }
}
