<?php

namespace App\Filament\App\Resources\ProductToUploadResource\Pages;

use App\Filament\App\Resources\ProductToUploadResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditProductToUpload extends EditRecord
{
    protected static string $resource = ProductToUploadResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
