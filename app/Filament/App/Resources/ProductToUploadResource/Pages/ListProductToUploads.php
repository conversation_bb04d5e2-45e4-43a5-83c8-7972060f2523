<?php

namespace App\Filament\App\Resources\ProductToUploadResource\Pages;

use App\Filament\App\Resources\ProductToUploadResource;
use App\Filament\App\Resources\ProductUploadTaskResource\Widgets\ProductUploadStatusStats;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListProductToUploads extends ListRecords
{
    protected static string $resource = ProductToUploadResource::class;
    protected static ?string $title = '';
    protected function getHeaderActions(): array
    {
        return [
            //Actions\CreateAction::make(),
        ];
    }
    protected function getHeaderWidgets(): array
    {
        return [
      
        ];
    }
}
