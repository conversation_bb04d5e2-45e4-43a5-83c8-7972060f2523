<?php

namespace App\Filament\App\Resources\SmartSkuResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class SupplierProductsRelationManager extends RelationManager
{
    protected static string $relationship = 'supplier_products';
    protected static ?string $recordTitleAttribute = 'sku';
    protected static ?string $title = 'Products';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('supplier_name')
                    ->required()
                    ->maxLength(191),

                Forms\Components\TextInput::make('price')
                    ->required()
                    ->numeric()
                    ->prefix('$'),

                Forms\Components\TextInput::make('sku')
                    ->required()
                    ->maxLength(191),

                Forms\Components\TextInput::make('variant_id')
                    ->required()
                    ->maxLength(191),

                Forms\Components\TextInput::make('style')
                    ->required()
                    ->maxLength(191),

                Forms\Components\TextInput::make('color')
                    ->required()
                    ->maxLength(191),

                Forms\Components\TextInput::make('size')
                    ->required()
                    ->maxLength(191),

                Forms\Components\Toggle::make('active')
                    ->required(),

                Forms\Components\Toggle::make('tiktok'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('supplier_name'),
                Tables\Columns\TextColumn::make('price')
                    ->money(),
                Tables\Columns\TextColumn::make('sku'),
                Tables\Columns\TextColumn::make('variant_id'),
                Tables\Columns\IconColumn::make('active')
                    ->boolean(),
                Tables\Columns\IconColumn::make('tiktok')
                    ->boolean(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}