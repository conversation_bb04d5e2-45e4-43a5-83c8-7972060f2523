<?php

namespace App\Filament\App\Resources\SmartSkuResource\Pages;

use App\Filament\App\Resources\SmartSkuResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSmartSkus extends ListRecords
{
    protected static string $resource = SmartSkuResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
