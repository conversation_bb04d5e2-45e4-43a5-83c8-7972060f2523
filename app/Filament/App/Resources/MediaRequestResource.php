<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\MediaRequestResource\Pages;
use App\Models\MediaRequest;
use App\Services\MediaRequestService;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use App\Enums\MediaRequestStatus;
use Filament\Notifications\Notification;

class MediaRequestResource extends Resource
{
    protected static ?string $model = MediaRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-video-camera';

    protected static ?string $navigationGroup = 'Design & Media';

    protected static ?int $navigationSort = 3;

    public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Developer', 'Media Manager', 'Media', 'Seller']);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['seller', 'mediaHandler', 'production', 'mediaService']))
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('priority')
                    ->badge()
                    ->formatStateUsing(fn($state) => match($state) {
                        'low' => '🟢 Thấp',
                        'normal' => '🟡 Bình thường',
                        'high' => '🟠 Cao',
                        'urgent' => '🔴 Khẩn cấp',
                        default => $state
                    })
                    ->color(fn($state) => match($state) {
                        'low' => 'success',
                        'normal' => 'warning',
                        'high' => 'danger',
                        'urgent' => 'danger',
                        default => 'gray'
                    })
                    ->sortable(),

                // Tables\Columns\ViewColumn::make('production.design_files')
                //     ->label('Designs')
                //     ->view('filament.tables.columns.design-files')
                //     ->toggleable(),

                Tables\Columns\TextColumn::make('mediaService.name')
                    ->label('Dịch vụ')
                    ->searchable()
                    ->sortable()
                    ->placeholder('N/A')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('seller.name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('mediaHandler.name')
                    ->searchable()
                    ->sortable()
                    ->label('Handler')
                    ->placeholder('Chưa phân công'),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(fn(MediaRequestStatus $state) => $state->getLabel())
                    ->icon(fn(MediaRequestStatus $state) => $state->getIcon())
                    ->color(fn(MediaRequestStatus $state) => $state->getColor()),

                Tables\Columns\TextColumn::make('deadline')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->color(fn($record) => $record->deadline && $record->deadline->isPast() ? 'danger' : 'gray')
                    ->icon(fn($record) => $record->deadline && $record->deadline->isPast() ? 'heroicon-o-exclamation-triangle' : 'heroicon-o-clock')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('quantity')
                    ->label('Số lượng')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('price')
                    ->label('Giá đơn vị')
                    ->money('USD')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Tổng tiền')
                    ->money('USD')
                    ->sortable()
                    ->weight('bold')
                    ->color('primary')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->date()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(MediaRequestStatus::class),

                Tables\Filters\SelectFilter::make('priority')
                    ->options([
                        'low' => '🟢 Thấp',
                        'normal' => '🟡 Bình thường',
                        'high' => '🟠 Cao',
                        'urgent' => '🔴 Khẩn cấp',
                    ]),

                Tables\Filters\SelectFilter::make('media_handler_id')
                    ->relationship('mediaHandler', 'name')
                    ->label('Media Handler'),

                Tables\Filters\Filter::make('overdue')
                    ->label('Quá hạn')
                    ->query(fn (Builder $query): Builder => $query->where('deadline', '<', now()))
                    ->indicator('Quá hạn'),

                Tables\Filters\Filter::make('due_soon')
                    ->label('Sắp hết hạn (6h)')
                    ->query(fn (Builder $query): Builder => $query->whereBetween('deadline', [now(), now()->addHours(6)]))
                    ->indicator('Sắp hết hạn'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),

                Tables\Actions\Action::make('auto_assign')
                    ->label('Auto Assign')
                    ->icon('heroicon-o-user-plus')
                    ->color('info')
                    ->visible(fn($record) => !$record->media_handler_id)
                    ->action(function ($record) {
                        $service = app(MediaRequestService::class);
                        $success = $service->autoAssignHandler($record);

                        if ($success) {
                            Notification::make()
                                ->title('Đã phân công tự động')
                                ->body('Media Handler ít việc nhất đã được phân công cho yêu cầu này')
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Không thể phân công')
                                ->body('Không có Media Handler nào khả dụng')
                                ->warning()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Tự động phân công Media Handler')
                    ->modalDescription('Hệ thống sẽ tự động phân công cho Media Handler ít việc nhất'),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMediaRequests::route('/'),
            'create' => Pages\CreateMediaRequest::route('/create'),
            'edit' => Pages\EditMediaRequest::route('/{record}/edit'),
            // 'view' => Pages\ViewMediaRequest::route('/{record}'),
        ];
    }

}
