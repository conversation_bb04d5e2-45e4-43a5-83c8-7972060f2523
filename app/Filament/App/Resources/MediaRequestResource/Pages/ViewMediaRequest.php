<?php

namespace App\Filament\App\Resources\MediaRequestResource\Pages;

use App\Filament\App\Resources\MediaRequestResource;
use App\Enums\MediaRequestStatus;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Support\Enums\MaxWidth;

class ViewMediaRequest extends ViewRecord
{
    protected static string $resource = MediaRequestResource::class;

    public function getTitle(): string
    {
        return 'Chi tiết Media Request #' . $this->record->id;
    }

    public function getSubheading(): string
    {
        $createdAt = is_string($this->record->created_at)
            ? \Carbon\Carbon::parse($this->record->created_at)->format('d/m/Y H:i')
            : $this->record->created_at->format('d/m/Y H:i');

        return "📅 Tạo lúc: {$createdAt} • 👤 Seller: {$this->record->seller->name}";
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Thông tin cơ bản')
                    ->icon('heroicon-o-information-circle')
                    ->schema([
                        Infolists\Components\TextEntry::make('title')
                            ->label('Tiêu đề')
                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large)
                            ->weight('bold'),

                        Infolists\Components\TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge()
                            ->formatStateUsing(fn(MediaRequestStatus $state) => $state->getLabel())
                            ->icon(fn(MediaRequestStatus $state) => $state->getIcon())
                            ->color(fn(MediaRequestStatus $state) => $state->getColor()),

                        Infolists\Components\TextEntry::make('seller.name')
                            ->label('Seller')
                            ->icon('heroicon-o-user'),

                        Infolists\Components\TextEntry::make('mediaService.name')
                            ->label('Loại dịch vụ')
                            ->icon('heroicon-o-video-camera')
                            ->placeholder('N/A'),

                        Infolists\Components\TextEntry::make('price')
                            ->label('Giá')
                            ->money('USD')
                            ->icon('heroicon-o-currency-dollar'),

                        Infolists\Components\TextEntry::make('mediaHandler.name')
                            ->label('Media Handler')
                            ->icon('heroicon-o-user-group')
                            ->placeholder('Chưa được phân công'),

                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Ngày tạo')
                            ->dateTime()
                            ->icon('heroicon-o-calendar'),

                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Cập nhật cuối')
                            ->dateTime()
                            ->icon('heroicon-o-clock'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Production liên quan')
                    ->icon('heroicon-o-cube')
                    ->schema([
                        Infolists\Components\TextEntry::make('production.name')
                            ->label('Tên Production')
                            ->placeholder('Không có production liên quan'),

                        Infolists\Components\TextEntry::make('production.blank.name')
                            ->label('Blank')
                            ->placeholder('N/A'),

                        Infolists\Components\TextEntry::make('production.quantity')
                            ->label('Số lượng')
                            ->placeholder('N/A'),

                        Infolists\Components\TextEntry::make('production.status')
                            ->label('Trạng thái Production')
                            ->badge()
                            ->placeholder('N/A'),
                    ])
                    ->columns(2)
                    ->visible(fn () => $this->record->production_id),

                Infolists\Components\Section::make('Mô tả yêu cầu')
                    ->icon('heroicon-o-document-text')
                    ->schema([
                        Infolists\Components\TextEntry::make('description')
                            ->label('')
                            ->html()
                            ->placeholder('Không có mô tả'),
                    ])
                    ->collapsible(),

                Infolists\Components\Section::make('Video & Media')
                    ->icon('heroicon-o-video-camera')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('video_url')
                            ->label('Videos')
                            ->schema([
                                Infolists\Components\TextEntry::make('.')
                                    ->label('')
                                    ->formatStateUsing(function ($state) {
                                        $filename = basename($state);
                                        $url = asset('storage/' . $state);
                                        return new \Illuminate\Support\HtmlString(
                                            "<div class='flex items-center gap-2'>
                                                <video controls class='rounded' style='width: auto; height: auto; max-width: 200px; max-height: 150px;'>
                                                    <source src='{$url}' type='video/mp4'>
                                                    Your browser does not support the video tag.
                                                </video>
                                                <div>
                                                    <p class='font-medium'>{$filename}</p>
                                                    <a href='{$url}' download class='text-primary-600 hover:text-primary-500'>
                                                        <span class='flex items-center gap-1'>
                                                            <svg class='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'></path>
                                                            </svg>
                                                            Tải xuống
                                                        </span>
                                                    </a>
                                                </div>
                                            </div>"
                                        );
                                    }),
                            ])
                            ->placeholder('Chưa có video nào được tải lên'),
                    ])
                    ->visible(fn () => $this->record->video_url && count($this->record->video_url) > 0),

                Infolists\Components\Section::make('Lý do từ chối')
                    ->icon('heroicon-o-x-circle')
                    ->schema([
                        Infolists\Components\TextEntry::make('rejection_reason')
                            ->label('')
                            ->placeholder('Không có lý do từ chối'),
                    ])
                    ->visible(fn () => $this->record->status === MediaRequestStatus::Rejected && $this->record->rejection_reason)
                    ->collapsible(),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Chỉnh sửa')
                ->icon('heroicon-o-pencil'),

            Actions\Action::make('back_to_list')
                ->label('Quay lại danh sách')
                ->icon('heroicon-m-arrow-left')
                ->color('gray')
                ->url(fn (): string => MediaRequestResource::getUrl('index')),
        ];
    }
}