<?php

namespace App\Filament\App\Resources\MediaRequestResource\Pages;

use App\Filament\App\Resources\MediaRequestResource;
use App\Models\Production;
use App\Models\MediaServicePricing;
use App\Enums\MediaRequestStatus;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;
use App\Forms\Components\ProductionSelectorField;
use App\Services\LarkService;

class CreateMediaRequest extends CreateRecord
{
    protected static string $resource = MediaRequestResource::class;

    public function getTitle(): string
    {
        return 'Tạo Media Request mới';
    }

    public function getSubheading(): string
    {
        return 'Tạo yêu cầu xử lý media cho production';
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Thông tin cơ bản')
                ->description('Nhập thông tin yêu cầu media')
                ->icon('heroicon-o-information-circle')
                ->schema([
                    Forms\Components\TextInput::make('title')
                        ->label('Tiêu đề')
                        ->required()
                        ->maxLength(255)
                        ->placeholder('Nhập tiêu đề cho media request...')
                        ->columnSpan(1),

                    Forms\Components\Radio::make('media_handler_id')
                        ->label('Chọn người xử lý media cho yêu cầu này')
                        ->options(function () {
                            return \App\Models\MediaRequest::getMediaHandlerOptions();
                        })
                        ->descriptions(function () {
                            // Lấy thông tin chi tiết về workload của từng handler
                            $handlers = \App\Models\User::whereHas('roles', function ($query) {
                                $query->where('name', 'Media');
                            })->withCount(['mediaRequests' => function ($query) {
                                $query->whereNotIn('status', ['completed', 'rejected']);
                            }])->get();

                            $descriptions = [];
                            foreach ($handlers as $handler) {
                                $descriptions[$handler->id] = "Đang xử lý: {$handler->media_requests_count} yêu cầu";
                            }
                            return $descriptions;
                        })
                      
                        ->default(function () {
                            // Tìm Media Handler có ít task nhất
                            $mediaHandlers = \App\Models\User::whereHas('roles', function ($query) {
                                $query->where('name', 'Media');
                            })->withCount(['mediaRequests' => function ($query) {
                                $query->whereNotIn('status', ['completed', 'rejected']);
                            }])->orderBy('media_requests_count', 'asc')->first();

                            return $mediaHandlers?->id;
                        })
                        ->afterStateUpdated(function ($state, $set) {
                            // Nếu có chọn media handler, tự động chuyển status sang InProgress
                            if ($state) {
                                $set('status', MediaRequestStatus::InProgress->value);
                            } else {
                                $set('status', MediaRequestStatus::Pending->value);
                            }
                        })
                        ->columnSpan(1),

                    ProductionSelectorField::make('production_id')
                        ->label('Production')
                        ->columnSpanFull()
                        ->hidden(fn ($livewire) => $livewire instanceof RelationManager),

                    Forms\Components\Select::make('media_service_type')
                        ->label('Loại dịch vụ')
                        ->options(function () {
                            return MediaServicePricing::active()
                                ->ordered()
                                ->get()
                                ->mapWithKeys(function ($service) {
                                    return [
                                        $service->service_type => $service->name . ' (' . $service->getFormattedPrice() . ')'
                                    ];
                                });
                        })
                        ->default(function () {
                            // Chọn loại dịch vụ đầu tiên
                            return MediaServicePricing::active()->ordered()->first()?->service_type;
                        })
                        ->required()
                        ->live()
                        ->helperText('Chọn loại dịch vụ media phù hợp')
                        ->columnSpan(1),

                    Forms\Components\TextInput::make('quantity')
                        ->label('Số lượng video')
                        ->numeric()
                        ->default(1)
                        ->minValue(1)
                        ->maxValue(100)
                        ->required()
                        ->live()
                        ->helperText('Số lượng video cần làm')
                        ->columnSpan(1),

                   Forms\Components\Placeholder::make('service_details')
                                        ->label('Chi tiết dịch vụ')
                                        ->content(function ($get, $livewire) {
                                            $serviceType = $get('media_service_type');
                                            $quantity = $get('quantity') ?: 1;

                                            if (!$serviceType) {
                                                return new \Illuminate\Support\HtmlString(
                                                    "<div class='text-sm text-gray-500'>Vui lòng chọn loại dịch vụ</div>"
                                                );
                                            }

                                            $service = MediaServicePricing::where('service_type', $serviceType)->first();
                                            if (!$service) {
                                                return new \Illuminate\Support\HtmlString(
                                                    "<div class='text-sm text-red-500'>Không tìm thấy thông tin dịch vụ</div>"
                                                );
                                            }

                                            $unitPrice = $service->base_price;
                                            $totalPrice = $unitPrice * $quantity;
                                            $totalHours = $service->estimated_hours * $quantity;

                                            return new \Illuminate\Support\HtmlString(
                                                "<div class='text-sm space-y-2 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border'>
                                                    <div class='grid grid-cols-2 gap-4'>
                                                        <div>
                                                            <span class='font-medium text-gray-700 dark:text-gray-300'>Giá đơn vị:</span>
                                                            <span class='text-gray-900 dark:text-gray-100'>{$service->getFormattedPrice()}</span>
                                                        </div>
                                                        <div>
                                                            <span class='font-medium text-gray-700 dark:text-gray-300'>Số lượng:</span>
                                                            <span class='text-gray-900 dark:text-gray-100'>{$quantity} video</span>
                                                        </div>
                                                        <div class='col-span-2'>
                                                            <span class='font-medium text-gray-700 dark:text-gray-300'>Tổng tiền:</span>
                                                            <span class='text-xl font-bold text-primary-600 dark:text-primary-400'>$" . number_format($totalPrice, 2) . "</span>
                                                        </div>
                                                        <div>
                                                            <span class='font-medium text-gray-700 dark:text-gray-300'>Thời gian giao:</span>
                                                            <span class='text-gray-900 dark:text-gray-100'>{$service->delivery_days} ngày</span>
                                                        </div>
                                                        <div>
                                                            <span class='font-medium text-gray-700 dark:text-gray-300'>Ước tính:</span>
                                                            <span class='text-gray-900 dark:text-gray-100'>{$totalHours} giờ</span>
                                                        </div>
                                                    </div>
                                                </div>"
                                            );
                                        })
                                        ->reactive()
                                        ->columnSpan(2),

                    Forms\Components\RichEditor::make('description')
                        ->label('Mô tả yêu cầu')
                        ->toolbarButtons([
                            'bold',
                            'italic',
                            'link',
                            'bulletList',
                            'orderedList',
                        ])
                        ->placeholder('Mô tả chi tiết yêu cầu xử lý media (tùy chọn)...')
                        ->columnSpanFull(),

                    Forms\Components\Hidden::make('seller_id')
                        ->default(Auth::id()),

                    Forms\Components\Hidden::make('status')
                        ->default(function ($get) {
                            // Nếu có chọn media handler, chuyển sang InProgress
                            // if ($get('media_handler_id')) {
                            //     return MediaRequestStatus::InProgress->value;
                            // }
                            return MediaRequestStatus::Pending->value;
                        }),
                ])
                ->columns(2)
        ]);
    }

    protected function getCreatedNotification(): ?Notification
    {
        $body = 'Media Request đã được tạo thành công';

        if ($this->record->media_handler_id) {
            $handlerName = $this->record->mediaHandler?->name ?? 'Media Handler';
            $body .= " và đã được phân công cho {$handlerName}";
        }

        return Notification::make()
            ->success()
            ->title('Tạo thành công')
            ->body($body)
            ->icon('heroicon-o-check-circle');
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('edit', ['record' => $this->record]);
    }

    protected function afterCreate(): void
    {
        // Gửi thông báo cho media handler nếu được chọn
        if ($this->record->media_handler_id && $this->record->mediaHandler) {
            // Gửi thông báo database
            Notification::make()
                ->title('Yêu cầu Media mới được phân công')
                ->body("Bạn đã được phân công xử lý Media Request: {$this->record->title}")
                ->success()
                ->sendToDatabase($this->record->mediaHandler);

            // Gửi thông báo Lark
            $this->sendLarkNotificationToMediaHandler();
        }
    }

    /**
     * Gửi thông báo Lark cho Media Handler khi có request mới
     */
    private function sendLarkNotificationToMediaHandler(): void
    {
        try {
            $mediaHandler = $this->record->mediaHandler;
            $sender = Auth::user();

            // Kiểm tra Media Handler có cấu hình Lark không
            if (!$mediaHandler->hasLarkBotConfigured()) {
                return; // Không gửi thông báo nếu chưa cấu hình
            }

            // Tạo nội dung thông báo
            $message = "🎬 MEDIA REQUEST MỚI\n\n";
            $message .= "📋 Tiêu đề: {$this->record->title}\n";
            $message .= "👤 Người tạo: {$sender->name}\n";
            $message .= "🎯 Loại dịch vụ: {$this->record->media_service_type}\n";
            $message .= "📊 Số lượng: {$this->record->quantity} video\n";
            $message .= "📅 Ngày tạo: " . $this->record->created_at->format('d/m/Y H:i') . "\n";
            $message .= "📝 Trạng thái: " . $this->record->status->getLabel() . "\n";

            if ($this->record->description) {
                $message .= "\n📄 Mô tả:\n" . strip_tags($this->record->description);
            }

            $message .= "\n\nVui lòng kiểm tra và xử lý request này!";

            // Gửi thông báo qua LarkService
            $larkService = app(LarkService::class);
            $larkSend = $larkService->sendMessage($sender, $mediaHandler, $message);

            // Log kết quả
            if ($larkSend->isSent()) {
                \Log::info('Lark notification sent successfully for MediaRequest', [
                    'media_request_id' => $this->record->id,
                    'media_handler_id' => $mediaHandler->id,
                    'sender_id' => $sender->id,
                ]);
            } else {
                \Log::warning('Failed to send Lark notification for MediaRequest', [
                    'media_request_id' => $this->record->id,
                    'media_handler_id' => $mediaHandler->id,
                    'error' => $larkSend->error_message,
                ]);
            }

        } catch (\Exception $e) {
            \Log::error('Exception while sending Lark notification for MediaRequest', [
                'media_request_id' => $this->record->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
