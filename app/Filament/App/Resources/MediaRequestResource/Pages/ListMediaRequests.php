<?php

namespace App\Filament\App\Resources\MediaRequestResource\Pages;

use App\Filament\App\Resources\MediaRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use App\Enums\MediaRequestStatus;
use Illuminate\Database\Eloquent\Builder;

class ListMediaRequests extends ListRecords
{
    protected static string $resource = MediaRequestResource::class;

    protected function getHeaderActions(): array
    {
        $actions = [
            Actions\CreateAction::make(),
        ];

        // Action quản lý giá đã được xóa

        return $actions;
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All')
                ->icon('heroicon-o-rectangle-stack')
                ->badge(static::getModel()::count())
                ->badgeColor('primary'),
                
            'pending' => Tab::make('Pending')
                ->icon('heroicon-o-clock')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', MediaRequestStatus::Pending->value))
                ->badge($this->getTabBadge('pending'))
                ->badgeColor('gray'),
                
            'in_progress' => Tab::make('In Progress')
                ->icon('heroicon-o-arrow-path')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', MediaRequestStatus::InProgress->value))
                ->badge($this->getTabBadge('in_progress'))
                ->badgeColor('warning'),
                
            'review' => Tab::make('Under Review')
                ->icon('heroicon-o-eye')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', MediaRequestStatus::Review->value))
                ->badge($this->getTabBadge('review'))
                ->badgeColor('info'),
                
            'completed' => Tab::make('Completed')
                ->icon('heroicon-o-check-circle')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', MediaRequestStatus::Completed->value))
                ->badge($this->getTabBadge('completed'))
                ->badgeColor('success'),
                
            'rejected' => Tab::make('Rejected')
                ->icon('heroicon-o-x-circle')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', MediaRequestStatus::Rejected->value))
                ->badge($this->getTabBadge('rejected'))
                ->badgeColor('danger'),
        ];
    }

    protected function getTabBadge(string $status): int
    {
        return match ($status) {
            'pending' => static::getModel()::where('status', MediaRequestStatus::Pending->value)->count(),
            'in_progress' => static::getModel()::where('status', MediaRequestStatus::InProgress->value)->count(),
            'review' => static::getModel()::where('status', MediaRequestStatus::Review->value)->count(),
            'completed' => static::getModel()::where('status', MediaRequestStatus::Completed->value)->count(),
            'rejected' => static::getModel()::where('status', MediaRequestStatus::Rejected->value)->count(),
            default => 0,
        };
    }

    public function getDefaultActiveTab(): string | int | null
    {
        return 'all';
    }
}
