<?php

namespace App\Filament\App\Resources\MediaRequestResource\Pages;

use App\Filament\App\Resources\MediaRequestResource;
use App\Models\MediaServicePricing;
use App\Models\Production;
use App\Enums\MediaRequestStatus;
use App\Forms\Components\VideoQrCode;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class EditMediaRequest extends EditRecord
{
    protected static string $resource = MediaRequestResource::class;

    /**
     * Override để đảm bảo chỉ lưu các fields cần thiết
     */
    protected function handleRecordUpdate(\Illuminate\Database\Eloquent\Model $record, array $data): \Illuminate\Database\Eloquent\Model
    {
        // Debug: Log dữ liệu trước khi xử lý
        \Log::info('HandleRecordUpdate - Raw data:', [
            'video_url_exists' => isset($data['video_url']),
            'video_url_value' => $data['video_url'] ?? 'NOT_SET',
            'video_url_type' => isset($data['video_url']) ? gettype($data['video_url']) : 'N/A',
            'all_keys' => array_keys($data)
        ]);

        // Chỉ lấy các fields có trong fillable của model
        $fillableFields = $record->getFillable();
        $cleanData = collect($data)->only($fillableFields)->toArray();

        // Debug: Log dữ liệu sau khi filter
        \Log::info('HandleRecordUpdate - Clean data:', [
            'fillable_fields' => $fillableFields,
            'video_url_in_clean' => isset($cleanData['video_url']),
            'clean_video_url' => $cleanData['video_url'] ?? 'NOT_SET',
            'clean_data_keys' => array_keys($cleanData)
        ]);

        $record->update($cleanData);

        // Debug: Log dữ liệu sau khi update
        \Log::info('HandleRecordUpdate - After update:', [
            'record_video_url' => $record->video_url,
            'record_video_url_type' => gettype($record->video_url)
        ]);

        return $record;
    }

    /**
     * Kiểm tra xem có được phép chỉnh sửa không
     * Đơn giản: chỉ khóa khi status = Completed
     */
    protected function canEdit(): bool
    {
        // Khi status = Completed, không ai được chỉnh sửa
        // So sánh với enum object thay vì value
        if ($this->record->status === MediaRequestStatus::Completed) {
            return false;
        }

        // Tất cả các trạng thái khác đều cho phép chỉnh sửa
        return true;
    }

    /**
     * Ngăn việc lưu dữ liệu khi status = Completed
     */
    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Debug: Log dữ liệu đầu vào
        \Log::info('MutateFormDataBeforeSave - Input data:', [
            'video_url_exists' => isset($data['video_url']),
            'video_url_value' => $data['video_url'] ?? 'NOT_SET',
            'video_url_type' => isset($data['video_url']) ? gettype($data['video_url']) : 'N/A',
            'status' => $this->record->status->value ?? 'NO_STATUS'
        ]);

        // Nếu status hiện tại là Completed, không cho phép lưu thay đổi
        if ($this->record->status === MediaRequestStatus::Completed) {
            // Throw exception hoặc notification
            \Filament\Notifications\Notification::make()
                ->title('Không thể cập nhật')
                ->body('Media Request đã hoàn thành và không thể chỉnh sửa.')
                ->danger()
                ->send();

            // Trả về dữ liệu gốc (không thay đổi)
            return $this->record->toArray();
        }

        // Loại bỏ các relationship objects khỏi data để tránh lỗi SQL
        unset($data['seller'], $data['media_handler'], $data['production'], $data['mediaService']);

        // Debug: Log dữ liệu sau khi xử lý
        \Log::info('MutateFormDataBeforeSave - Output data:', [
            'video_url_exists' => isset($data['video_url']),
            'video_url_value' => $data['video_url'] ?? 'NOT_SET',
            'video_url_type' => isset($data['video_url']) ? gettype($data['video_url']) : 'N/A'
        ]);

        return $data;
    }

    /**
     * Xử lý data khi load form để đảm bảo video_url hiển thị đúng
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Đảm bảo video_url là array để FileUpload component có thể hiển thị
        if (isset($data['video_url'])) {
            if (is_string($data['video_url'])) {
                $decoded = json_decode($data['video_url'], true);
                $data['video_url'] = $decoded ?: [$data['video_url']];
            } elseif (!is_array($data['video_url'])) {
                $data['video_url'] = [];
            }

            // Lọc bỏ các giá trị null hoặc empty
            $data['video_url'] = array_filter($data['video_url'], function($url) {
                return !empty($url) && $url !== null;
            });
        }

        return $data;
    }

    public function getTitle(): string
    {
        return 'Media Request #' . $this->record->id;
    }

    public function getSubheading(): string
    {
        // Return simple text for subheading, we'll add the badge in the header actions
        $status = $this->record->status;
        return $status ? $status->getLabel() : 'N/A';
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Tabs::make('MediaRequestTabs')
                ->tabs([
                    // Tab 1: Thông tin cơ bản
                    Forms\Components\Tabs\Tab::make('Thông tin cơ bản')
                        ->icon('heroicon-o-information-circle')
                        ->visible(true) // Luôn hiển thị tab này
                        ->schema([
                            // Thông báo trạng thái chỉnh sửa
                            Forms\Components\Section::make()
                                ->schema([
                                    Forms\Components\Placeholder::make('edit_status_info')
                                        ->label('')
                                        ->content(function () {
                                            if ($this->canEdit()) {
                                                return '';
                                            }

                                            $currentUser = auth()->user();
                                            $status = $this->record->status;
                                            $statusLabel = $status->getLabel();

                                            // Chỉ thông báo khi status = Completed
                                            if ($status === MediaRequestStatus::Completed) {
                                                return new \Illuminate\Support\HtmlString("
                                                    <div class='bg-green-50 border border-green-200 rounded-lg p-4'>
                                                        <div class='flex items-center'>
                                                            <svg class='w-5 h-5 text-green-400 mr-2' fill='currentColor' viewBox='0 0 20 20'>
                                                                <path fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z' clip-rule='evenodd'></path>
                                                            </svg>
                                                            <div>
                                                                <h4 class='text-sm font-medium text-green-800'>Media Request đã hoàn thành</h4>
                                                                <p class='text-sm text-green-700 mt-1'>
                                                                    Media Request này đã hoàn thành và không thể chỉnh sửa thêm.
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ");
                                            }

                                            return '';
                                        })
                                        ->columnSpanFull(),
                                ])
                                ->visible(fn() => !$this->canEdit())
                                ->columnSpanFull(),
                            Forms\Components\Grid::make(2)
                                ->schema([
                                    Forms\Components\TextInput::make('title')
                                        ->label('Tiêu đề')
                                        ->required(fn() => $this->canEdit())
                                        ->maxLength(255)
                                        ->disabled(fn() => !$this->canEdit())
                                        ->columnSpan(1),

                                    Forms\Components\Select::make('media_handler_id')
                                        ->label('Media Handler')
                                        ->options(function () {
                                            return \App\Models\MediaRequest::getMediaHandlerOptions();
                                        })
                                        ->searchable()
                                        ->placeholder('Chọn Media Handler...')
                                        ->helperText('Chọn người xử lý media cho yêu cầu này')
                                        ->disabled(fn() => !$this->canEdit())
                                        ->afterStateUpdated(function ($state, $set) {
                                            // Nếu có chọn media handler, tự động chuyển status sang InProgress
                                            if ($state) {
                                                $set('status', MediaRequestStatus::InProgress->value);
                                            } else {
                                                $set('status', MediaRequestStatus::Pending->value);
                                            }
                                        })
                                        ->columnSpan(1),

                                    Forms\Components\Select::make('production_id')
                                        ->label('Production')
                                        ->relationship(
                                            'production',
                                            'name',
                                            fn(Builder $query) => $query->where('seller_id', Auth::id())
                                        )
                                        ->searchable()
                                        ->preload(false)
                                        ->live()
                                        ->disabled(fn() => !$this->canEdit())
                                        ->placeholder('Tìm kiếm production...')
                                        ->getSearchResultsUsing(function (string $search) {
                                            return Production::query()
                                                ->with(['blank', 'product'])
                                                ->where(function ($query) use ($search) {
                                                    $query->where('name', 'like', "%{$search}%")
                                                        ->orWhereHas('blank', function ($query) use ($search) {
                                                            $query->where('name', 'like', "%{$search}%");
                                                        });
                                                })
                                                ->limit(10)
                                                ->get()
                                                ->mapWithKeys(function($production) {
                                                    $label = ($production->blank?->name ?? 'N/A') . ' - ' .
                                                            ($production->product?->name ?? 'N/A') .
                                                            ' (Qty: ' . ($production->quantity ?? 0) . ')';
                                                    return [$production->id => $label];
                                                });
                                        })
                                        ->getOptionLabelUsing(function ($value) {
                                            $production = Production::with(['blank', 'product'])->find($value);
                                            if (!$production) return 'Production not found';

                                            return view('filament.components.production-option', [
                                                'id' => $production->id,
                                                'blank' => $production->blank?->name ?? 'N/A',
                                                'quantity' => $production->quantity ?? 0,
                                                'design_files' => $production->product?->image ?? null,
                                                'product' => $production->product?->name ?? 'N/A',
                                                'status' => $production->status ?? 'pending',
                                            ])->render();
                                        })
                                        ->allowHtml()
                                        ->columnSpan(2),

                                    Forms\Components\Select::make('media_service_type')
                                        ->label('Loại dịch vụ')
                                        ->options(function () {
                                            return MediaServicePricing::active()
                                                ->ordered()
                                                ->get()
                                                ->mapWithKeys(function ($service) {
                                                    return [
                                                        $service->service_type => $service->name . ' (' . $service->getFormattedPrice() . ')'
                                                    ];
                                                });
                                        })
                                        ->default(function () {
                                            // Chọn loại dịch vụ đầu tiên nếu chưa có giá trị
                                            return MediaServicePricing::active()->ordered()->first()?->service_type;
                                        })
                                        ->live()
                                        ->disabled(fn() => !$this->canEdit())
                                        ->afterStateUpdated(function ($state, $set, $get) {
                                            $service = MediaServicePricing::where('service_type', $state)->first();
                                            if ($service) {
                                                $set('price', $service->base_price);
                                                $set('estimated_hours', $service->estimated_hours);

                                                // Cập nhật deadline dựa trên delivery_days
                                                $deliveryDays = $service->delivery_days ?? 1;
                                                $set('deadline', now()->addDays($deliveryDays));

                                                // Tính toán total_amount
                                                $quantity = $get('quantity') ?: 1;
                                                $set('total_amount', $service->base_price * $quantity);
                                            }
                                        })
                                        ->helperText('Chọn loại dịch vụ media phù hợp')
                                        ->columnSpan(1),

                                    Forms\Components\Placeholder::make('service_details')
                                        ->label('')
                                        ->content(function ($get) {
                                            $serviceType = $get('media_service_type');
                                            if (!$serviceType) return '';

                                            $service = MediaServicePricing::where('service_type', $serviceType)->first();
                                            if (!$service) return '';

                                            return new \Illuminate\Support\HtmlString(
                                                view('filament.components.service-detail-card', [
                                                    'service' => $service,
                                                    'compact' => false
                                                ])->render()
                                            );
                                        })
                                        ->visible(fn($get) => filled($get('media_service_type')))
                                        ->columnSpanFull(),

                                    Forms\Components\TextInput::make('quantity')
                                        ->label('Số lượng video')
                                        ->numeric()
                                        ->default(1)
                                        ->minValue(1)
                                        ->maxValue(100)
                                        ->required()
                                        ->live()
                                        ->disabled(fn() => !$this->canEdit())
                                        ->afterStateUpdated(function ($state, $set, $get) {
                                            $price = $get('price') ?: 0;
                                            $quantity = $state ?: 1;
                                            $set('total_amount', $price * $quantity);
                                        })
                                        ->helperText('Số lượng video cần làm')
                                        ->columnSpan(1),

                                    Forms\Components\TextInput::make('price')
                                        ->label('Giá đơn vị ($)')
                                        ->numeric()
                                        ->prefix('$')
                                        ->disabled()
                                        ->dehydrated()
                                        ->afterStateUpdated(function ($state, $set, $get) {
                                            $quantity = $get('quantity') ?: 1;
                                            $price = $state ?: 0;
                                            $set('total_amount', $price * $quantity);
                                        }),

                                    Forms\Components\TextInput::make('total_amount')
                                        ->label('Tổng tiền ($)')
                                        ->numeric()
                                        ->prefix('$')
                                        ->disabled()
                                        ->dehydrated()
                                        ->formatStateUsing(fn ($state) => number_format($state, 2)),

                                    Forms\Components\DateTimePicker::make('deadline')
                                        ->label('Deadline')
                                        ->native(false)
                                        ->displayFormat('d/m/Y H:i')
                                        ->default(function ($get) {
                                            $serviceType = $get('media_service_type');
                                            if ($serviceType) {
                                                $service = MediaServicePricing::where('service_type', $serviceType)->first();
                                                if ($service) {
                                                    $deliveryDays = $service->delivery_days ?? 1;
                                                    return now()->addDays($deliveryDays);
                                                }
                                            }
                                            return now()->addDay(); // Fallback 1 day
                                        })
                                        ->disabled(fn() => !$this->canEdit())
                                        ->required(fn() => $this->canEdit()),

                                    Forms\Components\Select::make('priority')
                                        ->label('Độ ưu tiên')
                                        ->options([
                                            'low' => '🟢 Thấp',
                                            'normal' => '🟡 Bình thường',
                                            'high' => '🟠 Cao',
                                            'urgent' => '🔴 Khẩn cấp',
                                        ])
                                        ->default('normal')
                                        ->disabled(fn() => !$this->canEdit())
                                        ->required(fn() => $this->canEdit()),
                                ]),

                            Forms\Components\RichEditor::make('description')
                                ->label('Mô tả yêu cầu')
                                ->disabled(fn() => !$this->canEdit())
                                ->toolbarButtons([
                                    'bold',
                                    'italic',
                                    'link',
                                    'bulletList',
                                    'orderedList',
                                ])
                                ->placeholder('Mô tả chi tiết yêu cầu xử lý media (tùy chọn)...')
                                ->columnSpanFull(),

                            // View::make('filament.components.production-detail-card')
                            //     ->visible(fn ($get) => filled($get('production_id')))
                            //     ->viewData([
                            //         'production' => null // Initialize as null
                            //     ])
                            //     ->dehydrated(false)
                            //     ->statePath('productionDetail')
                            //     ->afterStateHydrated(function (View $component, $get) {
                            //         $production = Production::with(['blank', 'assignedUser'])
                            //             ->find($get('production_id'));
                            //         $component->viewData(['production' => $production]);
                            //     })
                            //     ->columnSpanFull(),
                        ]),

                    // Tab 2: Xử lý & Quản lý
                    Forms\Components\Tabs\Tab::make('Xử lý & Quản lý')
                        ->icon('heroicon-o-user-group')
                        ->visible(true) // Luôn hiển thị tab này
                        ->schema([
                            Forms\Components\Grid::make(1)
                                ->schema([
                                    Forms\Components\TextInput::make('estimated_hours')
                                        ->label('Ước tính thời gian (giờ)')
                                        ->numeric()
                                        ->default(24)
                                        ->suffix('giờ')
                                        ->disabled(fn() => !$this->canEdit())
                                        ->helperText('Thời gian dự kiến hoàn thành'),
                                ]),

                          // Section hiển thị video và QR codes cho seller xem
                            Forms\Components\Section::make('Video & QR Codes')
                                ->schema([
                                    Forms\Components\Placeholder::make('video_qr_combined')
                                        ->label('')
                                        ->content(function ($record) {
                                            if (!$record || !$record->video_url) {
                                                return new \Illuminate\Support\HtmlString("
                                                    <div class='text-center py-8 text-gray-500'>
                                                        <svg class='w-12 h-12 mx-auto mb-4 text-gray-300' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z'></path>
                                                        </svg>
                                                        <p>Chưa có video nào được tải lên</p>
                                                    </div>
                                                ");
                                            }

                                            // Xử lý video_url - có thể là array hoặc string
                                            $videoUrls = $record->video_url;

                                            if (is_string($videoUrls)) {
                                                // Nếu là string, có thể là JSON hoặc single path
                                                $decoded = json_decode($videoUrls, true);
                                                $videoUrls = $decoded ?: [$videoUrls];
                                            } elseif (!is_array($videoUrls)) {
                                                $videoUrls = [];
                                            }

                                            // Lọc bỏ các giá trị null hoặc empty
                                            $videoUrls = array_filter($videoUrls, function($url) {
                                                return !empty($url) && $url !== null;
                                            });

                                            if (empty($videoUrls)) {
                                                return new \Illuminate\Support\HtmlString("
                                                    <div class='text-center py-8 text-gray-500'>
                                                        <svg class='w-12 h-12 mx-auto mb-4 text-gray-300' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z'></path>
                                                        </svg>
                                                        <p>Chưa có video nào được tải lên</p>
                                                    </div>
                                                ");
                                            }

                                            // Thông báo khác nhau dựa trên status
                                            $statusMessage = match($record->status) {
                                                MediaRequestStatus::Completed => "
                                                    <div class='bg-green-50 border border-green-200 rounded-lg p-4 mb-6'>
                                                        <div class='flex items-center'>
                                                            <svg class='w-5 h-5 text-green-400 mr-2' fill='currentColor' viewBox='0 0 20 20'>
                                                                <path fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z' clip-rule='evenodd'></path>
                                                            </svg>
                                                            <div>
                                                                <h4 class='text-sm font-medium text-green-800'>Video đã hoàn thành</h4>
                                                                <p class='text-sm text-green-700 mt-1'>
                                                                    Video đã được hoàn thành và sẵn sàng sử dụng.
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ",
                                                default => ""
                                            };

                                            $videoHtml = '';
                                            foreach ($videoUrls as $index => $videoPath) {
                                                if (empty($videoPath)) continue;

                                                // Sử dụng Storage facade với disk s3 để lấy URL từ DigitalOcean Spaces
                                                $videoUrl = \Illuminate\Support\Facades\Storage::disk('s3')->url($videoPath);
                                                $videoNumber = (int)$index + 1;

                                                // Tạo QR code cho video này
                                                $qrCodeSvg = \SimpleSoftwareIO\QrCode\Facades\QrCode::size(150)->generate($videoUrl);

                                                $videoHtml .= "
                                                    <div class='bg-white border border-gray-200 rounded-lg p-6 mb-6'>
                                                        <h3 class='text-lg font-semibold text-gray-800 mb-4'>Video {$videoNumber}</h3>
                                                        <div class='grid grid-cols-1 lg:grid-cols-2 gap-6'>
                                                            <!-- Video Player -->
                                                            <div class='space-y-3'>
                                                                <video controls class='w-full rounded-lg shadow-sm' style='max-height: 300px;' preload='metadata'>
                                                                    <source src='{$videoUrl}' type='video/mp4'>
                                                                    <source src='{$videoUrl}' type='video/quicktime'>
                                                                    Trình duyệt của bạn không hỗ trợ video.
                                                                </video>
                                                                <div class='flex gap-2'>
                                                                    <a href='{$videoUrl}' download class='inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100'>
                                                                        <svg class='w-4 h-4 mr-2' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'></path>
                                                                        </svg>
                                                                        Tải xuống
                                                                    </a>
                                                                    <button onclick='navigator.clipboard.writeText(\"{$videoUrl}\")' class='inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100'>
                                                                        <svg class='w-4 h-4 mr-2' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z'></path>
                                                                        </svg>
                                                                        Copy URL
                                                                    </button>
                                                                </div>
                                                            </div>

                                                            <!-- QR Code -->
                                                            <div class='flex flex-col items-center justify-center space-y-3'>
                                                                <div class='bg-white p-4 rounded-lg shadow-sm border'>
                                                                    {$qrCodeSvg}
                                                                </div>
                                                                <div class='text-center'>
                                                                    <p class='text-sm font-medium text-gray-700'>QR Code cho Video {$videoNumber}</p>
                                                                    <p class='text-xs text-gray-500 mt-1'>Quét để xem video trên điện thoại</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ";
                                            }

                                            return new \Illuminate\Support\HtmlString("
                                                <div class='space-y-4'>
                                                    {$statusMessage}
                                                    {$videoHtml}
                                                </div>
                                            ");
                                        })
                                        ->columnSpanFull(),
                                ])
                                ->visible(function ($record) {
                                    if (!$record || !$record->video_url) {
                                        return false;
                                    }

                                    // Xử lý video_url - có thể là array hoặc string
                                    $videoUrls = $record->video_url;
                                    if (is_string($videoUrls)) {
                                        $decoded = json_decode($videoUrls, true);
                                        $videoUrls = $decoded ?: [$videoUrls];
                                    } elseif (!is_array($videoUrls)) {
                                        return false;
                                    }

                                    // Lọc bỏ các giá trị null hoặc empty
                                    $videoUrls = array_filter($videoUrls, function($url) {
                                        return !empty($url) && $url !== null;
                                    });

                                    return !empty($videoUrls);
                                })
                                ->collapsible(),

                            // Review Actions Section (chỉ hiển thị khi status = Review)
                            Forms\Components\Section::make('Duyệt Video')
                                ->description('Video đã được tải lên và đang chờ bạn duyệt. Vui lòng xem video và quyết định.')
                                ->schema([
                                    Forms\Components\Actions::make([
                                        Forms\Components\Actions\Action::make('approve_video')
                                            ->label('Duyệt video')
                                            ->icon('heroicon-m-check-circle')
                                            ->color('success')
                                            ->requiresConfirmation()
                                            ->modalHeading('Duyệt video')
                                            ->modalDescription('Bạn có chắc chắn muốn duyệt video này? Sau khi duyệt, Media Request sẽ được đánh dấu là hoàn thành.')
                                            ->modalSubmitActionLabel('Duyệt video')
                                            ->action(function () {
                                                $this->record->update(['status' => MediaRequestStatus::Completed]);
                                                $this->sendStatusNotification(MediaRequestStatus::Completed);

                                                Notification::make()
                                                    ->title('Video đã được duyệt')
                                                    ->body('Media Request đã hoàn thành thành công')
                                                    ->success()
                                                    ->send();
                                            }),

                                        Forms\Components\Actions\Action::make('request_revision')
                                            ->label('Yêu cầu làm lại')
                                            ->icon('heroicon-m-arrow-path')
                                            ->color('warning')
                                            ->form([
                                                Forms\Components\Textarea::make('revision_feedback')
                                                    ->label('Góp ý cho Media Handler')
                                                    ->placeholder('Vui lòng mô tả những điều cần chỉnh sửa...')
                                                    ->required()
                                                    ->rows(4)
                                            ])
                                            ->requiresConfirmation()
                                            ->modalHeading('Yêu cầu làm lại video')
                                            ->modalDescription('Video sẽ được gửi lại cho Media Handler để chỉnh sửa theo yêu cầu của bạn.')
                                            ->modalSubmitActionLabel('Gửi yêu cầu')
                                            ->action(function (array $data) {
                                                $this->record->update([
                                                    'status' => MediaRequestStatus::InProgress,
                                                    'revision_notes' => $data['revision_feedback']
                                                ]);
                                                $this->sendStatusNotification(MediaRequestStatus::InProgress);

                                                // Gửi thông báo đặc biệt cho Media Handler về revision
                                                if ($this->record->mediaHandler) {
                                                    $revisionNotification = Notification::make()
                                                        ->title('Yêu cầu chỉnh sửa video')
                                                        ->body("Seller đã yêu cầu chỉnh sửa video cho Media Request #{$this->record->id}")
                                                        ->warning();
                                                    $revisionNotification->sendToDatabase($this->record->mediaHandler);
                                                }

                                                Notification::make()
                                                    ->title('Đã gửi yêu cầu chỉnh sửa')
                                                    ->body('Media Handler sẽ nhận được thông báo và tiến hành chỉnh sửa')
                                                    ->success()
                                                    ->send();
                                            }),
                                    ])
                                        ->columnSpanFull()
                                        ->alignment('center'),
                                ])
                                ->visible(function () {

                                    // Chỉ cho phép seller tạo ra job này được duyệt video
                                    if (!auth()->user()->hasRole(['Seller'])) {
                                        return false;
                                    }
                                  

                                    $currentStatus = $this->record->status;
                                    // Hiển thị khi status = Review và chưa Completed
                                    return $currentStatus === MediaRequestStatus::Review;
                                })
                                ->collapsible(),

                            // Thông báo về việc tải video lên
                            Forms\Components\Section::make('📹 Khu vực tải video')
                                ->schema([
                                    Forms\Components\Placeholder::make('video_upload_info')
                                        ->label('')
                                        ->content(function () {
                                            return new \Illuminate\Support\HtmlString("
                                                <div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>
                                                    <div class='flex items-start'>
                                                        <svg class='w-6 h-6 text-blue-400 mr-3 mt-1 flex-shrink-0' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12'></path>
                                                        </svg>
                                                        <div>
                                                            <h4 class='text-sm font-medium text-blue-800 mb-2'>Đây là nơi video sẽ được tải lên</h4>
                                                            <p class='text-sm text-blue-700 mb-2'>
                                                                Sau khi Media Handler hoàn thành công việc, video sẽ được tải lên và hiển thị tại đây.
                                                            </p>
                                                            <ul class='text-sm text-blue-600 space-y-1'>
                                                                <li>• Video sẽ được tải lên ở tab <strong>\"Xử lý Media\"</strong> bởi Media Handler</li>
                                                                <li>• Sau khi tải lên, video sẽ hiển thị ở phần trên để bạn xem và duyệt</li>
                                                                <li>• Bạn có thể tải xuống hoặc quét mã QR để xem video trên điện thoại</li>
                                                                <li>• Khi hài lòng với video, bạn có thể duyệt để hoàn thành yêu cầu</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            ");
                                        })
                                        ->columnSpanFull(),
                                ])
                                ->collapsible()
                                ->collapsed(false),
                        ]),

                    // Tab 3: Media Handler Actions
                    Forms\Components\Tabs\Tab::make('Xử lý Media')
                        ->icon('heroicon-o-video-camera')
                        ->visible(function () {
                            $currentUser = auth()->user();
                            // Chỉ cho phép Media Manager, Media Handler và Admin truy cập
                            return $currentUser->hasRole(['super_admin', 'User Manager', 'Media Manager', 'Media']) ||
                                   $currentUser->id === $this->record->media_handler_id;
                        })
                        ->schema([
                            Forms\Components\Grid::make(2)
                                ->schema([
                                    Forms\Components\Select::make('status')
                                        ->label('Trạng thái hiện tại')
                                        ->options(function () {
                                            $currentStatus = $this->record->status;
                                            return [$currentStatus->value => $currentStatus->getLabel()];
                                        })
                                        ->dehydrated(false),

                                    Forms\Components\Placeholder::make('status_actions')
                                        ->label('Thay đổi trạng thái')
                                        ->content(function () {
                                            $currentStatus = $this->record->status;
                                            $currentUser = auth()->user();

                                            // Nếu đã Completed, không thể thay đổi
                                            if ($currentStatus === MediaRequestStatus::Completed) {
                                                return '⚠️ Trạng thái đã hoàn thành và không thể thay đổi.';
                                            }

                                            return 'Sử dụng các nút bên dưới để thay đổi trạng thái.';
                                        }),
                                ]),

                            Forms\Components\Actions::make([
                                Forms\Components\Actions\Action::make('set_in_progress')
                                    ->label('Đang xử lý')
                                    ->icon('heroicon-m-arrow-path')
                                    ->color('info')
                                    ->visible(function () {
                                        $currentStatus = $this->record->status;
                                        return $currentStatus === MediaRequestStatus::Pending;
                                    })
                                    ->action(function () {
                                        $this->record->update(['status' => MediaRequestStatus::InProgress]);
                                        $this->sendStatusNotification(MediaRequestStatus::InProgress);

                                        Notification::make()
                                            ->title('Trạng thái đã cập nhật')
                                            ->body('Media Request đã chuyển sang trạng thái "Đang xử lý"')
                                            ->success()
                                            ->send();
                                    }),

                                Forms\Components\Actions\Action::make('set_review')
                                    ->label('Chờ duyệt')
                                    ->color('warning')
                                    ->visible(function () {
                                        $currentStatus = $this->record->status;
                                        return in_array($currentStatus, [MediaRequestStatus::InProgress]);
                                    })
                                    ->action(function () {
                                        $this->record->update(['status' => MediaRequestStatus::Review]);
                                        $this->sendStatusNotification(MediaRequestStatus::Review);

                                        Notification::make()
                                            ->title('Trạng thái đã cập nhật')
                                            ->body('Media Request đã chuyển sang trạng thái "Chờ duyệt"')
                                            ->success()
                                            ->send();
                                    }),

                                Forms\Components\Actions\Action::make('set_completed')
                                    ->label('Hoàn thành')
                                    ->icon('heroicon-m-check-circle')
                                    ->color('success')
                                    ->visible(function () {
                                        $currentStatus = $this->record->status;
                                        return !in_array($currentStatus, [MediaRequestStatus::Completed, MediaRequestStatus::Rejected]);
                                    })
                                    ->requiresConfirmation()
                                    ->modalHeading('Xác nhận hoàn thành')
                                    ->modalDescription('Bạn có chắc chắn muốn đánh dấu Media Request này là hoàn thành? Sau khi hoàn thành, bạn sẽ không thể thay đổi trạng thái nữa.')
                                    ->modalSubmitActionLabel('Xác nhận hoàn thành')
                                    ->action(function () {
                                        $this->record->update(['status' => MediaRequestStatus::Completed]);
                                        $this->sendStatusNotification(MediaRequestStatus::Completed);

                                        Notification::make()
                                            ->title('Media Request đã hoàn thành')
                                            ->body('Trạng thái đã được khóa và không thể thay đổi')
                                            ->success()
                                            ->send();
                                    }),

                                Forms\Components\Actions\Action::make('set_rejected')
                                    ->label('Từ chối')
                                    ->icon('heroicon-m-x-circle')
                                    ->color('danger')
                                    ->visible(function () {
                                        $currentStatus = $this->record->status;
                                        return !in_array($currentStatus, [MediaRequestStatus::Completed, MediaRequestStatus::Rejected]);
                                    })
                                    ->requiresConfirmation()
                                    ->modalHeading('Xác nhận từ chối')
                                    ->modalDescription('Bạn có chắc chắn muốn từ chối Media Request này? Sau khi từ chối, bạn sẽ không thể thay đổi trạng thái nữa.')
                                    ->modalSubmitActionLabel('Xác nhận từ chối')
                                    ->action(function () {
                                        $this->record->update(['status' => MediaRequestStatus::Rejected]);
                                        $this->sendStatusNotification(MediaRequestStatus::Rejected);

                                        Notification::make()
                                            ->title('Media Request đã bị từ chối')
                                            ->body('Trạng thái đã được khóa và không thể thay đổi')
                                            ->danger()
                                            ->send();
                                    }),
                            ])
                                ->visible(function () {
                                    $currentStatus = $this->record->status;
                                    // Chỉ ẩn khi đã Completed
                                    return $currentStatus !== MediaRequestStatus::Completed;
                                })
                                ->columnSpanFull(),

                            Forms\Components\Grid::make(2)
                                ->schema([
                                    Forms\Components\FileUpload::make('video_url')
                                        ->label('Tải lên video (có thể chọn nhiều file)')
                                        ->multiple()
                                        ->disk('s3') // Sử dụng s3 disk (DigitalOcean Spaces)
                                        ->directory('media-requests/videos')
                                        ->acceptedFileTypes(['video/mp4', 'video/quicktime', 'video/avi', 'video/mov'])
                                        ->maxSize(302400) // 300MB
                                        ->maxFiles(5) // Tối đa 5 video
                                        ->downloadable()
                                        ->previewable(true) // Bật preview để hiển thị file hiện có
                                        ->visibility('public') // Đảm bảo file có thể truy cập public
                                        ->disabled(fn() => !$this->canEdit())
                                        ->openable() // Cho phép mở file để xem
                                        ->deletable() // Cho phép xóa file
                                        ->afterStateUpdated(function ($state, $record, $set, $livewire) {
                                            if ($state && $record) {
                                                // Không lưu trực tiếp, chỉ hiển thị thông báo và trigger form save
                                                Notification::make()
                                                    ->title('Video đã được tải lên')
                                                    ->body('Video đang được xử lý, vui lòng nhấn Save để hoàn tất')
                                                    ->success()
                                                    ->send();

                                                // Trigger form save để Filament xử lý file upload đúng cách
                                                // $livewire->save();
                                            }
                                        })
                                        ->helperText('Hỗ trợ: MP4, MOV, AVI, QuickTime. Tối đa 300MB mỗi file. Có thể tải lên tối đa 5 video cùng lúc.')
                                        ->columnSpan(1), // Chiếm 50% chiều rộng

                                    // Cột thứ 2 - Hướng dẫn
                                    Forms\Components\Placeholder::make('upload_info')
                                        ->label('Hướng dẫn')
                                        ->content(new \Illuminate\Support\HtmlString("
                                            <div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>
                                                <h4 class='text-sm font-medium text-blue-800 mb-2'>📹 Hướng dẫn tải video</h4>
                                                <ul class='text-sm text-blue-700 space-y-1'>
                                                    <li>• Chọn file video từ máy tính</li>
                                                    <li>• Có thể chọn nhiều file cùng lúc</li>
                                                    <li>• Video sẽ được tải lên cloud storage</li>
                                                    <li>• Sau khi tải xong, nhấn Save để lưu</li>
                                                </ul>
                                            </div>
                                        "))
                                        ->columnSpan(1),
                                ])
                                ->columnSpanFull(),

                         Forms\Components\Textarea::make('rejection_reason')
                                ->label('Lý do từ chối')
                                ->requiredIf('status', MediaRequestStatus::Rejected->value)
                                ->visible(fn($get) => $get('status') === MediaRequestStatus::Rejected->value)
                                ->disabled(fn() => !$this->canEdit())
                                ->columnSpanFull(),

                            Forms\Components\Textarea::make('revision_notes')
                                ->label('Ghi chú sửa đổi')
                                ->placeholder('Ghi chú về những thay đổi cần thiết...')
                                ->rows(3)
                                ->disabled(fn() => !$this->canEdit())
                                ->columnSpanFull(),
                        ]),
                ])
                ->columnSpanFull()
        ]);
    }

    protected function getHeaderActions(): array
    {
        $actions = [];

        // Status Badge Action (non-clickable, just for display)
        $actions[] = $this->getStatusBadgeAction();

        // Action: Quay lại danh sách
        $actions[] = Actions\Action::make('back_to_list')
            ->label('Quay lại danh sách')
            ->icon('heroicon-m-arrow-left')
            ->color('gray')
            ->url(fn (): string => MediaRequestResource::getUrl('index'))
            ->tooltip('Quay lại danh sách Media Requests');

        // Action: Xem chi tiết Production
        if ($this->record->production_id) {
            $actions[] = Actions\Action::make('view_production')
                ->label('Xem Production')
                ->icon('heroicon-m-cube')
                ->color('info')
                ->url(fn (): string => route('filament.app.resources.productions.edit', ['record' => $this->record->production_id]))
                ->tooltip('Xem thông tin Production liên quan')
                ->openUrlInNewTab();
        }

      
        // Action: Send Notification
        $actions[] = Actions\Action::make('send_notification')
            ->label('Gửi thông báo')
            ->icon('heroicon-m-bell')
            ->color('info')
            ->form([
                \Filament\Forms\Components\Select::make('recipient')
                    ->label('Người nhận')
                    ->options(function () {
                        // Load relationships chỉ khi cần thiết
                        $this->record->loadMissing(['seller', 'mediaHandler']);

                        return [
                            'seller' => 'Seller (' . ($this->record->seller?->name ?? 'N/A') . ')',
                            'handler' => $this->record->mediaHandler ? 'Media Handler (' . $this->record->mediaHandler->name . ')' : 'Chưa có Media Handler',
                            'both' => 'Cả hai',
                        ];
                    })
                    ->required()
                    ->default('seller'),

                \Filament\Forms\Components\TextInput::make('title')
                    ->label('Tiêu đề')
                    ->required()
                    ->default('Cập nhật Media Request #' . $this->record->id),

                \Filament\Forms\Components\Textarea::make('message')
                    ->label('Nội dung')
                    ->required()
                    ->rows(3)
                    ->placeholder('Nhập nội dung thông báo...'),
            ])
            ->action(function (array $data) {
                $sentCount = 0;

                if (in_array($data['recipient'], ['seller', 'both']) && $this->record->seller) {
                    $sellerNotification = Notification::make()
                        ->title($data['title'])
                        ->body($data['message'])
                        ->info();
                    $sellerNotification->sendToDatabase($this->record->seller);
                    $sentCount++;
                }

                if (in_array($data['recipient'], ['handler', 'both']) && $this->record->mediaHandler) {
                    $handlerNotification = Notification::make()
                        ->title($data['title'])
                        ->body($data['message'])
                        ->info();
                    $handlerNotification->sendToDatabase($this->record->mediaHandler);
                    $sentCount++;
                }

                Notification::make()
                    ->title('Gửi thông báo thành công')
                    ->body("Đã gửi thông báo cho {$sentCount} người")
                    ->success()
                    ->send();
            })
            ->modalHeading('Gửi thông báo')
            ->modalSubmitActionLabel('Gửi')
            ->tooltip('Gửi thông báo cho Seller hoặc Media Handler');

        return $actions;
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Cập nhật thành công')
            ->body('Media Request đã được cập nhật thành công')
            ->icon('heroicon-o-check-circle');
    }

    /**
     * Tạo status badge action cho header
     */
    private function getStatusBadgeAction(): Actions\Action
    {
        $status = $this->record->status;

        $statusConfig = match($status) {
            MediaRequestStatus::Pending => [
                'label' => '' . $status->getLabel(),
                'color' => 'warning',
                'icon' => 'heroicon-m-clock'
            ],
            MediaRequestStatus::InProgress => [
                'label' => '' . $status->getLabel(),
                'color' => 'info',
                'icon' => 'heroicon-m-arrow-path'
            ],
            MediaRequestStatus::Review => [
                'label' => '' . $status->getLabel(),
                'color' => 'warning',
                'icon' => 'heroicon-m-eye'
            ],
            MediaRequestStatus::Completed => [
                'label' => '' . $status->getLabel(),
                'color' => 'success',
                'icon' => 'heroicon-m-check-circle'
            ],
            MediaRequestStatus::Rejected => [
                'label' => '❌ ' . $status->getLabel(),
                'color' => 'danger',
                'icon' => 'heroicon-m-x-circle'
            ],
            default => [
                'label' => '❓ Unknown',
                'color' => 'gray',
                'icon' => 'heroicon-m-question-mark-circle'
            ]
        };

        return Actions\Action::make('status_badge')
            ->label($statusConfig['label'])
            ->icon($statusConfig['icon'])
            ->color($statusConfig['color'])
            ->disabled()
            ->extraAttributes(['class' => 'pointer-events-none']);
    }

    /**
     * Gửi thông báo khi thay đổi status
     */
    private function sendStatusNotification(MediaRequestStatus $newStatus): void
    {
        $notificationData = match ($newStatus) {
            MediaRequestStatus::InProgress => [
                'title' => 'Đã bắt đầu xử lý',
                'body' => 'Media request đã được bắt đầu xử lý',
                'type' => 'success'
            ],
            MediaRequestStatus::Review => [
                'title' => 'Chờ duyệt',
                'body' => 'Media request đang chờ duyệt',
                'type' => 'warning'
            ],
            MediaRequestStatus::Completed => [
                'title' => 'Hoàn thành',
                'body' => 'Media request đã hoàn thành',
                'type' => 'success'
            ],
            MediaRequestStatus::Rejected => [
                'title' => 'Từ chối',
                'body' => 'Media request đã bị từ chối',
                'type' => 'danger'
            ],
            default => null,
        };

        if (!$notificationData) return;

        $currentUser = auth()->user();
        $record = $this->record;

        // Logic gửi thông báo dựa trên role của user hiện tại
        if ($currentUser->hasRole('seller') || $currentUser->id === $record->seller_id) {
            // Nếu seller thay đổi status → chỉ gửi cho media handler
            if ($record->media_handler_id && $record->mediaHandler) {
                $handlerNotification = Notification::make()
                    ->title($notificationData['title'])
                    ->body($notificationData['body'] . " - Media Request #{$record->id}");

                if ($notificationData['type'] === 'success') {
                    $handlerNotification->success();
                } elseif ($notificationData['type'] === 'warning') {
                    $handlerNotification->warning();
                } elseif ($notificationData['type'] === 'danger') {
                    $handlerNotification->danger();
                }

                $handlerNotification->sendToDatabase($record->mediaHandler);
            }
        } elseif ($currentUser->hasRole('Media') || $currentUser->id === $record->media_handler_id) {
            // Nếu media handler thay đổi status → chỉ gửi cho seller
            if ($record->seller) {
                $sellerNotification = Notification::make()
                    ->title($notificationData['title'])
                    ->body($notificationData['body'] . " - Media Request #{$record->id}");

                if ($notificationData['type'] === 'success') {
                    $sellerNotification->success();
                } elseif ($notificationData['type'] === 'warning') {
                    $sellerNotification->warning();
                } elseif ($notificationData['type'] === 'danger') {
                    $sellerNotification->danger();
                }

                $sellerNotification->sendToDatabase($record->seller);
            }
        }
    }
}
