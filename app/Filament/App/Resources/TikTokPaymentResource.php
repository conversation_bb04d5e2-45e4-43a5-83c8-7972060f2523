<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\TikTokPaymentResource\Pages;
use App\Filament\App\Resources\TikTokPaymentResource\RelationManagers;
use App\Models\TikTokPayment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Count;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class TikTokPaymentResource extends Resource
{

    public static function getNavigationGroup(): ?string
    {
        return 'TikTok Shop';
    }
    protected static ?string $label = "Tiktok Payment";
    protected static ?string $model = TikTokPayment::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('store_id')
                    ->relationship('store', 'name')
                    ->required()
                    ->searchable(),
                Forms\Components\Select::make('owner_id')
                    ->relationship('owner', 'name')
                    ->required()
                    ->searchable(),
                Forms\Components\TextInput::make('payment_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('amount')
                    ->required()
                    ->numeric()
                    ->prefix('$'),
                Forms\Components\Select::make('currency')
                    ->options([
                        'USD' => 'USD',
                        'GBP' => 'GBP',
                        'EUR' => 'EUR',
                    ])
                    ->required(),
                Forms\Components\TextInput::make('bank_account')
                    ->required()
                    ->maxLength(255),
                Forms\Components\DateTimePicker::make('create_time')
                    ->required(),
                Forms\Components\TextInput::make('exchange_rate')
                    ->required()
                    ->numeric()
                    ->step(0.000001),
                Forms\Components\DateTimePicker::make('paid_time'),
                Forms\Components\TextInput::make('payment_amount_before_exchange')
                    ->required()
                    ->numeric()
                    ->prefix('$'),
                Forms\Components\TextInput::make('reserve_amount')
                    ->required()
                    ->numeric()
                    ->prefix('$'),
                Forms\Components\TextInput::make('settlement_amount')
                    ->required()
                    ->numeric()
                    ->prefix('$'),
                Forms\Components\Select::make('status')
                    ->options([
                        'PAID' => 'Paid',
                        'PENDING' => 'Pending',
                        'FAILED' => 'Failed',
                    ])
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('store.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('owner.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->money(fn($record) => $record->currency)
                    ->summarize([
                        Sum::make()->money('USD')->label('Total Amount'),
                        Count::make()->label('Count'),
                    ])
                    ->sortable(),
                Tables\Columns\TextColumn::make('currency'),
                Tables\Columns\TextColumn::make('bank_account'),
                Tables\Columns\TextColumn::make('create_time')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('paid_time')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('settlement_amount')
                    ->money(fn($record) => $record->currency)
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'success' => 'PAID',
                        'warning' => 'PENDING',
                        'danger' => 'FAILED',
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('store')
                    ->relationship('store', 'name')->preload()
                    ->searchable(),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'PAID' => 'Paid',
                        'PENDING' => 'Pending',
                        'FAILED' => 'Failed',
                    ]),

                // Date Range Filter for Create Time
                DateRangeFilter::make('create_time')
                    ->label('Create Date Range')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->alwaysShowCalendar()
                    ->displayFormat('DD/MM/YYYY')
                    ->separator(' - '),

                // Date Range Filter for Paid Time
                DateRangeFilter::make('paid_time')
                    ->label('Paid Date Range')
                    ->withIndicator()
                    ->timezone(config('app.timezone'))
                    ->alwaysShowCalendar()
                    ->displayFormat('DD/MM/YYYY')
                    ->separator(' - '),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTikTokPayments::route('/'),
            'create' => Pages\CreateTikTokPayment::route('/create'),
            'edit' => Pages\EditTikTokPayment::route('/{record}/edit'),
        ];
    }
}
