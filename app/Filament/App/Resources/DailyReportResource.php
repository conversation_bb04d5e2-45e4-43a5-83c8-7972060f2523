<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\DailyReportResource\Pages;
use App\Filament\App\Resources\DailyReportResource\RelationManagers;
use App\Filament\App\Resources\DailyReportResource\Widgets;
use App\Models\DailyReport;
use App\Models\User;
use App\Traits\HasUserFilter;
use App\Services\VectorSync\DailyReportVectorService;
use App\Models\Team;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\ViewColumn;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use Illuminate\Database\Eloquent\Collection;

class DailyReportResource extends Resource
{
    use HasUserFilter;

    protected static ?string $model = DailyReport::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Báo cáo hàng ngày';
    protected static ?string $modelLabel = 'Báo cáo hàng ngày';
    protected static ?string $pluralModelLabel = 'Báo cáo hàng ngày';
    protected static ?int $navigationSort = 3;

    /**
     * Hiển thị badge số báo cáo chờ duyệt (chỉ trong 3 ngày gần nhất)
     * Chỉ hiển thị với Leader, User Manager và super_admin
     */
    public static function getNavigationBadge(): ?string
    {
        // Chỉ hiển thị badge cho Leader, User Manager và super_admin
        if (!Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Leader'])) {
            return null;
        }

        // Lấy user IDs mà user hiện tại có quyền truy cập
        $accessibleUserIds = static::getAccessibleUserIds();

        $count = static::getModel()::whereIn('seller_id', $accessibleUserIds)
            ->whereIn('status', ['submitted'])
            ->where('report_date', '>=', now()->subDays(3)->startOfDay())
            ->count();

        return $count > 0 ? (string) $count : null;
    }

    /**
     * Màu sắc badge
     */
    public static function getNavigationBadgeColor(): ?string
    {
        // Chỉ hiển thị badge cho Leader, User Manager và super_admin
        if (!Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Leader'])) {
            return null;
        }

        $accessibleUserIds = static::getAccessibleUserIds();
        $count = static::getModel()::whereIn('seller_id', $accessibleUserIds)
            ->whereIn('status', ['submitted'])
            ->where('report_date', '>=', now()->subDays(3)->startOfDay())
            ->count();

        return $count > 10 ? 'warning' : 'primary';
    }

    /**
     * Tooltip cho badge
     */
    public static function getNavigationBadgeTooltip(): ?string
    {
        return 'Số báo cáo chờ duyệt (3 ngày gần nhất)';
    }

    /**
     * Kiểm tra quyền truy cập resource
     */
    public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Seller', 'Leader']);
    }

    /**
     * Kiểm tra quyền xem danh sách
     */
    public static function canViewAny(): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Seller', 'Leader']);
    }

    /**
     * Kiểm tra quyền tạo record
     */
    public static function canCreate(): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Seller', 'Leader']);
    }

    /**
     * Kiểm tra quyền xem chi tiết record
     */
    public static function canView($record): bool
    {
        $user = Auth::user();

        // Super admin và User Manager có thể xem tất cả
        if ($user->hasAnyRole(['super_admin', 'User Manager'])) {
            return true;
        }

        // Leader có thể xem báo cáo của sellers được quản lý + chính mình
        if ($user->hasRole('Leader')) {
            $managedSellerIds = $user->leaderManagedSellers()->pluck('id')->toArray();
            $managedSellerIds[] = $user->id; // Thêm chính leader
            return in_array($record->seller_id, $managedSellerIds);
        }

        // Seller chỉ có thể xem báo cáo của mình
        if ($user->hasRole('Seller')) {
            return $record->seller_id === $user->id;
        }

        return false;
    }

    /**
     * Kiểm tra quyền chỉnh sửa record
     */
    public static function canEdit($record): bool
    {
        $user = Auth::user();

        // Chỉ có thể edit báo cáo ở trạng thái submitted
        if ($record->status !== 'submitted') {
            return false;
        }

        // Chỉ có thể edit báo cáo mà chính mình tạo
        return $record->seller_id === $user->id;
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Thông tin cơ bản - compact display
                Forms\Components\Placeholder::make('report_info')
                    ->label('')
                    ->content(function ($record) {
                        $sellerName = Auth::user()->name ?? 'N/A';
                        $reportDate = $record?->report_date ? $record->report_date->format('d/m/Y') : today()->format('d/m/Y');

                        return "📝 **Báo cáo của:** {$sellerName} | 📅 **Ngày:** {$reportDate}";
                    })
                    ->columnSpanFull(),

                // Hidden fields để lưu dữ liệu
                Forms\Components\Hidden::make('seller_id')
                    ->default(fn() => Auth::id()),

                Forms\Components\Hidden::make('report_date')
                    ->default(today()),

                // Layout 2 cột: Dữ liệu doanh số bên trái, Chi tiết công việc bên phải
                Forms\Components\Grid::make(2)
                    ->schema([
                        // Cột trái - Dữ liệu doanh số
                        Forms\Components\Section::make('📊 Dữ liệu doanh số')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('sales_data.dang_san_pham_so_luong')
                                            ->label('SL đăng SP')
                                            ->numeric()
                                            ->required(),
                                        Forms\Components\TextInput::make('sales_data.dang_san_pham_niche')
                                            ->label('Niche')
                                            ->required(),
                                    ]),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('sales_data.dang_video_so_luong')
                                            ->label('SL Videos')
                                            ->numeric()
                                            ->required(),
                                        Forms\Components\TextInput::make('sales_data.don_hang_tong')
                                            ->label('Tổng đơn')
                                            ->numeric()
                                            ->required(),
                                    ]),

                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('sales_data.chay_quang_cao_ngan_sach')
                                            ->label('Ngân sách QC')
                                            ->required(),
                                        Forms\Components\TextInput::make('sales_data.khach_hang_phan_hoi')
                                            ->label('Phản hồi KH')
                                            ->required(),
                                    ]),

                                Forms\Components\Textarea::make('sales_data.dang_video_acc')
                                    ->label('Tên kênh kiểm tra')
                                    ->placeholder('Ghi tên kênh để kiểm tra')
                                    ->required()
                                    ->rows(2),

                                Forms\Components\Textarea::make('sales_data.don_hang_ghi_chu')
                                    ->label('Ghi chú đơn hàng')
                                    ->required()
                                    ->rows(2),

                                Forms\Components\Textarea::make('sales_data.chay_quang_cao_ket_qua')
                                    ->label('Kết quả quảng cáo')
                                    ->required()
                                    ->rows(2),

                                Forms\Components\Textarea::make('sales_data.khach_hang_feedback')
                                    ->label('Feedback khách hàng')
                                    ->required()
                                    ->rows(2),

                                Forms\Components\Textarea::make('sales_data.kiem_tra_shop_health')
                                    ->label('Shop Health Check')
                                    ->helperText('Acc nào die hay cần kháng và vì phạm nhiều cần ghi vào đây')
                                    ->required()
                                    ->rows(2),
                            ])
                            ->columnSpan(1),

                        // Cột phải - Chi tiết công việc
                        Forms\Components\Section::make('📝 Chi tiết công việc')
                            ->schema([
                                Forms\Components\Textarea::make('work_description')
                                    ->label('Mô tả công việc đã làm trong ngày')
                                    ->placeholder('Mô tả chi tiết các công việc, nhiệm vụ đã thực hiện trong ngày...')
                                    ->rows(8)
                                    ->required(),

                                Forms\Components\Textarea::make('activities')
                                    ->label('Góp ý để cải thiện công việc (không bắt buộc)')
                                    ->placeholder('Nêu những khó khăn, thắc mắc hay đề xuất để tăng hiệu quả công việc...')
                                    ->rows(8),

                                // Trạng thái - chỉ hiển thị cho admin
                                Forms\Components\Select::make('status')
                                    ->label('Trạng thái')
                                    ->options([
                                        'submitted' => 'Đã gửi',
                                        'reviewed' => 'Đã duyệt',
                                        'rejected' => 'Không duyệt',
                                    ])
                                    ->default('submitted')
                                    ->required()
                                    ->disabled(fn($record) => $record && !$record->canEdit())
                                    ->visible(fn() => Auth::user()->hasAnyRole(['super_admin', 'User Manager'])),

                                Forms\Components\Textarea::make('rejection_reason')
                                    ->label('Lý do không duyệt')
                                    ->rows(3)
                                    ->visible(fn($get) => $get('status') === 'rejected')
                                    ->required(fn($get) => $get('status') === 'rejected'),
                            ])
                            ->columnSpan(1),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('seller.name')
                    ->label('Seller & Team')
                    ->formatStateUsing(function ($record) {
                        $sellerName = $record->seller->name ?? 'N/A';
                        $teams = $record->seller->teams ?? collect();
                        $teamNames = $teams->pluck('name')->join(', ') ?: 'Chưa có team';

                        return $sellerName . "\n" . $teamNames;
                    })
                    ->html()
                    ->formatStateUsing(function ($record) {
                        $sellerName = $record->seller->name ?? 'N/A';
                        $teams = $record->seller->teams ?? collect();
                        $teamNames = $teams->pluck('name')->join(', ') ?: 'Chưa có team';

                        return '<div class="space-y-1">
                            <div class="font-medium text-gray-900 dark:text-white">' . $sellerName . '</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">' . $teamNames . '</div>
                        </div>';
                    })
                    ->searchable(['seller.name'])
                    ->sortable()
                    ->visible(fn() => !Auth::user()->hasRole('seller'))
                    ->width('150px'),

                Tables\Columns\TextColumn::make('report_date')
                    ->label('Ngày')
                    ->date('d/m')
                    ->sortable()
                    ->searchable(),

              

                Tables\Columns\TextColumn::make('work_description')
                    ->label('Công việc đã làm')
                    ->limit(100)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 100) {
                            return null;
                        }
                        return $state;
                    })
                    ->wrap()
                    ->width('300px'),

                Tables\Columns\TextColumn::make('activities')
                    ->label('Góp ý')
                    ->limit(100)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 100) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable()
                    ->wrap()
                    ->width('300px'),

                Tables\Columns\TextColumn::make('niche')
                    ->label('Niche')
                    ->getStateUsing(function ($record) {
                        $salesData = $record->sales_data;
                        return $salesData['dang_san_pham_niche'] ?? 'N/A';
                    })
                    ->limit(20)
                    ->tooltip(function ($record): ?string {
                        $salesData = $record->sales_data;
                        $niche = $salesData['dang_san_pham_niche'] ?? null;
                        return ($niche && strlen($niche) > 20) ? $niche : null;
                    })
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\ViewColumn::make('sales_data')
                    ->label('Doanh số')
                    ->view('tables.columns.daily-report-sales-data')
                    ->width('200px'),
                  

                Tables\Columns\TextColumn::make('submitted_at')
                    ->label('Gửi lúc')
                    ->dateTime('H:i d/m')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('processing_info')
                    ->label('Xử lý')
                    ->state(function ($record) {
                        if ($record->status === 'reviewed') {
                            return '✅ ' . ($record->reviewer->name ?? 'N/A');
                        }

                        if ($record->status === 'rejected') {
                            return '❌ ' . ($record->rejector->name ?? 'N/A');
                        }

                        return '⏳ Chờ';
                    })
                    ->tooltip(function ($record): ?string {
                        if ($record->status === 'rejected' && $record->rejection_reason) {
                            return 'Lý do: ' . $record->rejection_reason;
                        }
                        if ($record->status === 'reviewed') {
                            return 'Duyệt bởi ' . ($record->reviewer->name ?? 'N/A') .
                                   ($record->reviewed_at ? ' - ' . $record->reviewed_at->format('d/m H:i') : '');
                        }
                        return null;
                    })
                    ->toggleable(),
                    // ->visible(fn() => Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Leader'])),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'submitted' => 'Đã gửi',
                        'reviewed' => 'Đã duyệt',
                        'rejected' => 'Không duyệt',
                    ]),

                 // Bộ lọc theo Team - chỉ hiển thị cho super_admin, User Manager, Fullfillment Manager
                SelectFilter::make('team')
                    ->label('Team')
                    ->options(function () {
                        return Team::pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['values'] ?? null,
                            fn (Builder $query, $teamIds): Builder => $query->whereHas('seller.teams', function ($query) use ($teamIds) {
                                $query->whereIn('teams.id', $teamIds);
                            })
                        );
                    })
                    ->multiple()
                    ->visible(fn (): bool => Auth::check() && Auth::user()->hasAnyRole(['Fullfillment Manager', 'User Manager', 'super_admin']))
                    ->searchable()
                    ->preload(),

                static::getUserFilter('seller_id', 'Seller'),

                DateRangeFilter::make('report_date')
                    ->label('Khoảng thời gian')
                    ->withIndicator()
                    ->timezone('Asia/Ho_Chi_Minh')
                    ->displayFormat('D/M/Y')
                    ->default([now(), now()])
                    ->ranges([
                        'Hôm nay' => [now(), now()],
                        'Hôm qua' => [now()->subDay(), now()->subDay()],
                        '2 ngày trước' => [now()->subDays(2), now()->subDays(2)],
                        '3 ngày trước' => [now()->subDays(3), now()->subDays(3)],
                        '4 ngày trước' => [now()->subDays(4), now()->subDays(4)],
                        '5 ngày trước' => [now()->subDays(5), now()->subDays(5)],
                        '6 ngày trước' => [now()->subDays(6), now()->subDays(6)],
                        '7 ngày trước' => [now()->subDays(7), now()->subDays(7)],
                    ]),
            ])
            ->persistFiltersInSession()
            ->actions([
                Tables\Actions\ActionGroup::make([
                    // Action::make('submit')
                    //     ->label(fn (DailyReport $record): string =>
                    //         $record->status === 'rejected' ? 'Gửi lại báo cáo' : 'Gửi báo cáo'
                    //     )
                    //     ->icon('heroicon-o-paper-airplane')
                    //     ->color('warning')
                    //     ->visible(fn (DailyReport $record): bool =>
                    //         $record->canSubmit() && Auth::id() === $record->seller_id
                    //     )
                    //     ->action(function (DailyReport $record) {
                    //         $isResubmit = $record->status === 'rejected';
                    //         $record->submit();
                    //         $message = $isResubmit ?
                    //             'Báo cáo đã được gửi lại thành công!' :
                    //             'Báo cáo đã được gửi thành công!';
                    //         Notification::make()
                    //             ->title($message)
                    //             ->success()
                    //             ->send();
                    //     }),

                    Action::make('review')
                        ->label('Duyệt báo cáo')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->visible(fn (DailyReport $record): bool =>
                            $record->status === 'submitted' &&
                            Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Leader']) &&
                            $record->report_date >= now()->subDays(2)->startOfDay()
                        )
                        ->action(function (DailyReport $record) {
                            if ($record->review(Auth::id())) {
                                Notification::make()
                                    ->title('Báo cáo đã được duyệt!')
                                    ->success()
                                    ->send();
                            } else {
                                Notification::make()
                                    ->title('Không thể duyệt báo cáo!')
                                    ->body('Chỉ có thể duyệt báo cáo trong vòng 2 ngày gần nhất.')
                                    ->danger()
                                    ->send();
                            }
                        }),

                    Action::make('reject')
                        ->label('Không duyệt')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->visible(fn (DailyReport $record): bool =>
                            $record->status === 'submitted' &&
                            Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Leader']) &&
                            $record->report_date >= now()->subDays(2)->startOfDay()
                        )
                        ->form([
                            Forms\Components\Textarea::make('rejection_reason')
                                ->label('Lý do không duyệt')
                                ->placeholder('Vui lòng nhập lý do không duyệt báo cáo này...')
                                ->required()
                                ->rows(4)
                                ->maxLength(1000),
                        ])
                        ->action(function (DailyReport $record, array $data) {
                            if ($record->reject(Auth::id(), $data['rejection_reason'])) {
                                Notification::make()
                                    ->title('Báo cáo đã bị không duyệt!')
                                    ->body('Lý do: ' . $data['rejection_reason'])
                                    ->warning()
                                    ->send();
                            } else {
                                Notification::make()
                                    ->title('Không thể không duyệt báo cáo!')
                                    ->body('Chỉ có thể không duyệt báo cáo trong vòng 2 ngày gần nhất.')
                                    ->danger()
                                    ->send();
                            }
                        }),

                    Tables\Actions\EditAction::make()
                        ->label('Sửa')
                        ->icon('heroicon-o-pencil-square'),
                ])
                ->button()
                ->label('Thao tác')
                ->icon('heroicon-o-ellipsis-vertical'),

                Tables\Actions\ViewAction::make()
                    ->label('Xem chi tiết')
                    ->icon('heroicon-o-eye')
                    ->modalHeading('Chi tiết báo cáo')
                    ->modalContent(fn ($record) => view('filament.app.modals.daily-report-view-modal', ['record' => $record]))
                    ->modalWidth('5xl')
                    ->form([
                        // Form đã được thay thế bằng custom view
                    ])


            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    BulkAction::make('generate_summary')
                        ->label('🤖 Tạo tổng hợp báo cáo thông minh')
                        ->icon('heroicon-o-document-chart-bar')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('Tạo tổng hợp báo cáo thông minh')
                        ->modalDescription('AI sẽ phân tích và tạo báo cáo tổng hợp từ các báo cáo đã chọn, sau đó tạo file PDF và upload lên cloud.')
                        ->modalSubmitActionLabel('Tạo báo cáo')
                        ->form([
                            Forms\Components\Select::make('period')
                                ->label('Loại báo cáo')
                                ->options([
                                    'daily' => 'Báo cáo hàng ngày',
                                    'weekly' => 'Báo cáo hàng tuần',
                                    'monthly' => 'Báo cáo hàng tháng',
                                ])
                                ->default('daily')
                                ->required(),
                        ])
                        ->action(function (Collection $records, array $data) {
                            try {
                                $reportIds = $records->pluck('id')->toArray();
                                $period = $data['period'] ?? 'daily';

                                // Gọi Vector service tạo báo cáo AI (giống Command)
                                $vectorService = app(DailyReportVectorService::class);
                                $result = $vectorService->generateSummaryReport($reportIds, $period);

                                if ($result['success']) {
                                    $notification = Notification::make()
                                        ->title('🎉 Tạo báo cáo AI thành công!')
                                        ->body('Báo cáo AI đã được tạo từ Vector Database với ChatGPT analysis.')
                                        ->success()
                                        ->persistent();

                                    // Chỉ thêm action xem PDF nếu có URL
                                    if (!empty($result['data']['pdf_url'])) {
                                        $notification->actions([
                                            \Filament\Notifications\Actions\Action::make('view_pdf')
                                                ->label('📄 Xem PDF')
                                                ->url($result['data']['pdf_url'])
                                                ->openUrlInNewTab(),
                                        ]);
                                    }

                                    $notification->send();
                                } else {
                                    Notification::make()
                                        ->title('❌ Lỗi tạo báo cáo')
                                        ->body($result['error'] ?? 'Có lỗi xảy ra khi tạo báo cáo')
                                        ->danger()
                                        ->persistent()
                                        ->send();
                                }
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('❌ Lỗi hệ thống')
                                    ->body('Có lỗi xảy ra: ' . $e->getMessage())
                                    ->danger()
                                    ->persistent()
                                    ->send();
                            }
                        })
                        ->visible(fn() => Auth::user()->hasAnyRole(['super_admin', 'Super Admin', 'User Manager', 'Leader'])),


                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn() => Auth::user()->hasAnyRole(['super_admin'])),
                ]),
            ])
            ->defaultSort('report_date', 'desc')
            ->recordAction('view')
            ->recordUrl(null);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\MissingReportsWidget::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDailyReports::route('/'),
            'create' => Pages\CreateDailyReport::route('/create'),
            'edit' => Pages\EditDailyReport::route('/{record}/edit'),
        ];
    }

   
}
