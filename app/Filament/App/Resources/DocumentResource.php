<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\DocumentResource\Pages;
use App\Filament\App\Resources\DocumentResource\RelationManagers;
use App\Models\Document;
use Filament\Forms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class DocumentResource extends Resource
{
    protected static ?string $model = Document::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'AI Management';
    protected static ?string $navigationLabel = 'Documents';
    protected static ?string $modelLabel = 'Document';
    protected static ?string $pluralModelLabel = 'Documents';

    /**
     * <PERSON><PERSON>m tra quyền truy cập
     */
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user && $user->hasAnyRole(['super_admin', 'AI Manager', 'User Manager']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin cơ bản')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Tiêu đề')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('key')
                            ->label('Key (Unique Identifier)')
                            ->helperText('Key duy nhất để identify document trong code. VD: daily-reports-analysis-prompt')
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->regex('/^[a-z0-9\-_]+$/')
                            ->validationMessages([
                                'regex' => 'Key chỉ được chứa chữ thường, số, dấu gạch ngang và gạch dưới',
                                'unique' => 'Key này đã tồn tại, vui lòng chọn key khác'
                            ])
                            ->columnSpanFull(),

                        Forms\Components\Select::make('category')
                            ->label('Danh mục')
                            ->options([
                                'API Documentation' => 'API Documentation',
                                'User Guides' => 'User Guides',
                                'Business Processes' => 'Business Processes',
                                'Technical Specs' => 'Technical Specs',
                                'Training Materials' => 'Training Materials',
                                'Policies' => 'Policies',
                                'FAQs' => 'FAQs',
                                'AI Prompts' => 'AI Prompts',
                            ])
                            ->searchable()
                            ->preload(),

                        Forms\Components\TagsInput::make('tags')
                            ->label('Tags')
                            ->placeholder('Nhập tags và nhấn Enter'),

                        Forms\Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'draft' => 'Bản nháp',
                                'published' => 'Đã xuất bản',
                                'archived' => 'Đã lưu trữ',
                            ])
                            ->default('draft')
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Nội dung')
                    ->schema([
                        Forms\Components\FileUpload::make('file_path')
                            ->label('Upload File')
                            ->disk('s3')
                            ->directory('documents')
                            ->acceptedFileTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'text/markdown', 'text/html'])
                            ->maxSize(10240) // 10MB
                            ->columnSpanFull(),

                        Textarea::make('content')
                            ->label('Nội dung')
                            ->columnSpanFull()
                            ->rows(10),
                    ]),

                Forms\Components\Section::make('Embedding Status')
                    ->schema([
                        Forms\Components\Placeholder::make('embedding_status_display')
                            ->label('Trạng thái Embedding')
                            ->content(fn (?Document $record): string => $record?->embedding_status_display ?? 'Chưa xử lý'),

                        Forms\Components\Placeholder::make('word_count')
                            ->label('Số từ')
                            ->content(fn (?Document $record): string => number_format($record?->word_count ?? 0)),

                        Forms\Components\Placeholder::make('chunk_count')
                            ->label('Số chunks')
                            ->content(fn (?Document $record): string => number_format($record?->chunk_count ?? 0)),

                        Forms\Components\Textarea::make('embedding_error')
                            ->label('Lỗi Embedding')
                            ->rows(3)
                            ->disabled()
                            ->visible(fn (?Document $record): bool => !empty($record?->embedding_error)),
                    ])
                    ->columns(3)
                    ->visible(fn (?Document $record): bool => $record !== null),

                Forms\Components\Hidden::make('created_by')
                    ->default(fn () => Auth::id()),
                Forms\Components\Hidden::make('updated_by')
                    ->default(fn () => Auth::id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('Tiêu đề')
                    ->searchable()
                    ->sortable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('key')
                    ->label('Key')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->copyMessage('Key đã được copy!')
                    ->copyMessageDuration(1500)
                    ->badge()
                    ->color('gray')
                    ->limit(30),

                Tables\Columns\BadgeColumn::make('category')
                    ->label('Danh mục')
                    ->searchable()
                    ->colors([
                        'primary' => 'API Documentation',
                        'success' => 'User Guides',
                        'warning' => 'Business Processes',
                        'danger' => 'Technical Specs',
                        'info' => 'Training Materials',
                        'secondary' => 'Policies',
                        'gray' => 'FAQs',
                        'purple' => 'AI Prompts',
                    ]),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'warning' => 'draft',
                        'success' => 'published',
                        'gray' => 'archived',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'draft' => 'Bản nháp',
                        'published' => 'Đã xuất bản',
                        'archived' => 'Đã lưu trữ',
                        default => $state,
                    }),

                Tables\Columns\BadgeColumn::make('embedding_status')
                    ->label('Embedding')
                    ->colors([
                        'gray' => 'pending',
                        'warning' => 'processing',
                        'success' => 'completed',
                        'danger' => 'failed',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'pending' => 'Chờ xử lý',
                        'processing' => 'Đang xử lý',
                        'completed' => 'Hoàn thành',
                        'failed' => 'Thất bại',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('word_count')
                    ->label('Số từ')
                    ->numeric()
                    ->sortable()
                    ->formatStateUsing(fn (int $state): string => number_format($state)),

                Tables\Columns\TextColumn::make('chunk_count')
                    ->label('Chunks')
                    ->numeric()
                    ->sortable()
                    ->formatStateUsing(fn (int $state): string => number_format($state)),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Người tạo')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label('Danh mục')
                    ->options([
                        'API Documentation' => 'API Documentation',
                        'User Guides' => 'User Guides',
                        'Business Processes' => 'Business Processes',
                        'Technical Specs' => 'Technical Specs',
                        'Training Materials' => 'Training Materials',
                        'Policies' => 'Policies',
                        'FAQs' => 'FAQs',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'draft' => 'Bản nháp',
                        'published' => 'Đã xuất bản',
                        'archived' => 'Đã lưu trữ',
                    ]),

                Tables\Filters\SelectFilter::make('embedding_status')
                    ->label('Trạng thái Embedding')
                    ->options([
                        'pending' => 'Chờ xử lý',
                        'processing' => 'Đang xử lý',
                        'completed' => 'Hoàn thành',
                        'failed' => 'Thất bại',
                    ]),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Từ ngày'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Đến ngày'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Xem'),

                Tables\Actions\EditAction::make()
                    ->label('Sửa'),

                Tables\Actions\Action::make('download')
                    ->label('Tải xuống')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('info')
                    ->url(fn (Document $record): ?string => $record->file_url)
                    ->openUrlInNewTab()
                    ->visible(fn (Document $record): bool => !empty($record->file_path)),

                Tables\Actions\Action::make('embed_to_zilliz')
                    ->label('🚀 Embed to Zilliz')
                    ->icon('heroicon-o-cloud-arrow-up')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Embed Document to Zilliz')
                    ->modalDescription('Tạo embedding và upload trực tiếp lên Zilliz Cloud cho document này.')
                    ->action(function (Document $record) {
                        try {
                            if ($record->status !== 'published') {
                                Notification::make()
                                    ->title('Không thể embedding')
                                    ->body('Chỉ có thể embedding documents có status "published".')
                                    ->warning()
                                    ->send();
                                return;
                            }

                            // Import services
                            $zilliz = new \App\Services\ZillizVectorService();
                            $openAI = new \App\Services\OpenAIService();
                            $documentVectorService = new \App\Services\VectorSync\DocumentVectorService($zilliz, $openAI);

                            // Reset embedding status
                            $record->resetEmbeddingStatus();

                            // Tạo output mock
                            $output = new class {
                                public function info($message) {}
                                public function writeln($message) {}
                                public function newLine() {}
                                public function createProgressBar($max) {
                                    return new class {
                                        public function advance() {}
                                        public function finish() {}
                                    };
                                }
                            };

                            // Chạy embedding cho document này
                            $documentVectorService->syncDocuments(
                                1, // batch size = 1
                                false, // không phải dry run
                                null, // from date
                                null, // to date
                                $output,
                                false // không save backup
                            );

                            // Kiểm tra kết quả
                            $record->refresh();
                            if ($record->embedding_status === 'completed') {
                                Notification::make()
                                    ->title('🎉 Embedding thành công!')
                                    ->body("Document đã được embedding với {$record->chunk_count} chunks.")
                                    ->success()
                                    ->send();
                            } else {
                                Notification::make()
                                    ->title('❌ Embedding thất bại')
                                    ->body($record->embedding_error ?: 'Lỗi không xác định.')
                                    ->danger()
                                    ->send();
                            }

                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('❌ Lỗi khi embedding')
                                ->body('Chi tiết: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(fn (Document $record): bool => $record->status === 'published'),

                Tables\Actions\Action::make('re_embed')
                    ->label('🔄 Reset & Re-embed')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->modalHeading('Reset Embedding Status')
                    ->modalDescription('Reset trạng thái embedding về "pending" cho document này.')
                    ->action(function (Document $record) {
                        try {
                            $record->resetEmbeddingStatus();

                            Notification::make()
                                ->title('Đã reset trạng thái embedding')
                                ->body('Document đã được reset về trạng thái pending.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Lỗi')
                                ->body('Không thể reset embedding: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(fn (Document $record): bool => $record->can_re_embed),

                Tables\Actions\DeleteAction::make()
                    ->label('Xóa'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('bulk_embed_to_zilliz')
                        ->label('🚀 Embedding to Zilliz ngay')
                        ->icon('heroicon-o-cloud-arrow-up')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('Embedding Documents to Zilliz')
                        ->modalDescription('Tạo embedding và upload trực tiếp lên Zilliz Cloud cho tất cả documents đã chọn. Quá trình này sẽ chạy ngay lập tức.')
                        ->modalSubmitActionLabel('Bắt đầu Embedding')
                        ->form([
                            Forms\Components\Select::make('batch_size')
                                ->label('Batch Size')
                                ->options([
                                    '5' => '5 documents/batch',
                                    '10' => '10 documents/batch',
                                    '20' => '20 documents/batch',
                                ])
                                ->default('10')
                                ->required(),

                            Forms\Components\Toggle::make('save_backup')
                                ->label('Lưu backup text lên S3')
                                ->default(true),
                        ])
                        ->action(function ($records, array $data) {
                            // Import các service cần thiết
                            $zilliz = new \App\Services\ZillizVectorService();
                            $openAI = new \App\Services\OpenAIService();
                            $documentVectorService = new \App\Services\VectorSync\DocumentVectorService($zilliz, $openAI);

                            $batchSize = (int) $data['batch_size'];
                            $saveBackup = $data['save_backup'];

                            // Lọc chỉ những documents có thể embedding
                            $validRecords = $records->filter(function ($record) {
                                return $record->status === 'published';
                            });

                            if ($validRecords->isEmpty()) {
                                Notification::make()
                                    ->title('Không có documents hợp lệ')
                                    ->body('Chỉ có thể embedding documents có status "published".')
                                    ->warning()
                                    ->send();
                                return;
                            }

                            // Reset embedding status cho tất cả documents được chọn
                            foreach ($validRecords as $record) {
                                $record->resetEmbeddingStatus();
                            }

                            try {
                                // Tạo output mock để capture thông tin
                                $output = new class {
                                    public $messages = [];
                                    public function info($message) { $this->messages[] = "INFO: $message"; }
                                    public function writeln($message) { $this->messages[] = $message; }
                                    public function newLine() { $this->messages[] = ""; }
                                    public function createProgressBar($max) {
                                        return new class {
                                            public function advance() {}
                                            public function finish() {}
                                        };
                                    }
                                };

                                // Chạy sync documents
                                $allTexts = $documentVectorService->syncDocuments(
                                    $batchSize,
                                    false, // không phải dry run
                                    null, // from date
                                    null, // to date
                                    $output,
                                    $saveBackup
                                );

                                // Lưu backup nếu được yêu cầu
                                $backupUrl = null;
                                if ($saveBackup && !empty($allTexts)) {
                                    $timestamp = now()->format('Y-m-d_H-i-s');
                                    $filename = "documents-texts/bulk_embed_{$timestamp}.json";

                                    $completeData = [
                                        'sync_timestamp' => $timestamp,
                                        'total_documents' => count($allTexts),
                                        'documents' => $allTexts,
                                        'metadata' => [
                                            'generated_by' => 'DocumentResource_BulkAction',
                                            'purpose' => 'Bulk embedding from admin interface',
                                            'batch_size' => $batchSize,
                                            'user_id' => Auth::id(),
                                        ]
                                    ];

                                    \Illuminate\Support\Facades\Storage::disk('s3')->put(
                                        $filename,
                                        json_encode($completeData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                                    );

                                    $backupUrl = \Illuminate\Support\Facades\Storage::disk('s3')->url($filename);
                                }

                                // Đếm kết quả
                                $successCount = $validRecords->filter(function ($record) {
                                    return $record->fresh()->embedding_status === 'completed';
                                })->count();

                                $failedCount = $validRecords->count() - $successCount;

                                // Thông báo kết quả
                                $message = "Hoàn thành embedding: {$successCount} thành công, {$failedCount} thất bại";
                                if ($backupUrl) {
                                    $message .= "\nBackup đã lưu tại S3";
                                }

                                Notification::make()
                                    ->title('🎉 Embedding hoàn thành!')
                                    ->body($message)
                                    ->success()
                                    ->duration(10000)
                                    ->send();

                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('❌ Lỗi khi embedding')
                                    ->body('Chi tiết: ' . $e->getMessage())
                                    ->danger()
                                    ->duration(10000)
                                    ->send();
                            }
                        }),

                    Tables\Actions\BulkAction::make('bulk_reset_embedding')
                        ->label('🔄 Reset Embedding Status')
                        ->icon('heroicon-o-arrow-path')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalHeading('Reset Embedding Status')
                        ->modalDescription('Reset trạng thái embedding về "pending" cho tất cả documents đã chọn.')
                        ->action(function ($records) {
                            $count = 0;
                            foreach ($records as $record) {
                                $record->resetEmbeddingStatus();
                                $count++;
                            }

                            Notification::make()
                                ->title('Đã reset embedding status')
                                ->body("{$count} documents đã được reset về trạng thái pending.")
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Xóa đã chọn'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDocuments::route('/'),
            'create' => Pages\CreateDocument::route('/create'),
            'edit' => Pages\EditDocument::route('/{record}/edit'),
        ];
    }
}
