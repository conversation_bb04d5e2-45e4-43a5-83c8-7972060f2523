<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\ExpenseTypeResource\Pages;
use App\Filament\App\Resources\ExpenseTypeResource\RelationManagers;
use App\Models\ExpenseType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ExpenseTypeResource extends Resource
{
    protected static ?string $model = ExpenseType::class;
    protected static ?string $navigationIcon = 'heroicon-o-tag';
    protected static ?string $navigationGroup = 'Finance';
    protected static ?int $navigationSort = 1;


    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['super_admin', 'Super Accountant', 'Developer', 'Accountant']);
    }



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->description('Enter the basic details of the expense type')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->label('Expense Type Name')
                            ->placeholder('Enter expense type name'),

                        Forms\Components\TextInput::make('code')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->placeholder('e.g., TIKTOK_ADS_TOPUP')
                            ->helperText('Unique code for this expense type'),

                        Forms\Components\Select::make('category')
                            ->required()
                            ->options([
                                'advertising' => 'Advertising & Marketing',
                                'equipment' => 'Equipment & Devices',
                                'software' => 'Software & Tools',
                                'office' => 'Office Supplies',
                                'other' => 'Other Expenses'
                            ])
                            ->native(false),

                        Forms\Components\Textarea::make('description')
                            ->required()
                            ->rows(3)
                            ->placeholder('Detailed description of this expense type')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Limits & Approval Settings')
                    ->description('Configure spending limits and approval requirements')
                    ->schema([
                        Forms\Components\TextInput::make('limit_amount')
                            ->numeric()
                            ->prefix('$')
                            ->placeholder('0.00')
                            ->nullable()
                            ->helperText('Leave empty for no limit')
                            ->step(0.01),

                        Forms\Components\Select::make('limit_period')
                            ->options([
                                'daily' => 'Daily Limit',
                                'weekly' => 'Weekly Limit',
                                'monthly' => 'Monthly Limit',
                                'yearly' => 'Yearly Limit'
                            ])
                            ->native(false)
                            ->nullable()
                            ->helperText('Select the period for the limit'),

                        Forms\Components\Grid::make()
                            ->schema([
                                Forms\Components\Toggle::make('requires_approval')
                                    ->label('Requires Approval')
                                    ->helperText('Toggle if this expense type needs approval')
                                    ->default(true),

                                Forms\Components\Toggle::make('active')
                                    ->label('Active Status')
                                    ->helperText('Toggle to enable/disable this expense type')
                                    ->default(true),
                            ])
                            ->columns(2)
                            ->columnSpan(2),
                    ])
                    ->columns(2),

                    // Forms\Components\Section::make('Required Fields')
                    // ->schema([
                    //     Forms\Components\Repeater::make('field_config')
                    //         ->schema([
                    //             Forms\Components\TextInput::make('field_name')
                    //                 ->required()
                    //                 ->label('Field Name')
                    //                 ->helperText('Enter the field name (e.g., tiktok_ads_id, paypal_link)'),
    
                    //             Forms\Components\TextInput::make('field_description')
                    //                 ->required()
                    //                 ->label('Field Description')
                    //                 ->helperText('Enter the description for this field'),
                    //         ])
                    //         ->columns(2)
                    //         ->defaultItems(1)
                    //         ->createItemButtonLabel('Add Field')
                    //         ->live()
                    //         ->afterStateUpdated(function ($state, callable $set) {
                    //             $required_fields = [];
                    //             $field_descriptions = [];
                                
                    //             foreach ($state ?? [] as $field) {
                    //                 $required_fields[] = $field['field_name'];
                    //                 $field_descriptions[$field['field_name']] = $field['field_description'];
                    //             }
                                
                    //             $set('required_fields', $required_fields);
                    //             $set('field_descriptions', $field_descriptions);
                    //         })
                    //         ->afterStateHydrated(function ($component, $state, $record) {
                    //             if (!$record) return;
                                
                    //             $field_config = [];
                    //             foreach ($record->required_fields ?? [] as $field) {
                    //                 $field_config[] = [
                    //                     'field_name' => $field,
                    //                     'field_description' => $record->field_descriptions[$field] ?? '',
                    //                 ];
                    //             }
                                
                    //             $component->state($field_config);
                    //         }),
    
                    //     // Hidden fields to store the processed data
                    //     Forms\Components\Hidden::make('required_fields'),
                    //     Forms\Components\Hidden::make('field_descriptions'),
                    // ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('category')
                    ->colors([
                        'primary' => 'advertising',
                        'success' => 'equipment',
                        'warning' => 'software',
                        'info' => 'office',
                        'danger' => 'other',
                    ]),

                Tables\Columns\TextColumn::make('limit_amount')
                    ->money('USD')
                    ->sortable(),

                Tables\Columns\TextColumn::make('limit_period')
                    ->badge()
                    ->colors([
                        'success' => 'daily',
                        'warning' => 'weekly',
                        'primary' => 'monthly',
                        'danger' => 'yearly',
                    ]),

                Tables\Columns\IconColumn::make('requires_approval')
                    ->boolean(),

                Tables\Columns\IconColumn::make('active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'advertising' => 'Advertising',
                        'equipment' => 'Equipment',
                        'software' => 'Software',
                        'office' => 'Office',
                        'other' => 'Other',
                    ]),
                Tables\Filters\TernaryFilter::make('active'),
                Tables\Filters\TernaryFilter::make('requires_approval'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExpenseTypes::route('/'),
            'create' => Pages\CreateExpenseType::route('/create'),
            'edit' => Pages\EditExpenseType::route('/{record}/edit'),
        ];
    }
}
