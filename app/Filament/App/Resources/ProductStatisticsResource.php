<?php

namespace App\Filament\App\Resources;

use App\Exports\ProductStatisticsExport;
use App\Filament\App\Resources\ProductStatisticsResource\Pages;
use App\Filament\App\Resources\ProductStatisticsResource\Widgets;
use App\Models\Product;
use App\Models\User;
use App\Models\Store;
use App\Models\OrderItem;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\IconColumn;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;
use App\Traits\HasUserFilter;
use Illuminate\Support\Facades\DB;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Tables\Actions\Action as TableAction;
use Livewire\Attributes\Url;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Request;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Actions\ExportAction;
use Filament\Tables\Actions\ExportBulkAction;
use App\Exports\ProductStatisticsCsvExport;
use Maatwebsite\Excel\Facades\Excel;


class ProductStatisticsResource extends Resource
{
    use HasUserFilter;

    protected static ?string $model = Product::class;
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationGroup = 'Analytics';

    protected static ?string $modelLabel = 'Top 20 sản phẩm được đặt hàng nhiều nhất';
    protected static ?string $navigationLabel = 'Top sản phẩm đặt hàng nhiều  ';
    protected static ?string $slug = 'product-statistics';
    protected static ?int $navigationSort = 3;

    // Không sử dụng HasPageShield để tránh xung đột
    // use HasPageShield;

    /**
     * Custom permissions - không dùng Shield auto-generation
     */
    public static function canAccess(): bool
    {
        return auth()->user()->hasAnyRole([
            'super_admin',
            'User Manager',
            'Media Manager',
            'Analytic',
        ]);
    }

    public static function canViewAny(): bool
    {
        return static::canAccess();
    }

    public static function canView($record): bool
    {
        return static::canAccess();
    }

    public static function canCreate(): bool
    {
        return false; // Statistics không cần create
    }

    public static function canEdit($record): bool
    {
        return false; // Statistics không cần edit
    }

    public static function canDelete($record): bool
    {
        return false; // Statistics không cần delete
    }

    // Thêm biến kiểm soát lọc mặc định
    public bool $hasAppliedDefaultFilter = false;



    /**
     * Áp dụng bộ lọc theo khoảng thời gian
     */
    protected static function applyDateFilter(Builder $query, ?string $dateRange = null): Builder
    {
        // Tính toán thời gian mặc định (7 ngày gần đây)
        $sevenDaysAgo = Carbon::now()->subDays(7)->startOfDay();
        $today = Carbon::now()->endOfDay();

        $startDate = $sevenDaysAgo;
        $endDate = $today;

        // Áp dụng khoảng thời gian tùy chỉnh nếu có
        if (!empty($dateRange)) {
            $dates = explode(' - ', $dateRange);

            if (count($dates) === 2) {
                try {
                    $startDate = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $endDate = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                } catch (\Exception $e) {
                    // Nếu có lỗi, sử dụng giá trị mặc định
                }
            }
        }

        // Lưu thời gian để sử dụng trong truy vấn chính
        static::$dateFilterStart = $startDate;
        static::$dateFilterEnd = $endDate;

        return $query->whereBetween('orders.created_at', [$startDate, $endDate]);
    }

    // Biến static để lưu trữ khoảng thời gian của bộ lọc
    protected static $dateFilterStart = null;
    protected static $dateFilterEnd = null;

    public static function table(Table $table): Table
    {
        return $table
            ->query(function (Builder $query): Builder {
                // Loại bỏ global scope của Product để áp dụng logic phân quyền riêng
                $query = OrderItem::select(
                    'products.id',
                    'products.name',
                    'products.sku',
                    'products.price',
                    'products.type',
                    'products.image',
                    'products.seller_id',
                    'products.store_id',
                    'products.tiktok_product_id',
                    'users.name as seller_name',
                    'users.telegram_id',
                    'stores.name as store_name',
                    DB::raw('SUM(order_items.quantity) as total_ordered'),
                    DB::raw('MAX(orders.created_at) as latest_order_date'), // Đơn hàng mới nhất
                    DB::raw('MIN(orders.created_at) as first_order_date'), // Đơn hàng đầu tiên
                )
                ->join('products', 'order_items.product_id', '=', 'products.id')
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                // Loại bỏ global scope của Product model
                ->withoutGlobalScopes()
                ->leftJoin('users', 'products.seller_id', '=', 'users.id')
                ->leftJoin('stores', 'products.store_id', '=', 'stores.id')
                ->whereNotIn('orders.status', ['canceled', 'returned']) // Loại bỏ đơn hàng đã hủy hoặc trả lại
                ->groupBy(
                    'products.id',
                    'products.name',
                    'products.sku',
                    'products.price',
                    'products.type',
                    'products.image',
                    'products.seller_id',
                    'products.store_id',
                    'products.tiktok_product_id',
                    'users.name',
                    'users.telegram_id',
                    'stores.name'
                )
                ->orderBy('total_ordered', 'desc');

                return $query;
            })
            ->columns([
                Tables\Columns\ViewColumn::make('index')
                    ->label('Thứ hạng')
                    ->view('filament.app.resources.product-statistics-resource.columns.ranking'),

                Tables\Columns\ViewColumn::make('image')
                    ->label('Hình ảnh')
                    ->view('filament.app.resources.product-statistics-resource.columns.product-image'),

                Tables\Columns\ViewColumn::make('name')
                    ->label('Thông tin sản phẩm')
                    ->searchable()
                    ->view('filament.app.resources.product-statistics-resource.columns.product-info')
                    ,

                Tables\Columns\ViewColumn::make('order_dates')
                    ->label('Thông tin đơn hàng')
                    ->view('filament.app.resources.product-statistics-resource.columns.order-dates')
                    ->extraAttributes([
                        'class' => 'px-2',
                    ]),

                Tables\Columns\ViewColumn::make('seller_store_info')
                    ->label('Thông tin người bán & cửa hàng')
                    ->searchable(['seller_name', 'store_name'])
                    ->view('filament.app.resources.product-statistics-resource.columns.seller-store-info'),

                Tables\Columns\ViewColumn::make('tiktok_product_id')
                    ->label('TikTok ID')
                    ->searchable()
                    ->view('filament.app.resources.product-statistics-resource.columns.tiktok-product-id')
                    ->extraAttributes([
                        'class' => 'w-[120px] max-w-[120px]',
                    ]),

                Tables\Columns\ViewColumn::make('id')
                    ->label('File thiết kế')
                    ->view('filament.app.resources.product-statistics-resource.columns.design-files'),
            ])
            ->headerActions([
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new ProductStatisticsCsvExport($filters);
                        return Excel::download($export, $export->getFileName(), \Maatwebsite\Excel\Excel::CSV);
                    }),
            ])
            ->defaultSort('total_ordered', 'desc')
            ->filters([
                // Bộ lọc ngày
                DateRangeFilter::make('date')
                    ->label('Lọc theo thời gian')
                    ->withIndicator()
                    ->ranges([
                        'Hôm nay' => [now(), now()],
                        '2 ngày qua' => [now()->subDay(1), now()],
                        '7 ngày qua' => [now()->subDays(6), now()],
                        '30 ngày qua'=> [now()->subDays(29), now()],
                        '2 tháng qua' => [now()->subDays(59), now()],
                        'Tất cả' => [now()->subYears(10)->startOfYear(), now()],
                    ])
                    ->indicateUsing(function (array $data): string {
                        if (!$data['date'] ?? null) {
                            return '';
                        }
                        return 'Lọc từ ngày ' . $data['date'];
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return static::applyDateFilter($query, $data['date'] ?? null);
                    }),

                SelectFilter::make('type')
                    ->label('Loại sản phẩm')
                    ->options(function () {
                        return Product::distinct('type')->pluck('type', 'type')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['values'])) {
                            return $query;
                        }

                        return $query->whereIn('products.type', $data['values']);
                    })
                    ->multiple(),

                SelectFilter::make('seller_id')
                    ->label('Người bán')
                    ->options(function () {
                        return User::role('Seller')->pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }

                        return $query->where('products.seller_id', $data['value']);
                    })
                    ->searchable()
                    ->preload(),
            ])
            ->filtersFormColumns(3)
            ->filtersTriggerAction(function ($action) {
                return $action
                    ->button()
                    ->label('Bộ lọc');
            })
            ->bulkActions([
                ExportBulkAction::make()
                    ->label('Xuất Excel')
                    ->exporter(ProductStatisticsExport::class)
                    ->fileName(fn () => 'product-statistics-selected-' . now()->format('Y-m-d-H-i-s'))
                    ->formats([
                        ExportFormat::Xlsx,
                        ExportFormat::Csv,
                    ]),
            ])
            ->paginated([10, 25, 50, 100])
            ->defaultPaginationPageOption(25)
            ->persistFiltersInSession()
            ->deferLoading();
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\TopOrderedProductsChart::class,
        ];
    }



    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductStatistics::route('/'),
        ];
    }
}
