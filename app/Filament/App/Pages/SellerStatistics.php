<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;

use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Request;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Order;
use App\Models\Team;
use App\Models\OrderItem;
use App\Models\SupplierOrder;
use App\Models\SellerFundRequest;
use App\Models\Store;
use App\Models\DesignJob;

use App\Models\TikTokPayment;
use App\Models\PayoutTransaction;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Collection;
use Filament\Notifications\Notification;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use App\Models\SellerFinance;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\Url;
use Illuminate\Support\Facades\DB;
use App\Models\Invoice;
use Illuminate\Support\Facades\Auth;

// Import SellerService for optimized calculations
use App\Services\SellerService;
use App\Services\OptimizedSellerStatisticsService;


class SellerStatistics extends Page implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Seller Statistics';
    protected static ?string $navigationGroup = 'Seller Management';
    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.app.pages.seller-statistics';
    protected string $currency = '$';

    // Cache configuration constants - Redis optimized
    const CACHE_TTL_SUMMARY = 3600; // 1 hour for summary statistics
    const CACHE_TTL_SELLER_DATA = 7200; // 2 hours for individual seller data
    const CACHE_TTL_RANKINGS = 3600; // 1 hour for rankings
    const CACHE_TTL_AGGREGATED = 14400; // 4 hours for aggregated data
    const CACHE_PREFIX = 'seller_stats_redis_';
    const CACHE_TAG_SUMMARY = 'seller_summary';
    const CACHE_TAG_SELLER_DATA = 'seller_data';
    const CACHE_TAG_RANKINGS = 'seller_rankings';

    #[Url(except: '')]
    public $dateRange = null;

    // Add query string parameters
    protected $queryString = [
        'tableFilters',
        'tableSortColumn',
        'tableSortDirection',
        'tableSearchQuery',
        'tableColumnSearches',
        'team' => ['except' => ''],
        'role' => ['except' => ''],
        'dateRange' => ['except' => ''],
    ];

    // Cached data properties
    protected ?array $cachedSummaryStats = null;
    protected ?array $cachedSellerRankings = null;
    protected ?OptimizedSellerStatisticsService $optimizedService = null;
    protected ?array $cachedOptimizedData = null;

    protected function getUserInstance()
    {
        return Auth::user();
    }

    public static function canAccess(): bool
    {
        if (Auth::check()) {
            return Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Developer', 'Accountant']);
        }
        return false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                \Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker::make('dateRange')
                    ->label('Khoảng thời gian')
                    ->timezone(config('app.timezone'))
                    ->displayFormat('D/M/Y')
                    ->format('d/m/Y')
                    ->separator(' - ')
                    ->ranges(function() {
                        $thisMonth = Carbon::now();
                        $lastMonth = Carbon::now()->subMonth();
                        $twoMonthsAgo = Carbon::now()->subMonths(2);
                        $threeMonthsAgo = Carbon::now()->subMonths(3);

                        return [
                            'Tháng này' => [
                                $thisMonth->copy()->startOfMonth(),
                                $thisMonth->copy()->endOfMonth()
                            ],
                            'Tháng trước' => [
                                $lastMonth->copy()->startOfMonth(),
                                $lastMonth->copy()->endOfMonth(),
                            ],
                            '2 tháng trước' => [
                                $twoMonthsAgo->copy()->startOfMonth(),
                                $twoMonthsAgo->copy()->endOfMonth(),
                            ],
                            '3 tháng trước' => [
                                $threeMonthsAgo->copy()->startOfMonth(),
                                $threeMonthsAgo->copy()->endOfMonth(),
                            ],
                            '3 tháng gần nhất' => [
                                $twoMonthsAgo->copy()->startOfMonth(),
                                $thisMonth->copy()->endOfMonth(),
                            ],
                            'Năm nay' => [
                                Carbon::now()->startOfYear(),
                                Carbon::now()->endOfYear(),
                            ],
                        ];
                    })
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->dateRange = $state;
                        $this->clearCache();
                        $this->warmupCache();
                        $this->dispatch('$refresh');
                    })
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns($this->getTableColumns())
            ->filters($this->getTableFilters())
            ->defaultSort('total_orders', 'desc') // Sort by total orders by default
            ->recordUrl(
                fn (User $record): string => '/app/seller-invoice/' . $record->id
            );
    }

    public function mount()
    {
        $sortColumn = request()->query('tableSortColumn');
        $sortDirection = request()->query('tableSortDirection');

        // Nếu chưa có sort column, tự động sort theo orders DESC
        if (empty($sortColumn)) {
            return redirect()->to(request()->fullUrlWithQuery([
                'tableSortColumn' => 'total_orders',
                'tableSortDirection' => 'desc',
            ]));
        }

        // Thiết lập giá trị mặc định cho dateRange là tháng trước
        if (empty($this->dateRange)) {
            $lastMonth = Carbon::now()->subMonth();
            $startDate = $lastMonth->copy()->startOfMonth()->format('d/m/Y');
            $endDate = $lastMonth->copy()->endOfMonth()->format('d/m/Y');
            $this->dateRange = $startDate . ' - ' . $endDate;
        }

        // Pre-warm cache with optimized methods
        $this->warmupCache();
    }

    /**
     * Warm up cache with essential data
     */
    protected function warmupCache(): void
    {
        // Cache summary statistics
        $this->getCachedSummaryStatistics();

        // Cache seller rankings
        $this->getCachedSellerRankings();
    }

    /**
     * Generate cache key based on current filters
     */
    protected function getCacheKey(string $suffix = ''): string
    {
        $filterHash = md5($this->dateRange ?? 'default');
        return self::CACHE_PREFIX . $suffix . '_' . $filterHash;
    }

    /**
     * Clear all related cache - Redis optimized
     */
    public function clearCache(): void
    {
        // Clear cache using tags for efficiency
        Cache::tags([self::CACHE_TAG_SUMMARY, self::CACHE_TAG_SELLER_DATA, self::CACHE_TAG_RANKINGS])->flush();

        // Clear optimized service cache
        if ($this->optimizedService) {
            $this->optimizedService->clearCache();
        }

        // Reset cached properties
        $this->cachedSummaryStats = null;
        $this->cachedSellerRankings = null;
        $this->cachedOptimizedData = null;
        $this->optimizedService = null;
    }



    /**
     * Get or create optimized service instance
     */
    protected function getOptimizedService(): OptimizedSellerStatisticsService
    {
        if ($this->optimizedService === null) {
            $dateRange = $this->getDateRangeFromFilters();
            $sellers = $this->getTableQuery()->get(['id', 'name', 'email']);

            $this->optimizedService = new OptimizedSellerStatisticsService(
                $sellers,
                $dateRange['from'],
                $dateRange['until']
            );
        }

        return $this->optimizedService;
    }

    /**
     * Get cached optimized data for all sellers
     */
    protected function getCachedOptimizedData(): array
    {
        if ($this->cachedOptimizedData === null) {
            $this->cachedOptimizedData = $this->getOptimizedService()->getAllSellerData();
        }

        return $this->cachedOptimizedData;
    }

    public function getRefreshInterval()
    {
        // Tự động làm mới dữ liệu sau mỗi 5 giây
        return null; // Tắt tự động refresh
    }

    public function getPollingInterval(): ?string
    {
        // Thiết lập khoảng thời gian tự động cập nhật dữ liệu
        return null; // Tắt tự động refresh
    }

    public function getTablePollingInterval(): ?string
    {
        // Thiết lập khoảng thời gian tự động làm mới toàn bộ bảng
        return null; // Tắt tự động refresh
    }

    protected function getRefreshMethod(): ?string
    {
        return null; // Tắt refresh method
    }

    public function refresh(): void
    {
        // Clear cache and refresh data
        $this->clearCache();
        $this->warmupCache();
    }

    /**
     * Get cached summary statistics with optimized queries
     */
    protected function getCachedSummaryStatistics(): array
    {
        if ($this->cachedSummaryStats !== null) {
            return $this->cachedSummaryStats;
        }

        $cacheKey = $this->getCacheKey('summary_stats');

        try {
            $this->cachedSummaryStats = Cache::tags([self::CACHE_TAG_SUMMARY])->remember($cacheKey, self::CACHE_TTL_SUMMARY, function () {
                return $this->calculateOptimizedSummaryStatistics();
            });
        } catch (\Exception $e) {
            // Log error and return default values
            \Log::error('Error calculating summary statistics: ' . $e->getMessage());

            $this->cachedSummaryStats = [
                'totalSellers' => 0,
                'totalTeams' => 0,
                'totalOrders' => 0,
                'totalCompletedOrders' => 0,
                'totalCompletedRevenue' => 0,
                'totalRevenue' => 0,
                'totalSupplierOrders' => 0,
                'totalCompletedSupplierOrders' => 0,
                'totalSupplierCost' => 0,
                'totalDesignFees' => 0,
                'totalFundAdvances' => 0,
                'totalCompletedOrdersRevenue' => 0,
                'totalOrdersRevenue' => 0,
                'completedSupplierOrders' => 0,
                'totalSupplierOrdersCost' => 0,
                'totalSupplierOrdersTotalCost' => 0,
                'totalTikTokPayments' => 0,
                'totalTikTokCount' => 0,
                'totalBankTransactions' => 0,
                'totalBankCount' => 0,
                'totalProfit' => 0,
            ];
        }

        return $this->cachedSummaryStats;
    }

    /**
     * Get cached seller rankings
     */
    protected function getCachedSellerRankings(): array
    {
        if ($this->cachedSellerRankings !== null) {
            return $this->cachedSellerRankings;
        }

        $cacheKey = $this->getCacheKey('seller_rankings');

        $this->cachedSellerRankings = Cache::tags([self::CACHE_TAG_RANKINGS])->remember($cacheKey, self::CACHE_TTL_RANKINGS, function () {
            return $this->calculateSellerRankings();
        });

        return $this->cachedSellerRankings;
    }

    /**
     * Calculate seller rankings using optimized query
     */
    protected function calculateSellerRankings(): array
    {
        $sellers = $this->getTableQuery()->get(['id', 'completed_orders']);

        $rankings = [];
        $rank = 1;

        // Sort by completed orders descending
        $sortedSellers = $sellers->sortByDesc('completed_orders');

        foreach ($sortedSellers as $seller) {
            $rankings[$seller->id] = $rank++;
        }

        return $rankings;
    }



    protected function getTableQuery(): Builder
    {
        $user = $this->getUserInstance();

        // Get date range using simplified method
        $dateRange = $this->getDateRangeFromFilters();

        // Base query with pre-calculated aggregations
        $fromDate = $dateRange['from']->format('Y-m-d H:i:s');
        $untilDate = $dateRange['until']->format('Y-m-d H:i:s');

        return User::query()
            ->withoutGlobalScopes()
            ->select([
                'users.*',
                // Pre-calculate orders data
                DB::raw("(SELECT COUNT(*) FROM orders WHERE orders.seller_id = users.id AND orders.created_at BETWEEN '{$fromDate}' AND '{$untilDate}') as total_orders"),
                DB::raw("(SELECT COUNT(*) FROM orders WHERE orders.seller_id = users.id AND orders.status = 'Completed' AND orders.created_at BETWEEN '{$fromDate}' AND '{$untilDate}') as completed_orders"),
                // Pre-calculate revenue data - Updated to match SellerInvoice logic
                DB::raw("(SELECT COALESCE(SUM(order_items.total), 0) FROM orders JOIN order_items ON orders.id = order_items.order_id WHERE orders.seller_id = users.id AND orders.status IN ('Completed', 'Processing') AND orders.created_at BETWEEN '{$fromDate}' AND '{$untilDate}') as completed_revenue"),
                DB::raw("(SELECT COALESCE(SUM(order_items.total), 0) FROM orders JOIN order_items ON orders.id = order_items.order_id WHERE orders.seller_id = users.id AND orders.created_at BETWEEN '{$fromDate}' AND '{$untilDate}') as total_revenue"),
                // Pre-calculate supplier orders data - Updated to match SellerInvoice logic (all except Cancelled)
                DB::raw("(SELECT COUNT(*) FROM supplier_orders WHERE supplier_orders.seller_id = users.id AND supplier_orders.created_at BETWEEN '{$fromDate}' AND '{$untilDate}') as total_supplier_orders"),
                DB::raw("(SELECT COUNT(*) FROM supplier_orders WHERE supplier_orders.seller_id = users.id AND supplier_orders.status != 'Cancelled' AND supplier_orders.created_at BETWEEN '{$fromDate}' AND '{$untilDate}') as completed_supplier_orders"),
                DB::raw("(SELECT COALESCE(SUM(base_cost), 0) FROM supplier_orders WHERE supplier_orders.seller_id = users.id AND supplier_orders.status != 'Cancelled' AND supplier_orders.created_at BETWEEN '{$fromDate}' AND '{$untilDate}') as total_supplier_cost"),
                // Pre-calculate design fees
                DB::raw("(SELECT COALESCE(SUM(price), 0) FROM design_jobs WHERE design_jobs.created_by = users.id AND design_jobs.created_at BETWEEN '{$fromDate}' AND '{$untilDate}') as design_fees"),
                // Pre-calculate fund advances
                DB::raw("(SELECT COALESCE(SUM(amount), 0) FROM seller_fund_requests WHERE seller_fund_requests.seller_id = users.id AND seller_fund_requests.status = 'approved' AND seller_fund_requests.created_at BETWEEN '{$fromDate}' AND '{$untilDate}') as fund_advances")
            ])
            ->with([
                'teams:id,name',
                'stores:id,owner_id,name,bank_account'
            ])
            ->whereHas('roles', function ($query) {
                $query->where('name', 'Seller');
            });
    }

    protected function getDateRangeFromFilters($filters = null): array
    {
        // Sử dụng dateRange property thay vì filters phức tạp
        $dateRangeStr = $this->dateRange;

        if (empty($dateRangeStr)) {
            // Mặc định là tháng trước
            $from = Carbon::now()->subMonth()->startOfMonth();
            $until = Carbon::now()->subMonth()->endOfMonth();
        } else {
            $dates = explode(' - ', $dateRangeStr);
            if (count($dates) == 2) {
                $from = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                $until = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
            } else {
                // Fallback nếu format không đúng
                $from = Carbon::now()->subMonth()->startOfMonth();
                $until = Carbon::now()->subMonth()->endOfMonth();
            }
        }

        return ['from' => $from, 'until' => $until];
    }

    public function getFilteredTableQuery(): Builder
    {
        return $this->getTableQuery();
    }

    protected function getTableColumns(): array
    {
        return  [
            $this->getUserColumn(),
            $this->getReportStatusColumn(),
            $this->getOrdersColumn(),
            $this->getRevenueColumn(),
            $this->getSupplierOrdersColumn(),
            $this->getFeesAndAdvancesColumn(),
            $this->getProfitColumn(),
            $this->getReceivedPaymentsColumn(),
        ];
    }


    /**
     * Calculate user rank using cached rankings (optimized)
     */
    protected function calculateUserRank(User $user): int
    {
        $rankings = $this->getCachedSellerRankings();
        return $rankings[$user->id] ?? count($rankings) + 1;
    }

    // Cột User (thay thế cho cột Name) - Optimized
    protected function getUserColumn(): TextColumn
    {
        return TextColumn::make('name')
            ->label(function () {
                // Use cached summary statistics
                $summaryStats = $this->getCachedSummaryStatistics();

                $totalFilteredSellers = $summaryStats['totalSellers'] ?? 0;
                $totalDistinctTeams = $summaryStats['totalTeams'] ?? 0;

                $html = '';
                $html .= $this->formatSummaryItem('Active Sellers', number_format($totalFilteredSellers));
                $html .= $this->formatSummaryItem('Distinct Teams', number_format($totalDistinctTeams));

                return $this->getColumnHeaderLabel('User', 'Seller Details', $html);
            })
            ->formatStateUsing(function (User $record) {
                $rank = $this->calculateUserRank($record);
                $teamsList = $record->teams->pluck('name')->toArray();

                // Get optimized data
                $optimizedData = $this->getCachedOptimizedData();
                $sellerData = $optimizedData[$record->id] ?? null;

                // Get date range for SellerFinance lookup
                $dateRange = $this->getDateRangeFromFilters();
                $periodNetProfit = $this->getSellerFinanceNetProfit($record, $dateRange['from']);

                return view('filament.tables.columns.seller-statistics.user', [
                    'name' => $record->name,
                    'email' => $record->email,
                    'last_activity' => $record->last_activity,
                    'avatar' => $record->getFilamentAvatarUrl(),
                    'rank' => $rank,
                    'teamsList' => $teamsList,
                    'periodNetProfit' => $periodNetProfit,
                    'periodCompletedOrders' => $sellerData['orders']['completed_orders'] ?? 0,
                    'currency' => $this->currency,
                ]);
            })
            ->searchable()
            ->wrap()
            ->width('200px');
    }

    /**
     * Helper method to get SellerFinance net profit
     */
    protected function getSellerFinanceNetProfit(User $seller, Carbon $periodStart): ?float
    {
        $sellerFinance = SellerFinance::where('seller_id', $seller->id)
            ->whereYear('month', $periodStart->year)
            ->whereMonth('month', $periodStart->month)
            ->first();

        return $sellerFinance ? $sellerFinance->net_profit : null;
    }

    protected function getRevenueColumn(): TextColumn
    {
        return TextColumn::make('completed_revenue')
            ->label(function () {
                // Use cached summary statistics
                $summaryStats = $this->getCachedSummaryStatistics();

                $totalRevenueCompletedOrders = $summaryStats['totalCompletedRevenue'] ?? 0;
                $totalOrders = $summaryStats['totalOrders'] ?? 0;
                $totalRevenue = $summaryStats['totalRevenue'] ?? 0;

                // Tránh lỗi division by zero khi không có đơn hàng
                $averageRevenuePerOrder = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;

                $html = '';
                $html .= $this->formatSummaryItem('Revenue', $this->currency . number_format($totalRevenueCompletedOrders, 2));
                $html .= $this->formatSummaryItem('Orders', number_format($totalOrders));
                $html .= $this->formatSummaryItem('Avg', $this->currency . number_format($averageRevenuePerOrder, 2));

                return $this->getColumnHeaderLabel('Revenue', 'Doanh thu (Completed + Processing)', $html);
            })
            ->getStateUsing(function ($record) {
                // Use pre-calculated data from main query
                $totalRevenueCompleted = $record->completed_revenue ?? 0;
                $totalRevenue = $record->total_revenue ?? 0;
                $totalOrders = $record->total_orders ?? 0;
                $totalCostCompleted = $record->total_supplier_cost ?? 0;

                $profitPercentage = $totalCostCompleted > 0 ? ($totalRevenueCompleted / $totalCostCompleted * 100) : 0;

                return view('filament.tables.columns.seller-statistics.revenue', [
                    'totalRevenueCompleted' => $totalRevenueCompleted,
                    'totalRevenue' => $totalRevenue,
                    'totalOrders' => $totalOrders,
                    'currency' => $this->currency,
                    'profitPercentage' => $profitPercentage
                ]);
            })
            ->html();
    }


    // Cột Orders (Số đơn hàng) - Optimized
    protected function getOrdersColumn(): TextColumn
    {
        return TextColumn::make('total_orders')
            ->label(function () {
                // Use cached summary statistics
                $summaryStats = $this->getCachedSummaryStatistics();

                $totalCompletedOrders = $summaryStats['totalCompletedOrders'] ?? 0;
                $totalOrders = $summaryStats['totalOrders'] ?? 0;
                $totalRevenueFromCompleted = $summaryStats['totalCompletedRevenue'] ?? 0;

                $totalAverageOrderValue = ($totalCompletedOrders > 0) ? $totalRevenueFromCompleted / $totalCompletedOrders : 0;

                $html = '';
                $html .= $this->formatSummaryItem("Completed", number_format($totalCompletedOrders));
                $html .= $this->formatSummaryItem("Total", number_format($totalOrders));
                $html .= $this->formatSummaryItem("Avg Value (Comp.)", $this->currency . number_format($totalAverageOrderValue, 2));

                return $this->getColumnHeaderLabel('Orders', 'Đơn hàng', $html);
            })
            ->getStateUsing(function ($record) {
                // Get optimized data
                $optimizedData = $this->getCachedOptimizedData();
                $ordersData = $optimizedData[$record->id]['orders'] ?? [];

                $totalOrders = $ordersData['total_orders'] ?? 0;
                $completedOrders = $ordersData['completed_orders'] ?? 0;
                $pendingOrders = $ordersData['pending_orders'] ?? 0;
                $cancelledOrders = $ordersData['cancelled_orders'] ?? 0;
                $averageOrderValue = $ordersData['average_order_value'] ?? 0;

                return view('filament.tables.columns.seller-statistics.orders', [
                    'totalOrders' => $totalOrders,
                    'pendingOrders' => $pendingOrders,
                    'cancelledOrders' => $cancelledOrders,
                    'completedOrders' => $completedOrders,
                    'averageOrderValue' => $averageOrderValue,
                    'currency' => $this->currency
                ])->render();
            })
            ->sortable()
            ->html();
    }



    // New column for Fees & Advances - Optimized with SellerService
    protected function getFeesAndAdvancesColumn(): TextColumn
    {
        return TextColumn::make('fees_and_advances')
            ->label(function () {
                $summaryStats = $this->getCachedSummaryStatistics();

                $totalDesignFees = $summaryStats['totalDesignFees'] ?? 0;
                $totalFundAdvances = $summaryStats['totalFundAdvances'] ?? 0;

                $summaryItems = [
                    $this->formatSummaryItem("Design Fees", $this->currency . number_format($totalDesignFees, 2)),
                    $this->formatSummaryItem("Fund Advances", $this->currency . number_format($totalFundAdvances, 2)),
                ];

                return new HtmlString(view('filament.tables.columns.seller-statistics.header-label', [
                    'label' => 'Fees & Advances',
                    'summary' => $summaryItems
                ])->render());
            })
            ->html()
            ->getStateUsing(function ($record) {
                // Get optimized data
                $optimizedData = $this->getCachedOptimizedData();
                $designJobsData = $optimizedData[$record->id]['design_jobs'] ?? [];
                $fundRequestsData = $optimizedData[$record->id]['fund_requests'] ?? [];
                $ordersData = $optimizedData[$record->id]['orders'] ?? [];

                $designFeesTotal = $designJobsData['total_fees'] ?? 0;
                $designJobsCount = $designJobsData['count'] ?? 0;
                $fundAdvancesTotal = $fundRequestsData['total_amount'] ?? 0;
                $completedOrdersRevenue = $ordersData['completed_revenue'] ?? 0;

                $fundAdvancePercentage = $completedOrdersRevenue > 0 ? ($fundAdvancesTotal / $completedOrdersRevenue) * 100 : 0;

                return view('filament.tables.columns.seller-statistics.fees-advances', [
                    'designFeesTotal' => $designFeesTotal,
                    'designRushFees' => 0, // Could be calculated separately if needed
                    'designJobsCount' => $designJobsCount,
                    'fundAdvancesTotal' => $fundAdvancesTotal,
                    'fundAdvancePercentage' => $fundAdvancePercentage,
                    'currency' => $this->currency,
                ])->render();
            });
    }

    // New column for Received Payments - Optimized with SellerService
    protected function getReceivedPaymentsColumn(): TextColumn
    {
        return TextColumn::make('received_payments')
            ->label(function () {
                $summaryStats = $this->getCachedSummaryStatistics();

                $totalTikTokAmount = $summaryStats['totalTikTokPayments'] ?? 0;
                $totalTikTokCount = $summaryStats['totalTikTokCount'] ?? 0;
                $totalBankAmount = $summaryStats['totalBankTransactions'] ?? 0;
                $totalBankCount = $summaryStats['totalBankCount'] ?? 0;

                $html = '';
                $html .= $this->formatSummaryItem("TikTok", $this->currency . number_format($totalTikTokAmount, 2) . " ({$totalTikTokCount} txns)");
                $html .= $this->formatSummaryItem("Bank", $this->currency . number_format($totalBankAmount, 2) . " ({$totalBankCount} txns)");

                return $this->getColumnHeaderLabel('Payouts', 'Thanh toán đã nhận', $html);
            })
            ->html()
            ->getStateUsing(function ($record) {
                // Get optimized data
                $optimizedData = $this->getCachedOptimizedData();
                $paymentsData = $optimizedData[$record->id]['payments'] ?? [];

                $tiktokData = $paymentsData['tiktok'] ?? ['total_amount' => 0, 'count' => 0];
                $bankData = $paymentsData['bank'] ?? ['total_amount' => 0, 'count' => 0];

                return view('filament.tables.columns.seller-statistics.received-payments', [
                    'tikTokTotal' => $tiktokData['total_amount'],
                    'tikTokTransactionsCount' => $tiktokData['count'],
                    'bankTransactionsTotal' => $bankData['total_amount'],
                    'bankTransactionsCount' => $bankData['count'],
                    'currency' => $this->currency,
                ])->render();
            });
    }

    /**
     * Calculate optimized summary statistics using single query
     */
    protected function calculateOptimizedSummaryStatistics(): array
    {
        try {
            // Get all filtered users with pre-calculated data
            $users = $this->getFilteredTableQuery()->with(['teams:id,name', 'stores:id,owner_id,bank_account'])->get();

            // Calculate totals from pre-calculated fields
            $stats = [
                'totalSellers' => $users->count(),
                'totalTeams' => $users->pluck('teams')->flatten()->pluck('name')->unique()->count(),
                'totalOrders' => $users->sum('total_orders'),
                'totalCompletedOrders' => $users->sum('completed_orders'),
                'totalCompletedRevenue' => $users->sum('completed_revenue'),
                'totalRevenue' => $users->sum('total_revenue'),
                'totalSupplierOrders' => $users->sum('total_supplier_orders'),
                'totalCompletedSupplierOrders' => $users->sum('completed_supplier_orders'),
                'totalSupplierCost' => $users->sum('total_supplier_cost'),
                'totalDesignFees' => $users->sum('design_fees'),
                'totalFundAdvances' => $users->sum('fund_advances'),

                // Add aliases for view compatibility
                'totalCompletedOrdersRevenue' => $users->sum('completed_revenue'),
                'totalOrdersRevenue' => $users->sum('total_revenue'),
                'completedSupplierOrders' => $users->sum('completed_supplier_orders'),
                'totalSupplierOrdersCost' => $users->sum('total_supplier_cost'),
                'totalSupplierOrdersTotalCost' => $users->sum('total_supplier_cost'), // Simplified for now
            ];

            // Calculate additional stats using SellerService for a sample if needed
            $dateRange = $this->getDateRangeFromFilters();

            // Calculate TikTok and Bank payments using aggregated queries
            $allStoreIds = [];
            $allBankAccounts = [];

            foreach ($users as $user) {
                if ($user->stores) {
                    $allStoreIds = array_merge($allStoreIds, $user->stores->pluck('id')->toArray());
                    $allBankAccounts = array_merge($allBankAccounts, $user->stores->pluck('bank_account')->filter()->toArray());
                }
            }

            if (!empty($allStoreIds)) {
                $tiktokPayments = TikTokPayment::whereIn('store_id', $allStoreIds)
                    ->whereBetween('paid_time', [$dateRange['from'], $dateRange['until']])
                    ->where('status', 'PAID')
                    ->selectRaw('SUM(settlement_amount) as total_amount, COUNT(*) as total_count')
                    ->first();

                $stats['totalTikTokPayments'] = $tiktokPayments->total_amount ?? 0;
                $stats['totalTikTokCount'] = $tiktokPayments->total_count ?? 0;
            } else {
                $stats['totalTikTokPayments'] = 0;
                $stats['totalTikTokCount'] = 0;
            }

            if (!empty($allBankAccounts)) {
                $bankTransactions = PayoutTransaction::whereIn('card_no', $allBankAccounts)
                    ->where('type', 'Receive')
                    ->where('status', 'Success')
                    ->whereBetween('time', [$dateRange['from'], $dateRange['until']])
                    ->selectRaw('SUM(amount) as total_amount, COUNT(*) as total_count')
                    ->first();

                $stats['totalBankTransactions'] = $bankTransactions->total_amount ?? 0;
                $stats['totalBankCount'] = $bankTransactions->total_count ?? 0;
            } else {
                $stats['totalBankTransactions'] = 0;
                $stats['totalBankCount'] = 0;
            }

            // Calculate total profit for view compatibility
            $totalCosts = $stats['totalSupplierCost'] + $stats['totalDesignFees'] + $stats['totalFundAdvances'];
            $stats['totalProfit'] = $stats['totalCompletedRevenue'] - $totalCosts;

            return $stats;

        } catch (\Exception $e) {
            \Log::error('Error in calculateOptimizedSummaryStatistics: ' . $e->getMessage());
            throw $e; // Re-throw to be caught by getCachedSummaryStatistics
        }
    }

    // Cột Profit (Lợi nhuận) - Optimized with SellerService
    protected function getProfitColumn(): TextColumn
    {
        return TextColumn::make('calculated_profit')
            ->label(function () {
                $summaryStats = $this->getCachedSummaryStatistics();

                // Calculate total profit using cached data
                $totalRevenue = $summaryStats['totalCompletedRevenue'] ?? 0;
                $totalCosts = ($summaryStats['totalSupplierCost'] ?? 0) +
                             ($summaryStats['totalDesignFees'] ?? 0) +
                             ($summaryStats['totalFundAdvances'] ?? 0);

                $totalProfit = $totalRevenue - $totalCosts;
                $avgMargin = $totalRevenue > 0 ? ($totalProfit / $totalRevenue) * 100 : 0;

                $html = '';
                $html .= $this->formatSummaryItem('Total', $this->currency . number_format($totalProfit, 2));
                $html .= $this->formatSummaryItem('Margin', number_format($avgMargin, 1) . '%');

                return $this->getColumnHeaderLabel('Profit', 'Lợi nhuận thực', $html);
            })
            ->getStateUsing(function ($record) {
                // Get optimized data
                $optimizedData = $this->getCachedOptimizedData();
                $ordersData = $optimizedData[$record->id]['orders'] ?? [];
                $supplierOrdersData = $optimizedData[$record->id]['supplier_orders'] ?? [];
                $designJobsData = $optimizedData[$record->id]['design_jobs'] ?? [];
                $fundRequestsData = $optimizedData[$record->id]['fund_requests'] ?? [];
                $productionData = $optimizedData[$record->id]['production'] ?? [];

                // Calculate revenue and expenses
                $revenue = $ordersData['completed_revenue'] ?? 0;
                $supplierCost = $supplierOrdersData['completed_cost'] ?? 0;
                $designFees = $designJobsData['total_fees'] ?? 0;
                $fundAdvances = $fundRequestsData['total_amount'] ?? 0;
                $productionCost = $productionData['total_cost'] ?? 0;

                $totalExpenses = $supplierCost + $designFees + $fundAdvances + $productionCost;
                $profit = $revenue - $totalExpenses;
                $margin = $revenue > 0 ? ($profit / $revenue) * 100 : 0;

                return view('filament.tables.columns.seller-statistics.profit', [
                    'profit' => $profit,
                    'margin' => $margin,
                    'description' => "Revenue: {$revenue} - Expenses: {$totalExpenses}",
                    'currency' => $this->currency
                ]);
            })
            ->html()
            ->sortable()
            ->color('success');
    }

    // Thêm phương thức mới cho cột Supplier Orders - Optimized
    protected function getSupplierOrdersColumn(): TextColumn
    {
        return TextColumn::make('total_supplier_orders')
            ->label(function () {
                // Use cached summary statistics
                $summaryStats = $this->getCachedSummaryStatistics();

                $totalCompletedOrders = $summaryStats['totalCompletedSupplierOrders'] ?? 0;
                $totalOrders = $summaryStats['totalSupplierOrders'] ?? 0;
                $totalCost = $summaryStats['totalSupplierCost'] ?? 0;

                $html = '';
                $html .= $this->formatSummaryItem('Cost', $this->currency . number_format($totalCost, 2));
                $html .= $this->formatSummaryItem('Completed', number_format($totalCompletedOrders));
                $html .= $this->formatSummaryItem('Total', number_format($totalOrders));

                return $this->getColumnHeaderLabel('Supplier Orders', 'Đơn nhà cung cấp (All except Cancelled)', $html);
            })
            ->getStateUsing(function ($record) {
                // Get optimized data
                $optimizedData = $this->getCachedOptimizedData();
                $supplierOrdersData = $optimizedData[$record->id]['supplier_orders'] ?? [];

                $totalOrders = $supplierOrdersData['total_orders'] ?? 0;
                $completedOrders = $supplierOrdersData['completed_orders'] ?? 0;
                $completedCost = $supplierOrdersData['completed_cost'] ?? 0;
                $totalCost = $supplierOrdersData['total_cost'] ?? 0;

                return view('filament.tables.columns.seller-statistics.supplier-orders', [
                    'totalOrders' => $totalOrders,
                    'completedOrders' => $completedOrders,
                    'totalCostCompleted' => $completedCost,
                    'totalCost' => $totalCost,
                    'currency' => $this->currency
                ]);
            })
            ->sortable()
            ->html();
    }

    protected function getTableFilters(): array
    {
        return [
            SelectFilter::make('team')
                ->label('Team')
                ->relationship('teams', 'name')
                ->multiple()
                ->preload(),
        ];
    }

    protected function getColumnHeaderLabel(string $label, string $subLabel, string $html): \Illuminate\Support\HtmlString
    {
        return new \Illuminate\Support\HtmlString(
            view('filament.tables.columns.seller-statistics.header-label', [
                'label' => $label,
                'subLabel' => $subLabel,
                'html' => $html
            ])->render()
        );
    }

    // Helper to generate consistent HTML for summary data in column headers
    protected function formatSummaryItem(string $label, string $value): string
    {
        return "<div class=\"flex justify-between items-center text-xs\"><span class=\"text-gray-600 dark:text-gray-400\">{$label}:</span><span class=\"font-medium text-gray-800 dark:text-gray-200\">{$value}</span></div>";
    }

    public function getData(): array
    {
        return [
            'summaryStats' => $this->getCachedSummaryStatistics(),
            'currency' => $this->currency
        ];
    }

    // Add this new method after the last column method
    protected function getReportStatusColumn(): TextColumn
    {
        return TextColumn::make('report_status')
            ->label(function () {
                // Lấy thời gian từ filter sử dụng method có sẵn
                $dateRange = $this->getDateRangeFromFilters();
                $startDate = $dateRange['from'];
                $endDate = $dateRange['until'];

                // Đếm tất cả các seller based on current table filters
                $allSellersQuery = $this->getFilteredTableQuery(); // Use the table's own filtered query for sellers
                $sellerIds = $allSellersQuery->pluck('id');
                $totalSellers = $sellerIds->count();

                // SellerFinance data
                $sellerFinancesQuery = SellerFinance::query()
                    ->whereIn('seller_id', $sellerIds)
                    ->whereYear('month', $startDate->year)
                    ->whereMonth('month', $startDate->month);

                $createdSellerFinances = $sellerFinancesQuery->get();
                $createdReports = $createdSellerFinances->count();
                $completedFinanceReports = $createdSellerFinances->where('status', 'completed')->count();
                $totalNetProfitFinance = $createdSellerFinances->sum('net_profit');

                $pendingReports = $totalSellers - $createdReports;

                // Invoice data
                $invoicesQuery = Invoice::query()
                    ->whereIn('user_id', $sellerIds)
                    ->whereYear('billing_month', $startDate->year)
                    ->whereMonth('billing_month', $startDate->month);

                $periodInvoices = $invoicesQuery->get();
                $totalInvoices = $periodInvoices->count();
                $paidInvoices = $periodInvoices->where('status', 'paid')->count();
                $totalInvoiceAmount = $periodInvoices->sum('total_amount');
                $totalPaidInvoiceAmount = $periodInvoices->sum('paid_amount');

                $html = $this->formatSummaryItem('Total Sellers', number_format($totalSellers));
                $html .= $this->formatSummaryItem('Reports Created', number_format($createdReports));
                $html .= $this->formatSummaryItem('Reports Pending', number_format($pendingReports));
                $html .= $this->formatSummaryItem('Reports Completed', number_format($completedFinanceReports));
                $html .= $this->formatSummaryItem('Total Net Profit (Fin.)', $this->currency . number_format($totalNetProfitFinance, 2));
                $html .= "<hr class='my-1 border-gray-300 dark:border-gray-600'>";
                $html .= $this->formatSummaryItem('Total Invoices', number_format($totalInvoices));
                $html .= $this->formatSummaryItem('Paid Invoices', number_format($paidInvoices));
                $html .= $this->formatSummaryItem('Total Billed (Inv.)', $this->currency . number_format($totalInvoiceAmount, 2));
                $html .= $this->formatSummaryItem('Total Received (Inv.)', $this->currency . number_format($totalPaidInvoiceAmount, 2));

                return $this->getColumnHeaderLabel('Report & Invoice Status', 'Báo cáo & Hoá đơn', $html);
            })
            ->formatStateUsing(function (User $record): string {
                try {
                    // Lấy thời gian từ filter sử dụng method có sẵn
                    $dateRange = $this->getDateRangeFromFilters();
                    $currentMonthStart = $dateRange['from']->copy()->startOfMonth();

                    $sellerFinance = SellerFinance::where('seller_id', $record->id)
                        ->whereYear('month', $currentMonthStart->year)
                        ->whereMonth('month', $currentMonthStart->month)
                        ->first();

                    $invoice = Invoice::where('user_id', $record->id)
                        ->whereYear('billing_month', $currentMonthStart->year)
                        ->whereMonth('billing_month', $currentMonthStart->month)
                        ->first();

                    $refreshTimestamp = now()->timestamp;

                    return view('filament.tables.columns.seller-statistics.report-status', [
                        'sellerFinance' => $sellerFinance,
                        'invoice' => $invoice,
                        'hasReport' => (bool)$sellerFinance, // Keep for compatibility if view uses it
                        'timestamp' => $refreshTimestamp,
                        'currency' => $this->currency
                    ])->render();
                } catch (\Exception $e) {
                    // Log the error: Log::error("Error formatting report status for seller {$record->id}: {$e->getMessage()}");
                    return view('filament.tables.columns.seller-statistics.report-status', [
                        'sellerFinance' => null,
                        'invoice' => null,
                        'hasReport' => false,
                        'timestamp' => now()->timestamp,
                        'currency' => $this->currency,
                        'error' => 'Error loading data'
                    ])->render();
                }
            })
            ->state(function (User $record): int {
                try {
                    // Lấy thời gian từ filter sử dụng method có sẵn
                    $dateRange = $this->getDateRangeFromFilters();
                    $currentMonthStart = $dateRange['from']->copy()->startOfMonth();

                    $sellerFinance = SellerFinance::where('seller_id', $record->id)
                        ->whereYear('month', $currentMonthStart->year)
                        ->whereMonth('month', $currentMonthStart->month)
                        ->first();

                    if ($sellerFinance) {
                        return $sellerFinance->status === 'completed' ? 2 : 1; // Completed (2) > Pending (1)
                    }
                    return 0; // No report (0)
                } catch (\Exception $e) {
                    return 0;
                }
            })
            ->disabledClick()
            ->html();
    }
}