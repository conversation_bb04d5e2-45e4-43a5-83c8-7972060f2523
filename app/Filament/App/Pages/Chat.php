<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use Livewire\Attributes\Url;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use OpenAI;
use OpenAI\Responses\StreamResponse;
use OpenAI\Responses\Chat\CreateStreamedResponse;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use OpenAI\Client;

class Chat extends Page
{
    protected static string $view = 'filament.app.pages.chat';
    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';
    protected static ?string $navigationLabel = 'Chat Bot';
    protected static ?string $title = 'Chat Bot';
    protected static ?string $slug = 'chat';
    protected static ?int $navigationSort = 3;

    protected static bool $shouldRegisterNavigation = false;


    public $messages = [];

    public $prompt = '';
    public $answer = '';
    public $isLoading = false;
    public $showThinking = true; // Biến kiểm soát hiển thị thinking
    public $lastStreamedContent = ''; // <PERSON> dõi nội dung stream cuối cùng
    public $question = '';
    public $thinkingFound = false; // Biến để theo dõi nếu thinking tag đã được tìm thấy

    protected OpenAI\Client $openaiClient;

    public function mount()
    {
        // Kiểm tra quyền truy cập
        if (!Auth::user()->hasRole(['seller', 'super_admin'])) {
            abort(403);
        }
    }

    public function submitPrompt()
    {
        $this->messages[] = [
            'role' => 'user',
            'content' => $this->prompt,
        ];

        // Lưu câu hỏi và reset form
        $this->question = $this->prompt;
        $this->prompt = '';

        // Thêm tin nhắn trống cho trợ lý để cập nhật nội dung stream
        $this->messages[] = [
            'role' => 'assistant',
            'content' => ''
        ];

        // Sử dụng JavaScript để gọi phương thức ask
        $this->js('$wire.ask()');
    }

    public function ask()
    {
        $question = $this->question;

        // Reset state và hiển thị loading
        $this->isLoading = true;
        $this->answer = '';
        $this->thinkingFound = false;
        
        // Stream message ngay lập tức
        $this->stream('answer', '<think>Đang suy nghĩ...</think>', true);

        // Lấy index của tin nhắn cuối cùng
        $lastMessageIndex = count($this->messages) - 1;

        try {
            // Chuẩn bị tin nhắn
            $messages = [];
            foreach ($this->messages as $message) {
                // Chỉ lấy tin nhắn có nội dung
                if (!empty($message['content']) && $message['role'] !== 'assistant') {
                    $messages[] = [
                        'role' => $message['role'],
                        'content' => $message['content'],
                    ];
                }
            }

            // Gọi API trực tiếp và xử lý streaming
            $fullContent = $this->directStreamFromAPI($messages);

            // Lưu nội dung đầy đủ (bao gồm phần thinking) vào message
            $this->messages[$lastMessageIndex]['content'] = $fullContent;

        } catch (\Exception $e) {
            // Cập nhật tin nhắn với thông báo lỗi
            $errorMessage = 'An error occurred: ' . $e->getMessage();
            $this->messages[$lastMessageIndex]['content'] = $errorMessage;
            $this->answer = $errorMessage;
            $this->isLoading = false;
            return $errorMessage;
        } finally {
            $this->isLoading = false;
        }
    }

    /**
     * Xử lý streaming trực tiếp từ API
     */
    protected function directStreamFromAPI(array $messages)
    {
        try {
            // URL API OpenAI
            $url = "https://agent-lylmwcl5lhaiixhmipaqshjq-oomop.ondigitalocean.app/api/v1/chat/completions";

            // Khởi tạo cURL
            $ch = curl_init();

            // Thiết lập các tùy chọn cURL
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, false); // Trả kết quả trực tiếp ra màn hình
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Theo dõi redirect
            curl_setopt($ch, CURLOPT_TIMEOUT, 0); // Không timeout
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // Thời gian chờ kết nối
            curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $data) {
                // Xử lý từng chunk
                $lines = explode("\n", $data);
                foreach ($lines as $line) {
                    // Loại bỏ khoảng trắng đầu cuối
                    $line = trim($line);

                    // Bỏ qua các dòng rỗng
                    if (empty($line)) continue;

                    // Bỏ qua các dòng không phải là JSON (ví dụ: "data: ")
                    if (!str_starts_with($line, "data: ")) continue;

                    // Loại bỏ tiền tố "data: "
                    $jsonString = substr($line, 6);

                    // Giải mã JSON
                    $json = json_decode($jsonString, true);

                    // Kiểm tra lỗi JSON
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        continue;
                    }

                    // Kiểm tra xem chunk có nội dung không
                    if (isset($json['choices'][0]['delta']['content'])) {
                        $content = $json['choices'][0]['delta']['content'];

                        // Nếu có nội dung, truyền về frontend và lưu vào danh sách
                        if (!empty($content)) {
                            // Thêm nội dung vào answer
                            $this->answer .= $content;
                            
                            // Kiểm tra nếu tìm thấy tag </think>
                            if (!$this->thinkingFound && str_contains($this->answer, '</think>')) {
                                $this->thinkingFound = true;
                                // Format lại câu trả lời để hiển thị phần thinking trong khung riêng
                                $formattedAnswer = str_replace('</think>', '</div></div><div class="actual-answer mt-3">', $this->answer);
                                $this->stream('answer', $formattedAnswer, true);
                            } else {
                                // Kiểm tra nếu tìm thấy tag <think>
                                if (!$this->thinkingFound && str_contains($content, '<think>') && !str_contains($this->answer, '<div class="thinking-section')) {
                                    // Tạo HTML prefix cho phần thinking
                                    $thinkingPrefix = '<div class="thinking-section mb-2 p-2 bg-yellow-50 dark:bg-gray-600 rounded-md border border-yellow-200 dark:border-gray-500 relative">';
                                    $thinkingPrefix .= '<div class="text-xs text-gray-500 dark:text-gray-400 mb-1 flex items-center">';
                                    $thinkingPrefix .= '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                                    $thinkingPrefix .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />';
                                    $thinkingPrefix .= '</svg><span>Suy nghĩ</span></div>';
                                    $thinkingPrefix .= '<div class="italic text-sm text-gray-600 dark:text-gray-300">';
                                    
                                    // Thay thế tag <think> bằng HTML prefix
                                    $formattedAnswer = str_replace('<think>', $thinkingPrefix, $this->answer);
                                    $this->stream('answer', $formattedAnswer, true);
                                } else {
                                    // Stream nội dung thông thường
                                    $this->stream('answer', nl2br($content));
                                }
                            }

                            // Cập nhật tin nhắn trong danh sách chat
                            $lastMessageIndex = count($this->messages) - 1;
                            $this->messages[$lastMessageIndex]['content'] = $this->answer;

                            flush();
                            usleep(100000);
                        }
                    }
                }
                return strlen($data); // Trả về độ dài chunk để tiếp tục stream
            });

            // Thiết lập phương thức POST và dữ liệu JSON
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer bUZxBhizEpWXrBnXx9VnuCUk6lJ4uhQe'
            ]);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
                'model' => 'gpt-3.5-turbo',
                'messages' => $messages,
                'temperature' => 0.7,
                'max_tokens' => 2048,
                'stream' => true
            ]));

            // Thực hiện cURL request
            curl_exec($ch);

            // Kiểm tra lỗi cURL
            if (curl_errno($ch)) {
                $errorMessage = 'cURL Error: ' . curl_error($ch);
                Log::error($errorMessage);
                $this->stream('answer', $errorMessage);
                return $errorMessage;
            }

            // Đóng cURL
            curl_close($ch);
            return $this->answer;
        } catch (\Exception $e) {
            $errorMessage = 'An unexpected error occurred: ' . $e->getMessage();
            Log::error($errorMessage);
            $this->stream('answer', $errorMessage);
            return $errorMessage;
        }
    }

    /**
     * Lấy phần nội dung thực tế (loại bỏ phần thinking)
     */
    public function getActualAnswer($content)
    {
        // Nếu có tag </think> trong nội dung, chỉ lấy phần nội dung sau tag
        if (str_contains($content, '</think>')) {
            $parts = explode('</think>', $content, 2);
            if (count($parts) > 1) {
                return $parts[1];
            }
        }
        return $content;
    }

    /**
     * Lấy phần suy nghĩ từ nội dung
     */
    public function getThinking($content)
    {
        // Trích xuất phần suy nghĩ (nội dung trong tag <think>...</think>)
        if (preg_match('/<think>(.*?)<\/think>/s', $content, $matches)) {
            return $matches[1];
        }
        return '';
    }

    /**
     * Kiểm tra xem nội dung có phần suy nghĩ không
     */
    public function hasThinking($content)
    {
        return str_contains($content, '<think>') && str_contains($content, '</think>');
    }

    /**
     * Hiển thị nội dung với phần suy nghĩ và phần trả lời được format
     */
    public function formatMessageContent($content)
    {
        if ($this->hasThinking($content) && $this->showThinking) {
            $thinking = $this->getThinking($content);
            $answer = $this->getActualAnswer($content);
            
            $thinkingHtml = '<div class="thinking-section mb-2 p-2 bg-yellow-50 dark:bg-gray-600 rounded-md border border-yellow-200 dark:border-gray-500 relative"><div class="text-xs text-gray-500 dark:text-gray-400 mb-1 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg><span>Suy nghĩ</span></div><div class="italic text-sm text-gray-600 dark:text-gray-300">' . nl2br(e($thinking)) . '</div></div>';
            
            return $thinkingHtml . '<div class="actual-answer mt-3">' . nl2br(e($answer)) . '</div>';
        } else {
            // Nếu không có thinking hoặc không hiển thị thinking
            if ($this->hasThinking($content)) {
                return nl2br(e($this->getActualAnswer($content)));
            } else {
                return nl2br(e($content));
            }
        }
    }

    public function toggleThinking()
    {
        $this->showThinking = !$this->showThinking;
    }

    // Override phương thức stream để debug
    public function stream($to, $content, $replace = false)
    {
        // Ghi log trước khi stream
        Log::info('Streaming content', [
            'to' => $to,
            'content_length' => strlen($content),
            'replace' => $replace
        ]);

        // Cập nhật biến theo dõi
        $this->lastStreamedContent = $content;

        // Gọi phương thức stream của lớp cha
        parent::stream($to, $content, $replace);
    }

    public function getViewData(): array
    {
        return [
            'messages' => $this->messages,
            'answer' => $this->answer,
            'isLoading' => $this->isLoading,
            'showThinking' => $this->showThinking
        ];
    }
}
