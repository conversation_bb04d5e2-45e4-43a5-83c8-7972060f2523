<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class AmazonMerchSpy extends Page
{
    use HasPageShield;
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin','Seller']) ;
    }
    protected static bool $shouldRegisterNavigation = false;

    public static function getNavigationGroup(): ?string
    {
        return null;
    }

    public static function getNavigationIcon(): ?string
    {
        return null;
    }
    protected static ?string $navigationIcon = 'heroicon-o-at-symbol';
    protected static ?string $navigationGroup = 'Spy Idea';
    protected static string $view = 'filament.app.pages.amazon-merch-spy';

    public $page_view;

    public function mount()
    {
        $this->page_view = request('page_view', 'amazon-winning-product');
    }



}
