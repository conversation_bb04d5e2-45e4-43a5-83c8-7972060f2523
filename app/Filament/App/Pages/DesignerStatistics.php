<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use App\Models\User;
use App\Models\DesignJob;
use App\Exports\DesignerCsvExport;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\Url;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class DesignerStatistics extends Page implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-paint-brush';
    protected static ?string $navigationLabel = 'Designer Dashboard';
    protected static ?int $navigationSort = 2;

    protected static string $view = 'filament.app.pages.designer-statistics';
    protected string $currency = '$';
    protected static bool $shouldRegisterNavigation = false;

    // Cache configuration
    const CACHE_TTL = 3600; // 1 hour
    const CACHE_PREFIX = 'designer_stats_';

    #[Url(except: '')]
    public $dateRange = null;

    #[Url(except: '')]
    public $selectedDesigner = null;

    // Form data property
    public ?array $data = [];

    // Query string parameters
    protected $queryString = [
        'dateRange' => ['except' => ''],
        'selectedDesigner' => ['except' => ''],
    ];

    // Cached data properties
    protected ?array $cachedSummaryStats = null;

    public static function canAccess(): bool
    {
        if (Auth::check()) {
            return Auth::user()->hasAnyRole(['super_admin', 'User Manager']);
        }
        return false;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('export_all_designers')
                ->label('Export All Designers')
                ->icon('heroicon-o-cloud-arrow-down')
                ->color('danger')
                ->size('lg')
                ->action(function () {
                    return $this->exportAllDesignersData();
                })
                ->requiresConfirmation()
                ->modalHeading('Xuất dữ liệu tất cả Designer')
                ->modalDescription(function () {
                    $totalDesigners = User::whereHas('roles', function($q) {
                        $q->where('name', 'Designer');
                    })->count();

                    $dateRange = $this->getDateRangeFromFilters();
                    $monthText = $dateRange['from']->locale('vi')->translatedFormat('F Y') . ' - ' .
                                $dateRange['until']->locale('vi')->translatedFormat('F Y');

                    return "Bạn có chắc chắn muốn xuất dữ liệu của TẤT CẢ {$totalDesigners} designer trong hệ thống cho khoảng thời gian {$monthText}? " .
                           "Quá trình này có thể mất rất nhiều thời gian và tạo ra file ZIP rất lớn. " .
                           "Chỉ nên thực hiện khi thực sự cần thiết.";
                })
                ->modalSubmitActionLabel('Xuất tất cả dữ liệu')
                ->modalCancelActionLabel('Hủy bỏ')
                ->visible(function () {
                    // Chỉ cho phép super_admin và User Manager sử dụng chức năng này
                    $user = auth()->user();
                    return $user && $user->hasAnyRole(['super_admin', 'User Manager']);
                })
                ->tooltip('Xuất dữ liệu của tất cả designer (chỉ dành cho Super Admin và User Manager)'),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedDesigner')
                    ->label('Chọn Designer')
                    ->options($this->getDesignerOptions())
                    ->searchable()
                    ->placeholder('Chọn designer để xem thống kê')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedDesigner = $state;
                        $this->data['selectedDesigner'] = $state;
                        $this->clearCache();
                        $this->resetTable();
                    })
                    ->columnSpan(1),

                DateRangePicker::make('dateRange')
                    ->label('Khoảng thời gian')
                    ->timezone(config('app.timezone'))
                    ->displayFormat('D/M/Y')
                    ->format('d/m/Y')
                    ->separator(' - ')
                    ->ranges(function() {
                        $thisMonth = Carbon::now();
                        $lastMonth = Carbon::now()->subMonth();
                        $twoMonthsAgo = Carbon::now()->subMonths(2);
                        $threeMonthsAgo = Carbon::now()->subMonths(3);

                        return [
                            'Tháng này' => [
                                $thisMonth->copy()->startOfMonth(),
                                $thisMonth->copy()->endOfMonth()
                            ],
                            'Tháng trước' => [
                                $lastMonth->copy()->startOfMonth(),
                                $lastMonth->copy()->endOfMonth(),
                            ],
                            '2 tháng trước' => [
                                $twoMonthsAgo->copy()->startOfMonth(),
                                $twoMonthsAgo->copy()->endOfMonth(),
                            ],
                            '3 tháng trước' => [
                                $threeMonthsAgo->copy()->startOfMonth(),
                                $threeMonthsAgo->copy()->endOfMonth(),
                            ],
                            '3 tháng gần nhất' => [
                                $twoMonthsAgo->copy()->startOfMonth(),
                                $thisMonth->copy()->endOfMonth(),
                            ],
                            'Năm nay' => [
                                Carbon::now()->startOfYear(),
                                Carbon::now()->endOfYear(),
                            ],
                        ];
                    })
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->dateRange = $state;
                        $this->data['dateRange'] = $state;
                        $this->clearCache();
                        $this->resetTable();
                    })
                    ->columnSpan(1),
            ])
            ->columns(2)
            ->statePath('data');
    }



    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns($this->getTableColumns())
            ->filters($this->getTableFilters())
            ->actions([
                TableAction::make('export_designer_data')
                    ->label('Export Data')
                    ->icon('heroicon-o-archive-box-arrow-down')
                    ->color('warning')
                    ->action(function (User $record) {
                        return $this->exportDesignerData($record);
                    })
                    ->tooltip('Xuất tất cả dữ liệu của designer này')
                    ->requiresConfirmation()
                    ->modalHeading('Xuất dữ liệu designer')
                    ->modalDescription(function (User $record): string {
                        $dateRange = $this->getDateRangeFromFilters();
                        $periodText = $dateRange['from']->format('d/m/Y') . ' - ' . $dateRange['until']->format('d/m/Y');

                        return "Bạn có chắc chắn muốn xuất tất cả dữ liệu của designer {$record->name} cho khoảng thời gian {$periodText}? " .
                               "Quá trình này có thể mất vài phút.";
                    })
                    ->modalSubmitActionLabel('Xuất dữ liệu')
                    ->visible(function (User $record) {
                        $user = auth()->user();
                        return $user && $user->hasAnyRole(['super_admin', 'User Manager']);
                    }),

                TableAction::make('print_invoice')
                    ->label('Print Invoice')
                    ->icon('heroicon-o-printer')
                    ->color('success')
                    ->url(function (User $record) {
                        $dateRange = $this->getDateRangeFromFilters();
                        $startDate = $dateRange['from']->format('d/m/Y');
                        $endDate = $dateRange['until']->format('d/m/Y');
                        $timeRange = $startDate . ' - ' . $endDate;

                        return route('invoice-design.print', [
                            'designer_id' => $record->id,
                            'summary' => [
                                'time_range' => [
                                    'created_at' => $timeRange
                                ]
                            ]
                        ]);
                    })
                    ->openUrlInNewTab()
                    ->tooltip('In hóa đơn designer cho khoảng thời gian đã chọn'),
            ])
            ->defaultSort('total_jobs', 'desc')
            ->paginated(false); // Disable pagination for simplicity
    }

    /**
     * Override getTableRecordKey to handle User records
     */
    public function getTableRecordKey($record): string
    {
        return (string) $record->id;
    }

    public function mount()
    {
        // Set default date range to last month
        if (empty($this->dateRange)) {
            $lastMonth = Carbon::now()->subMonth();
            $startDate = $lastMonth->copy()->startOfMonth()->format('d/m/Y');
            $endDate = $lastMonth->copy()->endOfMonth()->format('d/m/Y');
            $this->dateRange = $startDate . ' - ' . $endDate;
        }

        // Initialize form data
        $this->data = [
            'selectedDesigner' => $this->selectedDesigner,
            'dateRange' => $this->dateRange,
        ];

        $this->form->fill($this->data);
    }

    protected function getDesignerOptions(): array
    {
        try {
            return User::whereHas('roles', function ($query) {
                $query->where('name', 'Designer');
            })
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
        } catch (\Exception $e) {
            \Log::error('Error getting designer options: ' . $e->getMessage());
            return [];
        }
    }

    protected function getTableQuery(): Builder
    {
        $dateRange = $this->getDateRangeFromFilters();
        $fromDate = $dateRange['from']->format('Y-m-d H:i:s');
        $untilDate = $dateRange['until']->format('Y-m-d H:i:s');

        // Create a subquery to get aggregated data
        $subQuery = DesignJob::query()
            ->select([
                'designer_id',
                DB::raw('COUNT(*) as total_jobs'),
                DB::raw('SUM(price) as total_earnings'),
                DB::raw('SUM(rush_fee) as total_rush_fees'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_jobs'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN price ELSE 0 END) as completed_earnings'),
                DB::raw('SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_jobs'),
                DB::raw('SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned_jobs'),
                DB::raw('SUM(CASE WHEN is_rush = 1 THEN 1 ELSE 0 END) as rush_jobs'),
            ])
            ->whereBetween('created_at', [$fromDate, $untilDate])
            ->whereNotNull('designer_id')
            ->groupBy('designer_id');

        // Filter by selected designer if specified
        if ($this->selectedDesigner) {
            $subQuery->where('designer_id', $this->selectedDesigner);
        }

        // Now create the main query using User model to have proper primary keys
        $query = User::query()
            ->select([
                'users.id',
                'users.name',
                'users.email',
                'stats.total_jobs',
                'stats.total_earnings',
                'stats.total_rush_fees',
                'stats.completed_jobs',
                'stats.completed_earnings',
                'stats.pending_jobs',
                'stats.assigned_jobs',
                'stats.rush_jobs'
            ])
            ->joinSub($subQuery, 'stats', function ($join) {
                $join->on('users.id', '=', 'stats.designer_id');
            })
            ->whereHas('roles', function ($roleQuery) {
                $roleQuery->where('name', 'Designer');
            });

        return $query;
    }

    protected function addJobStatisticsToQuery(Builder $query): void
    {
        $baseJobQuery = function($status = null, $isRush = null) {
            $subQuery = DesignJob::query()->whereColumn('designer_id', 'users.id');

            if ($status) {
                $subQuery->where('status', $status);
            }

            if ($isRush !== null) {
                $subQuery->where('is_rush', $isRush);
            }

            return $subQuery;
        };

        $query->addSelect([
            'users.*',
            'total_jobs' => $baseJobQuery()->selectRaw('COUNT(*)'),
            'total_earnings' => $baseJobQuery()->selectRaw('COALESCE(SUM(price), 0)'),
            'total_rush_fees' => $baseJobQuery()->selectRaw('COALESCE(SUM(rush_fee), 0)'),
            'completed_jobs' => $baseJobQuery('completed')->selectRaw('COUNT(*)'),
            'completed_earnings' => $baseJobQuery('completed')->selectRaw('COALESCE(SUM(price), 0)'),
            'pending_jobs' => $baseJobQuery('pending')->selectRaw('COUNT(*)'),
            'assigned_jobs' => $baseJobQuery('assigned')->selectRaw('COUNT(*)'),
            'rush_jobs' => $baseJobQuery(null, true)->selectRaw('COUNT(*)'),
        ]);
    }

    protected function getTableFilters(): array
    {
        return [
            // Chỉ giữ lại designer filter, date range sẽ được xử lý qua form
        ];
    }

    protected function getDateRangeFromFilters(): array
    {
        $dateRangeStr = $this->dateRange;

        if (empty($dateRangeStr)) {
            // Default to last month
            $from = Carbon::now()->subMonth()->startOfMonth();
            $until = Carbon::now()->subMonth()->endOfMonth();
        } else {
            $dates = explode(' - ', $dateRangeStr);
            if (count($dates) == 2) {
                $from = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                $until = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
            } else {
                // Fallback if format is incorrect
                $from = Carbon::now()->subMonth()->startOfMonth();
                $until = Carbon::now()->subMonth()->endOfMonth();
            }
        }

        return ['from' => $from, 'until' => $until];
    }

    /**
     * Reset table state
     */
    protected function resetTable(): void
    {
        // Reset table pagination and sorting if needed
        $this->dispatch('$refresh');
    }





    protected function getTableColumns(): array
    {
        return [
            TextColumn::make('name')
                ->label('Designer')
                ->searchable()
                ->sortable()
                ->formatStateUsing(function ($record) {
                    return view('filament.tables.columns.designer-statistics.designer-info', [
                        'name' => $record->name,
                        'email' => $record->email,
                        'avatar' => $record->getFilamentAvatarUrl(),
                    ]);
                })
                ->html(),

            TextColumn::make('total_jobs')
                ->label('Total Jobs')
                ->sortable()
                ->alignCenter()
                ->badge()
                ->color('primary'),

            TextColumn::make('completed_jobs')
                ->label('Completed')
                ->sortable()
                ->alignCenter()
                ->badge()
                ->color('success'),

            TextColumn::make('pending_jobs')
                ->label('Pending')
                ->sortable()
                ->alignCenter()
                ->badge()
                ->color('warning'),

            TextColumn::make('assigned_jobs')
                ->label('Assigned')
                ->sortable()
                ->alignCenter()
                ->badge()
                ->color('info'),

            TextColumn::make('rush_jobs')
                ->label('Rush Jobs')
                ->sortable()
                ->alignCenter()
                ->badge()
                ->color('danger'),

            TextColumn::make('total_earnings')
                ->label('Total Earnings')
                ->sortable()
                ->money('USD')
                ->alignEnd(),

            TextColumn::make('completed_earnings')
                ->label('Completed Earnings')
                ->sortable()
                ->money('USD')
                ->alignEnd()
                ->description('Earnings from completed jobs only'),

            TextColumn::make('total_rush_fees')
                ->label('Rush Fees')
                ->sortable()
                ->money('USD')
                ->alignEnd(),

            TextColumn::make('completion_rate')
                ->label('Completion Rate')
                ->getStateUsing(function ($record) {
                    if ($record->total_jobs == 0) return 0;
                    return round(($record->completed_jobs / $record->total_jobs) * 100, 1);
                })
                ->suffix('%')
                ->alignCenter()
                ->badge()
                ->color(fn ($state) => $state >= 80 ? 'success' : ($state >= 60 ? 'warning' : 'danger')),
        ];
    }

    /**
     * Clear cache
     */
    public function clearCache(): void
    {
        $this->cachedSummaryStats = null;
        Cache::forget($this->getCacheKey('summary_stats'));

        // Clear all related cache keys
        $keys = ['summary_stats'];
        foreach ($keys as $key) {
            Cache::forget($this->getCacheKey($key));
        }
    }



    /**
     * Generate cache key based on current filters
     */
    protected function getCacheKey(string $suffix = ''): string
    {
        $filterHash = md5(($this->dateRange ?? 'default') . '_' . ($this->selectedDesigner ?? 'all'));
        return self::CACHE_PREFIX . $suffix . '_' . $filterHash;
    }

    /**
     * Get summary statistics for the view
     */
    public function getSummaryStatistics(): array
    {
        if ($this->cachedSummaryStats !== null) {
            return $this->cachedSummaryStats;
        }

        $cacheKey = $this->getCacheKey('summary_stats');

        $this->cachedSummaryStats = Cache::remember($cacheKey, self::CACHE_TTL, function () {
            $stats = $this->calculateSummaryStatistics();

            // Ensure completedEarnings key exists
            if (!array_key_exists('completedEarnings', $stats)) {
                $stats['completedEarnings'] = 0;
            }

            return $stats;
        });

        return $this->cachedSummaryStats;
    }

    /**
     * Calculate summary statistics based on current table filters
     */
    protected function calculateSummaryStatistics(): array
    {
        // Get current table filters
        $tableFilters = $this->getTableFilters();
        $filterData = $this->tableFilters ?? [];

        // Build query based on filters
        $query = DesignJob::whereNotNull('designer_id');

        // Apply date range filter if set
        if (!empty($filterData['created_at'])) {
            $from = $filterData['created_at']['from'] ?? null;
            $until = $filterData['created_at']['until'] ?? null;

            if ($from && $until) {
                $query->whereBetween('created_at', [$from, $until]);
            }
        }

        // Apply designer filter if set
        if (!empty($filterData['designer']['value'])) {
            $query->where('designer_id', $filterData['designer']['value']);
        }

        $stats = $query->selectRaw('
            COUNT(*) as total_jobs,
            SUM(price) as total_earnings,
            SUM(rush_fee) as total_rush_fees,
            SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_jobs,
            SUM(CASE WHEN status = "completed" THEN price ELSE 0 END) as completed_earnings,
            SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_jobs,
            SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned_jobs,
            SUM(CASE WHEN is_rush = 1 THEN 1 ELSE 0 END) as rush_jobs,
            COUNT(DISTINCT designer_id) as active_designers
        ')->first();

        return [
            'totalJobs' => $stats->total_jobs ?? 0,
            'totalEarnings' => $stats->total_earnings ?? 0,
            'completedEarnings' => $stats->completed_earnings ?? 0,
            'totalRushFees' => $stats->total_rush_fees ?? 0,
            'completedJobs' => $stats->completed_jobs ?? 0,
            'pendingJobs' => $stats->pending_jobs ?? 0,
            'assignedJobs' => $stats->assigned_jobs ?? 0,
            'rushJobs' => $stats->rush_jobs ?? 0,
            'activeDesigners' => $stats->active_designers ?? 0,
            'completionRate' => $stats->total_jobs > 0 ? round(($stats->completed_jobs / $stats->total_jobs) * 100, 1) : 0,
        ];
    }

    public function getData(): array
    {
        $filterData = $this->tableFilters ?? [];
        $selectedDesignerId = $filterData['designer']['value'] ?? null;

        return [
            'summaryStats' => $this->getSummaryStatistics(),
            'currency' => $this->currency,
            'selectedDesigner' => $selectedDesignerId,
            'selectedDesignerName' => $selectedDesignerId ?
                User::find($selectedDesignerId)?->name : null,
        ];
    }

    public function exportDesignerData(User $designer)
    {
        try {
            // Kiểm tra quyền truy cập
            $user = auth()->user();
            if (!$user || !$user->hasAnyRole(['super_admin', 'User Manager'])) {
                throw new \Exception('Bạn không có quyền thực hiện chức năng này');
            }

            $dateRange = $this->getDateRangeFromFilters();
            $startDate = $dateRange['from'];
            $endDate = $dateRange['until'];

            $designerName = str_replace(' ', '_', $designer->name);
            $dateRangeStr = $startDate->format('d_m_Y') . '_to_' . $endDate->format('d_m_Y');

            // Danh sách các bảng cần xuất
            $tables = [
                'all_jobs' => 'All Jobs Comprehensive',
                'statistics' => 'Statistics Summary'
            ];

            // Tạo thư mục tạm để lưu các file CSV
            $tempDir = storage_path('app/temp/exports');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $exportedFiles = [];
            $filePaths = [];

            foreach ($tables as $tableType => $tableName) {
                try {
                    Log::info("Starting export for {$tableType} - Designer: {$designer->id}");

                    $export = new DesignerCsvExport($designer, $startDate, $endDate, $tableType);
                    $fileName = "designer-{$designerName}-{$tableType}-{$dateRangeStr}.csv";
                    $filePath = $tempDir . '/' . $fileName;

                    Log::info("Exporting to file: {$fileName}");

                    // Lưu file CSV vào thư mục tạm
                    Excel::store($export, 'temp/exports/' . $fileName, 'local', \Maatwebsite\Excel\Excel::CSV);

                    if (file_exists($filePath)) {
                        $filePaths[] = $filePath;
                        $exportedFiles[] = $tableName;
                        Log::info("Successfully exported {$tableType} to {$filePath}");
                    } else {
                        Log::warning("File not found after export: {$filePath}");
                    }
                } catch (\Exception $e) {
                    Log::error("Error exporting {$tableType} for designer {$designer->id}: " . $e->getMessage());
                    Log::error("Stack trace: " . $e->getTraceAsString());
                }
            }

            // Tạo file designer_information.csv
            try {
                $designerInfoFileName = "designer-{$designerName}-information-{$dateRangeStr}.csv";
                $designerInfoFilePath = $tempDir . '/' . $designerInfoFileName;

                // Chuẩn bị dữ liệu designer information
                $designerInfoData = [
                    ['field_name', 'field_value'], // Header
                    ['Designer ID', $designer->id ?? ''],
                    ['Designer Name', $designer->name ?? ''],
                    ['Email', $designer->email ?? ''],
                    ['Created Date', $designer->created_at ? $designer->created_at->format('d/m/Y H:i') : ''],
                    ['Export Period', $startDate->format('d/m/Y') . ' - ' . $endDate->format('d/m/Y')],
                ];

                // Tạo file CSV
                $handle = fopen($designerInfoFilePath, 'w');
                if ($handle) {
                    foreach ($designerInfoData as $row) {
                        fputcsv($handle, $row);
                    }
                    fclose($handle);

                    if (file_exists($designerInfoFilePath)) {
                        $filePaths[] = $designerInfoFilePath;
                        $exportedFiles[] = 'Designer Information';
                    }
                }
            } catch (\Exception $e) {
                Log::error("Error creating designer_information.csv for designer {$designer->id}: " . $e->getMessage());
            }

            if (empty($filePaths)) {
                throw new \Exception('Không có file nào được tạo thành công');
            }

            // Tạo file ZIP
            $zipFileName = "designer-{$designerName}-all-data-{$dateRangeStr}.zip";
            $zipPath = $tempDir . '/' . $zipFileName;

            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
                foreach ($filePaths as $filePath) {
                    $zip->addFile($filePath, basename($filePath));
                }
                $zip->close();

                // Thông báo thành công
                Notification::make()
                    ->title('Xuất dữ liệu thành công')
                    ->success()
                    ->body("Đã xuất dữ liệu của designer {$designer->name} thành công!")
                    ->duration(5000)
                    ->send();

                // Tải xuống file ZIP
                return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
            } else {
                throw new \Exception('Không thể tạo file ZIP');
            }

        } catch (\Exception $e) {
            Log::error('Error in exportDesignerData: ' . $e->getMessage());

            Notification::make()
                ->title('Lỗi xuất dữ liệu')
                ->danger()
                ->body('Có lỗi xảy ra khi xuất dữ liệu cho designer ' . $designer->name . ': ' . $e->getMessage())
                ->duration(8000)
                ->send();
        }
    }

    public function exportAllDesignersData()
    {
        try {
            // Kiểm tra quyền truy cập
            $user = auth()->user();
            if (!$user || !$user->hasAnyRole(['super_admin', 'User Manager'])) {
                throw new \Exception('Bạn không có quyền thực hiện chức năng này');
            }

            // Lấy tất cả designer trong hệ thống
            $designers = User::whereHas('roles', function ($query) {
                $query->where('name', 'Designer');
            })->get();

            if ($designers->isEmpty()) {
                throw new \Exception('Không tìm thấy designer nào trong hệ thống');
            }

            $dateRange = $this->getDateRangeFromFilters();
            $startDate = $dateRange['from'];
            $endDate = $dateRange['until'];
            $dateRangeStr = $startDate->format('d_m_Y') . '_to_' . $endDate->format('d_m_Y');

            // Tạo thư mục tạm chính
            $tempDir = storage_path('app/temp/exports/all-designers');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            // Danh sách các bảng cần xuất
            $tables = [
                'all_jobs' => 'All Jobs Comprehensive',
                'statistics' => 'Statistics Summary'
            ];

            $designerDirectories = [];
            $processedDesigners = 0;
            $totalDesigners = $designers->count();

            foreach ($designers as $designer) {
                try {
                    $designerName = str_replace(' ', '_', $designer->name);
                    $designerDir = $tempDir . '/designer-' . $designerName;

                    // Tạo thư mục cho designer
                    if (!file_exists($designerDir)) {
                        mkdir($designerDir, 0755, true);
                    }

                    $hasData = false;

                    foreach ($tables as $tableType => $tableName) {
                        try {
                            Log::info("Exporting {$tableType} for designer {$designer->id} ({$designer->name})");

                            $export = new DesignerCsvExport($designer, $startDate, $endDate, $tableType);
                            $fileName = "{$tableType}.csv";
                            $filePath = $designerDir . '/' . $fileName;

                            // Lưu file CSV vào thư mục designer
                            Excel::store($export, 'temp/exports/all-designers/designer-' . $designerName . '/' . $fileName, 'local', \Maatwebsite\Excel\Excel::CSV);

                            if (file_exists($filePath)) {
                                $hasData = true;
                                Log::info("Successfully created {$fileName} for designer {$designer->name}");
                            } else {
                                Log::warning("File {$fileName} not found for designer {$designer->name}");
                            }
                        } catch (\Exception $e) {
                            Log::error("Error exporting {$tableType} for designer {$designer->id}: " . $e->getMessage());
                            Log::error("Stack trace: " . $e->getTraceAsString());
                        }
                    }

                    // Tạo file designer_information.csv
                    try {
                        $designerInfoFileName = 'designer_information.csv';
                        $designerInfoFilePath = $designerDir . '/' . $designerInfoFileName;

                        // Chuẩn bị dữ liệu designer information
                        $designerInfoData = [
                            ['field_name', 'field_value'], // Header
                            ['Designer ID', $designer->id ?? ''],
                            ['Designer Name', $designer->name ?? ''],
                            ['Email', $designer->email ?? ''],
                            ['Created Date', $designer->created_at ? $designer->created_at->format('d/m/Y H:i') : ''],
                            ['Export Period', $startDate->format('d/m/Y') . ' - ' . $endDate->format('d/m/Y')],
                        ];

                        // Tạo file CSV
                        $handle = fopen($designerInfoFilePath, 'w');
                        if ($handle) {
                            foreach ($designerInfoData as $row) {
                                fputcsv($handle, $row);
                            }
                            fclose($handle);

                            if (file_exists($designerInfoFilePath)) {
                                $hasData = true;
                            }
                        }
                    } catch (\Exception $e) {
                        Log::error("Error creating designer_information.csv for designer {$designer->id}: " . $e->getMessage());
                    }

                    if ($hasData) {
                        $designerDirectories[] = $designerDir;
                        $processedDesigners++;
                    }

                } catch (\Exception $e) {
                    Log::error("Error processing designer {$designer->id}: " . $e->getMessage());
                }
            }

            if (empty($designerDirectories)) {
                throw new \Exception('Không có dữ liệu nào được tạo thành công');
            }

            // Tạo file ZIP chính chứa tất cả thư mục designer
            $zipFileName = "all-designers-data-{$dateRangeStr}.zip";
            $zipPath = storage_path('app/temp/exports/' . $zipFileName);

            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
                foreach ($designerDirectories as $designerDir) {
                    $designerDirName = basename($designerDir);

                    // Thêm tất cả file trong thư mục designer vào ZIP
                    $files = glob($designerDir . '/*.csv');
                    foreach ($files as $file) {
                        $relativePath = $designerDirName . '/' . basename($file);
                        $zip->addFile($file, $relativePath);
                    }
                }
                $zip->close();

                // Thông báo thành công
                Notification::make()
                    ->title('Xuất dữ liệu thành công')
                    ->success()
                    ->body("Đã xuất dữ liệu của {$processedDesigners}/{$totalDesigners} designer thành công!")
                    ->duration(8000)
                    ->send();

                // Tải xuống file ZIP
                return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
            } else {
                throw new \Exception('Không thể tạo file ZIP chính');
            }

        } catch (\Exception $e) {
            Log::error('Error in exportAllDesignersData: ' . $e->getMessage());

            Notification::make()
                ->title('Lỗi xuất dữ liệu tất cả designer')
                ->danger()
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->duration(10000)
                ->send();
        }
    }
}
