<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Services\OpenAIService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use App\Models\UserSetting;

class RedesignTool extends Page
{
    use HasPageShield;
    
    public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['super_admin']);
    }



    protected static ?string $navigationIcon = 'heroicon-o-sparkles';
    protected static string $view = 'filament.app.pages.redesign-tool';
    protected static ?string $navigationLabel = 'POD Redesign Tool';
    protected static ?string $navigationGroup = 'Tools';



    // Input properties
    public $originalImageDataUrl = '';
    public $redesignPrompt = '';
    public $genDescPrompt = 'Hãy mô tả thiết kế một cách chi tiết, bao gồm màu sắc, kiể<PERSON> dáng, họa tiết, text và phong cách tổng thể. Nếu có text thì mô tả cả text đó. Nếu hình ảnh là một chiếc áo thun thì bỏ qua việc phân tích áo, tập trung vào thiết kế.';
    public $designDesc = '';
    public $genDesignPrompt = '';
    
    // Output properties
    public $generatedImageUrl = '';
    public $downloadReady = false;
    public $isLoading = false;
    public $isDescribing = false;
    public $isUsingFallback = false;

    // Usage tracking
    public $remainingGenerations = 300;
    const DAILY_GENERATION_LIMIT = 300;
    const USAGE_KEY = 'pod_redesign_usage';

    public function mount()
    {
        $this->updateRemainingGenerations();
        $this->updateGenDesignPrompt();
        
        if ($this->hasReachedDailyLimit() && !$this->isSuperAdmin()) {
            Notification::make()
                ->warning()
                ->title('Đã đạt giới hạn')
                ->body('Bạn đã sử dụng hết 300 lượt hôm nay.')
                ->send();
        }
    }

    public function updatedOriginalImageDataUrl()
    {
        $this->generatedImageUrl = '';
        $this->downloadReady = false;
        $this->designDesc = '';
        $this->updateGenDesignPrompt();
    }

    public function updatedDesignDesc()
    {
        $this->updateGenDesignPrompt();
    }

    public function updateGenDesignPrompt()
    {
        $basePrompt = 'Tạo thiết kế POD trong suốt cho áo thun:
        1. Có nền HOÀN TOÀN TRONG SUỐT (transparent PNG)
2. Phù hợp với phong cách và bảng màu đã chọn
3. Có độ phân giải cao, rõ nét
4. Kích thước phù hợp với vị trí in trên áo
5. Dễ nhìn trên màu áo được chọn
6. Tuân theo xu hướng POD hiện tại
7. Chỉ tạo thiết kế không tạo áo thun, nếu hình ảnh là một chiếc áo, hãy bỏ qua việc phân tích áo, tập trung vào thiết kế.';
        
        if (!empty($this->designDesc)) {
            $this->genDesignPrompt = $basePrompt . ': ' . $this->designDesc;
        } else {
            $this->genDesignPrompt = $basePrompt;
        }
    }

    public function generateDescription()
    {
        if (empty($this->originalImageDataUrl)) {
            Notification::make()
                ->warning()
                ->title('Chưa có ảnh')
                ->body('Vui lòng upload ảnh trước.')
                ->send();
            return;
        }

        if ($this->hasReachedDailyLimit() && !$this->isSuperAdmin()) {
            Notification::make()
                ->warning()
                ->title('Đã đạt giới hạn')
                ->body('Bạn đã hết lượt hôm nay.')
                ->send();
            return;
        }

        $this->isDescribing = true;

        try {
            $openAIService = new OpenAIService();
            $result = $openAIService->generateImageDescription(
                $this->originalImageDataUrl,
                $this->genDescPrompt
            );
            
            if (isset($result['description']) && !empty($result['description'])) {
                $this->designDesc = $result['description'];
                $this->updateGenDesignPrompt();
                
                Notification::make()
                    ->success()
                    ->title('Mô tả đã tạo!')
                    ->body('Mô tả thiết kế đã sẵn sàng.')
                    ->send();

                if (!$this->isSuperAdmin()) {
                    $this->incrementGenerationCount();
                    $this->updateRemainingGenerations();
                }
            } else {
                throw new \Exception('Không thể tạo mô tả');
            }
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Lỗi')
                ->body('Không thể tạo mô tả. Vui lòng thử lại.')
                ->send();
        }

        $this->isDescribing = false;
    }

    public function generateRedesign()
    {
        if ($this->hasReachedDailyLimit() && !$this->isSuperAdmin()) {
            Notification::make()
                ->warning()
                ->title('Đã đạt giới hạn')
                ->body('Bạn đã hết lượt hôm nay.')
                ->send();
            return;
        }

        $this->isLoading = true;
        $this->isUsingFallback = false;

        try {
            $openAIService = new OpenAIService();
            
            // Prompt đơn giản: dùng genDesignPrompt hoặc redesignPrompt
            $promptToUse = !empty($this->genDesignPrompt) ? $this->genDesignPrompt : $this->redesignPrompt;
            
            if (empty($promptToUse)) {
                $promptToUse = 'Tạo thiết kế POD trong suốt cho áo thun';
            }
            
            $result = $openAIService->generatePODDesign(
                $this->originalImageDataUrl,
                $promptToUse
            );
            
            if (isset($result['is_fallback']) && $result['is_fallback']) {
                $this->isUsingFallback = true;
                
                Notification::make()
                    ->danger()
                    ->title('Lỗi tạo thiết kế')
                    ->body($result['error_message'] ?? 'Không thể tạo thiết kế. Vui lòng thử lại.')
                    ->send();
                    
                $this->generatedImageUrl = '';
                $this->downloadReady = false;
            } else {
                $this->generatedImageUrl = $result['image_url'] ?? '';
                $this->downloadReady = !empty($this->generatedImageUrl);

                if ($this->downloadReady) {
                    Notification::make()
                        ->success()
                        ->title('Thiết kế đã tạo!')
                        ->body('Thiết kế POD đã sẵn sàng.')
                        ->send();

                    if (!$this->isSuperAdmin()) {
                        $this->incrementGenerationCount();
                        $this->updateRemainingGenerations();
                    }
                }
            }
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Lỗi hệ thống')
                ->body('Đã xảy ra lỗi. Vui lòng thử lại.')
                ->send();
                
            $this->generatedImageUrl = '';
            $this->downloadReady = false;
        }

        $this->isLoading = false;
    }

    public function downloadDesign()
    {
        if (!$this->downloadReady || empty($this->generatedImageUrl)) {
            Notification::make()
                ->warning()
                ->title('Không có ảnh')
                ->body('Vui lòng tạo thiết kế trước.')
                ->send();
            return;
        }

        $filename = 'pod_design_' . date('Y-m-d_H-i-s') . '.png';
        
        $this->dispatchBrowserEvent('download-image', [
            'url' => $this->generatedImageUrl,
            'filename' => $filename
        ]);

        Notification::make()
            ->success()
            ->title('Đang tải')
            ->body('Thiết kế POD đang được tải xuống.')
            ->send();
    }

    public function isSuperAdmin()
    {
        return Auth::user() && Auth::user()->hasRole('super_admin');
    }

    protected function getGenerationCount()
    {
        $userId = Auth::id();
        if (!$userId) return 0;

        $usageData = UserSetting::get($userId, self::USAGE_KEY, [
            'count' => 0, 
            'date' => Carbon::today()->toDateString()
        ]);

        if ($usageData['date'] !== Carbon::today()->toDateString()) {
            $this->resetUsageCount();
            return 0;
        }

        return $usageData['count'] ?? 0;
    }

    protected function incrementGenerationCount()
    {
        $userId = Auth::id();
        if (!$userId) return;

        $usageData = UserSetting::get($userId, self::USAGE_KEY, [
            'count' => 0,
            'date' => Carbon::today()->toDateString()
        ]);

        if ($usageData['date'] !== Carbon::today()->toDateString()) {
            $usageData = ['count' => 1, 'date' => Carbon::today()->toDateString()];
        } else {
            $usageData['count'] = ($usageData['count'] ?? 0) + 1;
        }

        UserSetting::set($userId, self::USAGE_KEY, $usageData);
    }

    protected function updateRemainingGenerations()
    {
        $count = $this->getGenerationCount();
        $this->remainingGenerations = max(0, self::DAILY_GENERATION_LIMIT - $count);
    }

    protected function hasReachedDailyLimit()
    {
        return $this->getGenerationCount() >= self::DAILY_GENERATION_LIMIT;
    }

    protected function resetUsageCount()
    {
        $userId = Auth::id();
        if (!$userId) return;

        UserSetting::set($userId, self::USAGE_KEY, [
            'count' => 0,
            'date' => Carbon::today()->toDateString()
        ]);
        
        $this->remainingGenerations = self::DAILY_GENERATION_LIMIT;
    }
}
