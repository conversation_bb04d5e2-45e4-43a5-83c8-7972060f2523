<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Services\OpenAIService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;
use App\Models\UserSetting;

class ProductNameGenerator extends Page
{
    use HasPageShield;
    public static function canAccess(): bool
    {
        // Kiểm tra quyền truy cập: admin hoặc User Manager hoặc chính người bán
        return \Illuminate\Support\Facades\Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'Seller']);
    }

    protected static ?string $navigationIcon = 'heroicon-o-sparkles';

    protected static string $view = 'filament.app.pages.product-name-generator';
    protected static ?string $navigationLabel = 'Product Name Generator';
    protected static ?string $navigationGroup = 'Tools';

    public $productDescription = '';
    public $imageDataUrl = '';
    public $generatedName = '';
    public $keywords = '';
    public $hashtags = '';
    public $isLoading = false;
    public $isUsingFallback = false;

    // Khai báo biến mặc định nhưng sẽ được ghi đè bởi dữ liệu từ database
    protected $defaultSystemPrompt = 'Bạn là một chuyên gia đặt tên sản phẩm cho TikTok Shop, tạo ra các tên sản phẩm hấp dẫn, từ khóa và hashtag dựa trên hình ảnh hoặc mô tả sản phẩm được cung cấp. Vui lòng luôn trả lời bằng tiếng Anh, ngay cả khi hướng dẫn bằng tiếng Việt.';
    protected $defaultUserPrompt = 'Hãy tạo thông tin sản phẩm theo format sau:

Product Name: (Tạo một tên sản phẩm hấp dẫn dài 150-175 ký tự, bao gồm các tính năng chính và đối tượng mục tiêu) ,Text trên design là nội dung của sản phẩm, hãy đặt nó ở cụm từ khoá chính đầu tiên, nếu là thiết kế 2 mặt hãy thêm cụm 2 sides

Keywords: (5 từ khóa SEO quan trọng cho sản phẩm này, phân tách bằng dấu phẩy)

Hashtags: (10 hashtag phổ biến liên quan đến sản phẩm này, bao gồm loại sản phẩm, tính năng và xu hướng)

Lưu ý: Vui lòng trả lời bằng tiếng Anh.';

    // Biến lưu trữ giá trị thực tế được lấy từ database
    protected $_systemPrompt = '';
    protected $_userPrompt = '';

    public $showCustomPrompt = true;
    public $remainingGenerations = 300; // Số lần tạo còn lại trong ngày

    // Khóa này cần public để hoạt động với wire:model
    
    public $systemPrompt = '';
    public $userPrompt = '';

    // Số lần tạo tối đa mỗi ngày
    const DAILY_GENERATION_LIMIT = 300;

    // Khóa cài đặt cho prompt
    const SETTING_KEY = 'product_name_prompts';

    // Khóa cài đặt cho số lần sử dụng
    const USAGE_KEY = 'product_name_usage';

    /**
     * Khởi tạo giá trị ban đầu cho $systemPrompt and $userPrompt
     */
    // private function initializePrompts()
    // {
    //     // Áp dụng giá trị mặc định cho public properties
    //     if (empty($this->systemPrompt)) {
    //         $this->systemPrompt = $this->defaultSystemPrompt;
    //     }
    //
    //     if (empty($this->userPrompt)) {
    //         $this->userPrompt = $this->defaultUserPrompt;
    //     }
    // }

    public function mount()
    {
        // Log thông tin người dùng và ID để debug
        $user = Auth::user();
        \Illuminate\Support\Facades\Log::info('Mounting ProductNameGenerator page', [
            'user_id' => $user ? $user->id : 'no-auth',
            'user_email' => $user ? $user->email : 'no-auth',
        ]);

        // Step 1: Check for and fix any corrupted settings
        $this->cleanupCorruptedSettings();

        // Step 2: Try to load from database first
        if (!$this->restorePromptsFromDatabase()) {
            // Step 3: Only set default values if nothing was loaded from database
            $this->systemPrompt = $this->defaultSystemPrompt;
            $this->userPrompt = $this->defaultUserPrompt;
            \Illuminate\Support\Facades\Log::info('Sử dụng giá trị mặc định vì không có dữ liệu từ database');
        }

        // Step 4: Update remaining generations when page is loaded
        $this->updateRemainingGenerations();

        // Step 5: Show notification if user has reached daily limit and is not admin
        if ($this->hasReachedDailyLimit() && !$this->isSuperAdmin()) {
            Notification::make()
                ->warning()
                ->title('Đã đạt giới hạn tạo tiêu đề')
                ->body('Bạn đã sử dụng hết 300 lần tạo tiêu đề trong ngày hôm nay. Vui lòng thử lại vào ngày mai.')
                ->send();
        }

        \Illuminate\Support\Facades\Log::info('Mount complete - prompt values:', [
            'system_prompt' => mb_substr($this->systemPrompt, 0, 50) . '...',
            'user_prompt' => mb_substr($this->userPrompt, 0, 50) . '...',
        ]);
    }

    protected function getUserDailyKey()
    {
        $userId = Auth::id() ?: 0;
        return self::USAGE_KEY;
    }

    protected function getGenerationCount()
    {
        $userId = Auth::id();
        if (!$userId) return 0;

        $usageData = UserSetting::get($userId, self::USAGE_KEY, ['count' => 0, 'date' => Carbon::today()->toDateString()]);

        // Kiểm tra nếu ngày đã thay đổi
        if ($usageData['date'] !== Carbon::today()->toDateString()) {
            // Reset count vì là ngày mới
            $this->resetUsageCount();
            return 0;
        }

        return $usageData['count'] ?? 0;
    }

    protected function incrementGenerationCount()
    {
        $userId = Auth::id();
        if (!$userId) return 0;

        try {
            $usageData = UserSetting::get($userId, self::USAGE_KEY, ['count' => 0, 'date' => Carbon::today()->toDateString()]);

            // Kiểm tra xem có phải ngày mới không
            if ($usageData['date'] !== Carbon::today()->toDateString()) {
                // Reset cho ngày mới
                $usageData = [
                    'count' => 1,
                    'date' => Carbon::today()->toDateString()
                ];
            } else {
                // Tăng count lên 1
                $usageData['count'] = ($usageData['count'] ?? 0) + 1;
            }

            // Lưu vào database
            UserSetting::set($userId, self::USAGE_KEY, $usageData);

            \Illuminate\Support\Facades\Log::info('Đã tăng số lần sử dụng', [
                'user_id' => $userId,
                'new_count' => $usageData['count'],
                'date' => $usageData['date']
            ]);

            return $usageData['count'];
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Lỗi khi tăng số lần sử dụng: ' . $e->getMessage());
            return 0;
        }
    }

    protected function updateRemainingGenerations()
    {
        $count = $this->getGenerationCount();
        $this->remainingGenerations = max(0, self::DAILY_GENERATION_LIMIT - $count);

        \Illuminate\Support\Facades\Log::info('Đã cập nhật số lần sử dụng còn lại', [
            'user_id' => Auth::id() ?: 'no-auth',
            'count' => $count,
            'remaining' => $this->remainingGenerations,
        ]);
    }

    protected function hasReachedDailyLimit()
    {
        return $this->getGenerationCount() >= self::DAILY_GENERATION_LIMIT;
    }

    public function isSuperAdmin()
    {
        // Check if the current user has super_admin role
        // Modify this according to your actual role detection mechanism
        return Auth::user() && Auth::user()->hasRole('super_admin');
    }

    public function updatedImageDataUrl()
    {
        // Xóa kết quả cũ khi hình ảnh được cập nhật, nhưng giữ nguyên mô tả sản phẩm
        $this->generatedName = '';
        $this->keywords = '';
        $this->hashtags = '';
    }

    public function updatedProductDescription()
    {
        // Xóa kết quả cũ khi mô tả được cập nhật
        $this->generatedName = '';
        $this->keywords = '';
        $this->hashtags = '';
    }

    public function generateName()
    {
        $this->isLoading = true;

        // Kiểm tra giới hạn tạo tiêu đề hàng ngày
        if ($this->hasReachedDailyLimit() && !$this->isSuperAdmin()) {
            $this->isLoading = false;

            Notification::make()
                ->warning()
                ->title('Đã đạt giới hạn tạo tiêu đề')
                ->body('Bạn đã sử dụng hết 300 lần tạo tiêu đề trong ngày hôm nay. Vui lòng thử lại vào ngày mai.')
                ->persistent()
                ->send();

            return;
        }

        $this->isUsingFallback = false;

        $openAIService = new OpenAIService();
        $result = $openAIService->generateProductNameWithKeywords(
            $this->imageDataUrl,
            $this->productDescription,
            $this->systemPrompt,
            $this->userPrompt
        );

        // Kiểm tra nếu có lỗi API
        if (isset($result['is_fallback']) && $result['is_fallback']) {
            $this->isUsingFallback = true;

            // Hiển thị thông báo lỗi cho tất cả người dùng, không chỉ super_admin
            $errorTitle = 'Lỗi kết nối API OpenAI';
            $errorBody = 'Không thể kết nối đến API OpenAI. Vui lòng thử lại sau hoặc liên hệ với quản trị viên.';

            // Nếu có mã lỗi cụ thể, hiển thị thông báo chi tiết hơn
            if (isset($result['error_code'])) {
                if ($result['error_code'] === 'API_CONNECTION_ERROR') {
                    $errorBody = 'Không thể kết nối đến API OpenAI. Vui lòng kiểm tra kết nối mạng và thử lại sau.';
                }

                \Illuminate\Support\Facades\Log::error('OpenAI API Error', [
                    'error_code' => $result['error_code'],
                    'user_id' => Auth::id() ?: 'no-auth'
                ]);
            }

            Notification::make()
                ->danger() // Thay đổi từ warning sang danger để nhấn mạnh đây là lỗi
                ->title($errorTitle)
                ->body($errorBody)
                ->persistent()
                ->send();

            // Đặt các giá trị kết quả thành thông báo lỗi
            $this->generatedName = $result['product_name'] ?? 'Lỗi kết nối API OpenAI';
            $this->keywords = $result['keywords'] ?? 'Lỗi kết nối API';
            $this->hashtags = $result['hashtags'] ?? 'Lỗi kết nối API';

            $this->isLoading = false;
            return;
        }

        // Lấy kết quả từ API (chỉ khi không có lỗi)
        $productName = $result['product_name'] ?? 'Không tạo được tên.';
        $this->keywords = $result['keywords'] ?? '';
        $this->hashtags = $result['hashtags'] ?? '';

        // Xử lý tiêu đề sản phẩm: loại bỏ ngoặc kép
        if ($productName !== 'Không tạo được tên.') {
            // Loại bỏ tất cả ngoặc kép trong chuỗi
            $productName = str_replace('"', '', $productName);

            \Illuminate\Support\Facades\Log::info('Đã xử lý ngoặc kép trong tiêu đề', [
                'original' => $result['product_name'],
                'processed' => $productName
            ]);
        }

        $this->generatedName = $productName;

        // Tăng số lần tạo đã sử dụng (chỉ khi không phải admin và tạo thành công)
        // Lưu ý: Chỉ tăng khi không có lỗi và kết quả hợp lệ
        if (!empty($this->generatedName) && $this->generatedName !== 'Không tạo được tên.' && !$this->isUsingFallback) {
            if (!$this->isSuperAdmin()) {
                $this->incrementGenerationCount();
                $this->updateRemainingGenerations();
            }
        }

        $this->isLoading = false;
    }

    /**
     * Bật/tắt hiển thị cài đặt prompt tùy chỉnh
     */
    public function toggleCustomPrompt()
    {
        $this->showCustomPrompt = !$this->showCustomPrompt;
    }

    /**
     * Khi systemPrompt được cập nhật
     */
    public function updatedSystemPrompt($value)
    {
        \Illuminate\Support\Facades\Log::info('systemPrompt được cập nhật', [
            'new_value' => mb_substr($value, 0, 50) . '...',
        ]);
        // Không lưu tự động, chờ người dùng nhấn nút Lưu
    }

    /**
     * Khi userPrompt được cập nhật
     */
    public function updatedUserPrompt($value)
    {
        \Illuminate\Support\Facades\Log::info('userPrompt được cập nhật', [
            'new_value' => mb_substr($value, 0, 50) . '...',
        ]);
        // Không lưu tự động, chờ người dùng nhấn nút Lưu
    }

    /**
     * Lưu tùy chỉnh prompt vào database
     */
    public function saveCustomPrompt()
    {
        // Ghi log để debug
        \Illuminate\Support\Facades\Log::info('saveCustomPrompt method called');

        try {
            // Lấy ID của người dùng hiện tại
            $userId = Auth::id();
            if (!$userId) {
                throw new \Exception('Không thể xác định ID người dùng');
            }

            // Kiểm tra dữ liệu trước khi lưu
            if (empty($this->systemPrompt) || empty($this->userPrompt)) {
                throw new \Exception('Prompt không thể để trống');
            }

            // Đảm bảo dữ liệu có định dạng đúng
            if (!$this->isValidPrompt($this->systemPrompt)) {
                throw new \Exception('System prompt không hợp lệ');
            }

            if (!$this->isValidPrompt($this->userPrompt)) {
                throw new \Exception('User prompt không hợp lệ');
            }

            // Log prompts trước khi lưu
            \Illuminate\Support\Facades\Log::info('Giá trị sẽ lưu vào database:', [
                'user_id' => $userId,
                'system_prompt' => mb_substr($this->systemPrompt, 0, 100) . '...',
                'user_prompt' => mb_substr($this->userPrompt, 0, 100) . '...',
            ]);

            // Xóa bản ghi cũ (nếu có) để tránh lỗi
            UserSetting::where('user_id', $userId)
                ->where('key', self::SETTING_KEY)
                ->delete();

            \Illuminate\Support\Facades\Log::info('Đã xóa bản ghi cũ để tránh xung đột');

            // Tạo mảng dữ liệu cần lưu
            $promptData = [
                'system_prompt' => $this->systemPrompt,
                'user_prompt' => $this->userPrompt,
                'updated_at' => now()->toDateTimeString(),
            ];

            // Lưu vào database
            $result = UserSetting::set($userId, self::SETTING_KEY, $promptData);

            // Kiểm tra kết quả
            if (!$result) {
                throw new \Exception('Không thể lưu dữ liệu vào database');
            }

            // Xác minh dữ liệu đã được lưu
            $savedData = UserSetting::get($userId, self::SETTING_KEY);
            \Illuminate\Support\Facades\Log::info('Dữ liệu sau khi lưu:', [
                'data_type' => gettype($savedData),
                'has_data' => !empty($savedData) ? 'yes' : 'no',
                'system_prompt_length' => isset($savedData['system_prompt']) ? strlen($savedData['system_prompt']) : 0,
                'user_prompt_length' => isset($savedData['user_prompt']) ? strlen($savedData['user_prompt']) : 0,
                'matches_system_prompt' => (isset($savedData['system_prompt']) && $savedData['system_prompt'] === $this->systemPrompt) ? 'yes' : 'no',
                'matches_user_prompt' => (isset($savedData['user_prompt']) && $savedData['user_prompt'] === $this->userPrompt) ? 'yes' : 'no',
            ]);

            // Ghi log thành công
            \Illuminate\Support\Facades\Log::info("Đã lưu tùy chỉnh prompt cho seller #{$userId} vào database");

            // Hiển thị thông báo
            Notification::make()
                ->success()
                ->title('Đã lưu tùy chỉnh')
                ->body('Các tùy chỉnh prompt đã được lưu thành công!')
                ->send();

        } catch (\Exception $e) {
            // Ghi log lỗi
            \Illuminate\Support\Facades\Log::error('Error saving prompts: ' . $e->getMessage(), [
                'exception' => $e,
                'user_id' => Auth::id() ?: 'no-auth',
                'trace' => $e->getTraceAsString()
            ]);

            // Hiển thị thông báo lỗi
            Notification::make()
                ->danger()
                ->title('Lỗi khi lưu')
                ->body('Không thể lưu tùy chỉnh: ' . $e->getMessage())
                ->send();
        }
    }

    /**
     * Khôi phục prompts từ database khi trang được tải
     * @return bool Trả về true nếu đã tải được dữ liệu từ database
     */
    protected function restorePromptsFromDatabase()
    {
        // Lấy ID người dùng
        $userId = Auth::id();
        if (!$userId) {
            \Illuminate\Support\Facades\Log::info('Không thể khôi phục prompt: người dùng chưa đăng nhập');
            return false;
        }

        // Ghi log
        \Illuminate\Support\Facades\Log::info('Đang khôi phục prompt từ database', [
            'user_id' => $userId,
            'setting_key' => self::SETTING_KEY,
        ]);

        try {
            // Truy vấn trực tiếp để kiểm tra xem bản ghi có tồn tại không
            $settingRecord = UserSetting::where('user_id', $userId)
                ->where('key', self::SETTING_KEY)
                ->first();

            if (!$settingRecord) {
                \Illuminate\Support\Facades\Log::info('Không tìm thấy bản ghi cài đặt trong database');
                return false;
            }

            \Illuminate\Support\Facades\Log::info('Đã tìm thấy bản ghi: ID=' . $settingRecord->id . ', raw value=' . mb_substr($settingRecord->value, 0, 50) . '...');

            // Lấy dữ liệu từ database
            $promptData = UserSetting::get($userId, self::SETTING_KEY);

            \Illuminate\Support\Facades\Log::info('Dữ liệu nhận được từ database:', [
                'data_type' => gettype($promptData),
                'is_null' => is_null($promptData) ? 'yes' : 'no',
                'is_array' => is_array($promptData) ? 'yes' : 'no',
                'has_system_prompt' => (is_array($promptData) && isset($promptData['system_prompt'])) ? 'yes' : 'no',
                'has_user_prompt' => (is_array($promptData) && isset($promptData['user_prompt'])) ? 'yes' : 'no',
            ]);

            // Kiểm tra nếu có dữ liệu từ database
            if (!empty($promptData) && is_array($promptData)) {
                $dataLoaded = false;

                // Khôi phục system prompt
                if (isset($promptData['system_prompt']) && $this->isValidPrompt($promptData['system_prompt'])) {
                    // Lưu vào biến public để wire:model có thể truy cập
                    $this->systemPrompt = $promptData['system_prompt'];
                    \Illuminate\Support\Facades\Log::info('Đã khôi phục system prompt từ database', [
                        'value' => mb_substr($this->systemPrompt, 0, 50) . '...',
                    ]);
                    $dataLoaded = true;
                } else {
                    \Illuminate\Support\Facades\Log::warning('system_prompt không hợp lệ hoặc thiếu');
                }

                // Khôi phục user prompt
                if (isset($promptData['user_prompt']) && $this->isValidPrompt($promptData['user_prompt'])) {
                    // Lưu vào biến public để wire:model có thể truy cập
                    $this->userPrompt = $promptData['user_prompt'];
                    \Illuminate\Support\Facades\Log::info('Đã khôi phục user prompt từ database', [
                        'value' => mb_substr($this->userPrompt, 0, 50) . '...',
                    ]);
                    $dataLoaded = true;
                } else {
                    \Illuminate\Support\Facades\Log::warning('user_prompt không hợp lệ hoặc thiếu');
                }

                \Illuminate\Support\Facades\Log::info('Khôi phục hoàn tất, lần cập nhật cuối: ' . ($promptData['updated_at'] ?? 'không xác định') . ', Kết quả: ' . ($dataLoaded ? 'Thành công' : 'Không có dữ liệu hợp lệ'));
                return $dataLoaded;
            } else {
                \Illuminate\Support\Facades\Log::info('Không tìm thấy dữ liệu tùy chỉnh trong database, sử dụng giá trị mặc định');
                return false;
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Lỗi khi khôi phục prompt từ database: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Khôi phục cài đặt prompt mặc định và xóa database
     */
    public function resetPromptDefaults()
    {
        // Ghi log
        \Illuminate\Support\Facades\Log::info('Đang khôi phục prompt về mặc định');

        $this->systemPrompt = $this->defaultSystemPrompt;
        $this->userPrompt = $this->defaultUserPrompt;

        try {
            // Lấy ID người dùng
            $userId = Auth::id();
            if ($userId) {
                // Xóa cài đặt từ database
                UserSetting::where('user_id', $userId)
                    ->where('key', self::SETTING_KEY)
                    ->delete();

                // Ghi log
                \Illuminate\Support\Facades\Log::info('Đã xóa dữ liệu tùy chỉnh trong database');
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Lỗi khi xóa dữ liệu trong database: ' . $e->getMessage());
        }

        Notification::make()
            ->success()
            ->title('Đã khôi phục')
            ->body('Đã khôi phục cài đặt prompt mặc định.')
            ->send();
    }

    /**
     * Kiểm tra xem prompt có hợp lệ không
     */
    protected function isValidPrompt($prompt)
    {
        // Phải là chuỗi
        if (!is_string($prompt)) {
            \Illuminate\Support\Facades\Log::warning('Prompt không hợp lệ: không phải là chuỗi', [
                'type' => gettype($prompt)
            ]);
            return false;
        }

        // Phải có độ dài hợp lý
        if (strlen($prompt) < 10) {
            \Illuminate\Support\Facades\Log::warning('Prompt không hợp lệ: quá ngắn', [
                'length' => strlen($prompt),
                'content' => $prompt
            ]);
            return false;
        }

        if (strlen($prompt) > 10000) {
            \Illuminate\Support\Facades\Log::warning('Prompt không hợp lệ: quá dài', [
                'length' => strlen($prompt)
            ]);
            return false;
        }

        // Log thành công
        \Illuminate\Support\Facades\Log::debug('Prompt hợp lệ', [
            'length' => strlen($prompt),
            'preview' => mb_substr($prompt, 0, 30) . '...'
        ]);

        return true;
    }

    /**
     * Reset số lần tạo đã sử dụng
     */
    public function resetUsageCount()
    {
        $userId = Auth::id();
        if (!$userId) return;

        try {
            $usageData = [
                'count' => 0,
                'date' => Carbon::today()->toDateString()
            ];

            // Lưu vào database
            UserSetting::set($userId, self::USAGE_KEY, $usageData);

            // Cập nhật số lượt còn lại
            $this->remainingGenerations = self::DAILY_GENERATION_LIMIT;

            \Illuminate\Support\Facades\Log::info('Đã reset số lần sử dụng', [
                'user_id' => $userId,
                'date' => $usageData['date']
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Lỗi khi reset số lần sử dụng: ' . $e->getMessage());
        }
    }

    /**
     * Kiểm tra và sửa chữa cài đặt bị hỏng trong database
     */
    protected function cleanupCorruptedSettings()
    {
        $userId = Auth::id();
        if (!$userId) return;

        try {
            // Lấy dữ liệu cài đặt hiện tại
            $promptData = UserSetting::get($userId, self::SETTING_KEY);

            // Kiểm tra nếu dữ liệu tồn tại nhưng bị hỏng (thiếu user_prompt hoặc có user_prompt rỗng)
            if (is_array($promptData) &&
                (
                    !isset($promptData['user_prompt']) ||
                    (isset($promptData['user_prompt']) && empty($promptData['user_prompt']))
                )) {

                \Illuminate\Support\Facades\Log::warning('Phát hiện cài đặt bị hỏng, thiếu hoặc có user_prompt rỗng', [
                    'user_id' => $userId,
                    'prompt_data' => $promptData,
                ]);

                // Đặt lại về mặc định bằng cách xóa bản ghi
                UserSetting::where('user_id', $userId)
                    ->where('key', self::SETTING_KEY)
                    ->delete();

                \Illuminate\Support\Facades\Log::info('Đã xóa cài đặt bị hỏng để khôi phục về mặc định');

                // Không cần thông báo cho người dùng vì sẽ tự động khôi phục
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Lỗi khi kiểm tra cài đặt: ' . $e->getMessage());
        }
    }
}
