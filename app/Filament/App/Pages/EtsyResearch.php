<?php

namespace App\Filament\App\Pages;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasPageShield;
use Filament\Pages\Page;

class EtsyResearch extends Page
{

    protected static bool $shouldRegisterNavigation = false;

    public static function getNavigationGroup(): ?string
    {
        return null;
    }

    public static function getNavigationIcon(): ?string
    {
        return null;
    }

    
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.app.pages.etsy-research';

    use HasPageShield;
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin','Seller']) ;
    }


    protected static ?string $navigationGroup = 'Spy Idea';

    public $page_view;

    public function mount()
    {
        $this->page_view = request('page_view', 'etsy-research');
    }
   
}
