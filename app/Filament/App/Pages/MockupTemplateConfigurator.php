<?php

namespace App\Filament\App\Pages;

use App\Enums\ColorMap;
use App\Models\MockupTemplateConfig;
use App\Models\MockupTemplateGroup;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\View;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\Facades\Storage;

class MockupTemplateConfigurator extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-wrench-screwdriver';
    protected static ?string $title = 'Mockup Template Configurator';
    protected static string $view = 'filament.app.pages.mockup-template-configurator';
    protected static bool $shouldRegisterNavigation = false;

    public ?MockupTemplateGroup $group = null;
    public $configData = [];
    public bool $previewingBack = false;
    public bool $isEditing = false;
    public ?MockupTemplateConfig $editingTemplate = null;

    public function mount(): void
    {
        $groupId = request()->query('groupId');
        
        if (!$groupId) {
            redirect()->route('filament.app.resources.mockup-template-groups.index');
            return;
        }

        $this->group = MockupTemplateGroup::findOrFail($groupId);
        
        // Set default values
        $this->configData = [
            'design_position_x' => 0,
            'design_position_y' => 0,
            'design_width' => 200,
            'design_height' => 200,
            'back_design_position_x' => 0,
            'back_design_position_y' => 0,
            'back_design_width' => 200,
            'back_design_height' => 200,
        ];

        $this->form->fill();
    }

    public function editTemplate(MockupTemplateConfig $template): void
    {
        $this->isEditing = true;
        $this->editingTemplate = $template;
        
        $this->configData = [
            'name' => $template->name,
            'apparel_type' => $template->apparel_type,
            'color' => $template->color,
            'apparel_image' => $template->apparel_image,
            'has_back_view' => $template->has_back_view,
            'design_position_x' => $template->design_position_x ?? 0,
            'design_position_y' => $template->design_position_y ?? 0,
            'design_width' => $template->design_width ?? 200,
            'design_height' => $template->design_height ?? 200,
            'back_design_position_x' => $template->back_design_position_x ?? 0,
            'back_design_position_y' => $template->back_design_position_y ?? 0,
            'back_design_width' => $template->back_design_width ?? 200,
            'back_design_height' => $template->back_design_height ?? 200,
            'status' => $template->status,
            'design_position_data' => [
                'design_position_x' => $template->design_position_x ?? 0,
                'design_position_y' => $template->design_position_y ?? 0,
                'design_width' => $template->design_width ?? 200,
                'design_height' => $template->design_height ?? 200,
                'back_design_position_x' => $template->back_design_position_x ?? 0,
                'back_design_position_y' => $template->back_design_position_y ?? 0,
                'back_design_width' => $template->back_design_width ?? 200,
                'back_design_height' => $template->back_design_height ?? 200,
            ]
        ];

        $this->form->fill($this->configData);
        $this->updatePreview();
    }

    public function deleteTemplate(MockupTemplateConfig $template): void
    {
        try {
            DB::beginTransaction();

            // Delete the image file if it exists
            if ($template->apparel_image && Storage::exists($template->apparel_image)) {
                Storage::delete($template->apparel_image);
            }

            $template->delete();

            DB::commit();

            Notification::make()
                ->success()
                ->title('Template deleted successfully')
                ->send();

        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title('Error deleting template')
                ->body('Please try again or contact support if the problem persists.')
                ->send();
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema($this->getFormSchema());
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make('Template Information')
                ->schema([
                    Grid::make(2)
                        ->schema([
                            TextInput::make('configData.name')
                                ->label('Template Name')
                                ->required()
                                ->maxLength(255),

                            FileUpload::make('configData.apparel_image')
                                ->label('Apparel Image')
                                ->image()
                                ->disk('s3')
                                ->directory('mockup')
                                ->visibility('public')
                                ->imageResizeMode('contain')
                                ->imageResizeTargetWidth('1200')
                                ->imageResizeTargetHeight('1200')
                                ->required(),

                            Toggle::make('configData.has_back_view')
                                ->label('Has Back View')
                                ->reactive(),

                            Select::make('configData.color')
                                ->label('Apparel Color')
                                ->options(array_combine(
                                    array_keys(ColorMap::toArray()),
                                    array_keys(ColorMap::toArray())
                                ))
                                ->searchable()
                                ->required(),

                            Select::make('configData.status')
                                ->label('Status')
                                ->options(MockupTemplateGroup::getStatuses())
                                ->default('draft')
                                ->required(),

                            // Hidden fields for design position
                            Hidden::make('configData.design_position_x'),
                            Hidden::make('configData.design_position_y'),
                            Hidden::make('configData.design_width'),
                            Hidden::make('configData.design_height'),
                            Hidden::make('configData.back_design_position_x'),
                            Hidden::make('configData.back_design_position_y'),
                            Hidden::make('configData.back_design_width'),
                            Hidden::make('configData.back_design_height'),
                        ]),
                ]),

            Section::make('Design Position')
                ->schema([
                    View::make('filament.app.pages.components.design-position-editor')
                        ->statePath('configData'),
                ])
        ];
    }

    public function save(): void
    {
        try {
            if (empty($this->configData['apparel_image'])) {
                Notification::make()
                    ->warning()
                    ->title('Missing Image')
                    ->body('Please upload an apparel image before saving')
                    ->duration(5000)
                    ->persistent()
                    ->send();
                return;
            }

            DB::beginTransaction();

            // Xử lý apparel_image
            $apparelImage = $this->configData['apparel_image'];
            if ($apparelImage instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                $apparelImage = $apparelImage->store('mockup', 's3');
            } elseif (is_array($apparelImage)) {
                $tempFile = array_values($apparelImage)[0];
                if ($tempFile instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                    $apparelImage = $tempFile->store('mockup', 's3');
                }
            }

            $templateData = [
                'name' => $this->configData['name'],
                'apparel_image' => $apparelImage,
                'has_back_view' => $this->configData['has_back_view'],
                'color' => $this->configData['color'],
                'status' => $this->configData['status'],
                'design_position_x' => $this->configData['design_position_x'] ?? 0,
                'design_position_y' => $this->configData['design_position_y'] ?? 0,
                'design_width' => $this->configData['design_width'] ?? 200,
                'design_height' => $this->configData['design_height'] ?? 200,
                'back_design_position_x' => $this->configData['back_design_position_x'] ?? 0,
                'back_design_position_y' => $this->configData['back_design_position_y'] ?? 0,
                'back_design_width' => $this->configData['back_design_width'] ?? 200,
                'back_design_height' => $this->configData['back_design_height'] ?? 200,
            ];

            if ($this->isEditing && $this->editingTemplate) {
                // Delete old image if new one is uploaded
                if ($this->editingTemplate->apparel_image !== $templateData['apparel_image']) {
                    Storage::disk('s3')->delete($this->editingTemplate->apparel_image);
                }
                
                $this->editingTemplate->update($templateData);
                $template = $this->editingTemplate;
            } else {
                $template = $this->group->configs()->create($templateData);
            }

            DB::commit();

            Notification::make()
                ->success()
                ->title($this->isEditing ? 'Template updated successfully' : 'Template added successfully')
                ->send();

            $this->form->fill();
            $this->isEditing = false;
            $this->editingTemplate = null;
            $this->configData = [];

        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title($this->isEditing ? 'Error updating template' : 'Error adding template')
                ->body($e->getMessage())
                ->send();

            \Log::error('Save Error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'configData' => $this->configData
            ]);
        }
    }

    public function getViewData(): array
    {
        return [
            'group' => $this->group,
            'existingTemplates' => $this->group->configs()
                ->orderBy('created_at', 'desc')
                ->get(),
        ];
    }

    public function togglePreviewView(): void
    {
        $this->previewingBack = !$this->previewingBack;
        $this->updatePreview();
    }

    public function updatePreview(): void
    {
        if (empty($this->configData['apparel_image'])) {
            return;
        }

        // Get image URL based on the current state
        $imageUrl = $this->getImageUrl($this->configData['apparel_image']);

        $this->dispatch('preview-updated', [
            'image' => $imageUrl,
            'isBack' => $this->previewingBack,
            'design_position_x' => $this->configData['design_position_x'] ?? 0,
            'design_position_y' => $this->configData['design_position_y'] ?? 0,
            'design_width' => $this->configData['design_width'] ?? 200,
            'design_height' => $this->configData['design_height'] ?? 200,
            'back_design_position_x' => $this->configData['back_design_position_x'] ?? 0,
            'back_design_position_y' => $this->configData['back_design_position_y'] ?? 0,
            'back_design_width' => $this->configData['back_design_width'] ?? 200,
            'back_design_height' => $this->configData['back_design_height'] ?? 200,
        ]);
    }

    protected function getImageUrl($image): ?string
    {
        if (empty($image)) {
            return null;
        }

        if (is_string($image)) {
            return Storage::disk('s3')->url($image);
        }

        // Xử lý temporary file từ Filament upload
        if (is_object($image) && method_exists($image, 'getFilename')) {
            return Storage::disk('s3')->url($image->getFilename());
        }

        return null;
    }

    public function updated($property): void
    {
        // Trigger preview update when apparel_image changes
        if ($property === 'configData.apparel_image') {
            $this->updatePreview();
        }
    }
}