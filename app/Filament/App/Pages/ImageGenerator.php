<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Services\IdeogramService;
use App\Services\ImageStorageService;
use App\Services\OpenAIService;
use App\Jobs\ProcessImageUpload;
use App\Models\GeneratedImage;
use App\Models\Document;
use App\Models\IdeogramAccount;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Livewire\Attributes\On;
use Livewire\Attributes\Computed;

class ImageGenerator extends Page implements HasForms
{
    use HasPageShield, InteractsWithForms;

    protected $listeners = ['file-uploaded' => 'handleFileUploaded'];

    public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager', 'AI Manager', 'Seller' ]);
    }

    protected static ?string $navigationIcon = 'heroicon-o-photo';
    protected static string $view = 'filament.app.pages.image-generator';
    protected static ?string $navigationLabel = 'Tạo Ảnh AI';
    protected static ?string $title = 'Tạo Ảnh AI';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationGroup = 'Tools';

    protected ?string $heading = 'Tạo Ảnh AI';

    // Form properties
    public ?array $data = [];

    // Output properties
    public array $streamMessages = [];
    public array $generatedImages = [];
    public array $rawResponses = [];
    public bool $isGenerating = false;
    public string $currentStatus = '';
    public bool $showRawData = false;
    public $userImages = null; // Danh sách ảnh đã tạo của user
    public int $currentPage = 1;
    public int $perPage = 12; // 12 ảnh mỗi trang (2 dòng x 6 ảnh)
    public int $totalPages = 1;
    public int $totalImages = 0;

    // Daily limit properties
    public int $dailyLimit = 100; // Default fallback
    public int $todayCount = 0;
    public int $remainingCount = 0;

    // Prompt generation properties
    public array $generatedPromptData = [];
    public bool $isGeneratingPrompt = false;

    // Redesign properties
    public string $uploadedImageId = '';
    public bool $isRedesigning = false;
    public $redesignImageFile = null;
    public string $pastedImageDataUrl = ''; // Thêm property cho ảnh paste
    public bool $isProcessingPastedImage = false; // Thêm property cho loading state
    public string $imageSourceType = 'upload'; // 'upload' hoặc 'paste' - tùy chọn nguồn ảnh

    // Image analysis properties
    public bool $isAnalyzingImage = false; // Loading state cho phân tích ảnh
    public string $imageAnalysisResult = ''; // Kết quả phân tích ảnh
    public string $analysisPrompt = 'Hãy mô tả thiết kế một cách cực kì chi tiết (khoảng 20 dòng), bao gồm màu sắc, kiểu dáng, họa tiết, text và phong cách tổng thể. Nếu có text thì mô tả cả text đó. Nếu hình ảnh là một chiếc áo thun thì bỏ qua việc phân tích áo, tập trung vào thiết kế. Trả lời bằng Tiếng Anh';
    public string $analysisProvider = 'openai'; // 'openai' hoặc 'ideogram'
    public string  $basePromt = 'Create a transparent POD (Print on Demand) design for a t-shirt:1. The background must be COMPLETELY TRANSPARENT (transparent PNG).2. The design should match the chosen style and color palette.3. The image must be high-resolution and sharp.4. The size should be appropriate for t-shirt print placement.5. The design must be clearly visible on the selected t-shirt color.6. The artwork should follow current POD design trends.7. Only create the design, do not generate a t-shirt. If the image includes a t-shirt, ignore the t-shirt and focus solely on the design.';
                  

    public function mount(): void
    {
        $this->form->fill([
            'prompt' => '',
            'num_images' => 4,
        ]); 

        // Reset pagination
        $this->currentPage = 1;
        $this->loadUserImages();

        // Initialize prompt generation data
        $this->generatedPromptData = [
            'product_name' => '',
            'keywords' => '',
            'hashtags' => '',
            'full_prompt' => ''
        ];

        // Load analysis prompt from database
        $this->loadAnalysisPromptFromDatabase();

        // Load daily limit information
        $this->loadDailyLimitInfo();
    }

    /**
     * Load analysis prompt from database
     */
    private function loadAnalysisPromptFromDatabase(): void
    {
        try {
            $document = Document::where('key', 'image-analysis-prompt')
                ->where('status', 'published')
                ->first();

            if ($document && !empty($document->content)) {
                $this->analysisPrompt = $document->content;
                \Log::info('Loaded analysis prompt from database', ['key' => 'image-analysis-prompt']);
            } else {
                // Nếu chưa có trong database, tạo mới với prompt mặc định
                $this->createAnalysisPromptInDatabase();
            }
        } catch (\Exception $e) {
            \Log::error('Failed to load analysis prompt from database: ' . $e->getMessage());
            // Sử dụng prompt mặc định nếu có lỗi
        }
    }

    /**
     * Create analysis prompt in database với prompt mặc định
     */
    private function createAnalysisPromptInDatabase(): void
    {
        try {
            Document::create([
                'title' => 'Prompt Phân Tích Ảnh cho AI Image Generator',
                'key' => 'image-analysis-prompt',
                'content' => $this->analysisPrompt,
                'category' => 'AI Prompts',
                'tags' => ['ai', 'image-analysis', 'prompt', 'image-generator'],
                'status' => 'published',
                'is_embedded' => false,
                'embedding_status' => 'pending',
                'word_count' => str_word_count($this->analysisPrompt),
                'created_by' => Auth::id() ?? 1,
                'updated_by' => Auth::id() ?? 1,
            ]);

            \Log::info('Created image analysis prompt in database with key: image-analysis-prompt');
        } catch (\Exception $e) {
            \Log::error('Failed to create analysis prompt in database: ' . $e->getMessage());
        }
    }

    /**
     * Get daily limit from IdeogramAccount or fallback to default
     */
    private function getDailyLimitFromAccount(): int
    {
        try {
            // Lấy account mặc định hoặc account tốt nhất
            $account = IdeogramAccount::getDefaultOrBest();

            if ($account && $account->daily_limit > 0) {
                return $account->daily_limit;
            }

            // Fallback: lấy giá trị cao nhất từ các account active
            $maxLimit = IdeogramAccount::active()
                ->max('daily_limit');

            return $maxLimit > 0 ? $maxLimit : 100; // Default fallback

        } catch (\Exception $e) {
            \Log::warning('Failed to get daily limit from IdeogramAccount: ' . $e->getMessage());
            return 100; // Default fallback
        }
    }

    /**
     * Load daily limit information
     */
    private function loadDailyLimitInfo(): void
    {
        $userId = Auth::id();

        // Lấy giới hạn từ IdeogramAccount
        $this->dailyLimit = $this->getDailyLimitFromAccount();

        $this->todayCount = GeneratedImage::getTodayCount($userId);
        $this->remainingCount = GeneratedImage::getRemainingCount($userId, $this->dailyLimit);
    }

    /**
     * Refresh daily limit information (public method for Livewire)
     */
    public function refreshDailyLimitInfo(): void
    {
        $this->loadDailyLimitInfo();
    }

    /**
     * Get current daily limit (public method for debugging)
     */
    public function getCurrentDailyLimit(): int
    {
        return $this->getDailyLimitFromAccount();
    }

    public function loadUserImages(): void
    {
        $query = GeneratedImage::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc');

        // Tính tổng số ảnh và số trang
        $this->totalImages = $query->count();
        $this->totalPages = max(1, ceil($this->totalImages / $this->perPage));

        // Đảm bảo currentPage không vượt quá totalPages
        if ($this->currentPage > $this->totalPages) {
            $this->currentPage = $this->totalPages;
        }

        // Lấy ảnh cho trang hiện tại
        $offset = ($this->currentPage - 1) * $this->perPage;
        $this->userImages = $query->skip($offset)
            ->take($this->perPage)
            ->get();

        // Refresh daily limit info
        $this->loadDailyLimitInfo();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Daily Limit Information Section
                Section::make('📊 Thông tin sử dụng hàng ngày')
                    ->description('Giới hạn tạo ảnh: ' . $this->dailyLimit . ' ảnh/ngày')
                    ->schema([
                        \Filament\Forms\Components\Placeholder::make('daily_limit_info')
                            ->label('')
                            ->content(function () {
                                $percentage = $this->dailyLimit > 0 ? ($this->todayCount / $this->dailyLimit) * 100 : 0;
                                $progressColor = $percentage >= 90 ? 'bg-red-500' : ($percentage >= 70 ? 'bg-yellow-500' : 'bg-green-500');

                                return new \Illuminate\Support\HtmlString('
                                    <div class="space-y-3">
                                        <div class="flex justify-between items-center">
                                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Đã sử dụng: ' . $this->todayCount . '/' . $this->dailyLimit . ' ảnh
                                            </span>
                                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Còn lại: ' . $this->remainingCount . ' ảnh
                                            </span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                            <div class="' . $progressColor . ' h-2.5 rounded-full transition-all duration-300"
                                                 style="width: ' . min(100, $percentage) . '%"></div>
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
                                            ' . ($this->remainingCount <= 0 ?
                                                '⚠️ Bạn đã đạt giới hạn hàng ngày. Vui lòng thử lại vào ngày mai.' :
                                                'Bạn có thể tạo thêm ' . $this->remainingCount . ' ảnh hôm nay.'
                                            ) . '
                                        </div>
                                    </div>
                                ');
                            })
                    ])
                    ->collapsible()
                    ->collapsed(false),

                // Prompt Generator Section
                // Section::make('🎯 Tạo Prompt Thông Minh')
                //     ->description('AI sẽ tạo prompt chi tiết từ mô tả trong ô prompt bên dưới')
                //     ->schema([
                //         Actions::make([
                //             Action::make('generateAIPrompt')
                //                 ->label(function () {
                //                     return $this->isGeneratingPrompt ? '⏳ Đang tạo...' : '🤖 Tạo Prompt bằng AI';
                //                 })
                //                 ->color('primary')
                //                 ->action('generateAIPrompt')
                //                 ->disabled(function () {
                //                     return $this->isGeneratingPrompt || empty(trim($this->data['prompt'] ?? ''));
                //                 })
                //                 ->button(),

                //             Action::make('clearPrompt')
                //                 ->label('🗑️ Xóa Kết Quả')
                //                 ->color('gray')
                //                 ->action('clearGeneratedPrompt')
                //                 ->button(),
                //         ])
                //         ->alignCenter(),
                //     ])
                //     ->collapsible(),

                // Tabs cho tạo ảnh mới và redesign
                Tabs::make('image_generation_tabs')
                    ->tabs([
                           // Tab 2: Redesign ảnh
                        Tab::make('🎨 Redesign ảnh')
                         ->schema([
                                Grid::make(12)
                                ->schema([
                                Textarea::make('prompt')
                                    ->label('Mô tả ảnh mới (Prompt)')
                                    ->required()
                                    ->rows(4)
                                    ->columnSpan(10)
                                    ->placeholder('Nhập mô tả cho ảnh redesign...'),

                                Select::make('num_images')
                                            ->label('Số lượng ảnh')
                                            ->options([
                                                1 => '1 ảnh',
                                                2 => '2 ảnh',
                                                3 => '3 ảnh',
                                                4 => '4 ảnh',
                                            ])
                                            ->default(1)
                                            ->required()
                                            ->columnSpan(2),

                                // Section::make('Upload ảnh từ máy tính')
                                //             ->schema([
                                //                 FileUpload::make('redesignImageFile')
                                //                     ->label('')
                                //                     ->image()
                                //                     ->imagePreviewHeight('200')
                                //                     ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                                //                     ->maxSize(5120) // 5MB
                                //                     ->directory('temp/redesign-images')
                                //                     ->disk('public')
                                //                     ->visibility('public')
                                //                     ->live()
                                //                     ->afterStateUpdated(function ($state, $livewire) {
                                //                         if ($state) {
                                //                             // Lưu file vào storage ngay lập tức để tránh bị xóa
                                //                             try {
                                //                                 if ($state instanceof TemporaryUploadedFile) {
                                //                                     $filename = 'redesign_' . time() . '_' . $state->getClientOriginalName();
                                //                                     $storedPath = $state->storeAs('temp/redesign-images', $filename, 'public');

                                //                                  

                                //                                     // Lưu đường dẫn đã store
                                //                                     $livewire->redesignImageFile = $storedPath;

                                //                                     // Tự động upload lên Ideogram ngay lập tức
                                //                                     $livewire->uploadImageToIdeogram($storedPath);
                                //                                 } else {
                                //                                     $livewire->redesignImageFile = $state;
                                //                                 }
                                //                                 $livewire->dispatch('$refresh');
                                //                             } catch (\Exception $e) {
                                //                                 \Log::error('Failed to store file', ['error' => $e->getMessage()]);
                                //                                 $livewire->redesignImageFile = $state;
                                //                             }
                                //                         } else {
                                //                             // Clear khi user xóa file
                                //                             $livewire->uploadedImageId = '';
                                //                             $livewire->redesignImageFile = null;
                                //                             $livewire->pastedImageDataUrl = ''; // Clear paste image
                                //                         }
                                //                     })
                                //                     // ->helperText('Chọn ảnh từ máy tính để redesign')
                                //                     // ->hint(function () {
                                //                     //     if (!empty($this->uploadedImageId)) {
                                //                     //         return '✅ Ảnh đã upload thành công! ID: ' . $this->uploadedImageId;
                                //                     //     }
                                //                     //     return '';
                                //                     // }),
                                //             ])
                                //             ->columnSpan(6),


                                        Section::make('Ảnh đã dán từ clipboard')
                                            ->schema([
                                                \Filament\Forms\Components\Placeholder::make('pasted_image_info')
                                                    ->label('')
                                                    ->content(function () {
                                                        if (!empty($this->pastedImageDataUrl)) {
                                                            $status = !empty($this->uploadedImageId) ?
                                                                'Ảnh đã được xử lý thành công! ID: ' . $this->uploadedImageId :
                                                                'Ảnh đã dán từ clipboard';

                                                            return new \Illuminate\Support\HtmlString('
                                                                <div class="space-y-3">
                                                                    <div class="border rounded-lg p-2 bg-gray-50">
                                                                        <img src="' . $this->pastedImageDataUrl . '"
                                                                             alt="Pasted Image"
                                                                             class="max-w-full h-auto max-h-48 rounded"
                                                                             style="object-fit: contain;" />
                                                                    </div>
                                                                    <div class="text-xs text-gray-500">
                                                                        Dán ảnh bằng Ctrl+V hoặc upload file ở bên trái
                                                                    </div>
                                                                </div>
                                                            ');
                                                        }
                                                        return 'Chưa có ảnh nào được dán. Sử dụng Ctrl+V để dán ảnh từ clipboard.';
                                                    })
                                            ])
                                            ->columnSpan(6),
                                           
                                           
                                    ]),

                                // Provider selection với tự động phân tích ảnh
                                Grid::make(12)
                                    ->schema([
                                        \Filament\Forms\Components\Placeholder::make('provider_selection')
                                            ->label('Chọn AI Provider (Tự động phân tích ảnh)')
                                            ->content(function () {
                                                $hasReachedLimit = GeneratedImage::getTodayCount(Auth::id()) >= $this->dailyLimit;
                                                $isDisabled = $this->isAnalyzingImage || (empty($this->pastedImageDataUrl) && empty($this->redesignImageFile)) || $hasReachedLimit;
                                                $disabledClass = $isDisabled ? 'opacity-50 cursor-not-allowed' : '';

                                                return new \Illuminate\Support\HtmlString('
                                                    <div class="space-y-4">
                                                        <div class="flex gap-4 justify-center">
                                                            <button type="button"
                                                                wire:click="setAnalysisProviderAndAnalyze(\'openai\')"
                                                                ' . ($isDisabled ? 'disabled' : '') . '
                                                                class="provider-button px-6 py-4 text-sm rounded-lg border-2 transition-all duration-200 ' . $disabledClass . ' ' .
                                                                ($this->analysisProvider === 'openai' ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white border-primary-500 shadow-lg' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-primary-300 hover:shadow-md') . '">
                                                                <div class="flex flex-col items-center gap-2">
                                                                   
                                                                    <span class="font-semibold">AI 1</span>
                                                                </div>
                                                            </button>
                                                            <button type="button"
                                                                wire:click="setAnalysisProviderAndAnalyze(\'ideogram\')"
                                                                ' . ($isDisabled ? 'disabled' : '') . '
                                                                class="provider-button px-6 py-4 text-sm rounded-lg border-2 transition-all duration-200 ' . $disabledClass . ' ' .
                                                                ($this->analysisProvider === 'ideogram' ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white border-primary-500 shadow-lg' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-primary-300 hover:shadow-md') . '">
                                                                <div class="flex flex-col items-center gap-2">
                                                                   
                                                                    <span class="font-semibold">AI 2</span>
                                                                </div>
                                                            </button>
                                                        </div>
                                                        <div class="text-center">
                                                            <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ' .
                                                                ($this->isAnalyzingImage ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400') . '">
                                                                ' . ($this->isAnalyzingImage ?
                                                                    '<svg class="w-3 h-3 mr-1 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Đang phân tích ảnh...' :
                                                                    'Hiện tại: ' . ($this->analysisProvider === 'openai' ? 'AI 1' : 'AI 2') . ' - Click để phân tích ảnh'
                                                                ) . '
                                                            </div>
                                                        </div>
                                                    </div>
                                                ');
                                            })
                                            ->columnSpan(12),
                                    ])
                                    ->visible(fn () => !empty($this->uploadedImageId) || !empty($this->pastedImageDataUrl) || !empty($this->redesignImageFile)),
                            ]),
                  
                        // Tab 1: Tạo ảnh mới
                    //     Tab::make('🚀 Tạo ảnh mới')
                    //         ->schema([
                    //             Grid::make(12)
                    //                 ->schema([
                    //                     Textarea::make('prompt')
                    //                         ->label('Mô tả ảnh (Prompt)')
                    //                         ->required()
                    //                         ->rows(4)
                    //                         ->columnSpan(10)
                    //                         ->placeholder('Nhập mô tả chi tiết cho ảnh bạn muốn tạo...'),

                    //                     Select::make('num_images')
                    //                         ->label('Số lượng ảnh')
                    //                         ->options([
                    //                             1 => '1 ảnh',
                    //                             2 => '2 ảnh',
                    //                             3 => '3 ảnh',
                    //                             4 => '4 ảnh',
                    //                         ])
                    //                         ->default(1)
                    //                         ->required()
                    //                         ->columnSpan(2),
                    //                 ]),
                    //         ]),

                    ])
            ])
            ->statePath('data');
    }

    public function generateImages(): void
    {
        $this->validate();

        if ($this->isGenerating) {
            Notification::make()
                ->warning()
                ->title('Đang tạo ảnh')
                ->body('Vui lòng chờ quá trình tạo ảnh hiện tại hoàn thành.')
                ->send();
            return;
        }

        // Kiểm tra giới hạn số lần tạo ảnh trong ngày
        $userId = Auth::id();
        $numImages = $this->data['num_images'];

        $todayCount = GeneratedImage::getTodayCount($userId);
        $remainingCount = GeneratedImage::getRemainingCount($userId, $this->dailyLimit);

        if ($todayCount >= $this->dailyLimit) {
            Notification::make()
                ->danger()
                ->title('Đã đạt giới hạn hàng ngày')
                ->body("Bạn đã tạo {$todayCount}/{$this->dailyLimit} ảnh hôm nay. Vui lòng thử lại vào ngày mai.")
                ->persistent()
                ->send();
            return;
        }

        if ($numImages > $remainingCount) {
            Notification::make()
                ->warning()
                ->title('Vượt quá giới hạn')
                ->body("Bạn chỉ có thể tạo thêm {$remainingCount} ảnh hôm nay. Hiện tại: {$todayCount}/{$this->dailyLimit}")
                ->persistent()
                ->send();
            return;
        }

        $this->isGenerating = true;
        $this->streamMessages = [];
        $this->generatedImages = [];
        $this->rawResponses = [];
        $this->currentStatus = 'Đang khởi tạo...';

        try {
            $ideogramService = new IdeogramService();
            $prompt = $this->data['prompt'];
            $numImages = $this->data['num_images'];

            $this->dispatch('start-generation');

            // Kiểm tra có ảnh để redesign không (từ file upload hoặc paste)    
            if (!empty($this->uploadedImageId)) {
                $this->streamMessages[] = '🎨 Bắt đầu redesign ảnh...';
                $this->currentStatus = '🎨 Bắt đầu redesign ảnh...';

                // Gọi API redesign
                $result = $ideogramService->redesignImageSync($prompt, $this->uploadedImageId, $numImages);
            } elseif (!empty($this->pastedImageDataUrl)) {
                // Nếu có ảnh paste nhưng chưa có uploadedImageId, thử upload lại
                $this->streamMessages[] = '📋 Đang xử lý ảnh đã dán...';
                $this->currentStatus = '📋 Đang xử lý ảnh đã dán...';

                try {
                    $this->uploadBase64ImageToIdeogram($this->pastedImageDataUrl);

                    if (!empty($this->uploadedImageId)) {
                        $this->streamMessages[] = '🎨 Bắt đầu redesign ảnh đã dán...';
                        $this->currentStatus = '🎨 Bắt đầu redesign ảnh đã dán...';

                        // Gọi API redesign
                        $result = $ideogramService->redesignImageSync($prompt, $this->uploadedImageId, $numImages);
                    } else {
                        throw new \Exception('Không thể upload ảnh đã dán');
                    }
                } catch (\Exception $e) {
                    $this->streamMessages[] = '⚠️ Lỗi xử lý ảnh đã dán: ' . $e->getMessage();
                    $this->streamMessages[] = '🚀 Chuyển sang tạo ảnh mới...';
                    $this->currentStatus = '🚀 Bắt đầu tạo ảnh mới...';

                    // Fallback to normal image generation
                    $result = $ideogramService->createImageSync($prompt, $numImages);
                }
            } else {
                $this->streamMessages[] = '🚀 Bắt đầu tạo ảnh...';
                $this->currentStatus = '🚀 Bắt đầu tạo ảnh...';

                // Gọi API tạo ảnh thông thường
                $result = $ideogramService->createImageSync($prompt, $numImages);
            }

            // Lưu raw response
            $this->rawResponses[] = [
                'timestamp' => now()->format('H:i:s.u'),
                'data' => $result
            ];

            if ($result['success']) {
                $this->generatedImages = $result['images'];
                $this->streamMessages[] = '✅ ' . $result['message'];
                $this->currentStatus = '💾 Đang lưu ảnh lên S3...';

                if ($result['source'] === 'mock') {
                    $this->streamMessages[] = '⚠️ Sử dụng demo mode: ' . ($result['reason'] ?? 'API không khả dụng');
                }

                // Tạo records trong database và đẩy vào queue để upload S3
                if (!empty($this->generatedImages)) {
                    $this->streamMessages[] = '💾 Đang tạo records và đẩy vào queue upload S3...';

                    try {
                        $imageStorageService = new ImageStorageService();
                        $savedImages = $imageStorageService->createMultipleImageRecords(
                            $this->generatedImages,
                            $prompt,
                            Auth::id(),
                            [
                                'num_images' => $numImages,
                                'api_source' => $result['source'] ?? 'ideogram',
                                'generation_time' => now()->toISOString(),
                            ]
                        );

                        // Đẩy từng ảnh vào queue để upload S3
                        foreach ($savedImages as $savedImage) {
                            ProcessImageUpload::dispatch($savedImage)
                                ->onQueue('image_processing')
                                ->delay(now()->addSeconds(2)); // Delay 2s để tránh overwhelm
                        }

                        $this->streamMessages[] = '✅ Đã tạo ' . count($savedImages) . ' records và đẩy vào queue upload S3!';
                        $this->streamMessages[] = '🔄 Ảnh sẽ được upload lên S3 trong background...';
                        $this->currentStatus = '✅ Hoàn thành!';

                        Notification::make()
                            ->success()
                            ->title('Tạo ảnh thành công!')
                            ->body('Đã tạo thành công ' . count($savedImages) . ' ảnh. Upload S3 đang xử lý trong background.')
                            ->send();

                        // Refresh danh sách ảnh và thông tin giới hạn
                        $this->loadUserImages();

                    } catch (\Exception $e) {
                        $this->streamMessages[] = '⚠️ Lỗi tạo records: ' . $e->getMessage();
                        $this->streamMessages[] = '✅ Ảnh vẫn có thể xem từ URL gốc.';

                        Notification::make()
                            ->warning()
                            ->title('Tạo ảnh thành công, lưu database thất bại')
                            ->body('Ảnh đã được tạo nhưng không thể lưu vào database: ' . $e->getMessage())
                            ->send();
                    }
                } else {
                    throw new \Exception('Không có ảnh nào được tạo');
                }
            } else {
                throw new \Exception($result['error'] ?? 'Lỗi không xác định');
            }

        } catch (\Exception $e) {
            $this->streamMessages[] = '❌ Lỗi: ' . $e->getMessage();
            $this->currentStatus = 'Lỗi: ' . $e->getMessage();

            Notification::make()
                ->danger()
                ->title('Lỗi tạo ảnh')
                ->body($e->getMessage())
                ->send();
        } finally {
            $this->isGenerating = false;
            $this->dispatch('generation-complete');
        }
    }


    public function clearResults(): void
    {
        $this->streamMessages = [];
        $this->generatedImages = [];
        $this->rawResponses = [];
        $this->currentStatus = '';

        Notification::make()
            ->info()
            ->title('Đã xóa kết quả')
            ->body('Tất cả kết quả đã được xóa.')
            ->send();
    }

    public function toggleRawData(): void
    {
        $this->showRawData = !$this->showRawData;

        Notification::make()
            ->info()
            ->title($this->showRawData ? 'Hiển thị Raw Data' : 'Ẩn Raw Data')
            ->body($this->showRawData ? 'Raw API responses đang được hiển thị.' : 'Raw API responses đã được ẩn.')
            ->send();
    }



    public function deleteImage($imageId): void
    {
        $image = GeneratedImage::where('id', $imageId)
            ->where('user_id', Auth::id())
            ->first();

        if ($image) {
            // Xóa khỏi S3 nếu có
            if ($image->s3_path) {
                $imageStorageService = new ImageStorageService();
                $imageStorageService->deleteFromS3($image);
            }

            // Xóa khỏi database
            $image->delete();

            // Refresh danh sách
            $this->loadUserImages();

            Notification::make()
                ->success()
                ->title('Đã xóa ảnh')
                ->body('Ảnh đã được xóa thành công.')
                ->send();
        }
    }

    public function retryUpload($imageId): void
    {
        $image = GeneratedImage::where('id', $imageId)
            ->where('user_id', Auth::id())
            ->first();

        if ($image) {
            // Reset status và đẩy lại vào queue
            $image->update([
                'status' => 'pending',
                'error_message' => null,
            ]);

            ProcessImageUpload::dispatch($image)
                ->onQueue('image_processing');

            // Refresh danh sách
            $this->loadUserImages();

            Notification::make()
                ->success()
                ->title('Đã đẩy lại vào queue')
                ->body('Ảnh sẽ được upload lại trong background.')
                ->send();
        }
    }

    public function redesignFromImage($imageId): void
    {
        $image = GeneratedImage::where('id', $imageId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$image || !$image->display_url) {
            Notification::make()
                ->danger()
                ->title('Lỗi')
                ->body('Không tìm thấy ảnh hoặc ảnh không khả dụng.')
                ->send();
            return;
        }

        try {
            // Clear current redesign data
            $this->uploadedImageId = '';
            $this->redesignImageFile = null;
            $this->pastedImageDataUrl = '';
            $this->isProcessingPastedImage = false;
            $this->imageAnalysisResult = '';
            $this->isAnalyzingImage = false;

            // Set ảnh từ URL để redesign (sử dụng display_url thay vì base64)
            $this->pastedImageDataUrl = $image->display_url;

            // Upload ảnh lên Ideogram để lấy image_id
            $this->uploadImageUrlToIdeogram($image->display_url);

            // Set prompt với analysis prompt để sẵn sàng phân tích
            $this->data['prompt'] = $this->analysisPrompt;

            Notification::make()
                ->success()
                ->title('Đã chọn ảnh để redesign')
                ->body('Ảnh đã được chọn. Bạn có thể chỉnh sửa prompt và tạo ảnh mới.')
                ->send();

            // Scroll to top để user thấy form
            $this->dispatch('scroll-to-top');

        } catch (\Exception $e) {
            \Log::error('Redesign from image error: ' . $e->getMessage());

            Notification::make()
                ->danger()
                ->title('Lỗi')
                ->body('Không thể chuẩn bị ảnh để redesign: ' . $e->getMessage())
                ->send();
        }
    }

    public function goToPage($page): void
    {
        $this->currentPage = max(1, min($page, $this->totalPages));
        $this->loadUserImages();
    }

    public function previousPage(): void
    {
        if ($this->currentPage > 1) {
            $this->currentPage--;
            $this->loadUserImages();
        }
    }

    public function nextPage(): void
    {
        if ($this->currentPage < $this->totalPages) {
            $this->currentPage++;
            $this->loadUserImages();
        }
    }

    public function firstPage(): void
    {
        $this->currentPage = 1;
        $this->loadUserImages();
    }

    public function lastPage(): void
    {
        $this->currentPage = $this->totalPages;
        $this->loadUserImages();
    }

    public function getFormStatePath(): ?string
    {
        return 'data';
    }

    // ==================== PROMPT GENERATION METHODS ====================






    /**
     * Áp dụng prompt đã tạo vào form chính
     */
    public function useGeneratedPrompt(): void
    {
        if (empty($this->generatedPromptData['full_prompt'])) {
            Notification::make()
                ->warning()
                ->title('Chưa có prompt')
                ->body('Vui lòng tạo prompt trước.')
                ->send();
            return;
        }

        // Cập nhật form data
        $this->data['prompt'] = $this->generatedPromptData['full_prompt'];

        Notification::make()
            ->success()
            ->title('Đã áp dụng prompt')
            ->body('Prompt đã được chuyển vào form tạo ảnh.')
            ->send();
    }

    /**
     * Lưu template prompt để sử dụng lại
     */
    public function savePromptTemplate(): void
    {
        if (empty($this->generatedPromptData['full_prompt'])) {
            Notification::make()
                ->warning()
                ->title('Chưa có prompt')
                ->body('Vui lòng tạo prompt trước.')
                ->send();
            return;
        }

        // TODO: Implement save to database or session
        // Hiện tại chỉ thông báo
        Notification::make()
            ->info()
            ->title('Lưu template')
            ->body('Tính năng lưu template sẽ được phát triển trong phiên bản tiếp theo.')
            ->send();
    }

    /**
     * Xóa dữ liệu prompt đã tạo
     */
    public function clearGeneratedPrompt(): void
    {
        $this->generatedPromptData = [
            'product_name' => '',
            'keywords' => '',
            'hashtags' => '',
            'full_prompt' => ''
        ];

        // Clear uploaded file from form data
        $this->data['uploadedImageForPrompt'] = '';

        Notification::make()
            ->info()
            ->title('Đã xóa')
            ->body('Dữ liệu prompt đã được xóa.')
            ->send();
    }


    /**
     * Upload ảnh tới Ideogram API để lấy image_id cho redesign
     * (File đã được upload thành công lên server Laravel)
     */
    public function uploadImageToIdeogram($filePath): void
    {
        try {
            
            $actualPath = null;

            // Xử lý string path (đã được store vào storage)
            if (is_string($filePath)) {
                // Thử đường dẫn trong storage/app/public
                $storagePath = storage_path('app/public/' . $filePath);
                if (file_exists($storagePath)) {
                    $actualPath = $storagePath;
                } else {
                    // Thử các đường dẫn khác
                    $possiblePaths = [
                        $filePath,
                        storage_path('app/' . $filePath),
                        storage_path('app/livewire-tmp/' . basename($filePath))
                    ];

                    foreach ($possiblePaths as $path) {
                        if (file_exists($path)) {
                            $actualPath = $path;
                            break;
                        }
                    }
                }
            }
            // Xử lý TemporaryUploadedFile (fallback)
            elseif ($filePath instanceof TemporaryUploadedFile) {
                $actualPath = $filePath->getRealPath();
            }
            // Xử lý array
            elseif (is_array($filePath)) {
                $firstFile = $filePath[0] ?? null;
                if ($firstFile instanceof TemporaryUploadedFile) {
                    $actualPath = $firstFile->getRealPath();
                } elseif (is_string($firstFile)) {
                    $actualPath = storage_path('app/public/' . $firstFile);
                }
            }

            if (!$actualPath || !file_exists($actualPath)) {
                \Log::error('File not found', [
                    'input' => $filePath,
                    'actual_path' => $actualPath,
                    'storage_path' => is_string($filePath) ? storage_path('app/public/' . $filePath) : 'not_string',
                    'file_exists' => $actualPath ? file_exists($actualPath) : false
                ]);
                throw new \Exception('File không tìm thấy: ' . (is_string($filePath) ? $filePath : 'invalid_path'));
            }

            // Upload ảnh lên S3 trước
            $s3Url = $this->uploadImageToS3($actualPath);
            if (!$s3Url) {
                throw new \Exception('Không thể upload ảnh lên S3');
            }

            // Gửi S3 URL tới Node.js server
            $ideogramService = new IdeogramService();
            $result = $ideogramService->uploadImageUrl($s3Url);

            if ($result['success'] && !empty($result['image_id'])) {
                $this->uploadedImageId = $result['image_id'];

                Notification::make()
                    ->success()
                    ->title('Upload thành công!')
                    ->body('Ảnh đã được upload thành công. Image ID: ' . $result['image_id'])
                    ->send();

               
            } else {
                // Nếu API trả về success=false hoặc không có image_id
                $errorMsg = $result['error'] ?? $result['message'] ?? 'Upload thất bại - không nhận được image ID';
                throw new \Exception($errorMsg);
            }

        } catch (\Exception $e) {
            \Log::error('Upload image error: ' . $e->getMessage());

            Notification::make()
                ->danger()
                ->title('Lỗi upload')
                ->body('Không thể upload ảnh: ' . $e->getMessage())
                ->send();

            // Reset file upload
            $this->data['redesignImage'] = '';
            $this->uploadedImageId = '';
        }
    }

    /**
     * Handle file uploaded event
     */
    public function handleFileUploaded($filePath): void
    {
        if ($filePath) {
            $this->uploadImageToIdeogram($filePath);
        }
    }

    /**
     * Xóa ảnh redesign và reset
     */
    public function clearRedesignImage(): void
    {
        $this->uploadedImageId = '';
        $this->redesignImageFile = null;
        $this->pastedImageDataUrl = '';
        $this->isProcessingPastedImage = false;
        $this->imageAnalysisResult = '';
        $this->isAnalyzingImage = false;

        Notification::make()
            ->info()
            ->title('Đã xóa ảnh redesign')
            ->body('Chế độ tạo ảnh thông thường đã được kích hoạt.')
            ->send();
    }

    /**
     * Set analysis provider (legacy method - kept for compatibility)
     */
    public function setAnalysisProvider(string $provider): void
    {
        if (in_array($provider, ['openai', 'ideogram'])) {
            $this->analysisProvider = $provider;

            Notification::make()
                ->info()
                ->title('Đã chuyển AI Provider')
                ->body('Hiện tại sử dụng: ' . ($provider === 'openai' ? 'AI 1' : 'AI 2'))
                ->send();
        }
    }

    /**
     * Set analysis provider và tự động phân tích ảnh
     */
    public function setAnalysisProviderAndAnalyze(string $provider): void
    {
        // Kiểm tra giới hạn hàng ngày trước
        $userId = Auth::id();
        $todayCount = GeneratedImage::getTodayCount($userId);

        if ($todayCount >= $this->dailyLimit) {
            Notification::make()
                ->danger()
                ->title('Đã đạt giới hạn hàng ngày')
                ->body("Bạn đã sử dụng hết {$this->dailyLimit} lượt tạo ảnh hôm nay. Không thể sử dụng AI phân tích ảnh.")
                ->persistent()
                ->send();
            return;
        }

        // Kiểm tra có ảnh để phân tích không
        if (empty($this->pastedImageDataUrl) && empty($this->redesignImageFile)) {
            Notification::make()
                ->warning()
                ->title('Chưa có ảnh')
                ->body('Vui lòng chọn ảnh trước khi chọn AI provider.')
                ->send();
            return;
        }

        // Kiểm tra đang phân tích không
        if ($this->isAnalyzingImage) {
            Notification::make()
                ->warning()
                ->title('Đang phân tích')
                ->body('Vui lòng chờ quá trình phân tích hiện tại hoàn thành.')
                ->send();
            return;
        }

        // Set provider
        if (in_array($provider, ['openai', 'ideogram'])) {
            $this->analysisProvider = $provider;

            $providerName = $provider === 'openai' ? 'AI 1' : 'AI 2';

            Notification::make()
                ->info()
                ->title('Đã chọn ' . $providerName)
                ->body('Đang bắt đầu phân tích ảnh...')
                ->send();

            // Tự động gọi phân tích ảnh
            $this->analyzeImageForPrompt();
        }
    }

    /**
     * Phân tích ảnh bằng AI để tạo prompt (hỗ trợ OpenAI và Ideogram)
     */
    public function analyzeImageForPrompt(): void
    {
        // Kiểm tra giới hạn hàng ngày trước khi phân tích
        $userId = Auth::id();
        $todayCount = GeneratedImage::getTodayCount($userId);

        if ($todayCount >= $this->dailyLimit) {
            Notification::make()
                ->danger()
                ->title('Đã đạt giới hạn hàng ngày')
                ->body("Bạn đã sử dụng hết {$this->dailyLimit} lượt tạo ảnh hôm nay. Không thể phân tích ảnh thêm.")
                ->persistent()
                ->send();
            return;
        }

        if (empty($this->pastedImageDataUrl)) {
            Notification::make()
                ->warning()
                ->title('Chưa có ảnh')
                ->body('Vui lòng chọn ảnh trước khi phân tích.')
                ->send();
            return;
        }

        // Validate image format
        if (!str_starts_with($this->pastedImageDataUrl, 'data:image/') && !filter_var($this->pastedImageDataUrl, FILTER_VALIDATE_URL)) {
            Notification::make()
                ->danger()
                ->title('Định dạng ảnh không hợp lệ')
                ->body('Ảnh phải là base64 data URL hoặc URL hợp lệ.')
                ->send();
            return;
        }

        $this->isAnalyzingImage = true;
        $this->imageAnalysisResult = '';

        try {
            $result = [];

            if ($this->analysisProvider === 'ideogram') {
                // Sử dụng Ideogram API
                $ideogramService = new IdeogramService();

                // Nếu chưa có uploadedImageId, cần upload ảnh trước
                if (empty($this->uploadedImageId)) {
                    if (str_starts_with($this->pastedImageDataUrl, 'data:image/')) {
                        // Upload ảnh base64
                        $this->uploadBase64ImageToIdeogram($this->pastedImageDataUrl);
                    } elseif (filter_var($this->pastedImageDataUrl, FILTER_VALIDATE_URL)) {
                        // Upload ảnh từ URL
                        $this->uploadImageUrlToIdeogram($this->pastedImageDataUrl);
                    }

                    if (empty($this->uploadedImageId)) {
                        throw new \Exception('Không thể upload ảnh để phân tích');
                    }
                }

                // Gọi API describe với image_id đã có
                $result = $ideogramService->describeImage($this->uploadedImageId);

                if (!$result['success']) {
                    throw new \Exception('Có lỗi xảy ra');
                }
            } else {
                // Sử dụng OpenAI (mặc định)
                $openAIService = new OpenAIService();
                $result = $openAIService->generateImageDescription(
                    $this->pastedImageDataUrl,
                    $this->data['prompt']
                );

                // Kiểm tra nếu có lỗi từ service
                if (isset($result['error']) || !isset($result['success']) || $result['success'] !== true) {
                    $errorMessage = $result['error'] ?? 'Không có thông tin lỗi chi tiết';
                    throw new \Exception($errorMessage);
                }
            }

            if (isset($result['description']) && !empty($result['description'])) {
                $this->imageAnalysisResult = $result['description'];

                // Tự động cập nhật prompt chính
                if ($this->analysisProvider === 'openai') {
                     $this->data['prompt'] = $this->basePromt . $this->imageAnalysisResult;
                } else {
                    $this->data['prompt'] = $this->imageAnalysisResult;
                }

                $providerName = $this->analysisProvider === 'ideogram' ? 'AI 2' : 'AI 1';
                Notification::make()
                    ->success()
                    ->title("Phân tích ảnh thành công")
                    ->body('Prompt đã được tạo từ ảnh và cập nhật vào form.')
                    ->send();
            } else {
                throw new \Exception('Không nhận được mô tả từ API. Response: ' . json_encode($result));
            }
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();

            Notification::make()
                ->danger()
                ->title("Lỗi phân tích ảnh")
                ->body('Chi tiết lỗi: ' . $errorMessage)
                ->persistent() // Giữ notification để user có thể đọc
                ->send();

        } finally {
            $this->isAnalyzingImage = false;
        }
    }

  

    /**
     * Xử lý ảnh được dán từ clipboard
     */
    public function handlePastedImage($imageDataUrl): void
    {
        $this->isProcessingPastedImage = true;

        try {
            if (empty($imageDataUrl) || !str_starts_with($imageDataUrl, 'data:image/')) {
                throw new \Exception('Dữ liệu ảnh không hợp lệ');
            }

            $this->pastedImageDataUrl = $imageDataUrl;

            // Clear file upload nếu có
            $this->redesignImageFile = null;

            // Tự động set prompt với analysis prompt
            $this->data['prompt'] = $this->analysisPrompt;

            // Convert base64 to file và upload lên Ideogram
            // $this->uploadBase64ImageToIdeogram($pastedImageDataUrl);

            Notification::make()
                ->success()
                ->title('Ảnh đã được dán')
                ->body('Ảnh từ clipboard đã được xử lý thành công. Prompt đã được tự động điền.')
                ->send();

        } catch (\Exception $e) {
            \Log::error('Handle pasted image error: ' . $e->getMessage());

            Notification::make()
                ->danger()
                ->title('Lỗi xử lý ảnh')
                ->body('Không thể xử lý ảnh từ clipboard: ' . $e->getMessage())
                ->send();

            // Reset trạng thái khi có lỗi
            $this->pastedImageDataUrl = '';
        } finally {
            $this->isProcessingPastedImage = false;
        }
    }

    /**
     * Upload ảnh từ URL lên Ideogram
     */
    private function uploadImageUrlToIdeogram(string $imageUrl): void
    {
        try {
            // Gửi URL trực tiếp tới Node.js server
            $ideogramService = new IdeogramService();
            $result = $ideogramService->uploadImageUrl($imageUrl);

            if ($result['success'] && !empty($result['image_id'])) {
                $this->uploadedImageId = $result['image_id'];
            } else {
                throw new \Exception($result['error'] ?? 'Upload thất bại');
            }

        } catch (\Exception $e) {
            \Log::error('Upload image URL error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Upload ảnh base64 lên Ideogram
     */
    private function uploadBase64ImageToIdeogram(string $imageDataUrl): void
    {
        try {
            // Convert base64 to temporary file
            $tempFile = $this->base64ToTempFile($imageDataUrl);

            if (!$tempFile) {
                throw new \Exception('Không thể tạo file tạm từ base64');
            }

            // Upload lên S3 trước
            $s3Url = $this->uploadImageToS3($tempFile);
            if (!$s3Url) {
                throw new \Exception('Không thể upload ảnh lên S3');
            }

            // Gửi S3 URL tới Node.js server
            $ideogramService = new IdeogramService();
            $result = $ideogramService->uploadImageUrl($s3Url);

            if ($result['success'] && !empty($result['image_id'])) {
                $this->uploadedImageId = $result['image_id'];

                \Log::info('Base64 image uploaded successfully', [
                    'image_id' => $result['image_id'],
                    's3_url' => $s3Url
                ]);
            } else {
                throw new \Exception($result['error'] ?? 'Upload thất bại');
            }

            // Cleanup temp file
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }

        } catch (\Exception $e) {
            \Log::error('Upload base64 image error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Convert base64 image to temporary file
     */
    private function base64ToTempFile(string $imageDataUrl): ?string
    {
        try {
            // Extract base64 data
            if (!preg_match('/^data:image\/(\w+);base64,(.+)$/', $imageDataUrl, $matches)) {
                throw new \Exception('Invalid base64 image format');
            }

            $extension = $matches[1];
            $base64Data = $matches[2];

            // Decode base64
            $imageData = base64_decode($base64Data);
            if ($imageData === false) {
                throw new \Exception('Failed to decode base64');
            }

            // Create temp file
            $tempFile = tempnam(sys_get_temp_dir(), 'paste_image_') . '.' . $extension;

            if (file_put_contents($tempFile, $imageData) === false) {
                throw new \Exception('Failed to write temp file');
            }

            return $tempFile;

        } catch (\Exception $e) {
            \Log::error('Base64 to temp file error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Upload ảnh lên S3 và trả về URL
     */
    private function uploadImageToS3(string $filePath): ?string
    {
        try {
            $filename = 'redesign-uploads/' . time() . '_' . basename($filePath);

            // Upload lên S3
            $s3Path = Storage::disk('s3')->putFileAs(
                'redesign-uploads',
                new \Illuminate\Http\File($filePath),
                basename($filename),
                'public'
            );

            if (!$s3Path) {
                throw new \Exception('S3 upload failed');
            }

            // Lấy URL public
            $s3Url = Storage::disk('s3')->url($s3Path);

            

            return $s3Url;

        } catch (\Exception $e) {
            \Log::error('Failed to upload to S3', [
                'error' => $e->getMessage(),
                'file_path' => $filePath
            ]);
            return null;
        }
    }

    // ==================== HELPER METHODS ====================








}
