<?php

namespace App\Filament\App\Pages;

use App\Exports\TiktokHoldStatementsExport;
use App\Models\Store;
use App\Models\TiktokHoldStatement;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Actions\ExportAction;
use Filament\Actions\Action as PageAction;
use Filament\Pages\Page;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class TiktokHoldStatementsOverview extends Page implements HasTable
{
    use InteractsWithTable;
    use HasPageShield;

    // Cấu hình hiển thị trong sidebar
    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';
    protected static ?string $navigationLabel = 'TikTok Hold Statements';
    protected static ?string $navigationGroup = 'Store Manager';
    protected static ?int $navigationSort = 2;

    // Cấu hình slug URL
    protected static ?string $slug = 'tiktok-hold-statements';

    /**
     * Kiểm tra quyền truy cập vào trang
     * Cho phép người dùng có vai trò super_admin hoặc User Manager truy cập
     */
    public static function canAccess(array $parameters = []): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    /**
     * Xác định xem trang có nên hiển thị trong menu navigation không
     */
    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    protected static string $view = 'filament.app.pages.tiktok-hold-statements-overview';

    protected static ?string $title = 'TikTok Hold Statements';

    public function table(Table $table): Table
    {
        return $table
            ->query(TiktokHoldStatement::query()->with(['store', 'store.seller']))
            ->columns([
                // Di chuyển các cột trạng thái, thời gian tạo và thời gian cập nhật lên đầu bảng
                TextColumn::make('payment_status')
                    ->label('Trạng thái')
                    ->badge()
                    ->size('xs')
                    ->color(fn (string $state): string => match ($state) {
                        'PENDING' => 'warning',
                        'PROCESSING' => 'info',
                        'PAID' => 'success',
                        'FAILED' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('Thời gian tạo')
                    ->date('d/m/Y')
                    ->sortable()
                    ->size('sm'),

                TextColumn::make('updated_at')
                    ->label('Thời gian cập nhật')
                    ->since()
                    ->sortable()
                    ->size('sm')
                    ->color(fn ($record) => $record->updated_at->isToday() && !$record->created_at->isToday() ? 'success' : null)
                    ->icon(fn ($record) => $record->updated_at->isToday() && !$record->created_at->isToday() ? 'heroicon-o-arrow-path' : null)
                    ->tooltip(fn ($record) => $record->updated_at->isToday() && !$record->created_at->isToday() ? 'Cập nhật hôm nay' : null),

                TextColumn::make('settlement_amount')
                    ->label('Số tiền')
                    ->money('USD')
                    ->sortable()
                    ->size('sm')
                    ->summarize(Sum::make()->label('Tổng cộng')->money('USD')),

                TextColumn::make('store.name')
                    ->label('Tên cửa hàng')
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->description(fn (TiktokHoldStatement $record): string =>
                        $record->store?->tiktok_shop_name ?? 'N/A'
                    )
                    ->wrap()
                    ->limit(25),



                TextColumn::make('store.seller.name')
                    ->label('Người bán')
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->limit(15),

                TextColumn::make('statement_id')
                    ->label('Statement ID')
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->copyable()
                    ->limit(15),

                TextColumn::make('payment_id')
                    ->label('Payment ID')
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->copyable()
                    ->limit(15),

                TextColumn::make('statement_time')
                    ->label('Thời gian')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->size('sm'),
            ])
            ->filters([
                SelectFilter::make('payment_status')
                    ->label('Trạng thái thanh toán')
                    ->options([
                        'PENDING' => 'Pending',
                        'PROCESSING' => 'Processing',
                        'PAID' => 'Paid',
                        'FAILED' => 'Failed',
                    ])
                    ->multiple(),

                SelectFilter::make('store')
                    ->label('Cửa hàng')
                    ->relationship('store', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('seller')
                    ->label('Người bán')
                    ->relationship('store.seller', 'name')
                    ->searchable()
                    ->preload(),

                Filter::make('high_amount')
                    ->label('Số tiền lớn (>$50)')
                    ->query(fn (Builder $query) => $query->where('settlement_amount', '>', 50)),

                Filter::make('created_today')
                    ->label('Tạo hôm nay')
                    ->query(fn (Builder $query) => $query->whereDate('created_at', now())),

                Filter::make('updated_today')
                    ->label('Cập nhật hôm nay')
                    ->query(fn (Builder $query) => $query->whereDate('updated_at', now()))
                    ->default(true),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                ViewAction::make()
                    ->label('Xem chi tiết')
                    ->icon('heroicon-o-eye')
                    ->modalHeading(fn (TiktokHoldStatement $record): string => "Chi tiết Statement #{$record->statement_id}")
                    ->modalContent(fn (TiktokHoldStatement $record) => view(
                        'filament.app.pages.tiktok-hold-statement-detail',
                        ['statement' => $record]
                    ))
                    ->modalWidth('md'),
            ])
            ->bulkActions([
                ExportBulkAction::make()
                    ->label('Xuất Excel')
                    ->exporter(TiktokHoldStatementsExport::class)
                    ->fileName('tiktok-hold-statements')
                    ->formats([
                        ExportFormat::Xlsx,
                    ]),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    protected function getHeaderActions(): array
    {
        return [
            ExportAction::make()
                ->label('Xuất dữ liệu')
                ->color('success')
                ->icon('heroicon-o-arrow-down-tray')
                ->exporter(TiktokHoldStatementsExport::class)
                ->fileName('tiktok-hold-statements')
                ->formats([
                    ExportFormat::Xlsx,
                    ExportFormat::Csv,
                ]),
        ];
    }
}
