<?php

namespace App\Filament\App\Pages;

use App\Filament\App\Widgets\FulfillmentSpeedChart;
use App\Filament\App\Widgets\FulfillmentStatsOverview;
use App\Services\FulfillmentSpeedService;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use Carbon\Carbon;
use Livewire\Attributes\Url;

class FulfillmentSpeedDashboard extends Page implements HasForms
{
    use HasPageShield;
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-truck';
    protected static string $view = 'filament.app.pages.fulfillment-speed-dashboard-new';
    protected static ?string $title = 'Thống Kê Tốc Độ Fulfill';
    protected static ?string $navigationLabel = 'Tốc Độ Fulfill';
    protected static ?string $navigationGroup = 'Analytics';
    protected static ?int $navigationSort = 2;

    public $dashboardData;

    #[Url(except: '')]
    public $dateRange = null;

    protected $startDate;
    protected $endDate;

    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user && $user->hasRole(['super_admin', 'Fulfillment Manager', 'User Manager']);
    }

    public function mount()
    {
        // Mặc định là tháng này
        if (empty($this->dateRange)) {
            $startDate = Carbon::now()->startOfMonth()->format('d/m/Y');
            $endDate = Carbon::now()->format('d/m/Y');
            $this->dateRange = $startDate . ' - ' . $endDate;
        }

        $this->parseDateRange($this->dateRange);
        $this->loadDashboardData();
    }

    protected function parseDateRange(string $dateRange = null)
    {
        if (empty($dateRange)) {
            $this->startDate = Carbon::now()->startOfMonth()->startOfDay();
            $this->endDate = Carbon::now()->endOfDay();
            return;
        }

        $dates = explode(' - ', $dateRange);
        if (count($dates) == 2) {
            $this->startDate = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
            $this->endDate = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
            return;
        }

        $this->startDate = Carbon::now()->startOfMonth()->startOfDay();
        $this->endDate = Carbon::now()->endOfDay();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('dateRange')
                    ->label('Khoảng thời gian thống kê')
                    ->timezone(config('app.timezone'))
                    ->displayFormat('D/M/Y')
                    ->format('d/m/Y')
                    ->separator(' - ')
                    ->ranges(function() {
                        $now = Carbon::now();

                        return [
                            'Tháng này' => [
                                $now->copy()->startOfMonth(),
                                $now->copy()->endOfMonth()
                            ],
                            'Tháng trước' => [
                                $now->copy()->subMonth()->startOfMonth(),
                                $now->copy()->subMonth()->endOfMonth()
                            ],
                        ];
                    })
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->dateRange = $state;
                        $this->parseDateRange($this->dateRange);
                        $this->loadDashboardData();

                        // Dispatch single event để update widgets
                        $this->dispatch('dashboard-date-updated', dateRange: $this->dateRange);
                    })
                    ->columnSpanFull(),
            ]);
    }

    public function loadDashboardData()
    {
        try {
            $fulfillmentService = new FulfillmentSpeedService();

            $this->dashboardData = $fulfillmentService->getFulfillmentAnalytics(
                $this->startDate,
                $this->endDate
            );

            // Dispatch event để update widgets
            $this->dispatch('dashboard-date-updated', dateRange: $this->dateRange);

        } catch (\Exception $e) {
            // Fallback data nếu có lỗi
            $this->dashboardData = [
                'daily_stats' => [],
                'summary' => [
                    'total_fulfillments' => 0,
                    'avg_daily_fulfillments' => 0,
                    'avg_fulfill_time_hours' => 0,
                    'slowest_day' => null,
                    'fastest_day' => null,
                ],
                'weekend_analysis' => [
                    'weekend_avg' => 0,
                    'weekday_avg' => 0,
                    'weekend_slower_percentage' => 0,
                ],
                'chart_data' => [
                    'labels' => [],
                    'fulfillments' => [],
                    'avg_time' => [],
                ],
                'error' => $e->getMessage(),
                'period' => $this->getCurrentPeriodDisplay()
            ];
        }
    }

    public function getCurrentPeriodDisplay(): string
    {
        if (!$this->startDate || !$this->endDate) {
            return 'Chưa chọn thời gian';
        }

        return $this->startDate->format('d/m/Y') . ' - ' . $this->endDate->format('d/m/Y');
    }

    protected function getViewData(): array
    {
        return [
            'dashboardData' => $this->dashboardData,
        ];
    }

    public function getSlowDaysAlert(): array
    {
        if (!$this->dashboardData || empty($this->dashboardData['daily_stats'])) {
            return [];
        }

        $alerts = [];
        $avgTime = $this->dashboardData['summary']['avg_fulfill_time_hours'] ?? 0;
        
        foreach ($this->dashboardData['daily_stats'] as $day) {
            if ($day['avg_fulfill_time_hours'] > ($avgTime * 1.5)) {
                $alerts[] = [
                    'date' => $day['date'],
                    'day_name' => $day['day_name'],
                    'time' => $day['avg_fulfill_time_hours'],
                    'fulfillments' => $day['fulfillments'],
                    'severity' => $day['avg_fulfill_time_hours'] > ($avgTime * 2) ? 'critical' : 'warning'
                ];
            }
        }

        return $alerts;
    }

    /**
     * Public method để refresh chart từ frontend
     */
    public function refreshChart()
    {
        $this->loadDashboardData();
        $this->dispatch('chart-updated');
    }

    /**
     * Method để handle khi dateRange thay đổi
     */
    public function updatedDateRange($value)
    {
        $this->parseDateRange($value);
        $this->loadDashboardData();

        // Dispatch single event để update widgets
        $this->dispatch('dashboard-date-updated', dateRange: $this->dateRange);
    }

    /**
     * Lấy widgets cho header
     */
    protected function getHeaderWidgets(): array
    {
        return [
            FulfillmentStatsOverview::class,
            FulfillmentSpeedChart::class,
        ];
    }

    /**
     * Truyền data cho widgets
     */
    public function getWidgetData(): array
    {
        return [
            'dateRange' => $this->dateRange,
            'dashboardData' => $this->dashboardData,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];
    }

    /**
     * Số cột cho header widgets
     */
    public function getHeaderWidgetsColumns(): int | string | array
    {
        return [
            'md' => 1,
            'xl' => 1,
        ];
    }

    /**
     * Số cột cho footer widgets
     */
    public function getFooterWidgetsColumns(): int | string | array
    {
        return 1;
    }
}
