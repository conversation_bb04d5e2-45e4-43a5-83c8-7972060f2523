<?php

namespace App\Filament\App\Pages;

use App\Models\User;
use App\Models\Team;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class LeaderManagerDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';
    protected static ?string $navigationLabel = 'Leader Dashboard';
    protected static ?string $title = 'Leader Dashboard';
    protected static ?string $slug = 'leader-manager-dashboard';
    protected static ?int $navigationSort = 1;
    protected static string $view = 'filament.app.pages.leader-manager-dashboard';

    public $selectedSellerId = null;
    public $managedSellers = [];
    public $showTeamStats = true; // true = hiển thị stats của cả team, false = hiển thị stats của seller cụ thể
    public $teamNames = []; // Danh sách tên các team mà leader quản lý

    public static function canAccess(): bool
    {
        if (Auth::check()) {
            return Auth::user()->hasAnyRole(['Leader', 'User Manager', 'super_admin']);
        }
        return false;
    }

    public function mount(): void
    {
        $this->loadManagedSellers();
        $this->loadTeamNames();

        // Mặc định hiển thị stats của cả team
        $this->showTeamStats = true;
        $this->selectedSellerId = null;
    }

    /**
     * Load managed sellers for tabs
     */
    private function loadManagedSellers(): void
    {
        $user = auth()->user();

        $managedSellers = $user->getLeaderTeamMembers();

        $this->managedSellers = $managedSellers->map(function($seller) {
            return [
                'id' => $seller->id,
                'name' => $seller->name,
                'avatar' => $seller->avatar_url,
            ];
        })->toArray();
    }

    /**
     * Load team names for the current leader
     */
    private function loadTeamNames(): void
    {
        $user = auth()->user();

        if (!$user->hasRole('Leader')) {
            $this->teamNames = [];
            return;
        }

        // Lấy tất cả teams mà leader này thuộc về
        $teams = $user->teams()->get();
        $this->teamNames = $teams->pluck('name')->toArray();
    }

    /**
     * Get formatted team names for display
     */
    public function getTeamNamesDisplay(): string
    {
        if (empty($this->teamNames)) {
            return 'Theo Seller';
        }

        if (count($this->teamNames) === 1) {
            return 'Team ' . $this->teamNames[0];
        }

        // Nếu có nhiều team, hiển thị dạng "Team A, Team B"
        return 'Teams ' . implode(', ', $this->teamNames);
    }

    /**
     * Switch to selected seller tab
     */
    public function selectSeller($sellerId): void
    {
        if ($sellerId === 'team') {
            // Hiển thị stats của cả team
            $this->showTeamStats = true;
            $this->selectedSellerId = null;
        } else {
            // Hiển thị stats của seller cụ thể
            $this->showTeamStats = false;
            $this->selectedSellerId = (int) $sellerId;
        }

        // Dispatch event để notify Alpine.js và widgets về sự thay đổi
        $this->dispatch('seller-changed', [
            'sellerId' => $this->selectedSellerId,
            'showTeamStats' => $this->showTeamStats
        ]);
    }

    /**
     * Select team stats (hiển thị stats của cả team)
     */
    public function selectTeamStats(): void
    {
        $this->selectSeller('team');
    }



    /**
     * Get data for selected seller
     */
    public function getSelectedSellerData(): array
    {
        if (!$this->selectedSellerId) {
            return [];
        }

        $seller = User::find($this->selectedSellerId);
        if (!$seller) {
            return [];
        }

        return [
            'id' => $seller->id,
            'name' => $seller->name,
            'email' => $seller->email,
            'avatar' => $seller->avatar_url,
        ];
    }


}
