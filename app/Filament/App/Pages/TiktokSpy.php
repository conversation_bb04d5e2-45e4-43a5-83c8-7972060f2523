<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Illuminate\Support\Facades\Http;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use App\Services\ProductImportService;
use Filament\Tables\Actions\BulkAction;

class TempProduct extends Model
{
    protected $guarded = [];
    public $timestamps = false;

    public static function setTableName($name)
    {
        $instance = new static;
        $instance->setTable($name);
        return $instance;
    }
}

class TiktokSpy extends Page implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-magnifying-glass';
    protected static ?string $navigationGroup = 'Products';
    protected static ?string $title = 'TikTok Spy';
    protected static string $view = 'filament.app.pages.tiktok-spy';

    public ?array $data = [];
    public $formData;
    public $currentPage = 1;
    public $totalPages = 0;
    public $hasNextPage = false;
    public $hasPreviousPage = false;

    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin', 'Seller']);
    }

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        TextInput::make('search')
                            ->label('Search')
                            ->placeholder('Search by product name or product id'),
                        TextInput::make('shop_id')
                            ->label('Shop')
                            ->placeholder('Shop name or id separate by comma'),
                    ]),

                Grid::make(5)
                    ->schema([
                        TextInput::make('sold_min')
                            ->label('Min sale')
                            ->numeric()
                            ->default(0),
                        TextInput::make('sold_max')
                            ->label('Max sale')
                            ->numeric()
                            ->default(0),
                        TextInput::make('price_min')
                            ->label('Min price')
                            ->numeric()
                            ->default(0),
                        TextInput::make('price_max')
                            ->label('Max price')
                            ->numeric()
                            ->default(0),
                        Select::make('insight_time')
                            ->label('Insight time')
                            ->options([
                                0 => 'All time',
                                1 => 'Last 1 day',
                                3 => 'Last 3 days',
                                7 => 'Last 7 days',
                                30 => 'Last 30 days',
                            ])
                            ->default(3),
                    ]),
            ])
            ->statePath('formData');
    }

    public function search()
    {
        $this->currentPage = 1;
        $this->performSearch();
    }

    public function nextPage()
    {
        if ($this->hasNextPage) {
            $this->currentPage++;
            $this->performSearch();
        }
    }

    public function previousPage()
    {
        if ($this->hasPreviousPage) {
            $this->currentPage--;
            $this->performSearch();
        }
    }

    protected function performSearch()
    {
        $params = [
            'search' => $this->formData['search'] ?? '',
            'page' => $this->currentPage
        ];

        if (!empty($this->formData['insight_time']) && $this->formData['insight_time'] > 0) {
            $params['insight_time'] = $this->formData['insight_time'];
        }

        if (!empty($this->formData['sold_min'])) {
            $params['sold_min'] = $this->formData['sold_min'];
        }

        if (!empty($this->formData['sold_max'])) {
            $params['sold_max'] = $this->formData['sold_max'];
        }

        if (!empty($this->formData['price_min'])) {
            $params['price_min'] = $this->formData['price_min'];
        }

        if (!empty($this->formData['price_max'])) {
            $params['price_max'] = $this->formData['price_max'];
        }

        if (!empty($this->formData['shop_id'])) {
            $params['shop_id'] = $this->formData['shop_id'];
        }

        $response = Http::withHeaders([
            'authorization' => 'Token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4ODY3ODYyLCJqdGkiOiI5ZDcyZGM1MjZjNTk0ZjhmOWM5OTY5ZTJlNWM4M2JmNCIsInVzZXJfaWQiOjM4NH0.XI-0CjErzXC47USoCr3pvqB6dfXcS4aL8-Tia1AqTgA',
            'accept' => 'application/json',
        ])->get('https://tt-api.masstic.com/v1/tiktok/products', $params);

        if ($response->successful()) {
            $data = $response->json();
            $this->data = $data['results'];

            $this->totalPages = ceil($data['count'] / 60);
            $this->hasNextPage = !empty($data['next']);
            $this->hasPreviousPage = !empty($data['previous']);
        }
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(function () {
                $tempTable = 'temp_products_' . uniqid();

                DB::statement("CREATE TEMPORARY TABLE {$tempTable} (
                id VARCHAR(255),
                name TEXT,
                cover VARCHAR(255),
                sold_count INT,
                format_available_price DECIMAL(10,2),
                shop_nickname VARCHAR(255),
                shop_id VARCHAR(255),
                currency VARCHAR(10),
                video_id VARCHAR(255),
                video_url TEXT,
                last_sold INT
            )");

                foreach ($this->data ?? [] as $item) {
                    DB::table($tempTable)->insert([
                        'id' => $item['id'] ?? '',
                        'name' => $item['name'] ?? '',
                        'cover' => $item['cover'] ?? '',
                        'sold_count' => $item['sold_count'] ?? 0,
                        'format_available_price' => $item['format_available_price'] ?? 0,
                        'shop_nickname' => $item['shop']['nickname'] ?? '',
                        'shop_id' => $item['shop']['id'] ?? '',
                        'currency' => $item['currency'] ?? '$',
                        'video_id' => $item['video']['id'] ?? null,
                        'video_url' => $item['video']['share_url'] ?? null,
                        'last_sold' => $item['last_sold'] ?? 0
                    ]);
                }

                return TempProduct::setTableName($tempTable)->newQuery();
            })
            ->defaultSort('sold_count', 'desc')
            ->columns([
                ImageColumn::make('cover')
                    ->label('Product Image')
                    ->width(150)
                    ->height(150)
                    ->square(),
                TextColumn::make('name')
                    ->label('Product Name')
                    ->searchable()
                    ->limit(50)
                    ->wrap(),
                TextColumn::make('sold_count')
                    ->label('Sold')
                    ->sortable(),
                TextColumn::make('format_available_price')
                    ->label('Price')
                    ->prefix('$')
                    ->sortable(),
                TextColumn::make('shop_nickname')
                    ->label('Shop Name'),
            ])
            ->actions([
                Action::make('import')
                    ->label(function ($record) {
                        $exists = \App\Models\ProductSource::where('external_id', $record->id)
                            ->where('source_type', 'tiktok')
                            ->where('user_id', auth()->id())
                            ->exists();

                        return $exists ? 'Already Imported' : 'Import to Source';
                    })
                    ->icon('heroicon-o-arrow-down-tray')
                    ->disabled(function ($record) {
                        return \App\Models\ProductSource::where('external_id', $record->id)
                            ->where('source_type', 'tiktok')
                            ->where('user_id', auth()->id())
                            ->exists();
                    })
                    ->color(function ($record) {
                        $exists = \App\Models\ProductSource::where('external_id', $record->id)
                            ->where('source_type', 'tiktok')
                            ->where('user_id', auth()->id())
                            ->exists();

                        return $exists ? 'gray' : 'success';
                    })
                    ->action(function ($record) {
                        $exists = \App\Models\ProductSource::where('external_id', $record->id)
                            ->where('source_type', 'tiktok')
                            ->where('user_id', auth()->id())
                            ->exists();

                        if ($exists) {
                            Notification::make()
                                ->title('Product already imported')
                                ->warning()
                                ->send();
                            return;
                        }

                        $url = "https://shop.tiktok.com/view/product/{$record->id}?region=US&locale=en";

                        try {
                            $importService = new ProductImportService();
                            $data = $importService->importFromUrl($url);

                            $productSource = new \App\Models\ProductSource();
                            $productSource->title = $data['title'] ?? $record->name ?? 'Untitled Product';
                            $productSource->description = $data['description'] ?? '';
                            $productSource->source_type = 'tiktok';
                            $productSource->external_id = $record->id ?? '';
                            $productSource->source_url = $url;
                            $productSource->status = 'active';
                            $productSource->images = !empty($data['images']) ? $data['images'] : [$record->cover ?? ''];

                            $sourceData = [
                                'shop_name' => (string)($record->shop_nickname ?? ''),
                                'shop_id' => (string)($record->shop_id ?? ''),
                                'price' => (string)($record->format_available_price ?? '0'),
                                'currency' => (string)($record->currency ?? '$'),
                                'sold_count' => (string)($record->sold_count ?? '0'),
                                'last_sold' => (string)($record->last_sold ?? '0'),
                                'video_id' => (string)($record->video_id ?? ''),
                                'video_url' => (string)($record->video_url ?? ''),
                            ];

                            $productSource->source_data = $sourceData;
                            $productSource->metadata = $data['metadata'] ?? [];
                            $productSource->user_id = auth()->id();
                            $productSource->save();

                            Notification::make()
                                ->title('Product imported successfully')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Error importing product: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
            ])
            ->bulkActions([
                // BulkAction::make('bulk_import')
                //     ->label('Import Selected')
                //     ->icon('heroicon-o-arrow-down-tray')
                //     ->action(function ( $records) {
                //         foreach ($records as $record) {
                //             $exists = \App\Models\ProductSource::where('external_id', $record->id)
                //                 ->where('source_type', 'tiktok')
                //                 ->where('user_id', auth()->id())
                //                 ->exists();

                //             if (!$exists) {
                //                 $url = "https://shop.tiktok.com/view/product/{$record->id}?region=US&locale=en";

                //                 try {
                //                     $importService = new ProductImportService();
                //                     $data = $importService->importFromUrl($url);

                //                     $productSource = new \App\Models\ProductSource();
                //                     $productSource->title = $data['title'] ?? $record->name ?? 'Untitled Product';
                //                     $productSource->description = $data['description'] ?? '';
                //                     $productSource->source_type = 'tiktok';
                //                     $productSource->external_id = $record->id ?? '';
                //                     $productSource->source_url = $url;
                //                     $productSource->status = 'active';
                //                     $productSource->images = !empty($data['images']) ? $data['images'] : [$record->cover ?? ''];

                //                     $sourceData = [
                //                         'shop_name' => (string)($record->shop_nickname ?? ''),
                //                         'shop_id' => (string)($record->shop_id ?? ''),
                //                         'price' => (string)($record->format_available_price ?? '0'),
                //                         'currency' => (string)($record->currency ?? '$'),
                //                         'sold_count' => (string)($record->sold_count ?? '0'),
                //                         'last_sold' => (string)($record->last_sold ?? '0'),
                //                         'video_id' => (string)($record->video_id ?? ''),
                //                         'video_url' => (string)($record->video_url ?? ''),
                //                     ];

                //                     $productSource->source_data = $sourceData;
                //                     $productSource->metadata = $data['metadata'] ?? [];
                //                     $productSource->user_id = auth()->id();
                //                     $productSource->save();
                //                 } catch (\Exception $e) {
                //                     Notification::make()
                //                         ->title('Error importing product: ' . $e->getMessage())
                //                         ->danger()
                //                         ->send();
                //                 }
                //             }
                //         }

                //         Notification::make()
                //             ->title('Bulk Import Completed')
                //             ->success()
                //             ->send();
                //     })
            ]);
    }
}
