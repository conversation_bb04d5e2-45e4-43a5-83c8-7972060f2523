<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Models\User;
use App\Services\SellerInvoiceService;
use App\Exports\SellerCsvExport;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Actions\Action as TableAction;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Eloquent\Builder;
use Filament\Notifications\Notification;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Actions\Action;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\DatePicker;
use App\Models\Order;
use App\Models\PayoutTransaction;
use App\Exports\OrdersExporter;
use App\Exports\BankTransactionsExporter;
use App\Exports\OrdersDirectExporter;
use App\Exports\BankTransactionsDirectExporter;
use Filament\Tables\Columns\TextColumn as TableTextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;

use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Traits\HasDateRangeFilter;



class SellerOverview extends Page implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;
    use HasPageShield;
    use HasDateRangeFilter;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';
    protected static ?string $navigationLabel = 'Seller Overview';
    protected static ?string $title = 'Tổng quan và Xuất dữ liệu Seller';
    protected static ?string $navigationGroup = 'Seller Management';
    protected static ?int $navigationSort = 1;
    protected static string $view = 'filament.app.pages.seller-overview';

    protected function getViewData(): array
    {
        return [
            'dateRange' => $this->dateRange,
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }

        // Chỉ hiển thị cho admin, ẩn khỏi seller
        return $user->hasAnyRole(['super_admin', 'User Manager', 'Developer', 'Accountant']);
    }

    public ?array $data = [];

    // Active table tracking
    public string $activeTable = 'sellers';

    public function switchTable($table)
    {
        $this->activeTable = $table;

        // Đảm bảo dateRange được parse trước khi reset table
        $this->ensureDateRange();

        $this->resetTable();
        // Force refresh để đảm bảo filters và actions xuất hiện
        $this->dispatch('$refresh');
    }

    public function mount(): void
    {
        // Khởi tạo date range với trait
        $this->initializeDateRange('current_month');
    }



    public static function canAccess(): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }

        // Cho phép super_admin, User Manager, Developer và Seller truy cập
        return $user->hasAnyRole(['super_admin', 'User Manager', 'Developer', 'Seller', 'Accountant']);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Sử dụng trait để tạo DateRangePicker
                $this->createDateRangeField(
                    'dateRange',
                    'Khoảng thời gian lọc dữ liệu',
                    null, // Sử dụng ranges mặc định từ helper
                    1 // columnSpanFull
                )->helperText('Khoảng thời gian này sẽ áp dụng cho tất cả các bảng dữ liệu'),
            ]);
    }

    /**
     * Override method từ trait để thêm notification
     */
    protected function onDateRangeUpdated(): void
    {
        // Reset table nếu có
        if (method_exists($this, 'resetTable')) {
            $this->resetTable();
        }

        // Force refresh page data
        if (method_exists($this, 'dispatch')) {
            $this->dispatch('$refresh');
        }

        // Thêm notification
        Notification::make()
            ->title('Đã áp dụng bộ lọc thời gian')
            ->success()
            ->body('Dữ liệu đã được cập nhật theo khoảng thời gian đã chọn')
            ->duration(2000)
            ->send();
    }

    protected function getSelectedDateRange(): array
    {
        // Sử dụng method từ trait
        $dateRange = $this->getDateRangeForQuery();
        return [$dateRange['start'], $dateRange['end']];
    }



    protected function getHeaderActions(): array
    {
        return [
            Action::make('export_all_sellers')
                ->label('Export All Sellers')
                ->icon('heroicon-o-cloud-arrow-down')
                ->color('danger')
                ->size('lg')
                ->action(function () {
                    return $this->exportAllSellersData();
                })
                ->requiresConfirmation()
                ->modalHeading('Xuất dữ liệu tất cả Seller')
                ->modalDescription(function () {
                    $totalSellers = User::whereHas('roles', function($q) {
                        $q->where('name', 'Seller');
                    })->count();

                    [$startDate, $endDate] = $this->getSelectedDateRange();
                    $monthText = $startDate->locale('vi')->translatedFormat('F Y');

                    return "Bạn có chắc chắn muốn xuất dữ liệu của TẤT CẢ {$totalSellers} seller trong hệ thống cho tháng {$monthText}? " .
                           "Quá trình này có thể mất rất nhiều thời gian và tạo ra file ZIP rất lớn. " .
                           "Chỉ nên thực hiện khi thực sự cần thiết.";
                })
                ->modalSubmitActionLabel('Xuất tất cả dữ liệu')
                ->modalCancelActionLabel('Hủy bỏ')
                ->visible(function () {
                    // Chỉ cho phép super_admin và Developer sử dụng chức năng này
                    $user = auth()->user();
                    return $user && $user->hasAnyRole(['super_admin', 'Developer', 'Accountant']);
                })
                ->tooltip('Xuất dữ liệu của tất cả seller (chỉ dành cho Super Admin và Developer)'),
        ];
    }

    public function table(Table $table): Table
    {
        if ($this->activeTable === 'orders') {
            return $this->getOrdersTable($table);
        }

        if ($this->activeTable === 'bank_transactions') {
            return $this->getBankTransactionsTable($table);
        }

        return $this->getSellersTable($table);
    }

    public function getSellersTable(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('name')
                    ->label('Tên Seller')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),
                TextInputColumn::make('salary_amount')
                    ->label('Base Salary')
                    ->type('number')
                    ->rules(['nullable', 'numeric', 'min:0'])
                    ->placeholder('Chưa thiết lập')
                    ->sortable()
                    ->afterStateUpdated(function ($record, $state) {
                        Notification::make()
                            ->title('Cập nhật thành công')
                            ->success()
                            ->body("Base Salary của {$record->name} đã được cập nhật thành " . number_format($state ?? 0) . " VND")
                            ->duration(3000)
                            ->send();
                    }),
                TextInputColumn::make('commission_rate')
                    ->label('Tỷ lệ hoa hồng (%)')
                    ->type('number')
                    ->rules(['nullable', 'numeric', 'min:0', 'max:100'])
                    ->placeholder('Chưa thiết lập')
                    ->sortable()
                    ->afterStateUpdated(function ($record, $state) {
                        Notification::make()
                            ->title('Cập nhật thành công')
                            ->success()
                            ->body("Commission Rate của {$record->name} đã được cập nhật thành {$state}%")
                            ->duration(3000)
                            ->send();
                    }),
                TextColumn::make('last_activity')
                    ->label('Hoạt động cuối')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->placeholder('Chưa có hoạt động'),
                TextColumn::make('created_at')
                    ->label('Ngày tham gia')
                    ->date('d/m/Y')
                    ->sortable(),
            ])
            ->filters([
                // Không cần filter riêng vì đã có DateRangePicker chung
            ])
            ->actions([
                TableAction::make('view_invoice')
                    ->label('View Invoice')
                    ->icon('heroicon-o-document-text')
                    ->color('primary')
                    ->url(fn (User $record): string => '/app/seller-invoice/' . $record->id)
                    ->openUrlInNewTab(false)
                    ->tooltip('Xem hóa đơn và báo cáo chi tiết của seller'),

                TableAction::make('export_all_data')
                    ->label('Export All Data')
                    ->icon('heroicon-o-archive-box-arrow-down')
                    ->color('warning')
                    ->action(function (User $record) {
                        return $this->exportAllDataForSeller($record);
                    })
                    ->tooltip('Xuất tất cả dữ liệu của seller này')
                    ->requiresConfirmation()
                    ->modalHeading('Xuất dữ liệu seller')
                    ->modalDescription(function (User $record): string {
                        [$startDate, $endDate] = $this->getSelectedDateRange();
                        $monthText = $startDate->locale('vi')->translatedFormat('F Y');

                        return "Bạn có chắc chắn muốn xuất tất cả dữ liệu của seller {$record->name} cho tháng {$monthText}? " .
                               "Quá trình này có thể mất vài phút.";
                    })
                    ->modalSubmitActionLabel('Xuất dữ liệu')
                    ->visible(function (User $record) {
                        $user = auth()->user();
                        // Seller chỉ có thể export dữ liệu của chính họ
                        if ($user->hasRole('Seller')) {
                            return $record->id === $user->id;
                        }
                        return $user->hasAnyRole(['super_admin', 'User Manager', 'Developer' , 'Accountant']);
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có seller nào')
            ->emptyStateDescription('Không tìm thấy seller nào trong hệ thống')
            ->emptyStateIcon('heroicon-o-users');
    }

    protected function getTableQuery(): Builder
    {
        $user = auth()->user();

        $query = User::query()
            ->withoutGlobalScopes()
            ->whereHas('roles', function ($query) {
                $query->where('name', 'Seller');
            });

        // Bảng Danh sách Seller KHÔNG lọc theo thời gian - hiển thị tất cả sellers
        // Chỉ lọc theo quyền truy cập

        // Nếu user là Seller, chỉ cho phép xem thông tin của chính họ
        if ($user && $user->hasRole('Seller')) {
            $query->where('id', $user->id);
        }

        return $query;
    }


    public function exportAllDataForSeller(User $seller)
    {
        try {
            // Kiểm tra quyền truy cập: Seller chỉ có thể export dữ liệu của chính họ
            $user = auth()->user();
            if ($user->hasRole('Seller') && $seller->id !== $user->id) {
                throw new \Exception('Bạn không có quyền xuất dữ liệu của seller khác');
            }

            // Sử dụng tháng đã chọn từ form
            [$startDate, $endDate] = $this->getSelectedDateRange();
            
            // Khởi tạo SellerInvoiceService
            $sellerInvoiceService = new SellerInvoiceService($seller, $startDate, $endDate);
            
            $filters = [];
            $sellerName = Str::slug($seller->name);
            $dateRange = $startDate->format('d_m_Y') . '_to_' . $endDate->format('d_m_Y');

            // Danh sách các bảng cần xuất (tương tự SellerInvoice.php)
            $tables = [
                'orders' => 'Đơn hàng',
                'order_items' => 'Chi tiết đơn hàng',
                'fulfillment_cost' => 'Chi phí đi đơn',
                'advertising_cost' => 'Chi phí quảng cáo',
                'design_cost' => 'Chi phí thiết kế',
                'printing_cost' => 'Chi phí in áo',
                'bank_payout' => 'Thanh toán ngân hàng',
                'previous_month_loss' => 'Lỗ tháng trước'
            ];

            // Tạo thư mục tạm để lưu các file CSV
            $tempDir = storage_path('app/temp/exports');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $exportedFiles = [];
            $filePaths = [];

            foreach ($tables as $tableType => $tableName) {
                try {
                    $export = new SellerCsvExport($sellerInvoiceService, $tableType, $filters);
                    $fileName = "seller-{$sellerName}-{$tableType}-{$dateRange}.csv";
                    $filePath = $tempDir . '/' . $fileName;

                    // Lưu file CSV vào thư mục tạm
                    Excel::store($export, 'temp/exports/' . $fileName, 'local', \Maatwebsite\Excel\Excel::CSV);

                    if (file_exists($filePath)) {
                        $filePaths[] = $filePath;
                        $exportedFiles[] = $tableName;
                    }
                } catch (\Exception $e) {
                    Log::error("Error exporting {$tableType} for seller {$seller->id}: " . $e->getMessage());
                }
            }

            // Tạo file seller_information.csv
            try {
                $sellerInfoFileName = "seller-{$sellerName}-information-{$dateRange}.csv";
                $sellerInfoFilePath = $tempDir . '/' . $sellerInfoFileName;

                // Chuẩn bị dữ liệu seller information
                $sellerInfoData = [
                    ['field_name', 'field_value'], // Header
                    ['Seller ID', $seller->id ?? ''],
                    ['Seller Name', $seller->name ?? ''],
                    ['Base Salary', $seller->salary_amount ?? ''],
                    ['Commission Rate', $seller->commission_rate ? $seller->commission_rate . '%' : ''],
                ];

                // Tạo file CSV
                $handle = fopen($sellerInfoFilePath, 'w');
                if ($handle) {
                    foreach ($sellerInfoData as $row) {
                        fputcsv($handle, $row);
                    }
                    fclose($handle);

                    if (file_exists($sellerInfoFilePath)) {
                        $filePaths[] = $sellerInfoFilePath;
                        $exportedFiles[] = 'Thông tin Seller';
                    }
                }
            } catch (\Exception $e) {
                Log::error("Error creating seller_information.csv for seller {$seller->id}: " . $e->getMessage());
            }

            if (empty($filePaths)) {
                throw new \Exception('Không có file nào được tạo thành công');
            }

            // Tạo file ZIP
            $zipFileName = "seller-{$sellerName}-all-data-{$dateRange}.zip";
            $zipPath = $tempDir . '/' . $zipFileName;

            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
                foreach ($filePaths as $filePath) {
                    $zip->addFile($filePath, basename($filePath));
                }
                $zip->close();

                // Tải xuống file ZIP
                return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
            } else {
                throw new \Exception('Không thể tạo file ZIP');
            }

        } catch (\Exception $e) {
            Log::error('Error in exportAllDataForSeller: ' . $e->getMessage());

            Notification::make()
                ->title('Lỗi xuất dữ liệu')
                ->danger()
                ->body('Có lỗi xảy ra khi xuất dữ liệu cho seller ' . $seller->name . ': ' . $e->getMessage())
                ->duration(8000)
                ->send();
        }
    }

    public function exportOrdersDirectly()
    {
        try {
            $dateRange = $this->getDateRangeForQuery();
            $startDate = $dateRange['start']->format('d_m_Y');
            $endDate = $dateRange['end']->format('d_m_Y');
            $fileName = "orders-{$startDate}-to-{$endDate}.csv";

            // Lấy dữ liệu orders với relationships
            $orders = $this->getOrdersTableQuery()->with(['store:id,name', 'seller:id,name'])->get();

            // Sử dụng dedicated exporter class
            return Excel::download(new OrdersDirectExporter($orders), $fileName);

        } catch (\Exception $e) {
            Log::error('Error in exportOrdersDirectly: ' . $e->getMessage());

            Notification::make()
                ->title('Lỗi xuất dữ liệu')
                ->danger()
                ->body('Có lỗi xảy ra khi xuất dữ liệu đơn hàng: ' . $e->getMessage())
                ->duration(5000)
                ->send();
        }
    }

    public function exportBankTransactionsDirectly()
    {
        try {
            $dateRange = $this->getDateRangeForQuery();
            $startDate = $dateRange['start']->format('d_m_Y');
            $endDate = $dateRange['end']->format('d_m_Y');
            $fileName = "bank-transactions-{$startDate}-to-{$endDate}.csv";

            // Lấy dữ liệu transactions với relationships
            $transactions = $this->getBankTransactionsTableQuery()->with(['store:id,name'])->get();

            // Sử dụng dedicated exporter class
            return Excel::download(new BankTransactionsDirectExporter($transactions), $fileName);

        } catch (\Exception $e) {
            Log::error('Error in exportBankTransactionsDirectly: ' . $e->getMessage());

            Notification::make()
                ->title('Lỗi xuất dữ liệu')
                ->danger()
                ->body('Có lỗi xảy ra khi xuất dữ liệu giao dịch ngân hàng: ' . $e->getMessage())
                ->duration(5000)
                ->send();
        }
    }

    public function exportAllSellersData()
    {
        try {
            // Kiểm tra quyền truy cập
            $user = auth()->user();
            if (!$user || !$user->hasAnyRole(['super_admin', 'Developer', 'Accountant'])) {
                throw new \Exception('Bạn không có quyền thực hiện chức năng này');
            }

            // Lấy tất cả seller trong hệ thống
            $sellers = User::whereHas('roles', function ($query) {
                $query->where('name', 'Seller');
            })->get();

            if ($sellers->isEmpty()) {
                throw new \Exception('Không có seller nào trong hệ thống');
            }

            // Sử dụng tháng đã chọn từ form
            [$startDate, $endDate] = $this->getSelectedDateRange();
            $dateRange = $startDate->format('d_m_Y') . '_to_' . $endDate->format('d_m_Y');

            // Tạo thư mục tạm chính
            $tempDir = storage_path('app/temp/exports/all-sellers');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            // Danh sách các bảng cần xuất
            $tables = [
                'orders' => 'Đơn hàng',
                'order_items' => 'Chi tiết đơn hàng',
                'fulfillment_cost' => 'Chi phí đi đơn',
                'advertising_cost' => 'Chi phí quảng cáo',
                'design_cost' => 'Chi phí thiết kế',
                'printing_cost' => 'Chi phí in áo',
                'bank_payout' => 'Thanh toán ngân hàng',
                'previous_month_loss' => 'Lỗ tháng trước'
            ];

            $processedSellers = 0;
            $totalSellers = $sellers->count();
            $sellerDirectories = [];

            // Thông báo bắt đầu quá trình
            Notification::make()
                ->title('Bắt đầu xuất dữ liệu')
                ->info()
                ->body("Đang xử lý {$totalSellers} seller. Quá trình này có thể mất vài phút...")
                ->duration(5000)
                ->send();

            foreach ($sellers as $seller) {
                try {
                    $sellerName = Str::slug($seller->name);
                    $sellerDir = $tempDir . '/seller-' . $sellerName;

                    // Tạo thư mục cho seller
                    if (!file_exists($sellerDir)) {
                        mkdir($sellerDir, 0755, true);
                    }

                    // Khởi tạo SellerInvoiceService cho seller này
                    $sellerInvoiceService = new SellerInvoiceService($seller, $startDate, $endDate);
                    $filters = [];
                    $hasData = false;

                    foreach ($tables as $tableType => $tableName) {
                        try {
                            $export = new SellerCsvExport($sellerInvoiceService, $tableType, $filters);
                            $fileName = "{$tableType}.csv";
                            $filePath = $sellerDir . '/' . $fileName;

                            // Lưu file CSV vào thư mục seller
                            Excel::store($export, 'temp/exports/all-sellers/seller-' . $sellerName . '/' . $fileName, 'local', \Maatwebsite\Excel\Excel::CSV);

                            if (file_exists($filePath)) {
                                $hasData = true;
                            }
                        } catch (\Exception $e) {
                            Log::error("Error exporting {$tableType} for seller {$seller->id}: " . $e->getMessage());
                        }
                    }

                    // Tạo file seller_information.csv
                    try {
                        $sellerInfoFileName = 'seller_information.csv';
                        $sellerInfoFilePath = $sellerDir . '/' . $sellerInfoFileName;

                        // Chuẩn bị dữ liệu seller information
                        $sellerInfoData = [
                            ['field_name', 'field_value'], // Header
                            ['Seller ID', $seller->id ?? ''],
                            ['Seller Name', $seller->name ?? ''],
                            ['Base Salary', $seller->salary_amount ?? ''],
                            ['Commission Rate', $seller->commission_rate ? $seller->commission_rate . '%' : ''],
                        ];

                        // Tạo file CSV
                        $handle = fopen($sellerInfoFilePath, 'w');
                        if ($handle) {
                            foreach ($sellerInfoData as $row) {
                                fputcsv($handle, $row);
                            }
                            fclose($handle);

                            if (file_exists($sellerInfoFilePath)) {
                                $hasData = true;
                            }
                        }
                    } catch (\Exception $e) {
                        Log::error("Error creating seller_information.csv for seller {$seller->id}: " . $e->getMessage());
                    }

                    if ($hasData) {
                        $sellerDirectories[] = $sellerDir;
                        $processedSellers++;
                    }

                } catch (\Exception $e) {
                    Log::error("Error processing seller {$seller->id}: " . $e->getMessage());
                }
            }

            if (empty($sellerDirectories)) {
                throw new \Exception('Không có dữ liệu nào được tạo thành công');
            }

            // Tạo file ZIP chính chứa tất cả thư mục seller
            $zipFileName = "all-sellers-data-{$dateRange}.zip";
            $zipPath = storage_path('app/temp/exports/' . $zipFileName);

            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
                foreach ($sellerDirectories as $sellerDir) {
                    $sellerDirName = basename($sellerDir);

                    // Thêm tất cả file trong thư mục seller vào ZIP
                    $files = glob($sellerDir . '/*.csv');
                    foreach ($files as $file) {
                        $relativePath = $sellerDirName . '/' . basename($file);
                        $zip->addFile($file, $relativePath);
                    }
                }
                $zip->close();

                // Thông báo thành công
                Notification::make()
                    ->title('Xuất dữ liệu thành công')
                    ->success()
                    ->body("Đã xuất dữ liệu của {$processedSellers}/{$totalSellers} seller thành công!")
                    ->duration(8000)
                    ->send();

                // Tải xuống file ZIP
                return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
            } else {
                throw new \Exception('Không thể tạo file ZIP chính');
            }

        } catch (\Exception $e) {
            Log::error('Error in exportAllSellersData: ' . $e->getMessage());

            Notification::make()
                ->title('Lỗi xuất dữ liệu tất cả seller')
                ->danger()
                ->body('Có lỗi xảy ra: ' . $e->getMessage())
                ->duration(10000)
                ->send();
        }
    }

    /**
     * Orders Table
     */
    public function getOrdersTable(Table $table): Table
    {
        return $table
            ->query($this->getOrdersTableQuery())
            ->columns([
                TableTextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                TableTextColumn::make('order_number')
                    ->label('Mã đơn hàng')
                    ->searchable()
                    ->sortable(),
                TableTextColumn::make('seller.name')
                    ->label('Seller')
                    ->searchable()
                    ->sortable(),
                TableTextColumn::make('store.name')
                    ->label('Cửa hàng')
                    ->searchable()
                    ->sortable(),
                TableTextColumn::make('total')
                    ->label('Tổng tiền (USD)')
                    ->money('USD')
                    ->sortable(),
                TableTextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->color(function ($state): string {
                        $statusValue = $state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state;
                        return match ($statusValue) {
                            'Completed' => 'success',
                            'Processing' => 'warning',
                            'Cancelled' => 'danger',
                            'OnHold' => 'gray',
                            default => 'primary',
                        };
                    })
                    ->formatStateUsing(function ($state) {
                        $statusValue = $state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state;
                        return match ($statusValue) {
                            'Processing' => 'Đang xử lý',
                            'AwaitingShipment' => 'Chờ vận chuyển',
                            'Completed' => 'Hoàn thành',
                            'OnHold' => 'Tạm giữ',
                            'Refunded' => 'Hoàn tiền',
                            'Cancelled' => 'Đã hủy',
                            default => $statusValue,
                        };
                    })
                    ->sortable(),
                TableTextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            
            ->headerActions([
                TableAction::make('export_orders_direct')
                    ->label('Export CSV')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        return $this->exportOrdersDirectly();
                    })
                    ->tooltip('Xuất dữ liệu đơn hàng trực tiếp'),
            ])
            ->bulkActions([
                // Xóa ExportBulkAction để tránh queue
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có đơn hàng nào')
            ->emptyStateDescription('Không tìm thấy đơn hàng nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-shopping-bag');
    }

    protected function getOrdersTableQuery(): EloquentBuilder
    {
        $user = auth()->user();

        $query = Order::query()
            ->with(['store', 'seller'])
            ->withoutGlobalScopes();

        // Áp dụng filter theo thời gian từ trait
        $query = $this->applyDateRangeToQuery($query, 'created_at');

        // Nếu user là Seller, chỉ cho phép xem orders của chính họ
        if ($user && $user->hasRole('Seller')) {
            $query->where('seller_id', $user->id);
        }

        return $query;
    }

    /**
     * Bank Transactions Table
     */
    public function getBankTransactionsTable(Table $table): Table
    {
        return $table
            ->query($this->getBankTransactionsTableQuery())
            ->columns([
                TableTextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                TableTextColumn::make('time')
                    ->label('Thời gian')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                TableTextColumn::make('transaction_id')
                    ->label('Mã giao dịch')
                    ->searchable()
                    ->sortable(),
                TableTextColumn::make('amount')
                    ->label('Số tiền')
                    ->money('USD')
                    ->sortable(),
                TableTextColumn::make('type')
                    ->label('Loại giao dịch')
                    ->badge()
                    ->color(function ($state): string {
                        $typeValue = is_object($state) && method_exists($state, 'value') ? $state->value : (string) $state;
                        return match ($typeValue) {
                            'Receive' => 'success',
                            'Send' => 'warning',
                            default => 'gray',
                        };
                    })
                    ->formatStateUsing(function ($state) {
                        $typeValue = is_object($state) && method_exists($state, 'value') ? $state->value : (string) $state;
                        return match ($typeValue) {
                            'Receive' => 'Nhận tiền',
                            'Send' => 'Gửi tiền',
                            default => $typeValue,
                        };
                    })
                    ->sortable(),
                TableTextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->color(function ($state): string {
                        $statusValue = is_object($state) && method_exists($state, 'value') ? $state->value : (string) $state;
                        return match ($statusValue) {
                            'Success' => 'success',
                            'Pending' => 'warning',
                            'Failed' => 'danger',
                            default => 'gray',
                        };
                    })
                    ->formatStateUsing(function ($state) {
                        $statusValue = is_object($state) && method_exists($state, 'value') ? $state->value : (string) $state;
                        return match ($statusValue) {
                            'Success' => 'Thành công',
                            'Pending' => 'Chờ xử lý',
                            'Failed' => 'Thất bại',
                            default => $statusValue,
                        };
                    })
                    ->sortable(),
                TableTextColumn::make('card_no')
                    ->label('Số tài khoản')
                    ->formatStateUsing(function ($state) {
                        if (empty($state)) {
                            return 'N/A';
                        }
                        return '****' . substr((string) $state, -4);
                    })
                    ->searchable(),
                TableTextColumn::make('store.name')
                    ->label('Cửa hàng')
                    ->searchable()
                    ->sortable(),
            ])
          
            ->headerActions([
                TableAction::make('export_bank_transactions_direct')
                    ->label('Export CSV')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        return $this->exportBankTransactionsDirectly();
                    })
                    ->tooltip('Xuất dữ liệu giao dịch ngân hàng trực tiếp'),
            ])
            ->bulkActions([
                // Xóa ExportBulkAction để tránh queue
            ])
            ->defaultSort('time', 'desc')
            ->emptyStateHeading('Không có giao dịch nào')
            ->emptyStateDescription('Không tìm thấy giao dịch nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-banknotes');
    }

    protected function getBankTransactionsTableQuery(): EloquentBuilder
    {
        $user = auth()->user();

        $query = PayoutTransaction::query()
            ->with(['store'])
            ->withoutGlobalScopes();

        // Áp dụng filter theo thời gian từ trait (sử dụng cột 'time')
        $query = $this->applyDateRangeToQuery($query, 'time');

        // Nếu user là Seller, chỉ cho phép xem transactions của stores thuộc về họ
        if ($user && $user->hasRole('Seller')) {
            $storeIds = $user->stores()->pluck('bank_account')->filter()->toArray();
            if (!empty($storeIds)) {
                $query->whereIn('card_no', $storeIds);
            } else {
                // Nếu seller không có store nào, không hiển thị gì
                $query->whereRaw('1 = 0');
            }
        }

        return $query;
    }
}
