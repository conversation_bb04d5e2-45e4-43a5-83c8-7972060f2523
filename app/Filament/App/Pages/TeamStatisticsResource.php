<?php

namespace App\Filament\App\Pages;


use App\Models\Team;
use App\Models\SellerFinance;
use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Support\Exceptions\Halt;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section as InfolistSection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Card;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use Filament\Forms\Components\TextInput;
use App\Models\User;
use Filament\Notifications\Notification;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Url;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Illuminate\Database\Eloquent\Builder;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class TeamStatisticsResource extends Page
{
    use InteractsWithForms;
    use InteractsWithInfolists;
    use InteractsWithFormActions;
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationGroup = 'Seller Management';
    protected static ?string $navigationLabel = 'Thống kê Team (Mới)';
    protected static ?string $slug = 'team-statistics-new';
    protected static ?int $navigationSort = 6;

    protected static bool $shouldRegisterNavigation = false;
    

    
    protected static string $view = 'filament.app.pages.team-statistics-resource';

    // Biến để lưu trữ team được chọn
    public ?int $selectedTeamId = null;

    // Biến để lưu trữ khoảng thời gian
    #[Url(except: '')]
    public $dateRange = null;

    /**
     * Kiểm tra quyền truy cập vào page
     *
     * Chỉ cho phép:
     * - super_admin và User Manager có thể xem tất cả team
     * - Leader chỉ có thể xem team mà họ là leader
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        // Super admin và User Manager luôn có quyền truy cập
        if ($user->hasAnyRole(['super_admin', 'User Manager'])) {
            return true;
        }

        // Kiểm tra xem người dùng có vai trò Leader không
        if ($user->hasRole('Leader')) {
            // Nếu đang xem trang danh sách (không có team cụ thể được chọn), cho phép truy cập
            if (!request()->has('selectedTeamId') || !request()->selectedTeamId) {
                return true;
            }

            // Nếu đang xem một team cụ thể, kiểm tra xem người dùng có phải là leader của team đó không
            $selectedTeamId = request()->selectedTeamId;

            // Kiểm tra xem user có thuộc team được chọn không
            return $user->teams()
                ->where('teams.id', $selectedTeamId)
                ->exists();
        }

        // Các vai trò khác không có quyền truy cập
        return false;
    }

    public function mount(): void
    {
        // Thiết lập giá trị mặc định cho khoảng thời gian (tháng trước)
        $startDate = now()->subMonth()->startOfMonth()->format('d/m/Y');
        $endDate = now()->subMonth()->endOfMonth()->format('d/m/Y');
        $this->dateRange = $startDate . ' - ' . $endDate;

        // Kiểm tra nếu có selectedTeamId từ request
        if (request()->has('selectedTeamId')) {
            $requestedTeamId = request()->selectedTeamId;
            $user = Auth::user();

            // Kiểm tra quyền truy cập
            $hasAccess = true;

            // Nếu không phải super_admin hoặc User Manager, kiểm tra quyền truy cập
            if (!$user->hasAnyRole(['super_admin', 'User Manager'])) {
                // Nếu là Leader, kiểm tra xem có thuộc team được chọn không
                if ($user->hasRole('Leader')) {
                    $hasAccess = $user->teams()
                        ->where('teams.id', $requestedTeamId)
                        ->exists();
                } else {
                    // Các vai trò khác không có quyền truy cập
                    $hasAccess = false;
                }
            }

            // Nếu không có quyền truy cập, đặt selectedTeamId về null
            if (!$hasAccess) {
                $this->selectedTeamId = null;
                Notification::make()
                    ->danger()
                    ->title('Không có quyền truy cập')
                    ->body('Bạn không có quyền xem thống kê của team này')
                    ->send();
            } else {
                $this->selectedTeamId = $requestedTeamId;
            }
        }

        // Thiết lập form
        $this->form->fill([
            'dateRange' => $this->dateRange,
            'selectedTeamId' => $this->selectedTeamId,
        ]);
    }

    /**
     * Định nghĩa form
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        Select::make('selectedTeamId')
                            ->label('Chọn Team')
                            ->options(function () {
                                $user = Auth::user();

                                // Super admin và User Manager có thể xem tất cả team
                                if ($user->hasAnyRole(['super_admin', 'User Manager'])) {
                                    return Team::pluck('name', 'id');
                                }

                                // Leader chỉ có thể xem team mà họ là leader
                                if ($user->hasRole('Leader')) {
                                    return $user->teams()->pluck('teams.name', 'teams.id');
                                }

                                // Các vai trò khác không có quyền xem team nào
                                return [];
                            })
                            ->searchable()
                            ->required()
                            ->live()
                            ->columnSpan(1)
                            ->afterStateUpdated(function () {
                                $this->resetTeamData();

                                // Kiểm tra quyền truy cập khi thay đổi team
                                $user = Auth::user();

                                // Super admin và User Manager có thể xem tất cả team
                                if ($user->hasAnyRole(['super_admin', 'User Manager'])) {
                                    return;
                                }

                                // Leader chỉ có thể xem team mà họ là leader
                                if ($user->hasRole('Leader')) {
                                    $hasAccess = $user->teams()
                                        ->where('teams.id', $this->selectedTeamId)
                                        ->exists();

                                    if (!$hasAccess) {
                                        $this->selectedTeamId = null;
                                        Notification::make()
                                            ->danger()
                                            ->title('Không có quyền truy cập')
                                            ->body('Bạn không có quyền xem thống kê của team này')
                                            ->send();
                                    }
                                } else {
                                    // Các vai trò khác không có quyền xem team nào
                                    $this->selectedTeamId = null;
                                    Notification::make()
                                        ->danger()
                                        ->title('Không có quyền truy cập')
                                        ->body('Bạn không có quyền xem thống kê team')
                                        ->send();
                                }
                            }),

                        DateRangePicker::make('dateRange')
                            ->label('Khoảng thời gian')
                            ->timezone(config('app.timezone'))
                            ->live()
                            ->columnSpan(1)
                            ->afterStateUpdated(function () {
                                $this->resetTeamData();
                            })
                            ->ranges([
                                'Tháng này' => [now()->startOfMonth(), now()->endOfMonth()],
                                'Tháng trước' => [now()->subMonth()->startOfMonth(), now()->subMonth()->endOfMonth()],
                                '2 tháng trước' => [now()->subMonth(2)->startOfMonth(), now()->subMonth(2)->endOfMonth()],
                                'Năm nay' => [now()->startOfYear(), now()->endOfYear()],
                                'Tất cả' => [now()->subYear(), now()],
                            ])
                    ]),
            ]);
    }


    /**
     * Reset dữ liệu team khi thay đổi lựa chọn hoặc bộ lọc
     */
    protected function resetTeamData(): void
    {
        // Có thể thêm logic xử lý khi thay đổi bộ lọc ở đây sau này
    }

    /**
     * Áp dụng bộ lọc ngày tháng cho query
     *
     * @param Builder $query Query cần áp dụng bộ lọc
     * @param string|null $dateRange Chuỗi khoảng thời gian (format: 'd/m/Y - d/m/Y')
     * @param string $column Tên cột để lọc (mặc định: 'month')
     * @return Builder Query sau khi áp dụng bộ lọc
     */
    protected function applyDateFilter($query, $dateRange = null, $column = 'month'): Builder
    {
        // Nếu không có dateRange, trả về query ban đầu
        if (!$dateRange) {
            return $query;
        }

        try {
            // Phân tích chuỗi dateRange (format: 'd/m/Y - d/m/Y')
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                $startDate = Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay();
                $endDate = Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay();

                // Áp dụng filter theo khoảng thời gian
                $query->whereBetween($column, [$startDate, $endDate]);
            }
        } catch (\Exception $e) {
            // Xử lý lỗi nếu có
            \Log::error('Lỗi khi phân tích dateRange: ' . $e->getMessage());

            // Thông báo cho người dùng
            Notification::make()
                ->title('Lỗi xử lý khoảng thời gian')
                ->body('Định dạng khoảng thời gian không hợp lệ. Vui lòng thử lại.')
                ->danger()
                ->send();
        }

        return $query;
    }

    /**
     * Lấy báo cáo tài chính của các seller trong team
     */
    protected function getTeamFinances(array $sellerIds): Collection
    {
        $query = SellerFinance::whereIn('seller_id', $sellerIds);

        // Áp dụng bộ lọc ngày tháng
        $query = $this->applyDateFilter($query, $this->dateRange, 'month');

        return $query->get();
    }

    /**
     * Chuẩn bị dữ liệu cho view
     */
    protected function getViewData(): array
    {
        $data = parent::getViewData();

        if ($this->selectedTeamId) {
            $incomeData = $this->teamIncome();

            // Tạo mảng chứa danh sách seller và báo cáo tài chính tương ứng
            $sellersList = [];

            // Lấy tất cả báo cáo tài chính của các seller trong team trong một truy vấn
            $sellerIds = $incomeData['team_sellers']->pluck('id')->toArray();
            $sellerFinances = $this->applyDateFilter(SellerFinance::whereIn('seller_id', $sellerIds), $this->dateRange, 'month')
                ->get()
                ->keyBy('seller_id');

            foreach ($incomeData['team_sellers'] as $seller) {
                $sellerFinance = $sellerFinances[$seller->id] ?? null;
                $report = $sellerFinance ? $sellerFinance->getDetailedReport() : null;

                // Tạo thông tin seller hợp nhất cho hiển thị trong một cột
                $sellerInfo = [
                    'id' => $seller->id,
                    'name' => $seller->name,
                    'email' => $seller->email,
                    'phone' => $seller->phone_number ?? 'N/A',
                    'avatar' => $seller->avatar_url,
                ];

                $sellersList[] = [
                    'seller_info' => $sellerInfo, // Thông tin seller đã được gộp
                    'finance' => $sellerFinance,
                    'report' => $report
                ];
            }

            // Lấy chữ cái đầu tiên của tên team
            $initial = substr($incomeData['team_name'], 0, 1);


            // Thêm dữ liệu vào mảng trả về
            $data = array_merge($data, [
                'sellersList' => $sellersList,
                'initial' => $initial,
                'hasData' => $incomeData['has_data'],
                'teamName' => $incomeData['team_name'],
                'teamId' => $incomeData['team_id'],
                'teamCommissionRate' => $incomeData['team_commission_rate'] ?? 0, // Thêm team commission rate
                'teamSellers' => $incomeData['team_sellers'] ?? collect(),
                'teamLeaders' => $incomeData['team_leaders'] ?? collect(),
                'totalSellerIncome' => $incomeData['total_salary'],
                'totalRevenue' => $incomeData['net_revenue'],
                'totalProfit' => $incomeData['net_profit'],
                'totalBankIncome' => $incomeData['total_bank_income'] ?? 0, // Tổng tiền về bank
                'totalCost' => $incomeData['total_cost'] ?? 0, // Tổng chi phí
                'totalNetProfit' => $incomeData['total_net_profit'] ?? 0, // Tổng lợi nhuận ròng (bank income - cost)
                'totalRemaining' => $incomeData['total_remaining'] ?? 0, // Tổng số tiền còn lại
                'leaderCommissions' => $incomeData['leader_commissions'] ?? [], // Chi tiết hoa hồng của từng leader
                'totalLeaderCommission' => $incomeData['total_leader_commission'] ?? 0, // Tổng số tiền hoa hồng leader nhận được
                'financeCount' => $incomeData['finance_count'],
                'profitMargin' => $incomeData['profit_margin'] ?? 0,
            ]);
        } else {
            // Dữ liệu mặc định khi chưa chọn team
            $data['sellersList'] = [];
        }

        return $data;
    }

    /**
     * Tính toán thu nhập của team dựa trên báo cáo tài chính của các seller
     */
    #[\Livewire\Attributes\Computed]
    public function teamIncome(): array
    {
        // Đảm bảo phương thức này phụ thuộc vào $dateRange
        $dateRangeFilter = $this->dateRange;

        if (!$this->selectedTeamId) {
            return [
                'team_id' => null,
                'team_name' => '',
                'total_salary' => 0,
                'net_revenue' => 0,
                'net_profit' => 0,
                'finance_count' => 0,
                'has_data' => false,
                'team_seller_ids' => [],
                'profit_margin' => 0,
                'team_sellers' => collect(),
                'team_leaders' => collect(),
                'date_range' => $dateRangeFilter,
            ];
        }

        // Lấy thông tin team
        $team = Team::with(['users' => function($query) {
            $query->role('Seller');
        }])->find($this->selectedTeamId);

        // Khởi tạo kết quả mặc định
        $result = [
            'team_id' => $team->id,
            'team_name' => $team->name,
            'team_commission_rate' => $team->commission_rate ?? 0, // Thêm team commission rate
            'total_salary' => 0,
            'net_revenue' => 0,
            'net_profit' => 0,
            'total_bank_income' => 0, // Tổng tiền về bank
            'total_cost' => 0, // Tổng chi phí
            'total_net_profit' => 0, // Tổng lợi nhuận ròng (bank income - cost)
            'finance_count' => 0,
            'has_data' => false,
            'team_seller_ids' => [],
            'profit_margin' => 0,
            'team_sellers' => collect(),
            'team_leaders' => collect(),
            'date_range' => $dateRangeFilter,
        ];

        // Lấy các seller trong team
        $teamSellers = $team->users;
        $result['team_sellers'] = $teamSellers;
        $result['team_seller_ids'] = $teamSellers->pluck('id')->toArray();

        // Lấy các leader trong team
        $teamLeaders = User::role('Leader')
            ->whereHas('teams', function($query) use ($team) {
                $query->where('teams.id', $team->id);
            })
            ->get();
        $result['team_leaders'] = $teamLeaders;

        if (empty($result['team_seller_ids'])) {
            return $result;
        }

        // Lấy báo cáo tài chính
        $finances = $this->getTeamFinances($result['team_seller_ids']);

        $result['finance_count'] = $finances->count();

        if ($result['finance_count'] === 0) {
            return $result;
        }

        // Tính toán dữ liệu tài chính
        $result['total_salary'] = $finances->sum('total_salary');
        $result['net_revenue'] = $finances->sum('net_revenue');
        $result['net_profit'] = $finances->sum('net_profit');

        // Tính tổng tiền về bank, tổng chi phí và tổng số tiền còn lại
        $totalBankIncome = 0;
        $totalCost = 0;
        $totalCommission = 0;

        foreach ($finances as $finance) {
            $report = $finance->getDetailedReport();
            $bankIncome = $report['payout']['bankPayout']['sum'] ?? 0;
            $cost = $report['costs']['total'] ?? 0;
            $commission = $report['earnings']['commission'] ?? 0;

            $totalBankIncome += $bankIncome;
            $totalCost += $cost;
            $totalCommission += $commission;
        }

        // Tổng lợi nhuận = Tổng tiền về bank - Tổng chi phí
        $totalNetProfit = $totalBankIncome - $totalCost;

        // Tổng số tiền còn lại = Tổng lợi nhuận - Tổng hoa hồng
        $totalRemaining = $totalNetProfit - $totalCommission;

        $result['total_bank_income'] = $totalBankIncome;
        $result['total_cost'] = $totalCost;
        // Lợi nhuận ròng = Tiền về bank - Chi phí
        $result['total_net_profit'] = $totalBankIncome - $totalCost;
        // Tổng số tiền còn lại = Tổng (Lợi nhuận ròng - Thu nhập) của tất cả seller
        $result['total_remaining'] = $totalRemaining;

        // Tính toán số tiền hoa hồng leader nhận được dựa trên team commission_rate
        $leaderCommissions = [];
        $totalLeaderCommission = 0;

        // Lấy commission_rate từ team
        $teamCommissionRate = $team->commission_rate ?? 0;
        $leaderCount = $teamLeaders->count();

        // Chia đều commission_rate cho các leader
        $commissionRatePerLeader = $leaderCount > 0 ? $teamCommissionRate / $leaderCount : 0;

        foreach ($teamLeaders as $leader) {
            $commissionAmount = ($totalRemaining * $commissionRatePerLeader) / 100;
            $totalLeaderCommission += $commissionAmount;

            $leaderCommissions[] = [
                'leader_id' => $leader->id,
                'leader_name' => $leader->name,
                'commission_rate' => $commissionRatePerLeader,
                'commission_amount' => $commissionAmount,
                'team_commission_rate' => $teamCommissionRate, // Thêm thông tin team commission rate
            ];
        }

        $result['leader_commissions'] = $leaderCommissions;
        $result['total_leader_commission'] = $totalLeaderCommission;

        $result['has_data'] = true;
        $result['profit_margin'] = $result['net_revenue'] > 0
            ? ($result['net_profit'] / $result['net_revenue'] * 100)
            : 0;

        return $result;
    }
}
