<?php

namespace App\Filament\App\Pages;

use App\Enums\ColorMap;
use App\Models\MockupTemplate;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Contracts\View\View;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class MockupTemplateGenerator extends Page implements HasForms
{
    use InteractsWithForms;
    use HasPageShield;

    #[Livewire\Attributes\Model]
    public string $fileName = '';

    protected static string $view = 'filament.app.pages.mockup-template-generator';

    public $mockupTemplateId;
    public $design;
    public $backDesign;
    public $selectedColors = [];

    protected static ?string $navigationGroup = 'Mockup Tools';  // Thay đổi này
    protected static ?string $navigationLabel = 'Single Mockup Generator';
    protected static ?string $navigationIcon = 'heroicon-o-square-3-stack-3d';
    protected static ?int $navigationSort = 2;  // Thêm thứ tự
    public $frontDesignX;
    public $frontDesignY;
    public $frontDesignWidth;
    public $frontDesignHeight;
    public $backDesignX;
    public $backDesignY;
    public $backDesignWidth;
    public $backDesignHeight;
    public $sizeChart;
    public $sizeChartColor;


    public function mount(): void
    {
        $this->form->fill();
    }
    public function adjustFrontDesign($prop, $amount)
    {
        $this->{"frontDesign{$prop}"} += $amount;
    }
    public function generateRandomGradient($color)
    {

        $colorData = ColorMap::getColorData($color);
        $baseColor = $colorData['hex'];

        if (!empty($colorData['backgroundImage'])) {
            return "url('{$colorData['backgroundImage']}')";
        }

        // Convert hex to HSL
        list($h, $s, $l) = $this->hexToHSL($baseColor);

        // Adjust saturation slightly
        $s1 = min(100, max(0, $s - rand(0, 10)));
        $s2 = min(100, max(0, $s + rand(0, 10)));

        // Adjust lightness slightly
        $l1 = min(100, max(0, $l - rand(0, 5)));
        $l2 = min(100, max(0, $l + rand(0, 5)));

        $color1 = $this->hslToHex($h, $s1, $l1);
        $color2 = $this->hslToHex($h, $s2, $l2);

        // Random angle for the gradient
        $angle = rand(0, 360);

        return "linear-gradient({$angle}deg, {$color1}, {$color2})";
    }

    private function hexToHSL($hex)
    {
        $hex = ltrim($hex, '#');
        list($r, $g, $b) = array_map(function ($part) {
            return hexdec(str_pad($part, 2, $part));
        }, str_split($hex, strlen($hex) == 3 ? 1 : 2));

        $r /= 255;
        $g /= 255;
        $b /= 255;
        $max = max($r, $g, $b);
        $min = min($r, $g, $b);
        $l = ($max + $min) / 2;

        if ($max == $min) {
            $h = $s = 0;
        } else {
            $d = $max - $min;
            $s = $l > 0.5 ? $d / (2 - $max - $min) : $d / ($max + $min);
            switch ($max) {
                case $r:
                    $h = ($g - $b) / $d + ($g < $b ? 6 : 0);
                    break;
                case $g:
                    $h = ($b - $r) / $d + 2;
                    break;
                case $b:
                    $h = ($r - $g) / $d + 4;
                    break;
            }
            $h /= 6;
        }

        return [round($h * 360), round($s * 100), round($l * 100)];
    }

    private function hslToHex($h, $s, $l)
    {
        $h /= 360;
        $s /= 100;
        $l /= 100;

        $r = $g = $b = $l;

        $v = ($l <= 0.5) ? ($l * (1.0 + $s)) : ($l + $s - $l * $s);
        if ($v > 0) {
            $m = $l + $l - $v;
            $sv = ($v - $m) / $v;
            $h *= 6.0;
            $sextant = floor($h);
            $fract = $h - $sextant;
            $vsf = $v * $sv * $fract;
            $mid1 = $m + $vsf;
            $mid2 = $v - $vsf;

            switch ($sextant) {
                case 0:
                    $r = $v;
                    $g = $mid1;
                    $b = $m;
                    break;
                case 1:
                    $r = $mid2;
                    $g = $v;
                    $b = $m;
                    break;
                case 2:
                    $r = $m;
                    $g = $v;
                    $b = $mid1;
                    break;
                case 3:
                    $r = $m;
                    $g = $mid2;
                    $b = $v;
                    break;
                case 4:
                    $r = $mid1;
                    $g = $m;
                    $b = $v;
                    break;
                case 5:
                    $r = $v;
                    $g = $m;
                    $b = $mid2;
                    break;
            }
        }
        return sprintf('#%02x%02x%02x', round($r * 255), round($g * 255), round($b * 255));
    }
    public function adjustBackDesign($prop, $amount)
    {
        $this->{"backDesign{$prop}"} += $amount;
    }
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('mockupTemplateId')
                    ->label('Mockup Template')
                    ->options(function () {
                        return MockupTemplate::orderBy('created_at', 'desc')
                            ->get()
                            ->mapWithKeys(function ($template) {
                                $imageUrl = Storage::disk('s3')->url($template->apparel_image);
                                return [$template->id => view('components.mockup-template-option', [
                                    'name' => $template->name,
                                    'image' => $imageUrl,
                                    'hasBackView' => $template->has_back_view,
                                    'createdAt' => $template->created_at,
                                ])->render()];
                            });
                    })
                    
                    ->optionsLimit(50)
                    ->allowHtml()
                    ->searchable()
                    ->required()
                    ->live()->columnSpanFull()
                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                        $this->mockupTemplateId = $state;
                        $this->dispatch('mockup-template-updated');
                        if ($state) {
                            $template = MockupTemplate::find($state);
                            if ($template) {
                                // Update front design position and size
                                $set('frontDesignX', $template->design_position_x);
                                $set('frontDesignY', $template->design_position_y);
                                $set('frontDesignWidth', $template->design_width);
                                $set('frontDesignHeight', $template->design_height);

                                // Update back design position and size if applicable
                                if ($template->has_back_view) {
                                    $set('backDesignX', $template->back_design_position_x);
                                    $set('backDesignY', $template->back_design_position_y);
                                    $set('backDesignWidth', $template->back_design_width);
                                    $set('backDesignHeight', $template->back_design_height);
                                } else {
                                    // Reset back design values if not applicable
                                    $set('backDesignX', null);
                                    $set('backDesignY', null);
                                    $set('backDesignWidth', null);
                                    $set('backDesignHeight', null);
                                }

                                Notification::make()
                                    ->title('MockupTemplate Updated')
                                    ->body("ID: {$state}")
                                    ->send();
                            } else {
                                Notification::make()
                                    ->title('Error')
                                    ->body("MockupTemplate with ID {$state} not found")
                                    ->danger()
                                    ->send();
                            }
                        }
                    }),

                TextInput::make('design')
                    ->label('Front Design URL'),


                TextInput::make('backDesign')
                    ->label('Back Design URL')

                    ->visible(function (Get $get) {
                        $templateId = $get('mockupTemplateId');
                        if (!$templateId) return false;
                        $template = MockupTemplate::find($templateId);
                        return $template && $template->has_back_view;
                    }),

                Select::make('selectedColors')
                    ->multiple()
                    ->label('Select Colors')
                    ->options(array_combine(
                        array_keys(ColorMap::toArray()),
                        array_keys(ColorMap::toArray())
                    ))
                    ->searchable()
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedColors = $state;
                    })
                    ->columnSpanFull()
                    ->hintActions([
                        \Filament\Forms\Components\Actions\Action::make('selectDarkColors')
                            ->label('Dark Colors')
                            ->action(fn(Set $set, Get $get) => $this->selectColorGroup(ColorMap::getDarkColors(), $set, $get)),
                        \Filament\Forms\Components\Actions\Action::make('selectLightColors')
                            ->label('Light Colors')
                            ->action(fn(Set $set, Get $get) => $this->selectColorGroup(ColorMap::getLightColors(), $set, $get)),
                        \Filament\Forms\Components\Actions\Action::make('selectNeutralColors')
                            ->label(' Neutral Colors')
                            ->action(fn(Set $set, Get $get) => $this->addColorGroup(ColorMap::getNeutralColors(), $set, $get)),
                        \Filament\Forms\Components\Actions\Action::make('selectAllColors')
                            ->label('All Colors')
                            ->action(fn(Set $set, Get $get) => $this->selectColorGroup(ColorMap::getAllColors(), $set, $get)),
                    ]),
                Select::make('sizeChart')

                    ->label('Size Chart')
                    ->options([
                        "Hoodie_Sweatshirt" => "Hoodie - Sweatshirt",
                        "T-Shirt" => "T-Shirt",
                        "Hoodie_Sweatshirt|T-Shirt" => "Hoodie - Sweatshirt & T-Shirt",
                    ]),
                Select::make('sizeChartColor')

                    ->label('Size Chart Colors')
                    ->options(array_combine(
                        array_keys(ColorMap::toArray()),
                        array_keys(ColorMap::toArray())
                    ))

                    ->searchable()

                    ->live()
            ])->columns(2);
    }

    public function getMockupTemplateProperty()
    {
        return $this->mockupTemplateId ? MockupTemplate::find($this->mockupTemplateId) : null;
    }

    private function updateColorPreview($colors, Set $set, Get $get): void
    {
        $set('color_preview', [
            'colors' => $colors,
            'filePath' => $get('design'),
        ]);
    }

    private function selectColorGroup(array $colors, Set $set, Get $get): void
    {
        $colorNames = array_map(fn($color) => str_replace('_', ' ', $color->name), $colors);
        $set('selectedColors', $colorNames);
        $this->selectedColors = $colorNames;
        $this->updateColorPreview($colorNames, $set, $get);
    }

    private function addColorGroup(array $colors, Set $set, Get $get): void
    {
        $newColorNames = array_map(fn($color) => str_replace('_', ' ', $color->name), $colors);
        $currentColors = $get('selectedColors') ?? [];
        $updatedColors = array_unique(array_merge($currentColors, $newColorNames));
        $set('selectedColors', $updatedColors);
        $this->selectedColors = $updatedColors;
        $this->updateColorPreview($updatedColors, $set, $get);
    }
    function lightenColor($color, $percent) {
        $num = hexdec($color);
        $amt = round(2.55 * $percent);
        $R = ($num >> 16) + $amt;
        $G = ($num >> 8 & 0x00FF) + $amt;
        $B = ($num & 0x0000FF) + $amt;
        return sprintf("#%02x%02x%02x", min(255, max(0, $R)), min(255, max(0, $G)), min(255, max(0, $B)));
    }
    public function generate()
    {
        $data = $this->form->getState();

        if (empty($data['mockupTemplateId']) || empty($data['design']) || empty($data['selectedColors'])) {
            Notification::make()
                ->title('Missing Information')
                ->body('Please ensure all fields are filled out.')
                ->danger()
                ->send();
            return;
        }

        Notification::make()
            ->title('Mockups Generated')
            ->body('Your mockups have been successfully generated.')
            ->success()
            ->send();
    }

    public function getSizeChartData($chart)
    {
        $sizeChartData = [
            "Hoodie_Sweatshirt" => [
                ["Size" => "S", "Width" => 20, "Length" => 27, "Sleeve" => 33.5],
                ["Size" => "M", "Width" => 22, "Length" => 28, "Sleeve" => 34.5],
                ["Size" => "L", "Width" => 24, "Length" => 29, "Sleeve" => 35.5],
                ["Size" => "XL", "Width" => 26, "Length" => 30, "Sleeve" => 36.5],
                ["Size" => "2XL", "Width" => 28, "Length" => 31, "Sleeve" => 37.5],
                ["Size" => "3XL", "Width" => 30, "Length" => 32, "Sleeve" => 38.5],
                ["Size" => "4XL", "Width" => 32, "Length" => 33, "Sleeve" => 39.5],
                ["Size" => "5XL", "Width" => 34, "Length" => 34, "Sleeve" => 40.5],
            ],
            "T-Shirt" => [
                ["Size" => "S", "Width" => 18, "Length" => 28, "Sleeve" => 15.125],
                ["Size" => "M", "Width" => 20, "Length" => 29, "Sleeve" => 16.5],
                ["Size" => "L", "Width" => 22, "Length" => 30, "Sleeve" => 17.5],
                ["Size" => "XL", "Width" => 24, "Length" => 31, "Sleeve" => 18.5],
                ["Size" => "2XL", "Width" => 26, "Length" => 32, "Sleeve" => 19.5],
                ["Size" => "3XL", "Width" => 28, "Length" => 33, "Sleeve" => 20.5],
                ["Size" => "4XL", "Width" => 30, "Length" => 34, "Sleeve" => 21.5],
                ["Size" => "5XL", "Width" => 32, "Length" => 35, "Sleeve" => 22.5],
            ],
        ];

        return $sizeChartData[$chart] ?? [];
    }
}
