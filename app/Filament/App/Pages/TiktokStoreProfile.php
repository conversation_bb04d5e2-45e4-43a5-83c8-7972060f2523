<?php

namespace App\Filament\App\Pages;

use App\Models\Store;
use App\Services\Tiktok\TiktokStoreService;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Page;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Exception;

class TiktokStoreProfile extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';
    protected static ?string $title = 'TikTok Store Profile';
    protected static string $view = 'filament.app.pages.tiktok-store-profile';

    public $selectedStoreId;
    public $storeProfile = [];
    public $loading = false;

    public function mount(): void
    {
        // Don't auto-select, let user choose from the list
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Select Store')
                    ->schema([
                        Select::make('selectedStoreId')
                            ->label('Choose Store')
                            ->options(fn() => $this->getUserStores()->pluck('name', 'id'))
                            ->searchable()
                            ->placeholder('Select a store to view profile')
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedStoreId = $state;
                                if ($state) {
                                    $this->loadStoreProfile();
                                } else {
                                    $this->storeProfile = [];
                                }
                            })
                            ->required(),
                    ])
                    ->columns(1),
            ]);
    }

    protected function getUserStores()
    {
        return Store::with('partnerApp') // Load partner app relationship
            ->get();
    }

    protected function getConfiguredStores()
    {
        return Store::where('owner_id', Auth::id())
            ->whereNotNull('access_token')
            ->whereNotNull('api_key')
            ->whereNotNull('api_secret')
            ->get();
    }

    public function loadStoreProfile()
    {
        if (!$this->selectedStoreId) {
            return;
        }

        $this->loading = true;
        $this->storeProfile = [];

        try {
            $store = Store::findOrFail($this->selectedStoreId);
            
            // Allow access to all stores (removed ownership check)

            // Check if store has required API credentials via partner app
            $hasPartnerCredentials = $store->access_token && $store->app_partner_id && 
                                   $store->partnerApp && $store->partnerApp->app_key && 
                                   $store->partnerApp->app_secret;
            
            if (!$hasPartnerCredentials) {
                throw new Exception('Store is missing required TikTok API credentials. Need: access_token + app_partner_id with valid partner app (app_key + app_secret)');
            }

            $tiktokService = new TiktokStoreService($store);
            $this->storeProfile = $tiktokService->getFormattedStoreProfile();

            if (isset($this->storeProfile['error'])) {
                Notification::make()
                    ->title('Error loading store profile')
                    ->body($this->storeProfile['error'])
                    ->danger()
                    ->send();
            } else {
                Notification::make()
                    ->title('Store profile loaded successfully')
                    ->success()
                    ->send();
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Error loading store profile')
                ->body($e->getMessage())
                ->danger()
                ->send();
                
            $this->storeProfile = [
                'error' => $e->getMessage(),
                'username' => 'Error loading profile',
                'avatar' => null,
                'selection_region' => 'N/A',
                'register_region' => 'N/A',
                'seller_type' => 'N/A',
                'user_type' => 'N/A',
                'creator_user_id' => 'N/A',
                'permissions' => [],
                'store_url' => null,
                'shop_url' => null
            ];
        } finally {
            $this->loading = false;
        }
    }

    public function refreshProfile()
    {
        $this->loadStoreProfile();
    }

    public function getStoreProfile()
    {
        return $this->storeProfile;
    }

    public function isLoading()
    {
        return $this->loading;
    }

    public function getSelectedStore()
    {
        if (!$this->selectedStoreId) {
            return null;
        }

        return Store::find($this->selectedStoreId);
    }

    public function selectStore($storeId)
    {
        $this->selectedStoreId = $storeId;
        $this->loadStoreProfile();
    }
}