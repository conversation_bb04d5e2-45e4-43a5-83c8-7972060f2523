<?php

namespace App\Filament\App\Pages;

use Exception;
use Filament\Pages\Page;
use <PERSON>klex\PHPIMAP\ClientManager;
use Webklex\PHPIMAP\Exceptions\ConnectionFailedException;
use <PERSON><PERSON>hanSalleh\FilamentShield\Traits\HasPageShield;

class CheckMail extends Page
{

    use HasPageShield;

    protected static ?string $navigationGroup = 'Tools';

    protected static ?string $navigationIcon = 'heroicon-o-inbox';

    protected static string $view = 'filament.app.pages.check-mail';

    protected static bool $shouldRegisterNavigation = false;

    public $email_hotmail = '';
    public $password_hotmail = '';
    public array $messages = [];
    public array|null $selectedMessage = null;

    public function loginAndFetchInbox()
    {
        $cm = new ClientManager();
        $client = $cm->make([
            'host'          => 'outlook.office365.com',
            'port'          => 995,
            'encryption'    => 'ssl',
            'validate_cert' => false,
            'username'      => $this->email_hotmail,
            'password'      => $this->password_hotmail,
            'protocol'      => 'pop3'
        ]);

        try {
            $client->connect();
            $folder = $client->getFolder('INBOX');
            $folderMessages = $folder->messages()
                                     ->limit(10)
                                     ->all()
                                     ->fetchOrderDesc('date')
                                     ->get();

            $this->messages = $folderMessages->map(function ($message) {
                $bodyHtml = $message->getHTMLBody(true);
                $bodyText = $message->getTextBody(true);

                // Xử lý lỗi encoding bằng mb_convert_encoding
                $bodyHtml = mb_convert_encoding($bodyHtml ?? '', 'UTF-8', 'auto');
                $bodyText = mb_convert_encoding($bodyText ?? '', 'UTF-8', 'auto');

                // Chỉ hiển thị HTML hoặc văn bản thuần nếu không có HTML
                $cleanBodyHtml = $bodyHtml ?: nl2br(htmlspecialchars($bodyText, ENT_QUOTES, 'UTF-8'));

                return [
                    'id' => $message->getUid(),
                    'subject' => htmlspecialchars($message->getSubject(), ENT_QUOTES, 'UTF-8'),
                    'body' => base64_encode($cleanBodyHtml)
                ];
            })->toArray();

        } catch (ConnectionFailedException $e) {
            session()->flash('error', 'Could not connect to the email server: ' . $e->getMessage());
        } catch (Exception $e) {
            session()->flash('error', 'An unexpected error occurred: ' . $e->getMessage());
        }
    }

    public function selectMessage($id)
    {
        $message = collect($this->messages)->firstWhere('id', $id);
        if ($message) {
            $this->selectedMessage = [
                'id' => $message['id'],
                'subject' => $message['subject'],
                'body' => base64_decode($message['body'])
            ];
        } else {
            $this->selectedMessage = null;
        }
    }

}
