<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Team;
use App\Models\SupplierOrder;
use App\Models\SellerFundRequest;
use App\Models\Production;
use App\Models\Store;
use App\Models\DesignJob;
use App\Models\TikTokPayment;
use App\Models\PayoutTransaction;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\SellerFinance;
use App\Services\SellerService;
use App\Services\SellerInvoiceService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Routing\Route;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\Summarizers\Count;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action as TableAction;
use App\Exports\SellerExport;
use App\Exports\SellerCsvExport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\ViewAction;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\File;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\View;
use Livewire\Attributes\Url;
use Livewire\Attributes\Computed;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use Malzariey\FilamentDaterangepickerFilter\Enums\OpenDirection;
use Malzariey\FilamentDaterangepickerFilter\Enums\DropDirection;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class SellerInvoice extends Page implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Báo cáo tài chính';
    protected static ?string $title = 'Báo cáo tài chính';
    protected static ?string $navigationGroup = 'Seller Management';
    protected static ?int $navigationSort = 1;
    protected static string $view = 'filament.app.pages.seller-invoice';
    protected static ?string $slug = 'seller-invoice/{userId}';

    public static function shouldRegisterNavigation(): bool
    {
        // Ẩn khỏi navbar - chỉ truy cập qua URL trực tiếp
        return false;
    }

    public static function getNavigationUrl(): string
    {
        $user = auth()->user();
        $userId = $user->id;
        return static::getUrl(['userId' => $userId]);
    }

    public static function canAccess(): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }

        if ($user->hasRole(['super_admin', 'User Manager', 'Developer', 'Accountant'])) {
            return true;
        }

        if ($user->hasRole('Seller') && $userId != $user->id) {
            return false;
        }

    }

    public function canEdit(): bool
    {
        $user = Auth::user();
        // Chỉ admin mới có thể chỉnh sửa, seller chỉ được xem
        return $user->hasAnyRole(['super_admin', 'User Manager', 'Developer', 'Accountant']);
    }

    public $sellerId;
    public $seller;
    protected $sellerService;
    protected $sellerInvoiceService;

    #[Url(except: '')]
    public $dateRange = null;

    #[Url(except: false)]
    public $showInvoiceList = false;

    #[Url(except: 0)]
    public $previousMonthLoss = 0;

    #[Url(except: '')]
    public $notes = '';

    #[Url(except: 0)]
    public $platformFee = 0;

    #[Url(except: 0)]
    public $feeDesign = 0;

    #[Url(except: 0)]
    public $onHold = 0;

    #[Url(except: 0)]
    public $commission = 0;

    #[Url(except: 0)]
    public $baseSalary = 0;

    #[Url(except: 'details')]
    public $activeTable = 'details';

    #[Url(except: 'orders')]
    public $activeTab = 'orders';

    public function switchTable($table)
    {
        $this->activeTable = $table;
        $this->resetTable();
        // Force refresh để đảm bảo filters và actions xuất hiện
        $this->dispatch('$refresh');
    }

    public function switchTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetTable();
        // Force refresh để đảm bảo filters và actions xuất hiện
        $this->dispatch('$refresh');
    }

    protected $startDate;
    protected $endDate;
    public $canSaveReport = true;
    public $existingReport = null;

    public function mount($userId)
    {
        $this->sellerId = $userId;
        $this->seller = User::findOrFail($userId);

        //  Thời gian mặc định là tháng trước (logic đúng)
        $lastMonth = Carbon::now()->startOfMonth()->subMonth();
        $startDate = $lastMonth->copy()->startOfMonth()->format('d/m/Y');
        $endDate = $lastMonth->copy()->endOfMonth()->format('d/m/Y');
        $this->dateRange = $startDate . ' - ' . $endDate;

        session(['seller_invoice_user_id' => $userId]);
        $this->commission = $this->seller->commission_rate ?? 0;
        $this->baseSalary = $this->seller->salary_amount ?? 0;

        $this->parseDateRange($this->dateRange);
        $this->initializeSellerService();
        $this->previousMonthLoss = $this->getPreviousMonthLoss();
        $this->canSaveReport = $this->checkCanSaveReport();
        $this->checkExistingReport();


        $this->onHold = $this->sellerService->calculateOnHold();

        // Nếu có báo cáo tồn tại, load dữ liệu từ báo cáo
        if ($this->existingReport) {
            $this->updateFormFromExistingReport();
        }

        // Force refresh table để đảm bảo filters và actions xuất hiện
        $this->dispatch('$refresh');
    }

    public function hydrate()
    {
        // Đảm bảo service được khởi tạo sau khi hydrate
        if (!$this->sellerInvoiceService) {
            $this->initializeSellerService();
        }
    }

    protected function initializeSellerService()
    {
        // Đảm bảo startDate và endDate được khởi tạo
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        if (isset($this->startDate) && isset($this->endDate)) {
            $this->sellerService = new SellerService($this->seller, $this->startDate, $this->endDate);
            $this->sellerInvoiceService = new SellerInvoiceService($this->seller, $this->startDate, $this->endDate);
        }
    }

    protected function formatDateRange(Carbon $startDate, Carbon $endDate): string
    {
        return $startDate->format('d/m/Y') . ' - ' . $endDate->format('d/m/Y');
    }

    public function getCurrentDateRangeForDisplay(): string
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        if (isset($this->startDate) && isset($this->endDate)) {
            return $this->formatDateRange($this->startDate, $this->endDate);
        }

        return 'Chưa chọn thời gian';
    }

    protected function parseDateRange(string $dateRange = null)
    {
        if (empty($dateRange)) {
            $lastMonth = Carbon::now()->startOfMonth()->subMonth();
            $this->startDate = $lastMonth->copy()->startOfMonth()->startOfDay();
            $this->endDate = $lastMonth->copy()->endOfMonth()->endOfDay();
            return;
        }

        $dates = explode(' - ', $dateRange);
        if (count($dates) == 2) {
            $this->startDate = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
            $this->endDate = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
            $this->initializeSellerService();
            $this->canSaveReport = $this->checkCanSaveReport();
            $this->checkExistingReport();
            return;
        }

        $lastMonth = Carbon::now()->startOfMonth()->subMonth();
        $this->startDate = $lastMonth->copy()->startOfMonth()->startOfDay();
        $this->endDate = $lastMonth->copy()->endOfMonth()->endOfDay();
        $this->canSaveReport = $this->checkCanSaveReport();
    }

    protected function isValidDate(string $date): bool
    {
        return (bool) \DateTime::createFromFormat('Y-m-d', $date);
    }

    public function form(Form $form): Form
    {
        $canEdit = $this->canEdit();

        return $form
            ->schema([
              DateRangePicker::make('dateRange')
                ->label('Khoảng thời gian')
                ->timezone(config('app.timezone'))
                ->displayFormat('D/M/Y')
                ->format('d/m/Y')
                ->separator(' - ')
                ->ranges(function() {
                    // Logic đúng: Lấy đầu tháng hiện tại rồi trừ tháng
                    $thisMonth = Carbon::now();
                    $lastMonth = Carbon::now()->startOfMonth()->subMonth();
                    $twoMonthsAgo = Carbon::now()->startOfMonth()->subMonths(2);
                    $threeMonthsAgo = Carbon::now()->startOfMonth()->subMonths(3);

                    $ranges = [
                        'Tháng này' => [
                            $thisMonth->copy()->startOfMonth(),
                            $thisMonth->copy()->endOfMonth()
                        ],
                        'Tháng trước' => [
                            $lastMonth->copy()->startOfMonth(),
                            $lastMonth->copy()->endOfMonth(),
                        ],
                        '2 tháng trước' => [
                            $twoMonthsAgo->copy()->startOfMonth(),
                            $twoMonthsAgo->copy()->endOfMonth(),
                        ],
                        '3 tháng trước' => [
                            $threeMonthsAgo->copy()->startOfMonth(),
                            $threeMonthsAgo->copy()->endOfMonth(),
                        ],
                        '3 tháng gần nhất' => [
                            $twoMonthsAgo->copy()->startOfMonth(),
                            $thisMonth->copy()->endOfMonth(),
                        ],
                        
                        'Năm nay' => [
                            Carbon::now()->startOfYear(),
                            Carbon::now()->endOfYear(),
                        ],
                          'Tất cả' => [
                            Carbon::create(2020, 1, 1)->startOfDay(), // Từ năm 2020 (hoặc thời điểm bắt đầu hệ thống)
                            Carbon::now()->endOfDay(), // Đến hiện tại
                        ],
                    ];

                    return $ranges;
                })
                ->live()
                ->afterStateUpdated(function ($state) {
                    $this->dateRange = $state;
                    $this->parseDateRange($this->dateRange);
                    $this->dispatch('filtersUpdated');
                })
                    ->columnSpanFull(),

                ...($canEdit ? [
                    Grid::make(2)
                        ->schema([
                            TextInput::make('platformFee')
                                ->label('Phí nền tảng')
                                ->numeric()
                                ->prefix('$')
                                ->disabled(!$canEdit)
                                ->live(onBlur: true)
                                ->afterStateUpdated(function ($state) {
                                    $this->validateNonNegativeValues();
                                    Notification::make()
                                        ->title('Cập nhật thành công')
                                        ->success()
                                        ->duration(2000)
                                        ->send();
                                }),

                            TextInput::make('onHold')
                                ->label('Số tiền đang giữ ( ON HOLD )')
                                ->helperText($this->existingReport ? 'Dữ liệu từ báo cáo đã lưu' : 'Tự động tính toán, có thể chỉnh sửa thủ công')
                                ->numeric()
                                ->prefix('$')
                                ->disabled(!$canEdit)
                                ->live(onBlur: true)
                                ->afterStateUpdated(function ($state) {
                                    $this->validateNonNegativeValues();
                                    Notification::make()
                                        ->title('Cập nhật thành công')
                                        ->success()
                                        ->duration(2000)
                                        ->send();
                                })
                                ->when($canEdit, function ($component) {
                                    return $component->suffixAction(
                                        \Filament\Forms\Components\Actions\Action::make('calculate')
                                            ->icon('heroicon-m-calculator')
                                            ->tooltip('Tính toán lại')
                                            ->action(function () {
                                                $this->onHold = $this->sellerService ? $this->sellerService->calculateOnHold() : 0;
                                                Notification::make()
                                                    ->title('Đã tính toán số tiền đang giữ')
                                                    ->success()
                                                    ->send();
                                            })
                                    );
                                }),

                            TextInput::make('baseSalary')
                                ->label('Lương cơ bản')
                                ->numeric()
                                ->prefix('$')
                                ->disabled(!$canEdit)
                                ->helperText($this->existingReport ? 'Dữ liệu từ báo cáo đã lưu' : '')
                                ->live(onBlur: true)
                                ->afterStateUpdated(function ($state) {
                                    $this->validateNonNegativeValues();
                                    // Reset tỷ lệ hoa hồng về 0 khi thay đổi lương cơ bản
                                    $this->commission = 0;
                                    Notification::make()
                                        ->title('Cập nhật thành công')
                                        ->success()
                                        ->duration(2000)
                                        ->send();
                                }),

                            TextInput::make('commission')
                                ->label('Tỷ lệ hoa hồng (%)')
                                ->numeric()
                                ->suffix('%')
                                ->disabled(!$canEdit)
                                ->helperText($this->existingReport ? 'Dữ liệu từ báo cáo đã lưu' : '')
                                ->live()
                                ->afterStateUpdated(function ($state) {
                                    $this->validateNonNegativeValues();
                                    // Force refresh computed property và view
                                    unset($this->cachedMountedActions);
                                    $this->dispatch('$refresh');
                                    Notification::make()
                                        ->title('Cập nhật thành công')
                                        ->success()
                                        ->duration(2000)
                                        ->send();
                                }),
                        ])
                ] : [])
            ]);
    }

    public function table(Table $table): Table
    {
        if ($this->activeTable === 'finance') {
            return $this->getFinanceTable($table);
        }

        if ($this->activeTable === 'details') {
            return $this->getDetailsTable($table);
        }

        return $this->getInvoicesTable($table);
    }

    public function getFinanceTable(Table $table): Table
    {
        return $table
            ->query($this->getFinanceReportsQuery())
            ->columns([
                TextColumn::make('month')
                    ->label('Tháng')
                    ->date('m/Y')
                    ->sortable(),
                TextColumn::make('gross_revenue')
                    ->label('Doanh thu')
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('total_cost')
                    ->label('Chi phí')
                    ->money('USD')
                    ->sortable()
                    ->description(function ($record) {
                        return SellerService::formatCostDetails($record->costs);
                    }),
                TextColumn::make('previous_loss')
                    ->label('Lỗ tháng trước')
                    ->money('USD')
                    ->sortable()
                    ->color(fn ($state): string => $state > 0 ? 'danger' : 'gray')
                    ->formatStateUsing(fn ($state): string => $state > 0 ? '$' . number_format($state, 2) : '-'),
                // TextColumn::make('gross_profit')
                //     ->label('Lợi nhuận gộp')
                //     ->money('USD')
                //     ->sortable()
                //     ->color(fn ($state): string => $state >= 0 ? 'success' : 'danger'),
                // TextColumn::make('net_profit')
                //     ->label('Lợi nhuận rồng')
                //     ->money('USD')
                //     ->sortable()
                //     ->color(fn ($state): string => $state >= 0 ? 'success' : 'danger')
                //     ->description('Lợi nhụân gộp - Lỗ tháng trước'),

          
            

                    TextColumn::make('bank_payout')
                    ->label('Bank Payout')
                    ->money('USD')
                    ->sortable()
                    ->state(function ($record) {
                        return SellerService::getBankPayoutFromRecord($record);
                    })
                    ->color('info'),
        

                                  TextColumn::make('adjusted_profit')
                    ->label('Lợi nhuận điều chỉnh (Lãi thực tế)')
                    ->money('USD')
                    ->sortable()
                    ->color(fn ($state): string => $state >= 0 ? 'success' : 'danger')
                    ->description('Bank Payout - Chi phí - Lỗ tháng trước - Lương cơ bản'),
                TextColumn::make('total_salary')
                    ->label('Thu nhập của seller')
                    ->money('USD')
                    ->sortable()
                ->description(
                    fn ($record) => "Lương cơ bản: $" . number_format($record->base_salary, 2) . "|" .
                        "Hoa hồng: $" . number_format($record->total_bonus, 2)
                ),

                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'pending' => 'Chờ xử lý',
                        'completed' => 'Hoàn thành',
                        'cancelled' => 'Đã hủy',
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'completed' => 'success',
                        'pending' => 'warning',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->defaultSort('month', 'desc')
            ->emptyStateHeading('Không có báo cáo tài chính nào')
            ->emptyStateDescription('Không tìm thấy báo cáo tài chính nào trong khoảng thời gian bạn đã chọn')
            ->emptyStateIcon('heroicon-o-chart-bar');
    }

    public function getInvoicesTable(Table $table): Table
    {
        return $table
            ->query($this->getInvoicesQuery())
            ->columns([
                TextColumn::make('invoice_number')
                    ->label('Số hóa đơn')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('issue_date')
                    ->label('Ngày phát hành')
                    ->date('d/m/Y')
                    ->sortable(),
                TextColumn::make('due_date')
                    ->label('Ngày đến hạn')
                    ->date('d/m/Y')
                    ->sortable(),
                TextColumn::make('total_amount')
                    ->label('Tổng tiền')
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('paid_amount')
                    ->label('Đã thanh toán')
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('remaining_amount')
                    ->label('Còn lại')
                    ->state(function (Invoice $record): float {
                        return $record->getRemainingAmount();
                    })
                    ->money('USD'),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->state(function (Invoice $record): string {
                        return $record->getPaymentStatusAttribute();
                    })
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'paid' => 'Đã thanh toán',
                        'unpaid' => 'Chưa thanh toán',
                        'partial' => 'Thanh toán một phần',
                        'cancelled' => 'Đã hủy',
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'paid' => 'success',
                        'partial' => 'info',
                        'unpaid' => 'warning',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
            ])
            ->emptyStateHeading('Không có hóa đơn nào')
            ->emptyStateDescription('Không tìm thấy hóa đơn nào trong khoảng thời gian bạn đã chọn')
            ->emptyStateIcon('heroicon-o-document-text');
    }

    public function getDetailsTable(Table $table): Table
    {
        // Tạo action xuất hàng loạt
        $batchExportAction = TableAction::make('export_all_data')
            ->label('Xuất tất cả dữ liệu')
            ->icon('heroicon-o-archive-box-arrow-down')
            ->color('warning')
            ->action(function () {
                return $this->exportAllData();
            })
            ->tooltip('Xuất tất cả bảng dữ liệu thành file CSV');

        // Lấy table tương ứng với tab hiện tại và thêm action
        switch ($this->activeTab) {
            case 'orders':
                $baseTable = $this->getOrdersTable($table);
                break;
            case 'order_items':
                $baseTable = $this->getOrderItemsTable($table);
                break;
            case 'fulfillment':
                $baseTable = $this->getFulfillmentCostTable($table);
                break;
            case 'advertising':
                $baseTable = $this->getAdvertisingCostTable($table);
                break;
            case 'design':
                $baseTable = $this->getDesignCostTable($table);
                break;
            case 'printing':
                $baseTable = $this->getPrintingCostTable($table);
                break;
            case 'bank_payout':
                $baseTable = $this->getBankPayoutTable($table);
                break;
            default:
                $baseTable = $this->getOrdersTable($table);
                break;
        }

        // Thêm action vào đầu danh sách header actions
        $existingActions = $baseTable->getHeaderActions();
        array_unshift($existingActions, $batchExportAction);

        return $baseTable->headerActions($existingActions);
    }

    protected function getFinanceReportsQuery(): Builder
    {
        return SellerFinance::query()
            ->where('seller_id', $this->sellerId);
    }

    protected function getInvoicesQuery(): Builder
    {

        return Invoice::query()
            ->where('user_id', $this->sellerId);
    }

    protected function getTableQuery(): Builder
    {
        // This method is called by Filament, but we handle the logic in table() method
        // Default to finance reports
        return $this->getFinanceReportsQuery();
    }

    protected function updateDateRange()
    {
        if (!session()->has('seller_invoice_user_id') && $this->sellerId) {
            session(['seller_invoice_user_id' => $this->sellerId]);
        }

        $this->parseDateRange($this->dateRange);
        $this->previousMonthLoss = $this->getPreviousMonthLoss();
        $this->showInvoiceList = true;
        $this->canSaveReport = $this->checkCanSaveReport();
        $this->checkExistingReport();

        // Nếu có báo cáo tồn tại, load dữ liệu từ báo cáo
        if ($this->existingReport) {
            $this->updateFormFromExistingReport();
            Notification::make()
                ->title('Đã tải dữ liệu từ báo cáo')
                ->info()
                ->body('Hiển thị dữ liệu từ báo cáo đã lưu.')
                ->send();
        } else {
            // Nếu không có báo cáo, tính toán lại onHold
            $this->onHold = $this->sellerService ? $this->sellerService->calculateOnHold() : 0;
            Notification::make()
                ->title('Đã áp dụng bộ lọc')
                ->success()
                ->send();
        }
    }

    protected function checkCanSaveReport(): bool
    {

        $user = Auth::user();
        if ($user->hasAnyRole(['super_admin'])) {
            return true;
        }
        return false;

        
    }

    protected function checkExistingReport()
    {
        if (!isset($this->startDate)) {
            return;
        }

        // Chỉ kiểm tra báo cáo cho khoảng thời gian chính xác 1 tháng
        $startOfMonth = $this->startDate->copy()->startOfMonth();
        $endOfMonth = $this->startDate->copy()->endOfMonth();

        $isExactMonth = $this->startDate->isSameDay($startOfMonth) &&
                       $this->endDate->isSameDay($endOfMonth);

        

        if ($isExactMonth) {

            $monthKey = $this->startDate->copy()->startOfMonth()->toDateString();
            // dd($monthKey);


            $this->existingReport = SellerFinance::where([
                'seller_id' => $this->sellerId,
                'month' => $monthKey,
            ])->first();
        } else {
            // Nếu không phải khoảng thời gian chính xác 1 tháng, không có báo cáo
            $this->existingReport = null;
        }
    }

    protected function updateFormFromExistingReport()
    {
        if (!$this->existingReport) {
            return;
        }

        // Xử lý commission_details có thể là string JSON hoặc array
        $commissionDetails = $this->existingReport->commission_details;
        if (is_string($commissionDetails)) {
            $commissionDetails = json_decode($commissionDetails, true) ?? [];
        } elseif (!is_array($commissionDetails)) {
            $commissionDetails = [];
        }

        // Xử lý costs có thể là string JSON hoặc array
        $costs = $this->existingReport->costs;
        if (is_string($costs)) {
            $costs = json_decode($costs, true) ?? [];
        } elseif (!is_array($costs)) {
            $costs = [];
        }

        // Cập nhật các giá trị form từ báo cáo
        $this->notes = $commissionDetails['notes'] ?? '';
        // Không cập nhật previousMonthLoss từ báo cáo, luôn tính toán lại từ SellerService
        $this->platformFee = $this->existingReport->platform_fees ?? 0;
        $this->feeDesign = $costs['design_cost'] ?? 0;
        $this->onHold = $this->existingReport->payout_on_hold ?? 0;
        $this->commission = $commissionDetails['rate'] ?? 0;
        $this->baseSalary = $this->existingReport->base_salary ?? 0;
    }

    #[Computed]
    public function invoiceData(): array
    {
        if (!$this->sellerService) {
            $this->initializeSellerService();
        }

        // Luôn tính toán lại previousMonthLoss từ SellerService
        $calculatedPreviousMonthLoss = $this->getPreviousMonthLoss();

        $financialData = $this->sellerService->calculateFinancialData([
            'platformFee' => $this->platformFee,
            'feeDesign' => $this->feeDesign,
            'onHold' => $this->onHold,
            'commission' => $this->commission,
            'baseSalary' => $this->baseSalary,
            'previousMonthLoss' => $calculatedPreviousMonthLoss,
            'dateRange' => $this->dateRange,
        ]);

        // dd($financialData);

        // Kiểm tra nếu không thể tính hoa hồng (lãi trước hoa hồng <= 0)
        if (!($financialData['profit']['canEditCommission'] ?? false) && $this->commission > 0) {
            $this->commission = 0;

            Notification::make()
                ->title('Tỷ lệ hoa hồng đã được điều chỉnh')
                ->warning()
                ->body('Lãi trước hoa hồng <= 0 nên tỷ lệ hoa hồng đã được tự động đặt về 0%.')
                ->duration(30000)
                ->send();

            $financialData = $this->sellerService->calculateFinancialData([
                'platformFee' => $this->platformFee,
                'feeDesign' => $this->feeDesign,
                'onHold' => $this->onHold,
                'commission' => 0,
                'baseSalary' => $this->baseSalary,
                'previousMonthLoss' => $calculatedPreviousMonthLoss,
                'dateRange' => $this->dateRange,
            ]);
        }

        return [
            'invoiceNumber' => 'INV-' . Carbon::now()->format('YmdHis'),
            'issueDate' => Carbon::now()->format('d/m/Y'),
            'dueDate' => Carbon::now()->addDays(7)->format('d/m/Y'),
            'status' => 'Chờ xử lý',
            'startDate' => $this->startDate->format('d/m/Y'),
            'endDate' => $this->endDate->format('d/m/Y'),
            'notes' => $this->notes,

            'seller' => [
                'name' => $this->seller->name,
                'email' => $this->seller->email,
                'phone' => $this->seller->phone_number ?? '(Chưa cập nhật)',
                'telegram' => $this->seller->telegram ?? '(Chưa cập nhật)',
                'bank_name' => $this->seller->bank_name ?? 'Chưa cập nhật',
                'bank_account_number' => $this->seller->bank_account_number ?? 'Chưa cập nhật',
            ],

            'reportPeriod' => $this->startDate->format('d/m/Y') . ' - ' . $this->endDate->format('d/m/Y'),

            'revenue' => $financialData['revenue'],
            'expenses' => $financialData['expenses'],
            'orders' => $financialData['orders'],
            'bankPayments' => $financialData['bankPayments'],
            'tiktokPayout' => $financialData['tiktokPayout'],
            'profit' => $financialData['profit'],
            'income' => $financialData['income'],
        ];
    }

    protected function getViewData(): array
    {
        $data = parent::getViewData();

        $invoiceData = $this->invoiceData;
        $data['invoice'] = $invoiceData;
        $data['invoiceData'] = $invoiceData;
        $data['seller'] = $invoiceData['seller'] ?? [];
        $data['reportPeriod'] = $invoiceData['reportPeriod'] ?? '';
        $data['revenueData'] = $invoiceData['revenue'] ?? [];
        $data['expensesData'] = $invoiceData['expenses'] ?? [];
        $data['profitData'] = $invoiceData['profit'] ?? [];

        // Lấy commission data trực tiếp từ profit data đã được tính toán trong SellerService
        $profitData = $invoiceData['profit'] ?? [];

        $data['commissionData'] = [
            'commission' => $profitData['commission'] ?? 0, // Lấy trực tiếp từ SellerService
            'totalIncomeSeller' => $profitData['totalSellerIncome'] ?? 0,
        ];

        $data['showInvoiceList'] = $this->showInvoiceList;

        return $data;
    }



    protected function validateNonNegativeValues()
    {
        // Không validate previousMonthLoss vì nó được tính toán tự động từ SellerService
        if ($this->platformFee < 0) $this->platformFee = 0;
        if ($this->feeDesign < 0) $this->feeDesign = 0;
        if ($this->onHold < 0) $this->onHold = 0;
        if ($this->commission < 0) $this->commission = 0;
        if ($this->baseSalary < 0) $this->baseSalary = 0;
    }

    public function saveFinanceReport()
    {
        if (!$this->canEdit()) {
            Notification::make()
                ->title('Không có quyền chỉnh sửa')
                ->danger()
                ->body('Bạn không có quyền lưu báo cáo tài chính')
                ->duration(5000)
                ->send();
            return;
        }

        // if (!$this->canSaveReport) {
        //     $lastMonth = Carbon::now()->subMonth();
        //     $lastMonthName = $lastMonth->format('m/Y');

        //     Notification::make()
        //         ->title('Không thể lưu báo cáo')
        //         ->warning()
        //         ->body("Chỉ có thể lưu báo cáo cho tháng trước ({$lastMonthName}) và phải chọn đúng khoảng thời gian từ đầu tháng đến cuối tháng.")
        //         ->duration(8000)
        //         ->send();
        //     return;
        // }

        $invoiceData = $this->invoiceData;

        $costs = [
            'fulfillment_cost' => $invoiceData['expenses']['fulfillmentCost'] ?? 0,
            'advertising_cost' => $invoiceData['expenses']['advertisingCost'] ?? 0,
            'design_cost' => $invoiceData['expenses']['directDesignCost'] ?? 0,
            'print_cost' => $invoiceData['expenses']['printCost'] ?? 0,
            'platform_fee' => $invoiceData['expenses']['platformFee'] ?? 0,
        ];

        $commissionDetails = [
            'rate' => $this->commission,
            'base_salary' => $this->baseSalary,
            'gross_profit' => $invoiceData['profit']['grossProfit'] ?? 0,
            'adjusted_profit' => $invoiceData['profit']['adjustedProfit'] ?? 0,
            'bank_payments' => $invoiceData['bankPayments'] ?? ['sum' => 0, 'count' => 0],
            'tiktok_payments' => $invoiceData['tiktokPayout'] ?? ['sum' => 0, 'count' => 0],
            'orders_data' => $invoiceData['orders'] ?? [],
            'notes' => $this->notes,
        ];

        $monthKey = $this->startDate->startOfMonth()->toDateString();

        DB::beginTransaction();

        try {
            $existingId = DB::table('seller_finances')
                ->where('seller_id', $this->sellerId)
                ->where('month', $monthKey)
                ->value('id');

            $previousMonthLoss = $this->sellerService ? $this->sellerService->getPreviousMonthLoss($this->dateRange) : 0;

            if ($existingId) {
                // Cập nhật báo cáo hiện có
                DB::table('seller_finances')
                    ->where('id', $existingId)
                    ->update([
                        'gross_revenue' => $invoiceData['revenue']['grossRevenue'] ?? 0,
                        'platform_fees' => $invoiceData['revenue']['platformFee'] ?? 0,
                        'net_revenue' => $invoiceData['revenue']['netRevenue'] ?? 0,
                        'costs' => json_encode($costs),
                        'total_cost' => $invoiceData['expenses']['totalExpenses'] ?? 0,
                        'gross_profit' => $invoiceData['profit']['grossProfit'] ?? 0,
                        'net_profit' => $invoiceData['profit']['netProfit'] ?? 0,
                        'adjusted_profit' => $invoiceData['profit']['adjustedProfit'] ?? 0,
                        'previous_loss' => $previousMonthLoss,
                        'base_salary' => $this->baseSalary,
                        'commission' => $invoiceData['profit']['commission'] ?? 0,
                        'total_bonus' => 0,
                        'total_salary' => $invoiceData['profit']['totalSellerIncome'] ?? 0,
                        'payout_on_hold' => $invoiceData['revenue']['onHold'] ?? 0,
                        'commission_details' => json_encode($commissionDetails),
                        'updated_at' => now(),
                    ]);

                $action = 'cập nhật';
            } 
            
            else {
                // Tạo báo cáo mới
                DB::table('seller_finances')->insert([
                    'seller_id' => $this->sellerId,
                    'month' => $monthKey,
                    'gross_revenue' => $invoiceData['revenue']['grossRevenue'] ?? 0,
                    'platform_fees' => $invoiceData['revenue']['platformFee'] ?? 0,
                    'net_revenue' => $invoiceData['revenue']['netRevenue'] ?? 0,
                    'costs' => json_encode($costs),
                    'total_cost' => $invoiceData['expenses']['totalExpenses'] ?? 0,
                    'gross_profit' => $invoiceData['profit']['grossProfit'] ?? 0,
                    'net_profit' => $invoiceData['profit']['netProfit'] ?? 0,
                    'previous_loss' => $previousMonthLoss,
                    'adjusted_profit' => $invoiceData['profit']['adjustedProfit'] ?? 0,
                    'base_salary' => $this->baseSalary,
                    'commission' => $invoiceData['profit']['commission'] ?? 0,
                    'total_bonus' => 0,
                    'total_salary' => $invoiceData['profit']['totalSellerIncome'] ?? 0,
                    'payout_on_hold' => $invoiceData['revenue']['onHold'] ?? 0,
                    'commission_details' => json_encode($commissionDetails),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                $action = 'lưu mới';
            }

            DB::commit();

            Notification::make()
                ->title("Đã {$action} báo cáo tài chính")
                ->success()
                ->body("Báo cáo tài chính của {$this->seller->name} đã được {$action} thành công")
                ->duration(5000)
                ->send();

            return $this;

        } catch (\Exception $e) {
            DB::rollback();

            Notification::make()
                ->title('Lỗi khi lưu báo cáo')
                ->danger()
                ->body('Có lỗi xảy ra khi lưu báo cáo tài chính: ' . $e->getMessage())
                ->duration(8000)
                ->send();

            return;
        }
    }

    protected function getPreviousMonthLoss()
    {
        if ($this->sellerService) {
            return $this->sellerService->getPreviousMonthLoss($this->dateRange);
        }
        return 0.0;
    }

    public function exportAllData()
    {
        try {
            if (!$this->sellerInvoiceService) {
                $this->initializeSellerService();
            }

            $filters = request()->get('tableFilters', []);
            $sellerName = \Str::slug($this->seller->name);
            $dateRange = str_replace(['/', ' ', '-'], '_', $this->dateRange);

            // Danh sách các bảng cần xuất
            $tables = [
                'orders' => 'Đơn hàng',
                'order_items' => 'Chi tiết đơn hàng',
                'fulfillment_cost' => 'Chi phí đi đơn',
                'advertising_cost' => 'Chi phí quảng cáo',
                'design_cost' => 'Chi phí thiết kế',
                'printing_cost' => 'Chi phí in áo',
                'bank_payout' => 'Thanh toán ngân hàng',
                'previous_month_loss' => 'Lỗ tháng trước'
            ];

            // Tạo thư mục tạm để lưu các file CSV
            $tempDir = storage_path('app/temp/exports');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $exportedFiles = [];
            $filePaths = [];

            foreach ($tables as $tableType => $tableName) {
                try {
                    $export = new SellerCsvExport($this->sellerInvoiceService, $tableType, $filters);
                    $fileName = "seller-{$sellerName}-{$tableType}-{$dateRange}.csv";
                    $filePath = $tempDir . '/' . $fileName;

                    // Lưu file CSV vào thư mục tạm
                    Excel::store($export, 'temp/exports/' . $fileName, 'local', \Maatwebsite\Excel\Excel::CSV);

                    if (file_exists($filePath)) {
                        $filePaths[] = $filePath;
                        $exportedFiles[] = $tableName;
                    }
                } catch (\Exception $e) {
                    \Log::error("Error exporting {$tableType}: " . $e->getMessage());
                }
            }

            if (empty($filePaths)) {
                throw new \Exception('Không có file nào được tạo thành công');
            }

            // Tạo file ZIP
            $zipFileName = "seller-{$sellerName}-all-data-{$dateRange}.zip";
            $zipPath = $tempDir . '/' . $zipFileName;

            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
                foreach ($filePaths as $filePath) {
                    $zip->addFile($filePath, basename($filePath));
                }
                $zip->close();

                // Tải xuống file ZIP
                return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
            } else {
                throw new \Exception('Không thể tạo file ZIP');
            }

        } catch (\Exception $e) {
            \Log::error('Error in exportAllData: ' . $e->getMessage());

            Notification::make()
                ->title('Lỗi xuất dữ liệu')
                ->danger()
                ->body('Có lỗi xảy ra khi xuất dữ liệu: ' . $e->getMessage())
                ->duration(8000)
                ->send();
        }
    }

    public function getTableRecordKey($record): string
    {
        // Xử lý cho các record thông thường
        if (is_object($record) && isset($record->id)) {
            return (string) $record->id;
        }

        if (is_array($record) && isset($record['id'])) {
            return (string) $record['id'];
        }

        // Fallback cho các trường hợp đặc biệt
        if (is_object($record)) {
            // Sử dụng các thuộc tính khác để tạo unique key
            $key = '';
            if (isset($record->time)) $key .= $record->time;
            if (isset($record->amount)) $key .= $record->amount;
            if (isset($record->created_at)) $key .= $record->created_at;

            if ($key) {
                return md5($key);
            }
        }

        // Fallback cuối cùng
        return (string) uniqid();
    }

    // Tab Tables Methods
    public function getOrdersTable(Table $table): Table
    {
        if (!$this->sellerInvoiceService) {
            $this->initializeSellerService();
        }

        if (!$this->sellerInvoiceService) {
            // Fallback nếu vẫn không khởi tạo được
            return $table->query(Order::query()->whereRaw('1 = 0'));
        }

        return $table
            ->query($this->sellerInvoiceService->getOrdersQuery())
            ->columns([
                TextColumn::make('order_number')
                    ->label('Mã đơn hàng')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('store.name')
                    ->label('Cửa hàng')
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ($state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state) {
                        'Completed' => 'Hoàn thành',
                        'Processing' => 'Đang xử lý',
                        'Cancelled' => 'Đã hủy',
                        'Pending' => 'Chờ xử lý',
                        default => $state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state,
                    })
                    ->color(fn ($state): string => match ($state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state) {
                        'Completed' => 'success',
                        'Processing' => 'info',
                        'Pending' => 'warning',
                        'Cancelled' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('total')
                    ->label('Tổng tiền')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()->money('USD')->label('Tổng doanh thu'),
                        Count::make()->label('Tổng đơn hàng'),
                    ]),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_excel')
                    ->label('Xuất Excel')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerExport($this->sellerInvoiceService, 'orders', $filters);
                        return Excel::download($export, $export->getFileName());
                    }),
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerCsvExport($this->sellerInvoiceService, 'orders', $filters);
                        return Excel::download($export, $export->getFileName(), \Maatwebsite\Excel\Excel::CSV);
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'Processing' => 'Đang xử lý',
                        'AwaitingShipment' => 'Chờ vận chuyển',
                        'Completed' => 'Hoàn thành',
                        'OnHold' => 'Tạm giữ',
                        'Refunded' => 'Hoàn tiền',
                        'Cancelled' => 'Đã hủy',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có đơn hàng nào')
            ->emptyStateDescription('Không tìm thấy đơn hàng nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-shopping-cart');
    }

    public function getFulfillmentCostTable(Table $table): Table
    {
        if (!$this->sellerInvoiceService) {
            $this->initializeSellerService();
        }

        if (!$this->sellerInvoiceService) {
            // Fallback nếu vẫn không khởi tạo được
            return $table->query(SupplierOrder::query()->whereRaw('1 = 0'));
        }

        return $table
            ->query($this->sellerInvoiceService->getFulfillmentCostQuery())
            ->columns([
                TextColumn::make('order.order_number')
                    ->label('Mã đơn hàng')
                    ->searchable(),
                TextColumn::make('supplier.name')
                    ->label('Nhà cung cấp')
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ($state instanceof \App\Enums\SupplierOrderStatus ? $state->value : (string) $state) {
                        'Completed' => 'Hoàn thành',
                        'InProducing' => 'Đang sản xuất',
                        'AwaitingShipment' => 'Chờ vận chuyển',
                        'Cancelled' => 'Đã hủy',
                        'Pending' => 'Chờ xử lý',
                        'OnHold' => 'Tạm giữ',
                        'Refunded' => 'Hoàn tiền',
                        default => $state instanceof \App\Enums\SupplierOrderStatus ? $state->value : (string) $state,
                    })
                    ->color(fn ($state): string => match ($state instanceof \App\Enums\SupplierOrderStatus ? $state->value : (string) $state) {
                        'Completed' => 'success',
                        'InProducing' => 'info',
                        'AwaitingShipment' => 'warning',
                        'Pending' => 'warning',
                        'OnHold' => 'danger',
                        'Cancelled' => 'danger',
                        'Refunded' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('base_cost')
                    ->label('Chi phí')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()->money('USD')->label('Tổng chi phí đi đơn'),
                        Count::make()->label('Tổng số đơn'),
                    ]),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_excel')
                    ->label('Xuất Excel')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerExport($this->sellerInvoiceService, 'fulfillment_cost', $filters);
                        return Excel::download($export, $export->getFileName());
                    }),
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerCsvExport($this->sellerInvoiceService, 'fulfillment_cost', $filters);
                        return Excel::download($export, $export->getFileName(), \Maatwebsite\Excel\Excel::CSV);
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'Pending' => 'Chờ xử lý',
                        'AwaitingShipment' => 'Chờ vận chuyển',
                        'InProducing' => 'Đang sản xuất',
                        'Completed' => 'Hoàn thành',
                        'OnHold' => 'Tạm giữ',
                        'Cancelled' => 'Đã hủy',
                        'Refunded' => 'Hoàn tiền',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có chi phí đi đơn nào')
            ->emptyStateDescription('Không tìm thấy chi phí đi đơn nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-truck');
    }

    public function getAdvertisingCostTable(Table $table): Table
    {
        if (!$this->sellerInvoiceService) {
            $this->initializeSellerService();
        }

        if (!$this->sellerInvoiceService) {
            // Fallback nếu vẫn không khởi tạo được
            return $table->query(SellerFundRequest::query()->whereRaw('1 = 0'));
        }

        return $table
            ->query($this->sellerInvoiceService->getAdvertisingCostQuery())
            ->columns([
                TextColumn::make('amount')
                    ->label('Số tiền')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()->money('USD')->label('Tổng chi phí quảng cáo'),
                        Count::make()->label('Tổng yêu cầu'),
                    ]),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ((string) $state) {
                        'approved' => 'Đã duyệt',
                        'pending' => 'Chờ duyệt',
                        'rejected' => 'Từ chối',
                        default => (string) $state,
                    })
                    ->color(fn ($state): string => match ((string) $state) {
                        'approved' => 'success',
                        'pending' => 'warning',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('description')
                    ->label('Mô tả')
                    ->limit(50),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_excel')
                    ->label('Xuất Excel')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerExport($this->sellerInvoiceService, 'advertising_cost', $filters);
                        return Excel::download($export, $export->getFileName());
                    }),
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerCsvExport($this->sellerInvoiceService, 'advertising_cost', $filters);
                        return Excel::download($export, $export->getFileName(), \Maatwebsite\Excel\Excel::CSV);
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'pending' => 'Chờ duyệt',
                        'approved' => 'Đã duyệt',
                        'rejected' => 'Từ chối',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có chi phí quảng cáo nào')
            ->emptyStateDescription('Không tìm thấy chi phí quảng cáo nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-megaphone');
    }

    public function getDesignCostTable(Table $table): Table
    {
        if (!$this->sellerInvoiceService) {
            $this->initializeSellerService();
        }

        if (!$this->sellerInvoiceService) {
            // Fallback nếu vẫn không khởi tạo được
            return $table->query(DesignJob::query()->whereRaw('1 = 0'));
        }

        return $table
            ->query($this->sellerInvoiceService->getDesignCostQuery())
            ->columns([
                TextColumn::make('design.name')
                    ->label('Tên thiết kế')
                    ->searchable(),
                TextColumn::make('designer.name')
                    ->label('Designer')
                    ->searchable(),
                TextColumn::make('price')
                    ->label('Giá cơ bản')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()
                            ->money('USD')
                            ->label('Tổng chi phí thiết kế')
                            ->using(function ($query) {
                                return $query->sum(\DB::raw('COALESCE(price, 0) + COALESCE(rush_fee, 0)'));
                            }),
                        Count::make()->label('Tổng job'),
                    ]),
                TextColumn::make('rush_fee')
                    ->label('Phí gấp')
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('total_cost')
                    ->label('Tổng chi phí')
                    ->state(function ($record) {
                        return ($record->price ?? 0) + ($record->rush_fee ?? 0);
                    })
                    ->money('USD'),
                TextColumn::make('completed_at')
                    ->label('Ngày hoàn thành')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_excel')
                    ->label('Xuất Excel')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerExport($this->sellerInvoiceService, 'design_cost', $filters);
                        return Excel::download($export, $export->getFileName());
                    }),
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerCsvExport($this->sellerInvoiceService, 'design_cost', $filters);
                        return Excel::download($export, $export->getFileName(), \Maatwebsite\Excel\Excel::CSV);
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'pending' => 'Chờ xử lý',
                        'assigned' => 'Đã phân công',
                        'in_progress' => 'Đang thực hiện',
                        'under_review' => 'Chờ duyệt',
                        'needs_revision' => 'Cần sửa',
                        'completed' => 'Hoàn thành',
                        'cancelled' => 'Đã hủy',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('completed_at', 'desc')
            ->emptyStateHeading('Không có chi phí thiết kế nào')
            ->emptyStateDescription('Không tìm thấy chi phí thiết kế nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-paint-brush');
    }

    public function getPrintingCostTable(Table $table): Table
    {
        if (!$this->sellerInvoiceService) {
            $this->initializeSellerService();
        }

        if (!$this->sellerInvoiceService) {
            // Fallback nếu vẫn không khởi tạo được
            return $table->query(Production::query()->whereRaw('1 = 0'));
        }

        return $table
            ->query($this->sellerInvoiceService->getPrintingCostQuery())
            ->columns([
                TextColumn::make('blank.name')
                    ->label('Loại phôi')
                    ->searchable(),
                TextColumn::make('quantity')
                    ->label('Số lượng')
                    ->sortable()
                    ->summarize([
                        Sum::make()->label('Tổng số lượng'),
                        Count::make()->label('Tổng production'),
                    ]),
                TextColumn::make('cost_per_unit')
                    ->label('Giá/đơn vị')
                    ->money('USD')
                    ->state(function ($record) {
                        return $record->calculateProductionCost() / max($record->quantity, 1);
                    }),
                TextColumn::make('total_cost')
                    ->label('Tổng chi phí')
                    ->money('USD')
                    ->state(function ($record) {
                        return $record->calculateProductionCost();
                    }),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ((string) $state) {
                        'completed' => 'Hoàn thành',
                        'processing' => 'Đang xử lý',
                        'pending' => 'Chờ xử lý',
                        default => (string) $state,
                    })
                    ->color(fn ($state): string => match ((string) $state) {
                        'completed' => 'success',
                        'processing' => 'info',
                        'pending' => 'warning',
                        default => 'gray',
                    }),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_excel')
                    ->label('Xuất Excel')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerExport($this->sellerInvoiceService, 'printing_cost', $filters);
                        return Excel::download($export, $export->getFileName());
                    }),
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerCsvExport($this->sellerInvoiceService, 'printing_cost', $filters);
                        return Excel::download($export, $export->getFileName(), \Maatwebsite\Excel\Excel::CSV);
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'pending' => 'Chờ xử lý',
                        'in_production' => 'Đang sản xuất',
                        'completed' => 'Hoàn thành',
                        'rejected' => 'Từ chối',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có chi phí in áo nào')
            ->emptyStateDescription('Không tìm thấy chi phí in áo nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-printer');
    }

    public function getBankPayoutTable(Table $table): Table
    {
        if (!$this->sellerInvoiceService) {
            $this->initializeSellerService();
        }

        if (!$this->sellerInvoiceService) {
            // Fallback nếu vẫn không khởi tạo được
            return $table->query(PayoutTransaction::query()->whereRaw('1 = 0'));
        }

        // Chỉ hiển thị bank payout để tránh union query phức tạp
        return $table
            ->query($this->sellerInvoiceService->getBankPayoutQuery())
            ->columns([
                TextColumn::make('type')
                    ->label('Loại')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ((string) $state) {
                        'Receive' => 'Nhận tiền',
                        'Send' => 'Gửi tiền',
                        default => (string) $state,
                    })
                    ->color(fn ($state): string => match ((string) $state) {
                        'Receive' => 'success',
                        'Send' => 'info',
                        default => 'gray',
                    }),
                TextColumn::make('amount')
                    ->label('Số tiền')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()->money('USD')->label('Tổng bank payout'),
                        Count::make()->label('Tổng giao dịch'),
                    ]),
                TextColumn::make('card_no')
                    ->label('Số tài khoản')
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ((string) $state) {
                        'Success' => 'Thành công',
                        'Pending' => 'Chờ xử lý',
                        'Failed' => 'Thất bại',
                        default => (string) $state,
                    })
                    ->color(fn ($state): string => match ((string) $state) {
                        'Success' => 'success',
                        'Pending' => 'warning',
                        'Failed' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('time')
                    ->label('Thời gian')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_excel')
                    ->label('Xuất Excel')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerExport($this->sellerInvoiceService, 'bank_payout', $filters);
                        return Excel::download($export, $export->getFileName());
                    }),
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerCsvExport($this->sellerInvoiceService, 'bank_payout', $filters);
                        return Excel::download($export, $export->getFileName(), \Maatwebsite\Excel\Excel::CSV);
                    }),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->label('Loại giao dịch')
                    ->multiple()
                    ->options([
                        'Receive' => 'Nhận tiền',
                        'Send' => 'Gửi tiền',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('type', $data['values'])
                            : $query;
                    }),
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'Success' => 'Thành công',
                        'Pending' => 'Chờ xử lý',
                        'Failed' => 'Thất bại',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('time', 'desc')
            ->emptyStateHeading('Không có giao dịch nào')
            ->emptyStateDescription('Không tìm thấy giao dịch nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-banknotes');
    }

    public function getOrderItemsTable(Table $table): Table
    {
        if (!$this->sellerInvoiceService) {
            $this->initializeSellerService();
        }

        if (!$this->sellerInvoiceService) {
            // Fallback nếu vẫn không khởi tạo được
            return $table->query(OrderItem::query()->whereRaw('1 = 0'));
        }

        return $table
            ->query($this->sellerInvoiceService->getOrderItemsQuery())
            ->heading('Chi tiết đơn hàng (' . $this->formatDateRange($this->startDate, $this->endDate) . ')')
            ->columns([
                TextColumn::make('order.order_number')
                    ->label('Mã đơn hàng')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('product.name')
                    ->label('Tên sản phẩm')
                    ->searchable()
                    ->limit(30)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),

                TextColumn::make('product.sku')
                    ->label('SKU sản phẩm')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('quantity')
                    ->label('Số lượng')
                    ->numeric()
                    ->sortable()
                    ->summarize(Sum::make()->label('Tổng số lượng')),

                TextColumn::make('price')
                    ->label('Đơn giá')
                    ->money('USD')
                    ->sortable(),

                TextColumn::make('total')
                    ->label('Thành tiền')
                    ->money('USD')
                    ->sortable()
                    ->summarize(Sum::make()->label('Tổng thành tiền')->money('USD')),

                TextColumn::make('order.status')
                    ->label('Trạng thái đơn hàng')
                    ->badge()
                    ->color(function ($state): string {
                        $statusValue = $state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state;
                        return match ($statusValue) {
                            'Processing' => 'warning',
                            'AwaitingShipment' => 'info',
                            'Completed' => 'success',
                            'OnHold' => 'gray',
                            'Refunded' => 'danger',
                            'Cancelled' => 'danger',
                            default => 'gray',
                        };
                    })
                    ->formatStateUsing(function ($state): string {
                        $statusValue = $state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state;
                        return match ($statusValue) {
                            'Processing' => 'Đang xử lý',
                            'AwaitingShipment' => 'Chờ vận chuyển',
                            'Completed' => 'Hoàn thành',
                            'OnHold' => 'Tạm giữ',
                            'Refunded' => 'Hoàn tiền',
                            'Cancelled' => 'Đã hủy',
                            default => $statusValue,
                        };
                    }),

                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_excel')
                    ->label('Xuất Excel')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerExport($this->sellerInvoiceService, 'order_items', $filters);
                        return Excel::download($export, $export->getFileName());
                    }),
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        $filters = request()->get('tableFilters', []);
                        $export = new SellerCsvExport($this->sellerInvoiceService, 'order_items', $filters);
                        return Excel::download($export, $export->getFileName(), \Maatwebsite\Excel\Excel::CSV);
                    }),
            ])
            ->filters([
                SelectFilter::make('order.status')
                    ->label('Trạng thái đơn hàng')
                    ->multiple()
                    ->options([
                        'Processing' => 'Đang xử lý',
                        'AwaitingShipment' => 'Chờ vận chuyển',
                        'Completed' => 'Hoàn thành',
                        'OnHold' => 'Tạm giữ',
                        'Refunded' => 'Hoàn tiền',
                        'Cancelled' => 'Đã hủy',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['values'])) {
                            return $query;
                        }

                        return $query->whereHas('order', function ($q) use ($data) {
                            $q->whereIn('status', $data['values']);
                        });
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->emptyStateHeading('Không có chi tiết đơn hàng nào')
            ->emptyStateDescription('Không tìm thấy chi tiết đơn hàng nào trong khoảng thời gian bạn đã chọn')
            ->emptyStateIcon('heroicon-o-shopping-bag');
    }

}