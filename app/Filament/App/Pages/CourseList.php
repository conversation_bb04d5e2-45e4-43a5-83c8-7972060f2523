<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Models\Course;
use App\Models\Lesson;
use Livewire\Attributes\Url;

class CourseList extends Page
{
    protected static string $view = 'filament.app.pages.course-list';
    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static bool $shouldRegisterNavigation = false;


    #[Url]
    public $courseId = null;

    #[Url]
    public $sessionId = null;

    #[Url]
    public $lessonId = null;

    // #[Url(as: 'lessonId')]
    // public $lessonId2 = null;

    public $completedLessons = [];
    public $searchQuery = '';

    public function mount()
    {


        if ($this->courseId) {
            $this->completedLessons = auth()->user()
                ->completedLessons()
                ->whereHas('session', function($query) {
                    $query->where('course_id', $this->courseId);
                })
                ->pluck('lessons.id')
                ->toArray();
        }
    }

    public function getViewData(): array
    {
        if (!$this->courseId) {
            return [
                'courses' => $this->getAllCourses(),
                'course' => null,
                'sessions' => collect(),
                'completedLessons' => [],
                'currentLesson' => null,
                'progress' => 0,
                'totalFilteredLessons' => 0,
                'nextLesson' => null,
                'previousLesson' => null,
            ];
        }

        $course = $this->getCurrentCourse();
        $sessions = $course?->sessions ?? collect();
        $currentLesson = $this->getCurrentLesson();

        return [
            'courses' => collect(),
            'course' => $course,
            'sessions' => $sessions,
            'completedLessons' => $this->completedLessons,
            'currentLesson' => $currentLesson,
            'progress' => $this->calculateProgress($course),
            'totalFilteredLessons' => $sessions->sum(fn($s) => $s->lessons->count()),
            'nextLesson' => $currentLesson?->getNextLesson(),
            'previousLesson' => $currentLesson?->getPreviousLesson(),
        ];
    }

    public function getAllCourses()
    {
        return Course::with(['sessions' => function($query) {
            $query->withCount('lessons')
                ->withSum('lessons', 'minutes');
        }])->get();
    }

    public function selectLesson($lessonId, $sessionId)
    {
        $this->lessonId = $lessonId;
        $this->sessionId = $sessionId;
        $this->dispatch('scroll-to-session', sessionId: $sessionId);
    }

    public function getCurrentCourse()
    {
        return Course::with(['sessions.lessons' => function($query) {
            $query->orderBy('order');
        }])->find($this->courseId);
    }

    public function getCurrentLesson()
    {
        if (!$this->lessonId) {
            return null;
        }
        return Lesson::find($this->lessonId);
    }

    public function calculateProgress($course)
    {
        if (!$course) return 0;
        
        $totalLessons = $course->lessons()->count();
        if ($totalLessons === 0) return 0;
        
        $completedCount = count($this->completedLessons);
        return round(($completedCount / $totalLessons) * 100);
    }

    public function showSessionLessons($sessionId = null)
    {
        $this->sessionId = $sessionId;
        $this->lessonId = null;
        
        if ($sessionId) {
            $this->dispatch('scroll-to-session', sessionId: $sessionId);
        }
    }

    public function getCourseBreadcrumbs()
    {
        $breadcrumbs = [];
        
        if ($this->courseId) {
            $course = $this->getCurrentCourse();
            $breadcrumbs[] = [
                'label' => $course?->name,
                'link' => true,
                'action' => 'showSessionLessons(null)',
                'highlight' => false
            ];

            if ($this->lessonId) {
                $lesson = $this->getCurrentLesson();
                $session = $lesson?->session;
                
                if ($session) {
                    $breadcrumbs[] = [
                        'label' => $session->name,
                        'link' => true,
                        'action' => 'showSessionLessons(' . $session->id . ')',
                        'highlight' => false
                    ];
                }
                
                $breadcrumbs[] = [
                    'label' => $lesson?->title,
                    'link' => false,
                    'action' => null,
                    'highlight' => true
                ];
            }
        }

        return $breadcrumbs;
    }

    public function getNextLesson()
    {
        if (!$this->lessonId) return null;
        $course = $this->getCurrentCourse();
        $currentLesson = $this->getCurrentLesson();
        
        $allLessons = $course->lessons()->orderBy('session_id')->orderBy('id')->get();
        $currentIndex = $allLessons->search(fn($lesson) => $lesson->id === $currentLesson->id);
        
        return $currentIndex !== false && $currentIndex < $allLessons->count() - 1
            ? $allLessons[$currentIndex + 1]
            : null;
    }

    public function getPreviousLesson()
    {
        if (!$this->lessonId) return null;
        $course = $this->getCurrentCourse();
        $currentLesson = $this->getCurrentLesson();
        
        $allLessons = $course->lessons()->orderBy('session_id')->orderBy('id')->get();
        $currentIndex = $allLessons->search(fn($lesson) => $lesson->id === $currentLesson->id);
        
        return $currentIndex !== false && $currentIndex > 0
            ? $allLessons[$currentIndex - 1]
            : null;
    }

    public function toggleLessonCompletion($lessonId)
    {
        $lesson = Lesson::findOrFail($lessonId);
        $user = auth()->user();

        if ($user->hasCompletedLesson($lesson)) {
            $user->completedLessons()->detach($lesson);
        } else {
            $user->completedLessons()->attach($lesson);
        }

        // Reload completed lessons after toggling
        $this->completedLessons = auth()->user()
            ->completedLessons()
            ->whereHas('session', function($query) {
                $query->where('course_id', $this->courseId);
            })
            ->pluck('lessons.id')
            ->toArray();
    }
}