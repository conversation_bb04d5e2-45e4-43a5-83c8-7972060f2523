<?php

namespace App\Filament\App\Pages;

use App\Enums\TiktokShopStatus;
use App\Models\Store;
use App\Models\Order;
use App\Services\StoreService;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Carbon\Carbon;
use Filament\Pages\Page;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Url;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class StoreRevenueOverview extends Page
{
    use HasPageShield;

    // Cấu hình hiển thị trong sidebar
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';
    protected static ?string $navigationLabel = 'Thống kê doanh thu cửa hàng';
    protected static ?string $navigationGroup = 'Store Manager';
    protected static ?int $navigationSort = 2;

    // Cấu hình slug URL
    protected static ?string $slug = 'store-revenue-overview/{selectedStore?}';

    protected static string $view = 'filament.app.pages.store-revenue-overview';
    protected static ?string $title = 'Thống kê doanh thu cửa hàng';

    // Thuộc tính để lưu trữ khoảng thời gian
    protected $startDate;
    protected $endDate;

    // Form data cho filters
    public ?array $data = [];

    // Sử dụng URL parameters
    #[Url(except: '')]
    public $dateRange = null;

    #[Url(except: '')]
    public $selectedStore = null;



    /**
     * Kiểm tra quyền truy cập vào trang
     */
    public static function canAccess(array $parameters = []): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    /**
     * Xác định xem trang có nên hiển thị trong menu navigation không
     */
    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    /**
     * Tạo URL cho navigation (không có selectedStore)
     */
    public static function getNavigationUrl(array $parameters = []): string
    {
        return static::getUrl();
    }

    public function mount($selectedStore = null)
    {
        // Nếu có selectedStore từ URL parameter, sử dụng nó
        if ($selectedStore) {
            $this->selectedStore = (int) $selectedStore;
        } else {
            // Mặc định là null (tất cả cửa hàng)
            $this->selectedStore = null;
        }

        // Thiết lập giá trị mặc định cho khoảng thời gian (tháng hiện tại)
        if (empty($this->dateRange)) {
            $startDate = now()->startOfMonth()->format('d/m/Y');
            $endDate = now()->endOfMonth()->format('d/m/Y');
            $this->dateRange = $startDate . ' - ' . $endDate;
        }

        // Phân tích khoảng thời gian để thiết lập startDate và endDate
        $this->parseDateRange($this->dateRange);

        // Khởi tạo form data
        $this->form->fill([
            'store_id' => $this->selectedStore,
            'dateRange' => $this->dateRange,
        ]);

        // Debug: Kiểm tra store có tồn tại không (chỉ khi có selectedStore)
        if ($this->selectedStore) {
            $store = Store::find($this->selectedStore);
            if (!$store) {
                // Nếu store không tồn tại, reset về "Tất cả"
                $this->selectedStore = null;
                $this->form->fill([
                    'store_id' => '',
                    'dateRange' => $this->dateRange,
                ]);
            }
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('store_id')
                    ->label('Cửa hàng')
                    ->options(function () {
                        return Store::query()
                            ->with('seller')
                            ->orderBy('name')
                            ->get()
                            ->mapWithKeys(function ($store) {
                                $label = $store->name;
                                if ($store->seller) {
                                    $label .= ' (' . $store->seller->name . ')';
                                }
                                // Thêm status indicator
                                $statusLabel = match($store->tiktok_shop_status) {
                                    TiktokShopStatus::Live => ' [Live]',
                                    TiktokShopStatus::Suspended => ' [Suspended]',
                                    TiktokShopStatus::NotConnected => ' [Not Connected]',
                                    default => ''
                                };
                                $label .= $statusLabel;
                                return [$store->id => $label];
                            })
                            ->toArray();
                    })
                    ->searchable()
                    ->placeholder('Chọn cửa hàng')
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->selectedStore = $state ? (int) $state : null;
                        $this->dispatch('filtersUpdated');
                    }),

                DateRangePicker::make('dateRange')
                    ->label('Khoảng thời gian')
                    ->timezone(config('app.timezone'))
                    ->displayFormat('D/M/Y')
                    ->format('d/m/Y')
                    ->separator(' - ')
                    ->ranges([
                        'Tháng này' => [now()->startOfMonth(), now()->endOfMonth()],
                        'Tháng trước' => [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth(),
                        ],
                        '3 tháng gần nhất' => [
                            now()->subMonths(2)->startOfMonth(),
                            now()->endOfMonth(),
                        ],
                        'Quý này' => [
                            now()->startOfQuarter(),
                            now()->endOfQuarter(),
                        ],
                        'Năm nay' => [now()->startOfYear(), now()->endOfYear()],
                        'Tất cả' => [now()->subYear(), now()],
                    ])
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->dateRange = $state;
                        $this->parseDateRange($this->dateRange);
                        $this->dispatch('filtersUpdated');
                    }),
            ])
            ->columns(2)
            ->statePath('data');
    }



    /**
     * Phân tích chuỗi dateRange thành startDate và endDate
     */
    protected function parseDateRange($dateRange = null)
    {
        // Nếu không có dateRange, sử dụng giá trị mặc định (tháng hiện tại)
        if (empty($dateRange)) {
            $this->startDate = now()->startOfMonth()->startOfDay();
            $this->endDate = now()->endOfMonth()->endOfDay();
            return;
        }

        try {
            // Phân tích chuỗi dateRange (format: 'd/m/Y - d/m/Y')
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                $this->startDate = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                $this->endDate = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                return;
            }
        } catch (\Exception $e) {
            // Xử lý lỗi nếu có
            Log::error('Lỗi khi phân tích dateRange: ' . $e->getMessage());

            // Sử dụng giá trị mặc định nếu có lỗi
            $this->startDate = now()->startOfMonth()->startOfDay();
            $this->endDate = now()->endOfMonth()->endOfDay();
        }
    }

    /**
     * Áp dụng bộ lọc thời gian cho query
     */
    protected function applyDateFilter($query, string $dateColumn = 'created_at')
    {
        // Đảm bảo startDate và endDate đã được parse
        if (!$this->startDate || !$this->endDate) {
            $this->parseDateRange($this->dateRange);
        }

        // Áp dụng filter thời gian
        $query->whereBetween($dateColumn, [$this->startDate, $this->endDate]);

        return $query;
    }



    /**
     * Lấy danh sách stores theo filter
     */
    protected function getFilteredStores()
    {
        $query = Store::query()->with(['seller', 'orders']);

        // Chỉ lấy store được chọn
        if (!empty($this->selectedStore)) {
            // Cast selectedStore to integer để đảm bảo kiểu dữ liệu đúng
            $storeId = (int) $this->selectedStore;
            $query->where('id', $storeId);
        } else {
            // Nếu không có store được chọn, trả về collection rỗng
            return collect();
        }

        return $query->get();
    }

    /**
     * Tạo StoreService instance cho store
     */
    protected function getStoreService(Store $store): StoreService
    {
        // Đảm bảo startDate và endDate đã được parse
        if (!$this->startDate || !$this->endDate) {
            $this->parseDateRange($this->dateRange);
        }

        return new StoreService($store, $this->startDate, $this->endDate);
    }

    /**
     * Lấy thống kê chi tiết cho dashboard - Sử dụng StoreService
     */
    public function getDetailedStats(): array
    {
        // Lấy danh sách stores theo filter hiện tại
        $stores = $this->getFilteredStores();

        $stats = [
            'total_stores' => $stores->count(),
            'total_orders' => 0,
            'completed_orders' => 0,
            'failed_orders' => 0,
            'pending_orders' => 0,
            'total_revenue' => 0,
            'completed_revenue' => 0,
            'pending_revenue' => 0,
            'total_cost' => 0,
            'fulfillment_cost' => 0,
            'advertising_cost' => 0,
            'design_cost' => 0,
            'print_cost' => 0,
            'net_profit' => 0,
            'profit_margin' => 0,
            'total_on_hold' => 0,
            'success_rate' => 0,
            'avg_order_value' => 0,
            'bank_payout_sum' => 0,
            'bank_payout_count' => 0,
            'tiktok_payout_sum' => 0,
            'tiktok_payout_count' => 0,
        ];

        foreach ($stores as $store) {
            // Sử dụng StoreService để tính toán
            $storeService = $this->getStoreService($store);
            $storeData = $storeService->calculateFinancialData();

            // Tổng hợp dữ liệu
            $stats['total_orders'] += $storeData['orders']['total'];
            $stats['completed_orders'] += $storeData['orders']['completed'];
            $stats['failed_orders'] += $storeData['orders']['failed'];
            $stats['pending_orders'] += $storeData['orders']['pending'];

            $stats['total_revenue'] += $storeData['revenue']['total'];
            $stats['completed_revenue'] += $storeData['revenue']['completed'];
            $stats['pending_revenue'] += $storeData['revenue']['pending'];

            $stats['fulfillment_cost'] += $storeData['costs']['fulfillment'];
            $stats['advertising_cost'] += $storeData['costs']['advertising'];
            $stats['design_cost'] += $storeData['costs']['design'];
            $stats['print_cost'] += $storeData['costs']['print'];
            $stats['total_cost'] += $storeData['costs']['total'];

            $stats['total_on_hold'] += $storeData['metrics']['on_hold'];

            $stats['bank_payout_sum'] += $storeData['payout']['bank']['sum'];
            $stats['bank_payout_count'] += $storeData['payout']['bank']['count'];

            $stats['tiktok_payout_sum'] += $storeData['payout']['tiktok']['sum'];
            $stats['tiktok_payout_count'] += $storeData['payout']['tiktok']['count'];
        }

        // Tính toán các chỉ số phái sinh
        $stats['net_profit'] = $stats['completed_revenue'] - $stats['total_cost'];
        $stats['profit_margin'] = $stats['completed_revenue'] > 0 ?
            (($stats['net_profit'] / $stats['completed_revenue']) * 100) : 0;
        $stats['success_rate'] = $stats['total_orders'] > 0 ?
            (($stats['completed_orders'] / $stats['total_orders']) * 100) : 0;
        $stats['avg_order_value'] = $stats['completed_orders'] > 0 ?
            ($stats['completed_revenue'] / $stats['completed_orders']) : 0;

        return $stats;
    }

    /**
     * Lấy thông tin store được chọn
     */
    public function getSelectedStoreInfo(): ?array
    {
        if (!$this->selectedStore) {
            return null;
        }

        $store = Store::with(['seller'])
            ->withCount('orders')
            ->find($this->selectedStore);

        if (!$store) {
            return null;
        }

        return [
            'store' => $store,
            'seller' => $store->seller,
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            // Có thể thêm các action khác ở đây nếu cần
        ];
    }
}
