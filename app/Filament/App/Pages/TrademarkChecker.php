<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Models\Trademark;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;

class TrademarkChecker extends Page implements HasForms
{
    use InteractsWithForms;
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin']) ;
    }
    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $navigationIcon = 'heroicon-o-magnifying-glass';
    protected static ?string $navigationGroup = 'Ideas';
    protected static ?string $navigationLabel = 'Kiểm Tra Trademark';
    protected static ?int $navigationSort = 2;

    protected static string $view = 'filament.app.pages.trademark-checker';

    public ?array $data = [];
    public ?string $highlightedText = null;
    public array $foundTrademarks = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('title')
                    ->label('Tiêu đề sản phẩm')
                    ->required()
                    ->placeholder('Nhập tiêu đề sản phẩm cần kiểm tra')
                    ->columnSpanFull(),
            ])
            ->statePath('data');
    }

    public function check(): void
    {
        $this->validate();
        
        $title = $this->data['title'] ?? '';
        
        if (empty($title)) {
            Notification::make()
                ->title('Vui lòng nhập tiêu đề sản phẩm')
                ->warning()
                ->send();
            return;
        }
        
        $this->checkTitle($title);
    }
    
    private function checkTitle(string $title): void
    {
        $trademarks = Trademark::all()->pluck('keyword')->toArray();
        $this->foundTrademarks = [];
        
        $highlightedText = $title;
        
        foreach ($trademarks as $trademark) {
            $pattern = '/\b' . preg_quote($trademark, '/') . '\b/i';
            
            if (preg_match($pattern, $title)) {
                $this->foundTrademarks[] = $trademark;
                $highlightedText = preg_replace(
                    $pattern, 
                    '<span class="bg-red-200 text-red-800 px-1 rounded">$0</span>', 
                    $highlightedText
                );
            }
        }
        
        $this->highlightedText = $highlightedText;
        
        if (empty($this->foundTrademarks)) {
            Notification::make()
                ->title('Không tìm thấy từ khóa Trademark')
                ->description('Tiêu đề này an toàn để sử dụng')
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Tìm thấy ' . count($this->foundTrademarks) . ' từ khóa Trademark')
                ->description('Vui lòng sửa lại tiêu đề để tránh vi phạm')
                ->warning()
                ->send();
        }
    }
    
    public function getTrademarkCountProperty(): int
    {
        return Trademark::count();
    }
}
