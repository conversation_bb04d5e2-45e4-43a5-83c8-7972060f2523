<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\SupplierOrder;
use App\Models\SellerFundRequest;
use App\Models\Production;
use App\Models\DesignJob;
use App\Models\PayoutTransaction;
use App\Models\TikTokPayment;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\Summarizers\Count;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action as TableAction;
use App\Exports\SellerExport;
use App\Exports\SellerCsvExport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Eloquent\Builder;
use Filament\Notifications\Notification;
use Livewire\Attributes\Url;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class SellerData extends Page implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationLabel = 'Seller Data';
    protected static ?string $title = 'Dữ liệu tổng hợp Seller';
    protected static ?string $navigationGroup = 'Seller Management';
    protected static ?int $navigationSort = 2;
    protected static string $view = 'filament.app.pages.seller-data';

    public static function canAccess(): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }

        // Chỉ cho phép super_admin, User Manager, Developer và Accountant truy cập
        return $user->hasAnyRole(['super_admin', 'User Manager', 'Developer', 'Accountant']);
    }

    #[Url(except: '')]
    public $dateRange = null;

    #[Url(except: 'orders')]
    public $activeTab = 'orders';

    protected $startDate;
    protected $endDate;

    public function switchTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetTable();
        // Force refresh để đảm bảo filters và actions xuất hiện
        $this->dispatch('$refresh');
    }

    public function mount()
    {
        // Thời gian mặc định là tháng hiện tại
        $thisMonth = Carbon::now();
        $startDate = $thisMonth->copy()->startOfMonth()->format('d/m/Y');
        $endDate = $thisMonth->copy()->endOfMonth()->format('d/m/Y');
        $this->dateRange = $startDate . ' - ' . $endDate;

        $this->parseDateRange($this->dateRange);
    }

    protected function parseDateRange(string $dateRange = null)
    {
        if (empty($dateRange)) {
            $thisMonth = Carbon::now();
            $this->startDate = $thisMonth->copy()->startOfMonth()->startOfDay();
            $this->endDate = $thisMonth->copy()->endOfMonth()->endOfDay();
            return;
        }

        $dates = explode(' - ', $dateRange);
        if (count($dates) == 2) {
            $this->startDate = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
            $this->endDate = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
            return;
        }

        $thisMonth = Carbon::now();
        $this->startDate = $thisMonth->copy()->startOfMonth()->startOfDay();
        $this->endDate = $thisMonth->copy()->endOfMonth()->endOfDay();
    }

    public function getCurrentDateRangeForDisplay(): string
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        if (isset($this->startDate) && isset($this->endDate)) {
            return $this->startDate->format('d/m/Y') . ' - ' . $this->endDate->format('d/m/Y');
        }

        return 'Chưa chọn thời gian';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('dateRange')
                    ->label('Khoảng thời gian')
                    ->timezone(config('app.timezone'))
                    ->displayFormat('D/M/Y')
                    ->format('d/m/Y')
                    ->separator(' - ')
                    ->ranges(function() {
                        $thisMonth = Carbon::now();
                        $lastMonth = Carbon::now()->startOfMonth()->subMonth();
                        $twoMonthsAgo = Carbon::now()->startOfMonth()->subMonths(2);
                        $threeMonthsAgo = Carbon::now()->startOfMonth()->subMonths(3);

                        $ranges = [
                            'Tháng này' => [
                                $thisMonth->copy()->startOfMonth(),
                                $thisMonth->copy()->endOfMonth()
                            ],
                            'Tháng trước' => [
                                $lastMonth->copy()->startOfMonth(),
                                $lastMonth->copy()->endOfMonth(),
                            ],
                            '2 tháng trước' => [
                                $twoMonthsAgo->copy()->startOfMonth(),
                                $twoMonthsAgo->copy()->endOfMonth(),
                            ],
                            '3 tháng trước' => [
                                $threeMonthsAgo->copy()->startOfMonth(),
                                $threeMonthsAgo->copy()->endOfMonth(),
                            ],
                            '3 tháng gần nhất' => [
                                $twoMonthsAgo->copy()->startOfMonth(),
                                $thisMonth->copy()->endOfMonth(),
                            ],
                            'Năm nay' => [
                                Carbon::now()->startOfYear(),
                                Carbon::now()->endOfYear(),
                            ],
                            'Tất cả' => [
                                Carbon::create(2020, 1, 1)->startOfDay(),
                                Carbon::now()->endOfDay(),
                            ],
                        ];

                        return $ranges;
                    })
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->dateRange = $state;
                        $this->parseDateRange($this->dateRange);
                        $this->resetTable();
                        $this->dispatch('$refresh');
                        
                        Notification::make()
                            ->title('Đã áp dụng bộ lọc')
                            ->success()
                            ->send();
                    })
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        // Tạo action xuất tất cả dữ liệu
        $exportAllAction = TableAction::make('export_all_data')
            ->label('Xuất tất cả dữ liệu')
            ->icon('heroicon-o-archive-box-arrow-down')
            ->color('warning')
            ->action(function () {
                return $this->exportAllData();
            })
            ->tooltip('Xuất tất cả bảng dữ liệu thành file ZIP');

        // Lấy table tương ứng với tab hiện tại
        switch ($this->activeTab) {
            case 'orders':
                $baseTable = $this->getOrdersTable($table);
                break;
            case 'order_items':
                $baseTable = $this->getOrderItemsTable($table);
                break;
            case 'fulfillment':
                $baseTable = $this->getFulfillmentCostTable($table);
                break;
            case 'advertising':
                $baseTable = $this->getAdvertisingCostTable($table);
                break;
            case 'design':
                $baseTable = $this->getDesignCostTable($table);
                break;
            case 'printing':
                $baseTable = $this->getPrintingCostTable($table);
                break;
            case 'bank_payout':
                $baseTable = $this->getBankPayoutTable($table);
                break;
            default:
                $baseTable = $this->getOrdersTable($table);
                break;
        }

        // Thêm action vào đầu danh sách header actions
        $existingActions = $baseTable->getHeaderActions();
        array_unshift($existingActions, $exportAllAction);

        return $baseTable->headerActions($existingActions);
    }

    protected function getViewData(): array
    {
        return [
            'dateRange' => $this->dateRange,
            'currentDateRange' => $this->getCurrentDateRangeForDisplay(),
        ];
    }

    // Orders Table - Lấy tất cả đơn hàng từ tất cả seller
    // Sử dụng withoutGlobalScopes() để đảm bảo lấy được tất cả dữ liệu cho tính lương
    public function getOrdersTable(Table $table): Table
    {
        return $table
            ->query($this->getOrdersQuery())
            ->columns([
                TextColumn::make('seller_id')
                    ->label('User ID')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('order_number')
                    ->label('Mã đơn hàng')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('seller.name')
                    ->label('Seller')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('store.name')
                    ->label('Cửa hàng')
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ($state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state) {
                        'Completed' => 'Hoàn thành',
                        'Processing' => 'Đang xử lý',
                        'Cancelled' => 'Đã hủy',
                        'Pending' => 'Chờ xử lý',
                        default => $state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state,
                    })
                    ->color(fn ($state): string => match ($state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state) {
                        'Completed' => 'success',
                        'Processing' => 'info',
                        'Pending' => 'warning',
                        'Cancelled' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('total')
                    ->label('Tổng tiền')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()->money('USD')->label('Tổng doanh thu'),
                        Count::make()->label('Tổng đơn hàng'),
                    ]),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        return $this->exportData('orders');
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'Processing' => 'Đang xử lý',
                        'AwaitingShipment' => 'Chờ vận chuyển',
                        'Completed' => 'Hoàn thành',
                        'OnHold' => 'Tạm giữ',
                        'Refunded' => 'Hoàn tiền',
                        'Cancelled' => 'Đã hủy',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
                SelectFilter::make('seller_id')
                    ->label('Seller')
                    ->multiple()
                    ->options(function () {
                        return User::whereHas('roles', function ($query) {
                            $query->where('name', 'Seller');
                        })->pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('seller_id', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có đơn hàng nào')
            ->emptyStateDescription('Không tìm thấy đơn hàng nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-shopping-cart');
    }

    protected function getOrdersQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        // Sử dụng withoutGlobalScopes() để đảm bảo Accountant lấy được tất cả dữ liệu cho tính lương
        return Order::query()
            ->withoutGlobalScopes()
            ->with(['store', 'seller'])
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->whereHas('seller.roles', function ($query) {
                $query->where('name', 'Seller');
            });
    }

    // Order Items Table - Lấy tất cả chi tiết đơn hàng
    public function getOrderItemsTable(Table $table): Table
    {
        return $table
            ->query($this->getOrderItemsQuery())
            ->columns([
                TextColumn::make('order.seller_id')
                    ->label('User ID')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('order.order_number')
                    ->label('Mã đơn hàng')
                    ->searchable(),
                TextColumn::make('order.seller.name')
                    ->label('Seller')
                    ->searchable(),
                TextColumn::make('order.status')
                    ->label('Trạng thái đơn hàng')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ($state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state) {
                        'Completed' => 'Hoàn thành',
                        'Processing' => 'Đang xử lý',
                        'Cancelled' => 'Đã hủy',
                        'Pending' => 'Chờ xử lý',
                        'AwaitingShipment' => 'Chờ vận chuyển',
                        'OnHold' => 'Tạm giữ',
                        'Refunded' => 'Hoàn tiền',
                        default => $state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state,
                    })
                    ->color(fn ($state): string => match ($state instanceof \App\Enums\OrderStatus ? $state->value : (string) $state) {
                        'Completed' => 'success',
                        'Processing' => 'info',
                        'Pending' => 'warning',
                        'AwaitingShipment' => 'warning',
                        'OnHold' => 'danger',
                        'Cancelled' => 'danger',
                        'Refunded' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('product.name')
                    ->label('Sản phẩm')
                    ->searchable(),
                TextColumn::make('quantity')
                    ->label('Số lượng')
                    ->sortable()
                    ->summarize([
                        Sum::make()->label('Tổng số lượng'),
                        Count::make()->label('Tổng item'),
                    ]),
                TextColumn::make('price')
                    ->label('Đơn giá')
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('total')
                    ->label('Thành tiền')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()->money('USD')->label('Tổng tiền'),
                    ]),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        return $this->exportData('order_items');
                    }),
            ])
            ->filters([
                SelectFilter::make('seller_id')
                    ->label('Seller')
                    ->multiple()
                    ->options(function () {
                        return User::whereHas('roles', function ($query) {
                            $query->where('name', 'Seller');
                        })->pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereHas('order', function ($q) use ($data) {
                                $q->whereIn('seller_id', $data['values']);
                            })
                            : $query;
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có chi tiết đơn hàng nào')
            ->emptyStateDescription('Không tìm thấy chi tiết đơn hàng nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-cube');
    }

    protected function getOrderItemsQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return OrderItem::query()
            ->withoutGlobalScopes()
            ->whereHas('order', function ($query) {
                $query->withoutGlobalScopes()
                      ->whereBetween('created_at', [$this->startDate, $this->endDate])
                      ->whereHas('seller.roles', function ($q) {
                          $q->where('name', 'Seller');
                      });
            })
            ->with(['order.store', 'order.seller', 'product']);
    }

    // Fulfillment Cost Table - Chi phí đi đơn
    public function getFulfillmentCostTable(Table $table): Table
    {
        return $table
            ->query($this->getFulfillmentCostQuery())
            ->columns([
                TextColumn::make('seller_id')
                    ->label('User ID')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('order.order_number')
                    ->label('Mã đơn hàng')
                    ->searchable(),
                TextColumn::make('order.seller.name')
                    ->label('Seller')
                    ->searchable(),
                TextColumn::make('supplier.name')
                    ->label('Nhà cung cấp')
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ($state instanceof \App\Enums\SupplierOrderStatus ? $state->value : (string) $state) {
                        'Completed' => 'Hoàn thành',
                        'InProducing' => 'Đang sản xuất',
                        'AwaitingShipment' => 'Chờ vận chuyển',
                        'Cancelled' => 'Đã hủy',
                        'Pending' => 'Chờ xử lý',
                        'OnHold' => 'Tạm giữ',
                        'Refunded' => 'Hoàn tiền',
                        default => $state instanceof \App\Enums\SupplierOrderStatus ? $state->value : (string) $state,
                    })
                    ->color(fn ($state): string => match ($state instanceof \App\Enums\SupplierOrderStatus ? $state->value : (string) $state) {
                        'Completed' => 'success',
                        'InProducing' => 'info',
                        'AwaitingShipment' => 'warning',
                        'Pending' => 'warning',
                        'OnHold' => 'danger',
                        'Cancelled' => 'danger',
                        'Refunded' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('base_cost')
                    ->label('Chi phí')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()->money('USD')->label('Tổng chi phí đi đơn'),
                        Count::make()->label('Tổng số đơn'),
                    ]),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        return $this->exportData('fulfillment_cost');
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'Pending' => 'Chờ xử lý',
                        'AwaitingShipment' => 'Chờ vận chuyển',
                        'InProducing' => 'Đang sản xuất',
                        'Completed' => 'Hoàn thành',
                        'OnHold' => 'Tạm giữ',
                        'Cancelled' => 'Đã hủy',
                        'Refunded' => 'Hoàn tiền',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
                SelectFilter::make('seller_id')
                    ->label('Seller')
                    ->multiple()
                    ->options(function () {
                        return User::whereHas('roles', function ($query) {
                            $query->where('name', 'Seller');
                        })->pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('seller_id', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có chi phí đi đơn nào')
            ->emptyStateDescription('Không tìm thấy chi phí đi đơn nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-truck');
    }

    protected function getFulfillmentCostQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return SupplierOrder::query()
            ->withoutGlobalScopes()
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->whereHas('seller.roles', function ($query) {
                $query->where('name', 'Seller');
            })
            ->with(['order', 'supplier', 'seller']);
    }

    // Advertising Cost Table - Chi phí quảng cáo
    public function getAdvertisingCostTable(Table $table): Table
    {
        return $table
            ->query($this->getAdvertisingCostQuery())
            ->columns([
                TextColumn::make('seller_id')
                    ->label('User ID')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('seller.name')
                    ->label('Seller')
                    ->searchable(),
                TextColumn::make('amount')
                    ->label('Số tiền')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()->money('USD')->label('Tổng chi phí quảng cáo'),
                        Count::make()->label('Tổng yêu cầu'),
                    ]),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ((string) $state) {
                        'approved' => 'Đã duyệt',
                        'pending' => 'Chờ duyệt',
                        'rejected' => 'Từ chối',
                        default => (string) $state,
                    })
                    ->color(fn ($state): string => match ((string) $state) {
                        'approved' => 'success',
                        'pending' => 'warning',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('description')
                    ->label('Mô tả')
                    ->limit(50),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        return $this->exportData('advertising_cost');
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'pending' => 'Chờ duyệt',
                        'approved' => 'Đã duyệt',
                        'rejected' => 'Từ chối',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
                SelectFilter::make('seller_id')
                    ->label('Seller')
                    ->multiple()
                    ->options(function () {
                        return User::whereHas('roles', function ($query) {
                            $query->where('name', 'Seller');
                        })->pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('seller_id', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có chi phí quảng cáo nào')
            ->emptyStateDescription('Không tìm thấy chi phí quảng cáo nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-megaphone');
    }

    protected function getAdvertisingCostQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return SellerFundRequest::query()
            ->withoutGlobalScopes()
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->whereHas('seller.roles', function ($query) {
                $query->where('name', 'Seller');
            })
            ->with(['seller']);
    }

    // Design Cost Table - Chi phí thiết kế
    public function getDesignCostTable(Table $table): Table
    {
        return $table
            ->query($this->getDesignCostQuery())
            ->columns([
                TextColumn::make('created_by')
                    ->label('User ID')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('design.name')
                    ->label('Tên thiết kế')
                    ->searchable(),
                TextColumn::make('creator.name')
                    ->label('Seller')
                    ->searchable(),
                TextColumn::make('designer.name')
                    ->label('Designer')
                    ->searchable(),
                TextColumn::make('price')
                    ->label('Giá cơ bản')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()
                            ->money('USD')
                            ->label('Tổng chi phí thiết kế')
                            ->using(function ($query) {
                                return $query->sum(\DB::raw('COALESCE(price, 0) + COALESCE(rush_fee, 0)'));
                            }),
                        Count::make()->label('Tổng job'),
                    ]),
                TextColumn::make('rush_fee')
                    ->label('Phí gấp')
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('total_cost')
                    ->label('Tổng chi phí')
                    ->state(function ($record) {
                        return ($record->price ?? 0) + ($record->rush_fee ?? 0);
                    })
                    ->money('USD'),
                TextColumn::make('completed_at')
                    ->label('Ngày hoàn thành')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        return $this->exportData('design_cost');
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'pending' => 'Chờ xử lý',
                        'assigned' => 'Đã phân công',
                        'in_progress' => 'Đang thực hiện',
                        'under_review' => 'Chờ duyệt',
                        'needs_revision' => 'Cần sửa',
                        'completed' => 'Hoàn thành',
                        'cancelled' => 'Đã hủy',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
                SelectFilter::make('created_by')
                    ->label('Seller')
                    ->multiple()
                    ->options(function () {
                        return User::whereHas('roles', function ($query) {
                            $query->where('name', 'Seller');
                        })->pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('created_by', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('completed_at', 'desc')
            ->emptyStateHeading('Không có chi phí thiết kế nào')
            ->emptyStateDescription('Không tìm thấy chi phí thiết kế nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-paint-brush');
    }

    protected function getDesignCostQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return DesignJob::query()
            ->withoutGlobalScopes()
            ->whereBetween('completed_at', [$this->startDate, $this->endDate])
            ->whereHas('creator.roles', function ($query) {
                $query->where('name', 'Seller');
            })
            ->with(['design', 'designer', 'creator']);
    }

    // Printing Cost Table - Chi phí in áo
    public function getPrintingCostTable(Table $table): Table
    {
        return $table
            ->query($this->getPrintingCostQuery())
            ->columns([
                TextColumn::make('seller_id')
                    ->label('User ID')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('seller.name')
                    ->label('Seller')
                    ->searchable(),
                TextColumn::make('blank.name')
                    ->label('Loại phôi')
                    ->searchable(),
                TextColumn::make('quantity')
                    ->label('Số lượng')
                    ->sortable()
                    ->summarize([
                        Sum::make()->label('Tổng số lượng'),
                        Count::make()->label('Tổng production'),
                    ]),
                TextColumn::make('cost_per_unit')
                    ->label('Giá/đơn vị')
                    ->money('USD')
                    ->state(function ($record) {
                        return $record->calculateProductionCost() / max($record->quantity, 1);
                    })
                    ->sortable(false),
                TextColumn::make('total_cost')
                    ->label('Tổng chi phí')
                    ->money('USD')
                    ->state(function ($record) {
                        return $record->calculateProductionCost();
                    })
                    ->sortable(false),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ((string) $state) {
                        'completed' => 'Hoàn thành',
                        'processing' => 'Đang xử lý',
                        'pending' => 'Chờ xử lý',
                        default => (string) $state,
                    })
                    ->color(fn ($state): string => match ((string) $state) {
                        'completed' => 'success',
                        'processing' => 'info',
                        'pending' => 'warning',
                        default => 'gray',
                    }),
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        return $this->exportData('printing_cost');
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'pending' => 'Chờ xử lý',
                        'in_production' => 'Đang sản xuất',
                        'completed' => 'Hoàn thành',
                        'rejected' => 'Từ chối',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
                SelectFilter::make('seller_id')
                    ->label('Seller')
                    ->multiple()
                    ->options(function () {
                        return User::whereHas('roles', function ($query) {
                            $query->where('name', 'Seller');
                        })->pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('seller_id', $data['values'])
                            : $query;
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('Không có chi phí in áo nào')
            ->emptyStateDescription('Không tìm thấy chi phí in áo nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-printer');
    }

    protected function getPrintingCostQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return Production::query()
            ->withoutGlobalScopes()
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->whereHas('seller.roles', function ($query) {
                $query->where('name', 'Seller');
            })
            ->with(['blank', 'seller']);
    }

    // Bank Payout Table - Thanh toán ngân hàng
    public function getBankPayoutTable(Table $table): Table
    {
        return $table
            ->query($this->getBankPayoutQuery())
            ->columns([
                TextColumn::make('store.owner_id')
                    ->label('User ID')
                    ->sortable()
                    ->searchable()
                    ->placeholder('N/A'),
                TextColumn::make('store.owner.name')
                    ->label('Seller')
                    ->searchable()
                    ->placeholder('N/A'),
                TextColumn::make('card_no')
                    ->label('Card No')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('type')
                    ->label('Loại')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ((string) $state) {
                        'Receive' => 'Nhận tiền',
                        'Send' => 'Gửi tiền',
                        default => (string) $state,
                    })
                    ->color(fn ($state): string => match ((string) $state) {
                        'Receive' => 'success',
                        'Send' => 'info',
                        default => 'gray',
                    }),
                TextColumn::make('amount')
                    ->label('Số tiền')
                    ->money('USD')
                    ->sortable()
                    ->summarize([
                        Sum::make()->money('USD')->label('Tổng bank payout'),
                        Count::make()->label('Tổng giao dịch'),
                    ]),
                TextColumn::make('transaction_id')
                    ->label('Mã giao dịch')
                    ->searchable(),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn ($state): string => match ((string) $state) {
                        'Success' => 'Thành công',
                        'Pending' => 'Chờ xử lý',
                        'Failed' => 'Thất bại',
                        default => (string) $state,
                    })
                    ->color(fn ($state): string => match ((string) $state) {
                        'Success' => 'success',
                        'Pending' => 'warning',
                        'Failed' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('time')
                    ->label('Thời gian')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->headerActions([
                TableAction::make('export_csv')
                    ->label('Xuất CSV')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->action(function () {
                        return $this->exportData('bank_payout');
                    }),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->label('Loại giao dịch')
                    ->multiple()
                    ->options([
                        'Receive' => 'Nhận tiền',
                        'Send' => 'Gửi tiền',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('type', $data['values'])
                            : $query;
                    }),
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->multiple()
                    ->options([
                        'Success' => 'Thành công',
                        'Pending' => 'Chờ xử lý',
                        'Failed' => 'Thất bại',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereIn('status', $data['values'])
                            : $query;
                    }),
                SelectFilter::make('seller_id')
                    ->label('Seller')
                    ->multiple()
                    ->options(function () {
                        return User::whereHas('roles', function ($query) {
                            $query->where('name', 'Seller');
                        })->pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return !empty($data['values'])
                            ? $query->whereHas('store', function ($q) use ($data) {
                                $q->whereIn('owner_id', $data['values']);
                            })
                            : $query;
                    }),
            ])
            ->defaultSort('time', 'desc')
            ->emptyStateHeading('Không có giao dịch bank payout nào')
            ->emptyStateDescription('Không tìm thấy giao dịch bank payout nào trong khoảng thời gian đã chọn')
            ->emptyStateIcon('heroicon-o-banknotes');
    }

    protected function getBankPayoutQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return PayoutTransaction::query()
            ->withoutGlobalScopes()
            ->whereBetween('time', [$this->startDate, $this->endDate])
            ->whereHas('store.owner.roles', function ($query) {
                $query->where('name', 'Seller');
            })
            ->with(['store.owner']);
    }

    // Export method
    public function exportData(string $type)
    {
        try {
            // Ensure we have current date range
            if (!isset($this->startDate) || !isset($this->endDate)) {
                $this->parseDateRange($this->dateRange);
            }

            // Use current dateRange for filename
            $currentDateRange = $this->getCurrentDateRangeForDisplay();
            $dateRange = str_replace(['/', ' ', '-'], '_', $currentDateRange);
            $fileName = "seller-data-{$type}-{$dateRange}.csv";

            // Tạo data array dựa trên type
            $data = [];
            $headers = [];

            switch ($type) {
                case 'orders':
                    $records = $this->getOrdersExportQuery()->with(['seller', 'store'])->get();
                    $headers = ['User ID', 'Mã đơn hàng', 'Seller', 'Cửa hàng', 'Trạng thái', 'Tổng tiền (USD)', 'Ngày tạo'];

                    foreach ($records as $record) {
                        $status = $record->status instanceof \App\Enums\OrderStatus ? $record->status->value : (string) $record->status;
                        $data[] = [
                            $record->seller_id,
                            $record->order_number,
                            $record->seller->name ?? '',
                            $record->store->name ?? '',
                            $status,
                            $record->total,
                            $record->created_at->format('d/m/Y H:i'),
                        ];
                    }
                    break;

                case 'order_items':
                    $records = $this->getOrderItemsExportQuery()->get();
                    $headers = ['User ID', 'Mã đơn hàng', 'Seller', 'Trạng thái đơn hàng', 'Sản phẩm', 'Số lượng', 'Đơn giá (USD)', 'Thành tiền (USD)', 'Ngày tạo'];
                    foreach ($records as $record) {
                        $orderStatus = $record->order->status instanceof \App\Enums\OrderStatus ? $record->order->status->value : (string) $record->order->status;
                        $data[] = [
                            $record->order->seller_id ?? '',
                            $record->order->order_number ?? '',
                            $record->order->seller->name ?? '',
                            $orderStatus,
                            $record->product->name ?? '',
                            $record->quantity,
                            $record->price,
                            $record->total,
                            $record->created_at->format('d/m/Y H:i'),
                        ];
                    }
                    break;

                case 'fulfillment_cost':
                    $records = $this->getFulfillmentCostExportQuery()->get();
                    $headers = ['User ID', 'Mã đơn hàng', 'Seller', 'Nhà cung cấp', 'Trạng thái', 'Chi phí (USD)', 'Ngày tạo'];
                    foreach ($records as $record) {
                        $status = $record->status instanceof \App\Enums\SupplierOrderStatus ? $record->status->value : (string) $record->status;
                        $data[] = [
                            $record->seller_id,
                            $record->order->order_number ?? '',
                            $record->seller->name ?? '',
                            $record->supplier->name ?? '',
                            $status,
                            $record->base_cost,
                            $record->created_at->format('d/m/Y H:i'),
                        ];
                    }
                    break;

                case 'advertising_cost':
                    $records = $this->getAdvertisingCostExportQuery()->get();
                    $headers = ['User ID', 'Seller', 'Số tiền (USD)', 'Trạng thái', 'Mô tả', 'Ngày tạo'];
                    foreach ($records as $record) {
                        $data[] = [
                            $record->seller_id,
                            $record->seller->name ?? '',
                            $record->amount,
                            $record->status,
                            $record->description,
                            $record->created_at->format('d/m/Y H:i'),
                        ];
                    }
                    break;

                case 'design_cost':
                    $records = $this->getDesignCostExportQuery()->get();
                    $headers = ['User ID', 'Tên thiết kế', 'Seller', 'Designer', 'Giá cơ bản (USD)', 'Phí gấp (USD)', 'Tổng chi phí (USD)', 'Ngày hoàn thành'];
                    foreach ($records as $record) {
                        $data[] = [
                            $record->created_by,
                            $record->design->name ?? '',
                            $record->creator->name ?? '',
                            $record->designer->name ?? '',
                            $record->price ?? 0,
                            $record->rush_fee ?? 0,
                            ($record->price ?? 0) + ($record->rush_fee ?? 0),
                            $record->completed_at ? $record->completed_at->format('d/m/Y H:i') : '',
                        ];
                    }
                    break;

                case 'printing_cost':
                    $records = $this->getPrintingCostExportQuery()->get();
                    $headers = ['User ID', 'Seller', 'Loại phôi', 'Số lượng', 'Giá/đơn vị (USD)', 'Tổng chi phí (USD)', 'Trạng thái', 'Ngày tạo'];
                    foreach ($records as $record) {
                        $totalCost = $record->calculateProductionCost();
                        $data[] = [
                            $record->seller_id,
                            $record->seller->name ?? '',
                            $record->blank->name ?? '',
                            $record->quantity,
                            $totalCost / max($record->quantity, 1),
                            $totalCost,
                            $record->status,
                            $record->created_at->format('d/m/Y H:i'),
                        ];
                    }
                    break;

                case 'bank_payout':
                    $records = $this->getBankPayoutExportQuery()->get();
                    $headers = ['User ID', 'Seller', 'Card No', 'Loại', 'Số tiền (USD)', 'Mã giao dịch', 'Trạng thái', 'Thời gian'];
                    foreach ($records as $record) {
                        $data[] = [
                            $record->store->owner_id ?? 'N/A',
                            $record->store->owner->name ?? 'N/A',
                            $record->card_no ?? 'N/A',
                            $record->type,
                            $record->amount,
                            $record->transaction_id,
                            $record->status,
                            $record->time ? $record->time->format('d/m/Y H:i') : 'N/A',
                        ];
                    }
                    break;
            }

            // Nếu không có dữ liệu, tạo row thông báo với số cột bằng headers
            if (empty($data)) {
                $emptyRow = array_fill(0, count($headers), '');
                $emptyRow[0] = 'Không có dữ liệu trong khoảng thời gian đã chọn';
                $data[] = $emptyRow;
            }

            // Tạo CSV content với proper encoding
            $output = fopen('php://temp', 'r+');

            // Add BOM for UTF-8 to ensure proper encoding
            fwrite($output, "\xEF\xBB\xBF");

            // Add headers
            fputcsv($output, $headers, ',', '"', '\\');

            // Add data
            foreach ($data as $row) {
                // Clean and convert data
                $cleanRow = array_map(function($field) {
                    // Convert any object/enum to string
                    if (is_object($field)) {
                        if (method_exists($field, 'value')) {
                            $field = $field->value; // For enums
                        } else {
                            $field = (string) $field;
                        }
                    }

                    // Ensure field is string and clean it
                    $field = (string) $field;

                    // Remove any problematic characters
                    $field = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $field);

                    return $field;
                }, $row);

                fputcsv($output, $cleanRow, ',', '"', '\\');
            }

            rewind($output);
            $csvContent = stream_get_contents($output);
            fclose($output);

            // Return download response with proper headers
            return response()->streamDownload(function() use ($csvContent) {
                echo $csvContent;
            }, $fileName, [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0',
            ]);

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi xuất dữ liệu')
                ->danger()
                ->body('Có lỗi xảy ra khi xuất dữ liệu: ' . $e->getMessage())
                ->duration(8000)
                ->send();

            return null;
        }
    }

    // Export Query Methods - Sử dụng withoutGlobalScopes() để đảm bảo lấy tất cả dữ liệu cho tính lương
    // Chỉ áp dụng bộ lọc thời gian, không áp dụng filter từ UI
    protected function getOrdersExportQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return Order::query()
            ->withoutGlobalScopes()
            ->with(['store', 'seller'])
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->whereHas('seller.roles', function ($query) {
                $query->where('name', 'Seller');
            });
    }

    protected function getOrderItemsExportQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return OrderItem::query()
            ->withoutGlobalScopes()
            ->whereHas('order', function ($query) {
                $query->withoutGlobalScopes()
                      ->whereBetween('created_at', [$this->startDate, $this->endDate])
                      ->whereHas('seller.roles', function ($q) {
                          $q->where('name', 'Seller');
                      });
            })
            ->with(['order.store', 'order.seller', 'product']);
    }

    protected function getFulfillmentCostExportQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return SupplierOrder::query()
            ->withoutGlobalScopes()
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->whereHas('seller.roles', function ($query) {
                $query->where('name', 'Seller');
            })
            ->with(['order', 'supplier', 'seller']);
    }

    protected function getAdvertisingCostExportQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return SellerFundRequest::query()
            ->withoutGlobalScopes()
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->whereHas('seller.roles', function ($query) {
                $query->where('name', 'Seller');
            })
            ->with(['seller']);
    }

    protected function getDesignCostExportQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return DesignJob::query()
            ->withoutGlobalScopes()
            ->whereBetween('completed_at', [$this->startDate, $this->endDate])
            ->whereHas('creator.roles', function ($query) {
                $query->where('name', 'Seller');
            })
            ->with(['design', 'designer', 'creator']);
    }

    protected function getPrintingCostExportQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return Production::query()
            ->withoutGlobalScopes()
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->whereHas('seller.roles', function ($query) {
                $query->where('name', 'Seller');
            })
            ->with(['blank', 'seller']);
    }

    protected function getBankPayoutExportQuery(): Builder
    {
        if (!isset($this->startDate) || !isset($this->endDate)) {
            $this->parseDateRange($this->dateRange);
        }

        return PayoutTransaction::query()
            ->withoutGlobalScopes()
            ->whereBetween('time', [$this->startDate, $this->endDate])
            ->whereHas('store.owner.roles', function ($query) {
                $query->where('name', 'Seller');
            })
            ->with(['store.owner']);
    }

    public function exportAllData()
    {
        try {
            $dateRange = str_replace(['/', ' ', '-'], '_', $this->dateRange);

            // Danh sách các bảng cần xuất
            $tables = [
                'orders' => 'Đơn hàng',
                'order_items' => 'Chi tiết đơn hàng',
                'fulfillment_cost' => 'Chi phí đi đơn',
                'advertising_cost' => 'Chi phí quảng cáo',
                'design_cost' => 'Chi phí thiết kế',
                'printing_cost' => 'Chi phí in áo',
                'bank_payout' => 'Thanh toán ngân hàng',
            ];

            // Tạo thư mục tạm để lưu các file CSV
            $tempDir = storage_path('app/temp/exports');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $exportedFiles = [];
            $filePaths = [];

            foreach ($tables as $tableType => $tableName) {
                try {
                    $fileName = "seller-data-{$tableType}-{$dateRange}.csv";
                    $filePath = $tempDir . '/' . $fileName;

                    // Xuất dữ liệu trực tiếp thành CSV
                    $this->exportDataToFile($tableType, $filePath);

                    if (file_exists($filePath)) {
                        $filePaths[] = $filePath;
                        $exportedFiles[] = $tableName;
                    }
                } catch (\Exception $e) {
                    \Log::error("Error exporting {$tableType}: " . $e->getMessage());
                }
            }

            if (empty($filePaths)) {
                throw new \Exception('Không có file nào được tạo thành công');
            }

            // Tạo file ZIP
            $zipFileName = "seller-data-all-{$dateRange}.zip";
            $zipPath = $tempDir . '/' . $zipFileName;

            $zip = new \ZipArchive();
            if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
                foreach ($filePaths as $filePath) {
                    $zip->addFile($filePath, basename($filePath));
                }
                $zip->close();

                // Xóa các file CSV tạm thời
                foreach ($filePaths as $filePath) {
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                }

                // Tải xuống file ZIP
                return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
            } else {
                throw new \Exception('Không thể tạo file ZIP');
            }

        } catch (\Exception $e) {
            \Log::error('Error in exportAllData: ' . $e->getMessage());

            Notification::make()
                ->title('Lỗi xuất dữ liệu')
                ->danger()
                ->body('Có lỗi xảy ra khi xuất dữ liệu: ' . $e->getMessage())
                ->duration(8000)
                ->send();

            return null;
        }
    }

    protected function exportDataToFile($type, $filePath)
    {
        $data = [];
        $headers = [];

        switch ($type) {
            case 'orders':
                $records = $this->getOrdersExportQuery()->get();
                $headers = ['User ID', 'Mã đơn hàng', 'Seller', 'Cửa hàng', 'Trạng thái', 'Tổng tiền (USD)', 'Ngày tạo'];

                foreach ($records as $record) {
                    $status = $record->status;
                    if ($status instanceof \App\Enums\OrderStatus) {
                        $status = $status->value;
                    }

                    $data[] = [
                        $record->seller_id ?? 'N/A',
                        $record->order_number ?? 'N/A',
                        $record->seller->name ?? 'N/A',
                        $record->store->name ?? 'N/A',
                        $status,
                        $record->total,
                        $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A',
                    ];
                }
                break;

            case 'order_items':
                $records = $this->getOrderItemsExportQuery()->get();
                $headers = ['User ID', 'Mã đơn hàng', 'Seller', 'Trạng thái đơn hàng', 'Sản phẩm', 'Số lượng', 'Đơn giá (USD)', 'Thành tiền (USD)', 'Ngày tạo'];

                foreach ($records as $record) {
                    $orderStatus = $record->order->status;
                    if ($orderStatus instanceof \App\Enums\OrderStatus) {
                        $orderStatus = $orderStatus->value;
                    }

                    $data[] = [
                        $record->order->seller_id ?? 'N/A',
                        $record->order->order_number ?? 'N/A',
                        $record->order->seller->name ?? 'N/A',
                        $orderStatus,
                        $record->product->name ?? 'N/A',
                        $record->quantity,
                        $record->price,
                        $record->total,
                        $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A',
                    ];
                }
                break;

            case 'fulfillment_cost':
                $records = $this->getFulfillmentCostExportQuery()->get();
                $headers = ['User ID', 'Mã đơn hàng', 'Seller', 'Nhà cung cấp', 'Trạng thái', 'Chi phí (USD)', 'Ngày tạo'];

                foreach ($records as $record) {
                    $status = $record->status;
                    if ($status instanceof \App\Enums\SupplierOrderStatus) {
                        $status = $status->value;
                    }

                    $data[] = [
                        $record->seller_id ?? 'N/A',
                        $record->order->order_number ?? 'N/A',
                        $record->seller->name ?? 'N/A',
                        $record->supplier->name ?? 'N/A',
                        $status,
                        $record->base_cost,
                        $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A',
                    ];
                }
                break;

            case 'advertising_cost':
                $records = $this->getAdvertisingCostExportQuery()->get();
                $headers = ['User ID', 'Seller', 'Số tiền (USD)', 'Trạng thái', 'Mô tả', 'Ngày tạo'];

                foreach ($records as $record) {
                    $data[] = [
                        $record->seller_id ?? 'N/A',
                        $record->seller->name ?? 'N/A',
                        $record->amount,
                        $record->status,
                        $record->description ?? 'N/A',
                        $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A',
                    ];
                }
                break;

            case 'design_cost':
                $records = $this->getDesignCostExportQuery()->get();
                $headers = ['User ID', 'Seller', 'Tên thiết kế', 'Designer', 'Giá cơ bản (USD)', 'Phí gấp (USD)', 'Tổng chi phí (USD)', 'Ngày hoàn thành'];

                foreach ($records as $record) {
                    $data[] = [
                        $record->creator_id ?? 'N/A',
                        $record->creator->name ?? 'N/A',
                        $record->design->name ?? 'N/A',
                        $record->designer->name ?? 'N/A',
                        $record->price ?? 0,
                        $record->rush_fee ?? 0,
                        ($record->price ?? 0) + ($record->rush_fee ?? 0),
                        $record->completed_at ? $record->completed_at->format('d/m/Y H:i') : 'N/A',
                    ];
                }
                break;

            case 'printing_cost':
                $records = $this->getPrintingCostExportQuery()->get();
                $headers = ['User ID', 'Seller', 'Blank', 'Số lượng', 'Chi phí (USD)', 'Ngày tạo'];

                foreach ($records as $record) {
                    $data[] = [
                        $record->seller_id ?? 'N/A',
                        $record->seller->name ?? 'N/A',
                        $record->blank->name ?? 'N/A',
                        $record->quantity,
                        $record->cost,
                        $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A',
                    ];
                }
                break;

            case 'bank_payout':
                $records = $this->getBankPayoutExportQuery()->get();
                $headers = ['User ID', 'Seller', 'Card No', 'Loại', 'Số tiền (USD)', 'Mã giao dịch', 'Trạng thái', 'Thời gian'];

                foreach ($records as $record) {
                    $data[] = [
                        $record->store->owner_id ?? 'N/A',
                        $record->store->owner->name ?? 'N/A',
                        $record->card_no ?? 'N/A',
                        $record->type,
                        $record->amount,
                        $record->transaction_id,
                        $record->status,
                        $record->time ? $record->time->format('d/m/Y H:i') : 'N/A',
                    ];
                }
                break;
        }

        // Nếu không có dữ liệu, tạo row thông báo với số cột bằng headers
        if (empty($data)) {
            $emptyRow = array_fill(0, count($headers), '');
            $emptyRow[0] = 'Không có dữ liệu trong khoảng thời gian đã chọn';
            $data[] = $emptyRow;
        }

        // Tạo CSV content với proper encoding
        $output = fopen($filePath, 'w');

        // Add BOM for UTF-8 to ensure proper encoding
        fwrite($output, "\xEF\xBB\xBF");

        // Add headers
        fputcsv($output, $headers, ',', '"', '\\');

        // Add data
        foreach ($data as $row) {
            // Clean and convert data
            $cleanRow = array_map(function($field) {
                // Convert any object/enum to string
                if (is_object($field)) {
                    // Handle Laravel Enums (backed enums)
                    if ($field instanceof \BackedEnum) {
                        $field = $field->value;
                    }
                    // Handle other enums with value property
                    elseif (method_exists($field, 'value')) {
                        $field = $field->value;
                    }
                    // Handle Carbon dates
                    elseif ($field instanceof \Carbon\Carbon) {
                        $field = $field->format('d/m/Y H:i');
                    }
                    // Handle other objects
                    else {
                        $field = (string) $field;
                    }
                }

                // Ensure field is string and clean it
                $field = (string) $field;

                // Remove any problematic characters
                $field = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $field);

                return $field;
            }, $row);

            fputcsv($output, $cleanRow, ',', '"', '\\');
        }

        fclose($output);
    }
}
