<?php

namespace App\Filament\App\Pages;

use App\Models\Order;
use App\Models\SupplierOrder;
use Filament\Pages\Page;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Actions;
use Carbon\Carbon;
use Livewire\Attributes\Url;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class FulfillmentVariantChecker extends Page implements HasForms
{
    use HasPageShield;
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-check-circle';
    protected static string $view = 'filament.app.pages.fulfillment-variant-checker';
    protected static ?string $slug = 'fulfillment-variant-checker';
    protected static ?string $title = 'Kiểm Tra Variant Fulfillment';
    protected static ?string $navigationLabel = 'Kiểm Tra Variant';
    protected static ?string $navigationGroup = 'Quality Control';
    protected static ?int $navigationSort = 1;

    #[Url(except: '')]
    public $dateRange = null;

    #[Url(except: '')]
    public $searchOrderCode = '';

    public $orders = [];
    public $showResults = false;
    public $searchMode = false;
    public $todayMode = false;

    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user && $user->hasRole(['super_admin', 'Fullfillment Manager', 'User Manager']);
    }

    public function mount()
    {
        // Mặc định là 3 ngày gần nhất 
        if (empty($this->dateRange)) {
            $startDate = Carbon::now()->subDays(3)->format('d/m/Y');
            $endDate = Carbon::now()->format('d/m/Y');
            $this->dateRange = $startDate . ' - ' . $endDate;
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('dateRange')
                    ->label('Chọn khoảng thời gian')
                    ->placeholder('Chọn ngày bắt đầu và kết thúc')
                    ->displayFormat('D/M/Y')
                    ->separator(' - ')
                    ->required()
                    ->columnSpanFull(),

                TextInput::make('searchOrderCode')
                    ->label('Tìm kiếm theo Order Code')
                    ->placeholder('Nhập order code để tìm kiếm cụ thể...')
                    ->helperText('Để trống để tìm kiếm theo khoảng thời gian')
                    ->columnSpanFull(),
            ]);
    }

    public function checkVariants()
    {
        $this->validate();

        // Kiểm tra xem có tìm kiếm theo order code không
        if (!empty($this->searchOrderCode)) {
            $this->searchMode = true;
            $this->todayMode = false;
            $this->searchByOrderCode();
        } else {
            $this->searchMode = false;
            $this->todayMode = false;
            $this->searchByDateRange();
        }

        $this->showResults = true;
    }

    public function clearSearch()
    {
        $this->searchOrderCode = '';
        $this->searchMode = false;
        $this->todayMode = false;
        $this->orders = [];
        $this->showResults = false;
    }

    public function randomTodaySupplierOrders()
    {
        $this->searchMode = false;
        $this->todayMode = true;
        $today = Carbon::today();

        // Lấy random 10 supplier orders hôm nay
        $this->orders = Order::with([
            'orderItems.productVariant',
            'supplier_orders' => function($query) use ($today) {
                $query->whereNotNull('form_data')
                      ->whereNotNull('log_status')
                      ->whereDate('created_at', $today);
            }
        ])
        ->whereHas('supplier_orders', function($query) use ($today) {
            $query->whereDate('created_at', $today)
                  ->whereNotNull('form_data')
                  ->whereNotNull('log_status');
        })
        ->inRandomOrder()
        ->limit(10)
        ->get();

        $this->showResults = true;
    }

    private function searchByOrderCode()
    {
        // Tìm kiếm theo order code cụ thể
        $this->orders = Order::with([
            'orderItems.productVariant',
            'supplier_orders' => function($query) {
                // $query->whereNotNull('form_data')
                //       ->whereNotNull('log_status');
            }
        ])
        ->where('order_code', 'like', '%' . $this->searchOrderCode . '%')
        ->whereHas('supplier_orders', function($query) {
            // $query->whereNotNull('form_data')
            //       ->whereNotNull('log_status');
        })
        ->get();
    }

    private function searchByDateRange()
    {
        // Parse date range
        $dates = explode(' - ', $this->dateRange);
        $startDate = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
        $endDate = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();

        // Lấy random 10 đơn hàng có supplier orders trong khoảng thời gian
        $this->orders = Order::with([
            'orderItems.productVariant',
            'supplier_orders' => function($query) {
                // $query->whereNotNull('form_data')
                //       ->whereNotNull('log_status');
            }
        ])
        ->whereHas('supplier_orders', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })
        ->inRandomOrder()
        ->limit(10)
        ->get();
    }

    public function getVariantInfo($orderItem)
    {
        $variant = $orderItem->productVariant;
        if (!$variant) return 'N/A';

        return [
            'name' => $variant->variant_name ?? 'N/A',
            'id' => $variant->variant_id ?? 'N/A',
            'fulfill_name' => $variant->variant_fulfill_name ?? 'N/A',
            'fulfill_id' => $variant->variant_fulfill_id ?? 'N/A',
            'quantity' => $orderItem->quantity ?? 0,
            'image' => $orderItem->image ?? null,
            'product_name' => $orderItem->name ?? 'N/A',
            'sku' => $orderItem->sku ?? 'N/A',
            'link' => $orderItem->link ?? null,
        ];
    }

    public function getFormDataVariants($supplierOrder)
    {
        $formData = $supplierOrder->form_data ?? [];
        $variants = [];

        if (isset($formData['items'])) {
            foreach ($formData['items'] as $item) {
                if (isset($item['selected']) && $item['selected']) {
                    $variants[] = [
                        'variant' => $item['variant'] ?? 'N/A',
                        'quantity' => $item['quantity'] ?? 0,
                        'my_sku' => $item['my_sku'] ?? 'N/A',
                        'mockup_front' => $item['mockup_front'] ?? null,
                        'mockup_back' => $item['mockup_back'] ?? null,
                        'front_design' => $item['front_design'] ?? null,
                        'back_design' => $item['back_design'] ?? null,
                    ];
                }
            }
        }

        return $variants;
    }

    public function getLogStatusVariants($supplierOrder)
    {
        $logStatus = $supplierOrder->log_status ?? [];
        $variants = [];

        // Xử lý cho Flashship - cấu trúc data.products
        if (isset($logStatus['data']['products'])) {
            foreach ($logStatus['data']['products'] as $item) {
                $variants[] = [
                    'variant_id' => $item['variant_id'] ?? 'N/A',
                    'quantity' => $item['quantity'] ?? 0,
                    'line_id' => $item['lineId'] ?? 'N/A',
                    'note' => $item['note'] ?? '',
                    'variant_sku' => $item['variant_sku'] ?? 'N/A',
                    'mockup_front' => $item['mockup_front'] ?? null,
                    'mockup_back' => $item['mockup_back'] ?? null,
                    'front_print_url' => $item['front_print_url'] ?? null,
                    'back_print_url' => $item['back_print_url'] ?? null,
                ];
            }
        }
        // Xử lý cho Flashship - cấu trúc data.items (backup)
        elseif (isset($logStatus['data']['items'])) {
            foreach ($logStatus['data']['items'] as $item) {
                $variants[] = [
                    'variant_id' => $item['variant_id'] ?? 'N/A',
                    'quantity' => $item['quantity'] ?? 0,
                    'sku' => $item['sku'] ?? 'N/A',
                    'name' => $item['name'] ?? 'N/A',
                ];
            }
        }
        // Xử lý cho Pressify
        elseif (isset($logStatus['items'])) {
            foreach ($logStatus['items'] as $item) {
                $variants[] = [
                    'variant_id' => $item['variant_id'] ?? 'N/A',
                    'quantity' => $item['quantity'] ?? 0,
                    'sku' => $item['sku'] ?? 'N/A',
                    'product_name' => $item['product_name'] ?? 'N/A',
                ];
            }
        }

        return $variants;
    }

    public function compareVariants($original, $formData, $logStatus)
    {
        $issues = [];

        // Kiểm tra số lượng variant giữa TikTok gốc và form_data
        $originalCount = count($original);
        $formDataCount = count($formData);
        $logStatusCount = count($logStatus);

        if ($originalCount !== $formDataCount) {
            $issues[] = "Số lượng variant không khớp: TikTok gốc ({$originalCount} items) vs Form Data ({$formDataCount} variants)";
        }

        // Kiểm tra số lượng variant giữa form_data và log_status
        if ($formDataCount !== $logStatusCount) {
            $issues[] = "Số lượng variant không khớp: Form Data ({$formDataCount} variants) vs Log Status ({$logStatusCount} variants)";
        }

        // So sánh tổng quantity
        $originalTotalQty = array_sum(array_column($original, 'quantity'));
        $formDataTotalQty = array_sum(array_column($formData, 'quantity'));
        $logStatusTotalQty = array_sum(array_column($logStatus, 'quantity'));

        if ($originalTotalQty !== $formDataTotalQty) {
            $issues[] = "Tổng quantity không khớp: TikTok gốc ({$originalTotalQty}) vs Form Data ({$formDataTotalQty})";
        }

        if ($formDataTotalQty !== $logStatusTotalQty) {
            $issues[] = "Tổng quantity không khớp: Form Data ({$formDataTotalQty}) vs Log Status ({$logStatusTotalQty})";
        }

        // Kiểm tra quantity giữa TikTok gốc và form_data
        foreach ($original as $index => $originalItem) {
            if (isset($formData[$index])) {
                $formVariant = $formData[$index];
                $originalQuantity = $originalItem['quantity'] ?? 0;
                $formQuantity = $formVariant['quantity'] ?? 0;

                if ($originalQuantity !== $formQuantity) {
                    $variantId = $formVariant['variant'] ?? ($index + 1);
                    $issues[] = "Variant {$variantId}: Số lượng không khớp - TikTok gốc ({$originalQuantity}) vs Form Data ({$formQuantity})";
                }
            }
        }

        // Kiểm tra ảnh trong form_data vs log_status
        foreach ($formData as $index => $formVariant) {
            if (isset($logStatus[$index])) {
                $logVariant = $logStatus[$index];

                // Kiểm tra quantity giữa form_data và log_status
                $formQuantity = $formVariant['quantity'] ?? 0;
                $logQuantity = $logVariant['quantity'] ?? 0;

                if ($formQuantity !== $logQuantity) {
                    $variantId = $formVariant['variant'] ?? ($index + 1);
                    $issues[] = "Variant {$variantId}: Số lượng không khớp - Form Data ({$formQuantity}) vs Log Status ({$logQuantity})";
                }

                // Đếm ảnh trong form_data
                $formImageCount = 0;
                if (!empty($formVariant['mockup_front'])) $formImageCount++;
                if (!empty($formVariant['mockup_back'])) $formImageCount++;
                if (!empty($formVariant['front_design'])) $formImageCount++;
                if (!empty($formVariant['back_design'])) $formImageCount++;

                // Đếm ảnh trong log_status
                $logImageCount = 0;
                if (!empty($logVariant['mockup_front'])) $logImageCount++;
                if (!empty($logVariant['mockup_back'])) $logImageCount++;
                if (!empty($logVariant['front_print_url'])) $logImageCount++;
                if (!empty($logVariant['back_print_url'])) $logImageCount++;

                // So sánh số lượng ảnh
                if ($formImageCount !== $logImageCount) {
                    $variantId = $formVariant['variant'] ?? ($index + 1);
                    $issues[] = "Variant {$variantId}: Số ảnh không khớp - Form Data ({$formImageCount} ảnh) vs Log Status ({$logImageCount} ảnh)";
                }

                // Kiểm tra cụ thể từng loại ảnh
                $missingImages = [];
                if (!empty($formVariant['mockup_front']) && empty($logVariant['mockup_front'])) {
                    $missingImages[] = 'Mockup Front';
                }
                if (!empty($formVariant['mockup_back']) && empty($logVariant['mockup_back'])) {
                    $missingImages[] = 'Mockup Back';
                }
                if (!empty($formVariant['front_design']) && empty($logVariant['front_print_url'])) {
                    $missingImages[] = 'Front Design';
                }
                if (!empty($formVariant['back_design']) && empty($logVariant['back_print_url'])) {
                    $missingImages[] = 'Back Design';
                }

                if (!empty($missingImages)) {
                    $variantId = $formVariant['variant'] ?? ($index + 1);
                    $issues[] = "Variant {$variantId}: Thiếu ảnh trong Log Status - " . implode(', ', $missingImages);
                }

                // Kiểm tra ngược lại - có ảnh trong log_status nhưng không có trong form_data
                $extraImages = [];
                if (empty($formVariant['mockup_front']) && !empty($logVariant['mockup_front'])) {
                    $extraImages[] = 'Mockup Front';
                }
                if (empty($formVariant['mockup_back']) && !empty($logVariant['mockup_back'])) {
                    $extraImages[] = 'Mockup Back';
                }
                if (empty($formVariant['front_design']) && !empty($logVariant['front_print_url'])) {
                    $extraImages[] = 'Front Design';
                }
                if (empty($formVariant['back_design']) && !empty($logVariant['back_print_url'])) {
                    $extraImages[] = 'Back Design';
                }

                if (!empty($extraImages)) {
                    $variantId = $formVariant['variant'] ?? ($index + 1);
                    $issues[] = "Variant {$variantId}: Ảnh thừa trong Log Status - " . implode(', ', $extraImages);
                }
            }
        }

        return $issues;
    }
}
