<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Illuminate\Support\Facades\Auth;

class ContactSupport extends Page
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';
    
    protected static string $view = 'filament.app.pages.contact-support';
    
    protected static ?string $navigationLabel = 'Liên hệ hỗ trợ';
    
    protected static ?string $title = 'Liên hệ hỗ trợ';
    
    protected static ?int $navigationSort = 999; // Hiển thị cuối cùng

    protected ?string $heading = 'Liên hệ hỗ trợ';

    protected ?string $subheading = 'Thông tin liên hệ và hỗ trợ kỹ thuật';

    /**
     * Kiểm tra quyền truy cập
     */
    public static function canAccess(): bool
    {
        // Tất cả user đã đăng nhập đều có thể truy cập
        return Auth::check();
    }

    /**
     * L<PERSON>y thông tin liên hệ từ config hoặc database
     */
    public function getSupportInfo(): array
    {
        return [
            'iframe_url' => 'https://www.canva.com/design/DAGsv_EbWRw/ZK2JZJ4RI6LSf_4qXkkRTw/view?utm_content=DAGsv_EbWRw&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=hed9be76aa6&embed',
            'contact_info' => [
                'email' => '<EMAIL>',
                'phone' => '+84 123 456 789',
                'telegram' => '@support_bot',
                'working_hours' => '8:00 - 22:00 (T2-CN)',
            ],
            'quick_links' => [
                [
                    'title' => 'Hướng dẫn sử dụng',
                    'url' => '#',
                    'icon' => 'heroicon-o-book-open',
                ],
                [
                    'title' => 'FAQ - Câu hỏi thường gặp',
                    'url' => '#',
                    'icon' => 'heroicon-o-question-mark-circle',
                ],
                [
                    'title' => 'Báo lỗi',
                    'url' => '#',
                    'icon' => 'heroicon-o-bug-ant',
                ],
                [
                    'title' => 'Yêu cầu tính năng',
                    'url' => '#',
                    'icon' => 'heroicon-o-light-bulb',
                ],
            ]
        ];
    }

    /**
     * Mount method để khởi tạo dữ liệu
     */
    public function mount(): void
    {
        // Có thể thêm logic khởi tạo ở đây
    }
}
