<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Models\Design;
use App\Enums\DesignStatus;
use Livewire\Attributes\Url;
use Livewire\WithPagination;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Notifications\Notification;

class DesignSearch extends Page
{
    use HasPageShield;

    protected static ?string $navigationGroup = 'Design & Media';
    
    use WithPagination;
    protected static string $view = 'filament.app.pages.design-search';
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    #[Url]
    public $searchTerm = '';
    #[Url]
    public $status = ''; // Add status property
    #[Url]
    public $hasFiles = true; // Add hasFiles property
    #[Url]
    public $page;
    #[Url]
    public $randomCount = 100; // Số lượng mẫu mặc định

    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin','DesignSearch']) ;
    }
    public function getViewData(): array
    {
        if (isset($this->designs)) {
            // <PERSON><PERSON><PERSON> có designs từ random, sử dụng nó
            return [
                'designs' => $this->designs,
                'statuses' => DesignStatus::cases(),
            ];
        }

        // Logic tìm kiếm thông thường
        $designs = Design::query();

        if (!empty($this->searchTerm)) {
            $designs = $designs->where(function ($query) {
                $query->where('name', 'like', '%' . $this->searchTerm . '%')
                    ->orWhere('desc', 'like', '%' . $this->searchTerm . '%');
            });
        }

        if (!empty($this->status)) {
            $designs = $designs->where('status', $this->status);
        }

        if ($this->hasFiles) {
            $designs = $designs->whereHas('designFiles');
        }

        $designs = $designs->withoutGlobalScopes()->orderBy('created_at', 'desc')->paginate(25);

        return [
            'designs' => $designs,
            'statuses' => DesignStatus::cases(),
        ];
    }
    public function updatingPage($page)
    {
        if ($page > 50) {
            abort(403, 'Bạn không được phép duyệt quá 50 trang.'); // Trả về lỗi 403
        }
    }
    public function search()
    {
        $this->resetPage();
        $this->getViewData();
    }
    public function randomDesign()
    {
        $startDate = now()->subDays(10)->startOfDay();
        
        // Lấy danh sách designs ngẫu nhiên
        $designs = Design::where('created_at', '>=', $startDate)
            ->inRandomOrder()
            ->limit($this->randomCount)
            ->paginate($this->randomCount); // Sử dụng paginate thay vì get
            
        if ($designs->count() > 0) {
            // Gán lại giá trị designs để view cập nhật
            $this->designs = $designs;
        } else {
            Notification::make()
                ->title('Không tìm thấy design nào trong 10 ngày gần đây')
                ->warning()
                ->send();
        }
    }
}
