<?php

namespace App\Filament\App\Pages;

use App\Services\OpenAIService;
use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class RewriteAI extends Page implements Forms\Contracts\HasForms
{
    use HasPageShield;
    protected static bool $shouldRegisterNavigation = false;

    public static function getNavigationGroup(): ?string
    {
        return null;
    }

    public static function getNavigationIcon(): ?string
    {
        return null;
    }
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin','Seller']) ;
    }

    use Forms\Concerns\InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-bottom-center';
    protected static string $view = 'filament.app.pages.rewrite-a-i';
    protected static ?string $navigationGroup = 'Spy Idea'; 
    protected static ?string $navigationLabel = 'Rewrite AI'; 
    
    public $titleData = [
        'originalTitle' => '',
        'rewrittenTitle' => '',
    ];

    public $descriptionData = [
        'originalDescription' => '',
        'rewrittenDescription' => '',
    ];

    protected function getForms(): array
    {
        return [
            'titleForm' => $this->titleForm(),
            'descriptionForm' => $this->descriptionForm(),
        ];
    }

    public function titleForm(): Form
    {
        return $this->makeForm()
            ->schema($this->getTitleFormSchema())
            ->statePath('titleData');
    }

    public function descriptionForm(): Form
    {
        return $this->makeForm()
            ->schema($this->getDescriptionFormSchema())
            ->statePath('descriptionData');
    }

    protected function getTitleFormSchema(): array
    {
        return [
            TextInput::make('originalTitle')
                ->label('Original Title')
                ->required(),
            TextInput::make('rewrittenTitle')
                ->label('Rewritten Title')
                ->disabled(),
        ];
    }

    protected function getDescriptionFormSchema(): array
    {
        return [
            Textarea::make('originalDescription')
                ->label('Original Description')
                ->required(),
            Textarea::make('rewrittenDescription')
                ->label('Rewritten Description')
                ->disabled(),
        ];
    }

    public function rewriteTitle()
    {
        $openAIService = new OpenAIService();
        $this->titleData['rewrittenTitle'] = $openAIService->rewriteTitle($this->titleData['originalTitle']);
    }

    public function rewriteDescription()
    {
        $openAIService = new OpenAIService();
        $this->descriptionData['rewrittenDescription'] = $openAIService->rewriteDescription($this->descriptionData['originalDescription']);
    }
}
