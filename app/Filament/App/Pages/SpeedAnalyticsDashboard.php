<?php

namespace App\Filament\App\Pages;

use App\Services\SpeedAnalyticsService;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use Carbon\Carbon;
use Livewire\Attributes\Url;

class SpeedAnalyticsDashboard extends Page implements HasForms
{
    use HasPageShield;
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static string $view = 'filament.app.pages.speed-analytics-dashboard';
    protected static ?string $title = 'Thống Kê Tốc Độ';
    protected static ?string $navigationLabel = 'Thống Kê Tốc Độ';
    protected static ?string $navigationGroup = 'Analytics';
    protected static ?int $navigationSort = 1;
    protected static bool $shouldRegisterNavigation = false;

    public $dashboardData;

    #[Url(except: '')]
    public $dateRange = null;

    protected $startDate;
    protected $endDate;

    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user && $user->hasRole(['super_admin', 'Leader', 'Seller', 'Fulfillment']);
    }

    public function mount()
    {
        // Mặc định là 1 tháng gần nhất
        if (empty($this->dateRange)) {
            $lastMonth = Carbon::now()->subMonth();
            $startDate = $lastMonth->format('d/m/Y');
            $endDate = Carbon::now()->format('d/m/Y');
            $this->dateRange = $startDate . ' - ' . $endDate;
        }

        $this->parseDateRange($this->dateRange);
        $this->loadDashboardData();
    }

    protected function parseDateRange(string $dateRange = null)
    {
        if (empty($dateRange)) {
            $this->startDate = Carbon::now()->subMonth()->startOfDay();
            $this->endDate = Carbon::now()->endOfDay();
            return;
        }

        $dates = explode(' - ', $dateRange);
        if (count($dates) == 2) {
            $this->startDate = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
            $this->endDate = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
            return;
        }

        $this->startDate = Carbon::now()->subMonth()->startOfDay();
        $this->endDate = Carbon::now()->endOfDay();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('dateRange')
                    ->label('Khoảng thời gian thống kê')
                    ->timezone(config('app.timezone'))
                    ->displayFormat('D/M/Y')
                    ->format('d/m/Y')
                    ->separator(' - ')
                    ->ranges(function() {
                        $now = Carbon::now();

                        return [
                            '1 tuần gần nhất' => [
                                $now->copy()->subWeek(),
                                $now->copy()
                            ],
                            '1 tháng gần nhất' => [
                                $now->copy()->subMonth(),
                                $now->copy()
                            ],
                            '3 tháng gần nhất' => [
                                $now->copy()->subMonths(3),
                                $now->copy()
                            ],
                            '1 năm gần nhất' => [
                                $now->copy()->subYear(),
                                $now->copy()
                            ],
                            'Tháng này' => [
                                $now->copy()->startOfMonth(),
                                $now->copy()->endOfMonth()
                            ],
                            'Tháng trước' => [
                                $now->copy()->subMonth()->startOfMonth(),
                                $now->copy()->subMonth()->endOfMonth()
                            ],
                            'Năm nay' => [
                                $now->copy()->startOfYear(),
                                $now->copy()->endOfYear()
                            ],
                        ];
                    })
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->dateRange = $state;
                        $this->parseDateRange($this->dateRange);
                        $this->loadDashboardData();
                    })
                    ->columnSpanFull(),
            ]);
    }

    public function loadDashboardData()
    {
        try {
            $speedAnalytics = new SpeedAnalyticsService();

            // Tính toán timeframe dựa trên khoảng thời gian được chọn
            $timeframe = $this->calculateTimeframe();

            $this->dashboardData = $speedAnalytics->getDashboardData(null, $this->startDate, $this->endDate, $timeframe);

            // Thêm dữ liệu so sánh 24h
            $this->dashboardData['daily_comparison'] = $speedAnalytics->getDailyComparison();
        } catch (\Exception $e) {
            // Fallback data nếu có lỗi
            $this->dashboardData = [
                'order_speed' => [
                    'speed' => ['hourly' => 0, 'daily_avg' => 0, 'weekly_avg' => 0],
                    'trend' => ['today_vs_yesterday' => 0, 'growth_rate' => 0],
                    'processing_time' => ['avg_time_to_design_hours' => 0, 'avg_time_to_fulfill_hours' => 0],
                    'queue' => ['pending_design' => 0, 'pending_fulfillment' => 0, 'total_pending' => 0]
                ],
                'design_speed' => [
                    'speed' => ['hourly' => 0, 'daily_avg' => 0, 'per_designer' => 0],
                    'capacity' => ['active_designers' => 0, 'utilization_rate' => 0],
                    'processing_time' => ['avg_design_time_hours' => 0],
                    'queue' => ['pending' => 0, 'in_progress' => 0, 'total_queue' => 0]
                ],
                'fulfillment_speed' => [
                    'speed' => ['hourly' => 0, 'daily_avg' => 0, 'per_staff' => 0],
                    'capacity' => ['active_staff' => 0, 'utilization_rate' => 0],
                    'automation' => ['auto_fulfillment_rate' => 0, 'manual_fulfillments' => 0, 'auto_fulfillments' => 0],
                    'processing_time' => ['avg_fulfill_time_days' => 0],
                    'queue' => ['total_queue' => 0]
                ],
                'bottlenecks' => [
                    'bottlenecks' => [],
                    'recommendations' => [],
                    'overall_health' => ['score' => 100, 'status' => 'excellent', 'design_balance' => 100, 'fulfillment_balance' => 100]
                ],
                'timestamp' => now()->toISOString(),
                'error' => $e->getMessage(),
                'period' => $this->getCurrentPeriodDisplay()
            ];
        }
    }

    protected function calculateTimeframe(): string
    {
        if (!$this->startDate || !$this->endDate) {
            return '1m';
        }

        $diffInDays = $this->startDate->diffInDays($this->endDate);

        if ($diffInDays <= 7) {
            return '1w';
        } elseif ($diffInDays <= 31) {
            return '1m';
        } elseif ($diffInDays <= 93) {
            return '3m';
        } else {
            return '1y';
        }
    }

    public function getCurrentPeriodDisplay(): string
    {
        if (!$this->startDate || !$this->endDate) {
            return 'Chưa chọn thời gian';
        }

        return $this->startDate->format('d/m/Y') . ' - ' . $this->endDate->format('d/m/Y');
    }

    protected function getViewData(): array
    {
        return [
            'dashboardData' => $this->dashboardData,
        ];
    }

    public function getDailyComparison(): array
    {
        $now = Carbon::now();

        // 24h gần nhất (hiện tại -> 24h trước)
        $last24hStart = $now->copy()->subHours(24);
        $last24hEnd = $now->copy();

        // 24h trước đó (48h trước -> 24h trước)
        $previous24hStart = $now->copy()->subHours(48);
        $previous24hEnd = $now->copy()->subHours(24);

        // Lấy dữ liệu orders
        $ordersLast24h = \App\Models\Order::whereBetween('created_at', [$last24hStart, $last24hEnd])->count();
        $ordersPrevious24h = \App\Models\Order::whereBetween('created_at', [$previous24hStart, $previous24hEnd])->count();

        // Lấy dữ liệu design jobs completed
        $designsLast24h = \App\Models\DesignJob::where('status', 'completed')
            ->whereNotNull('completed_at')
            ->whereBetween('completed_at', [$last24hStart, $last24hEnd])
            ->count();
        $designsPrevious24h = \App\Models\DesignJob::where('status', 'completed')
            ->whereNotNull('completed_at')
            ->whereBetween('completed_at', [$previous24hStart, $previous24hEnd])
            ->count();

        // Lấy dữ liệu fulfillments (SupplierOrders created)
        $fulfillmentsLast24h = \App\Models\SupplierOrder::whereBetween('created_at', [$last24hStart, $last24hEnd])->count();
        $fulfillmentsPrevious24h = \App\Models\SupplierOrder::whereBetween('created_at', [$previous24hStart, $previous24hEnd])->count();

        // Tính growth rate
        $orderGrowthRate = $ordersPrevious24h > 0
            ? round((($ordersLast24h - $ordersPrevious24h) / $ordersPrevious24h) * 100, 2)
            : ($ordersLast24h > 0 ? 100 : 0);

        $designGrowthRate = $designsPrevious24h > 0
            ? round((($designsLast24h - $designsPrevious24h) / $designsPrevious24h) * 100, 2)
            : ($designsLast24h > 0 ? 100 : 0);

        $fulfillmentGrowthRate = $fulfillmentsPrevious24h > 0
            ? round((($fulfillmentsLast24h - $fulfillmentsPrevious24h) / $fulfillmentsPrevious24h) * 100, 2)
            : ($fulfillmentsLast24h > 0 ? 100 : 0);

        // Tính tốc độ per hour
        $orderRateLast24h = round($ordersLast24h / 24, 2);
        $orderRatePrevious24h = round($ordersPrevious24h / 24, 2);

        $designRateLast24h = round($designsLast24h / 24, 2);
        $designRatePrevious24h = round($designsPrevious24h / 24, 2);

        $fulfillmentRateLast24h = round($fulfillmentsLast24h / 24, 2);
        $fulfillmentRatePrevious24h = round($fulfillmentsPrevious24h / 24, 2);

        return [
            'period_last_24h' => $last24hStart->format('d/m H:i') . ' - ' . $last24hEnd->format('d/m H:i'),
            'period_previous_24h' => $previous24hStart->format('d/m H:i') . ' - ' . $previous24hEnd->format('d/m H:i'),
            'orders' => [
                'last_24h' => $ordersLast24h,
                'previous_24h' => $ordersPrevious24h,
                'growth_rate' => $orderGrowthRate,
                'rate_last_24h' => $orderRateLast24h,
                'rate_previous_24h' => $orderRatePrevious24h,
                'alert_level' => $this->getAlertLevel($orderGrowthRate)
            ],
            'designs' => [
                'last_24h' => $designsLast24h,
                'previous_24h' => $designsPrevious24h,
                'growth_rate' => $designGrowthRate,
                'rate_last_24h' => $designRateLast24h,
                'rate_previous_24h' => $designRatePrevious24h,
                'alert_level' => $this->getAlertLevel($designGrowthRate)
            ],
            'fulfillments' => [
                'last_24h' => $fulfillmentsLast24h,
                'previous_24h' => $fulfillmentsPrevious24h,
                'growth_rate' => $fulfillmentGrowthRate,
                'rate_last_24h' => $fulfillmentRateLast24h,
                'rate_previous_24h' => $fulfillmentRatePrevious24h,
                'alert_level' => $this->getAlertLevel($fulfillmentGrowthRate)
            ]
        ];
    }

    private function getAlertLevel($growthRate): string
    {
        if ($growthRate > 50) return 'critical';
        if ($growthRate > 30) return 'warning';
        if ($growthRate > 15) return 'info';
        if ($growthRate < -30) return 'danger';
        return 'normal';
    }

    public function getSpeedOverviewStats(): array
    {
        if (!$this->dashboardData) {
            return [];
        }

        $data = $this->dashboardData;

        return [
            [
                'label' => 'Tốc Độ Order',
                'value' => $data['order_speed']['speed']['hourly'] . '/giờ',
                'description' => $data['order_speed']['trend']['growth_rate'] . '% ' . ($data['order_speed']['trend']['period_comparison'] ?? 'so với kỳ trước'),
                'icon' => $data['order_speed']['trend']['growth_rate'] >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down',
                'color' => $data['order_speed']['trend']['growth_rate'] >= 0 ? 'success' : 'danger'
            ],
            [
                'label' => 'Tốc Độ Design',
                'value' => $data['design_speed']['speed']['hourly'] . '/giờ',
                'description' => 'Hiệu suất: ' . $data['design_speed']['capacity']['utilization_rate'] . '%',
                'icon' => 'heroicon-m-paint-brush',
                'color' => $data['design_speed']['capacity']['utilization_rate'] > 80 ? 'warning' : 'success'
            ],
            [
                'label' => 'Tốc Độ Fulfillment',
                'value' => $data['fulfillment_speed']['speed']['hourly'] . '/giờ',
                'description' => 'Auto: ' . $data['fulfillment_speed']['automation']['auto_fulfillment_rate'] . '%',
                'icon' => 'heroicon-m-truck',
                'color' => $data['fulfillment_speed']['automation']['auto_fulfillment_rate'] > 70 ? 'success' : 'warning'
            ],
            [
                'label' => 'Sức Khỏe Hệ Thống',
                'value' => $data['bottlenecks']['overall_health']['score'] . '%',
                'description' => 'Trạng thái: ' . ucfirst($data['bottlenecks']['overall_health']['status']),
                'icon' => $this->getHealthIcon($data['bottlenecks']['overall_health']['status']),
                'color' => $this->getHealthColor($data['bottlenecks']['overall_health']['status'])
            ]
        ];
    }

    public function getBottleneckStats(): array
    {
        if (!$this->dashboardData) {
            return [];
        }

        $data = $this->dashboardData['bottlenecks'];
        $stats = [];

        if (empty($data['bottlenecks'])) {
            $stats[] = [
                'label' => 'Trạng Thái',
                'value' => 'Hệ thống hoạt động tốt',
                'description' => 'Không có bottleneck nào được phát hiện',
                'icon' => 'heroicon-m-check-circle',
                'color' => 'success'
            ];
        } else {
            foreach ($data['bottlenecks'] as $bottleneck) {
                $stats[] = [
                    'label' => 'Bottleneck: ' . ucfirst($bottleneck['type']),
                    'value' => 'Mức độ: ' . $bottleneck['severity'] . '%',
                    'description' => 'Gap: ' . ($bottleneck['gap'] ?? 0) . ' đơn vị/giờ',
                    'icon' => 'heroicon-m-exclamation-triangle',
                    'color' => $bottleneck['severity'] > 50 ? 'danger' : 'warning'
                ];
            }
        }

        // Thêm recommendations
        if (!empty($data['recommendations'])) {
            foreach (array_slice($data['recommendations'], 0, 2) as $recommendation) {
                $stats[] = [
                    'label' => 'Khuyến nghị',
                    'value' => $recommendation['message'],
                    'description' => 'Ưu tiên: ' . ucfirst($recommendation['priority']),
                    'icon' => 'heroicon-m-light-bulb',
                    'color' => $recommendation['priority'] === 'high' ? 'danger' : 'warning'
                ];
            }
        }

        return $stats;
    }

    private function getHealthIcon($status)
    {
        return match($status) {
            'excellent' => 'heroicon-m-heart',
            'good' => 'heroicon-m-check-circle',
            'warning' => 'heroicon-m-exclamation-triangle',
            'critical' => 'heroicon-m-x-circle',
            default => 'heroicon-m-question-mark-circle'
        };
    }

    private function getHealthColor($status)
    {
        return match($status) {
            'excellent' => 'success',
            'good' => 'info',
            'warning' => 'warning',
            'critical' => 'danger',
            default => 'gray'
        };
    }

    protected function getHeaderWidgets(): array
    {
        return [
            \App\Filament\App\Widgets\OrderVolatilityStats::class,
            \App\Filament\App\Widgets\OrderVolatilityChart::class,
            \App\Filament\App\Widgets\OrderSpikeDetailsTable::class,
        ];
    }
}
