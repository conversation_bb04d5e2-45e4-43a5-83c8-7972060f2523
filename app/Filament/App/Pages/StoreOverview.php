<?php

namespace App\Filament\App\Pages;

use App\Enums\TiktokShopStatus;
use App\Exports\StoresExport;
use App\Models\Store;
use App\Tables\Columns\TextAreaInputColumn;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Actions\ExportAction;
use Filament\Pages\Page;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class StoreOverview extends Page implements HasTable
{
    use InteractsWithTable;
    use HasPageShield;

    // Cấu hình hiển thị trong sidebar
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationLabel = 'Tổng quan cửa hàng';
    protected static ?string $navigationGroup = 'Store Manager';
    protected static ?int $navigationSort = 1;

    // Cấu hình slug URL
    protected static ?string $slug = 'store-overview';

    /**
     * Kiểm tra quyền truy cập vào trang
     * Cho phép người dùng có vai trò super_admin hoặc User Manager truy cập
     */
    public static function canAccess(array $parameters = []): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    /**
     * Xác định xem trang có nên hiển thị trong menu navigation không
     */
    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    protected static string $view = 'filament.app.pages.store-overview';

    protected static ?string $title = 'Tổng quan cửa hàng';



    public function table(Table $table): Table
    {
        return $table
            ->query(Store::query()->with(['seller', 'storeMetric']))
            ->columns([
                TextInputColumn::make('note')
                    ->label('Ghi chú')
                    ->placeholder('Nhập ghi chú...')
                    ->extraAttributes(['class' => 'w-[120px] text-sm']),

                TextInputColumn::make('note_bank')
                    ->label('Ghi chú ngân hàng')
                    ->placeholder('Nhập ghi chú ngân hàng...')
                    ->extraAttributes(['class' => 'w-[120px] text-sm']),

                TextColumn::make('name')
                    ->label('Tên cửa hàng')
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->description(function (Store $record): HtmlString {
                        $descriptions = [];

                        // Thêm tên người bán
                        if ($record->seller?->name) {
                            $sellerName = strlen($record->seller->name) > 12
                                ? '...' . Str::substr($record->seller->name, -12)
                                : $record->seller->name;
                            $descriptions[] = $sellerName;
                        }

                        // Thêm telegram
                        if ($record->seller?->telegram_id) {
                            $descriptions[] = $record->seller->telegram_id;
                        }

                        return new HtmlString(implode('<br>', $descriptions));
                    })
                    ->copyableState(function ($record) {
                        if ($record->seller && $record->seller->telegram_id) {
                            return $record->seller->telegram_id;
                        }
                        return null;
                    })
                    ->copyMessage('Đã sao chép username Telegram')
                    ->wrap()
                    ->limit(40),

                TextColumn::make('tiktok_shop_code')
                    ->label('Mã cửa hàng')
                    ->searchable()
                    ->sortable()
                    ->size('sm')
                    ->copyable()
                    ->url(fn (Store $record): ?string => $record->link ? $record->link : null, shouldOpenInNewTab: true)
                    ->icon('heroicon-m-link')
                    ->limit(12),

                TextColumn::make('tiktok_shop_status')
                    ->label('Trạng thái TikTok')
                    ->formatStateUsing(fn ($state) => match (true) {
                        $state === TiktokShopStatus::Live => 'Live',
                        $state === TiktokShopStatus::NotConnected => 'Not Connected',
                        $state === TiktokShopStatus::Suspended => 'Suspended',
                        default => (string) $state,
                    })
                    ->badge()
                    ->size('xs')
                    ->color(fn ($state) => match (true) {
                        $state === TiktokShopStatus::Live => 'success',
                        $state === TiktokShopStatus::NotConnected => 'warning',
                        $state === TiktokShopStatus::Suspended => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),



                TextColumn::make('orders_count')
                    ->label('Số đơn hàng')
                    ->size('sm')
                    ->counts('orders')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->withCount('orders')
                            ->orderBy('orders_count', $direction);
                    }),

                TextColumn::make('tiktok_payout_on_hold')
                    ->label('Tổng số tiền đang giữ')
                    ->size('sm')
                    ->money('USD')
                    ->description(function (Store $record): HtmlString {
                        $line1 = $record->tiktok_paid_amount
                            ? 'Đã paid: $' . number_format($record->tiktok_paid_amount, 2)
                            : 'Chưa có tiền paid';

                        $line2 = $record->tiktok_payout_on_hold
                            ? 'Payout on hold: $' . number_format($record->tiktok_payout_on_hold, 2)
                            : 'Không có payout hold';

                        $line3 = 'Payout: ' . ($record->tiktok_payout_day ?? 'N/A') . ' days (' . ($record->shop_health ?? '0/48') . ')';
                      
                        return new HtmlString($line1 . '<br>' . $line2 . '<br>' . $line3);
                    })
                    ->sortable(),

                TextColumn::make('last_sync_tiktok')
                    ->label('Thời gian đồng bộ cuối')
                    ->size('sm')
                    ->dateTime('d/m/Y H:i')
                    ->since()
                    ->sortable(),

                TextColumn::make('card')
                    ->label('TK TikTok')
                    ->size('sm')
                    ->formatStateUsing(fn ($state) => $state ? '****' . substr($state, -4) : 'N/A')
                    ->sortable(),

                TextColumn::make('bank_account')
                    ->label('TK đã lưu')
                    ->size('sm')
                    ->formatStateUsing(fn ($state) => $state ? '****' . substr($state, -4) : 'N/A')
                    ->sortable(),

                TextColumn::make('app_partner_id')
                    ->label('API App')
                    ->badge()
                    ->size('xs')
                    ->formatStateUsing(fn ($state) => $state ? 'Đã liên kết' : 'Chưa liên kết')
                    ->color(fn ($state) => $state ? 'success' : 'danger'),


            ])
            ->filters([
                SelectFilter::make('tiktok_shop_status')
                    ->label('Trạng thái TikTok')
                    ->options([
                        TiktokShopStatus::Live->value => 'Live',
                        TiktokShopStatus::NotConnected->value => 'Not Connected',
                        TiktokShopStatus::Suspended->value => 'Suspended',
                    ])
                    ->multiple(),

                SelectFilter::make('seller')
                    ->label('Người bán')
                    ->relationship('seller', 'name')
                    ->searchable()
                    ->preload(),

                TernaryFilter::make('bank_match')
                    ->label('Trạng thái ngân hàng')
                    ->placeholder('Tất cả')
                    ->trueLabel('Khớp')
                    ->falseLabel('Không khớp')
                    ->queries(
                        true: fn (Builder $query) => $query
                            ->whereNotNull('bank_account')
                            ->whereNotNull('card')
                            ->whereRaw('RIGHT(bank_account, 4) = RIGHT(card, 4)'),
                        false: fn (Builder $query) => $query
                            ->whereNotNull('bank_account')
                            ->whereNotNull('card')
                            ->whereRaw('RIGHT(bank_account, 4) != RIGHT(card, 4)'),
                    ),

                Filter::make('missing_bank')
                    ->label('Thiếu thông tin ngân hàng')
                    ->query(fn (Builder $query) => $query->whereNull('bank_account')),

                Filter::make('missing_tiktok_bank')
                    ->label('Thiếu thông tin TikTok')
                    ->query(fn (Builder $query) => $query->whereNull('card')),

                Filter::make('has_orders_no_bank')
                    ->label('Đã có đơn nhưng chưa add bank')
                    ->query(fn (Builder $query) => $query
                        ->whereNull('bank_account')
                        ->whereHas('orders'))
                    ->default(),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                Action::make('view_revenue')
                    ->label('Xem doanh thu')
                    ->icon('heroicon-o-chart-bar')
                    ->color('primary')
                    ->url(function (Store $record): string {
                        return StoreRevenueOverview::getUrl(['selectedStore' => $record->id]);
                    })
                    ->openUrlInNewTab(false)
                    ->tooltip('Xem chi tiết doanh thu cửa hàng'),
            ])
            ->bulkActions([
                ExportBulkAction::make()
                    ->label('Xuất Excel')
                    ->exporter(StoresExport::class)
                    ->fileName('stores')
                    ->formats([
                        ExportFormat::Xlsx,
                    ]),
            ])
            ->defaultSort('orders_count', 'desc');
    }

    protected function getHeaderActions(): array
    {
        return [
            ExportAction::make()
                ->label('Xuất dữ liệu')
                ->color('success')
                ->icon('heroicon-o-arrow-down-tray')
                ->exporter(StoresExport::class)
                ->formats([
                    ExportFormat::Xlsx,
                    ExportFormat::Csv,
                ]),
        ];
    }


}
