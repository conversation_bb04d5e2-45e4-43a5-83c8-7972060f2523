<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;
use Illuminate\Support\Facades\Cache;

class CreditCardGenerator extends Page
{
    use HasPageShield;

    private const DEFAULT_QUANTITY = 10;
    private const MIN_BIN_LENGTH = 6;
    private const CARD_LENGTH = 16;
    private const CVV_LENGTH = 3;
    private const CACHE_KEY = 'generated_cards';

    // Card issuer prefixes
    private const CARD_PREFIXES = [
        'visa' => ['4'],
        'mastercard' => ['51', '52', '53', '54', '55'],
        'amex' => ['34', '37'],
        'discover' => ['6011', '644', '645', '646', '647', '648', '649', '65'],
        'jcb' => ['3528', '3529', '353', '354', '355', '356', '357', '358'],
        'diners' => ['300', '301', '302', '303', '304', '305', '36', '38'],
    ];

    // Card lengths by issuer
    private const CARD_LENGTHS = [
        'visa' => 16,
        'mastercard' => 16,
        'amex' => 15,
        'discover' => 16,
        'jcb' => 16,
        'diners' => [14, 16],
    ];

    public $bin;
    public $cardType = 'visa'; // Default to Visa
    public $expirationMonth = 'Random';
    public $expirationYear = 'Random'; 
    public $cvv = '';
    public $quantity = self::DEFAULT_QUANTITY;
    public $generatedCards = '';
    private $generatedNumbers = [];
    
    protected static ?string $navigationGroup = 'Tools';
    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static string $view = 'filament.pages.credit-card-generator';

    protected function rules()
    {
        return [
            'bin' => ['required', 'string', 'min:' . self::MIN_BIN_LENGTH],
            'cardType' => ['required', 'string', 'in:' . implode(',', array_keys(self::CARD_PREFIXES))],
            'quantity' => ['required', 'integer', 'min:1', 'max:100'],
            'cvv' => ['nullable', 'string', 'max:' . self::CVV_LENGTH],
            'expirationMonth' => ['required', 'string'],
            'expirationYear' => ['required', 'string'],
        ];
    }

    private function validateBINForCardType($bin, $cardType)
    {
        $prefixes = self::CARD_PREFIXES[$cardType];
        foreach ($prefixes as $prefix) {
            if (str_starts_with($bin, $prefix)) {
                return true;
            }
        }
        return false;
    }

    private function getCardLength($cardType)
    {
        $length = self::CARD_LENGTHS[$cardType];
        if (is_array($length)) {
            return $length[array_rand($length)];
        }
        return $length;
    }

    public function generateCards()
    {
        $this->validate();

        if (!$this->validateBINForCardType($this->bin, $this->cardType)) {
            throw new \Exception("Invalid BIN for selected card type");
        }
        
        $cards = [];
        $uniqueNumbers = [];

        for ($i = 0; $i < $this->quantity; $i++) {
            $card = $this->generateValidCard($uniqueNumbers);
            if ($card) {
                $cards[] = $card;
            }
        }

        $this->generatedCards = implode("\n", $cards);
        Cache::put(self::CACHE_KEY, $this->generatedCards, now()->addMinutes(30));
    }

    private function generateValidCard($uniqueNumbers) 
    {
        $maxAttempts = 10;
        $attempts = 0;

        while ($attempts < $maxAttempts) {
            $cardNumber = $this->generateCardNumber($this->bin);
            
            if (!in_array($cardNumber, $uniqueNumbers) && $this->isCardNumberValid($cardNumber)) {
                $uniqueNumbers[] = $cardNumber;
                
                return $this->formatCard(
                    $cardNumber,
                    $this->generateExpirationMonth(),
                    $this->generateExpirationYear(),
                    $this->generateCVV()
                );
            }
            
            $attempts++;
        }

        return null;
    }

    private function generateCardNumber($bin)
    {
        $cardLength = $this->getCardLength($this->cardType);
        $number = $bin;
        $remainingLength = $cardLength - strlen($number) - 1;
        
        // Generate random digits for the remaining length
        $randomPart = '';
        for ($i = 0; $i < $remainingLength; $i++) {
            $randomPart .= random_int(0, 9);
        }
        
        $number .= $randomPart;
        
        // Calculate and append check digit using Luhn algorithm
        $number .= $this->calcLuhnCheckDigit($number);

        return $number;
    }

    private function calcLuhnCheckDigit($number)
    {
        $sum = 0;
        $length = strlen($number);
        $parity = ($length + 1) % 2;

        // Calculate sum using improved Luhn algorithm
        for ($i = $length - 1; $i >= 0; $i--) {
            $digit = (int)$number[$i];
            
            if ($i % 2 === $parity) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }
            
            $sum += $digit;
        }

        $checkDigit = (10 - ($sum % 10)) % 10;
        return (string)$checkDigit;
    }

    private function generateExpirationMonth()
    {
        return $this->expirationMonth === 'Random' 
            ? str_pad(random_int(1, 12), 2, '0', STR_PAD_LEFT) 
            : $this->expirationMonth;
    }

    private function generateExpirationYear()
    {
        return $this->expirationYear === 'Random'
            ? random_int(date('y'), date('y') + 5)
            : $this->expirationYear;
    }

    private function generateCVV()
    {
        $length = ($this->cardType === 'amex') ? 4 : 3;
        return empty($this->cvv)
            ? str_pad(random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT)
            : $this->cvv;
    }

    private function formatCard($number, $month, $year, $cvv)
    {
        return implode('|', [$number, $month, $year, $cvv]);
    }

    private function isCardNumberValid($number)
    {
        // Length validation
        $expectedLength = $this->getCardLength($this->cardType);
        if (is_array($expectedLength)) {
            if (!in_array(strlen($number), $expectedLength)) {
                return false;
            }
        } elseif (strlen($number) !== $expectedLength) {
            return false;
        }

        // Luhn algorithm validation
        $sum = 0;
        $length = strlen($number);
        $parity = $length % 2;

        for ($i = $length - 1; $i >= 0; $i--) {
            $digit = (int)$number[$i];
            if ($i % 2 === $parity) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }
            $sum += $digit;
        }

        return $sum % 10 === 0;
    }

    public function shuffleCards()
    {
        $cards = explode("\n", $this->generatedCards);
        shuffle($cards);
        $this->generatedCards = implode("\n", $cards);
    }

    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin']) ;
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }
}
