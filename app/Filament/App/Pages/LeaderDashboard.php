<?php

namespace App\Filament\App\Pages;

use App\Models\User;
use App\Models\Team;
use App\Models\Idea;
use App\Models\Order;
use Filament\Pages\Page;
use Carbon\Carbon;
use App\Models\Store;
use App\Filament\App\Resources\ProductResource;
use App\Filament\App\Resources\IdeaResource;
use App\Models\Product;
use App\Models\VideoRequest;
use App\Models\MediaRequest;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use App\Models\SellerLevel;
use Filament\Notifications\Notification;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Enums\TiktokShopStatus;
use App\Actions\GetTeamMembersData;
use Illuminate\Support\Facades\Ray;


use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class LeaderDashboard extends Page
{
    use InteractsWithActions;
    use InteractsWithForms;
    use HasPageShield;

    public static function canAccess(): bool
    {
        return Auth::user()->hasRole(['super_admin', 'User Manager', 'Accountant']);
    }

    
    protected static ?string $navigationIcon = 'heroicon-o-presentation-chart-bar';
    protected static ?string $navigationLabel = 'Team Dashboard';
    protected static ?string $title = 'Team Dashboard';
    protected static ?string $slug = 'team-dashboard';
    protected static ?int $navigationSort = 1;
    protected static string $view = 'filament.app.pages.leader-dashboard';

    public $selectedTeamId = null;
    public $selectedSellerId = null;
    public $selectedSellerLevelId = null;
    public $isLoading = false;
    public $sellerLevels = [];
    public $timeRange = 'monthly';
    public $teamData = [];
    public $commissionRates = [];

    // Định nghĩa các hằng số cho invoice status
    const INVOICE_STATUS = [
        'PAID' => 'paid',
        'PARTIAL' => 'partial',
        'PENDING' => 'pending',
        'APPROVED' => 'approved',
        'CONFIRMED' => 'confirmed',
        'CANCELLED' => 'cancelled'
    ];

    protected function getStats($userId): array
    {
        $user = User::find($userId);
        $now = Carbon::now();
        $startDate = Carbon::parse($user->created_at);
        $monthStart = $now->copy()->startOfMonth();

        // Nếu tạo account giữa tháng, chỉ tính từ ngày tạo
        $monthStart = max($startDate, $now->copy()->startOfMonth());
        $weekStart = max($startDate, $now->copy()->startOfWeek());
        
        $yesterdayStart = $now->copy()->subDay()->startOfDay();
        $yesterdayEnd = $now->copy()->subDay()->endOfDay();
        $dayStart = $now->copy()->startOfDay();
        $dayEnd = $now->copy()->endOfDay();
        $weekEnd = $now->copy()->endOfWeek();
        $monthEnd = $now->copy()->endOfMonth();

        $workingDays = (int)$startDate->diffInDays($now) + 1;
        $isNewSeller = $workingDays <= 30;

        // Supplier Orders with costs
        $pendingSupplierOrders = \App\Models\SupplierOrder::where('seller_id', $userId)
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->whereNotIn('status', ['Completed', 'Cancelled', 'Refunded']);
        
        $completedSupplierOrders = \App\Models\SupplierOrder::where('seller_id', $userId)
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->where('status', 'Completed');

        // Tính toán TikTok payout on hold
        $tikTokPayoutOnHold = Store::where('owner_id', $userId)
            ->sum('tiktok_payout_on_hold');


        // Tính tổng fund requests của seller
        $totalFundRequests = \App\Models\SellerFundRequest::where('seller_id', $userId)
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->where('status', 'approved')
            ->sum('amount');

        return [
            'yesterday_products' => Product::where('seller_id', $userId)
                ->whereBetween('created_at', [$yesterdayStart, $yesterdayEnd])
                ->count(),
            'daily_products' => Product::where('seller_id', $userId)
                ->whereBetween('created_at', [$dayStart, $dayEnd])
                ->count(),
            'weekly_products' => Product::where('seller_id', $userId)
                ->whereBetween('created_at', [$weekStart, $weekEnd])
                ->count(),
            'monthly_products' => Product::where('seller_id', $userId)
                ->whereBetween('created_at', [$monthStart, $monthEnd])
                ->count(),
            'yesterday_orders' => Order::where('seller_id', $userId)
                ->whereBetween('created_at', [$yesterdayStart, $yesterdayEnd])
                ->count(),
            'daily_orders' => Order::where('seller_id', $userId)
                ->whereBetween('created_at', [$dayStart, $dayEnd])
                ->count(),
            'weekly_orders' => Order::where('seller_id', $userId)
                ->whereBetween('created_at', [$weekStart, $weekEnd])
                ->count(),
            'monthly_orders' => Order::where('seller_id', $userId)
                ->whereBetween('created_at', [$monthStart, $monthEnd])
                ->count(),
            'working_days' => $workingDays,
            'is_new_seller' => $isNewSeller,
            'start_date' => $startDate->format('Y-m-d'),
            
            // TikTok Payments
            'tiktok_payment_today' => \App\Models\PayoutTransaction::whereHas('store', function($q) use ($userId) {
                    $q->where('owner_id', $userId);
                })
                ->whereBetween('time', [$dayStart, $dayEnd])
                ->where('status', 'Success')
                ->where('type', 'Receive')
                ->sum('amount'),
            'tiktok_payment_month' => \App\Models\PayoutTransaction::whereHas('store', function($q) use ($userId) {
                    $q->where('owner_id', $userId);
                })
                ->whereBetween('time', [$monthStart, $monthEnd])
                ->where('status', 'Success')
                ->where('type', 'Receive')
                ->sum('amount'),
            'tiktok_payout_on_hold' => $tikTokPayoutOnHold,
      
            
            // Supplier Orders
            'pending_supplier_orders' => [
                'count' => $pendingSupplierOrders->count(),
                'cost' => $pendingSupplierOrders->sum('base_cost'),
            ],
            'completed_supplier_orders' => [
                'count' => $completedSupplierOrders->count(),
                'cost' => $completedSupplierOrders->sum('base_cost'),
            ],
            
            // Bank Info
            'bank_name' => $user->bank_name,
            'bank_account_number' => $user->bank_account_number,
            
            'seller_level' => $user->sellerLevel ? [
                'id' => $user->sellerLevel->id,
                'name' => $user->sellerLevel->name,
                'code' => $user->sellerLevel->code,
                'base_salary' => $user->sellerLevel->base_salary,
                'bonus_rate' => $user->sellerLevel->bonus_rate,
                'commission_type' => $user->has_fixed_commission ? 'fixed' : 'variable',
                'commission_rate' => $user->has_fixed_commission ? $user->fixed_commission_rate : ($user->sellerLevel ? $user->sellerLevel->bonus_rate : 0),
                'has_base_salary' => $user->has_base_salary,
            ] : null,
            'total_fund_requests' => $totalFundRequests,
        ];
    }

    protected function getAvailableTeams(): array
    {
        $user = Auth::user();
        $teams = [];

        if ($user->hasAnyRole(['super_admin', 'Accountant', 'User Manager'])) {
            $teams = Team::all()->pluck('name', 'id')->toArray();
        } elseif ($user->hasRole('Leader')) {
            $teams = $user->teams()->pluck('teams.name', 'teams.id')->toArray();
        } elseif ($user->hasRole('Seller')) {
            $teams = Team::whereHas('users', function ($query) use ($user) {
                $query->where('users.id', $user->id);
            })->pluck('name', 'id')->toArray();
        }

        return $teams;
    }

    public function mount(): void
    {
        // Check for selected team in session
        $this->selectedTeamId = session('selected_team_id');
        
        // If no team is selected, find the first available team
        if (!$this->selectedTeamId) {
            $availableTeams = $this->getAvailableTeams();
            if (count($availableTeams) === 1) {
                $this->selectedTeamId = array_key_first($availableTeams);
                session(['selected_team_id' => $this->selectedTeamId]);
            } elseif (count($availableTeams) > 0) {
                // Default to first team
                $this->selectedTeamId = array_key_first($availableTeams);
                session(['selected_team_id' => $this->selectedTeamId]);
            } else {
                // No teams available, show notification
                Notification::make()
                    ->warning()
                    ->title('No teams available')
                    ->body('You do not belong to any team. Please contact an administrator.')
                    ->persistent()
                    ->send();
            }
        } else {
            // Verify access to the selected team
            $user = Auth::user();
            $hasTeamAccess = false;
            
            if ($user->hasAnyRole(['super_admin', 'Accountant', 'User Manager'])) {
                $hasTeamAccess = true;
            } elseif ($user->teams()->where('teams.id', $this->selectedTeamId)->exists()) {
                $hasTeamAccess = true;
            }
            
            if (!$hasTeamAccess) {
                // Reset session if no access
                session()->forget('selected_team_id');
                $this->selectedTeamId = null;
                
                Notification::make()
                    ->warning()
                    ->title('Session expired')
                    ->body('Please select your team again')
                    ->persistent()
                    ->send();
            }
        }
        
        // Load team data
        if ($this->selectedTeamId) {
            $this->loadTeamData();
        }
    }

    #[On('set-team')]
    public function setTeam($teamId): void
    {
        $this->isLoading = true;
        
        // Verify user has access to this team
        $user = Auth::user();
        
        // Find team in database
        $team = Team::find($teamId);
        if (!$team) {
            $this->isLoading = false;
            Notification::make()
                ->danger()
                ->title('Team doesn\'t exist')
                ->send();
            return;
        }
        
        // Check access permissions
        if ($user->hasRole('super_admin')) {
            $hasTeamAccess = true;
        } elseif ($user->hasRole('Leader')) {
            // Leaders only see their teams
            $hasTeamAccess = $user->teams()->where('teams.id', $teamId)->exists();
        } elseif ($user->hasRole('Seller')) {
            // Sellers only see teams they belong to
            $hasTeamAccess = $user->teams()->where('teams.id', $teamId)->exists();
        } else {
            $hasTeamAccess = false;
        }
        
        if (!$hasTeamAccess) {
            $this->isLoading = false;
            Notification::make()
                ->danger()
                ->title('Access denied')
                ->body('You don\'t have permission to view this team\'s data')
                ->send();
            return;
        }
        
        // Store teamId in session
        if (is_array($teamId) && isset($teamId['teamId'])) {
            $teamId = $teamId['teamId'];
        }
        
        session(['selected_team_id' => $teamId]);
        $this->selectedTeamId = $teamId;
        
        // Load team data
        $this->loadTeamData();
        
        $this->isLoading = false;
        
        Notification::make()
            ->success()
            ->title('Team data loaded')
            ->duration(3000)
            ->send();
    }

    public function updatedSelectedTeamId($value)
    {
        $this->isLoading = true;
        usleep(500000); // Add a small delay to prevent flickering
        $this->isLoading = false;
    }

    public function getTeamsData(): array
    {
        $this->isLoading = true;
        
        // Only load data for the selected team
        if (!$this->selectedTeamId) {
            $this->isLoading = false;
            return [];
        }
        
        // Get team data
        $team = Team::with([
            'users' => function ($query) {
                $query->select('users.id', 'users.name', 'users.email', 'users.avatar_url', 
                    'users.telegram_id', 'users.bank_name', 'users.bank_account_number', 
                    'users.seller_level_id', 'users.created_at')
                    ->with([
                        'roles',
                        'sellerLevel'
                    ]);
            }
        ])->find($this->selectedTeamId);
        
        if (!$team) {
            $this->isLoading = false;
            return [];
        }
        
        // Process the team data
        $result = [$this->processTeamData($team)];
        
        $this->isLoading = false;
        return $result;
    }

    protected function getTeamStats($sellers): array 
    {
        $now = Carbon::now();
        $monthStart = $now->copy()->startOfMonth();
        $monthEnd = $now->copy()->endOfMonth();
        $yesterdayStart = $now->copy()->subDay()->startOfDay();
        $yesterdayEnd = $now->copy()->subDay()->endOfDay();
        $dayStart = $now->copy()->startOfDay();
        $dayEnd = $now->copy()->endOfDay();
        $weekStart = $now->copy()->startOfWeek();
        $weekEnd = $now->copy()->endOfWeek();

        // Đảm bảo $sellerIds là một mảng đơn giản
        $sellerIds = $sellers->filter(function($user) {
            return $user && method_exists($user, 'hasRole') && $user->hasRole('Seller') && !$user->hasRole('Leader');
        })->pluck('id')->toArray();

        // Kiểm tra nếu mảng rỗng, tránh lỗi khi truy vấn
        if (empty($sellerIds)) {
            $sellerIds = [0]; // Sử dụng ID không tồn tại để trả về kết quả rỗng
        }

        // Tối ưu: Gộp các truy vấn liên quan đến thời gian
        $timeRanges = [
            'yesterday' => [$yesterdayStart, $yesterdayEnd],
            'daily' => [$dayStart, $dayEnd],
            'weekly' => [$weekStart, $weekEnd],
            'monthly' => [$monthStart, $monthEnd]
        ];

        // Tối ưu: Cache kết quả của các truy vấn phổ biến
        $productCounts = [];
        $orderCounts = [];
        foreach ($timeRanges as $range => [$start, $end]) {
            $productCounts[$range] = Product::whereIn('seller_id', $sellerIds)
                ->whereBetween('created_at', [$start, $end])
                ->count();
            
            $orderCounts[$range] = Order::whereIn('seller_id', $sellerIds)
                ->whereBetween('created_at', [$start, $end])
                ->count();
        }

        // Tối ưu: Sử dụng eager loading cho các mối quan hệ
        $completedSupplierOrders = \App\Models\SupplierOrder::whereIn('seller_id', $sellerIds)
            ->where('status', 'Completed')
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->select('seller_id', 'base_cost')
            ->get();

        $teamTikTokPayoutOnHold = Store::whereIn('owner_id', $sellerIds)
            ->sum('tiktok_payout_on_hold');

        // Get all stores belonging to these sellers
        $storeIds = Store::whereIn('owner_id', $sellerIds)->pluck('id')->toArray();
        
        // If no stores found, create an empty array to avoid query errors
        if (empty($storeIds)) {
            $storeIds = [0];
        }
        
        // Get bank accounts for all these stores
        $bankAccounts = Store::whereIn('id', $storeIds)->pluck('bank_account')->toArray();
        
        // Optimized: Get TikTok payments using bank accounts
        $tiktokPayments = \App\Models\PayoutTransaction::whereIn('card_no', $bankAccounts)
            ->where('status', 'Success')
            ->where('type', 'Receive')
            ->whereBetween('time', [$monthStart, $monthEnd])
            ->sum('amount');
            
        // If TikTok payments is 0, try to get from SellerFinance
        if ($tiktokPayments <= 0) {
            $tiktokPayments = \App\Models\SellerFinance::whereIn('seller_id', $sellerIds)
                ->whereBetween('month', [$monthStart, $monthEnd])
                ->sum('gross_revenue');
        }

        // Tối ưu: Gộp các truy vấn fund requests
        $fundRequests = \App\Models\SellerFundRequest::whereIn('seller_id', $sellerIds)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->sum('amount');

        // Tối ưu: Cache kết quả của các truy vấn phức tạp
        $threeMonthsAgo = $now->copy()->subMonths(3)->startOfMonth();
        $threeMonthsData = $this->getThreeMonthsData($sellerIds, $threeMonthsAgo, $monthEnd);

        // Lấy dữ liệu invoices cho tháng hiện tại
        $currentMonthInvoices = \App\Models\Invoice::whereIn('user_id', $sellerIds)
            ->whereBetween('billing_month', [$monthStart, $monthEnd])
            ->get();

        return [
            'yesterday_products' => $productCounts['yesterday'],
            'daily_products' => $productCounts['daily'],
            'weekly_products' => $productCounts['weekly'],
            'monthly_products' => $productCounts['monthly'],
            'yesterday_orders' => $orderCounts['yesterday'],
            'daily_orders' => $orderCounts['daily'],
            'weekly_orders' => $orderCounts['weekly'],
            'monthly_orders' => $orderCounts['monthly'],
            'tiktok_payments' => $tiktokPayments,
            'tiktok_payout_on_hold' => $teamTikTokPayoutOnHold,
            'supplier_orders' => [
                'count' => $completedSupplierOrders->count(),
                'cost' => $completedSupplierOrders->sum('base_cost')
            ],
            'fund_requests' => $fundRequests,
            'three_months' => $threeMonthsData,
            'monthly_stats' => $this->getMonthlyTeamStats($sellerIds),
            'invoices' => [
                'total' => $currentMonthInvoices->count(),
                'paid' => $currentMonthInvoices->where('status', 'paid')->count(),
                'partial' => $currentMonthInvoices->where('status', 'partial')->count(),
                'unpaid' => $currentMonthInvoices->whereIn('status', ['pending', 'approved', 'confirmed'])->count(),
                'total_amount' => $currentMonthInvoices->sum('total_amount'),
                'paid_amount' => $currentMonthInvoices->where('status', 'paid')->sum('total_amount'),
                'remaining_amount' => $currentMonthInvoices->sum('total_amount') - $currentMonthInvoices->where('status', 'paid')->sum('total_amount'),
                'partial_amount' => $currentMonthInvoices->where('status', 'partial')->sum('total_amount'),
            ],
        ];
    }

    // Tách logic xử lý dữ liệu 3 tháng ra một method riêng
    protected function getThreeMonthsData($sellerIds, $startDate, $endDate): array
    {
        // Get all stores belonging to these sellers
        $storeIds = Store::whereIn('owner_id', $sellerIds)->pluck('id')->toArray();
        
        // If no stores found, create an empty array to avoid query errors
        if (empty($storeIds)) {
            $storeIds = [0];
        }
        
        // Get bank accounts for all these stores
        $bankAccounts = Store::whereIn('id', $storeIds)->pluck('bank_account')->toArray();
        
        // Get revenue using bank accounts
        $revenue = \App\Models\PayoutTransaction::whereIn('card_no', $bankAccounts)
            ->whereBetween('time', [$startDate, $endDate])
            ->where('status', 'Success')
            ->where('type', 'Receive')
            ->sum('amount');
            
        // If revenue is 0, try to get it from SellerFinance as a backup
        if ($revenue <= 0) {
            $revenue = \App\Models\SellerFinance::whereIn('seller_id', $sellerIds)
                ->whereBetween('month', [$startDate, $endDate])
                ->sum('gross_revenue');
        }

        $supplierCost = \App\Models\SupplierOrder::whereIn('seller_id', $sellerIds)
            ->where('status', 'Completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('base_cost');

        $fundRequests = \App\Models\SellerFundRequest::whereIn('seller_id', $sellerIds)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        $totalCost = $supplierCost + $fundRequests;
        $grossProfit = $revenue - $totalCost;

        // Tối ưu: Sử dụng eager loading cho invoices
        $invoices = \App\Models\Invoice::whereIn('user_id', $sellerIds)
            ->whereBetween('billing_month', [$startDate, $endDate])
            ->get();

        $totalInvoiceAmount = $invoices->sum('total_amount');
        $paidInvoiceAmount = $invoices->where('status', 'paid')->sum('total_amount');
        $remainingToPay = $totalInvoiceAmount - $paidInvoiceAmount;

        return [
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'revenue' => $revenue,
            'supplier_cost' => $supplierCost,
            'fund_requests' => $fundRequests,
            'total_cost' => $totalCost,
            'profit' => $grossProfit,
            'profit_status' => $grossProfit > 0 ? 'profit' : 'loss',
            'total_invoice_amount' => $totalInvoiceAmount,
            'paid_invoice_amount' => $paidInvoiceAmount,
            'unpaid_invoice_amount' => $remainingToPay,
            'partial_invoice_amount' => $invoices->where('status', 'partial')->sum('total_amount'),
            'net_profit' => $grossProfit - $paidInvoiceAmount,
            'net_profit_status' => ($grossProfit - $paidInvoiceAmount) > 0 ? 'positive' : 'negative',
        ];
    }

    protected function getMonthlyTeamStats($sellerIds): array
    {
        $now = Carbon::now();
        $result = [];
        
        // Lấy tháng hiện tại làm mốc
        $currentMonth = $now->copy()->startOfMonth();
        
        // Tối ưu: Lấy dữ liệu cho 3 tháng trong một lần query
        $monthRanges = [];
        for ($i = 0; $i < 3; $i++) {
            $monthStart = $currentMonth->copy()->subMonths($i)->startOfMonth();
            $monthEnd = $monthStart->copy()->endOfMonth();
            $monthRanges[] = [
                'start' => $monthStart,
                'end' => $monthEnd,
                'key' => $monthStart->format('Y-m')
            ];
        }
        
        // First, get all stores belonging to these sellers
        $storeIds = Store::whereIn('owner_id', $sellerIds)->pluck('id')->toArray();
        
        // If no stores found, create an empty array to avoid query errors
        if (empty($storeIds)) {
            $storeIds = [0];
        }
        
        // Get bank accounts for all these stores
        $bankAccounts = Store::whereIn('id', $storeIds)->pluck('bank_account')->toArray();
        
        // Tối ưu: Lấy tất cả dữ liệu TikTok cho 3 tháng trong một lần query bằng cách sử dụng store relationship
        $tiktokData = \App\Models\PayoutTransaction::whereIn('card_no', $bankAccounts)
            ->where('status', 'Success')
            ->where('type', 'Receive')
            ->whereBetween('time', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->select('time', 'amount')
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->time)->format('Y-m');
            });
            
        // Backup from SellerFinance if TikTok data is empty
        $sellerFinanceData = \App\Models\SellerFinance::whereIn('seller_id', $sellerIds)
            ->whereBetween('month', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->select('month', 'gross_revenue')
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->month)->format('Y-m');
            });

        // Tối ưu: Lấy tất cả supplier orders cho 3 tháng trong một lần query
        $supplierOrders = \App\Models\SupplierOrder::whereIn('seller_id', $sellerIds)
            ->where('status', 'Completed')
            ->whereBetween('created_at', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->select('created_at', 'base_cost')
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->created_at)->format('Y-m');
            });

        // Tối ưu: Lấy tất cả fund requests cho 3 tháng trong một lần query
        $fundRequests = \App\Models\SellerFundRequest::whereIn('seller_id', $sellerIds)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->select('created_at', 'amount')
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->created_at)->format('Y-m');
            });

        // Tối ưu: Lấy tất cả invoices cho 3 tháng trong một lần query
        $invoices = \App\Models\Invoice::whereIn('user_id', $sellerIds)
            ->whereBetween('billing_month', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->with(['items'])
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->billing_month)->format('Y-m');
            });

        // Tối ưu: Lấy tất cả orders cho 3 tháng trong một lần query
        $orders = \App\Models\SupplierOrder::whereIn('seller_id', $sellerIds)
            ->whereBetween('created_at', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->select('created_at', 'status')
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->created_at)->format('Y-m');
            });

        foreach ($monthRanges as $i => $range) {
            $monthKey = $range['key'];
            $monthStart = $range['start'];
            $monthEnd = $range['end'];

            // Tính toán từ dữ liệu đã cache
            $monthTiktokData = $tiktokData->get($monthKey, collect());
            $monthSupplierOrders = $supplierOrders->get($monthKey, collect());
            $monthFundRequests = $fundRequests->get($monthKey, collect());
            $monthInvoices = $invoices->get($monthKey, collect());
            $monthOrders = $orders->get($monthKey, collect());
            $monthSellerFinance = $sellerFinanceData->get($monthKey, collect());
            
            // Try to get revenue from TikTok payout transactions first
            $revenue = $monthTiktokData->sum('amount');
            
            // If TikTok revenue is 0, try to get it from SellerFinance as a backup
            if ($revenue <= 0 && $monthSellerFinance->isNotEmpty()) {
                $revenue = $monthSellerFinance->sum('gross_revenue');
            }
            
            $supplierCost = $monthSupplierOrders->sum('base_cost');
            $fundRequestCost = $monthFundRequests->sum('amount');
            $totalCost = $supplierCost + $fundRequestCost;
            $grossProfit = $revenue - $totalCost;
            $totalSalary = $monthInvoices->sum('total_amount');
            $netProfit = $grossProfit - $totalSalary;

            $result[] = [
                'month' => $i === 0 
                    ? 'Current Month (' . $monthStart->format('F Y') . ')' 
                    : $monthStart->format('F Y'),
                'month_key' => $monthKey,
                'date_range' => $monthStart->format('d/m/Y') . ' - ' . $monthEnd->format('d/m/Y'),
                'is_current' => $i === 0,
                'revenue' => $revenue,
                'total_cost' => $totalCost,
                'supplier_cost' => $supplierCost,
                'fund_requests' => $fundRequestCost,
                'gross_profit' => $grossProfit,
                'gross_profit_status' => $grossProfit >= 0 ? 'profit' : 'loss',
                'net_profit' => $netProfit,
                'net_profit_status' => $netProfit >= 0 ? 'profit' : 'loss',
                'profit' => [
                    'gross' => $grossProfit,
                    'net' => $netProfit,
                    'margin' => $revenue > 0 ? ($netProfit / $revenue * 100) : 0,
                    'net_profit_status' => $netProfit >= 0 ? 'profit' : 'loss'
                ],
                'invoices' => [
                    'total' => $monthInvoices->count(),
                    'paid' => $monthInvoices->where('status', 'paid')->count(),
                    'partial' => $monthInvoices->where('status', 'partial')->count(),
                    'unpaid' => $monthInvoices->whereIn('status', ['pending', 'approved', 'confirmed'])->count(),
                    'total_amount' => $totalSalary,
                    'paid_amount' => $monthInvoices->where('status', 'paid')->sum('total_amount'),
                    'remaining_amount' => $totalSalary - $monthInvoices->where('status', 'paid')->sum('total_amount'),
                ],
                'order_count' => $monthOrders->count(),
                'orders' => [
                    'total' => $monthOrders->count(),
                    'completed' => $monthOrders->where('status', 'Completed')->count(),
                    'cancelled' => $monthOrders->where('status', 'Cancelled')->count(),
                ],
                'kpis' => [
                    'revenue_per_order' => $monthOrders->where('status', 'Completed')->count() > 0 
                        ? ($revenue / $monthOrders->where('status', 'Completed')->count()) 
                        : 0,
                    'cost_per_order' => $monthOrders->where('status', 'Completed')->count() > 0 
                        ? ($totalCost / $monthOrders->where('status', 'Completed')->count()) 
                        : 0,
                    'profit_per_order' => $monthOrders->where('status', 'Completed')->count() > 0 
                        ? ($netProfit / $monthOrders->where('status', 'Completed')->count()) 
                        : 0,
                    'completion_rate' => $monthOrders->count() > 0 
                        ? ($monthOrders->where('status', 'Completed')->count() / $monthOrders->count() * 100) 
                        : 0,
                    'cancellation_rate' => $monthOrders->count() > 0 
                        ? ($monthOrders->where('status', 'Cancelled')->count() / $monthOrders->count() * 100) 
                        : 0
                ]
            ];
        }
        
        return $result;
    }



    protected function getOrderColorClass($count, $isNewSeller = false, $workingDays = 0): string 
    {
        // Điều chỉnh ngưỡng cho seller mới (dưới 30 ngày)
        if ($isNewSeller) {
            if ($workingDays <= 7) { // 7 ngày đầu
                if ($count < 1) return 'bg-red-100 dark:bg-red-900/50';
                if ($count < 5) return 'bg-orange-50 dark:bg-orange-900/50';
                if ($count < 15) return 'bg-yellow-50 dark:bg-yellow-900/50';
                if ($count < 25) return 'bg-lime-50 dark:bg-lime-900/50';
                if ($count >= 50) return 'bg-green-100 dark:bg-green-900/50';
            } else { // 8-30 ngày
                if ($count < 2) return 'bg-red-100 dark:bg-red-900/50';
                if ($count < 7) return 'bg-orange-50 dark:bg-orange-900/50';
                if ($count < 20) return 'bg-yellow-50 dark:bg-yellow-900/50';
                if ($count < 35) return 'bg-lime-50 dark:bg-lime-900/50';
                if ($count >= 70) return 'bg-green-100 dark:bg-green-900/50';
            }
        } else { // Seller cũ (>30 ngày)
            if ($count < 2) return 'bg-red-100 dark:bg-red-900/50';
            if ($count < 10) return 'bg-orange-50 dark:bg-orange-900/50';
            if ($count < 30) return 'bg-yellow-50 dark:bg-yellow-900/50';
            if ($count < 50) return 'bg-lime-50 dark:bg-lime-900/50';
            if ($count >= 100) return 'bg-green-100 dark:bg-green-900/50';
        }
        
        return 'bg-white dark:bg-gray-800';
    }

    protected function getPerformanceLabel($count, $isNewSeller = false, $workingDays = 0): string
    {
        if ($isNewSeller) {
            return $this->getNewSellerPerformanceLabel($count, $workingDays);
        }
        
        // Chỉ trả về label cho các trường hợp tốt
        if ($count >= 100) return 'Tốt';
        return ''; // Các trường hợp còn lại không hiển thị label
    }

    protected function getNewSellerPerformanceLabel($count, $workingDays): string
    {
        if ($workingDays <= 7) {
            if ($count >= 50) return 'Tốt (Mới)';
        } else {
            if ($count >= 70) return 'Tốt (Mới)';
        }
        return '';
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('updateLevel')
                ->label('Cập nhật Level')
                ->modalHeading('Cập nhật Level Seller')
                ->modalDescription('Chọn level mới cho seller')
                ->form([
                    Select::make('seller_level_id')
                        ->label('Level')
                        ->options(SellerLevel::where('is_active', true)->pluck('name', 'id'))
                        ->required()
                ])
                ->action(function (array $data): void {
                    $user = User::findOrFail($this->selectedSellerId);
                    $user->update([
                        'seller_level_id' => $data['seller_level_id']
                    ]);
                    
                    Notification::make()
                        ->success()
                        ->title('Level đã được cập nhật')
                        ->description('Level của seller đã được cập nhật thành công')
                        ->send();
                })
                ->modalSubmitActionLabel('Cập nhật')
                ->hidden()
                ->after(function () {
                    $this->redirect(LeaderDashboard::getUrl());
                })
        ];
    }

    public function showUpdateLevelModal($sellerId, $currentLevelId): void
    {
        $this->selectedSellerId = $sellerId;
        $this->selectedSellerLevelId = $currentLevelId;
        $this->mountAction('updateLevel');
    }

    /**
     * Check if current user can modify commission settings or level
     */
    protected function canModifyCommissionSettings(): bool
    {
        $user = Auth::user();
        return $user->hasRole(['super_admin','User Manager']);
    }

    public function updateCommissionSetting($sellerId, $setting, $value)
    {
        try {
            // Check if user has permission to modify settings
            if (!$this->canModifyCommissionSettings()) {
                Notification::make()
                    ->danger()
                    ->title('Access denied')
                    ->body('You do not have permission to modify commission settings')
                    ->duration(5000)
                    ->send();
                return;
            }
            
            $user = User::findOrFail($sellerId);
            
            switch ($setting) {
                case 'has_fixed_commission':
                    $user->has_fixed_commission = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                    break;
                    
                case 'fixed_commission_rate':
                    $value = floatval($value);
                    // Giới hạn giá trị từ 0-100
                    $user->fixed_commission_rate = max(0, min(100, $value));
                    
                    // Lưu giá trị vào biến commissionRates để hiển thị đúng trên form
                    $this->commissionRates[$sellerId] = $user->fixed_commission_rate;
                    break;
                    
                case 'has_base_salary':
                    $user->has_base_salary = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                    break;
                    
                default:
                    throw new \Exception('Invalid setting type');
            }
            
            $user->save();
            
            // Làm mới dữ liệu
            $this->loadTeamData();
            
            Notification::make()
                ->success()
                ->title('Setting updated: ' . $user->name)
                ->body($setting . ' set to ' . $value)
                ->duration(3000)
                ->send();
            
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Error updating setting')
                ->body($e->getMessage())
                ->duration(5000)
                ->send();
        }
    }

    public function updateSellerLevel($sellerId, $newLevelId)
    {
        try {
            // Check if user has permission to modify settings
            if (!$this->canModifyCommissionSettings()) {
                Notification::make()
                    ->danger()
                    ->title('Access denied')
                    ->body('You do not have permission to modify seller level')
                    ->duration(5000)
                    ->send();
                return;
            }
            
            if (empty($newLevelId)) {
                throw new \Exception('Please select a level');
            }

            $user = User::findOrFail($sellerId);
            $level = SellerLevel::where('is_active', true)->findOrFail($newLevelId);

            // Store old level name for notification
            $oldLevelName = $user->sellerLevel ? $user->sellerLevel->name : 'None';
            
            // Update user's level
            $user->update([
                'seller_level_id' => $newLevelId
            ]);

            // Update level in model
            $this->setSellerLevelInCache($sellerId, $newLevelId);
            
            // Refresh team data
            $this->teamData = $this->getTeamData($this->selectedTeamId);

            // Send success notification
            Notification::make()
                ->success()
                ->title('Level updated')
                ->body("Level for {$user->name} has been changed from {$oldLevelName} to {$level->name}")
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Failed to update level')
                ->body('An error occurred while updating the level. Please try again.')
                ->send();
        }
    }

    // Method to update sellerLevels property
    protected function setSellerLevelInCache($sellerId, $levelId)
    {
        if (!isset($this->sellerLevels)) {
            $this->sellerLevels = [];
        }
        
        $this->sellerLevels[$sellerId] = $levelId;
    }

    protected function processTeamData($team): array
    {
        $result = [
            'id' => $team->id,
            'team_name' => $team->name,
        ];
        
        // Filter users by role
        $sellers = $team->users->filter(function($user) {
            return $user && method_exists($user, 'hasRole') && $user->hasRole('Seller');
        });
        
        $leaders = $team->users->filter(function($user) {
            return $user && method_exists($user, 'hasRole') && $user->hasRole('Leader');
        });
        
        // First, get team stats with basic info that's needed immediately
        $sellerIds = $sellers->pluck('id')->toArray();
        
        // Only calculate full stats if we have sellers
        if (!empty($sellerIds)) {
            // Load basic stats first (most important counters)
            $basicStats = $this->getBasicTeamStats($sellerIds);
            $result['stats'] = $basicStats;
            
            // Always include monthly stats
            $monthlyStats = $this->getMonthlyTeamStats($sellerIds);
            $result['monthly_stats'] = $monthlyStats;
        } else {
            $result['stats'] = $this->getEmptyStats();
            $result['monthly_stats'] = [];
        }

        // Load simplified leader data
        $result['leaders'] = $leaders->map(function($leader) {
            return [
                'id' => $leader->id,
                'name' => $leader->name,
                'email' => $leader->email,
                'avatar_url' => $leader->getFilamentAvatarUrl(),
                'telegram_id' => $leader->telegram_id,
            ];
        })->values();

        // Process each seller to get their individual stats
        $processedSellers = $sellers->map(function($seller) {
            $stats = $this->getBasicSellerStats($seller);
            $storeCounts = $this->getSellerStoreCounts($seller->id);
            
            // Default values for has_fixed_commission and has_base_salary
            $has_fixed_commission = $seller->has_fixed_commission ?? false;
            $fixed_commission_rate = $seller->fixed_commission_rate ?? 0;
            $has_base_salary = $seller->has_base_salary ?? true;
            
            // Fetch recent invoices for this seller (last 3 months)
            $now = Carbon::now();
            $threeMonthsAgo = $now->copy()->subMonths(3)->startOfMonth();
            $recentInvoices = \App\Models\Invoice::where('user_id', $seller->id)
                ->whereBetween('billing_month', [$threeMonthsAgo, $now])
                ->orderBy('billing_month', 'desc')
                ->select('id', 'invoice_number', 'billing_month', 'total_amount', 'paid_amount', 'status')
                ->get()
                ->map(function($invoice) {
                    return [
                        'id' => $invoice->id,
                        'number' => $invoice->invoice_number,
                        'month' => $invoice->billing_month->format('M Y'),
                        'total' => $invoice->total_amount,
                        'paid' => $invoice->paid_amount,
                        'remaining' => $invoice->total_amount - $invoice->paid_amount,
                        'status' => $invoice->status,
                        'status_color' => $this->getInvoiceStatusColor($invoice->status),
                    ];
                });
            
            return [
                'id' => $seller->id,
                'name' => $seller->name,
                'email' => $seller->email,
                'avatar_url' => $seller->getFilamentAvatarUrl(),
                'telegram_id' => $seller->telegram_id,
                'bank_name' => $seller->bank_name,
                'bank_account_number' => $seller->bank_account_number,
                'is_leader' => $seller->hasRole('Leader'),
                'has_fixed_commission' => $has_fixed_commission,
                'fixed_commission_rate' => $fixed_commission_rate,
                'has_base_salary' => $has_base_salary,
                'seller_level' => $seller->sellerLevel ? [
                    'id' => $seller->sellerLevel->id,
                    'name' => $seller->sellerLevel->name,
                    'base_salary' => $seller->sellerLevel->base_salary,
                    'bonus_rate' => $seller->sellerLevel->bonus_rate,
                    'commission_type' => $has_fixed_commission ? 'fixed' : 'variable',
                    'commission_rate' => $has_fixed_commission ? $fixed_commission_rate : ($seller->sellerLevel ? $seller->sellerLevel->bonus_rate : 0),
                    'has_base_salary' => $has_base_salary,
                ] : null,
                'working_days' => $stats['working_days'] ?? Carbon::parse($seller->created_at)->diffInDays(Carbon::now()) + 1,
                'is_new_seller' => $stats['is_new_seller'] ?? (Carbon::parse($seller->created_at)->diffInDays(Carbon::now()) + 1) <= 30,
                'yesterday_orders' => $stats['yesterday_orders'] ?? 0,
                'daily_orders' => $stats['daily_orders'] ?? 0,
                'weekly_orders' => $stats['weekly_orders'] ?? 0,
                'monthly_orders' => $stats['monthly_orders'] ?? 0,
                'yesterday_products' => $stats['yesterday_products'] ?? 0,
                'daily_products' => $stats['daily_products'] ?? 0,
                'weekly_products' => $stats['weekly_products'] ?? 0,
                'monthly_products' => $stats['monthly_products'] ?? 0,
                'tiktok_payment_today' => $stats['tiktok_payment_today'] ?? 0,
                'tiktok_payment_month' => $stats['tiktok_payment_month'] ?? 0,
                'tiktok_payout_on_hold' => $stats['tiktok_payout_on_hold'] ?? 0,
                'total_fund_requests' => $stats['total_fund_requests'] ?? 0,
                'store_count' => $storeCounts['total'] ?? 0,
                'active_stores' => $storeCounts['active'] ?? 0,
                'pending_stores' => $storeCounts['pending'] ?? 0,
                'suspended_stores' => $storeCounts['suspended'] ?? 0,
                'inactive_stores' => $storeCounts['inactive'] ?? 0,
                'not_synced_stores' => $storeCounts['not_synced'] ?? 0,
                'recent_invoices' => $recentInvoices,
            ];
        })->values();

        $result['sellers'] = $processedSellers;
        return $result;
    }
    
    /**
     * Get color class for invoice status
     */
    protected function getInvoiceStatusColor($status): string
    {
        switch ($status) {
            case self::INVOICE_STATUS['PAID']:
                return 'success';
            case self::INVOICE_STATUS['PARTIAL']:
                return 'warning';
            case self::INVOICE_STATUS['PENDING']:
            case self::INVOICE_STATUS['APPROVED']:
            case self::INVOICE_STATUS['CONFIRMED']:
                return 'info';
            case self::INVOICE_STATUS['CANCELLED']:
                return 'danger';
            default:
                return 'secondary';
        }
    }

    /**
     * Get basic statistics for an individual seller
     */
    protected function getBasicSellerStats($seller): array
    {
        $now = Carbon::now();
        $startDate = $seller->created_at;
        $monthStart = max($startDate, $now->copy()->startOfMonth());
        $weekStart = max($startDate, $now->copy()->startOfWeek());
        
        $yesterdayStart = $now->copy()->subDay()->startOfDay();
        $yesterdayEnd = $now->copy()->subDay()->endOfDay();
        $dayStart = $now->copy()->startOfDay();
        $dayEnd = $now->copy()->endOfDay();
        $weekEnd = $now->copy()->endOfWeek();
        $monthEnd = $now->copy()->endOfMonth();
        
        // Get order statistics
        $orderStats = Order::where('seller_id', $seller->id)
            ->selectRaw('
                COUNT(*) as total_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as yesterday_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as daily_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as weekly_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as monthly_orders
            ', [
                $yesterdayStart, $yesterdayEnd,
                $dayStart, $dayEnd,
                $weekStart, $weekEnd,
                $monthStart, $monthEnd
            ])
            ->first();
            
        // Get product statistics
        $productStats = Product::where('seller_id', $seller->id)
            ->selectRaw('
                COUNT(*) as total_products,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as yesterday_products,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as daily_products,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as weekly_products,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as monthly_products
            ', [
                $yesterdayStart, $yesterdayEnd,
                $dayStart, $dayEnd,
                $weekStart, $weekEnd,
                $monthStart, $monthEnd
            ])
            ->first();
        
        // Get TikTok payment data
        $tikTokPayoutOnHold = Store::where('owner_id', $seller->id)
            ->sum('tiktok_payout_on_hold');
        
        // Get all stores belonging to this seller
        $storeIds = Store::where('owner_id', $seller->id)->pluck('id')->toArray();
        
        // If no stores found, use 0 for everything
        if (empty($storeIds)) {
            return [
                'yesterday_orders' => $orderStats->yesterday_orders ?? 0,
                'daily_orders' => $orderStats->daily_orders ?? 0,
                'weekly_orders' => $orderStats->weekly_orders ?? 0,
                'monthly_orders' => $orderStats->monthly_orders ?? 0,
                'yesterday_products' => $productStats->yesterday_products ?? 0,
                'daily_products' => $productStats->daily_products ?? 0,
                'weekly_products' => $productStats->weekly_products ?? 0,
                'monthly_products' => $productStats->monthly_products ?? 0,
                'tiktok_payment_today' => 0,
                'tiktok_payment_month' => 0,
                'tiktok_payout_on_hold' => 0,
                'total_fund_requests' => 0,
            ];
        }
        
        // Get bank accounts for all stores
        $bankAccounts = Store::whereIn('id', $storeIds)->pluck('bank_account')->toArray();
        
        // Use bank accounts to query PayoutTransaction
        $tiktokPaymentToday = \App\Models\PayoutTransaction::whereIn('card_no', $bankAccounts)
            ->where('status', 'Success')
            ->where('type', 'Receive')
            ->whereBetween('time', [$dayStart, $dayEnd])
            ->sum('amount');
        
        $tiktokPaymentMonth = \App\Models\PayoutTransaction::whereIn('card_no', $bankAccounts)
            ->where('status', 'Success')
            ->where('type', 'Receive')
            ->whereBetween('time', [$monthStart, $monthEnd])
            ->sum('amount');
        
        // Get fund requests
        $fundRequests = DB::table('seller_fund_requests')
            ->where('seller_id', $seller->id)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->sum('amount');
        
        return [
            'yesterday_orders' => $orderStats->yesterday_orders ?? 0,
            'daily_orders' => $orderStats->daily_orders ?? 0,
            'weekly_orders' => $orderStats->weekly_orders ?? 0,
            'monthly_orders' => $orderStats->monthly_orders ?? 0,
            'yesterday_products' => $productStats->yesterday_products ?? 0,
            'daily_products' => $productStats->daily_products ?? 0,
            'weekly_products' => $productStats->weekly_products ?? 0,
            'monthly_products' => $productStats->monthly_products ?? 0,
            'tiktok_payment_today' => $tiktokPaymentToday ?? 0,
            'tiktok_payment_month' => $tiktokPaymentMonth ?? 0,
            'tiktok_payout_on_hold' => $tikTokPayoutOnHold ?? 0,
            'total_fund_requests' => $fundRequests ?? 0,
        ];
    }

    /**
     * Get store counts for a seller
     */
    protected function getSellerStoreCounts($sellerId): array
    {
        // Get store counts by status
        $storeStats = Store::where('owner_id', $sellerId)
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN tiktok_shop_status = ? THEN 1 ELSE 0 END) as active,
                SUM(CASE WHEN tiktok_shop_status = ? THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN tiktok_shop_status = ? THEN 1 ELSE 0 END) as suspended,
                SUM(CASE WHEN status = "inactive" THEN 1 ELSE 0 END) as inactive
            ', [
                TiktokShopStatus::Live,
                TiktokShopStatus::NotConnected,
                TiktokShopStatus::Suspended
            ])
            ->first();
            
        // Calculate stores not synced for over 7 days
        $notSyncedThreshold = Carbon::now()->subDays(7);
        $notSyncedStores = Store::where('owner_id', $sellerId)
            ->where('tiktok_shop_status', TiktokShopStatus::Live)
            ->where(function($query) use ($notSyncedThreshold) {
                $query->whereNull('last_sync_tiktok')
                    ->orWhere('last_sync_tiktok', '<', $notSyncedThreshold);
            })
            ->count();
        
        return [
            'total' => $storeStats->total ?? 0,
            'active' => $storeStats->active ?? 0,
            'pending' => $storeStats->pending ?? 0,
            'suspended' => $storeStats->suspended ?? 0,
            'inactive' => $storeStats->inactive ?? 0,
            'not_synced' => $notSyncedStores ?? 0,
        ];
    }

    /**
     * Get basic team statistics
     */
    protected function getBasicTeamStats($sellerIds): array
    {
        if (empty($sellerIds)) {
            return $this->getEmptyStats();
        }
        
        $now = Carbon::now();
        $monthStart = $now->copy()->startOfMonth();
        $monthEnd = $now->copy()->endOfMonth();
        $yesterdayStart = $now->copy()->subDay()->startOfDay();
        $yesterdayEnd = $now->copy()->subDay()->endOfDay();
        $dayStart = $now->copy()->startOfDay();
        $dayEnd = $now->copy()->endOfDay();
        $weekStart = $now->copy()->startOfWeek();
        $weekEnd = $now->copy()->endOfWeek();
        
        // Get order counts
        $orderStats = Order::whereIn('seller_id', $sellerIds)
            ->selectRaw('
                COUNT(*) as total_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as yesterday_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as daily_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as weekly_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as monthly_orders
            ', [
                $yesterdayStart, $yesterdayEnd,
                $dayStart, $dayEnd,
                $weekStart, $weekEnd,
                $monthStart, $monthEnd
            ])
            ->first();
        
        // Get TikTok payment data
        $tikTokPayoutOnHold = Store::whereIn('owner_id', $sellerIds)
            ->sum('tiktok_payout_on_hold');
        
        // Get all stores belonging to these sellers
        $storeIds = Store::whereIn('owner_id', $sellerIds)->pluck('id')->toArray();
        
        // If no stores found, create an empty array to avoid query errors
        if (empty($storeIds)) {
            $storeIds = [0];
        }
        
        // Get bank accounts for all these stores
        $bankAccounts = Store::whereIn('id', $storeIds)->pluck('bank_account')->toArray();
        
        // Optimized: Get TikTok payments using bank accounts
        $tiktokPayments = \App\Models\PayoutTransaction::whereIn('card_no', $bankAccounts)
            ->where('status', 'Success')
            ->where('type', 'Receive')
            ->whereBetween('time', [$monthStart, $monthEnd])
            ->sum('amount');
        
        // If TikTok payments is 0, try to get from SellerFinance
        if ($tiktokPayments <= 0) {
            $tiktokPayments = \App\Models\SellerFinance::whereIn('seller_id', $sellerIds)
                ->whereBetween('month', [$monthStart, $monthEnd])
                ->sum('gross_revenue');
        }
        
        // Get supplier order data
        $supplierOrders = DB::table('supplier_orders')
            ->whereIn('seller_id', $sellerIds)
            ->where('status', 'Completed')
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->selectRaw('COUNT(*) as count, SUM(base_cost) as total_cost')
            ->first();
        
        // Get fund requests
        $fundRequests = DB::table('seller_fund_requests')
            ->whereIn('seller_id', $sellerIds)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->sum('amount');
        
        return [
            'yesterday_orders' => $orderStats->yesterday_orders ?? 0,
            'daily_orders' => $orderStats->daily_orders ?? 0,
            'weekly_orders' => $orderStats->weekly_orders ?? 0,
            'monthly_orders' => $orderStats->monthly_orders ?? 0,
            'tiktok_payments' => $tiktokPayments ?? 0,
            'tiktok_payout_on_hold' => $tikTokPayoutOnHold ?? 0,
            'supplier_orders' => [
                'count' => $supplierOrders->count ?? 0,
                'cost' => $supplierOrders->total_cost ?? 0
            ],
            'fund_requests' => $fundRequests ?? 0
        ];
    }

    /**
     * Get empty statistics structure
     */
    protected function getEmptyStats(): array
    {
        return [
            'yesterday_orders' => 0,
            'daily_orders' => 0,
            'weekly_orders' => 0,
            'monthly_orders' => 0,
            'tiktok_payments' => 0,
            'tiktok_payout_on_hold' => 0,
            'supplier_orders' => [
                'count' => 0,
                'cost' => 0
            ],
            'fund_requests' => 0
        ];
    }


    /**
     * Get data for a specific team by its ID
     */
    public function getTeamData($teamId): array
    {
        $action = new GetTeamMembersData();
        return $action->execute($teamId);
    }

    // Hàm riêng để load lại dữ liệu team
    public function loadTeamData()
    {
        if ($this->selectedTeamId) {
            $this->teamData = $this->getTeamData($this->selectedTeamId);
            
            // Khởi tạo lại commissionRates
            if (!empty($this->teamData['sellers'])) {
                foreach ($this->teamData['sellers'] as $seller) {
                    if (isset($seller['has_fixed_commission']) && $seller['has_fixed_commission']) {
                        $this->commissionRates[$seller['id']] = $seller['fixed_commission_rate'] ?? 0;
                    }
                }
            }
        }
    }
}
