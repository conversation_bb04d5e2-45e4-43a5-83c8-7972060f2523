<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Actions\Action;

class MySellerDashboard extends Page
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';
    protected static ?string $navigationLabel = 'Thông tin của tôi';
    protected static ?string $title = 'Thông tin Seller của tôi';
    protected static ?string $navigationGroup = 'Seller Management';
    protected static ?int $navigationSort = 0;
    protected static string $view = 'filament.app.pages.my-seller-dashboard';
    protected static ?string $slug = 'my-seller-dashboard';

    protected static bool $shouldRegisterNavigation = false;

    public static function canAccess(): bool
    {
        return Auth::user()->hasRole(['super_admin', 'User Manager']);
    }

  

    protected function getHeaderActions(): array
    {
        $user = Auth::user();
        
        return [
            Action::make('view_my_overview')
                ->label('Xem tổng quan')
                ->icon('heroicon-o-chart-bar-square')
                ->color('primary')
                ->url('/app/seller-overview')
                ->tooltip('Xem tổng quan thông tin seller'),
                
            Action::make('view_my_invoice')
                ->label('Xem báo cáo tài chính')
                ->icon('heroicon-o-document-text')
                ->color('success')
                ->url('/app/seller-invoice/' . $user->id)
                ->tooltip('Xem báo cáo tài chính chi tiết'),
        ];
    }

    public function mount(): void
    {
        // Đảm bảo chỉ seller mới có thể truy cập
        $user = Auth::user();
        if (!$user || !$user->hasRole('Seller')) {
            abort(403, 'Bạn không có quyền truy cập trang này');
        }
    }

    public function getUser(): User
    {
        return Auth::user();
    }
}
