<?php

namespace App\Filament\App\Pages;

use App\Models\Store;
use App\Models\Template;
use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use App\Services\Tiktok\TiktokShopService;
use App\Services\Tiktok\ProductUploadService;
use App\Models\ProductToUpload;
use App\Enums\ProductUploadStatus;
use App\Enums\TiktokShopStatus;
use App\Forms\Components\ImageGalleryField;
use App\Services\ProductImportService;
use Filament\Forms\Components\Actions;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Get;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Set;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Grid;

class CreateProduct extends Page implements Forms\Contracts\HasForms
{
    use Forms\Concerns\InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-plus-circle';
    protected static ?string $navigationLabel = 'Create Product';
    protected static ?string $title = 'Create Product';
    protected static string $view = 'filament.app.pages.create-product';
    protected static ?string $navigationGroup = 'Products';
    protected static bool $shouldRegisterNavigation = false;
    public $selectedStore;
    public $templateId;
    public $productTitle;
    public $productDescription;
    public $productImages = [];
    public $sizeChartImage;
    public $productCategoryId;
    public $skus = [];
    public $warehouseId;
    public $warehouses = [];
    public $saveMode;
    public $total_skus = 0;
    public $price_range = '-';
    public $variants = '-';
    public $scheduled_at;
    public $source_url = '';

    protected function getFormSchema(): array
    {
        return [
            Forms\Components\Grid::make()
                ->schema([
                    // Left Column - Main Information (2/3 width)
                    Forms\Components\Section::make()
                        ->schema([
                            // Store Selection
                            Select::make('selectedStore')
                                ->label('Select Store')
                                ->options(function () {
                                    return Store::where('app_partner_id', '!=', '')
                                        ->whereIn('tiktok_shop_status', [TiktokShopStatus::Live, TiktokShopStatus::NotConnected])
                                        ->orderBy('id', 'desc')
                                        ->pluck('name', 'id');
                                })
                                ->searchable()
                                ->preload()

                                ->required()
                                ->live()
                                ->afterStateUpdated(function ($state) {
                                    if ($state) {
                                        $this->loadWarehouses();
                                    }
                                })
                                ->columnSpanFull(),

                            Forms\Components\Grid::make()
                                ->schema([
                                    Select::make('templateId')
                                        ->label('Template')
                                        ->options(function () {
                                            return Template::orderBy('created_at', 'desc')
                                                ->get()
                                                ->mapWithKeys(function ($template) {
                                                    $activeSkus = collect($template->skus)->filter(function ($sku) {
                                                        return ($sku['stock'] ?? 0) > 0;
                                                    })->count();

                                                    $typeStats = collect($template->skus)
                                                        ->flatMap(function ($sku) {
                                                            return collect($sku['sales_attributes'] ?? [])->filter(function ($attr) {
                                                                return in_array($attr['value_name'], [
                                                                    'T-shirt',
                                                                    'Hoodie',
                                                                    'Sweatshirt',
                                                                    'Unisex T-Shirt',
                                                                    'Unisex Hoodie',
                                                                    'Unisex Sweatshirt'
                                                                ]);
                                                            });
                                                        })
                                                        ->groupBy('value_name')
                                                        ->map(function ($group) {
                                                            return $group->count();
                                                        })
                                                        ->map(function ($count, $type) {
                                                            return "{$type} ({$count})";
                                                        })
                                                        ->implode(', ');

                                                    $prices = collect($template->skus)->pluck('price');
                                                    $priceRange = $prices->min() === $prices->max()
                                                        ? "\${$prices->min()}"
                                                        : "\${$prices->min()} - \${$prices->max()}";

                                                    $warningClass = $activeSkus > 300 ? 'text-danger-600' : '';

                                                    return [
                                                        $template->id => view('filament.components.template-option', [
                                                            'name' => $template->name,
                                                            'typeStats' => $typeStats,
                                                            'activeSkus' => $activeSkus,
                                                            'priceRange' => $priceRange,
                                                            'warningClass' => $warningClass,
                                                            'creator' => $template->user?->name ?? 'Unknown', // Thêm thông tin người tạo
                                                        ])->render()
                                                    ];
                                                });
                                        })
                                        ->allowHtml()
                                        ->searchable()
                                        ->live()
                                        ->afterStateUpdated(function ($state) {
                                            $template = Template::find($state);
                                            if ($template) {
                                                $this->form->fill([
                                                    'productDescription' => $template->description,
                                                    'productCategoryId' => $template->category_id,
                                                    'skus' => $template->skus,
                                                    'sizeChartImage' => $template->size_chart,
                                                ]);
                                                $this->updateSkuSummary($template->skus ?? []);

                                                if ($this->total_skus > 300) {
                                                    Notification::make()
                                                        ->title('Warning')
                                                        ->body("This template has {$this->total_skus} SKUs. TikTok Shop only allows up to 300 SKUs per product.")
                                                        ->danger()
                                                        ->duration(5000)
                                                        ->persistent()
                                                        ->send();
                                                }
                                            }
                                        })
                                        ->required()
                                        ->columnSpan(2),
                                    TextInput::make('productTitle')
                                        ->label('Product Title')
                                        ->live()
                                        ->required()
                                        ->helperText(fn ($state): string => strlen($state) . ' / 255 characters')

                                        ->maxLength(255)
                                        ->columnSpan(2),
                                    Grid::make()->schema([



                                        Forms\Components\TextInput::make('source_url')
                                            ->label('Import from URL')
                                            ->placeholder('https://www.etsy.com/listing/..., https://www.shop.tiktok.com/...'),
                                        Forms\Components\Actions::make([
                                            Forms\Components\Actions\Action::make('Import')
                                                ->label('Import')
                                                ->color('gray')
                                                ->icon('heroicon-o-check-circle')
                                                ->action('importFromUrl'),
                                        ])->verticallyAlignEnd()
                                    ])->columns(2),
                                ])
                                ->columns(2),

                            RichEditor::make('productDescription')
                                ->label('Description')
                                ->toolbarButtons([
                                    'bold',
                                    'italic',
                                    'underline',
                                    'bulletList',
                                    'orderedList',
                                    'h2',
                                    'h3',
                                    'link',
                                ])
                                ->required()
                                ->columnSpanFull()
                                ->extraInputAttributes(['style' => 'max-height: 300px; overflow: scroll']),
                        ])
                        ->columnSpan(['lg' => 2]),

                    // Right Column - Summary (1/3 width)
                    Forms\Components\Section::make('Product Information')
                        ->schema([
                            Select::make('warehouseId')
                                ->label('Warehouse')
                                ->options($this->warehouses)
                                ->required(),

                            Select::make('saveMode')
                                ->label('Status')
                                ->options([
                                    'AS_DRAFT' => 'Draft',
                                    'LISTING' => 'Published',
                                ])
                                ->required()
                                ->placeholder('Select status'),

                            DateTimePicker::make('scheduled_at')
                                ->label('Schedule Upload Time')
                                ->timezone('Asia/Ho_Chi_Minh')
                                ->default(now()->addMinutes(5))
                                ->minDate(now()),

                            Actions::make([

                                Action::make('autoSchedule')
                                    ->icon('heroicon-m-clock')
                                    ->button()
                                    ->label('Auto Schedule')
                                    ->color('success')
                                    ->size('sm')
                                    ->tooltip('Tự động đặt lịch upload trong giờ làm việc của Mỹ (9 AM - 10 PM EST)')

                                    ->extraAttributes([
                                        'class' => 'flex items-center gap-1'
                                    ])
                                    ->action(function (Get $get, Set $set) {
                                        // Convert current time to EST (US Eastern Time)
                                        $now = now()->setTimezone('America/New_York');
                                        $optimalTime = $now->copy();

                                        // If current time in US is between 22:00 and 09:00 (sleeping hours)
                                        if ($now->hour >= 22 || $now->hour < 9) {
                                            // Set time to 9:00 AM the next day
                                            $optimalTime = $now->hour >= 22
                                                ? $now->addDay()->setHour(9)->setMinute(rand(0, 30))->setSecond(rand(0, 59))
                                                : $now->setHour(9)->setMinute(rand(0, 30))->setSecond(rand(0, 59));
                                        } else {
                                            // During working hours, add a random delay between 5-30 minutes
                                            $optimalTime = $now->addMinutes(rand(5, 30))->setSecond(rand(0, 59));
                                        }

                                        // Convert back to Vietnam timezone for display
                                        $optimalTime = $optimalTime->setTimezone('Asia/Ho_Chi_Minh');

                                        // Update form value
                                        $set('scheduled_at', $optimalTime->format('Y-m-d H:i:s'));
                                    })
                            ])->columnSpanFull()->fullWidth(),

                            Placeholder::make('sizeChart')
                                ->label('Size Chart')
                                ->live()
                                ->content(function (Get $get) {
                                    $template = Template::find($get('templateId'));
                                    if (!$template || !$template->size_chart) {
                                        return 'No size chart available';
                                    }
                                    return new HtmlString(
                                        '<img src="' . Storage::disk('s3')->url($template->size_chart) . '" 
                                        alt="Size Chart" class="max-w-full h-auto rounded-lg">'
                                    );
                                })
                        ])
                        ->columnSpan(['lg' => 1]),

                    ImageGalleryField::make('productImages')
                        ->label('Product Images')
                        ->columnSpanFull(),

                    Forms\Components\Hidden::make('skus'),
                    Forms\Components\Hidden::make('productCategoryId'),
                ])
                ->columns(['lg' => 3]),

            // Action Buttons
            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Actions::make([
                        Forms\Components\Actions\Action::make('uploadNow')
                            ->label('Upload Now')
                            ->action(function (array $data) {
                                $this->createProduct(false);
                            })
                            ->color('primary')
                            ->size('lg')
                            ->icon('heroicon-o-cloud-arrow-up')
                            ->extraAttributes(['class' => 'px-6']),

                        Forms\Components\Actions\Action::make('scheduleUpload')
                            ->label('Schedule Upload')
                            ->action(function () {
                                $this->createProduct(true);
                            })
                            ->color('success')
                            ->size('lg')
                            ->icon('heroicon-o-clock')
                            ->extraAttributes(['class' => 'px-6']),
                    ])
                        ->alignment(\Filament\Support\Enums\Alignment::Center)
                        ->verticalAlignment(\Filament\Support\Enums\VerticalAlignment::Center),
                ])
                ->columnSpan('full'),
        ];
    }

    public function mount(): void
    {
        $this->form->fill([
            'selectedStore' => null,
            'templateId' => null,
            'productTitle' => '',
            'productDescription' => '',
            'productCategoryId' => '',
            'skus' => [],
            'saveMode' => null,
            'scheduled_at' => now()->addMinutes(5),
        ]);
    }

    public function loadWarehouses()
    {
        if (!$this->selectedStore) return;

        $store = Store::find($this->selectedStore);
        $tiktokService = new TiktokShopService($store);

        try {
            $warehouses = $tiktokService->getWarehouseList();
            $this->warehouses = collect($warehouses)
                ->filter(function ($warehouse) {
                    return $warehouse['type'] === 'SALES_WAREHOUSE';
                })
                ->mapWithKeys(function ($warehouse) {
                    return [$warehouse['id'] => $warehouse['name']];
                })
                ->toArray();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Unable to load warehouses: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function createProduct($isScheduled = false)
    {
        $this->form->validate([
            'templateId' => ['required'],
            'productTitle' => ['required', 'string', 'max:255'],
            'productDescription' => ['required'],
            'productImages' => ['required', 'array', 'max:9'],
            'warehouseId' => ['required'],
            'saveMode' => ['required'],
            'scheduled_at' => $isScheduled ? ['required', 'date', 'after:now'] : ['nullable'],
        ]);
        $data = $this->form->getState();
        $store = Store::find($this->selectedStore);

        try {
            // Validate images
            if (empty($data['productImages'])) {
                throw new \Exception('At least one product image is required');
            }
            if (count($data['productImages']) > 9) {
                throw new \Exception('Maximum 9 images allowed in total');
            }

            // Get size chart URL from template
            $template = Template::find($data['templateId']);
            $sizeChartUrl = null;
            if ($template && $template->size_chart) {
                $sizeChartUrl = Storage::disk('s3')->url($template->size_chart);
            }

            // Nếu là schedule thì tạo ProductToUpload
            if ($isScheduled) {
                $productToUpload = ProductToUpload::create([
                    'store_id' => $store->id,
                    'template_id' => $data['templateId'],
                    'product_title' => $data['productTitle'],
                    'description' => $data['productDescription'],
                    'warehouse_id' => $data['warehouseId'],
                    'save_mode' => $data['saveMode'],
                    'size_chart_url' => $sizeChartUrl,
                    'status' => ProductUploadStatus::Pending->value,
                    'scheduled_at' => $data['scheduled_at'],
                    'user_id' => $store->owner_id,
                    'images' => $data['productImages'],
                ]);

                Notification::make()
                    ->success()
                    ->body('Product will be uploaded at ' . $productToUpload->scheduled_at->format('Y-m-d H:i:s'))
                    ->send();

                $this->resetForm();
                return;
            }

            // Upload trực tiếp không qua ProductToUpload
            $uploadService = new ProductUploadService();
            $response = $uploadService->uploadDirectly($store, [
                'template_id' => $data['templateId'],
                'product_title' => $data['productTitle'],
                'description' => $data['productDescription'],
                'warehouse_id' => $data['warehouseId'],
                'save_mode' => $data['saveMode'],
                'images' => $data['productImages'],
            ]);

            if ($response['product_id']) {
                $this->resetForm();
                Notification::make()
                    ->success()
                    ->body("Product uploaded successfully! Product ID: {$response['product_id']}")
                    ->duration(5000)

                    ->send();
            } else {
                throw new \Exception(isset($response['message']) ? $response['message'] : 'Unknown error');
            }
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Unable to create product: ' . $e->getMessage())
                ->duration(5000)
                ->persistent()
                ->send();
        }
    }

    public function updateSkuSummary($skus)
    {
        $activeSkus = array_filter($skus, function ($sku) {
            return $sku['stock'] > 0;
        });

        $this->total_skus = count($activeSkus);

        if ($this->total_skus > 300) {
            Notification::make()
                ->title('Warning')
                ->body('This product has ' . $this->total_skus . ' SKUs. TikTok Shop only allows up to 300 SKUs per product.')
                ->warning()
                ->persistent()
                ->send();
        }

        // Tính price range
        if (!empty($skus)) {
            $prices = array_map(function ($sku) {
                return is_array($sku['price']) ? $sku['price']['amount'] : $sku['price'];
            }, $skus);
            $min = min($prices);
            $max = max($prices);
            $this->price_range = $min === $max ? "\${$min}" : "\${$min} - \${$max}";
        } else {
            $this->price_range = '-';
        }

        // Lấy các loại sản phẩm
        $types = [];
        foreach ($skus as $sku) {
            foreach ($sku['sales_attributes'] as $attr) {
                if (in_array($attr['value_name'], ['T-shirt', 'Hoodie', 'Sweatshirt', 'Unisex T-Shirt', 'Unisex Hoodie', 'Unisex Sweatshirt'])) {
                    $types[$attr['value_name']] = true;
                }
            }
        }
        $this->variants = !empty($types) ? implode(', ', array_keys($types)) : '-';
    }

    protected function resetForm()
    {
        $this->form->fill([
            'productTitle' => '',
            'productImages' => [],
        ]);
    }

    public function importFromUrl()
    {
        try {

            if (empty($this->source_url)) {
                Notification::make()
                    ->title('Please enter a valid URL')
                    ->danger()
                    ->send();
                return;
            }

            $this->productTitle = '';
            $this->productDescription = '';
            $this->productImages = [];


            $importService = new ProductImportService();
            $data = $importService->importFromUrl($this->source_url);

            // Set basic fields

            $this->productTitle = $data['title'] ?? '';
            $this->productDescription = $data['description'] ?? '';
            $this->productImages = $data['images'] ?? [];
            $this->source_url = $data['url'] ?? $this->source_url;

            Notification::make()
                ->success()
                ->title('Import Successful')
                ->body(sprintf('Imported product details with %d images', count($this->productImages)))
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Import Failed')
                ->body($e->getMessage())
                ->send();
        }
    }

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['super_admin']);
    }
}
