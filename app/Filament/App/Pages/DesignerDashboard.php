<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use Carbon\Carbon;
use App\Models\DesignJob;
use App\Enums\DesignJobStatus;
use App\Enums\DesignJobType;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class DesignerDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';
    protected static ?string $navigationLabel = 'Designer Dashboard';
    protected static ?string $navigationGroup = 'Design & Media';
    protected static ?int $navigationSort = 1;
    protected static ?string $title = 'Designer Dashboard';

    protected static string $view = 'filament.app.pages.designer-dashboard';

    // Cache configuration
    const CACHE_TTL = 300; // 5 minutes
    const CACHE_PREFIX = 'designer_dashboard_simple_';

    /**
     * Kiểm tra quyền truy cập - Designer, super_admin và User Manager được phép vào
     */
    public static function canAccess(): bool
    {
        if (Auth::check()) {
            return Auth::user()->hasAnyRole(['Designer', 'super_admin', 'User Manager']);
        }
        return false;
    }

    /**
     * Đảm bảo page được đăng ký trong navigation
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }

    /**
     * Lấy danh sách pending jobs cho Designer (loại trừ jobs mới trong 2 tiếng)
     */
    public function getPendingJobs()
    {
        $user = Auth::user();
        $cacheKey = $this->getCacheKey('pending_jobs_' . $user->id);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return DesignJob::where('status', DesignJobStatus::PENDING)
                ->where('created_at', '<=', Carbon::now()->subHours(2)) // Loại trừ jobs mới trong 2 tiếng
                ->with(['creator:id,name', 'designer:id,name'])
                ->orderByRaw('
                    CASE
                        WHEN is_rush = 1 THEN 1
                        WHEN created_at <= ? THEN 2
                        WHEN created_at <= ? THEN 3
                        ELSE 4
                    END
                ', [
                    Carbon::now()->subDays(2), // Quá 2 ngày
                    Carbon::now()->subDay()     // Quá 1 ngày
                ])
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($job) {
                    $timeLeftSeconds = max(0, 86400 - $job->created_at->diffInSeconds(now())); // 24h - elapsed time

                    return [
                        'id' => $job->id,
                        'title' => $this->parseJobTitle($job->title ?: 'Untitled Job'),
                        'description' => $this->parseJobTitle($job->description ?: ''),
                        'job_type' => $job->job_type->value,
                        'job_type_label' => $job->job_type->getLabel(),
                        'price' => $job->price,
                        'is_rush' => $job->is_rush,
                        'created_at' => $job->created_at,
                        'created_at_human' => $job->created_at->diffForHumans(),
                        'created_by_name' => $job->creator?->name ?? 'Unknown',
                        'designer_name' => $job->designer?->name ?? 'Unassigned',
                        'edit_url' => route('filament.app.resources.design-jobs.edit', $job),
                        'time_left_seconds' => $timeLeftSeconds,
                        'time_left_formatted' => $this->formatTimeLeft($timeLeftSeconds),
                        'time_left_display' => $this->getTimeLeftDisplay($timeLeftSeconds),
                        'overdue_info' => $this->getOverdueInfo($job->created_at),
                        'is_overdue' => $timeLeftSeconds <= 0,
                        'urgency_level' => $this->getUrgencyLevel($job, $timeLeftSeconds),
                    ];
                });
        });
    }

    /**
     * Lấy danh sách jobs mới trong 2 tiếng
     */
    public function getNewJobs()
    {
        $user = Auth::user();
        $cacheKey = $this->getCacheKey('new_jobs_' . $user->id);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return DesignJob::where('status', DesignJobStatus::PENDING)
                ->where('created_at', '>', Carbon::now()->subHours(2)) // Chỉ lấy jobs mới trong 2 tiếng
                ->with(['creator:id,name', 'designer:id,name'])
                ->orderByRaw('
                    CASE
                        WHEN is_rush = 1 THEN 1
                        ELSE 2
                    END
                ')
                ->orderBy('created_at', 'desc') // Jobs mới nhất trước
                ->get()
                ->map(function ($job) {
                    $timeLeftSeconds = max(0, 86400 - $job->created_at->diffInSeconds(now())); // 24h - elapsed time

                    return [
                        'id' => $job->id,
                        'title' => $this->parseJobTitle($job->title ?: 'Untitled Job'),
                        'description' => $this->parseJobTitle($job->description ?: ''),
                        'job_type' => $job->job_type->value,
                        'job_type_label' => $job->job_type->getLabel(),
                        'price' => $job->price,
                        'is_rush' => $job->is_rush,
                        'created_at' => $job->created_at,
                        'created_at_human' => $job->created_at->diffForHumans(),
                        'created_by_name' => $job->creator?->name ?? 'Unknown',
                        'designer_name' => $job->designer?->name ?? 'Unassigned',
                        'edit_url' => route('filament.app.resources.design-jobs.edit', $job),
                        'time_left_seconds' => $timeLeftSeconds,
                        'time_left_formatted' => $this->formatTimeLeft($timeLeftSeconds),
                        'time_left_display' => $this->getTimeLeftDisplay($timeLeftSeconds),
                        'overdue_info' => $this->getOverdueInfo($job->created_at),
                        'is_overdue' => $timeLeftSeconds <= 0,
                        'urgency_level' => 'new', // Đánh dấu là job mới
                        'is_new' => true,
                    ];
                });
        });
    }

    /**
     * Format time left to human readable
     */
    protected function formatTimeLeft(int $seconds): string
    {
        if ($seconds <= 0) {
            return 'OVERDUE';
        }

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        if ($hours > 0) {
            return sprintf('%dh %dm', $hours, $minutes);
        } else {
            return sprintf('%dm', $minutes);
        }
    }

    /**
     * Parse HTML content and convert URLs to images
     */
    protected function parseJobTitle(string $title): string
    {
        // Convert URLs to images
        $title = preg_replace_callback(
            '/<p>(https?:\/\/[^\s<]+\.(jpg|jpeg|png|gif|webp)[^<]*)<\/p>/i',
            function ($matches) {
                $url = $matches[1];
                return '<p><img src="' . htmlspecialchars($url) . '" alt="Job Image" loading="lazy"></p>';
            },
            $title
        );

        // Clean up any remaining URLs that aren't images
        $title = preg_replace(
            '/<p>(https?:\/\/[^\s<]+)<\/p>/i',
            '<p><a href="$1" target="_blank" rel="noopener">$1</a></p>',
            $title
        );

        return $title;
    }

    /**
     * Get detailed time left display
     */
    protected function getTimeLeftDisplay(int $seconds): array
    {
        if ($seconds <= 0) {
            return [
                'text' => 'OVERDUE',
                'color' => 'red',
                'urgent' => true
            ];
        }

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        if ($hours > 0) {
            $text = sprintf('%dh %dm', $hours, $minutes);
        } else if ($minutes > 0) {
            $text = sprintf('%dm %ds', $minutes, $remainingSeconds);
        } else {
            $text = sprintf('%ds', $remainingSeconds);
        }

        // Determine color and urgency
        if ($seconds <= 3600) { // < 1 hour
            $color = 'red';
            $urgent = true;
        } else if ($seconds <= 10800) { // < 3 hours
            $color = 'orange';
            $urgent = true;
        } else if ($seconds <= 21600) { // < 6 hours
            $color = 'yellow';
            $urgent = false;
        } else {
            $color = 'green';
            $urgent = false;
        }

        return [
            'text' => $text,
            'color' => $color,
            'urgent' => $urgent,
            'hours' => $hours,
            'minutes' => $minutes,
            'seconds' => $remainingSeconds
        ];
    }

    /**
     * Get overdue information
     */
    protected function getOverdueInfo($createdAt): array
    {
        $now = Carbon::now();
        $deadline = $createdAt->copy()->addDay(); // 24 hours from creation

        if ($now->lessThan($deadline)) {
            return [
                'is_overdue' => false,
                'message' => '',
                'severity' => 'normal'
            ];
        }

        $overdueSeconds = $now->diffInSeconds($deadline);
        $overdueDays = floor($overdueSeconds / 86400);
        $overdueHours = floor(($overdueSeconds % 86400) / 3600);

        if ($overdueDays > 0) {
            $message = "Overdue by {$overdueDays} day" . ($overdueDays > 1 ? 's' : '');
            if ($overdueHours > 0) {
                $message .= " {$overdueHours}h";
            }
            $severity = $overdueDays >= 3 ? 'critical' : 'high';
        } else {
            $message = "Overdue by {$overdueHours} hour" . ($overdueHours > 1 ? 's' : '');
            $severity = $overdueHours >= 12 ? 'high' : 'medium';
        }

        return [
            'is_overdue' => true,
            'message' => $message,
            'severity' => $severity,
            'days' => $overdueDays,
            'hours' => $overdueHours
        ];
    }

    /**
     * Get urgency level for styling
     */
    protected function getUrgencyLevel($job, int $timeLeftSeconds): string
    {
        if ($job->is_rush) {
            return 'rush';
        }

        if ($timeLeftSeconds <= 0) {
            return 'overdue';
        }

        if ($timeLeftSeconds <= 3600) { // < 1 hour
            return 'critical';
        }

        if ($timeLeftSeconds <= 10800) { // < 3 hours
            return 'urgent';
        }

        if ($timeLeftSeconds <= 21600) { // < 6 hours
            return 'warning';
        }

        return 'normal';
    }

    /**
     * Refresh dashboard data - Improved version
     */
    public function refreshDashboard()
    {
        try {
            // Clear cache để load data mới
            $this->clearCache();

            // Dispatch custom event để thông báo refresh thành công
            $this->dispatch('dashboard-refreshed', [
                'timestamp' => now()->toISOString(),
                'message' => 'Dashboard refreshed successfully'
            ]);

            // Log refresh action

            return true;
        } catch (\Exception $e) {
            // Log error

            // Dispatch error event
            $this->dispatch('dashboard-refresh-error', [
                'message' => 'Failed to refresh dashboard'
            ]);

            return false;
        }
    }

    /**
     * Auto refresh method for polling
     */
    public function autoRefresh()
    {
        return $this->refreshDashboard();
    }

    /**
     * Generate cache key
     */
    protected function getCacheKey(string $suffix = ''): string
    {
        return self::CACHE_PREFIX . $suffix;
    }

    /**
     * Clear cache
     */
    public function clearCache(): void
    {
        $user = Auth::user();
        Cache::forget($this->getCacheKey('pending_jobs_' . $user->id));
        Cache::forget($this->getCacheKey('new_jobs_' . $user->id));
    }

    /**
     * Mount method
     */
    public function mount(): void
    {
        // Clear cache để đảm bảo dữ liệu mới nhất
        $this->clearCache();
    }

}
