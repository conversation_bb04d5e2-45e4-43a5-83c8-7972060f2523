<?php
namespace App\Filament\App\Pages;

use App\Services\MerchSpyService;
use App\Services\Tokapi;
use Filament\Pages\Page;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;

class TiktokResearch extends Page
{
    use HasPageShield;

    protected static bool $shouldRegisterNavigation = false;

    public static function getNavigationGroup(): ?string
    {
        return null;
    }

    public static function getNavigationIcon(): ?string
    {
        return null;
    }
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin','Seller']) ;
    }

    protected static ?string $navigationIcon = 'heroicon-o-cursor-arrow-ripple';
    protected static ?string $navigationGroup = 'Spy Idea';
    protected static string $view = 'filament.app.pages.tiktok-research';
    public $page_view;

    public function mount()
    {
        $this->page_view = request('page_view', 'tiktok-winning-products');
    }
   
}
