<?php

namespace App\Filament\App\Pages;

use App\Models\MockupTemplateGroup;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Contracts\View\View;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Livewire\WithFileUploads;
use Intervention\Image\Facades\Image;
use ZipArchive;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;

class MockupTemplateGroupGenerator extends Page implements HasForms
{
    use InteractsWithForms;
    use HasPageShield;
    use WithFileUploads;

    protected static string $view = 'filament.app.pages.mockup-template-group-generator';
    protected static ?string $navigationGroup = 'Mockup Tools';
    protected static ?string $navigationLabel = 'Group Mockup Generator';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 1;
    protected static ?string $title = 'Group Mockup Generator';

    public $groupId;
    public $design = null;
    public $backDesign = null;
    public $designUrl = null;
    public $backDesignUrl = null;
    public $fileName = 'mockup';
    public $configs = [];
    public $isGenerating = false;
    public $progress = 0;
    public $downloadUrl;
    public $currentConfigIndex = 0;

    public function mount(): void
    {
        $this->form->fill([
            'fileName' => 'mockup'
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('groupId')
                    ->label('Mockup Group')
                    ->options(function () {
                        return MockupTemplateGroup::orderBy('created_at', 'desc')
                            ->get()
                            ->mapWithKeys(function ($group) {
                                return [$group->id => view('components.mockup-group-option', [
                                    'name' => $group->name,
                                    'configCount' => $group->configs()->count(),
                                    'createdAt' => $group->created_at,
                                ])->render()];
                            });
                    })
                    ->allowHtml()
                    ->searchable()
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->reset(['design', 'backDesign', 'downloadUrl', 'designUrl', 'backDesignUrl']);
                        if ($state) {
                            $group = MockupTemplateGroup::find($state);
                            if ($group) {
                                $this->configs = $group->configs()->get()->toArray();
                                $this->currentConfigIndex = 0;
                            }
                        }
                    })
                    ->columnSpanFull(),

                TextInput::make('designUrl')
                    ->label('Front Design URL')
                    ->url()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        if ($state) {
                            try {
                                $imageContent = file_get_contents($state);
                                if ($imageContent !== false) {
                                    $this->design = "data:image/png;base64," . base64_encode($imageContent);
                                }
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Error')
                                    ->body('Could not load image from URL')
                                    ->danger()
                                    ->send();
                            }
                        }
                    }),

                TextInput::make('backDesignUrl')
                    ->label('Back Design URL')
                    ->url()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        if ($state) {
                            try {
                                $imageContent = file_get_contents($state);
                                if ($imageContent !== false) {
                                    $this->backDesign = "data:image/png;base64," . base64_encode($imageContent);
                                }
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Error')
                                    ->body('Could not load image from URL')
                                    ->danger()
                                    ->send();
                            }
                        }
                    }),

             
            ])->columns(2);
    }

    public function generate()
    {
        $this->isGenerating = true;
        $this->progress = 0;

        try {
            // Your generation logic here
            // Update progress as needed:
            // $this->progress = $currentProgress;

            $this->isGenerating = false;
            $this->progress = 100;

            Notification::make()
                ->title('Success')
                ->body('Mockups generated successfully')
                ->success()
                ->send();
        } catch (\Exception $e) {
            $this->isGenerating = false;
            Notification::make()
                ->title('Error')
                ->body('Failed to generate mockups: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    private function generateMockup($apparelImage, $designImage, $posX, $posY, $width, $height, $outputPath)
    {
        $apparel = Image::make(Storage::disk('s3')->get($apparelImage));
        
        // Handle design image based on type
        if ($this->isBase64Image($designImage)) {
            $design = Image::make(base64_decode($designImage));
        } else {
            $design = Image::make(Storage::disk('public')->get($designImage));
        }

        $design->resize($width, $height, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });

        $apparel->insert($design, 'top-left', $posX, $posY);
        $apparel->save($outputPath);
    }

    private function addDirToZip($dir, $zip, $relativePath = '')
    {
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file == '.' || $file == '..') continue;
            
            $filePath = $dir . '/' . $file;
            $zipPath = $relativePath . ($relativePath ? '/' : '') . $file;

            if (is_dir($filePath)) {
                $zip->addEmptyDir($zipPath);
                $this->addDirToZip($filePath, $zip, $zipPath);
            } else {
                $zip->addFile($filePath, $zipPath);
            }
        }
    }

    private function removeDirectory($dir)
    {
        if (!file_exists($dir)) return;

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        return rmdir($dir);
    }

    private function isBase64Image($string)
    {
        try {
            $decoded = base64_decode($string, true);
            if ($decoded === false) return false;
            
            // Check if it's actually an image
            $image = imagecreatefromstring($decoded);
            return $image !== false;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function updatedGroupId($value)
    {
        if ($value) {
            $group = MockupTemplateGroup::find($value);
            if ($group) {
                $this->configs = $group->configs()->get()->toArray();
                $this->currentConfigIndex = 0;
            }
        }
    }

    public function getCurrentConfigProperty()
    {
        return $this->configs[$this->currentConfigIndex] ?? null;
    }

    public function getConfigsCountProperty()
    {
        return count($this->configs);
    }

    public function nextConfig()
    {
        if ($this->currentConfigIndex < $this->configsCount - 1) {
            $this->currentConfigIndex++;
        }
    }

    public function previousConfig()
    {
        if ($this->currentConfigIndex > 0) {
            $this->currentConfigIndex--;
        }
    }
}
