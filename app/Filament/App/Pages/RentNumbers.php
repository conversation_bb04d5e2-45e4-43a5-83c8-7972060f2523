<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class RentNumbers extends Page
{
    use HasPageShield;
    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user->hasRole(['super_admin','DarkMember']) ;
    }
    
    protected static ?string $navigationGroup = 'Tools';

        protected static bool $shouldRegisterNavigation = false;



    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.app.pages.rent-numbers';


}
