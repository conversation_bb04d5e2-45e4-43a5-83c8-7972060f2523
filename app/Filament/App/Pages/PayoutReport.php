<?php

namespace App\Filament\App\Pages;

use App\Models\Store;
use App\Models\TikTokPayment;
use App\Models\PayoutTransaction;
use App\Services\StoreRevenueService;
use Carbon\Carbon;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\DB;
use Filament\Support\Exceptions\Halt;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Filament\Notifications\Notification;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use App\Models\SupplierOrder;

class PayoutReport extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';
    protected static ?string $navigationLabel = 'Tiktok Payout Report';
    protected static ?string $navigationGroup = 'TikTok Shop';
    protected static ?int $navigationSort = 2;
    protected static string $view = 'filament.app.pages.payout-report';

    public $metrics = [];
    public $selectedMonth;
    public $availableMonths = [];
    public $totalPayout = 0;
    public $period = [];
    public $bankWarnings = [];
    public $activeTab = 'payout';
    public $selectedSeller = null;
    public $availableSellers = [];
    private ?StoreRevenueService $revenueService = null;
    public $totalCost = 0;
    public $totalPingPong = 0;

    public static function canAccess(): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }
        return $user->hasRole('Seller', 'super_admin', 'User Manager');
    }


    protected function getRevenueService(): StoreRevenueService
    {
        if ($this->revenueService === null) {
            $this->revenueService = new StoreRevenueService();
        }
        return $this->revenueService;
    }

    public function mount(): void
    {
        $this->selectedMonth = now()->format('Y-m');
        
        if (auth()->user()->hasRole('super_admin')) {
            $this->loadSellers();
        }
        
        $this->initializeMonths();
        $this->loadMetrics();
    }

    protected function loadSellers(): void
    {
        $this->availableSellers = \App\Models\User::role('Seller')
            ->select('id', 'name')
            ->orderBy('name')
            ->get()
            ->pluck('name', 'id')
            ->toArray();
    }

    protected function getFormSchema(): array 
    {
        $schema = [
            Select::make('selectedMonth')
                ->label('Select Month')
                ->options($this->availableMonths)
                ->reactive()
                ->afterStateUpdated(fn () => $this->loadMetrics()),
        ];

        if (auth()->user()->hasRole('super_admin')) {
            $schema[] = Select::make('selectedSeller')
                ->label('Select Seller')
                ->options($this->availableSellers)
                ->placeholder('All Sellers')
                ->searchable()
                ->reactive()
                ->afterStateUpdated(fn () => $this->loadMetrics());
        }

        return $schema;
    }

    protected function initializeMonths(): void
    {
        $laTime = now();

        for ($i = 0; $i < 4; $i++) {
            $date = $laTime->copy()->subMonths($i);
            $this->availableMonths[$date->format('Y-m')] = $date->format('F Y');
        }

        $this->selectedMonth = $laTime->format('Y-m');
    }

    protected function loadMetrics(): void
    {
        // Nếu không có tháng được chọn, sử dụng tháng hiện tại
        if (!$this->selectedMonth) {
            $this->selectedMonth = now()->format('Y-m');
        }

        [$year, $month] = explode('-', $this->selectedMonth);
        $startDate = Carbon::create($year, $month, 1, 0, 0, 0, 'America/Los_Angeles');
        $endDate = $startDate->copy()->endOfMonth();

        $this->period = [
            'start' => $startDate->format('Y-m-d'),
            'end' => $endDate->format('Y-m-d'),
        ];

        $this->checkBankAccounts();

        // Query TikTok payments theo create_time và tính tổng amount
        $tiktokPayments = TikTokPayment::select(
            'store_id',
            DB::raw('COUNT(*) as payment_count'),
            DB::raw('SUM(amount) as total_amount')
        )
            ->with([
                'store:id,name,bank_account,card,owner_id',
                'store.owner:id,name'
            ])
            ->whereBetween('create_time', [
                $startDate,
                $endDate
            ])
            ->when($this->selectedSeller && auth()->user()->hasRole('super_admin'), function ($query) {
                $query->whereHas('store', function($q) {
                    $q->where('owner_id', $this->selectedSeller);
                });
            })
            ->when(!auth()->user()->hasRole('super_admin'), function ($query) {
                $query->whereHas('store', function($q) {
                    $q->where('owner_id', auth()->id());
                });
            })
            ->where('status', 'PAID')
            ->groupBy('store_id')
            ->get();

        // Query PingPong payments
        $pingpongPayments = PayoutTransaction::select(
            'card_no',
            DB::raw('COUNT(*) as payment_count'),
            DB::raw('SUM(amount) as total_amount')
        )
            ->with(['store:id,name,bank_account,card,owner_id', 'store.owner:id,name'])
            ->whereBetween('time', [
                $startDate,
                $endDate
            ])
            ->when($this->selectedSeller && auth()->user()->hasRole('super_admin'), function ($query) {
                $query->whereHas('store', function($q) {
                    $q->where('owner_id', $this->selectedSeller);
                });
            })
            ->when(!auth()->user()->hasRole('super_admin'), function ($query) {
                $query->whereHas('store', function($q) {
                    $q->where('owner_id', auth()->id());
                });
            })
            ->groupBy('card_no')
            ->get();

        $this->totalPayout = 0;
        $this->totalCost = 0;
        $this->totalPingPong = 0;
        $metrics = [];

        // Process TikTok payments
        foreach ($tiktokPayments as $payment) {
            if (!$payment->store) continue;

            $storeId = $payment->store->id;
            $this->totalPayout += $payment->total_amount;

            // Tính tổng vốn từ SupplierOrder cho store này
            $totalCost = SupplierOrder::where('store_id', $storeId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('base_cost');

            $this->totalCost += $totalCost;

            // Lấy tổng số tiền TikTok đã trả
            $totalTiktokPaid = TikTokPayment::where('store_id', $storeId)
                ->where('status', 'PAID')
                ->sum('amount');

            // Lấy tổng số tiền đã nhận qua PingPong
            $totalPingpongReceived = PayoutTransaction::where('card_no', $payment->store->bank_account)
                ->where('status', 'Success')
                ->sum('amount');

            $metrics[$storeId] = [
                'store' => [
                    'id' => $storeId,
                    'name' => $payment->store->name,
                    'owner' => $payment->store->owner?->name ?? 'Unknown Owner',
                    'bank_account' => $payment->store->bank_account ?? null,
                    'card' => $payment->store->card ?? null,
                ],
                'stats' => [
                    'tiktok' => [
                        'total' => $payment->total_amount,
                        'count' => $payment->payment_count,
                    ],
                    'total_tiktok_paid' => $totalTiktokPaid,
                    'total_pingpong_received' => $totalPingpongReceived,
                    'total_cost' => $totalCost,
                    'profit' => $payment->total_amount - $totalCost,
                    'profit_margin' => $totalCost > 0 ? round(($payment->total_amount - $totalCost) / $payment->total_amount * 100, 2) : 0,
                ],
                'payments' => []
            ];
        }

        // Process PingPong payments
        foreach ($pingpongPayments as $payment) {
            if (!$payment->store) continue;
            $this->totalPingPong += $payment->total_amount;

            $storeId = $payment->store->id;
            if (!isset($metrics[$storeId])) {
                // Nếu store chưa có trong metrics, tạo mới
                $store = $payment->store;

                // Lấy tổng số tiền TikTok đã trả
                $totalTiktokPaid = TikTokPayment::where('store_id', $storeId)
                    ->where('status', 'PAID')
                    ->sum('amount');

                // Lấy tổng số tiền đã nhận qua PingPong
                $totalPingpongReceived = PayoutTransaction::where('card_no', $store->bank_account)
                    ->where('status', 'Success')
                    ->sum('amount');

                // Tính tổng vốn từ SupplierOrder
                $totalCost = SupplierOrder::where('store_id', $storeId)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->sum('base_cost');

                $metrics[$storeId] = [
                    'store' => [
                        'id' => $storeId,
                        'name' => $store->name,
                        'owner' => $store->owner?->name ?? 'Unknown Owner',
                        'bank_account' => $store->bank_account ?? null,
                        'card' => $store->card ?? null,
                    ],
                    'stats' => [
                        'tiktok' => [
                            'total' => 0,
                            'count' => 0,
                        ],
                        'pingpong' => [
                            'total' => $payment->total_amount,
                            'count' => $payment->payment_count,
                        ],
                        'total_tiktok_paid' => $totalTiktokPaid,
                        'total_pingpong_received' => $totalPingpongReceived,
                        'total_cost' => $totalCost,
                        'profit' => 0,
                        'profit_margin' => 0,
                    ],
                    'payments' => []
                ];
            } else {
                // Nếu store đã có trong metrics, cập nhật thông tin PingPong
                $metrics[$storeId]['stats']['pingpong'] = [
                    'total' => $payment->total_amount,
                    'count' => $payment->payment_count,
                ];
            }
        }

        $this->metrics = collect($metrics)
            ->sortByDesc(function ($metric) {
                return $metric['stats']['tiktok']['total'] ?? 0;
            })
            ->values()
            ->all();
    }

    public function loadPayments($storeId): void
    {
        [$year, $month] = explode('-', $this->selectedMonth);
        $store = Store::find($storeId);

        // Load TikTok payments
        $tiktokPayments = TikTokPayment::where('store_id', $storeId)
            ->whereYear('create_time', $year)
            ->whereMonth('create_time', $month)
            ->where('status', 'PAID')
            ->get()
            ->map(fn($payment) => [
                'id' => $payment->payment_id,
                'amount' => $payment->amount,
                'create_time' => $payment->create_time,
                'status' => $payment->status,
                'type' => 'TikTok'
            ]);

        // Load PingPong payments
        $pingpongPayments = PayoutTransaction::where('card_no', $store->bank_account)
            ->whereYear('time', $year)
            ->whereMonth('time', $month)
            ->get()
            ->map(fn($payment) => [
                'id' => $payment->id,
                'amount' => $payment->amount,
                'create_time' => $payment->time,
                'status' => $payment->status,
                'type' => 'PingPong'
            ]);

        // Merge and sort payments
        $allPayments = $tiktokPayments->concat($pingpongPayments)
            ->sortByDesc('create_time')
            ->values()
            ->all();

        // Update payments for store
        foreach ($this->metrics as &$metric) {
            if ($metric['store']['id'] === $storeId) {
                $metric['payments'] = $allPayments;
                break;
            }
        }
    }

    protected function checkBankAccounts(): void
    {
        $this->bankWarnings = [];

        $stores = Store::select('id', 'name', 'bank_account', 'card', 'owner_id')
            ->with(['owner' => function ($query) {
                $query->select('id', 'name');
            }])
            ->when($this->selectedSeller && auth()->user()->hasRole('super_admin'), function ($query) {
                $query->where('owner_id', $this->selectedSeller);
            })
            ->when(!auth()->user()->hasRole('super_admin'), function ($query) {
                $query->where('owner_id', auth()->id());
            })
            ->whereHas('tiktokPayout')
            ->get();

        foreach ($stores as $store) {
            $warnings = [];

            // Trường hợp 1: Chưa có bank_account
            if (empty($store->bank_account)) {
                $warnings[] = "Bank account not set in tool";
            }

            // Trường hợp 2: Chưa có card (chưa add bank trên TikTok Shop)
            if (empty($store->card)) {
                $warnings[] = "Bank not added on TikTok Shop";
            }

            // Trường hợp 3: Số cuối không khớp
            elseif (!empty($store->bank_account)) {
                $bankLast4 = substr($store->bank_account, -4);
                $cardLast4 = substr($store->card, -4);

                if ($bankLast4 !== $cardLast4) {
                    $warnings[] = "Bank account mismatch: Tool ({$bankLast4}) ≠ TikTok ({$cardLast4})";
                }
            }

            if (!empty($warnings)) {
                $this->bankWarnings[] = [
                    'store' => [
                        'id' => $store->id,
                        'name' => $store->name,
                        'owner' => $store->owner?->name ?? 'Unknown Owner',
                    ],
                    'issues' => $warnings
                ];
            }
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('export')
                ->label('Export Report')
                ->icon('heroicon-o-arrow-down-tray')
                ->button()
                ->action(fn() => $this->exportReport())
        ];
    }

    protected function getViewData(): array
    {
        return [
            'pageTitle' => 'Payout Report',
            'period' => $this->period,
            'timezone' => 'GMT-8 (Los Angeles)',
            'bankWarnings' => $this->bankWarnings,
            'totalWarnings' => count($this->bankWarnings),
            'totalPayout' => $this->totalPayout,
            'totalCost' => $this->totalCost,
            'totalPingPong' => $this->totalPingPong,
            'totalProfit' => $this->totalPayout - $this->totalCost,
        ];
    }

    public function exportReport(): void
    {
        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Cập nhật headers
            $sheet->setCellValue('A1', 'Store Name');
            $sheet->setCellValue('B1', 'Owner');
            $sheet->setCellValue('C1', 'TikTok Amount');
            $sheet->setCellValue('D1', 'TikTok Count');
            $sheet->setCellValue('E1', 'PingPong Amount');
            $sheet->setCellValue('F1', 'PingPong Count');
            $sheet->setCellValue('G1', 'Total Revenue');
            $sheet->setCellValue('H1', 'Total Cost');
            $sheet->setCellValue('I1', 'Total Profit');
            $sheet->setCellValue('J1', 'Profit Margin');
            $sheet->setCellValue('K1', 'Total Orders');
            $sheet->setCellValue('L1', 'Paid Orders');
            $sheet->setCellValue('M1', 'Pending Orders');
            $sheet->setCellValue('N1', 'Period');

            // Add data
            $row = 2;
            foreach ($this->metrics as $metric) {
                $sheet->setCellValue('A' . $row, $metric['store']['name']);
                $sheet->setCellValue('B' . $row, $metric['store']['owner']);
                $sheet->setCellValue('C' . $row, $metric['stats']['tiktok']['total']);
                $sheet->setCellValue('D' . $row, $metric['stats']['tiktok']['count']);
                $sheet->setCellValue('E' . $row, $metric['stats']['pingpong']['total'] ?? 0);
                $sheet->setCellValue('F' . $row, $metric['stats']['pingpong']['count'] ?? 0);
                $sheet->setCellValue('G' . $row, $metric['stats']['revenue']['total_revenue']);
                $sheet->setCellValue('H' . $row, $metric['stats']['revenue']['total_cost']);
                $sheet->setCellValue('I' . $row, $metric['stats']['revenue']['total_profit']);
                $sheet->setCellValue('J' . $row, $metric['stats']['revenue']['profit_margin'] . '%');
                $sheet->setCellValue('K' . $row, $metric['stats']['revenue']['total_orders']);
                $sheet->setCellValue('L' . $row, $metric['stats']['revenue']['paid_orders']);
                $sheet->setCellValue('M' . $row, $metric['stats']['revenue']['pending_orders']);
                $sheet->setCellValue('N' . $row, "{$this->period['start']} - {$this->period['end']}");
                $row++;
            }

            // Format currency columns
            $currencyFormat = '_("$"* #,##0.00_);_("$"* \(#,##0.00\);_("$"* "-"??_);_(@_)';
            $sheet->getStyle('C:C')->getNumberFormat()->setFormatCode($currencyFormat);
            $sheet->getStyle('E:E')->getNumberFormat()->setFormatCode($currencyFormat);
            $sheet->getStyle('G:I')->getNumberFormat()->setFormatCode($currencyFormat);

            // Format percentage column
            $sheet->getStyle('J')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_PERCENTAGE_00);

            // Auto size columns
            foreach (range('A', 'N') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }

            // Create writer
            $writer = new Xlsx($spreadsheet);

            // Save to memory
            ob_start();
            $writer->save('php://output');
            $content = ob_get_clean();

            // Generate filename and path
            $filename = "payout-report-{$this->selectedMonth}.xlsx";
            $path = "exports/" . date('Y/m/d/') . $filename;

            // Store file in S3
            Storage::disk('s3')->put($path, $content);

            // Get temporary URL for download
            $url = Storage::disk('s3')->temporaryUrl(
                $path,
                now()->addMinutes(5)
            );

            // Delete file after 5 minutes
            dispatch(function () use ($path) {
                Storage::disk('s3')->delete($path);
            })->delay(now()->addMinutes(5));

            Notification::make()
                ->success()
                ->title('Report exported successfully')
                ->send();

            $this->js("window.open('{$url}', '_blank')");
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Failed to export report')
                ->body($e->getMessage())
                ->send();

            throw new Halt();
        }
    }

    public function togglePayments($storeId): void
    {
        // Nếu đã có payments thì xóa đi (đóng)
        foreach ($this->metrics as &$metric) {
            if ($metric['store']['id'] === $storeId) {
                if (!empty($metric['payments'])) {
                    $metric['payments'] = [];
                } else {
                    // Nếu chưa có payments thì load mới (mở)
                    $this->loadPayments($storeId);
                }
                break;
            }
        }
    }
}
