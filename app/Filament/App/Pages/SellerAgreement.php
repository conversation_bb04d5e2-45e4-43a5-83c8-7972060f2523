<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Livewire\Attributes\Url;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class SellerAgreement extends Page implements HasForms
{
    use InteractsWithForms;
    use HasPageShield;

    //------------------------------------------------
    // Page Configuration
    //------------------------------------------------
    
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Thỏa thuận làm việc';
    protected static ?string $title = 'Thỏa thuận làm việc với Seller';
    protected static ?string $navigationGroup = 'Seller Management';
    protected static ?int $navigationSort = 2;
    
    protected static string $view = 'filament.app.pages.seller-agreement';
    
    // Định nghĩa slug URL - có thể truy cập qua URL /seller-agreement hoặc /seller-agreement/{userId}
    protected static ?string $slug = 'seller-agreement/{userId?}';

    public static function canAccess(): bool 
    {
        // Kiểm tra quyền truy cập: admin hoặc User Manager hoặc chính người bán
        return Auth::user()->hasAnyRole(['super_admin', 'User Manager']);
    }

    // Static method to match parent class signature
    public static function getUrl(array $parameters = [], bool $isAbsolute = true, ?string $panel = null, ?\Illuminate\Database\Eloquent\Model $tenant = null): string
    {
        return parent::getUrl($parameters, $isAbsolute, $panel, $tenant);
    }
   
    //------------------------------------------------
    // Properties
    //------------------------------------------------
    
    public $userId;
    public $seller;
    public $agreementData = [];
    
    #[Url(except: 'probation')]
    public $agreementType = 'probation';
    
    #[Url(except: '')]
    public $startDate;
    
    #[Url(except: '')]
    public $endDate;
    
    #[Url(except: '')]
    public $agreementNumber;
    
    #[Url(except: 0)]
    public $targetOrders = 5;
    
    #[Url(except: 0)]
    public $dailySalary = 120000;
    
    #[Url(except: 0)]
    public $monthlySalary = 5000000;
    
    #[Url(except: 0)]
    public $commissionPercentage = 5;
    
    public $formattedDailySalary;
    public $formattedMonthlySalary;
    
    #[Url(except: '')]
    public $supportTelegramLink = 'https://t.me/winwin017'; // Default HR support
    
    #[Url(except: '')]
    public $teamSupportTelegramLink = 'https://t.me/proa157'; // Default team support
    
    #[Url(except: '')]
    public $trainerName = 'Minh Tin'; // Default trainer name
    
    #[Url(except: '')]
    public $trainerTelegramLink = 'https://t.me/tin93qn'; // Default trainer telegram
    
    #[Url(except: '')]
    public $hrName = 'Minh Win'; // Default HR name
    
    #[Url(except: '')]
    public $supportName = 'Minh Tâm'; // Default support person name
    
    #[Url(except: '')]
    public $companyRepresentativeName = 'Minh Đức'; // Default company representative
    
    #[Url(except: '')]
    public $customSellerName = '';
    
    #[Url(except: '')]
    public $customSellerEmail = '';
    
    #[Url(except: '')]
    public $customSellerPhone = '';
    
    #[Url(except: '')]
    public $customSellerAddress = '';
    
    #[Url(except: '')]
    public $customSellerIdNumber = '';
    
    public $data = [];
    
    //------------------------------------------------
    // Page Lifecycle
    //------------------------------------------------
    
    public function mount($userId = null)
    {
        // If userId is not provided, use the current authenticated user
        $this->userId = $userId ?? Auth::id();
        
        // Get seller information
        $this->loadSellerInfo();
        
        // Initialize dates
        $this->initializeDates();
        
        // Generate agreement number
        $this->generateAgreementNumber();
        
        // Format daily and monthly salary with thousand separator
        $this->formattedDailySalary = number_format($this->dailySalary, 0, ',', '.');
        $this->formattedMonthlySalary = number_format($this->monthlySalary, 0, ',', '.');
        
        // Initialize custom seller fields with defaults from seller if available
        $this->customSellerName = $this->seller->name ?? '';
        $this->customSellerEmail = $this->seller->email ?? '';
        $this->customSellerPhone = $this->seller->phone_number ?? '';
        $this->customSellerAddress = $this->seller->address ?? '';
        $this->customSellerIdNumber = $this->seller->id_number ?? '';
        
        // Set form data
        $this->data = [
            'agreementType' => $this->agreementType,
            'sellerName' => $this->customSellerName,
            'sellerEmail' => $this->customSellerEmail,
            'sellerPhone' => $this->customSellerPhone,
            'sellerAddress' => $this->customSellerAddress,
            'sellerIdNumber' => $this->customSellerIdNumber,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'targetOrders' => $this->targetOrders,
            'dailySalary' => $this->dailySalary,
            'monthlySalary' => $this->monthlySalary,
            'commissionPercentage' => $this->commissionPercentage,
            'companyRepresentative' => $this->companyRepresentativeName,
            'hrName' => $this->hrName,
            'supportTelegramLink' => $this->supportTelegramLink,
            'supportName' => $this->supportName,
            'teamSupportTelegramLink' => $this->teamSupportTelegramLink,
            'trainerName' => $this->trainerName,
            'trainerTelegramLink' => $this->trainerTelegramLink,
        ];
        
        // Prepare agreement data for display
        $this->prepareAgreementData();
    }
    
    /**
     * Load seller information
     */
    protected function loadSellerInfo()
    {
        $this->seller = User::find($this->userId);
        
        // If user doesn't exist, redirect to dashboard
        if (!$this->seller) {
            redirect()->route('filament.app.pages.dashboard');
        }
    }
    
    /**
     * Initialize start and end dates
     */
    protected function initializeDates()
    {
        // Leave dates empty by default
        // The form will allow selecting dates or leaving them blank
    }
    
    /**
     * Generate unique agreement number
     */
    protected function generateAgreementNumber()
    {
        if (empty($this->agreementNumber)) {
            $this->agreementNumber = 'AGR-' . strtoupper(substr($this->seller->name, 0, 3)) . '-' . Carbon::now()->format('YmdHis');
        }
    }
    
    /**
     * Prepare agreement data for display
     */
    protected function prepareAgreementData()
    {
        // Handle case where dates might be empty
        $formattedStartDate = '..........................';
        $formattedEndDate = '..........................';
        $totalDays = 0;
        $workingDays = 0;
        
        if (!empty($this->startDate) && !empty($this->endDate)) {
            $startDate = Carbon::parse($this->startDate);
            $endDate = Carbon::parse($this->endDate);
            
            $formattedStartDate = $startDate->format('d/m/Y');
            $formattedEndDate = $endDate->format('d/m/Y');
            
            // Calculate total days in the period
            $totalDays = $startDate->diffInDays($endDate) + 1;
            
            // Calculate actual working days (excluding only Sundays)
            $workingDays = 0;
            for ($i = 0; $i < $totalDays; $i++) {
                $currentDate = $startDate->copy()->addDays($i);
                // Skip only Sunday (0)
                if ($currentDate->dayOfWeek !== 0) {
                    $workingDays++;
                }
            }
        }
        
        $totalProbationSalary = $workingDays > 0 ? number_format($this->dailySalary * $workingDays, 0, ',', '.') . ' ₫' : '..........................';

        $this->agreementData = [
            'agreementNumber' => $this->agreementNumber,
            'agreementType' => $this->agreementType,
            'seller' => [
                'name' => $this->customSellerName,
                'email' => $this->customSellerEmail,
                'phone' => $this->customSellerPhone,
                'address' => $this->customSellerAddress,
                'idNumber' => $this->customSellerIdNumber,
            ],
            'agreement' => [
                'startDate' => $formattedStartDate,
                'endDate' => $formattedEndDate,
                'totalDays' => $totalDays,
                'workingDays' => $workingDays,
                'targetOrders' => $this->targetOrders,
                'dailySalary' => number_format($this->dailySalary, 0, ',', '.') . ' ₫',
                'monthlySalary' => number_format($this->monthlySalary, 0, ',', '.') . ' ₫',
                'commissionPercentage' => $this->commissionPercentage,
                'totalProbationSalary' => $totalProbationSalary,
                'createdAt' => now()->format('d/m/Y'),
            ],
            'company' => [
                'name' => config('app.company_name', 'Bug Media'),
                'representative' => $this->data['companyRepresentative'] ?? $this->companyRepresentativeName,
                'address' => config('app.company_address', 'Lô 17E Nguyễn Sinh Sắc, Quận Liên Chiểu, Đà Nẵng'),
                'taxId' => config('app.company_tax_id', '0109687255'),
                'directorTelegram' => 'https://t.me/Marcus_Aurelius_91',
            ],
            'support' => [
                'hrName' => $this->hrName,
                'hrTelegramLink' => $this->supportTelegramLink,
                'supportName' => $this->supportName,
                'teamTelegramLink' => $this->teamSupportTelegramLink,
                'trainerName' => $this->trainerName,
                'trainerTelegramLink' => $this->trainerTelegramLink,
            ],
        ];
    }
    
    /**
     * Update properties when changed from form
     */
    public function updatedData($value, $key)
    {
        if ($key === 'endDate') {
            $this->endDate = $value;
            $this->prepareAgreementData();
        } elseif ($key === 'agreementType') {
            $this->agreementType = $value;
            $this->prepareAgreementData();
        }
    }
    
    //------------------------------------------------
    // Form & Actions
    //------------------------------------------------
    
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Loại hợp đồng')
                    ->schema([
                        \Filament\Forms\Components\Radio::make('agreementType')
                            ->label('Chọn loại hợp đồng')
                            ->options([
                                'probation' => 'Thỏa thuận thử việc',
                                'official' => 'Hợp đồng chính thức',
                            ])
                            ->default($this->agreementType)
                            ->inline()
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->agreementType = $state;
                                $this->prepareAgreementData();
                            }),
                    ]),
                    
                Section::make('Thông tin người bán (Seller)')
                    ->description('Nhập thông tin người bán tham gia thỏa thuận')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('sellerName')
                                    ->label('Tên nhân viên')
                                    ->default($this->customSellerName)
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->customSellerName = $state;
                                        $this->prepareAgreementData();
                                    }),
                                    
                                TextInput::make('sellerIdNumber')
                                    ->label('Số CCCD/CMND')
                                    ->default($this->customSellerIdNumber)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->customSellerIdNumber = $state;
                                        $this->prepareAgreementData();
                                    }),
                            ]),
                            
                        Grid::make(2)
                            ->schema([
                                TextInput::make('sellerEmail')
                                    ->label('Email')
                                    ->default($this->customSellerEmail)
                                    ->email()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->customSellerEmail = $state;
                                        $this->prepareAgreementData();
                                    }),
                                    
                                TextInput::make('sellerPhone')
                                    ->label('Số điện thoại')
                                    ->default($this->customSellerPhone)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->customSellerPhone = $state;
                                        $this->prepareAgreementData();
                                    }),
                            ]),
                            
                        TextInput::make('sellerAddress')
                            ->label('Địa chỉ')
                            ->default($this->customSellerAddress)
                            ->reactive()
                            ->afterStateUpdated(function ($state) {
                                $this->customSellerAddress = $state;
                                $this->prepareAgreementData();
                            }),
                    ]),

                Section::make('Thông tin người hỗ trợ')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('trainerName')
                                    ->label('Người đào tạo')
                                    ->default($this->trainerName)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->trainerName = $state;
                                        $this->prepareAgreementData();
                                    }),
                                    
                                TextInput::make('trainerTelegramLink')
                                    ->label('Link Telegram người đào tạo')
                                    ->placeholder('tin93qn')
                                    ->prefix('https://t.me/')
                                    ->default(str_replace('https://t.me/', '', $this->trainerTelegramLink))
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        // Remove the prefix if user included it
                                        $username = str_replace('https://t.me/', '', $state);
                                        $this->trainerTelegramLink = 'https://t.me/' . $username;
                                        $this->prepareAgreementData();
                                    }),
                            ]),
                            
                        Grid::make(2)
                            ->schema([
                                TextInput::make('hrName')
                                    ->label('Tên HR hỗ trợ')
                                    ->default($this->hrName)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->hrName = $state;
                                        $this->prepareAgreementData();
                                    }),
                                
                                TextInput::make('supportTelegramLink')
                                    ->label('Link Telegram HR hỗ trợ')
                                    ->placeholder('winwin017')
                                    ->prefix('https://t.me/')
                                    ->default(str_replace('https://t.me/', '', $this->supportTelegramLink))
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        // Remove the prefix if user included it
                                        $username = str_replace('https://t.me/', '', $state);
                                        $this->supportTelegramLink = 'https://t.me/' . $username;
                                        $this->prepareAgreementData();
                                    }),
                            ]),
                            
                        Grid::make(2)
                            ->schema([
                                TextInput::make('supportName')
                                    ->label('Tên người hỗ trợ setup')
                                    ->default($this->supportName)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->supportName = $state;
                                        $this->prepareAgreementData();
                                    }),
                                    
                                TextInput::make('teamSupportTelegramLink')
                                    ->label('Link Telegram hỗ trợ setup')
                                    ->placeholder('proa157')
                                    ->prefix('https://t.me/')
                                    ->default(str_replace('https://t.me/', '', $this->teamSupportTelegramLink))
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        // Remove the prefix if user included it
                                        $username = str_replace('https://t.me/', '', $state);
                                        $this->teamSupportTelegramLink = 'https://t.me/' . $username;
                                        $this->prepareAgreementData();
                                    }),
                            ]),
                    ]),
                    
                Section::make('Thông tin hợp đồng')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                DatePicker::make('startDate')
                                    ->label('Ngày bắt đầu')
                                    ->default($this->startDate)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->startDate = $state;
                                        
                                        if ($state) {
                                            $startDate = Carbon::parse($state);
                                            
                                            // Set end date based on agreement type
                                            if ($this->agreementType === 'probation') {
                                                // For probation: 14 days (start + 13)
                                                $this->endDate = $startDate->copy()->addDays(13)->format('Y-m-d');
                                            } else {
                                                // For official: 12 months
                                                $this->endDate = $startDate->copy()->addMonths(12)->subDay()->format('Y-m-d');
                                            }
                                            
                                            $this->data['endDate'] = $this->endDate;
                                        }
                                        
                                        $this->prepareAgreementData();
                                    }),
                                    
                                DatePicker::make('endDate')
                                    ->label('Ngày kết thúc')
                                    ->default($this->endDate)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->endDate = $state;
                                        $this->prepareAgreementData();
                                    })
                                    ->helperText('Có thể điều chỉnh ngày kết thúc'),
                                    
                                TextInput::make('companyRepresentative')
                                    ->label('Người đại diện công ty')
                                    ->default($this->companyRepresentativeName)
                                    ->required(),
                            ]),
                            
                        Grid::make(2)
                            ->schema([
                                TextInput::make('targetOrders')
                                    ->label('Số đơn mục tiêu')
                                    ->numeric()
                                    ->default($this->targetOrders)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->targetOrders = (int) $state;
                                        $this->prepareAgreementData();
                                    })
                                    ->visible(fn (callable $get) => $get('agreementType') === 'probation'),
                                    
                                TextInput::make('dailySalary')
                                    ->label('Lương thử việc hàng ngày (VNĐ)')
                                    ->numeric()
                                    ->default($this->dailySalary)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->dailySalary = (int) $state;
                                        $this->formattedDailySalary = number_format($this->dailySalary, 0, ',', '.');
                                        $this->prepareAgreementData();
                                    })
                                    ->visible(fn (callable $get) => $get('agreementType') === 'probation'),
                                    
                                TextInput::make('monthlySalary')
                                    ->label('Lương cơ bản hàng tháng (VNĐ)')
                                    ->numeric()
                                    ->default($this->monthlySalary)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->monthlySalary = (int) $state;
                                        $this->formattedMonthlySalary = number_format($this->monthlySalary, 0, ',', '.');
                                        $this->prepareAgreementData();
                                    })
                                    ->visible(fn (callable $get) => $get('agreementType') === 'official'),
                                    
                                TextInput::make('commissionPercentage')
                                    ->label('Phần trăm hoa hồng (%)')
                                    ->numeric()
                                    ->default($this->commissionPercentage)
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->commissionPercentage = (int) $state;
                                        $this->prepareAgreementData();
                                    })
                                    ->visible(fn (callable $get) => $get('agreementType') === 'official'),
                            ]),
                    ]),
                    
                Actions::make([
                    Action::make('generateAgreement')
                        ->label('Tạo hợp đồng')
                        ->color('primary')
                        ->icon('heroicon-o-document-text')
                        ->button()
                        ->tooltip('Tạo thỏa thuận làm việc mới')
                        ->action(function () {
                            $this->prepareAgreementData();
                            $this->js('window.print()');
                        }),
                    
                    Action::make('printAgreement')
                        ->label('In hợp đồng')
                        ->color('success')
                        ->icon('heroicon-o-printer')
                        ->button()
                        ->tooltip('In thỏa thuận làm việc')
                        ->action(function () {
                            $this->js('window.print()');
                        }),
                ]),
            ])
            ->statePath('data');
    }
} 