<?php

namespace App\Filament\App\Pages;

use App\Services\ProxyPurchaseService;
use App\Models\ProxyPurchase;
use App\Models\User;
use Filament\Pages\Page;
use Filament\Forms\Form;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Hidden;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use App\Models\ProxyAccount;

class ProxyPurchasePage extends Page implements HasForms, HasActions, HasTable
{
    use InteractsWithForms, InteractsWithActions, InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';
    protected static ?string $navigationLabel = 'Mua Proxy';
    protected static ?string $title = 'Mua Proxy';
    protected static string $view = 'filament.app.pages.proxy-purchase-page';
    protected static ?string $navigationGroup = 'Tools';
    protected static ?int $navigationSort = 1;

    public ?array $data = [];
    public array $quota = [];

    public static function canAccess(): bool
    {
        return Auth::check() && Auth::user()->hasRole(['super_admin', 'User Manager']);
    }

    public function mount(): void
    {
        $this->loadUserQuota();
        $this->form->fill();
    }

    protected function loadUserQuota(): void
    {
        $this->quota = Auth::user()->getRemainingProxyQuota();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Quota hôm nay')
                    ->description('Giới hạn mua proxy trong ngày')
                    ->schema([
                        Placeholder::make('quota_info')
                            ->label('Thông tin quota')
                            ->content(function () {
                                return "Còn lại: {$this->quota['remaining_count']} số lần mua, {$this->quota['remaining_budget']} USD";
                            })
                            ->columnSpanFull(),
                        ]),

                Section::make('Thông tin mua proxy')
                    ->description('Điền thông tin để mua proxy mới')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Select::make('type')
                                    ->label('Loại proxy')
                                    ->options([
                                        'dedicated' => 'Dedicated',
                                    ])
                                    ->default('dedicated')
                                    ->required(),

                                TextInput::make('numberPorts')
                                    ->label('Số lượng proxy')
                                    ->numeric()
                                    ->default(1)
                                    ->minValue(1)
                                    ->maxValue(10)
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state) {
                                        $this->validateQuota($state);
                                    }),

                                TextInput::make('username')
                                    ->label('Username')
                                    ->required()
                                    ->maxLength(50)
                                    ->readonly()
                                    ->default(fn() => 'user_' . \Str::random(8)),

                                TextInput::make('password')
                                    ->label('Password')
                                    ->required()
                                    ->maxLength(50)
                                    ->readonly()
                                    ->default(fn() => 'proxy_' . \Str::random(8)),

                                Select::make('geolocs')
                                    ->label('Vị trí địa lý')
                                    ->options([
                                        'US-NC' => 'US - North Carolina',
                                    ])
                                    ->multiple()
                                    ->default(['US-NC'])
                                    ->required()
                                    ->columnSpanFull(),

                                TextInput::make('authIps')
                                    ->label('Auth IPs')
                                    ->placeholder('IP được phép truy cập proxy')
                                    ->default(fn() => request()->ip())
                                    ->columnSpanFull(),
                            ]),
                    ]),
            ])
            ->statePath('data');
    }

    protected function validateQuota(?int $quantity): void
    {
        if (!$quantity) return;

        $this->loadUserQuota();

        // Kiểm tra giới hạn số lượng
        if ($quantity > $this->quota['remaining_count']) {
            Notification::make()
                ->title('Vượt quá giới hạn số lượng')
                ->body("Bạn chỉ có thể mua thêm {$this->quota['remaining_count']} proxy hôm nay")
                ->warning()
                ->send();
            return;
        }

        // Kiểm tra giới hạn ngân sách (mỗi proxy $3)
        $estimatedCost = $quantity * 3.0;
        if ($estimatedCost > $this->quota['remaining_budget']) {
            Notification::make()
                ->title('Vượt quá giới hạn ngân sách')
                ->body("Chi phí ước tính: $" . number_format($estimatedCost, 2) . ". Ngân sách còn lại: $" . number_format($this->quota['remaining_budget'], 2))
                ->warning()
                ->send();
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('buyProxy')
                ->label('Mua Proxy')
                ->icon('heroicon-o-shopping-cart')
                ->color('primary')
                ->requiresConfirmation()
                ->modalHeading('Xác nhận mua proxy')
                ->modalDescription('Bạn có chắc chắn muốn mua proxy với thông tin đã nhập?')
                ->modalSubmitActionLabel('Xác nhận mua')
                ->action('purchaseProxy')
                ->disabled(fn() => $this->quota['remaining_count'] <= 0),

            Action::make('refreshQuota')
                ->label('Làm mới')
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->action(function () {
                    $this->loadUserQuota();
                    Notification::make()
                        ->title('Đã làm mới thông tin quota')
                        ->success()
                        ->send();
                }),
        ];
    }

    public function purchaseProxy(): void
    {
        try {
            $data = $this->form->getState();

            // Debug: Log payload để kiểm tra
            \Log::info('Proxy Purchase Payload:', $data);

            // Validate form data
            if (empty($data['type']) || empty($data['numberPorts']) || empty($data['username']) || empty($data['password']) || empty($data['geolocs'])) {
                Notification::make()
                    ->title('Lỗi')
                    ->body('Vui lòng điền đầy đủ thông tin bắt buộc')
                    ->danger()
                    ->send();
                return;
            }

            $proxyService = app(ProxyPurchaseService::class);
            $result = $proxyService->purchaseProxy(Auth::user(), $data);

            if ($result['success']) {
                Notification::make()
                    ->title('Thành công!')
                    ->body($result['message'])
                    ->success()
                    ->send();

                // Reset form và reload quota
                $this->form->fill();
                $this->loadUserQuota();
                
                // Refresh table
                $this->resetTable();
            } else {
                Notification::make()
                    ->title('Thất bại')
                    ->body($result['message'])
                    ->danger()
                    ->send();

                if (!empty($result['errors'])) {
                    foreach ($result['errors'] as $error) {
                        Notification::make()
                            ->title('Chi tiết lỗi')
                            ->body($error)
                            ->warning()
                            ->send();
                    }
                }
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Có lỗi xảy ra')
                ->body('Vui lòng thử lại sau: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(ProxyPurchase::query()->with('user')->latest())
            ->columns([
                TextColumn::make('user.name')
                    ->label('Người mua')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('purchase_date')
                    ->label('Ngày mua')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'success',
                        'danger' => 'failed',
                        'secondary' => 'cancelled',
                    ])
                    ->icons([
                        'heroicon-o-clock' => 'pending',
                        'heroicon-o-check-circle' => 'success',
                        'heroicon-o-x-circle' => 'failed',
                        'heroicon-o-minus-circle' => 'cancelled',
                    ]),

                TextColumn::make('proxy_type')
                    ->label('Loại')
                    ->badge(),

                TextColumn::make('number_ports')
                    ->label('Số lượng')
                    ->alignCenter(),

                TextColumn::make('proxy_location')
                    ->label('Vị trí')
                    ->badge()
                    ->color('gray'),

                TextColumn::make('username')
                    ->label('Username')
                    ->alignCenter(),
                
                TextColumn::make('password')
                    ->label('Password')
                    ->alignCenter(),

                TextColumn::make('cost')
                    ->label('Chi phí')
                    ->money('USD'),

                TextColumn::make('proxy_info')
                    ->label('Proxy Info')
                    ->limit(70)
                    ->placeholder('Chưa có thông tin')
                    ->formatStateUsing(function (?string $state, ProxyPurchase $record): string {
                        if (empty($state) || $state === '[]') {
                            // Hiển thị username/password từ database nếu chưa có proxy_info
                            if ($record->username && $record->password) {
                                return "User: {$record->username} | Pass: {$record->password}";
                            }
                            return 'Chưa có thông tin';
                        }

                        $proxyData = json_decode($state, true);
                        if (!$proxyData || !is_array($proxyData)) {
                            return 'Dữ liệu không hợp lệ';
                        }

                        $info = [];
                        foreach ($proxyData as $proxy) {
                            $address = ($proxy['proxy_address'] ?? 'N/A') . ':' . ($proxy['proxy_port'] ?? 'N/A');
                            $username = $proxy['username'] ?? $record->username ?? 'N/A';
                            $password = $proxy['password'] ?? $record->password ?? 'N/A';
                            $info[] = "{$address} | {$username}:{$password}";
                        }

                        return implode(' | ', $info);
                    })
                    ->tooltip(function (ProxyPurchase $record): ?string {
                        if (empty($record->proxy_info) || $record->proxy_info === '[]') {
                            // Hiển thị thông tin từ database nếu chưa có proxy_info
                            if ($record->username && $record->password) {
                                return "Username: {$record->username}\nPassword: {$record->password}\n\nChưa có thông tin proxy chi tiết từ API";
                            }
                            return 'Chưa có thông tin proxy';
                        }

                        $proxyData = json_decode($record->proxy_info, true);
                        if (!$proxyData) {
                            return 'Dữ liệu không hợp lệ';
                        }

                        $details = [];
                        foreach ($proxyData as $index => $proxy) {
                            $username = $proxy['username'] ?? $record->username ?? 'N/A';
                            $password = $proxy['password'] ?? $record->password ?? 'N/A';

                            $details[] = sprintf(
                                "Proxy #%d:\nIP: %s:%s\nUsername: %s\nPassword: %s\nCountry: %s\nAuth IPs: %s",
                                $index + 1,
                                $proxy['proxy_address'] ?? 'N/A',
                                $proxy['proxy_port'] ?? 'N/A',
                                $username,
                                $password,
                                $proxy['country'] ?? 'N/A',
                                is_array($proxy['auth_ips'] ?? null) ? implode(', ', $proxy['auth_ips']) : ($proxy['auth_ips'] ?? 'N/A')
                            );
                        }

                        return implode("\n\n", $details);
                    }),
            ])
            ->filters([
                SelectFilter::make('user_id')
                    ->label('Người mua')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->visible(fn() => Auth::user()->hasRole(['super_admin', 'User Manager'])),

                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'success' => 'Success',
                        'failed' => 'Failed',
                        'cancelled' => 'Cancelled',
                    ]),
                SelectFilter::make('proxy_type')
                    ->options([
                        'dedicated' => 'Dedicated',
                        'shared' => 'Shared',
                    ]),
            ])
            ->actions([
             
                TableAction::make('view_proxy_details')
                    ->label('Xem chi tiết')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->visible(fn (ProxyPurchase $record): bool =>
                        !empty($record->proxy_info) && $record->proxy_info !== '[]' && $record->proxy_info !== 'null'
                    )
                    ->modalHeading('Chi tiết Proxy')
                    ->modalWidth('4xl')
                    ->modalContent(function (ProxyPurchase $record) {
                        $proxyData = json_decode($record->proxy_info, true);
                        if (!$proxyData) return 'Không có thông tin proxy';

                        $html = '<div class="space-y-4">';

                        foreach ($proxyData as $index => $proxy) {
                            $html .= '<div class="border rounded-lg p-4 bg-gray-50">';
                            $html .= '<h3 class="font-bold text-lg mb-4 text-blue-600">Proxy #' . ($index + 1) . '</h3>';

                            $html .= '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';

                            // Chỉ hiển thị các trường cần thiết
                            $essentialFields = [
                                'proxy_address' => 'IP Address',
                                'proxy_port' => 'Port',
                                'username' => 'Username',
                                'password' => 'Password',
                                'country' => 'Country',
                                'state' => 'State',
                                'auth_ips' => 'Auth IPs',
                                'expires_at' => 'Expires At',
                                'created_at' => 'Created At',
                                'type' => 'Type',
                                'dedicated' => 'Dedicated',
                                'cost' => 'Cost',
                                'auto_renew' => 'Auto Renew'
                            ];

                            foreach ($essentialFields as $key => $label) {
                                if (!isset($proxy[$key])) continue;

                                $value = $proxy[$key];
                                $html .= '<div class="flex flex-col">';
                                $html .= '<span class="text-sm font-medium text-gray-600">' . $label . ':</span>';

                                if (is_array($value)) {
                                    $html .= '<span class="text-sm font-semibold text-gray-900">' . implode(', ', $value) . '</span>';
                                } elseif (is_bool($value)) {
                                    $html .= '<span class="text-sm font-semibold text-gray-900">' . ($value ? 'Yes' : 'No') . '</span>';
                                } elseif (is_null($value) || $value === '') {
                                    $html .= '<span class="text-sm text-gray-400 italic">N/A</span>';
                                } else {
                                    // Highlight các trường quan trọng
                                    $importantFields = ['proxy_address', 'proxy_port', 'username', 'password'];
                                    $class = in_array($key, $importantFields)
                                        ? 'text-sm font-bold text-gray-900 bg-yellow-100 px-2 py-1 rounded'
                                        : 'text-sm font-semibold text-gray-900';
                                    $html .= '<span class="' . $class . '">' . htmlspecialchars((string) $value) . '</span>';
                                }

                                $html .= '</div>';
                            }

                            $html .= '</div>'; // End grid
                            $html .= '</div>'; // End proxy container
                        }

                        $html .= '</div>';

                        return new \Illuminate\Support\HtmlString($html);
                    })
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Đóng'),
            ])
            ->headerActions([
                \Filament\Tables\Actions\Action::make('sync_all_proxies')
                    ->label('Đồng bộ tất cả Proxy (Force)')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->modalHeading('Đồng bộ tất cả Proxy')
                    ->modalDescription('Hệ thống sẽ lấy tất cả proxy từ API và cập nhật thông tin cho các đơn hàng thành công có username bắt đầu bằng "user_" và password bắt đầu bằng "proxy_" (kể cả đã có thông tin). Quá trình này có thể mất vài phút.')
                    ->modalSubmitActionLabel('Bắt đầu đồng bộ')
                    ->action(function () {
                        try {
                            $result = $this->syncAllProxies();

                            if ($result['success']) {
                                Notification::make()
                                    ->title('Đồng bộ thành công!')
                                    ->body($result['message'])
                                    ->success()
                                    ->send();

                                // Refresh table
                                $this->resetTable();
                            } else {
                                Notification::make()
                                    ->title('Đồng bộ thất bại')
                                    ->body($result['message'])
                                    ->danger()
                                    ->send();
                            }
                        } catch (\Exception $e) {
                            \Log::error('Sync All Proxies Error:', [
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString()
                            ]);

                            Notification::make()
                                ->title('Có lỗi xảy ra')
                                ->body('Lỗi: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->defaultSort('purchase_date', 'desc')
            ->heading(Auth::user()->hasRole(['super_admin', 'User Manager']) ? 'Lịch sử mua proxy (Tất cả)' : 'Lịch sử mua proxy của bạn')
            ->description(Auth::user()->hasRole(['super_admin', 'User Manager']) ? 'Danh sách tất cả proxy đã mua trong hệ thống' : 'Danh sách các proxy đã mua');
    }

   

    /**
     * Đồng bộ tất cả proxy từ API
     */
    protected function syncAllProxies(): array
    {
        try {
            \Log::channel('proxy')->info('Sync All Proxies Started', [
                'user_id' => Auth::id(),
                'filter_criteria' => 'username starts with "user_" AND password starts with "proxy_"'
            ]);

            // Lấy TẤT CẢ proxy purchases thành công của user hiện tại (kể cả đã có proxy_info)
            $successRecords = ProxyPurchase::where('status', 'success')
                ->where('user_id', Auth::id())
                ->get();

            if ($successRecords->isEmpty()) {
                return [
                    'success' => true,
                    'message' => 'Không có đơn hàng thành công nào để đồng bộ.',
                ];
            }

            // Lấy ProxyAccount để gọi API
            $proxyAccount = ProxyAccount::getBestAccount();
            if (!$proxyAccount) {
                throw new \Exception('Không tìm thấy tài khoản proxy để gọi API');
            }

            // Lấy tất cả proxy từ API một lần
            $allProxiesResult = $proxyAccount->getProxies(['active' => 1]);
            if (!$allProxiesResult['success']) {
                throw new \Exception('Không thể lấy danh sách proxy từ API: ' . $allProxiesResult['message']);
            }

            $allProxies = $allProxiesResult['data'];

            // Sort theo created_at descending
            if (!empty($allProxies)) {
                usort($allProxies, function($a, $b) {
                    $timeA = isset($a['created_at']) ? strtotime($a['created_at']) : 0;
                    $timeB = isset($b['created_at']) ? strtotime($b['created_at']) : 0;
                    return $timeB - $timeA;
                });
            }

            // Lấy danh sách username+password từ database để tạo unique key
            // Chỉ lấy những đơn có username bắt đầu bằng "user_" và password bắt đầu bằng "proxy_"
            $userCredentials = $successRecords->map(function($record) {
                // Trim whitespace và ký tự đặc biệt
                $username = trim($record->username);
                $password = trim($record->password);

                return [
                    'username' => $username,
                    'password' => $password,
                    'key' => $username . '|' . $password
                ];
            })
            ->filter(function($item) {
                return !empty($item['username']) && !empty($item['password']) &&
                       str_starts_with($item['username'], 'user_') &&
                       str_starts_with($item['password'], 'proxy_');
            })
            ->pluck('key')->unique()->values()->toArray();

            // Tạo mapping "username|password" -> proxy data (chỉ cho credentials của user)
            $proxyMap = [];
            foreach ($allProxies as $proxy) {
                if (isset($proxy['username']) && isset($proxy['password'])) {
                    $proxyKey = $proxy['username'] . '|' . $proxy['password'];
                    if (in_array($proxyKey, $userCredentials)) {
                        if (!isset($proxyMap[$proxyKey])) {
                            $proxyMap[$proxyKey] = [];
                        }
                        $proxyMap[$proxyKey][] = $proxy;
                    }
                }
            }

            $syncedCount = 0;
            $failedCount = 0;

            // Cập nhật từng record
            foreach ($successRecords as $record) {
                try {
                    if (empty($record->username) || empty($record->password)) {
                        $failedCount++;
                        continue;
                    }

                    // Trim whitespace và ký tự đặc biệt
                    $username = trim($record->username);
                    $password = trim($record->password);

                    // Chỉ đồng bộ những đơn có username bắt đầu bằng "user_" và password bắt đầu bằng "proxy_"
                    if (!str_starts_with($username, 'user_') || !str_starts_with($password, 'proxy_')) {
                        $failedCount++;
                        continue;
                    }

                    $proxyKey = $username . '|' . $password;

                    // Double check: chỉ đồng bộ nếu username+password thuộc về user này
                    if (!in_array($proxyKey, $userCredentials)) {
                        $failedCount++;
                        continue;
                    }

                    if (isset($proxyMap[$proxyKey]) && !empty($proxyMap[$proxyKey])) {
                        $proxyData = $proxyMap[$proxyKey];

                        // Kiểm tra xem đã có proxy_info chưa
                        $hadProxyInfo = !empty($record->proxy_info) && $record->proxy_info !== '[]';

                        // Tính tổng cost từ tất cả proxy trong response
                        $totalCost = 0;
                        foreach ($proxyData as $proxy) {
                            if (isset($proxy['cost']) && is_numeric($proxy['cost'])) {
                                $totalCost += (float) $proxy['cost'];
                            }
                        }

                        $record->update([
                            'proxy_info' => json_encode($proxyData),
                            'proxy_data' => $proxyData,
                            'cost' => $totalCost > 0 ? $totalCost : null,
                        ]);

                        \Log::channel('proxy')->info('Proxy Synced', [
                            'purchase_id' => $record->id,
                            'username' => $username,
                            'password' => $password,
                            'proxy_key' => $proxyKey,
                            'proxy_count' => count($proxyData),
                            'total_cost' => $totalCost,
                            'action' => $hadProxyInfo ? 'updated' : 'added'
                        ]);

                        $syncedCount++;
                    } else {
                        $failedCount++;
                    }
                } catch (\Exception $e) {
                    \Log::channel('proxy')->error('Sync Failed', [
                        'purchase_id' => $record->id,
                        'error' => $e->getMessage()
                    ]);
                    $failedCount++;
                }
            }

            \Log::channel('proxy')->info('Sync All Proxies Completed', [
                'user_id' => Auth::id(),
                'total_records' => $successRecords->count(),
                'synced' => $syncedCount,
                'failed' => $failedCount
            ]);

            return [
                'success' => true,
                'message' => "Đồng bộ hoàn tất: {$syncedCount} thành công, {$failedCount} thất bại từ tổng {$successRecords->count()} đơn hàng.",
            ];

        } catch (\Exception $e) {
            \Log::channel('proxy')->error('Sync All Proxies Error', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Lỗi khi đồng bộ: ' . $e->getMessage(),
            ];
        }
    }
}
