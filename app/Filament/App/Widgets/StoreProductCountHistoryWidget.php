<?php

namespace App\Filament\App\Widgets;

use App\Models\StoreProductCountHistory;
use Filament\Widgets\Widget;

class StoreProductCountHistoryWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.store-product-count-history-widget';
    public function getColumnSpan(): int|string|array
    {
        return 'full';
    }
    protected function getViewData(): array
    {
        $increases = StoreProductCountHistory::getDailyIncreases();

        return ['increases' => $increases];
    }
}
