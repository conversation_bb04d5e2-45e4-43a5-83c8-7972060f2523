<?php

namespace App\Filament\App\Widgets;

use App\Models\PayoutTransaction;
use App\Models\SupplierOrder;
use App\Models\TikTokPayment;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class MonthlyPayouts extends Widget
{
    protected static string $view = 'filament.app.widgets.monthly-payouts-widget';
  
    public $payouts = [];
    public $baseCosts = [];
    public $profits = [];
    public $tiktok_payout = [];
    protected int | string | array $columnSpan = [
        'default' => 2,
        'md' => 2,
        'lg' => 2,
        'xl' => 2,
    ];
    public function mount()
    {
        $this->payouts = $this->getMonthlyPayouts();
        $this->baseCosts = $this->getMonthlyBaseCosts();
        $this->profits = $this->calculateProfits();
        $this->tiktok_payout = $this->getMonthlyTiktokPayment();
    }

    protected function getMonthlyPayouts()
    {
        $payouts = [];
        for ($i = 0; $i < 4; $i++) {
            $date = Carbon::now()->subMonths($i);
            $payouts[$date->format('F Y')] = PayoutTransaction::whereMonth('time', $date->month)
                ->whereYear('time', $date->year)
                ->sum('amount');
        }
        return $payouts;
    }
    protected function getMonthlyTiktokPayment()
    {
        $payouts = [];
        for ($i = 0; $i < 4; $i++) {
            $date = Carbon::now()->subMonths($i);
            $payouts[$date->format('F Y')] = TikTokPayment::whereMonth('paid_time', $date->month)
                ->whereYear('paid_time', $date->year)
                ->where('status', 'PAID')
                ->sum('settlement_amount');
        }
        return $payouts;
    }
    protected function getMonthlyBaseCosts()
    {
        $baseCosts = [];
        $user = auth()->user();
        $query = SupplierOrder::query();
    
        // Only filter by owner_id if user is not super_admin
        if (!$user->hasRole('super_admin')) {
            $query->whereHas('store', function ($q) use ($user) {
                $q->where('owner_id', $user->id);
            });
        }
    
        for ($i = 0; $i < 4; $i++) {
            $date = Carbon::now()->subMonths($i);
            $baseCosts[$date->format('F Y')] = (clone $query)
                ->whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->sum('base_cost');
        }
        return $baseCosts;
    }

    protected function calculateProfits()
    {
        $profits = [];
        foreach ($this->payouts as $month => $payout) {
            $baseCost = $this->baseCosts[$month] ?? 0;
            $profits[$month] = $payout - $baseCost;
        }
        return $profits;
    }
}
