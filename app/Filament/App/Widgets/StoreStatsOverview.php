<?php

namespace App\Filament\App\Widgets;

use App\Models\Store;
use App\Models\Order;
use App\Models\Bank;
use App\Models\SupplierOrder;
use App\Models\TikTokPayment;
use App\Enums\TiktokShopStatus;
use App\Enums\StoreType;
use Filament\Widgets\Widget;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Livewire\Attributes\Reactive;


class StoreStatsOverview extends Widget
{
    protected static string $view = 'filament.app.widgets.store-stats-overview';

    // Removed polling for performance optimization
    protected static ?int $sort = 1;

    // State để theo dõi filter sync 7 ngày cho Top Store Hold Amount
    public bool $showOnlySync7Days = false;

    protected int | string | array $columnSpan = [
        'default' => 2,  // Mobile: chiếm 2 cột (full width)
        'sm' => 2,       // Small: chiếm 2 cột (full width)
        'md' => 3,       // Medium: chiếm 3 cột
        'lg' => 4,       // Large: chiếm 4 cột (full width)
        'xl' => 'full',  // XL: chiếm full width để chứa 5 cột
    ];
    /**
     * Lấy dữ liệu cho widget với cache có điều kiện (chỉ cache cho super_admin)
     */
    public function getViewData(): array
    {
        $user = auth()->user();
        $isSuperAdmin = $user && $user->hasRole('super_admin');

        // Chỉ cache khi user là super_admin
        if ($isSuperAdmin) {
            return Cache::remember('store_stats_overview_widget_super_admin', 2 * 60 * 60, function () {
                return $this->calculateWidgetData();
            });
        }

        // Không cache cho user khác
        return $this->calculateWidgetData();
    }

    /**
     * Tính toán dữ liệu widget
     */
    private function calculateWidgetData(): array
    {
        return [
            'storeStats' => $this->calculateStats(),
            'topStoresLoss' => $this->getTopStoresByLoss(),
            'topStoresLastMonth' => $this->getTopStoresByOrdersLastMonth(),
            'payoutStats' => $this->calculatePayoutStats(),
            'topStoresHold' => $this->getTopStoresByHoldAmount(),
            'topStoresRefund' => $this->getTopStoresByRefund(),
            'payoutDayBreakdown' => $this->getPayoutDayBreakdown(),
            'tiktokPaymentStats' => $this->calculateTikTokPaymentStats(),
        ];
    }

    /**
     * Tính toán các thống kê store với queries tối ưu
     */
    private function calculateStats(): array
    {
        // Gộp nhiều thống kê cơ bản trong 1 query
        $basicStats = Store::selectRaw('
            COUNT(*) as total_stores,
            SUM(CASE WHEN tiktok_shop_status = ? THEN 1 ELSE 0 END) as live_stores,
            SUM(CASE WHEN bank_account IS NOT NULL AND bank_account != "" THEN 1 ELSE 0 END) as stores_with_bank,
            SUM(CASE WHEN status = "Active" THEN 1 ELSE 0 END) as active_stores,
            SUM(CASE WHEN tiktok_shop_status = ? THEN 1 ELSE 0 END) as suspended_stores,
            SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as new_stores_this_week,
            SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as new_stores_this_month,
            SUM(CASE WHEN last_created_tiktok_product IS NOT NULL AND last_created_tiktok_product >= ? THEN 1 ELSE 0 END) as active_product_stores
        ', [
            TiktokShopStatus::Live->value,
            TiktokShopStatus::Suspended->value,
            Carbon::now()->startOfWeek(),
            Carbon::now()->startOfMonth(),
            Carbon::now()->subDays(7)
        ])->first();

        $totalStores = $basicStats->total_stores;
        $liveStores = $basicStats->live_stores;
        $storesWithBank = $basicStats->stores_with_bank;
        $activeStores = $basicStats->active_stores;
        $suspendedStores = $basicStats->suspended_stores;
        $newStoresThisWeek = $basicStats->new_stores_this_week;
        $newStoresThisMonth = $basicStats->new_stores_this_month;
        $activeProductStores = $basicStats->active_product_stores;

        // Gộp các thống kê phức tạp hơn trong 1 query
        $complexStats = Store::selectRaw('
            SUM(CASE WHEN EXISTS(SELECT 1 FROM orders WHERE orders.store_id = stores.id)
                AND (bank_account IS NULL OR bank_account = "")
                AND (card IS NULL OR card = "") THEN 1 ELSE 0 END) as stores_with_orders_no_bank,
            SUM(CASE WHEN NOT EXISTS(SELECT 1 FROM orders WHERE orders.store_id = stores.id) THEN 1 ELSE 0 END) as stores_without_orders,
            SUM(CASE WHEN tiktok_shop_status = ? AND (last_sync_tiktok IS NULL OR last_sync_tiktok < ?) THEN 1 ELSE 0 END) as stores_die_update_late,
            SUM(CASE WHEN tiktok_shop_status = ? AND (last_sync_tiktok IS NULL OR last_sync_tiktok < ?) THEN 1 ELSE 0 END) as stores_live_update_late
        ', [
            TiktokShopStatus::Suspended->value,
            Carbon::now()->subDays(3),
            TiktokShopStatus::Live->value,
            Carbon::now()->subDays(3)
        ])->first();

        $storesWithOrdersNoBank = $complexStats->stores_with_orders_no_bank;
        $storesWithoutOrders = $complexStats->stores_without_orders;
        $storesDieUpdateLate = $complexStats->stores_die_update_late;
        $storesLiveUpdateLate = $complexStats->stores_live_update_late;

        // Tính phần trăm
        $livePercentage = $totalStores > 0 ? round(($liveStores / $totalStores) * 100, 1) : 0;
        $bankPercentage = $totalStores > 0 ? round(($storesWithBank / $totalStores) * 100, 1) : 0;

        // Gộp bank statistics trong 1 query
        $bankStats = Store::selectRaw('
            SUM(CASE WHEN bank_account IS NULL OR bank_account = "" THEN 1 ELSE 0 END) as stores_no_bank_account,
            SUM(CASE WHEN bank_account IS NOT NULL AND bank_account != "" AND (card IS NULL OR card = "") THEN 1 ELSE 0 END) as stores_no_tiktok_bank,
            SUM(CASE WHEN (bank_account IS NULL OR bank_account = "") THEN 1 ELSE 0 END) as stores_no_tool_bank,
            SUM(CASE WHEN (bank_account IS NULL OR bank_account = "") AND card IS NOT NULL AND card != "" THEN 1 ELSE 0 END) as stores_has_card_no_bank,
            SUM(CASE WHEN card IS NOT NULL AND card != "" AND (bank_account IS NULL OR bank_account = "") THEN 1 ELSE 0 END) as stores_has_tiktok_bank_no_system_bank,
            SUM(CASE WHEN bank_account IS NOT NULL AND bank_account != "" AND card IS NOT NULL AND card != "" AND RIGHT(bank_account, 4) != RIGHT(card, 4) THEN 1 ELSE 0 END) as stores_bank_mismatch,
            SUM(CASE WHEN last_sync_tiktok IS NULL OR last_sync_tiktok < ? THEN 1 ELSE 0 END) as stores_not_synced_over_3_days,
            SUM(CASE WHEN last_sync_tiktok IS NOT NULL AND last_sync_tiktok >= ? THEN 1 ELSE 0 END) as stores_synced_under_3_days
        ', [now()->subDays(3), now()->subDays(3)])->first();

        $storesNoBankAccount = $bankStats->stores_no_bank_account;
        $storesNoTikTokBank = $bankStats->stores_no_tiktok_bank;
        $storesNoToolBank = $bankStats->stores_no_tool_bank;
        $storesHasCardNoBank = $bankStats->stores_has_card_no_bank;
        $storesHasTikTokBankNoSystemBank = $bankStats->stores_has_tiktok_bank_no_system_bank;
        $storesBankMismatch = $bankStats->stores_bank_mismatch;
        $storesNotSyncedOver3Days = $bankStats->stores_not_synced_over_3_days;
        $storesSyncedUnder3Days = $bankStats->stores_synced_under_3_days;

        // Tính toán settlement statistics từ tiktok_data JSON
        $settlementStats = $this->calculateSettlementStats();

        return [
            'totalStores' => $totalStores,
            'liveStores' => $liveStores,
            'livePercentage' => $livePercentage,
            'storesWithBank' => $storesWithBank,
            'bankPercentage' => $bankPercentage,
            'storesWithOrdersNoBank' => $storesWithOrdersNoBank,
            'storesDieUpdateLate' => $storesDieUpdateLate,
            'storesLiveUpdateLate' => $storesLiveUpdateLate,
            'activeStores' => $activeStores,
            'suspendedStores' => $suspendedStores,
            'storesWithoutOrders' => $storesWithoutOrders,
            'newStoresThisWeek' => $newStoresThisWeek,
            'newStoresThisMonth' => $newStoresThisMonth,
            // Bank status statistics
            'storesNoBankAccount' => $storesNoBankAccount,
            'storesNoTikTokBank' => $storesNoTikTokBank,
            'storesHasTikTokBankNoSystemBank' => $storesHasTikTokBankNoSystemBank,
            'storesBankMismatch' => $storesBankMismatch,
            // Sync statistics
            'storesNotSyncedOver3Days' => $storesNotSyncedOver3Days,
            'storesSyncedUnder3Days' => $storesSyncedUnder3Days,
            // Settlement statistics
            'pendingConfirmation' => $settlementStats['pendingConfirmation'],
            'pendingRefund' => $settlementStats['pendingRefund'],
            'pendingPayment' => $settlementStats['pendingPayment'],
        ];
    }

    /**
     * Lấy top stores theo mức độ lỗ - Chỉ tính bank payout từ payout_transactions
     */
    private function getTopStoresByLoss(): array
    {
        // Subquery 1: Tính fulfill cost, loại bỏ cancelled orders
        $fulfillCostSubquery = SupplierOrder::selectRaw('store_id, SUM(base_cost) as total_fulfill_cost')
            ->where('status', '!=', 'Cancelled') // Loại bỏ đơn cancelled
            ->groupBy('store_id');

        // Subquery 2: Tính bank payout từ payout_transactions (giống logic SellerService)
        $bankPayoutSubquery = DB::table('payout_transactions')
            ->join('stores', function($join) {
                $join->on('stores.bank_account', '=', 'payout_transactions.card_no')
                     ->whereNotNull('stores.bank_account')
                     ->where('stores.bank_account', '!=', '');
            })
            ->selectRaw('stores.id as store_id, SUM(payout_transactions.amount) as total_bank_payout')
            ->where('payout_transactions.type', 'Receive')
            ->where('payout_transactions.status', 'Success')
            ->groupBy('stores.id');

        // Main query với joins
        $storesWithLoss = Store::with('owner')
            ->leftJoinSub($fulfillCostSubquery, 'fulfill_costs', function ($join) {
                $join->on('stores.id', '=', 'fulfill_costs.store_id');
            })
            ->leftJoinSub($bankPayoutSubquery, 'bank_payouts', function ($join) {
                $join->on('stores.id', '=', 'bank_payouts.store_id');
            })
            ->selectRaw('
                stores.id,
                stores.name,
                stores.owner_id,
                stores.last_sync_tiktok,
                COALESCE(fulfill_costs.total_fulfill_cost, 0) as total_fulfill_cost,
                COALESCE(bank_payouts.total_bank_payout, 0) as total_bank_payout,
                (COALESCE(fulfill_costs.total_fulfill_cost, 0) - COALESCE(bank_payouts.total_bank_payout, 0)) as loss_amount
            ')
            ->havingRaw('loss_amount > 0') // Chỉ lấy stores có lỗ (fulfill cost > bank payout)
            ->orderBy('loss_amount', 'desc')
            ->limit(100)
            ->get();

        return $storesWithLoss->map(function ($store) {
            // Tính thời gian sync
            $syncTime = 'Chưa sync';
            if ($store->last_sync_tiktok) {
                $diffForHumans = Carbon::parse($store->last_sync_tiktok)->diffForHumans();
                // Bỏ chữ "ago" để tiết kiệm diện tích
                $syncTime = str_replace(' ago', '', $diffForHumans);
            }

            return [
                'id' => $store->id,
                'name' => $store->name,
                'owner_name' => $store->owner ? $store->owner->name : 'N/A',
                'total_fulfill_cost' => (float) $store->total_fulfill_cost,
                'total_bank_payout' => (float) $store->total_bank_payout,
                'loss_amount' => (float) $store->loss_amount, // Số tiền lỗ (fulfill cost - bank payout)
                'sync_time' => $syncTime,
            ];
        })->toArray();
    }

    /**
     * Lấy top stores theo số lượng orders trong tháng trước
     */
    private function getTopStoresByOrdersLastMonth(): array
    {
        $now = Carbon::now();
        $dateRange = [$now->copy()->subMonth()->startOfMonth(), $now->copy()->subMonth()->endOfMonth()];

        // Subquery để tính order count và revenue
        $orderStats = Order::selectRaw('store_id, COUNT(*) as order_count, SUM(total) as total_revenue')
            ->whereBetween('created_at', $dateRange)
            ->groupBy('store_id')
            ->havingRaw('COUNT(*) > 0');

        // Main query với join
        $topStores = Store::with('owner')
            ->joinSub($orderStats, 'order_stats', function ($join) {
                $join->on('stores.id', '=', 'order_stats.store_id');
            })
            ->select([
                'stores.id',
                'stores.name',
                'stores.owner_id',
                'order_stats.order_count',
                'order_stats.total_revenue'
            ])
            ->orderBy('order_stats.order_count', 'desc')
            ->limit(100)
            ->get();

        return $topStores->map(function ($store) {
            return [
                'id' => $store->id,
                'name' => $store->name,
                'owner_name' => $store->owner ? $store->owner->name : 'N/A',
                'order_count' => $store->order_count,
                'total_revenue' => $store->total_revenue,
            ];
        })->toArray();
    }

    /**
     * Tính toán thống kê payout - Chỉ tính các store sync trong 7 ngày
     */
    private function calculatePayoutStats(): array
    {
        // Điều kiện chung: chỉ tính stores đã sync trong 7 ngày
        $syncedWithin7Days = function($query) {
            $query->whereNotNull('last_sync_tiktok')
                  ->where('last_sync_tiktok', '>=', now()->subDays(7));
        };

        // Tổng payout on hold từ stores sync trong 7 ngày (cast to decimal for proper calculation)
        $totalPayoutOnHold = Store::whereNotNull('tiktok_payout_on_hold')
            ->where('tiktok_payout_on_hold', '>', 0)
            ->where($syncedWithin7Days)
            ->selectRaw('SUM(CAST(tiktok_payout_on_hold AS DECIMAL(15,2))) as total')
            ->value('total');

        // Trung bình ngày payout (từ tiktok_payout_day) - chỉ stores sync trong 7 ngày
        $avgPayoutDay = Store::whereNotNull('tiktok_payout_day')
            ->where('tiktok_payout_day', '>', 0)
            ->where($syncedWithin7Days)
            ->avg('tiktok_payout_day');

        // Số stores có payout on hold và sync trong 7 ngày
        $storesWithHold = Store::where('tiktok_payout_on_hold', '>', 0)
            ->where($syncedWithin7Days)
            ->count();

        // Tổng payout on hold của stores sync > 3 ngày (giữ nguyên logic cũ cho thống kê so sánh)
        $payoutSyncOver3Days = Store::whereNotNull('tiktok_payout_on_hold')
            ->where('tiktok_payout_on_hold', '>', 0)
            ->where(function($query) {
                $query->whereNull('last_sync_tiktok')
                      ->orWhere('last_sync_tiktok', '<', now()->subDays(3));
            })
            ->selectRaw('SUM(CAST(tiktok_payout_on_hold AS DECIMAL(15,2))) as total')
            ->value('total');

        // Số lượng store sync < 7 ngày 
        $storesNotSyncedOver7Days = Store::where($syncedWithin7Days)
            ->count();

        return [
            'totalPayoutOnHold' => $totalPayoutOnHold ?? 0,
            'avgPayoutDay' => round($avgPayoutDay ?? 0, 1),
            'storesWithHold' => $storesWithHold,
            'payoutSyncOver3Days' => $payoutSyncOver3Days ?? 0,
            'storesNotSyncedOver7Days' => $storesNotSyncedOver7Days ?? 0,
        ];
    }

    /**
     * Lấy top stores theo hold amount
     */
    private function getTopStoresByHoldAmount(): array
    {
        $query = Store::with('owner')
            ->where('tiktok_payout_on_hold', '>', 0);

        // Nếu filter sync 7 ngày được bật, chỉ lấy stores sync trong 7 ngày
        if ($this->showOnlySync7Days) {
            $query->whereNotNull('last_sync_tiktok')
                  ->where('last_sync_tiktok', '>=', now()->subDays(7));
        }

        $topStores = $query->orderByRaw('CAST(tiktok_payout_on_hold AS DECIMAL(15,2)) DESC')
            ->limit(100)
            ->get();

        return $topStores->map(function ($store) {
            // Tính thời gian sync
            $syncTime = 'Chưa sync';
            if ($store->last_sync_tiktok) {
                $diffForHumans = Carbon::parse($store->last_sync_tiktok)->diffForHumans();
                // Bỏ chữ "ago" để tiết kiệm diện tích
                $syncTime = str_replace(' ago', '', $diffForHumans);
            }

            return [
                'id' => $store->id,
                'name' => $store->name,
                'owner_name' => $store->owner ? $store->owner->name : 'N/A',
                'payout_on_hold' => (float) $store->tiktok_payout_on_hold,
                'sync_time' => $syncTime,
                'last_sync_tiktok' => $store->last_sync_tiktok,
            ];
        })->toArray();
    }

    /**
     * Toggle filter sync 7 ngày cho Top Store Hold Amount
     */
    public function toggleSync7DaysFilter()
    {
        $this->showOnlySync7Days = !$this->showOnlySync7Days;

        // Clear cache để force refresh data
        self::clearCache();
    }

    /**
     * Lấy top stores theo pending refund amount
     */
    private function getTopStoresByRefund(): array
    {
        $topStores = Store::with('owner')
            ->where('pending_refund_amount', '>', 0)
            ->orderByRaw('CAST(pending_refund_amount AS DECIMAL(15,2)) DESC')
            ->limit(100)
            ->get();

        return $topStores->map(function ($store) {
            return [
                'id' => $store->id,
                'name' => $store->name,
                'owner_name' => $store->owner ? $store->owner->name : 'N/A',
                'pending_refund' => (float) $store->pending_refund_amount,
            ];
        })->toArray();
    }

    /**
     * Phân loại stores theo số ngày payout
     */
    private function getPayoutDayBreakdown(): array
    {
        $payoutDayGroups = Store::whereNotNull('tiktok_payout_day')
            ->where('tiktok_payout_day', '>', 0)
            ->selectRaw('tiktok_payout_day, COUNT(*) as store_count')
            ->groupBy('tiktok_payout_day')
            ->orderBy('tiktok_payout_day')
            ->get();

        // Tính số stores có payout day sync > 7 ngày
        $storesPayoutSyncOver7Days = Store::whereNotNull('tiktok_payout_day')
            ->where('tiktok_payout_day', '>', 7)
            ->where(function($query) {
                $query->whereNull('last_sync_tiktok')
                      ->orWhere('last_sync_tiktok', '<', now()->subDays(7));
            })
            ->count();

        // Phân loại stores theo số ngày payout nhưng chỉ tính stores đã update trong 7 ngày
        $payoutDayGroupsOver7Day = Store::whereNotNull('tiktok_payout_day')
            ->where('tiktok_payout_day', '>', 0)
            ->whereNotNull('last_sync_tiktok')
            ->where('last_sync_tiktok', '>=', now()->subDays(7))
            ->selectRaw('tiktok_payout_day, COUNT(*) as store_count')
            ->groupBy('tiktok_payout_day')
            ->orderBy('tiktok_payout_day')
            ->get();

        // Thêm thống kê tổng số order của stores sync trong 7 ngày, phân loại theo ngày payout
        // Chỉ tính orders trong 60 ngày gần đây - Sử dụng subquery để tính chính xác
        $orderCountSubquery = Order::selectRaw('store_id, COUNT(*) as order_count')
            ->where('created_at', '>=', now()->subDays(60))
            ->groupBy('store_id');

        $payoutDayOrderStats = Store::whereNotNull('tiktok_payout_day')
            ->where('tiktok_payout_day', '>', 0)
            ->whereNotNull('last_sync_tiktok')
            ->where('last_sync_tiktok', '>=', now()->subDays(7))
            ->leftJoinSub($orderCountSubquery, 'order_counts', function($join) {
                $join->on('stores.id', '=', 'order_counts.store_id');
            })
            ->selectRaw('
                stores.tiktok_payout_day,
                COUNT(stores.id) as store_count,
                COALESCE(SUM(order_counts.order_count), 0) as total_orders
            ')
            ->groupBy('stores.tiktok_payout_day')
            ->orderBy('stores.tiktok_payout_day')
            ->get();

        $breakdown = $payoutDayGroups->map(function ($group) {
            return [
                'payout_day' => (int) $group->tiktok_payout_day,
                'store_count' => $group->store_count,
            ];
        })->toArray();

        $breakdownOver7Day = $payoutDayGroupsOver7Day->map(function ($group) {
            return [
                'payout_day' => (int) $group->tiktok_payout_day,
                'store_count' => $group->store_count,
            ];
        })->toArray();

        // Tạo breakdown với thông tin order
        $breakdownWithOrders = $payoutDayOrderStats->map(function ($group) {
            return [
                'payout_day' => (int) $group->tiktok_payout_day,
                'store_count' => $group->store_count,
                'total_orders' => $group->total_orders,
            ];
        })->toArray();

        return [
            'breakdown' => $breakdown,
            'storesPayoutSyncOver7Days' => $storesPayoutSyncOver7Days,
            'payoutDayGroupsOver7Day' => $breakdownOver7Day,
            'payoutDayOrderStats' => $breakdownWithOrders, // Thêm thống kê order
        ];
    }

    /**
     * Tính toán thống kê settlement từ các trường trong stores table
     */
    private function calculateSettlementStats(): array
    {
        // Tính tổng thực tế từ tất cả stores có dữ liệu settlement
        $pendingConfirmation = Store::whereNotNull('pending_delivery_amount')
            ->where('pending_delivery_amount', '>', 0)
            ->sum('pending_delivery_amount');

        $pendingRefund = Store::whereNotNull('pending_refund_amount')
            ->where('pending_refund_amount', '>', 0)
            ->sum('pending_refund_amount');

        $pendingPayment = Store::whereNotNull('pending_settlement_amount')
            ->where('pending_settlement_amount', '>', 0)
            ->sum('pending_settlement_amount');

        return [
            'pendingConfirmation' => (float) $pendingConfirmation,
            'pendingRefund' => (float) $pendingRefund,
            'pendingPayment' => (float) $pendingPayment,
        ];
    }

    /**
     * Xóa cache của widget (chỉ cho super_admin)
     */
    public static function clearCache(): void
    {
        Cache::forget('store_stats_overview_widget_super_admin');
    }

    /**
     * Tính toán thống kê TikTok Payments 30 ngày gần đây
     */
    private function calculateTikTokPaymentStats(): array
    {
        $thirtyDaysAgo = Carbon::now()->subDays(30);

        // Thống kê tổng quan TikTok Payments 30 ngày gần đây
        $paymentStats = TikTokPayment::where('paid_time', '>=', $thirtyDaysAgo)
            ->where('status', 'PAID')
            ->selectRaw('
                COUNT(*) as total_payments,
                COUNT(DISTINCT store_id) as stores_with_payments,
                SUM(settlement_amount) as total_amount,
                AVG(settlement_amount) as avg_amount
            ')
            ->first();

        return [
            'total_payments' => $paymentStats->total_payments ?? 0,
            'stores_with_payments' => $paymentStats->stores_with_payments ?? 0,
            'total_amount' => (float) ($paymentStats->total_amount ?? 0),
            'avg_amount' => (float) ($paymentStats->avg_amount ?? 0),
        ];
    }

    /**
     * Kiểm tra quyền xem widget
     */
    public static function canView(): bool
    {
        $user = auth()->user();
        return $user && ($user->hasRole('super_admin') || $user->hasRole('Leader') || $user->hasRole('Seller'));
    }
}
