<?php

namespace App\Filament\App\Widgets;

use App\Models\Idea;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Log;

class IdeaStatisticsWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.idea-statistics-widget';

    public $stats;

    protected int | string | array $columnSpan = [
        'default' => 1,
        'md' => 1,
        'lg' => 1,
        'xl' => 1,
    ];

    public function mount()
    {
        $ideas = Idea::where('status', 'completed')->get();
        $totalIdeas = $ideas->count();
        $soldIdeas = $ideas->where('has_sold', true)->count();

        $this->stats = [
            'overall' => [
                'total' => $totalIdeas,
                'sold' => $soldIdeas,
                'rate' => $totalIdeas > 0 ? ($soldIdeas / $totalIdeas) * 100 : 0
            ],
            'top_sources' => $this->getTopPerformers($ideas, 'source', 3),
            'top_tags' => $this->getTopPerformers($ideas, 'tags', 3),
            'top_niches' => $this->getTopPerformers($ideas, 'niche', 3)
        ];
    }

    private function getTopPerformers($ideas, $field, $limit)
    {
        $data = [];
        if ($field === 'tags' || $field === 'niche') {
            $items = $ideas->pluck($field)->flatten()->countBy();
        } else {
            $items = $ideas->groupBy($field)->map->count();
        }

        foreach ($items as $key => $total) {
            if (empty($key)) continue;
            
            $sold = $field === 'tags' || $field === 'niche' 
                ? $ideas->where('has_sold', true)->filter(fn($idea) => in_array($key, $idea[$field]))->count()
                : $ideas->where('has_sold', true)->where($field, $key)->count();
            
            $data[$key] = [
                'total' => $total,
                'sold' => $sold,
                'rate' => $total > 0 ? ($sold / $total) * 100 : 0
            ];
        }

        return collect($data)
            ->sortByDesc('rate')
            ->take($limit)
            ->toArray();
    }
}