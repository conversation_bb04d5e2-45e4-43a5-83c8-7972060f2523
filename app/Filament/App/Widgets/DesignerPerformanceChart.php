<?php

namespace App\Filament\App\Widgets;

use App\Models\DesignJob;
use App\Models\User;
use App\Enums\DesignJobStatus;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class DesignerPerformanceChart extends ChartWidget
{
    protected static ?string $heading = 'Designer Performance';
    protected static ?int $sort = 2;
    
    protected function getData(): array
    {
        // Get top 10 designers by total jobs
        $designers = User::role('Designer')
            ->select([
                'users.id',
                'users.name',
                DB::raw('COUNT(design_jobs.id) as total_jobs'),
                DB::raw('SUM(design_jobs.price) as total_earnings'),
                DB::raw('COUNT(CASE WHEN design_jobs.status = "completed" THEN 1 END) as completed_jobs'),
            ])
            ->leftJoin('design_jobs', 'users.id', '=', 'design_jobs.designer_id')
            ->groupBy('users.id', 'users.name')
            ->orderBy('total_jobs', 'desc')
            ->limit(10)
            ->get();
            
        $labels = $designers->pluck('name')->toArray();
        $jobsData = $designers->pluck('total_jobs')->toArray();
        $earningsData = $designers->pluck('total_earnings')->toArray();
        $completedJobsData = $designers->pluck('completed_jobs')->toArray();
        
        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Total Jobs',
                    'data' => $jobsData,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.5)',
                    'borderColor' => 'rgb(59, 130, 246)',
                ],
                [
                    'label' => 'Completed Jobs',
                    'data' => $completedJobsData,
                    'backgroundColor' => 'rgba(16, 185, 129, 0.5)',
                    'borderColor' => 'rgb(16, 185, 129)',
                ],
            ],
        ];
    }
    
    protected function getType(): string
    {
        return 'bar';
    }
} 