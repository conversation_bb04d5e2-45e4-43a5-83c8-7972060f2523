<?php

namespace App\Filament\App\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

use App\Models\OrderItem;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
class TopSellingProductsWidget extends BaseWidget
{
    public $stats = [];

    protected function getStats(): array
    {
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        $topProducts = OrderItem::join('products', 'order_items.product_id', '=', 'products.id')
        ->selectRaw('products.name, SUM(order_items.quantity) as total_sold')
        ->whereBetween('order_items.created_at', [$startDate, $endDate])
        ->groupBy('order_items.product_id', 'products.name')
        ->orderByDesc('total_sold')
        ->limit(20)
        ->get();

        $this->stats  = [];
        $this->stats = $topProducts->map(function ($product, $index) {
            return [
                'label' => "Top " . ($index + 1) . ": " . $product->name,
                'value' => $product->total_sold . ' units',
                'description' => 'Sold in the last 30 days'
            ];
        })->all();
  
        return $this->stats;
    }
    public function render(): \Illuminate\View\View
    {
        $this->getStats(); 
        return view('filament.widgets.top-selling-products', [
            'stats' => $this->stats
        ]);
    }
}
