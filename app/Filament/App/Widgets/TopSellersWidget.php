<?php

namespace App\Filament\App\Widgets;

use App\Models\Order;
use App\Models\User;
use App\Models\Store;
use App\Models\SupplierOrder;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\HtmlString;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class TopSellersWidget extends Widget
{
    protected static ?int $sort = 5;
    protected static string $view = 'filament.app.widgets.top-sellers-widget';

    protected int | string | array $columnSpan = [
        'default' => 2,  // Mobile: chiếm 2 cột (full width)
        'sm' => 2,       // Small: chiếm 2 cột (full width)
        'md' => 2,       // Medium: chiếm 2 cột (full width)
        'lg' => 4,       // Large: chiếm 4 cột (full width)
        'xl' => 4,       // XL: chiếm 4 cột (full width)
    ];

    public function getHeading() {
        return 'Top Sellers';
    }

    /**
     * Lấy dữ liệu cho widget với cache nửa ngày
     */
    public function getViewData(): array
    {
        return Cache::remember('top_sellers_widget', 15 * 60, function () {
            return [
                'todayData' => $this->getTopSellersData('today'),
                'yesterdayData' => $this->getTopSellersData('yesterday'),
                'thisWeekData' => $this->getTopSellersData('this_week'),
                'thisMonthData' => $this->getTopSellersData('this_month'),
                'lastMonthData' => $this->getTopSellersData('last_month'),
            ];
        });
    }

    /**
     * Lấy dữ liệu top sellers cho một khoảng thời gian
     */
    private function getTopSellersData(string $period): array
    {
        $dateRange = $this->getDateRangeForPeriod($period);

        // Subquery để tính order count và revenue
        $orderStats = Order::selectRaw('seller_id, COUNT(*) as order_count, SUM(total) as total_revenue')
            ->whereBetween('created_at', $dateRange)
            ->groupBy('seller_id');

        // Subquery để tính fulfillment count và amount
        $fulfillmentStats = SupplierOrder::selectRaw('seller_id, COUNT(*) as fulfilled_count, SUM(base_cost) as fulfilled_amount')
            ->where('status', '!=', 'Cancelled')
            ->whereBetween('created_at', $dateRange)
            ->groupBy('seller_id');

        // Tất cả 3 bảng đều chỉ lấy sellers có đơn hàng
        $sellers = User::query()
            ->whereHas('roles', function ($query) {
                $query->whereIn('name', ['Seller']);
            })
            ->joinSub($orderStats, 'order_stats', function ($join) {
                $join->on('users.id', '=', 'order_stats.seller_id');
            })
            ->leftJoinSub($fulfillmentStats, 'fulfillment_stats', function ($join) {
                $join->on('users.id', '=', 'fulfillment_stats.seller_id');
            })
            ->select([
                'users.id',
                'users.name',
                'order_stats.order_count',
                'order_stats.total_revenue',
                DB::raw('COALESCE(fulfillment_stats.fulfilled_count, 0) as fulfilled_count'),
                DB::raw('COALESCE(fulfillment_stats.fulfilled_amount, 0) as fulfilled_amount')
            ])
            ->orderBy('total_revenue', 'desc')
            ->orderBy('order_count', 'desc')
            ->orderBy('name', 'asc')
            ->get();

        return $sellers->map(function ($seller, $index) {
            return [
                'rank' => $index + 1,
                'name' => $seller->name,
                'order_count' => $seller->order_count,
                'total_revenue' => $seller->total_revenue,
                'fulfilled_count' => $seller->fulfilled_count,
                'fulfilled_amount' => $seller->fulfilled_amount,
            ];
        })->toArray();
    }

    private function getDateRangeForPeriod(string $period): array
    {
        $now = Carbon::now();
        return match ($period) {
            'today' => [$now->copy()->startOfDay(), $now->copy()->endOfDay()],
            'yesterday' => [$now->copy()->subDay()->startOfDay(), $now->copy()->subDay()->endOfDay()],
            'this_week' => [$now->copy()->subDays(7), $now->copy()],
            'this_month' => [$now->copy()->startOfMonth(), $now->copy()->endOfMonth()],
            'last_month' => [$now->copy()->subMonth()->startOfMonth(), $now->copy()->subMonth()->endOfMonth()],
            default => [$now->copy()->startOfMonth(), $now->copy()->endOfMonth()],
        };
    }

    /**
     * Xóa cache của widget
     */
    public static function clearCache(): void
    {
        Cache::forget('top_sellers_widget');
    }

    /**
     * Kiểm tra quyền xem widget
     */
    public static function canView(): bool
    {
        $user = auth()->user();
        return $user && ($user->hasRole('super_admin') || $user->hasRole('User Manager'));
    }

 
}
