<?php

namespace App\Filament\App\Widgets;

use App\Models\Order;
use App\Models\Store;
use App\Enums\OrderStatus;
use App\Enums\TiktokShopStatus;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class OrderStatusStats extends Widget
{
    protected static string $view = 'filament.app.widgets.order-status-stats-widget';

    protected static ?string $pollingInterval = '30s';
    protected static ?int $sort = 4;

    protected int | string | array $columnSpan = [
        'default' => 2,  // Mobile: chiếm 2 cột (full width)
        'sm' => 2,       // Small: chiếm 2 cột (full width)
        'md' => 2,       // Medium: chiếm 2 cột (full width)
        'lg' => 3,       // Large: chiếm 3 cột (full width)
        'xl' => 4,       // XL: chiếm 4 cột (full width)
    ];

    /**
     * <PERSON><PERSON><PERSON> dữ liệu cho widget với caching
     */
    public function getViewData(): array
    {
        return cache()->remember(
            'order_status_stats_' . auth()->id(),
            now()->addMinutes(5),
            fn() => [
                'orderStatusData' => $this->calculateOrderStatusStats(),
                'storeStatsData' => $this->calculateStoreStats(),
            ]
        );
    }

    /**
     * Tính toán các thống kê trạng thái đơn hàng
     */
    private function calculateOrderStatusStats(): array
    {
        $user = Auth::user();
        $query = Order::query();

        if (!$user->hasRole('super_admin')) {
            $query->whereHas('store', function ($q) use ($user) {
                $q->where('owner_id', $user->id);
            });
        }

        $totalOrders = $query->count();

        $statusCounts = $query->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $statusStats = [];

        // Thống kê theo từng trạng thái
        foreach (OrderStatus::cases() as $status) {
            $count = $statusCounts[$status->value] ?? 0;
            $percentage = $totalOrders > 0 ? round(($count / $totalOrders) * 100, 1) : 0;

            $statusStats[] = [
                'label' => $status->getLabel(),
                'count' => $count,
                'percentage' => $percentage,
                'icon' => $status->getIcon(),
                'color' => $status->getColor(),
            ];
        }

        return [
            'totalOrders' => $totalOrders,
            'statusStats' => $statusStats,
        ];
    }

    /**
     * Tính toán các thống kê store
     */
    private function calculateStoreStats(): array
    {
        $totalStores = Store::count();
        $liveStores = Store::where('tiktok_shop_status', TiktokShopStatus::Live)->count();
        $storesWithBank = Store::whereNotNull('bank_account')
            ->where('bank_account', '!=', '')
            ->count();
        $activeProductStores = Store::whereNotNull('last_created_tiktok_product')
            ->where('last_created_tiktok_product', '>=', Carbon::now()->subDays(7))
            ->count();

        // Tính số store có đơn hàng nhưng chưa có bank
        $storesWithOrdersNoBank = Store::whereHas('orders')
            ->where(function($query) {
                $query->whereNull('bank_account')
                      ->orWhere('bank_account', '');
            })
            ->where(function($query) {
                $query->whereNull('card')
                      ->orWhere('card', '');
            })
            ->count();

        return [
            'totalStores' => $totalStores,
            'liveStores' => $liveStores,
            'storesWithBank' => $storesWithBank,
            'storesWithOrdersNoBank' => $storesWithOrdersNoBank,
            'activeProductStores' => $activeProductStores,
        ];
    }

    /**
     * Kiểm tra quyền xem widget
     */
    public static function canView(): bool
    {
        $user = auth()->user();
        return $user && ($user->hasRole('super_admin') || $user->hasRole('Leader') || $user->hasRole('Seller'));
    }

    /**
     * Lấy heading cho widget
     */
    protected function getHeading(): ?string
    {
        return 'Thống kê Trạng thái Đơn hàng';
    }

    /**
     * Lấy description cho widget
     */
    protected function getDescription(): ?string
    {
        return 'Phân bố trạng thái của các đơn hàng';
    }
}