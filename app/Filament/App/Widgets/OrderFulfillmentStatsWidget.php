<?php

namespace App\Filament\App\Widgets;

use Filament\Widgets\Widget;
use App\Models\Order;
use App\Models\SupplierOrder;
use App\Enums\OrderStatus;
use App\Enums\SupplierOrderStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class OrderFulfillmentStatsWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.order-fulfillment-stats-widget';
    
    protected static ?string $pollingInterval = null;
    protected static ?int $sort = 2;
    
    protected int | string | array $columnSpan = [
        'default' => 2,  // Mobile: chiếm 2 cột (full width)
        'sm' => 2,       // Small: chiếm 2 cột (full width)
        'md' => 3,       // Medium: chiếm 3 cột
        'lg' => 4,       // Large: chiếm 4 cột (full width)
        'xl' => 'full',  // XL: chiếm full width để chứa 5 cột + trạng thái đơn hàng
    ];

    /**
     * Lấy dữ liệu cho widget với cache có điều kiện (chỉ cache cho super_admin)
     */
    public function getViewData(): array
    {
        $user = auth()->user();
        $isSuperAdmin = $user && $user->hasRole('super_admin');

        // Chỉ cache khi user là super_admin
        if ($isSuperAdmin) {
            return Cache::remember('order_fulfillment_stats_widget_super_admin', 15 * 60, function () {
                return $this->calculateWidgetData();
            });
        }

        // Không cache cho user khác
        return $this->calculateWidgetData();
    }

    /**
     * Tính toán dữ liệu widget
     */
    private function calculateWidgetData(): array
    {
        // Tính toán ngày tháng một lần duy nhất
        $dateRanges = $this->getDateRanges();

        // Lấy dữ liệu một lần và tái sử dụng
        $orderStats = $this->calculateOrderStatsOptimized($dateRanges);
        $fulfillmentStats = $this->calculateFulfillmentStatsOptimized($dateRanges);

        return [
            'orderStats' => $orderStats,
            'fulfillmentStats' => $fulfillmentStats,
            'combinedMetrics' => $this->calculateCombinedMetricsOptimized($orderStats, $fulfillmentStats),
            'orderStatusStats' => $this->calculateOrderStatusStatsOptimized(),
        ];
    }

    /**
     * Tính toán các khoảng thời gian một lần duy nhất
     */
    private function getDateRanges(): array
    {
        return [
            'today' => Carbon::today(),
            'yesterday' => Carbon::yesterday(),
            'thisWeek' => Carbon::now()->subDays(7),
            'thisMonth' => Carbon::now()->startOfMonth(),
            'lastWeekStart' => Carbon::now()->subWeek()->startOfWeek(),
            'lastWeekEnd' => Carbon::now()->subWeek()->endOfWeek(),
            'lastMonthStart' => Carbon::now()->subMonth()->startOfMonth(),
            'lastMonthEnd' => Carbon::now()->subMonth()->endOfMonth(),
        ];
    }

    /**
     * Tính toán thống kê đơn hàng tối ưu - sử dụng onlyMe() như cũ và loại bỏ đơn hàng bị Cancel
     */
    private function calculateOrderStatsOptimized(array $dateRanges): array
    {
        // Sử dụng onlyMe() như logic cũ và loại bỏ đơn hàng bị Cancel
        return [
            'today' => [
                'orders' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->whereDate('created_at', $dateRanges['today'])
                    ->count(),
                'revenue' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->whereDate('created_at', $dateRanges['today'])
                    ->sum('total') ?? 0,
            ],
            'yesterday' => [
                'orders' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->whereDate('created_at', $dateRanges['yesterday'])
                    ->count(),
                'revenue' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->whereDate('created_at', $dateRanges['yesterday'])
                    ->sum('total') ?? 0,
            ],
            'thisWeek' => [
                'orders' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->where('created_at', '>=', $dateRanges['thisWeek'])
                    ->count(),
                'revenue' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->where('created_at', '>=', $dateRanges['thisWeek'])
                    ->sum('total') ?? 0,
            ],
            'lastWeek' => [
                'orders' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->whereBetween('created_at', [$dateRanges['lastWeekStart'], $dateRanges['lastWeekEnd']])
                    ->count(),
                'revenue' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->whereBetween('created_at', [$dateRanges['lastWeekStart'], $dateRanges['lastWeekEnd']])
                    ->sum('total') ?? 0,
            ],
            'thisMonth' => [
                'orders' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->where('created_at', '>=', $dateRanges['thisMonth'])
                    ->count(),
                'revenue' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->where('created_at', '>=', $dateRanges['thisMonth'])
                    ->sum('total') ?? 0,
            ],
            'lastMonth' => [
                'orders' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->whereBetween('created_at', [$dateRanges['lastMonthStart'], $dateRanges['lastMonthEnd']])
                    ->count(),
                'revenue' => Order::onlyMe()
                    ->where('status', '!=', OrderStatus::Cancelled->value)
                    ->whereBetween('created_at', [$dateRanges['lastMonthStart'], $dateRanges['lastMonthEnd']])
                    ->sum('total') ?? 0,
            ],
        ];
    }

    /**
     * Tính toán thống kê fulfillment tối ưu - sử dụng dateRanges và loại bỏ đơn hàng bị Cancel
     */
    private function calculateFulfillmentStatsOptimized(array $dateRanges): array
    {
        return [
            'today' => [
                'orders' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->whereDate('created_at', $dateRanges['today'])
                    ->count(),
                'cost' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->whereDate('created_at', $dateRanges['today'])
                    ->sum('base_cost') ?? 0,
            ],
            'yesterday' => [
                'orders' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->whereDate('created_at', $dateRanges['yesterday'])
                    ->count(),
                'cost' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->whereDate('created_at', $dateRanges['yesterday'])
                    ->sum('base_cost') ?? 0,
            ],
            'thisWeek' => [
                'orders' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->where('created_at', '>=', $dateRanges['thisWeek'])
                    ->count(),
                'cost' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->where('created_at', '>=', $dateRanges['thisWeek'])
                    ->sum('base_cost') ?? 0,
            ],
            'lastWeek' => [
                'orders' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->whereBetween('created_at', [$dateRanges['lastWeekStart'], $dateRanges['lastWeekEnd']])
                    ->count(),
                'cost' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->whereBetween('created_at', [$dateRanges['lastWeekStart'], $dateRanges['lastWeekEnd']])
                    ->sum('base_cost') ?? 0,
            ],
            'thisMonth' => [
                'orders' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->where('created_at', '>=', $dateRanges['thisMonth'])
                    ->count(),
                'cost' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->where('created_at', '>=', $dateRanges['thisMonth'])
                    ->sum('base_cost') ?? 0,
            ],
            'lastMonth' => [
                'orders' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->whereBetween('created_at', [$dateRanges['lastMonthStart'], $dateRanges['lastMonthEnd']])
                    ->count(),
                'cost' => SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value)
                    ->whereBetween('created_at', [$dateRanges['lastMonthStart'], $dateRanges['lastMonthEnd']])
                    ->sum('base_cost') ?? 0,
            ],
        ];
    }

    /**
     * Tính toán combined metrics tối ưu - sử dụng dữ liệu đã có
     */
    private function calculateCombinedMetricsOptimized(array $orderStats, array $fulfillmentStats): array
    {
        $result = [];

        foreach (['today', 'yesterday', 'thisWeek', 'thisMonth', 'lastMonth'] as $period) {
            $revenue = $orderStats[$period]['revenue'] ?? 0;
            $cost = $fulfillmentStats[$period]['cost'] ?? 0;
            $orders = $orderStats[$period]['orders'] ?? 0;
            $fulfillments = $fulfillmentStats[$period]['orders'] ?? 0;

            $result[$period] = [
                'profit_margin' => $this->calculateProfitMargin($revenue, $cost),
                'fulfillment_rate' => $this->calculateFulfillmentRate($orders, $fulfillments),
            ];
        }

        return $result;
    }

    /**
     * Tính tỷ lệ lợi nhuận
     */
    private function calculateProfitMargin(float $revenue, float $cost): float
    {
        if ($revenue <= 0) return 0;
        return (($revenue - $cost) / $revenue) * 100;
    }

    /**
     * Tính tỷ lệ fulfillment
     */
    private function calculateFulfillmentRate(int $orders, int $fulfillments): float
    {
        if ($orders <= 0) return 0;
        return ($fulfillments / $orders) * 100;
    }



    /**
     * Kiểm tra quyền xem widget
     */
    public static function canView(): bool
    {
        $user = auth()->user();
        return $user && ($user->hasRole('super_admin') || $user->hasRole('Leader') || $user->hasRole('Seller') || $user->hasRole('Fullfillment Manager'));
    }

    /**
     * Tính toán order status stats tối ưu - sử dụng logic cũ và loại bỏ đơn hàng bị Cancel khỏi tổng số
     */
    private function calculateOrderStatusStatsOptimized(): array
    {
        $user = auth()->user();
        $query = Order::query();

        if (!$user->hasRole('super_admin')) {
            $query->whereHas('store', function ($q) use ($user) {
                $q->where('owner_id', $user->id);
            });
        }

        // Tổng số đơn hàng (loại bỏ đơn bị Cancel khỏi tổng số)
        $totalOrders = $query->where('status', '!=', OrderStatus::Cancelled->value)->count();
        $totalRevenue = $query->where('status', '!=', OrderStatus::Cancelled->value)->sum('total') ?? 0;

        // Thống kê fulfillment (loại bỏ đơn bị Cancel khỏi tổng số)
        $fulfillmentQuery = SupplierOrder::where('status', '!=', SupplierOrderStatus::Cancelled->value);
        $totalFulfillments = $fulfillmentQuery->count();
        $totalFulfillmentCost = $fulfillmentQuery->sum('base_cost') ?? 0;

        // Lấy thống kê theo trạng thái (bao gồm cả Cancel để hiển thị)
        $statusCounts = $query->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $statusStats = [];

        // Thống kê theo từng trạng thái (hiển thị tất cả trạng thái kể cả Cancel)
        foreach (OrderStatus::cases() as $orderStatus) {
            $count = $statusCounts[$orderStatus->value] ?? 0;

            $statusStats[] = [
                'label' => $orderStatus->getLabel(),
                'count' => $count,
                'icon' => $orderStatus->getIcon(),
                'color' => $orderStatus->getColor(),
            ];
        }

        return [
            'totalOrders' => $totalOrders,
            'totalRevenue' => $totalRevenue,
            'averageOrderValue' => $totalOrders > 0 ? $totalRevenue / $totalOrders : 0,
            'totalFulfillments' => $totalFulfillments,
            'totalFulfillmentCost' => $totalFulfillmentCost,
            'averageFulfillmentCost' => $totalFulfillments > 0 ? $totalFulfillmentCost / $totalFulfillments : 0,
            'overallProfitMargin' => $totalRevenue > 0 ? (($totalRevenue - $totalFulfillmentCost) / $totalRevenue) * 100 : 0,
            'statusStats' => $statusStats,
        ];
    }

    /**
     * Xóa cache của widget (chỉ cho super_admin)
     */
    public static function clearCache(): void
    {
        Cache::forget('order_fulfillment_stats_widget_super_admin');
    }

    /**
     * Lấy heading cho widget
     */
    protected function getHeading(): ?string
    {
        return 'Thống kê Đơn hàng & Fulfillment';
    }
}
