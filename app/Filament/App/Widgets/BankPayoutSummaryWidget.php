<?php

namespace App\Filament\App\Widgets;

use App\Models\SupplierOrder;
use App\Models\Store;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class BankPayoutSummaryWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.bank-payout-summary-widget';
    
    protected static ?int $sort = 2;
    
    protected int | string | array $columnSpan = [
        'default' => 2,  // Mobile: chiếm 2 cột (full width)
        'sm' => 2,       // Small: chiếm 2 cột (full width)
        'md' => 2,       // Medium: chiếm 2 cột
        'lg' => 2,       // Large: chiếm 2 cột (chỉ 2 cards)
        'xl' => 2,       // XL: chiếm 2 cột (chỉ 2 cards)
    ];

    /**
     * Lấy dữ liệu cho widget với cache có điều kiện (chỉ cache cho super_admin)
     */
    public function getViewData(): array
    {
        $user = auth()->user();
        $isSuperAdmin = $user && $user->hasRole('super_admin');
        
        // Chỉ cache khi user là super_admin
        if ($isSuperAdmin) {
            return Cache::remember('bank_payout_summary_6m_widget_super_admin', 2 * 60 * 60, function () {
                return $this->calculateSummaryData();
            });
        }
        
        // Không cache cho user khác
        return $this->calculateSummaryData();
    }

    /**
     * Tính toán dữ liệu tóm tắt
     */
    private function calculateSummaryData(): array
    {
        $chartData = $this->getChartDataFor6Months();
        
        // Tính toán dự đoán cho tháng hiện tại
        $prediction = $this->calculateCurrentMonthPrediction($chartData);

        // Tính toán dữ liệu 3 tháng gần đây
        $last3Months = $this->calculateLast3MonthsData($chartData);

        return [
            'totalFulfillCost' => array_sum($chartData['fulfill']),
            'totalBankPayout' => array_sum($chartData['bankPayout']),
            'totalProfit' => array_sum($chartData['profit']),
            'monthlyData' => $chartData,
            'prediction' => $prediction,
            'last3Months' => $last3Months,
        ];
    }

    /**
     * Lấy dữ liệu biểu đồ cho 6 tháng gần đây
     */
    private function getChartDataFor6Months(): array
    {
        $now = Carbon::now();
        $months = [];
        $fulfillData = [];
        $bankPayoutData = [];
        $profitData = [];

        // Lấy dữ liệu cho 6 tháng: tháng hiện tại + 5 tháng trước
        // Index 0 = tháng hiện tại, Index 1-5 = 5 tháng trước
        for ($i = 5; $i >= 0; $i--) {
            $monthStart = $now->copy()->subMonths($i)->startOfMonth();
            $monthEnd = $now->copy()->subMonths($i)->endOfMonth();

            // Tên tháng định dạng MM/YYYY
            $months[] = $monthStart->format('m/Y');

            // Tính fulfill amount
            $fulfillAmount = SupplierOrder::where('status', '!=', 'Cancelled')
                ->whereBetween('created_at', [$monthStart, $monthEnd])
                ->sum('base_cost') ?? 0;

            // Tính bank payout amount - Dùng trường 'time' thay vì 'created_at'
            $bankPayoutAmount = DB::table('payout_transactions')
                ->join('stores', function($join) {
                    $join->on('stores.bank_account', '=', 'payout_transactions.card_no')
                         ->whereNotNull('stores.bank_account')
                         ->where('stores.bank_account', '!=', '');
                })
                ->where('payout_transactions.type', 'Receive')
                ->where('payout_transactions.status', 'Success')
                ->whereBetween('payout_transactions.time', [$monthStart, $monthEnd])
                ->sum('payout_transactions.amount') ?? 0;

            $fulfillData[] = round($fulfillAmount, 2);
            $bankPayoutData[] = round($bankPayoutAmount, 2);
            $profitData[] = round($bankPayoutAmount - $fulfillAmount, 2);
        }

        return [
            'months' => $months,
            'fulfill' => $fulfillData,
            'bankPayout' => $bankPayoutData,
            'profit' => $profitData,
        ];
    }

    /**
     * Tính toán dự đoán cho tháng hiện tại dựa trên tỷ lệ tăng trưởng
     */
    private function calculateCurrentMonthPrediction(array $chartData): array
    {
        // Lấy 3 tháng gần đây (index 2, 3, 4 - loại trừ tháng hiện tại index 5)
        // Ví dụ: Nếu tháng hiện tại là tháng 7, lấy dữ liệu tháng 4, 5, 6
        $last3MonthsFulfill = array_slice($chartData['fulfill'], 2, 3);
        $last3MonthsBankPayout = array_slice($chartData['bankPayout'], 2, 3);

        // Tính tỷ lệ tăng trưởng fulfill cost
        $fulfillGrowthRate = $this->calculateGrowthRate($last3MonthsFulfill);

        // Tính tỷ lệ tăng trưởng bank payout
        $bankPayoutGrowthRate = $this->calculateGrowthRate($last3MonthsBankPayout);

        // Lấy giá trị tháng gần nhất (tháng trước)
        $lastMonthFulfill = $chartData['fulfill'][4] ?? 0; // Tháng trước (index 4)
        $lastMonthBankPayout = $chartData['bankPayout'][4] ?? 0; // Tháng trước (index 4)

        // Dự đoán tháng này dựa trên tỷ lệ tăng trưởng
        $predictedFulfillCost = $lastMonthFulfill * (1 + $fulfillGrowthRate / 100);
        $predictedBankPayout = $lastMonthBankPayout * (1 + $bankPayoutGrowthRate / 100);

        // Ước tính profit
        $predictedProfit = $predictedBankPayout - $predictedFulfillCost;

        // Lấy fulfill cost hiện tại (nếu có)
        $currentMonthFulfill = $chartData['fulfill'][5] ?? 0; // Tháng hiện tại (index 5)

        // Tính tổng fulfill tháng trước từ database (loại trừ cancelled)
        $now = Carbon::now();
        $lastMonthStart = $now->copy()->subMonth()->startOfMonth();
        $lastMonthEnd = $now->copy()->subMonth()->endOfMonth();

        $lastMonthFulfillFromDB = SupplierOrder::where('status', '!=', 'Cancelled')
            ->whereBetween('created_at', [$lastMonthStart, $lastMonthEnd])
            ->sum('base_cost') ?? 0;

        return [
            'currentMonthFulfill' => $currentMonthFulfill,
            'predictedFulfillCost' => $predictedFulfillCost,
            'predictedBankPayout' => $predictedBankPayout,
            'predictedProfit' => $predictedProfit,
            'fulfillGrowthRate' => $fulfillGrowthRate,
            'bankPayoutGrowthRate' => $bankPayoutGrowthRate,
            'lastMonthFulfill' => $lastMonthFulfill,
            'lastMonthBankPayout' => $lastMonthBankPayout,
            'lastMonthFulfillFromDB' => $lastMonthFulfillFromDB,
            'lastMonthName' => $lastMonthStart->format('m/Y'),
            'basedOnMonths' => 3,
        ];
    }

    /**
     * Tính tỷ lệ tăng trưởng trung bình từ mảng dữ liệu
     * Bỏ qua các tháng có dữ liệu bất thường (quá thấp so với tháng trước)
     */
    private function calculateGrowthRate(array $data): float
    {
        if (count($data) < 2) {
            return 0;
        }

        $growthRates = [];

        // Tính tỷ lệ tăng trưởng giữa các tháng liên tiếp
        for ($i = 1; $i < count($data); $i++) {
            $previous = $data[$i - 1];
            $current = $data[$i];

            if ($previous > 0) {
                $growthRate = (($current - $previous) / $previous) * 100;

                // Bỏ qua tỷ lệ tăng/giảm quá mạnh để tránh outliers
                // Chỉ chấp nhận tỷ lệ từ -80% đến +200%
                if ($growthRate > -80 && $growthRate < 200) {
                    $growthRates[] = $growthRate;
                }
            }
        }

        // Nếu không có dữ liệu hợp lệ, dùng tỷ lệ tăng trưởng từ 2 tháng đầu
        if (count($growthRates) == 0 && count($data) >= 2) {
            $first = $data[0];
            $second = $data[1];
            if ($first > 0) {
                return (($second - $first) / $first) * 100;
            }
        }

        // Trả về tỷ lệ tăng trưởng trung bình
        return count($growthRates) > 0 ? array_sum($growthRates) / count($growthRates) : 0;
    }

    /**
     * Tính toán dữ liệu 3 tháng gần đây (loại trừ tháng hiện tại)
     */
    private function calculateLast3MonthsData(array $chartData): array
    {
        // Lấy 3 tháng gần đây (index 1, 2, 3 - loại trừ tháng hiện tại index 0)
        // Ví dụ: Tháng hiện tại = 7, lấy tháng 4, 5, 6
        // chartData được sắp xếp từ cũ đến mới: [02, 03, 04, 05, 06, 07]
        // Index 0=02, 1=03, 2=04, 3=05, 4=06, 5=07
        // Cần lấy index 2, 3, 4 (tháng 4, 5, 6)
        $last3MonthsFulfill = array_slice($chartData['fulfill'], 2, 3);
        $last3MonthsBankPayout = array_slice($chartData['bankPayout'], 2, 3);
        $last3MonthsProfit = array_slice($chartData['profit'], 2, 3);
        $last3MonthsNames = array_slice($chartData['months'], 2, 3);

        // Tính tổng
        $totalFulfill = array_sum($last3MonthsFulfill);
        $totalBankPayout = array_sum($last3MonthsBankPayout);
        $totalProfit = array_sum($last3MonthsProfit);

        // Tính trung bình
        $avgFulfill = count($last3MonthsFulfill) > 0 ? $totalFulfill / count($last3MonthsFulfill) : 0;
        $avgBankPayout = count($last3MonthsBankPayout) > 0 ? $totalBankPayout / count($last3MonthsBankPayout) : 0;
        $avgProfit = count($last3MonthsProfit) > 0 ? $totalProfit / count($last3MonthsProfit) : 0;

        return [
            'totalFulfill' => $totalFulfill,
            'totalBankPayout' => $totalBankPayout,
            'totalProfit' => $totalProfit,
            'avgFulfill' => $avgFulfill,
            'avgBankPayout' => $avgBankPayout,
            'avgProfit' => $avgProfit,
            'monthNames' => $last3MonthsNames,
            'fulfillData' => $last3MonthsFulfill,
            'bankPayoutData' => $last3MonthsBankPayout,
            'profitData' => $last3MonthsProfit,
        ];
    }

    /**
     * Xóa cache của widget (chỉ cho super_admin)
     */
    public static function clearCache(): void
    {
        Cache::forget('bank_payout_summary_6m_widget_super_admin');
    }

    /**
     * Kiểm tra quyền xem widget
     */
    public static function canView(): bool
    {
        $user = auth()->user();
        return $user && ($user->hasRole('super_admin') || $user->hasRole('Leader') || $user->hasRole('Seller'));
    }
}
