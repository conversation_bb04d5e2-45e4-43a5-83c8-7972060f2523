<?php

namespace App\Filament\App\Widgets;

use App\Models\SupplierOrder;
use App\Models\User;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
// Removed Flowframe\Trend dependency - using custom implementation

class Overall<PERSON><PERSON>ill<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = 'Fulfill theo thời gian';
    protected static ?string $description = 'Biểu đồ fulfill và chi phí cho tất cả Seller được quản lý';
    protected static string $color = 'success';
    protected static ?int $sort = 2;

    // Best Practice: Disable polling để tránh snapshot issues
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = true;
    protected static ?string $maxHeight = '300px';

    // Best Practice: Thêm unique key cho component
    public function getKey(): string
    {
        return 'fulfill-chart-' . auth()->id();
    }

    /**
     * Best Practice: Kiểm tra quyền truy cập
     */
    public static function canView(): bool
    {
        return Auth::check() && Auth::user()->hasAnyRole(['Leader', 'User Manager', 'super_admin']);
    }

    /**
     * Best Practice: Filters cho chart
     */
    protected function getFilters(): ?array
    {
        return [
            'today' => 'Hôm nay',
            'week' => '7 ngày qua',
            'month' => '30 ngày qua',
            'quarter' => '3 tháng qua',
        ];
    }

    public ?string $filter = 'week';

    /**
     * Best Practice: Cached data với Laravel Trend
     */
    protected function getData(): array
    {
        $activeFilter = $this->filter;
        $cacheKey = "overall_fulfill_chart_{$activeFilter}_" . Auth::id();
        
        return Cache::remember($cacheKey, 300, function () use ($activeFilter) {
            $managedSellers = $this->getManagedSellers();
            
            if ($managedSellers->isEmpty()) {
                return [
                    'datasets' => [],
                    'labels' => [],
                ];
            }

            [$startDate, $endDate] = $this->getDateRange($activeFilter);

            // Best Practice: Custom implementation thay vì Laravel Trend
            $chartData = $this->getFulfillChartData($managedSellers->pluck('id')->toArray(), $startDate, $endDate);

            return [
                'datasets' => [
                    [
                        'label' => 'Đơn fulfill',
                        'data' => $chartData['fulfill_orders'],
                        'backgroundColor' => 'rgba(147, 51, 234, 0.8)',
                        'borderColor' => 'rgb(147, 51, 234)',
                        'borderWidth' => 1,
                        'yAxisID' => 'y',
                        'type' => 'bar',
                    ],
                    [
                        'label' => 'Chi phí fulfill ($)',
                        'data' => $chartData['fulfill_values'],
                        'borderColor' => 'rgb(249, 115, 22)',
                        'backgroundColor' => 'rgba(249, 115, 22, 0.1)',
                        'tension' => 0.4,
                        'yAxisID' => 'y1',
                        'type' => 'line',
                        'fill' => false,
                    ],
                ],
                'labels' => $chartData['labels'],
            ];
        });
    }

    /**
     * Mixed chart type (bar + line)
     */
    protected function getType(): string
    {
        return 'bar';
    }

    /**
     * Best Practice: Advanced chart options với dual y-axis
     */
    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Số đơn fulfill',
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Chi phí ($)',
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => 'Ngày',
                    ],
                ],
            ],
            'interaction' => [
                'mode' => 'nearest',
                'axis' => 'x',
                'intersect' => false,
            ],
        ];
    }

    /**
     * Helper methods
     */
    private function getManagedSellers()
    {
        $user = Auth::user();

        if ($user->hasRole('super_admin')) {
            return User::role('Seller')->get();
        }

        if ($user->hasRole(['Leader', 'User Manager'])) {
            // Sử dụng leaderManagedSellers thay vì managedSellers
            return $user->leaderManagedSellers()->role('Seller')->get();
        }

        return collect();
    }

    /**
     * Best Practice: Custom chart data implementation
     */
    private function getFulfillChartData(array $sellerIds, Carbon $startDate, Carbon $endDate): array
    {
        if (empty($sellerIds)) {
            return [
                'fulfill_orders' => [],
                'fulfill_values' => [],
                'labels' => [],
            ];
        }

        // Generate date range
        $dates = [];
        $current = $startDate->copy();
        while ($current->lte($endDate)) {
            $dates[] = $current->format('Y-m-d');
            $current->addDay();
        }

        // Get fulfill orders count by date sử dụng Eloquent
        $fulfillOrdersData = SupplierOrder::whereIn('user_id', $sellerIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupByRaw('DATE(created_at)')
            ->pluck('count', 'date')
            ->toArray();

        // Get fulfill values by date sử dụng Eloquent
        $fulfillValuesData = SupplierOrder::whereIn('user_id', $sellerIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as total')
            ->groupByRaw('DATE(created_at)')
            ->pluck('total', 'date')
            ->toArray();

        // Fill missing dates with 0
        $fulfillOrders = [];
        $fulfillValues = [];
        $labels = [];

        foreach ($dates as $date) {
            $fulfillOrders[] = $fulfillOrdersData[$date] ?? 0;
            $fulfillValues[] = $fulfillValuesData[$date] ?? 0;
            $labels[] = Carbon::parse($date)->format('d/m');
        }

        return [
            'fulfill_orders' => $fulfillOrders,
            'fulfill_values' => $fulfillValues,
            'labels' => $labels,
        ];
    }

    private function getDateRange(string $filter): array
    {
        $endDate = Carbon::now();
        
        $startDate = match ($filter) {
            'today' => Carbon::today(),
            'week' => Carbon::now()->subDays(7),
            'month' => Carbon::now()->subDays(30),
            'quarter' => Carbon::now()->subDays(90),
            default => Carbon::now()->subDays(7),
        };
        
        return [$startDate, $endDate];
    }
}
