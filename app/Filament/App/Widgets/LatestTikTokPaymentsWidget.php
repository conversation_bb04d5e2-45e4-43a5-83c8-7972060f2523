<?php

namespace App\Filament\App\Widgets;

use App\Filament\App\Resources\TikTokPaymentResource;
use App\Models\TikTokPayment;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class LatestTikTokPaymentsWidget extends BaseWidget
{
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                TikTokPayment::query()
                    ->where('status', 'PAID')
                    ->latest('paid_time')
            )
            ->paginationPageOptions([5])
            ->columns([
                Tables\Columns\TextColumn::make('store.name')
                   
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_id')
                   ,
                Tables\Columns\TextColumn::make('amount')
                    ->money(fn($record) => $record->currency)
                    ->sortable(),
                Tables\Columns\TextColumn::make('currency'),
                Tables\Columns\TextColumn::make('paid_time')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('settlement_amount')
                    ->money(fn($record) => $record->currency)
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'success' => 'PAID',
                        'warning' => 'PENDING',
                        'danger' => 'FAILED',
                    ]),
            ])
            ->heading('5 Thanh Toán TikTok Gần Nhất')
            ->description('Múi giờ -8, Nhận được sau 3-5 ngày')
            ->headerActions([
                Tables\Actions\Action::make('view_all')
                    ->label('Xem Tất Cả')
                    ->url(TikTokPaymentResource::getUrl())
                    ->icon('heroicon-m-arrow-top-right-on-square')
                    ->color('success')
            ]);
    }
} 