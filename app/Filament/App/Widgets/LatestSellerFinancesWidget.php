<?php

namespace App\Filament\App\Widgets;

use App\Filament\App\Resources\SellerFinanceResource;
use App\Models\SellerFinance;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\HtmlString;

class LatestSellerFinancesWidget extends BaseWidget
{
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                SellerFinance::query()
            )
            ->paginationPageOptions([2])
            ->defaultSort('month', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('gross_revenue')
                    ->label('Thông Tin')
                    ->formatStateUsing(function (SellerFinance $record) {
                        $html = '<div class="space-y-2">';
                        $html .= '<div class="flex flex-col">';
                        $html .= '<div class="text-lg font-bold">' . $record->seller->teams->first()?->name . '</div>';
                        $html .= '<div class="text-sm text-gray-500">' . $record->seller->name . '</div>';
                        $html .= '</div>';
                        $html .= '<div class="text-base font-medium">' . $record->month->format('m/Y') . '</div>';
                        $html .= '</div>';
                        return new HtmlString($html);
                    }),

                Tables\Columns\TextColumn::make('net_revenue')
                    ->weight('bold')
                    ->label('Doanh Thu')
                    ->money('USD'),

                Tables\Columns\TextColumn::make('total_salary')
                    ->label('Thu Nhập')
                    ->money('USD')
                    ->description(function (SellerFinance $record) {
                        $report = $record->getDetailedReport();
                        $commission = $report['earnings']['commission'];
                        return new HtmlString(
                            "Lương: $" . number_format($record->base_salary, 2) . "<br>" .
                            "Hoa Hồng ({$commission['total_rate']}%): $" . number_format($record->total_bonus, 2)
                        );
                    }),

                Tables\Columns\TextColumn::make('remaining_amount')
                    ->label('Thanh Toán')
                    ->money('USD')
                    ->state(fn(SellerFinance $record): float => $record->getRemainingAmount())
                    ->color(fn($state): string => $state > 0 ? 'danger' : 'success')
                    ->formatStateUsing(function (SellerFinance $record) {
                        $remainingAmount = $record->getRemainingAmount();
                        return $remainingAmount > 0 
                            ? new HtmlString('<span class="text-danger-600">Còn lại: $' . number_format($remainingAmount, 2) . '</span>')
                            : new HtmlString('<span class="text-success-600">Đã thanh toán</span>');
                    }),
            ])
            ->heading('2 Báo Cáo Tài Chính Mới Nhất')
            ->headerActions([
                Tables\Actions\Action::make('view_all')
                    ->label('Xem Tất Cả')
                    ->url(SellerFinanceResource::getUrl())
                    ->icon('heroicon-m-arrow-top-right-on-square')
                    ->color('success')
            ]);
    }
} 