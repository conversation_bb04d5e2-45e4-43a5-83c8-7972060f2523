<?php

namespace App\Filament\App\Widgets;

use App\Services\FulfillmentSpeedService;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Livewire\Attributes\On;

class FulfillmentSpeedChart extends ChartWidget
{
    use InteractsWithPageFilters;

    // Public property để nhận dateRange từ page
    public ?string $dateRange = null;

    // Cache key để tránh reload không cần thiết
    public ?string $lastDateRange = null;

    protected static ?string $heading = '📊 Biểu Đồ Tốc Độ Fulfill Theo <PERSON>';
    protected static ?string $description = 'Thống kê số lượng fulfillments và thời gian trung bình theo ngày';

    // Chiếm toàn bộ chiều rộng
    protected int | string | array $columnSpan = 'full';

    // Tắt auto refresh để tránh reload không cần thiết
    protected static ?string $pollingInterval = null;

    // Chiều cao tối đa
    protected static ?string $maxHeight = '700px';

    // Màu sắc
    // protected static string $color = 'info';

    // Lazy loading
    protected static bool $isLazy = false;

    /**
     * Listen for date range updates from parent page
     */
    #[On('dashboard-date-updated')]
    public function updateDateRange($dateRange)
    {
        $this->dateRange = $dateRange;
        $this->lastDateRange = null; // Clear cache
    }

    protected function getData(): array
    {
        // Lấy date range từ filters hoặc mặc định
        $startDate = $this->getStartDate();
        $endDate = $this->getEndDate();

        // Tạo cache key từ date range
        $currentDateRange = $startDate?->format('Y-m-d') . '_' . $endDate?->format('Y-m-d');

        // Skip nếu date range không thay đổi (sử dụng cache từ service)
        if ($this->lastDateRange === $currentDateRange) {
            // Vẫn gọi service nhưng service sẽ return từ cache
        }

        try {
            $fulfillmentService = new FulfillmentSpeedService();
            $data = $fulfillmentService->getFulfillmentAnalytics($startDate, $endDate);
            
            if (!isset($data['chart_data'])) {
                return $this->getEmptyData();
            }

            $chartData = $data['chart_data'];

            $chartResult = [
                'datasets' => [
                    [
                        'label' => 'Số lượng Fulfillments',
                        'data' => $chartData['fulfillments'] ?? [],
                        'borderColor' => 'rgb(59, 130, 246)',
                        'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                        'yAxisID' => 'y',
                        'tension' => 0.1,
                        'fill' => true,
                    ],
                    [
                        'label' => 'Thời gian TB (giờ)',
                        'data' => $chartData['avg_time'] ?? [],
                        'borderColor' => 'rgb(239, 68, 68)',
                        'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                        'yAxisID' => 'y1',
                        'tension' => 0.1,
                        'fill' => false,
                    ]
                ],
                'labels' => $chartData['labels'] ?? []
            ];

            // Update cache key
            $this->lastDateRange = $currentDateRange;

            return $chartResult;
        } catch (\Exception $e) {
            return $this->getEmptyData();
        }
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
            'plugins' => [
                'legend' => [
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ]
            ],
            'scales' => [
                'x' => [
                    'display' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Ngày'
                    ]
                ],
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'title' => [
                        'display' => true,
                        'text' => 'Số lượng Fulfillments'
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'title' => [
                        'display' => true,
                        'text' => 'Thời gian (giờ)'
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ]
            ]
        ];
    }

    private function getStartDate(): Carbon
    {
        $dateRange = $this->dateRange ?? $this->filters['dateRange'] ?? null;

        if ($dateRange) {
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                return Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
            }
        }

        return Carbon::now()->startOfMonth()->startOfDay();
    }

    private function getEndDate(): Carbon
    {
        $dateRange = $this->dateRange ?? $this->filters['dateRange'] ?? null;

        if ($dateRange) {
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                return Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
            }
        }

        return Carbon::now()->endOfDay();
    }

    private function getEmptyData(): array
    {
        return [
            'datasets' => [
                [
                    'label' => 'Số lượng Fulfillments',
                    'data' => [],
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                ]
            ],
            'labels' => []
        ];
    }

    public static function canView(): bool
    {
        $user = auth()->user();
        return $user && $user->hasRole(['super_admin', 'Fulfillment Manager', 'User Manager']);
    }
}
