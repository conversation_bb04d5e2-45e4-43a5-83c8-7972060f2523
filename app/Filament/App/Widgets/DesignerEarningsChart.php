<?php

namespace App\Filament\App\Widgets;

use App\Models\DesignJob;
use App\Models\User;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class DesignerEarningsChart extends ChartWidget
{
    protected static ?string $heading = 'Designer Earnings Distribution';
    protected static ?int $sort = 3;
    
    protected function getData(): array
    {
        // Get top 8 designers by earnings
        $designers = User::role('Designer')
            ->select([
                'users.id',
                'users.name',
                DB::raw('SUM(design_jobs.price) as total_earnings'),
            ])
            ->leftJoin('design_jobs', 'users.id', '=', 'design_jobs.designer_id')
            ->groupBy('users.id', 'users.name')
            ->orderBy('total_earnings', 'desc')
            ->limit(8)
            ->get();
            
        // Calculate total earnings for percentage
        $totalEarnings = $designers->sum('total_earnings');
        
        // Create color palette
        $colors = [
            'rgba(59, 130, 246, 0.8)', // Blue
            'rgba(16, 185, 129, 0.8)', // Green
            'rgba(245, 158, 11, 0.8)', // Yellow
            'rgba(239, 68, 68, 0.8)',  // Red
            'rgba(139, 92, 246, 0.8)', // Purple
            'rgba(236, 72, 153, 0.8)', // Pink
            'rgba(6, 182, 212, 0.8)',  // Cyan
            'rgba(249, 115, 22, 0.8)', // Orange
        ];
        
        return [
            'labels' => $designers->pluck('name')->toArray(),
            'datasets' => [
                [
                    'label' => 'Designer Earnings',
                    'data' => $designers->pluck('total_earnings')->toArray(),
                    'backgroundColor' => array_slice($colors, 0, $designers->count()),
                ],
            ],
        ];
    }
    
    protected function getType(): string
    {
        return 'pie';
    }
} 