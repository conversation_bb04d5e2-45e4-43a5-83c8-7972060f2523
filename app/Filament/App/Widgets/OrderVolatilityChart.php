<?php

namespace App\Filament\App\Widgets;

use App\Models\Order;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;

class OrderVolatilityChart extends ChartWidget
{
    protected static ?string $heading = '<PERSON>ân Tích Biến Động Orders Theo Khung <PERSON>ờ';
    protected static ?string $description = 'Hiển thị độ biến động và ngưỡng cảnh báo cho từng khung 3 giờ';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '400px';

    public ?string $filter = 'current_month';

    protected function getData(): array
    {
        $period = $this->getFilterPeriod();
        
        // L<PERSON>y dữ li<PERSON>u orders trong khoảng thời gian
        $orders = Order::whereBetween('created_at', [$period['start'], $period['end']])->get();
        
        // Đ<PERSON>nh nghĩa các khung 3 giờ
        $timeSlots = [
            '00:00-03:00' => ['hours' => range(0, 2), 'label' => '00:00-03:00'],
            '03:00-06:00' => ['hours' => range(3, 5), 'label' => '03:00-06:00'],
            '06:00-09:00' => ['hours' => range(6, 8), 'label' => '06:00-09:00'],
            '09:00-12:00' => ['hours' => range(9, 11), 'label' => '09:00-12:00'],
            '12:00-15:00' => ['hours' => range(12, 14), 'label' => '12:00-15:00'],
            '15:00-18:00' => ['hours' => range(15, 17), 'label' => '15:00-18:00'],
            '18:00-21:00' => ['hours' => range(18, 20), 'label' => '18:00-21:00'],
            '21:00-24:00' => ['hours' => range(21, 23), 'label' => '21:00-24:00'],
        ];

        $labels = [];
        $avgData = [];
        $maxData = [];
        $minData = [];
        $warningThresholds = [];
        $criticalThresholds = [];
        $volatilityData = [];

        foreach ($timeSlots as $slotKey => $slot) {
            $labels[] = $slot['label'];
            
            // Tính toán dữ liệu cho từng ngày trong khoảng thời gian
            $dailyData = [];
            $totalDays = $period['start']->diffInDays($period['end']) + 1;
            
            for ($day = 0; $day < $totalDays; $day++) {
                $currentDate = $period['start']->copy()->addDays($day);
                $count = 0;
                
                foreach ($slot['hours'] as $hour) {
                    $count += $orders->filter(function($order) use ($currentDate, $hour) {
                        $orderTime = $order->created_at->setTimezone('Asia/Ho_Chi_Minh');
                        return $orderTime->day == $currentDate->day && 
                               $orderTime->month == $currentDate->month &&
                               $orderTime->year == $currentDate->year &&
                               $orderTime->hour == $hour;
                    })->count();
                }
                
                if ($count > 0) {
                    $dailyData[] = $count;
                }
            }
            
            // Tính toán thống kê
            if (!empty($dailyData)) {
                $avg = array_sum($dailyData) / count($dailyData);
                $max = max($dailyData);
                $min = min($dailyData);
                $stdDev = $this->calculateStandardDeviation($dailyData, $avg);
                $cv = $avg > 0 ? ($stdDev / $avg) * 100 : 0;
                
                $avgData[] = round($avg, 2);
                $maxData[] = $max;
                $minData[] = $min;
                $warningThresholds[] = round($avg * 1.3, 2); // +30%
                $criticalThresholds[] = round($avg * 1.5, 2); // +50%
                $volatilityData[] = round($cv, 2);
            } else {
                $avgData[] = 0;
                $maxData[] = 0;
                $minData[] = 0;
                $warningThresholds[] = 0;
                $criticalThresholds[] = 0;
                $volatilityData[] = 0;
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Trung bình',
                    'data' => $avgData,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.5)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'type' => 'bar',
                ],
                [
                    'label' => 'Max',
                    'data' => $maxData,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.3)',
                    'borderColor' => 'rgb(34, 197, 94)',
                    'type' => 'line',
                    'fill' => false,
                ],
                [
                    'label' => 'Min',
                    'data' => $minData,
                    'backgroundColor' => 'rgba(156, 163, 175, 0.3)',
                    'borderColor' => 'rgb(156, 163, 175)',
                    'type' => 'line',
                    'fill' => false,
                ],
                [
                    'label' => 'Ngưỡng WARNING (+30%)',
                    'data' => $warningThresholds,
                    'backgroundColor' => 'rgba(245, 158, 11, 0.3)',
                    'borderColor' => 'rgb(245, 158, 11)',
                    'borderDash' => [5, 5],
                    'type' => 'line',
                    'fill' => false,
                ],
                [
                    'label' => 'Ngưỡng CRITICAL (+50%)',
                    'data' => $criticalThresholds,
                    'backgroundColor' => 'rgba(239, 68, 68, 0.3)',
                    'borderColor' => 'rgb(239, 68, 68)',
                    'borderDash' => [10, 5],
                    'type' => 'line',
                    'fill' => false,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Số Orders / 3h',
                    ],
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => 'Khung Giờ (VN Time)',
                    ],
                ],
            ],
            'interaction' => [
                'mode' => 'nearest',
                'axis' => 'x',
                'intersect' => false,
            ],
        ];
    }

    protected function getFilters(): ?array
    {
        return [
            'current_month' => 'Tháng hiện tại',
            'last_month' => 'Tháng trước',
            'last_3_months' => '3 tháng gần nhất',
            'current_year' => 'Năm nay',
        ];
    }

    private function getFilterPeriod(): array
    {
        $now = Carbon::now();
        
        return match($this->filter) {
            'last_month' => [
                'start' => $now->copy()->subMonth()->startOfMonth(),
                'end' => $now->copy()->subMonth()->endOfMonth(),
            ],
            'last_3_months' => [
                'start' => $now->copy()->subMonths(3)->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'current_year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [ // current_month
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
        };
    }

    private function calculateStandardDeviation(array $data, float $mean): float
    {
        if (count($data) <= 1) {
            return 0;
        }
        
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $data)) / count($data);
        
        return sqrt($variance);
    }

    public function getDescription(): ?string
    {
        $period = $this->getFilterPeriod();
        $orders = Order::whereBetween('created_at', [$period['start'], $period['end']])->count();
        
        return "Phân tích {$orders} orders từ {$period['start']->format('d/m/Y')} đến {$period['end']->format('d/m/Y')}. " .
               "Khung 15:00-18:00 có biến động cao nhất (74.82%), cần ngưỡng cảnh báo cao hơn để tránh spam.";
    }
}
