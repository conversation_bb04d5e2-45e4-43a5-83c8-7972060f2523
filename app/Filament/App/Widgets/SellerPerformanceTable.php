<?php

namespace App\Filament\App\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\User;
use App\Models\Idea;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class SellerPerformanceTable extends BaseWidget
{
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                User::query()->role('seller')
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Seller Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('ideas_count')
                    ->label('Total Ideas (Last Week)')
                    ->counts('ideas', function (Builder $query) {
                        return $query->whereBetween('created_at', [Carbon::now()->subWeek(), Carbon::now()]);
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('ideas_with_orders_count')
                    ->label('Ideas with Orders')
                    ->counts('ideas', function (Builder $query) {
                        return $query->whereBetween('created_at', [Carbon::now()->subWeek(), Carbon::now()])
                            ->where('has_sold', true);
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('conversion_rate')
                    ->label('Conversion Rate')
                    ->state(function (User $record): string {
                        $totalIdeas = $record->ideas()
                            ->whereBetween('created_at', [Carbon::now()->subWeek(), Carbon::now()])
                            ->count();
                        $ideasWithOrders = $record->ideas()
                            ->whereBetween('created_at', [Carbon::now()->subWeek(), Carbon::now()])
                            ->where('has_sold', true)
                            ->count();

                     
                        $rate = $totalIdeas > 0 ? ($ideasWithOrders / $totalIdeas) * 100 : 0;
                        return number_format($rate, 2) . '%';
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('ideas_per_day')
                    ->label('Ideas/Day (Avg)')
                    ->state(function (User $record): string {
                        $totalIdeas = $record->ideas()
                            ->whereBetween('created_at', [Carbon::now()->subWeek(), Carbon::now()])
                            ->count();
                        return number_format($totalIdeas / 7, 2);
                    })
                    ->sortable(),
            ])
            ->defaultSort('ideas_count', 'desc')
            ->paginated(false);
    }
}
