<?php

namespace App\Filament\App\Widgets;

use App\Models\Store;
use Filament\Widgets\Widget;
use Illuminate\Support\Carbon;
use App\Enums\TiktokShopStatus;

class StoreProductReminder extends Widget
{
    protected static string $view = 'filament.app.widgets.store-product-reminder';
    protected int | string | array $columnSpan = [
        'default' => 2,
        'md' => 1,
        'lg' => 1,
        'xl' => 1,
    ];
    public function getStoresNeedingAttention()
    {
        $oneWeekAgo = Carbon::now()->subWeek();
        
        return Store::where(function ($query) use ($oneWeekAgo) {
            $query->where('tiktok_product_count', '<', 70)
                ->orWhere(function ($q) use ($oneWeekAgo) {
                    $q->whereNotNull('last_created_tiktok_product')
                      ->where('last_created_tiktok_product', '<', $oneWeekAgo);
                })
                ->orWhereNull('last_created_tiktok_product');
        })
        ->whereIn('tiktok_shop_status', [TiktokShopStatus::NotConnected, TiktokShopStatus::Live]) // Only get stores with NotConnected and Live status
        ->get()
        ->map(function ($store) use ($oneWeekAgo) {
            $store->priority = $this->calculatePriority($store, $oneWeekAgo);
            return $store;
        })
        ->sortByDesc('priority');
    }

    protected function calculatePriority($store, $oneWeekAgo)
    {
        $priority = 0;
        if ($store->tiktok_product_count < 30) $priority += 3;
        elseif ($store->tiktok_product_count < 50) $priority += 2;
        elseif ($store->tiktok_product_count < 70) $priority += 1;

        if (!$store->last_created_tiktok_product) $priority += 3;
        elseif ($store->last_created_tiktok_product < $oneWeekAgo) $priority += 2;

        return $priority;
    }

    protected function getViewData(): array
    {
        $stores = $this->getStoresNeedingAttention();
        
        return [
            'storesCount' => $stores->count(),
            'stores' => $stores,
        ];
    }
}