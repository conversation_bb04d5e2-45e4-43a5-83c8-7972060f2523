<?php

namespace App\Filament\App\Widgets;

use App\Models\SupplierOrder;
use App\Models\TikTokPayment;
use Filament\Widgets\Widget;
use Carbon\Carbon;

class MonthlyGoalsWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.monthly-goals-widget';
    protected static ?int $sort = -2; // Place at top

    public function getColumnSpan(): int|string|array
    {
        return 'full';
    }

    public function getViewData(): array
    {
        $user = auth()->user();
        $currentMonth = Carbon::now();

        // Get current month's orders
        $orderQuery = SupplierOrder::query();
        if (!$user->hasRole('super_admin')) {
            $orderQuery->whereHas('store', function ($q) use ($user) {
                $q->where('owner_id', $user->id);
            });
        }

        $monthlyOrders = $orderQuery->whereMonth('created_at', $currentMonth->month)
            ->whereYear('created_at', $currentMonth->year)
            ->count();

        // Get current month's payout from TikTok payments
        $payoutQuery = TikTokPayment::query();
        $monthlyPayout = $payoutQuery
            ->whereMonth('paid_time', $currentMonth->month)
            ->whereYear('paid_time', $currentMonth->year)
            ->where('status', 'PAID')
            ->sum('settlement_amount');

        // Calculate commission
        $commissionRate = $user->commission_rate ?? 5.00;
        $monthlyCommission = ($monthlyPayout * $commissionRate) / 100;

        // Calculate goals
        $orderGoal = $this->calculateOrderGoal($monthlyOrders);
        $payoutGoal = $this->calculatePayoutGoal($monthlyPayout);

        // Calculate progress percentages
        $orderProgress = min(($monthlyOrders / $orderGoal) * 100, 100);
        $payoutProgress = min(($monthlyPayout / $payoutGoal) * 100, 100);

        return [
            'monthlyOrders' => $monthlyOrders,
            'monthlyPayout' => $monthlyPayout,
            'monthlyCommission' => $monthlyCommission,
            'commissionRate' => $commissionRate,
            'orderGoal' => $orderGoal,
            'payoutGoal' => $payoutGoal,
            'orderProgress' => $orderProgress,
            'payoutProgress' => $payoutProgress,
            'orderStatus' => $this->getProgressStatus($orderProgress),
            'payoutStatus' => $this->getProgressStatus($payoutProgress),
        ];
    }

    protected function calculateOrderGoal($currentOrders): int
    {
        $goals = [100, 300, 500, 1000, 2000];
        foreach ($goals as $goal) {
            if ($currentOrders < $goal) {
                return $goal;
            }
        }
        return end($goals);
    }

    protected function calculatePayoutGoal($currentPayout): int
    {
        $goals = [2000, 5000, 10000, 20000, 50000];
        foreach ($goals as $goal) {
            if ($currentPayout < $goal) {
                return $goal;
            }
        }
        return end($goals);
    }

    protected function getProgressStatus($percentage): array
    {
        if ($percentage >= 100) {
            return [
                'message' => "Goal Achieved!",
                'emoji' => "🎉",
                'color' => "text-success-500",
                'bg' => "bg-success-50"
            ];
        }
        if ($percentage >= 75) {
            return [
                'message' => "Almost There!",
                'emoji' => "💪",
                'color' => "text-warning-500",
                'bg' => "bg-warning-50"
            ];
        }
        if ($percentage >= 50) {
            return [
                'message' => "Halfway There!",
                'emoji' => "🚀",
                'color' => "text-info-500",
                'bg' => "bg-info-50"
            ];
        }
        if ($percentage >= 25) {
            return [
                'message' => "Keep Going!",
                'emoji' => "💫",
                'color' => "text-primary-500",
                'bg' => "bg-primary-50"
            ];
        }
        return [
            'message' => "Just Started!",
            'emoji' => "🌱",
            'color' => "text-gray-500",
            'bg' => "bg-gray-50"
        ];
    }
}
