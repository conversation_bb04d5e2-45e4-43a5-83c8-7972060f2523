<?php

namespace App\Filament\App\Widgets\SellerFinance;

use Filament\Widgets\ChartWidget;

class CostBreakdown extends ChartWidget
{
    protected static ?string $heading = 'Cost Breakdown';
    public array $data = [];

    protected function getData(): array
    {
        if (empty($this->data)) {
            return [
                'datasets' => [
                    [
                        'label' => 'Costs',
                        'data' => [0, 0, 0, 0, 0],
                        'backgroundColor' => [
                            'rgb(255, 99, 132)',
                            'rgb(54, 162, 235)',
                            'rgb(255, 205, 86)',
                            'rgb(75, 192, 192)',
                            'rgb(153, 102, 255)',
                        ],
                    ],
                ],
                'labels' => ['Fulfillment', 'Ads', 'Production', 'Direct Design', 'Shared Design'],
            ];
        }

        $costs = $this->data['costs']['breakdown'] ?? [];
        $values = [
            $costs['fulfillment'] ?? 0,
            $costs['ads'] ?? 0,
            $costs['production'] ?? 0,
            $costs['direct_design'] ?? 0,
            $costs['shared_design'] ?? 0,
        ];

        return [
            'datasets' => [
                [
                    'label' => 'Costs',
                    'data' => $values,
                    'backgroundColor' => [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 205, 86)',
                        'rgb(75, 192, 192)',
                        'rgb(153, 102, 255)',
                    ],
                ],
            ],
            'labels' => ['Fulfillment', 'Ads', 'Production', 'Direct Design', 'Shared Design'],
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
} 