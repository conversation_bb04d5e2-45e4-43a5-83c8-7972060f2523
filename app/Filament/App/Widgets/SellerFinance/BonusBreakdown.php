<?php

namespace App\Filament\App\Widgets\SellerFinance;

use Filament\Widgets\ChartWidget;

class BonusBreakdown extends ChartWidget
{
    protected static ?string $heading = 'Bonus Breakdown';
    public array $data = [];

    protected function getData(): array
    {
        if (empty($this->data)) {
            return [
                'datasets' => [
                    [
                        'label' => 'Bonuses',
                        'data' => [0, 0, 0],
                        'backgroundColor' => [
                            'rgb(54, 162, 235)',
                            'rgb(75, 192, 192)',
                            'rgb(153, 102, 255)',
                        ],
                    ],
                ],
                'labels' => ['Level Bonus', 'Position Bonus', 'Performance Bonus'],
            ];
        }

        $bonuses = $this->data['earnings']['bonuses'] ?? [];
        $values = [
            $bonuses['level'] ?? 0,
            $bonuses['position'] ?? 0,
            $bonuses['performance'] ?? 0,
        ];

        $labels = ['Level Bonus', 'Position Bonus', 'Performance Bonus'];

        // Add other bonuses if they exist
        if (!empty($bonuses['others'])) {
            $otherBonusesTotal = array_sum(array_column($bonuses['others'], 'amount'));
            if ($otherBonusesTotal > 0) {
                $values[] = $otherBonusesTotal;
                $labels[] = 'Other Bonuses';
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Bonuses',
                    'data' => $values,
                    'backgroundColor' => [
                        'rgb(54, 162, 235)',
                        'rgb(75, 192, 192)',
                        'rgb(153, 102, 255)',
                        'rgb(255, 159, 64)',
                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
} 