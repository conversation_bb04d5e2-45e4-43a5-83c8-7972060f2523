<?php

namespace App\Filament\App\Widgets\SellerFinance;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class MonthlyOverview extends BaseWidget
{
    public array $data = [];

    protected function getStats(): array
    {
        if (empty($this->data)) {
            return [];
        }

        return [
            Stat::make('Gross Revenue', '$' . number_format($this->data['revenue']['gross'], 2))
                ->description('Platform Fees: -$' . number_format($this->data['revenue']['platform_fees'], 2))
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('primary'),

            Stat::make('Net Revenue', '$' . number_format($this->data['revenue']['net'], 2))
                ->description('After Platform Fees')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success'),

            Stat::make('Total Costs', '$' . number_format($this->data['costs']['total'], 2))
                ->description('All Operating Costs')
                ->descriptionIcon('heroicon-m-arrow-trending-down')
                ->color('danger'),
        ];
    }
} 