<?php

namespace App\Filament\App\Widgets\SellerFinance;

use Filament\Widgets\ChartWidget;

class RevenueChart extends ChartWidget
{
    protected static ?string $heading = 'Revenue Trend';
    public ?array $data = [];

    protected function getData(): array
    {
        $data = $this->data;
        if (empty($data)) return [];

        return [
            'datasets' => [
                [
                    'label' => 'Revenue',
                    'data' => [
                        $data['revenue']['gross'],
                        $data['revenue']['platform_fees'],
                        $data['revenue']['net'],
                    ],
                    'backgroundColor' => [
                        '#10B981', // Gross Revenue
                        '#EF4444', // Platform Fees
                        '#3B82F6', // Net Revenue
                    ],
                ],
            ],
            'labels' => ['Gross Revenue', 'Platform Fees', 'Net Revenue'],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
} 