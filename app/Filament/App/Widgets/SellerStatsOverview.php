<?php

namespace App\Filament\App\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Idea;
use App\Models\User;
use Carbon\Carbon;

class SellerStatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $startDate = Carbon::now()->subWeek();
        $endDate = Carbon::now();

        $totalIdeas = Idea::whereBetween('created_at', [$startDate, $endDate])->count();
        $totalSellers = User::role('seller')->count();
        $ideasWithOrders = Idea::whereBetween('created_at', [$startDate, $endDate])
            ->where('has_sold', true)
            ->count();

        $conversionRate = $totalIdeas > 0 ? ($ideasWithOrders / $totalIdeas) * 100 : 0;
        $averageIdeasPerDay = $totalIdeas / 7;

        return [
            Stat::make('Total Ideas (Last Week)', $totalIdeas)
                ->description('Number of ideas created in the last 7 days')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),
            Stat::make('Conversion Rate', number_format($conversionRate, 2) . '%')
                ->description('Percentage of ideas that received orders')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),
            Stat::make('Average Ideas/Day', number_format($averageIdeasPerDay, 2))
                ->description('Average number of ideas created per day')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('info'),
        ];
    }
}
