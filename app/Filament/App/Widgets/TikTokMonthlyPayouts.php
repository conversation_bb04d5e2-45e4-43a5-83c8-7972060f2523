<?php

namespace App\Filament\App\Widgets;

use App\Models\Store;
use App\Models\TikTokPayment;
use Filament\Widgets\Widget;
use Carbon\Carbon;

class TikTokMonthlyPayouts extends Widget
{
    protected static string $view = 'filament.app.widgets.tiktok-monthly-payouts';
    public $metrics = [];

    public function getColumnSpan(): int|string|array
    {
        return 'full';
    }

    public function mount()
    {
        $this->metrics = $this->getStoreMetrics();
    }

    protected function getStoreMetrics()
    {
        $metrics = [];
        $stores = Store::with('owner')->get();

        foreach ($stores as $store) {
            $monthlyMetrics = [
                'store_name' => $store->name,
                'owner_name' => $store->owner->name,
                'store_avatar' => $store->avatar ?? $store->owner->avatar,
                'payouts' => $this->getMonthlyPayouts($store->id),
                'total_orders' => $this->getMonthlyOrderCounts($store->id),
            ];

            // Tính tổng cho mỗi tháng
            foreach ($monthlyMetrics['payouts'] as $month => $data) {
                $monthlyMetrics['monthly_totals'][$month] = [
                    'payout' => $data['total'],
                    'order_count' => $monthlyMetrics['total_orders'][$month] ?? 0,
                    'period' => $data['period'],
                ];
            }

            $metrics[] = $monthlyMetrics;
        }

        return $metrics;
    }

    protected function getMonthlyPayouts($storeId)
    {
        $payouts = [];
        $laTime = now()->setTimezone('America/Los_Angeles');

        // Lấy 4 tháng gần nhất
        for ($i = 0; $i < 4; $i++) {
            $date = $laTime->copy()->subMonths($i);
            
            $monthlyData = TikTokPayment::getLAMonthlyTotal(
                $storeId, 
                $date->month, 
                $date->year
            );

            $payouts[$date->format('F Y')] = [
                'total' => $monthlyData['total'],
                'period' => $monthlyData['date_range'],
                'payments' => $monthlyData['payments']->map(function($payment) {
                    return [
                        'id' => $payment->payment_id,
                        'amount' => $payment->settlement_amount,
                        'paid_time' => $payment->la_paid_time,
                        'status' => $payment->status
                    ];
                })
            ];
        }

        return $payouts;
    }

    protected function getMonthlyOrderCounts($storeId)
    {
        $orderCounts = [];
        $laTime = now()->setTimezone('America/Los_Angeles');

        for ($i = 0; $i < 4; $i++) {
            $date = $laTime->copy()->subMonths($i);
            
            // Tạo range time theo LA
            $startDate = Carbon::create($date->year, $date->month, 1, 0, 0, 0, 'America/Los_Angeles');
            $endDate = $startDate->copy()->endOfMonth();

            $orderCounts[$date->format('F Y')] = TikTokPayment::where('store_id', $storeId)
                ->byLATime($startDate, $endDate)
                ->count();
        }

        return $orderCounts;
    }
} 