<?php

namespace App\Filament\App\Widgets;

use Filament\Widgets\Widget;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class TopHandlersWidget extends Widget
{
    protected static string $view = 'filament.widgets.top-handlers-widget';

    public $todayTopHandlers;
    public $weekTopHandlers;
    public $thisMonthTopHandlers;
    public $lastMonthTopHandlers;

    public function getColumnSpan(): int|string|array
    {
        return 'full';
    }

    public function mount(): void
    {
        $this->todayTopHandlers = $this->getTopHandlers(Carbon::today(), Carbon::today()->endOfDay());
        $this->weekTopHandlers = $this->getTopHandlers(Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek());
        $this->thisMonthTopHandlers = $this->getTopHandlers(Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth());
        $this->lastMonthTopHandlers = $this->getTopHandlers(Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth());
    }

    private function getTopHandlers($startDate, $endDate)
    {
        return Order::whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('handler_id')
            ->orderByRaw('SUM(total) DESC')
            ->take(3)
            ->get(['handler_id', DB::raw('COUNT(*) as total_orders'), DB::raw('SUM(total) as total_rev')]);
    }
}

