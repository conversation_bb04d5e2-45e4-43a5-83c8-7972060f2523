<?php

namespace App\Filament\App\Widgets;

use App\Models\Order;
use App\Models\SupplierOrder;
use App\Models\Store;
use App\Models\User;
use App\Enums\OrderStatus;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Traits\HasUserFilter;
use Livewire\Attributes\On;

class OverallStatsOverview extends BaseWidget
{
    use HasUserFilter;
    protected static ?int $sort = 0; // Hiển thị đầu tiên

    // Best Practice: Disable polling để tránh snapshot issues
    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false; // Stats quan trọng nên load ngay

    // Livewire 3 Best Practice: Sử dụng #[On] attribute thay vì $listeners

    // Properties để lưu filter state từ parent
    public $dateRange;
    public $selectedUserId;
    public $startDate;
    public $endDate;

    // Best Practice: Thêm unique key cho component
    public function getKey(): string
    {
        return 'overall-stats-' . auth()->id();
    }

    /**
     * Best Practice: Kiểm tra quyền truy cập
     */
    public static function canView(): bool
    {
        return Auth::check() && Auth::user()->hasAnyRole(['Leader', 'User Manager', 'super_admin']);
    }

    /**
     * Livewire 3 Best Practice: Sử dụng #[On] attribute với named parameters
     */
    #[On('filtersUpdated')]
    public function updateFilters($dateRange = null, $selectedUserId = null)
    {
        // Cập nhật properties
        if ($dateRange !== null) {
            $this->dateRange = $dateRange;

            // Force reset startDate và endDate trước khi parse
            $this->startDate = null;
            $this->endDate = null;

            $this->parseDateRange($this->dateRange);
        }

        if ($selectedUserId !== null) {
            $this->selectedUserId = $selectedUserId;
        }

        // Force refresh để test
        $this->dispatch('$refresh');
    }

    /**
     * Mount method - widget sẽ nhận filter state từ parent qua events
     */
    public function mount()
    {
        // Thiết lập default nếu chưa có filter từ parent
        if (!$this->dateRange) {
            $now = \Carbon\Carbon::now();
            $this->dateRange = $now->copy()->subDays(30)->format('d/m/Y H:i') . ' - ' . $now->format('d/m/Y H:i');
        }

        $this->parseDateRange($this->dateRange);
    }

    /**
     * Sử dụng dữ liệu từ dashboard để tính tổng
     */
    protected function getStats(): array
    {
        try {
            // Đảm bảo có date range
            if (!$this->startDate || !$this->endDate) {
                $this->parseDateRange($this->dateRange);
            }

            // Removed cache for real-time data
            // Sử dụng logic từ dashboard để đảm bảo consistency
            $managedSellersWithStats = $this->getManagedSellersWithStats();

            if (empty($managedSellersWithStats)) {
                return $this->getEmptyStats();
            }

            // Tính tổng từ dữ liệu sellers
            $totals = $this->calculateTotalsFromSellers($managedSellersWithStats);

            return $this->buildStatsFromTotals($totals);
        } catch (\Exception $e) {
            \Log::error('Error in OverallStatsOverview widget: ' . $e->getMessage());
            return $this->getEmptyStats();
        }
    }

    /**
     * Lấy dữ liệu sellers với stats từ dashboard logic
     */
    private function getManagedSellersWithStats(): array
    {
        // Sử dụng logic tương tự dashboard
        $managedSellers = $this->getManagedSellers();

        if ($managedSellers->isEmpty()) {
            return [];
        }

        $sellersWithStats = [];

        foreach ($managedSellers as $seller) {
            $stats = $this->getSellerStats($seller->id);
            $sellersWithStats[] = [
                'seller' => $seller,
                'stats' => $stats
            ];
        }

        return $sellersWithStats;
    }

    /**
     * Tính tổng từ dữ liệu sellers
     */
    private function calculateTotalsFromSellers(array $managedSellersWithStats): array
    {
        $totals = [
            'total_orders' => 0,
            'completed_orders' => 0,
            'processing_orders' => 0,
            'cancelled_orders' => 0,
            'fulfilled_orders' => 0,
            'total_value' => 0,
            'completed_value' => 0,
            'fulfill_value' => 0,
            'total_stores' => 0,
            'active_stores' => 0,
        ];

        foreach ($managedSellersWithStats as $sellerData) {
            $stats = $sellerData['stats'];

            $totals['total_orders'] += $stats['total_orders'] ?? 0;
            $totals['completed_orders'] += $stats['completed_orders'] ?? 0;
            $totals['processing_orders'] += $stats['processing_orders'] ?? 0;
            $totals['cancelled_orders'] += $stats['cancelled_orders'] ?? 0;
            $totals['fulfilled_orders'] += $stats['fulfilled_orders'] ?? 0;
            $totals['total_value'] += $stats['total_value'] ?? 0;
            $totals['completed_value'] += $stats['completed_value'] ?? 0;
            $totals['fulfill_value'] += $stats['fulfill_value'] ?? 0;
            $totals['total_stores'] += $stats['total_stores'] ?? 0;
            $totals['active_stores'] += $stats['active_stores'] ?? 0;
        }

        return $totals;
    }

    /**
     * Build stats từ totals
     */
    private function buildStatsFromTotals(array $totals): array
    {
        $totalOrders = $totals['total_orders'];
        $completedOrders = $totals['completed_orders'];
        $fulfilledOrders = $totals['fulfilled_orders'];
        $totalValue = $totals['total_value'];

        // Calculate rates
        $completionRate = $totalOrders > 0 ? ($completedOrders / $totalOrders) * 100 : 0;
        $fulfillmentRate = $totalOrders > 0 ? ($fulfilledOrders / $totalOrders) * 100 : 0;

        return [
            // Total Orders Stat
            Stat::make('Tổng đơn hàng', number_format($totalOrders))
                ->description('Từ ' . count($this->getManagedSellers()) . ' sellers')
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->color('primary'),

            // Completed Orders Stat
            Stat::make('Đã hoàn thành', number_format($completedOrders))
                ->description(number_format($completionRate, 1) . '% tỷ lệ hoàn thành')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            // Total Value Stat
            Stat::make('Tổng giá trị', '$' . number_format($totalValue, 0))
                ->description('AOV: $' . number_format($totalOrders > 0 ? $totalValue / $totalOrders : 0, 0))
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('warning'),

            // Fulfilled Orders Stat
            Stat::make('Đã fulfill', number_format($fulfilledOrders))
                ->description(number_format($fulfillmentRate, 1) . '% tỷ lệ fulfill')
                ->descriptionIcon('heroicon-m-truck')
                ->color('info'),
        ];
    }

    /**
     * Parse date range string thành startDate và endDate (theo pattern SellerInvoice)
     */
    protected function parseDateRange($dateRange = null): void
    {
        if (empty($dateRange)) {
            // Default: 30 ngày gần nhất
            $now = \Carbon\Carbon::now();
            $this->startDate = $now->copy()->subDays(30)->startOfDay();
            $this->endDate = $now->endOfDay();
            return;
        }

        $dates = explode(' - ', $dateRange);
        if (count($dates) == 2) {
            try {
                // Try multiple formats
                $formats = [
                    'd/m/Y H:i',    // 14/05/2025 00:00 (primary format)
                    'd/m/Y h:i A',  // 14/05/2025 12:00 AM
                    'd/m/Y',        // 14/05/2025 (fallback)
                ];

                foreach ($formats as $format) {
                    try {
                        if ($format === 'd/m/Y') {
                            // For date-only format, set proper time boundaries
                            $this->startDate = \Carbon\Carbon::createFromFormat($format, trim($dates[0]))->startOfDay();
                            $this->endDate = \Carbon\Carbon::createFromFormat($format, trim($dates[1]))->endOfDay();
                        } else {
                            $this->startDate = \Carbon\Carbon::createFromFormat($format, trim($dates[0]));
                            $this->endDate = \Carbon\Carbon::createFromFormat($format, trim($dates[1]));
                        }
                        return;
                    } catch (\Exception $e) {
                        // Try next format
                        continue;
                    }
                }
            } catch (\Exception $e) {
                // Fallback nếu parse lỗi
            }
        }

        // Fallback: 30 ngày gần nhất
        $now = \Carbon\Carbon::now();
        $this->startDate = $now->copy()->subDays(30)->startOfDay();
        $this->endDate = $now->endOfDay();
    }

    /**
     * Lấy stats cho một seller (sử dụng logic từ dashboard)
     */
    private function getSellerStats($sellerId): array
    {
        // Đảm bảo có date range
        if (!$this->startDate || !$this->endDate) {
            $this->parseDateRange($this->dateRange);
        }

        // Removed cache for real-time data
        // Query orders với date range sử dụng Eloquent
        $baseQuery = Order::where('seller_id', $sellerId)
            ->whereBetween('created_at', [$this->startDate, $this->endDate]);

        $totalOrders = $baseQuery->count();
        $completedOrders = $baseQuery->where('status', OrderStatus::Completed)->count();
        $processingOrders = $baseQuery->where('status', OrderStatus::Processing)->count();
        $cancelledOrders = $baseQuery->where('status', OrderStatus::Cancelled)->count();
        $totalValue = $baseQuery->sum('total') ?? 0;
        $completedValue = $baseQuery->where('status', OrderStatus::Completed)->sum('total') ?? 0;

            // Fulfilled orders
            $fulfilledOrders = SupplierOrder::whereIn('order_id', function($query) use ($sellerId) {
                    $query->select('id')
                          ->from('orders')
                          ->where('seller_id', $sellerId)
                          ->whereBetween('created_at', [$this->startDate, $this->endDate]);
                })
                ->where('status', 'Completed')
                ->count();

            $fulfillValue = SupplierOrder::whereIn('order_id', function($query) use ($sellerId) {
                    $query->select('id')
                          ->from('orders')
                          ->where('seller_id', $sellerId)
                          ->whereBetween('created_at', [$this->startDate, $this->endDate]);
                })
                ->where('status', 'Completed')
                ->sum('base_cost');

            // Query store stats sử dụng Eloquent
            $totalStores = Store::where('owner_id', $sellerId)->count();
            $activeStores = Store::where('owner_id', $sellerId)
                ->where('status', 'active')
                ->count();

            return [
                'total_orders' => $totalOrders,
                'completed_orders' => $completedOrders,
                'processing_orders' => $processingOrders,
                'cancelled_orders' => $cancelledOrders,
                'fulfilled_orders' => $fulfilledOrders,
                'total_value' => $totalValue,
                'completed_value' => $completedValue,
                'fulfill_value' => $fulfillValue,
                'avg_order_value' => $totalOrders > 0 ? $totalValue / $totalOrders : 0,
                'total_stores' => $totalStores,
                'active_stores' => $activeStores,
                'completion_rate' => $totalOrders > 0 ? ($completedOrders / $totalOrders) * 100 : 0,
                'fulfillment_rate' => $totalOrders > 0 ? ($fulfilledOrders / $totalOrders) * 100 : 0,
        ];
    }

    /**
     * Helper methods
     */
    private function getManagedSellers()
    {
        // Sử dụng trait HasUserFilter để filter users theo role
        $query = User::role('Seller')
            ->select(['users.id', 'users.name', 'users.email', 'users.created_at'])
            ->with(['sellerLevel:id,name,code'])
            ->orderBy('users.name');

        // Áp dụng user filter từ trait
        $query = $this->applyUserFilter($query);

        // Áp dụng selectedUserId filter nếu có
        if ($this->selectedUserId) {
            $query->where('users.id', $this->selectedUserId);
        }

        return $query->get();
    }

    private function getEmptyStats(): array
    {
        return [
            Stat::make('Tổng đơn hàng', '0')
                ->description('Chưa có dữ liệu')
                ->descriptionIcon('heroicon-m-information-circle')
                ->color('gray'),
            
            Stat::make('Đã hoàn thành', '0')
                ->description('Chưa có dữ liệu')
                ->descriptionIcon('heroicon-m-information-circle')
                ->color('gray'),
            
            Stat::make('Tổng giá trị', '$0.00')
                ->description('Chưa có dữ liệu')
                ->descriptionIcon('heroicon-m-information-circle')
                ->color('gray'),
            
            Stat::make('Đã fulfill', '0')
                ->description('Chưa có dữ liệu')
                ->descriptionIcon('heroicon-m-information-circle')
                ->color('gray'),
        ];
    }


}
