<?php

namespace App\Filament\App\Widgets;

use App\Models\SupplierOrder;
use App\Models\Store;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class BankPayoutStatsWidget extends ChartWidget
{
    protected static ?string $heading = 'Bank Payout & Fulfillment Costs (6 Months)';
    protected static ?int $sort = 4;
    protected static ?string $maxHeight = '400px';

    // Thêm polling để tự động cập nhật
    protected static ?string $pollingInterval = '15m';

    protected int | string | array $columnSpan = [
        'default' => 2,  // Mobile: chiếm 2 cột (full width)
        'sm' => 2,       // Small: chiếm 2 cột (full width)
        'md' => 2,       // Medium: chiếm 2 cột
        'lg' => 2,       // Large: chiếm 2 cột (bên phải)
        'xl' => 2,       // XL: chiếm 2 cột (bên phải)
    ];

    /**
     * Lấy dữ liệu cho ChartWidget
     */
    protected function getData(): array
    {
        $user = auth()->user();
        $isSuperAdmin = $user && $user->hasRole('super_admin');

        // Chỉ cache khi user là super_admin
        if ($isSuperAdmin) {
            return Cache::remember('bank_payout_chart_6m_widget_super_admin', 2 * 60 * 60, function () {
                return $this->calculateChartData();
            });
        }

        // Không cache cho user khác
        return $this->calculateChartData();
    }

    /**
     * Tính toán dữ liệu biểu đồ theo format ChartWidget
     */
    private function calculateChartData(): array
    {
        $chartData = $this->getChartDataFor6Months();

        return [
            'datasets' => [
                [
                    'label' => 'Fulfillment Costs ($)',
                    'data' => $chartData['fulfill'],
                    'borderColor' => '#FF6384',
                    'backgroundColor' => 'rgba(255, 99, 132, 0.1)',
                    'tension' => 0.3,
                    'fill' => true,
                ],
                [
                    'label' => 'Bank Payout ($)',
                    'data' => $chartData['bankPayout'],
                    'borderColor' => '#4CAF50',
                    'backgroundColor' => 'rgba(76, 175, 80, 0.1)',
                    'tension' => 0.3,
                    'fill' => true,
                ]
            ],
            'labels' => $chartData['months'],
        ];
    }

    /**
     * Lấy dữ liệu biểu đồ cho 6 tháng gần đây
     */
    private function getChartDataFor6Months(): array
    {
        $now = Carbon::now();
        $months = [];
        $fulfillData = [];
        $bankPayoutData = [];
        $profitData = [];

        // Lấy dữ liệu cho 6 tháng gần đây
        for ($i = 5; $i >= 0; $i--) {
            $monthStart = $now->copy()->subMonths($i)->startOfMonth();
            $monthEnd = $now->copy()->subMonths($i)->endOfMonth();

            // Tên tháng định dạng MM/YYYY
            $months[] = $monthStart->format('m/Y');

            // Tính fulfill amount
            $fulfillAmount = SupplierOrder::where('status', '!=', 'Cancelled')
                ->whereBetween('created_at', [$monthStart, $monthEnd])
                ->sum('base_cost') ?? 0;

            // Tính bank payout amount
            $bankPayoutAmount = DB::table('payout_transactions')
                ->join('stores', function($join) {
                    $join->on('stores.bank_account', '=', 'payout_transactions.card_no')
                         ->whereNotNull('stores.bank_account')
                         ->where('stores.bank_account', '!=', '');
                })
                ->where('payout_transactions.type', 'Receive')
                ->where('payout_transactions.status', 'Success')
                ->whereBetween('payout_transactions.time', [$monthStart, $monthEnd])
                ->sum('payout_transactions.amount') ?? 0;

            $fulfillData[] = round($fulfillAmount, 2);
            $bankPayoutData[] = round($bankPayoutAmount, 2);
            $profitData[] = round($bankPayoutAmount - $fulfillAmount, 2);
        }

        return [
            'months' => $months,
            'fulfill' => $fulfillData,
            'bankPayout' => $bankPayoutData,
            'profit' => $profitData,
        ];
    }

    /**
     * Loại biểu đồ
     */
    protected function getType(): string
    {
        return 'line';
    }

    /**
     * Tùy chọn biểu đồ theo style OrderFulfillmentChart
     */
    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'title' => [
                        'display' => true,
                        'text' => 'Amount ($)'
                    ],
                    'beginAtZero' => true,
                ],
            ],
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
            'plugins' => [
                'legend' => [
                    'position' => 'top',
                ],
                'title' => [
                    'display' => true,
                ],
            ],
        ];
    }

    /**
     * Xóa cache của widget (chỉ cho super_admin)
     */
    public static function clearCache(): void
    {
        Cache::forget('bank_payout_chart_6m_widget_super_admin');
    }

    /**
     * Kiểm tra quyền xem widget
     */
    public static function canView(): bool
    {
        $user = auth()->user();

        // Debug user roles
        if ($user) {
            \Log::info('BankPayoutStatsWidget - User roles: ' . $user->getRoleNames()->implode(', '));
        } else {
            \Log::info('BankPayoutStatsWidget - No authenticated user');
        }

        return $user && ($user->hasRole('super_admin') || $user->hasRole('Leader') || $user->hasRole('Seller'));
    }
}
