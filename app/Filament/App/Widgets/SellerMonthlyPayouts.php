<?php


namespace App\Filament\App\Widgets;

use App\Models\PayoutTransaction;
use App\Models\SupplierOrder;
use App\Models\User;
use Filament\Widgets\Widget;
use Carbon\Carbon;

class SellerMonthlyPayouts extends Widget
{
    protected static string $view = 'filament.app.widgets.seller-monthly-payouts';

    public $metrics = [];
    public function getColumnSpan(): int|string|array
    {
        return 'full';
    }
    public function mount()
    {
        $this->metrics = $this->getSellerMetrics();
    }

    protected function getSellerMetrics()
    {
        $metrics = [];
        $sellers = User::role(['Seller', 'super_admin'])->get();

        foreach ($sellers as $seller) {
            $monthlyMetrics = [
                'avatar' => $seller->avatar, // Assuming you have an avatar field in your User model
                'name' => $seller->name,
                'payouts' => $this->getMonthlyPayouts($seller->id),
                'baseCosts' => $this->getMonthlyBaseCosts($seller->id),
                'profits' => [],
            ];

            foreach ($monthlyMetrics['payouts'] as $month => $payout) {
                $baseCost = $monthlyMetrics['baseCosts'][$month] ?? 0;
                $monthlyMetrics['profits'][$month] = $payout - $baseCost;
            }

            $metrics[] = $monthlyMetrics;
        }

        return $metrics;
    }

    protected function getMonthlyPayouts($sellerId)
    {
        $payouts = [];
        for ($i = 0; $i < 4; $i++) {
            $date = Carbon::now()->subMonths($i);
            $payouts[$date->format('F Y')] = PayoutTransaction::whereHas('store', function ($query) use ($sellerId) {
                $query->where('owner_id', $sellerId);
            })
            ->whereMonth('time', $date->month)
            ->whereYear('time', $date->year)
            ->sum('amount');
        }
        return $payouts;
    }

    protected function getMonthlyBaseCosts($sellerId)
    {
        $baseCosts = [];
        for ($i = 0; $i < 4; $i++) {
            $date = Carbon::now()->subMonths($i);
            $baseCosts[$date->format('F Y')] = SupplierOrder::where('seller_id', $sellerId)
                ->whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->sum('base_cost');
        }
        return $baseCosts;
    }
}
