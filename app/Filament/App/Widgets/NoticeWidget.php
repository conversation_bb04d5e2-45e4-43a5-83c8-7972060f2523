<?php

namespace App\Filament\App\Widgets;

use Filament\Widgets\Widget;
use App\Models\Store;
use Illuminate\Support\Facades\Auth;

class NoticeWidget extends Widget
{
    protected static string $view = 'filament.widgets.notice-widget';
    public function getColumnSpan(): int|string|array
    {
        return 'full';
    }
    public $notices = [];

    public function mount()
    {
        $this->notices = $this->getNotices();
    }

    protected function getNotices()
    {
        $notices = [];

        // Thêm thông báo về Proxy
        $storesWithoutProxy = Store::where('type', 'Tiktok')
            ->whereDoesntHave('proxy')
            ->get();

        if ($storesWithoutProxy->isNotEmpty()) {
            foreach ($storesWithoutProxy as $store) {
                $notices[] = [
                    'message' => "Store <strong>{$store->name}</strong> has not updated proxy.",
                    'link' => route('filament.app.resources.stores.edit', $store->id),
                    'link_text' => 'Edit Store'
                ];
            }
        }

        // Bạn có thể thêm các thông báo khác ở đây

        return $notices;
    }
}
