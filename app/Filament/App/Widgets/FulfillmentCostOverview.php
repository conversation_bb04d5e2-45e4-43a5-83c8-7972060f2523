<?php

namespace App\Filament\App\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\SupplierOrder;
use App\Enums\SupplierOrderStatus;
use Carbon\Carbon;

use Illuminate\Support\Facades\DB;

class FulfillmentCostOverview extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';
    protected static ?int $sort = 3;
    

    protected function getStats(): array
    {
        return $this->calculateFulfillmentStats();
    }

    /**
     * Tính toán các thống kê chi phí fulfill với queries tối ưu
     */
    private function calculateFulfillmentStats(): array
    {
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();
        
        // Sử dụng Eloquent để tính toán thống kê fulfillment
        $ordersToday = SupplierOrder::whereDate('created_at', $today)->count();
        $costToday = SupplierOrder::whereDate('created_at', $today)->sum('base_cost') ?? 0;

        // Thống kê hôm qua
        $yesterday = Carbon::yesterday();
        $ordersYesterday = SupplierOrder::whereDate('created_at', $yesterday)->count();
        $costYesterday = SupplierOrder::whereDate('created_at', $yesterday)->sum('base_cost') ?? 0;

        $ordersThisWeek = SupplierOrder::where('created_at', '>=', $thisWeek)->count();
        $costThisWeek = SupplierOrder::where('created_at', '>=', $thisWeek)->sum('base_cost') ?? 0;

        // Thống kê tuần trước
        $lastWeekStart = Carbon::now()->subWeek()->startOfWeek();
        $lastWeekEnd = Carbon::now()->subWeek()->endOfWeek();
        $ordersLastWeek = SupplierOrder::whereBetween('created_at', [$lastWeekStart, $lastWeekEnd])->count();
        $costLastWeek = SupplierOrder::whereBetween('created_at', [$lastWeekStart, $lastWeekEnd])->sum('base_cost') ?? 0;

        $ordersThisMonth = SupplierOrder::where('created_at', '>=', $thisMonth)->count();
        $costThisMonth = SupplierOrder::where('created_at', '>=', $thisMonth)->sum('base_cost') ?? 0;

        // Thống kê tháng trước
        $lastMonthStart = Carbon::now()->subMonth()->startOfMonth();
        $lastMonthEnd = Carbon::now()->subMonth()->endOfMonth();
        $ordersLastMonth = SupplierOrder::whereBetween('created_at', [$lastMonthStart, $lastMonthEnd])->count();
        $costLastMonth = SupplierOrder::whereBetween('created_at', [$lastMonthStart, $lastMonthEnd])->sum('base_cost') ?? 0;

        return [
            Stat::make('Hôm nay', number_format($ordersToday) . ' đơn | $' . number_format($costToday, 2))
                ->description('Hôm qua: ' . number_format($ordersYesterday) . ' đơn | $' . number_format($costYesterday, 2))
                ->color('success'),

            Stat::make('Tuần này', number_format($ordersThisWeek) . ' đơn | $' . number_format($costThisWeek, 2))
                ->description('Tuần trước: ' . number_format($ordersLastWeek) . ' đơn | $' . number_format($costLastWeek, 2))
                ->color('info'),

            Stat::make('Tháng này', number_format($ordersThisMonth) . ' đơn | $' . number_format($costThisMonth, 2))
                ->description('Tháng trước: ' . number_format($ordersLastMonth) . ' đơn | $' . number_format($costLastMonth, 2))
                ->color('warning'),
        ];
    }

    /**
     * Lấy dữ liệu biểu đồ xu hướng fulfillment trong 7 ngày qua
     */
    private function getFulfillmentTrendChart(): array
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i)->startOfDay();
            $cost = SupplierOrder::whereDate('created_at', $date)
                ->sum('base_cost');
            $data[] = round($cost, 2);
        }

        return $data;
    }

    /**
     * Kiểm tra quyền xem widget
     */
    public static function canView(): bool
    {
        $user = auth()->user();
        return $user && ($user->hasRole('super_admin') || $user->hasRole('Leader') || $user->hasRole('Seller'));
    }

    /**
     * Lấy heading cho widget
     */
    protected function getHeading(): ?string
    {
        return 'Chi phí Fulfillment';
    }

    /**
     * Lấy description cho widget
     */
    protected function getDescription(): ?string
    {
        return 'Tổng quan về chi phí fulfillment và số lượng đơn';
    }
}
