<?php

namespace App\Filament\App\Widgets;

use App\Models\Order;
use App\Models\Store;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class StoreOrdersWidget extends BaseWidget
{
    protected static ?string $heading = 'Top Store Orders (Tháng này)';
    protected static ?int $sort = 3;

    protected int | string | array $columnSpan =  [
        'default' => 2,  // Mobile: chiếm 2 cột (full width)
        'sm' => 2,       // Small: chiếm 2 cột (full width)
        'md' => 2,       // Medium: chiếm 2 cột (full width)
        'lg' => 3,       // Large: chiếm 3 cột (full width)
        'xl' => 2,       // XL: chiếm 4 cột (full width)
    ];


    public $selectedPeriod = 'this_month';


    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Store Name')
                    ->sortable()
                    ->weight('bold')
                    ->color('primary')
                    ->url(fn ($record) => route('filament.app.resources.orders.index', [
                        'tableFilters[store_id][value]' => $record->id
                    ]))
                    ->openUrlInNewTab(),

                Tables\Columns\TextColumn::make('owner.name')
                    ->label('Owner')
                    ->sortable()
                    ->color('gray'),

                Tables\Columns\TextColumn::make('order_count')
                    ->label('Orders')
                    ->sortable()
                    ->alignCenter()
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('total_revenue')
                    ->label('Revenue')
                    ->money('USD')
                    ->sortable()
                    ->alignEnd()
                    ->weight('bold')
                    ->color('primary'),
            ])
            ->defaultSort('order_count', 'desc')
            ->paginated(false)
            ->striped();
    }

    protected function getTableQuery(): Builder
    {
        $user = Auth::user();
        $dateRange = $this->getDateRange();

        // Subquery để tính order count và revenue sử dụng Eloquent
        $orderStats = Order::selectRaw('store_id, COUNT(*) as order_count, SUM(total) as total_revenue')
            ->whereBetween('created_at', $dateRange)
            ->when(!$user->hasRole('super_admin'), function ($query) use ($user) {
                $query->whereHas('store', function ($q) use ($user) {
                    $q->where('owner_id', $user->id);
                });
            })
            ->groupBy('store_id')
            ->havingRaw('COUNT(*) > 0');

        // Main query với join
        return Store::query()
            ->with('owner')
            ->joinSub($orderStats, 'order_stats', function ($join) {
                $join->on('stores.id', '=', 'order_stats.store_id');
            })
            ->select([
                'stores.*',
                'order_stats.order_count',
                'order_stats.total_revenue'
            ])
            ->limit(5);
    }

    protected function getDateRange(): array
    {
        $now = Carbon::now();
        return match ($this->selectedPeriod) {
            'today' => [$now->copy()->startOfDay(), $now->copy()->endOfDay()],
            'yesterday' => [$now->copy()->subDay()->startOfDay(), $now->copy()->subDay()->endOfDay()],
            'this_week' => [$now->copy()->startOfWeek(), $now->copy()->endOfWeek()],
            'this_month' => [$now->copy()->startOfMonth(), $now->copy()->endOfMonth()],
            'last_month' => [$now->copy()->subMonth()->startOfMonth(), $now->copy()->subMonth()->endOfMonth()],
            default => [$now->copy()->startOfWeek(), $now->copy()->endOfWeek()],
        };
    }

    /**
     * Kiểm tra quyền xem widget
     */
    public static function canView(): bool
    {
        $user = auth()->user();
        return $user && ($user->hasRole('super_admin') || $user->hasRole('Leader') || $user->hasRole('Seller'));
    }
}
