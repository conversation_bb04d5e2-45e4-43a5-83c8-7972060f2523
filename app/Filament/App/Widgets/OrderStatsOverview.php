<?php

namespace App\Filament\App\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Order;
use App\Enums\OrderStatus;
use Carbon\Carbon;


class OrderStatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';
    protected static ?int $sort = 2;

    protected int | string | array $columnSpan = [
        'default' => 2,  // Mobile: chiếm 2 cột (full width)
        'sm' => 2,       // Small: chiếm 2 cột (full width)
        'md' => 2,       // Medium: chiếm 2 cột (full width)
        'lg' => 3,       // Large: chiếm 3 cột (full width)
        'xl' => 4,       // XL: chiếm 4 cột (full width)
    ];


    protected function getStats(): array
    {
        return $this->calculateOrderStats();
    }

    /**
     * Tính toán các thống kê đơn hàng với Eloquent queries
     */
    private function calculateOrderStats(): array
    {
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();

        // Sử dụng Eloquent để tính toán thống kê hôm nay
        $ordersToday = Order::onlyMe()
            ->whereDate('created_at', $today)
            ->count();

        $revenueToday = Order::onlyMe()
            ->whereDate('created_at', $today)
            ->sum('total') ?? 0;

        // Thống kê hôm qua
        $yesterday = Carbon::yesterday();
        $ordersYesterday = Order::onlyMe()
            ->whereDate('created_at', $yesterday)
            ->count();

        $revenueYesterday = Order::onlyMe()
            ->whereDate('created_at', $yesterday)
            ->sum('total') ?? 0;

        // Thống kê tuần này
        $ordersThisWeek = Order::onlyMe()
            ->where('created_at', '>=', $thisWeek)
            ->count();

        $revenueThisWeek = Order::onlyMe()
            ->where('created_at', '>=', $thisWeek)
            ->sum('total') ?? 0;

        // Thống kê tuần trước
        $lastWeekStart = Carbon::now()->subWeek()->startOfWeek();
        $lastWeekEnd = Carbon::now()->subWeek()->endOfWeek();
        $ordersLastWeek = Order::onlyMe()
            ->whereBetween('created_at', [$lastWeekStart, $lastWeekEnd])
            ->count();

        $revenueLastWeek = Order::onlyMe()
            ->whereBetween('created_at', [$lastWeekStart, $lastWeekEnd])
            ->sum('total') ?? 0;

        // Thống kê tháng này
        $ordersThisMonth = Order::onlyMe()
            ->where('created_at', '>=', $thisMonth)
            ->count();

        $revenueThisMonth = Order::onlyMe()
            ->where('created_at', '>=', $thisMonth)
            ->sum('total') ?? 0;

        // Thống kê tháng trước
        $lastMonthStart = Carbon::now()->subMonth()->startOfMonth();
        $lastMonthEnd = Carbon::now()->subMonth()->endOfMonth();
        $ordersLastMonth = Order::onlyMe()
            ->whereBetween('created_at', [$lastMonthStart, $lastMonthEnd])
            ->count();

        $revenueLastMonth = Order::onlyMe()
            ->whereBetween('created_at', [$lastMonthStart, $lastMonthEnd])
            ->sum('total') ?? 0;

        return [
            Stat::make('Hôm nay', number_format($ordersToday) . ' đơn | $' . number_format($revenueToday, 2))
                ->description('Hôm qua: ' . number_format($ordersYesterday) . ' đơn | $' . number_format($revenueYesterday, 2))
                ->color('success'),

            Stat::make('Tuần này', number_format($ordersThisWeek) . ' đơn | $' . number_format($revenueThisWeek, 2))
                ->description('Tuần trước: ' . number_format($ordersLastWeek) . ' đơn | $' . number_format($revenueLastWeek, 2))
                ->color('info'),

            Stat::make('Tháng này', number_format($ordersThisMonth) . ' đơn | $' . number_format($revenueThisMonth, 2))
                ->description('Tháng trước: ' . number_format($ordersLastMonth) . ' đơn | $' . number_format($revenueLastMonth, 2))
                ->color('primary'),
        ];
    }




    /**
     * Lấy dữ liệu biểu đồ xu hướng đơn hàng trong 7 ngày qua
     */
    private function getOrderTrendChart(): array
    {
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i)->startOfDay();
            $count = Order::onlyMe()
                ->whereDate('created_at', $date)
                ->count();
            $data[] = $count;
        }

        return $data;
    }

    /**
     * Kiểm tra quyền xem widget
     */
    public static function canView(): bool
    {
        $user = auth()->user();
        return $user && ($user->hasRole('super_admin') || $user->hasRole('Leader') || $user->hasRole('Seller'));
    }

    /**
     * Lấy heading cho widget
     */
    protected function getHeading(): ?string
    {
        return 'Thống kê Đơn hàng';
    }

    /**
     * Lấy description cho widget
     */
    protected function getDescription(): ?string
    {
        return 'Tổng quan về tình trạng đơn hàng và doanh thu';
    }


}