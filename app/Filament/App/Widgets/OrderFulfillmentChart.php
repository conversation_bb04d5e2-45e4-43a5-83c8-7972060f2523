<?php

namespace App\Filament\App\Widgets;

use App\Models\Order;
use App\Models\SupplierOrder;
use App\Models\TikTokPayment;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;


class OrderFulfillmentChart extends ChartWidget
{
    protected static ?string $heading = 'Orders, Fulfillment Costs & TikTok Payouts (3 Months)';
    protected static ?int $sort = 3;
    protected static ?string $maxHeight = '400px';
    
    // Thêm polling để tự động cập nhật (tuỳ chọn)
    protected static ?string $pollingInterval = '15m'; // cập nhật mỗi 15 phút

    protected int | string | array $columnSpan = [
        'default' => 'full',
    ];



    protected function getData(): array
    {
        $user = Auth::user();

        // Thay đổi từ 30 ngày thành 3 tháng
        $months = 3;
        $endDate = Carbon::now()->endOfDay(); // Đảm bảo lấy đến cuối ngày hiện tại
        $startDate = Carbon::now()->subMonths($months)->startOfDay();

        // Generate date range
        $dates = collect();
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $dates->push($date->format('Y-m-d'));
        }

            // Base query for orders with preloading relationships
            $ordersQuery = Order::query()
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as count')
                )
                ->with(['store', 'seller']) // Preload relationships
                ->where('store_order_status', '!=', 'Cancelled')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('date');

            // Base query for supplier orders with preloading
            $supplierOrdersQuery = SupplierOrder::query()
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COALESCE(SUM(base_cost), 0) as total_cost')
                )
                ->with(['supplier', 'store']) // Preload relationships
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('date');

            // Base query for revenue with preloading
            $revenueQuery = Order::query()
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COALESCE(SUM(total), 0) as total_revenue')
                )
                ->with(['store', 'seller']) // Preload relationships
                ->where('store_order_status', '!=', 'Cancelled')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('date');

            // Base query for TikTok Payouts
            $tiktokPayoutQuery = TikTokPayment::query()
                ->select(
                    DB::raw('DATE(paid_time) as date'),
                    DB::raw('COALESCE(SUM(settlement_amount), 0) as total_payout')
                )
                ->with(['store']) // Preload relationships
                ->where('status', 'PAID')
                ->whereBetween('paid_time', [$startDate, $endDate])
                ->groupBy('date');

        // Apply role-based filtering
        if (!$user->hasRole('super_admin')) {
            $sellerId = $user->id;
            $ordersQuery->where('seller_id', $sellerId);
            $supplierOrdersQuery->where('seller_id', $sellerId);
            $revenueQuery->where('seller_id', $sellerId);
            $tiktokPayoutQuery->where('owner_id', $sellerId);
        }

        // Get the data
        $orders = $ordersQuery->pluck('count', 'date');
        $costs = $supplierOrdersQuery->pluck('total_cost', 'date');
        $revenues = $revenueQuery->pluck('total_revenue', 'date');
        $tiktokPayouts = $tiktokPayoutQuery->pluck('total_payout', 'date');

        // Fill in missing dates with zeros
        $orderData = $dates->map(fn ($date) => $orders[$date] ?? 0)->values();
        $costData = $dates->map(fn ($date) => round(floatval($costs[$date] ?? 0), 2))->values();
        $revenueData = $dates->map(fn ($date) => round(floatval($revenues[$date] ?? 0), 2))->values();
        $tiktokPayoutData = $dates->map(fn ($date) => round(floatval($tiktokPayouts[$date] ?? 0), 2))->values();

        return [
            'datasets' => [
                [
                    'label' => 'Daily Orders',
                    'data' => $orderData->toArray(),
                    'borderColor' => '#36A2EB',
                    'backgroundColor' => 'rgba(54, 162, 235, 0.1)',
                    'tension' => 0.3,
                    'fill' => true,
                ],
                [
                    'label' => 'Fulfillment Costs ($)',
                    'data' => $costData->toArray(),
                    'borderColor' => '#FF6384',
                    'backgroundColor' => 'rgba(255, 99, 132, 0.1)',
                    'tension' => 0.3,
                    'fill' => true,
                    'yAxisID' => 'y1',
                ],
                [
                    'label' => 'Revenue ($)',
                    'data' => $revenueData->toArray(),
                    'borderColor' => '#4CAF50',
                    'backgroundColor' => 'rgba(76, 175, 80, 0.1)',
                    'tension' => 0.3,
                    'fill' => true,
                    'yAxisID' => 'y1',
                ],
                [
                    'label' => 'TikTok Payouts ($)',
                    'data' => $tiktokPayoutData->toArray(),
                    'borderColor' => '#FF9800',
                    'backgroundColor' => 'rgba(255, 152, 0, 0.1)',
                    'tension' => 0.3,
                    'fill' => true,
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $dates->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'title' => [
                        'display' => true,
                        'text' => 'Orders Count'
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'title' => [
                        'display' => true,
                        'text' => 'Costs, Revenue & Payouts ($)'
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ],
            ],
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
            'plugins' => [
                'legend' => [
                    'position' => 'top',
                ],
                'title' => [
                    'display' => true,
                ],
            ],
        ];
    }
    public static function canView(): bool
    {
        $user = auth()->user();
        
        // Kiểm tra quyền user
        if ($user->hasAnyRole(['super_admin', 'User Manager', 'Fulfillment Manager'])) {
            return true;
        } else {
            return false;
        }
    }
}