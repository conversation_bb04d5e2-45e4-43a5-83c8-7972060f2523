<?php

namespace App\Filament\App\Widgets;

use Filament\Widgets\Widget;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Support\Facades\Auth;

class UserActivityWidget extends Widget
{
    protected static string $view = 'filament.widgets.user-activity-widget';
    public function getColumnSpan(): int|string|array
    {
        return 1;
    }
    public function getViewData(): array
    {
        $userId = Auth::id();
        $activities = Activity::where(function ($query) use ($userId) {
                $query->where('causer_id', $userId)
                      ->orWhereHasMorph('subject', ['App\Models\Order', 'App\Models\Design'], function ($query) use ($userId) {
                          $query->where('seller_id', $userId);
                      });
            })
            ->latest()
            ->take(30) // limit to the latest 10 entries
            ->get();

        return [
            'activities' => $activities,
        ];
    }
}
