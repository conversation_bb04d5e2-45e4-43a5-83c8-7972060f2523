<?php

namespace App\Filament\App\Widgets;

use App\Services\FulfillmentSpeedService;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Livewire\Attributes\On;

class FulfillmentStatsOverview extends BaseWidget
{
    use InteractsWithPageFilters;

    // Public property để nhận dateRange từ page
    public ?string $dateRange = null;

    // Cache key để tránh reload không cần thiết
    public ?string $lastDateRange = null;

    // Chiếm toàn bộ chiều rộng
    protected int | string | array $columnSpan = 'full';

    // Tắt auto refresh để tránh reload không cần thiết
    protected static ?string $pollingInterval = null;

    /**
     * Listen for date range updates from parent page
     */
    #[On('dashboard-date-updated')]
    public function updateDateRange($dateRange)
    {
        $this->dateRange = $dateRange;
        $this->lastDateRange = null; // Clear cache
    }

    protected function getStats(): array
    {
        // Lấy date range từ filters hoặc mặc định
        $startDate = $this->getStartDate();
        $endDate = $this->getEndDate();

        // Tạo cache key từ date range
        $currentDateRange = $startDate?->format('Y-m-d') . '_' . $endDate?->format('Y-m-d');

        // Skip nếu date range không thay đổi (sử dụng cache từ service)
        if ($this->lastDateRange === $currentDateRange) {
            // Vẫn gọi service nhưng service sẽ return từ cache
        }

        try {
            $fulfillmentService = new FulfillmentSpeedService();
            $data = $fulfillmentService->getFulfillmentAnalytics($startDate, $endDate);

            if (isset($data['error'])) {
                return $this->getErrorStats();
            }

            $summary = $data['summary'] ?? [];
            $weekendAnalysis = $data['weekend_analysis'] ?? [];

            $stats = [
                Stat::make('Tổng Fulfillments', $summary['total_fulfillments'] ?? 0)
                    ->description('Tổng số fulfillments trong kỳ')
                    ->descriptionIcon('heroicon-m-cube')
                    ->color('primary')
                    ->chart($this->getFulfillmentTrendData($data)),

                Stat::make('Trung bình/ngày', $summary['avg_daily_fulfillments'] ?? 0)
                    ->description('Số fulfillments trung bình mỗi ngày')
                    ->descriptionIcon('heroicon-m-chart-bar')
                    ->color('success'),

                Stat::make('Thời gian TB', $summary['avg_fulfill_time_display'] ?? '0 giờ')
                    ->description('Thời gian fulfill trung bình')
                    ->descriptionIcon('heroicon-m-clock')
                    ->color($this->getTimeColor($summary['avg_fulfill_time_hours'] ?? 0)),

                Stat::make('Cuối tuần chậm hơn', $this->formatWeekendPercentage($weekendAnalysis))
                    ->description('So với ngày thường')
                    ->descriptionIcon($this->getWeekendIcon($weekendAnalysis['weekend_slower_percentage'] ?? 0))
                    ->color($this->getWeekendColor($weekendAnalysis['weekend_slower_percentage'] ?? 0)),
            ];

            // Update cache key
            $this->lastDateRange = $currentDateRange;

            return $stats;

        } catch (\Exception $e) {
            return $this->getErrorStats();
        }
    }

    private function getStartDate(): Carbon
    {
        $dateRange = $this->dateRange ?? $this->filters['dateRange'] ?? null;

        if ($dateRange) {
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                return Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
            }
        }

        return Carbon::now()->startOfMonth()->startOfDay();
    }

    private function getEndDate(): Carbon
    {
        $dateRange = $this->dateRange ?? $this->filters['dateRange'] ?? null;

        if ($dateRange) {
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                return Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
            }
        }

        return Carbon::now()->endOfDay();
    }

    private function getFulfillmentTrendData(array $data): array
    {
        if (!isset($data['chart_data']['fulfillments'])) {
            return [];
        }

        // Lấy 7 điểm dữ liệu gần nhất cho mini chart
        $fulfillments = $data['chart_data']['fulfillments'];
        return array_slice($fulfillments, -7);
    }

    private function getTimeColor(float $hours): string
    {
        if ($hours <= 2) return 'success';
        if ($hours <= 6) return 'warning';
        return 'danger';
    }

    private function formatWeekendPercentage(array $weekendAnalysis): string
    {
        $percentage = $weekendAnalysis['weekend_slower_percentage'] ?? 0;
        
        if ($percentage > 0) {
            return "+{$percentage}%";
        } elseif ($percentage < 0) {
            return "{$percentage}%";
        }
        
        return "0%";
    }

    private function getWeekendIcon(float $percentage): string
    {
        if ($percentage > 10) return 'heroicon-m-exclamation-triangle';
        if ($percentage > 0) return 'heroicon-m-arrow-trending-up';
        return 'heroicon-m-check-circle';
    }

    private function getWeekendColor(float $percentage): string
    {
        if ($percentage > 10) return 'danger';
        if ($percentage > 0) return 'warning';
        return 'success';
    }

    private function getErrorStats(): array
    {
        return [
            Stat::make('Lỗi', 'Không thể tải dữ liệu')
                ->description('Vui lòng thử lại sau')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger'),
        ];
    }

    public static function canView(): bool
    {
        $user = auth()->user();
        return $user && $user->hasRole(['super_admin', 'Fulfillment Manager', 'User Manager']);
    }
}
