<?php

namespace App\Filament\App\Widgets;

use App\Models\Order;
use Carbon\Carbon;
use Filament\Widgets\Widget;

class OrderSpikeDetailsTable extends Widget
{
    protected static string $view = 'filament.app.widgets.order-spike-details-table';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    public function getSpikeData(): array
    {
        return $this->getTableQuery()->toArray();
    }

    protected function getTableQuery()
    {
        // Phân tích 7 ngày gần đây từ database thực tế - tất cả khung 6h
        $last7Days = [];
        $now = Carbon::now();

        // Định nghĩa các khung 6 tiếng
        $timeSlots = [
            '00:00-06:00' => ['start' => '00:00:00', 'end' => '05:59:59'],
            '06:00-12:00' => ['start' => '06:00:00', 'end' => '11:59:59'],
            '12:00-18:00' => ['start' => '12:00:00', 'end' => '17:59:59'],
            '18:00-24:00' => ['start' => '18:00:00', 'end' => '23:59:59'],
        ];

        for ($i = 6; $i >= 0; $i--) {
            $date = $now->copy()->subDays($i);
            $dateString = $date->toDateString();

            // Lấy tổng orders cả ngày
            $totalDayOrders = Order::whereDate('created_at', $dateString)->count();

            // Phân tích từng khung 6h
            $slotData = [];
            foreach ($timeSlots as $slotName => $timeRange) {
                $ordersInSlot = Order::whereDate('created_at', $dateString)
                    ->whereTime('created_at', '>=', $timeRange['start'])
                    ->whereTime('created_at', '<=', $timeRange['end'])
                    ->count();

                $slotData[$slotName] = $ordersInSlot;
            }

            // Tìm khung có nhiều orders nhất
            $peakSlot = array_keys($slotData, max($slotData))[0] ?? '00:00-06:00';
            $peakSlotOrders = max($slotData);

            // Tìm giờ có nhiều orders nhất trong ngày
            $hourlyData = [];
            for ($hour = 0; $hour < 24; $hour++) {
                $count = Order::whereDate('created_at', $dateString)
                    ->whereTime('created_at', '>=', sprintf('%02d:00:00', $hour))
                    ->whereTime('created_at', '<=', sprintf('%02d:59:59', $hour))
                    ->count();
                $hourlyData[$hour] = $count;
            }
            $peakHour = array_keys($hourlyData, max($hourlyData))[0] ?? 0;

            $last7Days[] = [
                'date' => $dateString,
                'total_day' => $totalDayOrders,
                'peak_hour' => $peakHour,
                'peak_slot' => $peakSlot,
                'peak_slot_orders' => $peakSlotOrders,
                'slot_00_06' => $slotData['00:00-06:00'],
                'slot_06_12' => $slotData['06:00-12:00'],
                'slot_12_18' => $slotData['12:00-18:00'],
                'slot_18_24' => $slotData['18:00-24:00'],
            ];
        }

        // Tính baseline cho từng khung 6h từ dữ liệu thực tế
        $baselines = [
            '00:00-06:00' => array_sum(array_column($last7Days, 'slot_00_06')) / 7,
            '06:00-12:00' => array_sum(array_column($last7Days, 'slot_06_12')) / 7,
            '12:00-18:00' => array_sum(array_column($last7Days, 'slot_12_18')) / 7,
            '18:00-24:00' => array_sum(array_column($last7Days, 'slot_18_24')) / 7,
        ];

        // Tạo collection với dữ liệu tính toán cho từng ngày
        $data = collect($last7Days)->map(function ($item) use ($baselines) {
            // Phân tích khung có biến động cao nhất trong ngày
            $slotAnalysis = [];
            $maxGrowthRate = -999;
            $maxAlertLevel = 'NORMAL';
            $criticalSlots = [];

            foreach (['00:00-06:00', '06:00-12:00', '12:00-18:00', '18:00-24:00'] as $slot) {
                // Tạo key đúng format: slot_00_06, slot_06_12, etc.
                $slotKeyMap = [
                    '00:00-06:00' => 'slot_00_06',
                    '06:00-12:00' => 'slot_06_12',
                    '12:00-18:00' => 'slot_12_18',
                    '18:00-24:00' => 'slot_18_24',
                ];
                $slotKey = $slotKeyMap[$slot];

                $orders = $item[$slotKey];
                $baseline = $baselines[$slot];
                $growthRate = $baseline > 0 ? (($orders - $baseline) / $baseline) * 100 : 0;

                if ($growthRate >= 50) {
                    $alertLevel = 'CRITICAL';
                } elseif ($growthRate >= 30) {
                    $alertLevel = 'WARNING';
                } elseif ($growthRate >= 15) {
                    $alertLevel = 'INFO';
                } elseif ($growthRate <= -30) {
                    $alertLevel = 'LOW';
                } else {
                    $alertLevel = 'NORMAL';
                }

                $slotAnalysis[$slot] = [
                    'orders' => $orders,
                    'baseline' => round($baseline, 2),
                    'growth_rate' => round($growthRate, 1),
                    'alert_level' => $alertLevel
                ];

                if ($growthRate > $maxGrowthRate) {
                    $maxGrowthRate = $growthRate;
                    $maxAlertLevel = $alertLevel;
                }

                if ($alertLevel === 'CRITICAL' || $alertLevel === 'WARNING') {
                    $criticalSlots[] = $slot . ' (' . $orders . ')';
                }
            }

            if ($item['total_day'] == 0) {
                $notes = 'Không có orders cả ngày';
            } elseif (count($criticalSlots) > 0) {
                $notes = 'Spike tại: ' . implode(', ', $criticalSlots);
            } elseif ($maxGrowthRate >= 15) {
                $notes = 'Tăng nhẹ tại khung ' . $item['peak_slot'];
            } elseif ($maxGrowthRate <= -30) {
                $notes = 'Giảm mạnh - cần kiểm tra';
            } else {
                $notes = 'Bình thường - phân bố đều';
            }

            return (object) [
                'date' => $item['date'],
                'day_of_week' => Carbon::parse($item['date'])->dayOfWeek,
                'total_day_orders' => $item['total_day'],
                'peak_hour' => $item['peak_hour'],
                'peak_slot' => $item['peak_slot'],
                'peak_slot_orders' => $item['peak_slot_orders'],
                'max_growth_rate' => round($maxGrowthRate, 1),
                'max_alert_level' => $maxAlertLevel,
                'slot_analysis' => $slotAnalysis,
                'critical_slots_count' => count($criticalSlots),
                'notes' => $notes,
            ];
        });

        // Trả về collection trực tiếp thay vì fake query builder
        return $data;
    }

    protected function getTableRecords(): array
    {
        return $this->getTableQuery()->toArray();
    }

    private function getHourlyDataForDate(string $date): array
    {
        // Tạo dữ liệu giả cho biểu đồ theo giờ
        $orders = Order::whereDate('created_at', $date)->get();
        
        $hourlyData = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $count = $orders->filter(function($order) use ($hour) {
                return $order->created_at->setTimezone('Asia/Ho_Chi_Minh')->hour == $hour;
            })->count();
            
            $hourlyData[] = [
                'hour' => sprintf('%02d:00', $hour),
                'orders' => $count,
                'is_peak' => in_array($hour, [15, 16, 17]),
            ];
        }
        
        return $hourlyData;
    }
}
