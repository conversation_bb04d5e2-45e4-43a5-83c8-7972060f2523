<?php

namespace App\Filament\Imports;

use App\Models\SupplierProduct;
use App\Models\Supplier;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Filament\Forms;
use Illuminate\Validation\ValidationException;

class SupplierProductImporter extends Importer
{
    protected static ?string $model = SupplierProduct::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('sku')
                ->label('SKU')
                ->rules(['required']),

            ImportColumn::make('price')
                ->label('Price')
                ->rules(['required', 'numeric', 'min:0']),

            ImportColumn::make('variant_id')
                ->label('Variant ID')
                ->rules(['required']),

            ImportColumn::make('style')
                ->label('Style')
                ->rules(['required']),

            ImportColumn::make('color')
                ->label('Color')
                ->rules(['required']),

            ImportColumn::make('size')
                ->label('Size')
                ->rules(['required']),

            ImportColumn::make('active')
                ->label('Active')
                ->boolean(),

            ImportColumn::make('tiktok')
                ->label('Tiktok')
                ->boolean(),

            ImportColumn::make('smart_sku_id')
                ->label('Smart SKU')
                ->rules(['nullable', 'exists:smart_skus,id'])
                ->relationship(),
        ];
    }

    public static function getOptionsFormComponents(): array
    {
        return [
            Forms\Components\Select::make('supplier_id')
                ->label('Select Supplier')
                ->options(Supplier::pluck('name', 'id'))
                ->required()
                ->searchable(),
        ];
    }

    public function resolveRecord(): ?SupplierProduct
    {
        return SupplierProduct::query()
            ->where('sku', $this->data['sku'])
            ->where('supplier_id', $this->options['supplier_id'])
            ->first() ?? new SupplierProduct();
    }

    protected function beforeCreate(): void
    {
        // Set default values and supplier_id
        $this->data['supplier_id'] = $this->options['supplier_id'];
        $this->data['active'] = $this->data['active'] ?? true;
        $this->data['tiktok'] = $this->data['tiktok'] ?? false;
        
        // Check for duplicate SKU within the same supplier
        $exists = SupplierProduct::query()
            ->where('sku', $this->data['sku'])
            ->where('supplier_id', $this->options['supplier_id'])
            ->exists();

        if ($exists) {
            throw ValidationException::withMessages([
                'sku' => "SKU '{$this->data['sku']}' already exists for this supplier",
            ]);
        }
    }

    protected function beforeUpdate(): void
    {
        // Set default values if not provided
        $this->data['active'] = $this->data['active'] ?? true;
        $this->data['tiktok'] = $this->data['tiktok'] ?? false;

        // Check for duplicate SKU within the same supplier, excluding current record
        $exists = SupplierProduct::query()
            ->where('sku', $this->data['sku'])
            ->where('supplier_id', $this->options['supplier_id'])
            ->where('id', '!=', $this->record->id)
            ->exists();

        if ($exists) {
            throw ValidationException::withMessages([
                'sku' => "SKU '{$this->data['sku']}' already exists for this supplier",
            ]);
        }
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $count = number_format($import->successful_rows);
        return "Successfully imported {$count} supplier products.";
    }
}