<?php

namespace App\Filament\Imports;

use App\Models\PayoutTransaction;
use Exception;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Filament\Actions\Imports\Exceptions\RowImportFailedException;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;


class PayoutTransactionImporter extends Importer
{
    protected static ?string $model = PayoutTransaction::class;

    public function getJobQueue(): ?string
    {
        return 'default';
    }
    public function getJobConnection(): ?string
    {
        return 'redis';
    }

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('time')
                ->requiredMapping()
                ->rules(['required']),
            ImportColumn::make('currency')
                ->requiredMapping()
                ->rules(['required', 'max:3']),
            ImportColumn::make('amount')
                ->requiredMapping()
                ->numeric()
                ->rules(['required']),
            ImportColumn::make('transaction_id')->label("TransactionId")
                ->requiredMapping()
                ->rules(['required', 'max:255'])
                ->guess(['TransactionId', 'transaction_id']),
            ImportColumn::make('card_no')->label("CardNo")
                ->requiredMapping()
                ->rules(['required', 'max:255'])
                ->guess(['CardNo', 'card_no']),
            ImportColumn::make('fee')
                ->numeric(),
            ImportColumn::make('net')
                ->requiredMapping()
                ->numeric()
                ->rules(['required']),
            ImportColumn::make('type')
                ->requiredMapping()
                ->rules(['required', 'max:50']),
            ImportColumn::make('from_to')->label('From/To')
                ->requiredMapping()
                ->rules(['required', 'max:255'])
                ->guess(['From/To', 'from_to']),
            ImportColumn::make('status')
                ->requiredMapping()
                ->rules(['required', 'max:50']),
            ImportColumn::make('note'),

        ];
    }

    public function resolveRecord(): ?PayoutTransaction
    {
        try {
            // Kiểm tra transaction_id có tồn tại không
            $transactionId = $this->data['transaction_id'] ?? null;

            if (empty($transactionId)) {
                throw new RowImportFailedException("Transaction ID is required.");
            }

            // Kiểm tra duplicate transaction_id
            if (PayoutTransaction::where('transaction_id', $transactionId)->exists()) {
                throw new RowImportFailedException("Duplicate transaction_id found: {$transactionId}");
            }

            // Kiểm tra business logic validation trước khi tạo record
            $fromTo = $this->data['from_to'] ?? '';
            $type = $this->data['type'] ?? '';
            $status = $this->data['status'] ?? '';

            // Validate theo business logic trong model
            if (!empty($fromTo) &&
                (strpos($fromTo, 'TikTok Inc') !== false ||
                 strpos($fromTo, 'TikTok Shop') !== false ||
                 strpos($fromTo, 'TikTok') !== false)) {

                if ($type !== 'Receive' || $status !== 'Success') {
                    throw new RowImportFailedException(
                        "Invalid TikTok transaction: type must be 'Receive' and status must be 'Success'. " .
                        "Current: type='{$type}', status='{$status}'"
                    );
                }
            }

            return new PayoutTransaction();

        } catch (RowImportFailedException $e) {
            // Re-throw RowImportFailedException để Filament xử lý đúng cách
            throw $e;
        } catch (Exception $e) {
            Log::error('PayoutTransaction Import Error: ' . $e->getMessage(), [
                'data' => $this->data,
                'trace' => $e->getTraceAsString()
            ]);
            throw new RowImportFailedException("Import error: " . $e->getMessage());
        }
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your payout transaction import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }





}
