<?php

namespace App\Filament\Imports;

use App\Enums\FileLocation;
use App\Models\Design;
use App\Models\Order;
use App\Models\OrderItem;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class OrderImporter extends Importer
{
    protected static ?string $model = Order::class;





    public static function getColumns(): array
    {
        $colums = [
            ImportColumn::make('order_number')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('product_variant_id')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('quantity')
                ->requiredMapping()
                ->rules(['required', 'max:255']),

            //ship
            ImportColumn::make('shipping_first_name')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('shipping_last_name')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('shipping_address_line1')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('shipping_address_line2')
                ->rules(['max:255']),
            ImportColumn::make('shipping_city')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('shipping_zip')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('shipping_region')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('shipping_country')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('shipping_email')
                ->requiredMapping()
                ->rules(['max:255']),
            ImportColumn::make('shipping_phone')
                ->requiredMapping()
                ->rules(['max:255']),


            ImportColumn::make('note')
                ->requiredMapping()
                ->rules(['max:255']),


            //design
            ImportColumn::make('mockup')
            ->requiredMapping()
            ->rules(['max:255']),

        ];
        foreach (FileLocation::cases() as $location) {

            $colums[] = ImportColumn::make($location->name)
                ->requiredMapping()
                ->rules(['max:255']);
        }
        return $colums;
    }
    protected function handleRecord(array $record, array $options = []): void
    {
        dd($record);
        // Validate dữ liệu record trước khi xử lý
        Validator::make($record, [
            'order_number' => ['required', 'max:255', Rule::unique(Order::class, 'order_number')],
            'product_variant_id' => ['required', 'exists:product_variants,id'],
            'quantity' => ['required', 'integer'],
            'shipping_first_name' => ['required', 'max:255'],
            'shipping_last_name' => ['required', 'max:255'],
            // Thêm các rule validation cho các trường khác
        ])->validate();

        // Tạo Order mới
        $order = new Order([
            'order_number' => $record['order_number'],
            'store_id' => $options['store_id'], // Sử dụng store_id được chọn từ form
            'shipping_first_name' => $record['shipping_first_name'],
            'shipping_last_name' => $record['shipping_last_name'],
            'shipping_email' => $record['shipping_email'],
            'shipping_phone' => $record['shipping_phone'],
            'shipping_country' => $record['shipping_country'],
            'shipping_region' => $record['shipping_region'],
            'shipping_address_line1' => $record['shipping_address_line1'],
            'shipping_address_line2' => $record['shipping_address_line2'],
            'shipping_city' => $record['shipping_city'],
            'shipping_zip' => $record['shipping_zip'],
            'note' => $record['note'],
            // Các trường khác
        ]);
        $order->save();

        // Tạo OrderItem và liên kết với Order
        $orderItem = new OrderItem([
            'order_id' => $order->id,
            'product_variant_id' => $record['product_variant_id'],
            'quantity' => $record['quantity'],
            // Các trường khác nếu có
        ]);
        $orderItem->save();

        $design = new Design([
            'order_item_id' => $orderItem->id,
            'mockup' => $record['mockup'],
            'file_chest' => $record['file_chest'],
            'file_left_arm' => $record['file_left_arm'],
            'file_right_arm' => $record['file_right_arm'],
            'file_collar' => $record['file_collar'],
            // Có thể thêm các trường khác tùy thuộc vào cấu trúc của bạn
        ]);
        $design->save();
    }
    protected function beforeFill(): void
    {
        // Runs before the validated CSV data for a row is filled into a model instance.
    }
    protected function afterValidate(): void
    {
        unset($this->data['product_variant_id']);
        unset($this->data['quantity']);
        unset($this->data['note']);
        //order
        unset($this->data['mockup']);
        unset($this->data['user']);
        foreach (FileLocation::cases() as $location) {
            unset($this->data[$location->name]);
        }
   
     
    }

    protected function beforeValidate(): void
    {
    }
    public function resolveRecord(): ?Order
    {
        //$this->data['store_id'] = $this->options['store_id'];
        // Thêm các trường khác cần thiết
        $record = $this->data;
        $options = $this->options;
        $order = new  Order(
            [
                'order_number' => $record['order_number'],
                'store_id' => $options['store_id'], // Sử dụng store_id được chọn từ form
                'shipping_first_name' => $record['shipping_first_name'],
                'shipping_last_name' => $record['shipping_last_name'],
                'shipping_email' => $record['shipping_email'],
                'shipping_phone' => $record['shipping_phone'],
                'shipping_country' => $record['shipping_country'],
                'shipping_region' => $record['shipping_region'],
                'shipping_address_line1' => $record['shipping_address_line1'],
                'shipping_address_line2' => $record['shipping_address_line2'],
                'shipping_city' => $record['shipping_city'],
                'shipping_zip' => $record['shipping_zip'],

            ]
        );
        $order->save();
        foreach ($record['orderItems'] as $item) {
            // Tạo OrderItem và liên kết với Order

            $orderItem = new OrderItem([
                'order_id' => $order->id,
                'product_variant_id' => $record['product_variant_id'],
                'quantity' => $record['quantity'],
                'note' => $record['note'],
                // Các trường khác nếu có
            ]);
            $orderItem->save();
            $design = $item['design'];

            $newDesign = new Design([
                'name' => 'Design Order #' . $order->id .' - $Item '.$orderItem->id,
                'mockup' => $design['mockup'],
                'created_by' => $record['user'],
            ]);
            $newDesign->save();
            $orderItem->design_id =  $newDesign->id;
            $orderItem->save();
            foreach (FileLocation::cases() as $location) {
                if ($design[$location->name]) {
                    $newDesign->designFiles()->create([
                        'location' => $location->name,
                        'file_type' => "PNG",
                        'file_url' => $design[$location->name],
                    ]);
                }
            }
      
        }
        return $order;


        return Order::firstOrNew($this->data);

        return new Order();
    }
    public static function getOptionsFormComponents(): array
    {
        return [
            Select::make('store_id')
                ->required()
                ->relationship(name: 'store', titleAttribute: 'name')
                ->preload()
                ->searchable(),
        ];
    }
    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your order import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
