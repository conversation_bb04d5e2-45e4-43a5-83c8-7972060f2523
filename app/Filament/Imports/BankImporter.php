<?php

namespace App\Filament\Imports;

use App\Models\Bank;
use Exception;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Filament\Actions\Imports\Exceptions\RowImportFailedException;

class BankImporter extends Importer
{
    protected static ?string $model = Bank::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('bank_account_number')
                ->label('Bank Account Number')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
                
            ImportColumn::make('bank_name')
                ->label('Bank Name')
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('bank_location')
                ->label('Bank Location')
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('account_holders_name')
                ->label("Account Holder's Name")
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('account_type')
                ->label('Account Type')
                ->rules(['nullable', 'string']),
                
            ImportColumn::make('currency')
                ->label('Currency')
                ->rules(['nullable', 'string']),
                
            ImportColumn::make('account_nickname')
                ->label('Account Nickname')
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('account_status')
                ->label('Account Status')
                ->rules(['nullable', 'string']),
                
            ImportColumn::make('swift_code')
                ->label('Swift Code/BIC')
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('routing_number')
                ->label('9-Digit Routing Number')
                ->rules(['nullable', 'max:9']),
                
            ImportColumn::make('sort_code')
                ->label('Bank Sort Code')
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('institution_number')
                ->label('Institution Number/Bank Code')
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('transit_number')
                ->label('Transit Number/Branch Number')
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('zip_code')
                ->label('Zip Code')
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('city')
                ->label('City')
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('bank_address')
                ->label('Bank Address')
                ->rules(['nullable']),
                
            ImportColumn::make('note')
                ->label('Note')
                ->rules(['nullable']),
                
            ImportColumn::make('iban')
                ->label('IBAN')
                ->rules(['nullable', 'max:255']),
                
            ImportColumn::make('usage')
                ->label('Usage')
                ->rules(['nullable', 'max:255']),
        ];
    }

    public function resolveRecord(): ?Bank
    {
        try {
            // Lấy email từ options
            $account_email = $this->options['account_email'] ?? null;
            if (empty($account_email)) {
                throw new RowImportFailedException("Account email is required.");
            }

            // Tìm bank account đã tồn tại
            $existingBank = Bank::where('bank_account_number', $this->data['bank_account_number'])->first();
            
            if ($existingBank) {
                // Nếu tồn tại, update email và trả về record đó
                $existingBank->account_email = $account_email;
                return $existingBank;
            }

            // Nếu không tồn tại, tạo mới với owner_id và email
            $bank = new Bank();
            $bank->owner_id = Auth::id();
            $bank->account_email = $account_email;
            return $bank;
        } catch (Exception $e) {
            Log::error('Bank import error: ' . $e->getMessage());
            throw new RowImportFailedException($e->getMessage());
        }
    }

    public function mutateBeforeCreate(array $data): array
    {
        $owner_id = Auth::id();

        if (!$owner_id) {
            throw new RowImportFailedException("No authenticated user found.");
        }

        // Xử lý account_email
        $account_email = $this->options['account_email'] ?? null;
        if (empty($account_email)) {
            throw new RowImportFailedException("Account email is required.");
        }

        return [
            ...$data,
            'owner_id' => $owner_id,
            'account_email' => $account_email,
            'account_type' => isset($data['account_type']) ? strtolower($data['account_type']) : 'personal',
            'account_status' => isset($data['account_status']) 
                ? in_array(strtolower($data['account_status']), ['true', '1', 'yes', 'active', 'on'])
                : true,
            'currency' => $data['currency'] ?? 'USD',
        ];
    }

    public static function getOptionsFormComponents(): array
    {
        return [
            TextInput::make('account_email')
                ->label('Account Email')
                ->email()
                ->required()
                ->helperText('This email will be used for all imported bank accounts')
                ->rules(['required', 'email']),
        ];
    }

    public function getJobQueue(): ?string
    {
        return 'default';
    }

    public function getJobConnection(): ?string
    {
        return 'redis';
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Bank account import completed with ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
} 