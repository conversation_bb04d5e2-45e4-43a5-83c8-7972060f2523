<?php

namespace App\Exports;

use App\Models\User;
use App\Models\Seller;
use Carbon\Carbon;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Color;

class SellersExport extends Exporter
{
    protected static ?string $model = User::class;

    public static function getColumns(): array
    {
        // Lấy danh sách các tháng trong khoảng thời gian (3 tháng gần nhất)
        $endDate = Carbon::now()->endOfMonth();
        $startDate = Carbon::now()->subMonths(2)->startOfMonth();

        $months = collect();
        $currentMonth = $startDate->copy();

        while ($currentMonth->lte($endDate)) {
            $months->push($currentMonth->format('m/Y'));
            $currentMonth->addMonth();
        }

        $columns = [
            ExportColumn::make('name')
                ->label('Tên người bán'),

            ExportColumn::make('month')
                ->label('Tháng'),

            ExportColumn::make('revenue')
                ->label('Doanh thu')
                ->state(function (User $record) {
                    return $record->revenue ?? 0;
                }),

            ExportColumn::make('cost')
                ->label('Chi phí hàng')
                ->state(function (User $record) {
                    return $record->cost ?? 0;
                }),

            ExportColumn::make('income')
                ->label('Tiền về')
                ->state(function (User $record) {
                    return $record->income ?? 0;
                }),

            ExportColumn::make('commission')
                ->label('Hoa hồng 10%')
                ->state(function (User $record) {
                    return $record->commission ?? 0;
                }),

            ExportColumn::make('profit')
                ->label('Lợi nhuận')
                ->state(function (User $record) {
                    return $record->profit ?? 0;
                }),
        ];

        return $columns;
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        return 'Xuất dữ liệu người bán đã hoàn tất và sẵn sàng để tải xuống.';
    }

    // Tắt xử lý queue để tải xuống trực tiếp
    public function getChunkSize(): int
    {
        return 10000; // Tăng kích thước chunk để xử lý nhiều dữ liệu hơn trong một lần
    }

    public function getJobConnection(): ?string
    {
        return 'sync'; // Sử dụng kết nối sync thay vì queue
    }

    protected function getQuery(): Builder
    {
        // Lấy danh sách người bán
        $query = User::query()
            ->whereHas('roles', function ($query) {
                $query->where('name', 'Seller');
            });

        // Lọc theo id nếu có
        if (request()->has('id') && !empty(request()->id)) {
            $query->where('id', request()->id);
        }

        return $query;
    }

    /**
     * Ghi đè phương thức để xử lý dữ liệu trước khi xuất
     */
    protected function getRecords(): \Illuminate\Support\Collection
    {
        // Lấy danh sách người bán từ query gốc
        $sellers = $this->getQuery()->get();

        // Lấy khoảng thời gian từ request hoặc sử dụng giá trị mặc định (3 tháng gần nhất)
        $startDate = null;
        $endDate = null;

        if (request()->has('dateRange') && !empty(request()->dateRange)) {
            try {
                // Phân tích chuỗi dateRange (format: 'd/m/Y - d/m/Y')
                $dates = explode(' - ', request()->dateRange);
                if (count($dates) == 2) {
                    $startDate = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $endDate = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                }
            } catch (\Exception $e) {
                // Xử lý lỗi nếu có
                \Log::error('Lỗi khi phân tích dateRange: ' . $e->getMessage());
            }
        }

        // Nếu không có khoảng thời gian, sử dụng giá trị mặc định (3 tháng gần nhất)
        if (!$startDate || !$endDate) {
            $endDate = Carbon::now()->endOfMonth();
            $startDate = Carbon::now()->subMonths(2)->startOfMonth();
        }

        // Tạo danh sách các tháng trong khoảng thời gian
        $months = collect();
        $currentMonth = $startDate->copy();

        while ($currentMonth->lte($endDate)) {
            $months->push($currentMonth->format('m/Y'));
            $currentMonth->addMonth();
        }

        // Tạo dữ liệu tài chính cho mỗi người bán và mỗi tháng
        $results = collect();

        foreach ($sellers as $seller) {
            foreach ($months as $month) {
                // Lấy dữ liệu tài chính của tháng
                $financialData = Seller::getMonthlyFinancialData($seller, $month);

                // Tạo bản sao của seller và thêm dữ liệu tài chính
                $sellerCopy = clone $seller;
                $sellerCopy->month = $financialData['month'];
                $sellerCopy->revenue = $financialData['revenue'];
                $sellerCopy->cost = $financialData['cost'];
                $sellerCopy->income = $financialData['income'];
                $sellerCopy->commission = $financialData['commission'];
                $sellerCopy->profit = $financialData['profit'];

                $results->push($sellerCopy);
            }
        }

        return $results;
    }

    public function setUp($export): void
    {
        $export->registerEvents([
            \Maatwebsite\Excel\Events\AfterSheet::class => function (\Maatwebsite\Excel\Events\AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // Định dạng tiêu đề
                $sheet->getStyle('A1:G1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 12,
                        'color' => [
                            'rgb' => 'FFFFFF',
                        ],
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => '4F46E5', // Indigo-600
                        ],
                    ],
                ]);

                // Định dạng các cột
                $lastRow = $sheet->getHighestRow();

                // Định dạng các cột số tiền
                $moneyColumns = ['C', 'D', 'E', 'F', 'G']; // Doanh thu, Chi phí hàng, Tiền về, Hoa hồng, Lợi nhuận
                foreach ($moneyColumns as $column) {
                    for ($row = 2; $row <= $lastRow; $row++) {
                        $sheet->getStyle($column . $row)->applyFromArray([
                            'numberFormat' => [
                                'formatCode' => '$#,##0.00',
                            ],
                            'alignment' => [
                                'horizontal' => Alignment::HORIZONTAL_RIGHT,
                            ],
                        ]);
                    }
                }

                // Định dạng tất cả các ô
                $sheet->getStyle('A1:G' . $lastRow)->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['rgb' => 'E5E7EB'], // Gray-200
                        ],
                    ],
                ]);

                // Tự động điều chỉnh chiều rộng cột
                foreach (range('A', 'G') as $column) {
                    $sheet->getColumnDimension($column)->setAutoSize(true);
                }
            },
        ]);
    }
}
