<?php

namespace App\Exports;

use App\Models\Product;
use App\Models\OrderItem;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Color;

class ProductStatisticsExport extends Exporter
{
    protected static ?string $model = Product::class;

    public static function getColumns(): array
    {
        return [
            // Cột A: Thứ hạng (tự tạo)
            ExportColumn::make('ranking')
                ->label('Thứ hạng')
                ->state(function ($record): int {
                    static $index = 0;
                    return ++$index;
                }),

            // Cột B: products.id (vị trí 1 trong query)
            ExportColumn::make('id')
                ->label('ID sản phẩm'),

            // Cột C: products.name (vị trí 2 trong query)
            ExportColumn::make('name')
                ->label('Tên sản phẩm'),

            // Cột D: products.sku (vị trí 3 trong query)
            ExportColumn::make('sku')
                ->label('SKU')
                ->formatStateUsing(fn ($state) => $state ?? 'N/A'),

            // Cột E: products.price (vị trí 4 trong query)
            ExportColumn::make('price')
                ->label('Giá (VNĐ)')
                ->formatStateUsing(fn ($state) => $state ? number_format($state, 0, ',', '.') : '0'),

            // Cột F: products.type (vị trí 5 trong query)
            ExportColumn::make('type')
                ->label('Loại sản phẩm')
                ->formatStateUsing(fn ($state) => $state ?? 'N/A'),

            // Cột G: products.tiktok_product_id (vị trí 6 trong query)
            ExportColumn::make('tiktok_product_id')
                ->label('TikTok Product ID')
                ->formatStateUsing(fn ($state) => $state ?? 'N/A'),

            // Cột H: users.name as seller_name (vị trí 7 trong query)
            ExportColumn::make('seller_name')
                ->label('Người bán')
                ->formatStateUsing(fn ($state) => $state ?? 'N/A'),

            // Cột I: users.telegram_id (vị trí 8 trong query)
            ExportColumn::make('telegram_id')
                ->label('Telegram ID')
                ->formatStateUsing(fn ($state) => $state ?? 'N/A'),

            // Cột J: stores.name as store_name (vị trí 9 trong query)
            ExportColumn::make('store_name')
                ->label('Cửa hàng')
                ->formatStateUsing(fn ($state) => $state ?? 'N/A'),

            // Cột K: total_ordered (vị trí 10 trong query)
            ExportColumn::make('total_ordered')
                ->label('Tổng số lượng đặt hàng')
                ->formatStateUsing(fn ($state) => number_format($state ?? 0, 0, ',', '.')),

            // Cột L: latest_order_date (vị trí 11 trong query)
            ExportColumn::make('latest_order_date')
                ->label('Đơn hàng mới nhất')
                ->formatStateUsing(fn ($state) => $state ? \Carbon\Carbon::parse($state)->format('d/m/Y H:i') : 'N/A'),

            // Cột M: first_order_date (vị trí 12 trong query)
            ExportColumn::make('first_order_date')
                ->label('Đơn hàng đầu tiên')
                ->formatStateUsing(fn ($state) => $state ? \Carbon\Carbon::parse($state)->format('d/m/Y H:i') : 'N/A'),

            // Cột N: Tính toán từ first_order_date và latest_order_date
            ExportColumn::make('order_period_days')
                ->label('Khoảng thời gian (ngày)')
                ->state(function ($record): string {
                    if (!$record->first_order_date || !$record->latest_order_date) {
                        return '0';
                    }

                    $first = \Carbon\Carbon::parse($record->first_order_date);
                    $latest = \Carbon\Carbon::parse($record->latest_order_date);
                    $days = $first->diffInDays($latest);

                    return (string) $days;
                }),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        return 'Xuất dữ liệu thống kê sản phẩm đã hoàn tất và sẵn sàng để tải xuống.';
    }

    // Tắt xử lý queue để tải xuống trực tiếp
    public function getChunkSize(): int
    {
        return 10000; // Tăng kích thước chunk để xử lý nhiều dữ liệu hơn trong một lần
    }

    public function getJobConnection(): ?string
    {
        return 'sync'; // Sử dụng sync connection để xử lý trực tiếp
    }

    protected function getQuery(): Builder
    {
        // Query chỉ lấy những field cần thiết cho export, theo đúng thứ tự ExportColumn
        $query = OrderItem::select(
            // Thứ tự 1: products.id
            'products.id',
            // Thứ tự 2: products.name
            'products.name',
            // Thứ tự 3: products.sku
            'products.sku',
            // Thứ tự 4: products.price
            'products.price',
            // Thứ tự 5: products.type
            'products.type',
            // Thứ tự 6: products.tiktok_product_id
            'products.tiktok_product_id',
            // Thứ tự 7: users.name as seller_name
            'users.name as seller_name',
            // Thứ tự 8: users.telegram_id
            'users.telegram_id',
            // Thứ tự 9: stores.name as store_name
            'stores.name as store_name',
            // Thứ tự 10: total_ordered
            DB::raw('SUM(order_items.quantity) as total_ordered'),
            // Thứ tự 11: latest_order_date
            DB::raw('MAX(orders.created_at) as latest_order_date'),
            // Thứ tự 12: first_order_date
            DB::raw('MIN(orders.created_at) as first_order_date')
        )
        ->join('products', 'order_items.product_id', '=', 'products.id')
        ->join('orders', 'order_items.order_id', '=', 'orders.id')
        ->leftJoin('users', 'products.seller_id', '=', 'users.id')
        ->leftJoin('stores', 'products.store_id', '=', 'stores.id')
        ->whereNotIn('orders.status', ['canceled', 'returned'])
        ->groupBy(
            'products.id',
            'products.name',
            'products.sku',
            'products.price',
            'products.type',
            'products.tiktok_product_id',
            'users.name',
            'users.telegram_id',
            'stores.name'
        )
        ->orderBy('total_ordered', 'desc');

        return $query;
    }

    public function setUp($export): void
    {
        $export->registerEvents([
            \Maatwebsite\Excel\Events\AfterSheet::class => function (\Maatwebsite\Excel\Events\AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // Định dạng tiêu đề
                $sheet->getStyle('A1:N1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 12,
                        'color' => [
                            'rgb' => 'FFFFFF',
                        ],
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => '4F46E5', // Indigo-600
                        ],
                    ],
                ]);

                // Định dạng các cột
                $lastRow = $sheet->getHighestRow();

                // Định dạng cột thứ hạng (cột A)
                $rankingColumn = 'A';
                for ($row = 2; $row <= $lastRow; $row++) {
                    $ranking = $sheet->getCell($rankingColumn . $row)->getValue();

                    if ($ranking <= 3) {
                        // Top 3 - Màu vàng
                        $sheet->getStyle($rankingColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'FEF3C7'], // Yellow-100
                            ],
                            'font' => [
                                'color' => ['rgb' => 'D97706'], // Yellow-600
                                'bold' => true,
                            ],
                        ]);
                    } elseif ($ranking <= 10) {
                        // Top 10 - Màu xanh lá nhạt
                        $sheet->getStyle($rankingColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'DCFCE7'], // Green-100
                            ],
                            'font' => [
                                'color' => ['rgb' => '16A34A'], // Green-600
                            ],
                        ]);
                    }
                }

                // Định dạng cột tổng số lượng đặt hàng (cột K)
                $totalOrderedColumn = 'K';
                for ($row = 2; $row <= $lastRow; $row++) {
                    $totalOrderedValue = $sheet->getCell($totalOrderedColumn . $row)->getValue();
                    $totalOrdered = (int) str_replace([',', '.'], '', $totalOrderedValue);

                    if ($totalOrdered >= 100) {
                        // Số lượng cao - Màu xanh lá
                        $sheet->getStyle($totalOrderedColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'DCFCE7'], // Green-100
                            ],
                            'font' => [
                                'color' => ['rgb' => '16A34A'], // Green-600
                                'bold' => true,
                            ],
                        ]);
                    } elseif ($totalOrdered >= 50) {
                        // Số lượng trung bình - Màu vàng
                        $sheet->getStyle($totalOrderedColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'FEF3C7'], // Yellow-100
                            ],
                            'font' => [
                                'color' => ['rgb' => 'D97706'], // Yellow-600
                            ],
                        ]);
                    }
                }

                // Định dạng tất cả các ô
                $sheet->getStyle('A1:N' . $lastRow)->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['rgb' => 'E5E7EB'], // Gray-200
                        ],
                    ],
                ]);

                // Tự động điều chỉnh chiều rộng cột
                foreach (range('A', 'N') as $column) {
                    $sheet->getColumnDimension($column)->setAutoSize(true);
                }
            },
        ]);
    }
}
