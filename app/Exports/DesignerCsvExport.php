<?php

namespace App\Exports;

use App\Models\DesignJob;
use App\Models\User;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class DesignerCsvExport implements FromQuery, WithHeadings, WithMapping
{
    protected $designer;
    protected $startDate;
    protected $endDate;
    protected $exportType;

    public function __construct(User $designer, Carbon $startDate, Carbon $endDate, string $exportType = 'design_jobs')
    {
        $this->designer = $designer;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->exportType = $exportType;
    }

    public function query()
    {
        return match ($this->exportType) {
            'all_jobs' => $this->getAllJobsQuery(),
            'statistics' => $this->getStatisticsQuery(),
            default => throw new \InvalidArgumentException("Invalid export type: {$this->exportType}")
        };
    }

    protected function getAllJobsQuery(): Builder
    {
        // Tr<PERSON> về tất cả jobs của designer trong kho<PERSON><PERSON> thời gian được chọn
        return DesignJob::query()
            ->where('designer_id', $this->designer->id)
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->orderBy('created_at', 'desc');
    }



    protected function getStatisticsQuery(): Builder
    {
        // For statistics, we'll return a single row with aggregated data
        return DesignJob::query()
            ->where('designer_id', $this->designer->id)
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->selectRaw('
                designer_id,
                COUNT(*) as total_jobs,
                SUM(price) as total_earnings,
                SUM(rush_fee) as total_rush_fees,
                SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_jobs,
                SUM(CASE WHEN status = "completed" THEN price ELSE 0 END) as completed_earnings,
                SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_jobs,
                SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned_jobs,
                SUM(CASE WHEN is_rush = 1 THEN 1 ELSE 0 END) as rush_jobs
            ')
            ->groupBy('designer_id')
            ->limit(1);
    }

    public function headings(): array
    {
        return match ($this->exportType) {
            'all_jobs' => [
                'Job ID',
                'Job Type',
                'Status',
                'Price (USD)',
                'Rush Fee (USD)',
                'Total (USD)',
                'Is Rush',
                'Created Date',
                'Assigned Date',
                'Completed Date',
                'Duration (Days)',
                'Description',
                'Designer Note'
            ],
            'statistics' => [
                'Designer Name',
                'Period',
                'Total Jobs',
                'Completed Jobs',
                'Pending Jobs',
                'Assigned Jobs',
                'Rush Jobs',
                'Total Earnings (USD)',
                'Completed Earnings (USD)',
                'Rush Fees (USD)',
                'Completion Rate (%)'
            ],
            default => []
        };
    }

    public function map($record): array
    {
        return match ($this->exportType) {
            'all_jobs' => [
                $record->id ?? 'N/A',
                $record->job_type ? $record->job_type->value : 'N/A',
                $this->formatDesignStatus($record->status),
                number_format($record->price ?? 0, 2),
                number_format($record->rush_fee ?? 0, 2),
                number_format(($record->price ?? 0) + ($record->rush_fee ?? 0), 2),
                $record->is_rush ? 'Yes' : 'No',
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A',
                $record->assigned_at ? $record->assigned_at->format('d/m/Y H:i') : 'N/A',
                $record->completed_at ? $record->completed_at->format('d/m/Y H:i') : 'N/A',
                $this->calculateDuration($record),
                $record->description ?? 'N/A',
                $record->designer_note ?? 'N/A'
            ],
            'statistics' => [
                $this->designer->name,
                $this->startDate->format('d/m/Y') . ' - ' . $this->endDate->format('d/m/Y'),
                $record->total_jobs ?? 0,
                $record->completed_jobs ?? 0,
                $record->pending_jobs ?? 0,
                $record->assigned_jobs ?? 0,
                $record->rush_jobs ?? 0,
                number_format($record->total_earnings ?? 0, 2),
                number_format($record->completed_earnings ?? 0, 2),
                number_format($record->total_rush_fees ?? 0, 2),
                $record->total_jobs > 0 ? number_format(($record->completed_jobs / $record->total_jobs) * 100, 1) : '0.0'
            ],
            default => []
        };
    }

    protected function formatDesignStatus($status): string
    {
        $statusValue = $status instanceof \App\Enums\DesignJobStatus ? $status->value : (string) $status;
        return match ($statusValue) {
            'pending' => 'Chờ xử lý',
            'assigned' => 'Đã phân công',
            'in_progress' => 'Đang thực hiện',
            'under_review' => 'Chờ duyệt',
            'needs_revision' => 'Cần sửa',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy',
            default => $statusValue,
        };
    }

    protected function calculateDuration($record): string
    {
        if (!$record->created_at) {
            return 'N/A';
        }

        $endDate = $record->completed_at ?? Carbon::now();
        $duration = $record->created_at->diffInDays($endDate);

        return $duration . ' days';
    }

    public function getFileName(): string
    {
        $designerName = str_replace(' ', '_', $this->designer->name);
        $dateRange = $this->startDate->format('d_m_Y') . '_to_' . $this->endDate->format('d_m_Y');
        
        $typeNames = [
            'all_jobs' => 'all_jobs_comprehensive',
            'statistics' => 'statistics',
        ];
        
        $typeName = $typeNames[$this->exportType] ?? $this->exportType;
        
        return "designer_{$designerName}_{$typeName}_{$dateRange}.csv";
    }
}
