<?php

namespace App\Exports;

use App\Services\SellerInvoiceService;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Carbon\Carbon;

class SellerCsvExport implements FromQuery, WithHeadings, WithMapping
{
    protected $sellerInvoiceService;
    protected $exportType;
    protected $filters;
    protected $seller;
    protected $dateRange;

    public function __construct(SellerInvoiceService $sellerInvoiceService, string $exportType, array $filters = [])
    {
        $this->sellerInvoiceService = $sellerInvoiceService;
        $this->exportType = $exportType;
        $this->filters = $filters;
        $this->seller = $sellerInvoiceService->seller;
        $this->dateRange = $sellerInvoiceService->startDate->format('d-m-Y') . '_' . $sellerInvoiceService->endDate->format('d-m-Y');
    }

    public function query()
    {
        $query = match ($this->exportType) {
            'orders' => $this->sellerInvoiceService->getOrdersQuery(),
            'order_items' => $this->sellerInvoiceService->getOrderItemsQuery(),
            'fulfillment_cost' => $this->sellerInvoiceService->getFulfillmentCostQuery(),
            'advertising_cost' => $this->sellerInvoiceService->getAdvertisingCostQuery(),
            'design_cost' => $this->sellerInvoiceService->getDesignCostQuery(),
            'printing_cost' => $this->sellerInvoiceService->getPrintingCostQuery(),
            'bank_payout' => $this->sellerInvoiceService->getBankPayoutQuery(),
            'previous_month_loss' => $this->sellerInvoiceService->getPreviousMonthLossQuery(),
            default => throw new \InvalidArgumentException("Invalid export type: {$this->exportType}")
        };

        // Áp dụng filters (không áp dụng cho previous_month_loss vì đây là dữ liệu đặc biệt)
        if (isset($this->filters['status']) && $this->filters['status'] && $this->exportType !== 'previous_month_loss') {
            $query->where('status', $this->filters['status']);
        }

        return $query;
    }

    public function headings(): array
    {
        return match ($this->exportType) {
            'orders' => [
                'Ma don hang',
                'Cua hang',
                'Trang thai',
                'Tong tien (USD)',
                'Ngay tao'
            ],
            'order_items' => [
                'Ma don hang',
                'Ten san pham',
                'SKU san pham',
                'So luong',
                'Don gia (USD)',
                'Thanh tien (USD)',
                'Trang thai don hang',
                'Ngay tao'
            ],
            'fulfillment_cost' => [
                'Ma don hang',
                'Nha cung cap',
                'Trang thai',
                'Chi phi (USD)',
                'Ngay tao'
            ],
            'advertising_cost' => [
                'So tien (USD)',
                'Trang thai',
                'Mo ta',
                'Ngay tao'
            ],
            'design_cost' => [
                'Ten thiet ke',
                'Designer',
                'Gia co ban (USD)',
                'Phi gap (USD)',
                'Tong chi phi (USD)',
                'Trang thai',
                'Ngay hoan thanh'
            ],
            'printing_cost' => [
                'Loai phoi',
                'So luong',
                'Gia/don vi (USD)',
                'Tong chi phi (USD)',
                'Trang thai',
                'Ngay tao'
            ],
            'bank_payout' => [
                'Loai giao dich',
                'So tien (USD)',
                'So tai khoan',
                'Trang thai',
                'Thoi gian'
            ],
            'previous_month_loss' => [
                'Thang bao cao',
                'Doanh thu gop (USD)',
                'Phi nen tang (USD)',
                'Doanh thu rong (USD)',
                'Tong chi phi (USD)',
                'Loi nhuan gop (USD)',
                'Loi nhuan rong (USD)',
                'Lo thang truoc (USD)',
                'Loi nhuan dieu chinh (USD)',
                'Luong co ban (USD)',
                'Hoa hong (USD)',
                'Tong thu nhap seller (USD)',
                'Tien dang giu (USD)',
                'Trang thai',
                'Ngay tao',
                'Ngay cap nhat'
            ],
            default => []
        };
    }

    public function map($record): array
    {
        return match ($this->exportType) {
            'orders' => [
                $record->order_number ?? 'N/A',
                $record->store->name ?? 'N/A',
                $this->formatOrderStatus($record->status),
                number_format($record->total ?? 0, 2),
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A'
            ],
            'order_items' => [
                $record->order->order_number ?? 'N/A',
                $record->product->name ?? 'N/A',
                $record->product->sku ?? 'N/A',
                $record->quantity ?? 0,
                number_format($record->price ?? 0, 2),
                number_format($record->total ?? 0, 2),
                $this->formatOrderStatus($record->order->status ?? ''),
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A'
            ],
            'fulfillment_cost' => [
                $record->order->order_number ?? 'N/A',
                $record->supplier->name ?? 'N/A',
                $this->formatSupplierOrderStatus($record->status),
                number_format($record->base_cost ?? 0, 2),
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A'
            ],
            'advertising_cost' => [
                number_format($record->amount ?? 0, 2),
                $this->formatAdvertisingStatus($record->status),
                $record->description ?? 'N/A',
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A'
            ],
            'design_cost' => [
                $record->design->name ?? 'N/A',
                $record->designer->name ?? 'N/A',
                number_format($record->price ?? 0, 2),
                number_format($record->rush_fee ?? 0, 2),
                number_format(($record->price ?? 0) + ($record->rush_fee ?? 0), 2),
                $this->formatDesignStatus($record->status),
                $record->completed_at ? $record->completed_at->format('d/m/Y H:i') : 'N/A'
            ],
            'printing_cost' => [
                $record->blank->name ?? 'N/A',
                $record->quantity ?? 0,
                number_format($record->unit_price ?? 0, 2),
                number_format($record->calculateProductionCost(), 2),
                $this->formatPrintingStatus($record->status),
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A'
            ],
            'bank_payout' => [
                $this->formatPayoutType($record->type),
                number_format($record->amount ?? 0, 2),
                '****' . substr($record->card_no ?? '', -4),
                $this->formatPayoutStatus($record->status),
                $record->time ? Carbon::parse($record->time)->format('d/m/Y H:i') : 'N/A'
            ],
            'previous_month_loss' => [
                $record->month ? Carbon::parse($record->month)->format('m/Y') : 'N/A',
                number_format($record->gross_revenue ?? 0, 2),
                number_format($record->platform_fees ?? 0, 2),
                number_format($record->net_revenue ?? 0, 2),
                number_format($record->total_cost ?? 0, 2),
                number_format($record->gross_profit ?? 0, 2),
                number_format($record->net_profit ?? 0, 2),
                number_format($record->previous_loss ?? 0, 2),
                number_format($record->adjusted_profit ?? 0, 2),
                number_format($record->base_salary ?? 0, 2),
                number_format($record->commission ?? 0, 2),
                number_format($record->total_salary ?? 0, 2),
                number_format($record->payout_on_hold ?? 0, 2),
                $this->formatFinanceStatus($record->status ?? 'pending'),
                $record->created_at ? Carbon::parse($record->created_at)->format('d/m/Y H:i') : 'N/A',
                $record->updated_at ? Carbon::parse($record->updated_at)->format('d/m/Y H:i') : 'N/A'
            ],
            default => []
        };
    }

    // Status formatting methods (same as SellerExport)
    protected function formatOrderStatus($status): string
    {
        $statusValue = $status instanceof \App\Enums\OrderStatus ? $status->value : (string) $status;
        return match ($statusValue) {
            'Processing' => 'Dang xu ly',
            'AwaitingShipment' => 'Cho van chuyen',
            'Completed' => 'Hoan thanh',
            'OnHold' => 'Tam giu',
            'Refunded' => 'Hoan tien',
            'Cancelled' => 'Da huy',
            default => $statusValue,
        };
    }

    protected function formatSupplierOrderStatus($status): string
    {
        $statusValue = $status instanceof \App\Enums\SupplierOrderStatus ? $status->value : (string) $status;
        return match ($statusValue) {
            'Pending' => 'Cho xu ly',
            'AwaitingShipment' => 'Cho van chuyen',
            'InProducing' => 'Dang san xuat',
            'Completed' => 'Hoan thanh',
            'OnHold' => 'Tam giu',
            'Cancelled' => 'Da huy',
            'Refunded' => 'Hoan tien',
            default => $statusValue,
        };
    }

    protected function formatAdvertisingStatus($status): string
    {
        return match ((string) $status) {
            'pending' => 'Cho duyet',
            'approved' => 'Da duyet',
            'rejected' => 'Tu choi',
            default => (string) $status,
        };
    }

    protected function formatDesignStatus($status): string
    {
        $statusValue = $status instanceof \App\Enums\DesignJobStatus ? $status->value : (string) $status;
        return match ($statusValue) {
            'pending' => 'Cho xu ly',
            'assigned' => 'Da phan cong',
            'in_progress' => 'Dang thuc hien',
            'under_review' => 'Cho duyet',
            'needs_revision' => 'Can sua',
            'completed' => 'Hoan thanh',
            'cancelled' => 'Da huy',
            default => $statusValue,
        };
    }

    protected function formatPrintingStatus($status): string
    {
        $statusValue = is_object($status) && method_exists($status, '__toString') ? (string) $status : (string) $status;
        return match ($statusValue) {
            'pending' => 'Cho xu ly',
            'in_production' => 'Dang san xuat',
            'completed' => 'Hoan thanh',
            'rejected' => 'Tu choi',
            default => $statusValue,
        };
    }

    protected function formatPayoutType($type): string
    {
        return match ((string) $type) {
            'Receive' => 'Nhan tien',
            'Send' => 'Gui tien',
            default => (string) $type,
        };
    }

    protected function formatPayoutStatus($status): string
    {
        return match ((string) $status) {
            'Success' => 'Thanh cong',
            'Pending' => 'Cho xu ly',
            'Failed' => 'That bai',
            default => (string) $status,
        };
    }

    protected function formatFinanceStatus($status): string
    {
        return match ((string) $status) {
            'pending' => 'Cho xu ly',
            'completed' => 'Hoan thanh',
            'cancelled' => 'Da huy',
            default => (string) $status,
        };
    }

    public function getFileName(): string
    {
        $tableNames = [
            'orders' => 'don_hang',
            'order_items' => 'chi_tiet_don_hang',
            'fulfillment_cost' => 'chi_phi_di_don',
            'advertising_cost' => 'chi_phi_quang_cao',
            'design_cost' => 'chi_phi_thiet_ke',
            'printing_cost' => 'chi_phi_in_ao',
            'bank_payout' => 'bank_payout',
            'previous_month_loss' => 'lo_thang_truoc',
        ];

        $tableName = $tableNames[$this->exportType] ?? $this->exportType;
        $sellerName = str_replace(' ', '_', $this->seller->name);

        return "{$tableName}_{$sellerName}_{$this->dateRange}.csv";
    }
}
