<?php

namespace App\Exports;

use App\Enums\TiktokShopStatus;
use App\Models\Store;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Color;

class StoresExport extends Exporter
{
    protected static ?string $model = Store::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('note')
                ->label('Ghi chú'),

            ExportColumn::make('note_bank')
                ->label('Ghi chú ngân hàng'),

            ExportColumn::make('name')
                ->label('Tên cửa hàng'),

            ExportColumn::make('seller.name')
                ->label('<PERSON>ư<PERSON><PERSON> bán (Owner)'),

            ExportColumn::make('seller.telegram_id')
                ->label('Telegram ID'),

            ExportColumn::make('tiktok_shop_code')
                ->label('Mã cửa hàng'),

            ExportColumn::make('tiktok_shop_status')
                ->label('Trạng thái TikTok')
                ->formatStateUsing(function ($state) {
                    if ($state instanceof TiktokShopStatus) {
                        return match ($state) {
                            TiktokShopStatus::Live => 'Live',
                            TiktokShopStatus::NotConnected => 'Not Connected',
                            TiktokShopStatus::Suspended => 'Suspended',
                            default => (string) $state,
                        };
                    }

                    return (string) $state;
                }),

            ExportColumn::make('orders_count')
                ->label('Số đơn hàng')
                ->state(function (Store $record): int {
                    return $record->orders()->count();
                }),

            ExportColumn::make('tiktok_total_reserve_amount')
                ->label('Tổng số tiền đang giữ')
                ->formatStateUsing(fn ($state) => $state ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('tiktok_paid_amount')
                ->label('Số tiền đã paid')
                ->formatStateUsing(fn ($state) => $state ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('payout_info')
                ->label('Payout Info')
                ->state(function (Store $record): string {
                    return '' . ($record->tiktok_payout_day ?? 'N/A') . ' days (' . ($record->shop_health ?? '0/48') . ')';
                }),

            ExportColumn::make('tiktok_payout_on_hold')
                ->label('Payout on hold')
                ->formatStateUsing(fn ($state) => $state ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('last_sync_tiktok')
                ->label('Thời gian đồng bộ cuối')
                ->formatStateUsing(fn ($state) => $state ? $state->format('d/m/Y H:i') : 'N/A'),

            ExportColumn::make('card')
                ->label('4 số cuối TK TikTok')
                ->formatStateUsing(fn ($state) => $state ? '****' . substr($state, -4) : 'N/A'),

            ExportColumn::make('bank_account')
                ->label('4 số cuối TK đã lưu')
                ->formatStateUsing(fn ($state) => $state ? '****' . substr($state, -4) : 'N/A'),

            ExportColumn::make('app_partner_id')
                ->label('Liên kết API App')
                ->formatStateUsing(fn ($state) => $state ? 'Đã liên kết' : 'Chưa liên kết'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        return 'Xuất dữ liệu cửa hàng đã hoàn tất và sẵn sàng để tải xuống.';
    }

    // Tắt xử lý queue để tải xuống trực tiếp
    public function getChunkSize(): int
    {
        return 10000; // Tăng kích thước chunk để xử lý nhiều dữ liệu hơn trong một lần
    }

    public function getJobConnection(): ?string
    {
        return 'sync'; // Sử dụng kết nối sync thay vì queue
    }

    protected function getQuery(): Builder
    {
        return Store::query()
            ->with(['seller', 'storeMetric', 'tiktokHoldStatements'])
            ->withCount('orders');
    }

    public function setUp($export): void
    {
        $export->registerEvents([
            \Maatwebsite\Excel\Events\AfterSheet::class => function (\Maatwebsite\Excel\Events\AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // Định dạng tiêu đề
                $sheet->getStyle('A1:N1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 12,
                        'color' => [
                            'rgb' => 'FFFFFF',
                        ],
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => '4F46E5', // Indigo-600
                        ],
                    ],
                ]);

                // Định dạng các cột
                $lastRow = $sheet->getHighestRow();

                // Định dạng cột trạng thái TikTok (cột G - tiktok_shop_status)
                $statusColumn = 'G';
                for ($row = 2; $row <= $lastRow; $row++) {
                    $status = $sheet->getCell($statusColumn . $row)->getValue();

                    if ($status === 'Live') {
                        $sheet->getStyle($statusColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'DCFCE7'], // Green-100
                            ],
                            'font' => [
                                'color' => ['rgb' => '16A34A'], // Green-600
                            ],
                        ]);
                    } elseif ($status === 'Not Connected') {
                        $sheet->getStyle($statusColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'FEF9C3'], // Yellow-100
                            ],
                            'font' => [
                                'color' => ['rgb' => 'CA8A04'], // Yellow-600
                            ],
                        ]);
                    } elseif ($status === 'Suspended') {
                        $sheet->getStyle($statusColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'FEE2E2'], // Red-100
                            ],
                            'font' => [
                                'color' => ['rgb' => 'DC2626'], // Red-600
                            ],
                        ]);
                    }
                }

                // Định dạng cột bank_account (cột L - bank_account)
                $bankColumn = 'L';
                for ($row = 2; $row <= $lastRow; $row++) {
                    $bankAccount = $sheet->getCell($bankColumn . $row)->getValue();

                    if ($bankAccount === 'N/A') {
                        // Thiếu thông tin ngân hàng - Màu đỏ cảnh báo
                        $sheet->getStyle($bankColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'FEE2E2'], // Red-100
                            ],
                            'font' => [
                                'color' => ['rgb' => 'DC2626'], // Red-600
                                'bold' => true,
                            ],
                        ]);
                    }
                }

                // Kiểm tra sự khớp nhau giữa bank_account và card
                $cardColumn = 'K'; // Cột K - card (4 số cuối TK TikTok)
                for ($row = 2; $row <= $lastRow; $row++) {
                    $card = $sheet->getCell($cardColumn . $row)->getValue();
                    $bankAccount = $sheet->getCell($bankColumn . $row)->getValue();

                    // Nếu cả hai đều có giá trị (không phải N/A)
                    if ($card !== 'N/A' && $bankAccount !== 'N/A') {
                        // Lấy 4 số cuối
                        $cardLast4 = substr($card, -4);
                        $bankLast4 = substr($bankAccount, -4);

                        if ($cardLast4 !== $bankLast4) {
                            // Không khớp - Màu vàng cảnh báo
                            $sheet->getStyle($cardColumn . $row . ':' . $bankColumn . $row)->applyFromArray([
                                'fill' => [
                                    'fillType' => Fill::FILL_SOLID,
                                    'startColor' => ['rgb' => 'FEF9C3'], // Yellow-100
                                ],
                                'font' => [
                                    'color' => ['rgb' => 'CA8A04'], // Yellow-600
                                ],
                            ]);
                        } else {
                            // Khớp - Màu xanh lá
                            $sheet->getStyle($cardColumn . $row . ':' . $bankColumn . $row)->applyFromArray([
                                'fill' => [
                                    'fillType' => Fill::FILL_SOLID,
                                    'startColor' => ['rgb' => 'DCFCE7'], // Green-100
                                ],
                                'font' => [
                                    'color' => ['rgb' => '16A34A'], // Green-600
                                ],
                            ]);
                        }
                    } elseif ($card === 'N/A') {
                        // Thiếu thông tin TikTok - Màu vàng cảnh báo
                        $sheet->getStyle($cardColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'FEF9C3'], // Yellow-100
                            ],
                            'font' => [
                                'color' => ['rgb' => 'CA8A04'], // Yellow-600
                                'bold' => true,
                            ],
                        ]);
                    }
                }

                // Định dạng cột Liên kết API App
                $apiColumn = 'M'; // Cột M - app_partner_id
                for ($row = 2; $row <= $lastRow; $row++) {
                    $apiStatus = $sheet->getCell($apiColumn . $row)->getValue();

                    if ($apiStatus === 'Đã liên kết') {
                        $sheet->getStyle($apiColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'DCFCE7'], // Green-100
                            ],
                            'font' => [
                                'color' => ['rgb' => '16A34A'], // Green-600
                            ],
                        ]);
                    } else {
                        $sheet->getStyle($apiColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'FEE2E2'], // Red-100
                            ],
                            'font' => [
                                'color' => ['rgb' => 'DC2626'], // Red-600
                            ],
                        ]);
                    }
                }

                // Định dạng tất cả các ô
                $sheet->getStyle('A1:N' . $lastRow)->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['rgb' => 'E5E7EB'], // Gray-200
                        ],
                    ],
                ]);

                // Tự động điều chỉnh chiều rộng cột
                foreach (range('A', 'N') as $column) {
                    $sheet->getColumnDimension($column)->setAutoSize(true);
                }
            },
        ]);
    }
}
