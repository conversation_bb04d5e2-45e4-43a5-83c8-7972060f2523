<?php

namespace App\Exports;

use App\Services\SellerInvoiceService;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Carbon\Carbon;

class SellerExport implements FromQuery, WithHeadings, WithMapping, WithStyles, WithEvents
{
    protected $sellerInvoiceService;
    protected $exportType;
    protected $filters;
    protected $seller;
    protected $dateRange;

    public function __construct(SellerInvoiceService $sellerInvoiceService, string $exportType, array $filters = [])
    {
        $this->sellerInvoiceService = $sellerInvoiceService;
        $this->exportType = $exportType;
        $this->filters = $filters;
        $this->seller = $sellerInvoiceService->seller;
        $this->dateRange = $sellerInvoiceService->startDate->format('d-m-Y') . '_' . $sellerInvoiceService->endDate->format('d-m-Y');
    }

    public function query()
    {
        $query = match ($this->exportType) {
            'orders' => $this->sellerInvoiceService->getOrdersQuery(),
            'order_items' => $this->sellerInvoiceService->getOrderItemsQuery(),
            'fulfillment_cost' => $this->sellerInvoiceService->getFulfillmentCostQuery(),
            'advertising_cost' => $this->sellerInvoiceService->getAdvertisingCostQuery(),
            'design_cost' => $this->sellerInvoiceService->getDesignCostQuery(),
            'printing_cost' => $this->sellerInvoiceService->getPrintingCostQuery(),
            'bank_payout' => $this->sellerInvoiceService->getBankPayoutQuery(),
            'previous_month_loss' => $this->sellerInvoiceService->getPreviousMonthLossQuery(),
            default => throw new \InvalidArgumentException("Invalid export type: {$this->exportType}")
        };

        // Áp dụng filters (không áp dụng cho previous_month_loss vì đây là dữ liệu đặc biệt)
        if (isset($this->filters['status']) && $this->filters['status'] && $this->exportType !== 'previous_month_loss') {
            $query->where('status', $this->filters['status']);
        }

        return $query;
    }

    public function headings(): array
    {
        return match ($this->exportType) {
            'orders' => [
                'Mã đơn hàng',
                'Cửa hàng',
                'Trạng thái',
                'Tổng tiền (USD)',
                'Ngày tạo'
            ],
            'order_items' => [
                'Mã đơn hàng',
                'Tên sản phẩm',
                'SKU sản phẩm',
                'Số lượng',
                'Đơn giá (USD)',
                'Thành tiền (USD)',
                'Trạng thái đơn hàng',
                'Ngày tạo'
            ],
            'fulfillment_cost' => [
                'Mã đơn hàng',
                'Nhà cung cấp',
                'Trạng thái',
                'Chi phí (USD)',
                'Ngày tạo'
            ],
            'advertising_cost' => [
                'Số tiền (USD)',
                'Trạng thái',
                'Mô tả',
                'Ngày tạo'
            ],
            'design_cost' => [
                'Tên thiết kế',
                'Designer',
                'Giá cơ bản (USD)',
                'Phí gấp (USD)',
                'Tổng chi phí (USD)',
                'Trạng thái',
                'Ngày hoàn thành'
            ],
            'printing_cost' => [
                'Loại phôi',
                'Số lượng',
                'Giá/đơn vị (USD)',
                'Tổng chi phí (USD)',
                'Trạng thái',
                'Ngày tạo'
            ],
            'bank_payout' => [
                'Loại giao dịch',
                'Số tiền (USD)',
                'Số tài khoản',
                'Trạng thái',
                'Thời gian'
            ],
            'previous_month_loss' => [
                'Tháng báo cáo',
                'Doanh thu gộp (USD)',
                'Phí nền tảng (USD)',
                'Doanh thu ròng (USD)',
                'Tổng chi phí (USD)',
                'Lợi nhuận gộp (USD)',
                'Lợi nhuận ròng (USD)',
                'Lỗ tháng trước (USD)',
                'Lợi nhuận điều chỉnh (USD)',
                'Lương cơ bản (USD)',
                'Hoa hồng (USD)',
                'Tổng thu nhập seller (USD)',
                'Tiền đang giữ (USD)',
                'Trạng thái',
                'Ngày tạo',
                'Ngày cập nhật'
            ],
            default => []
        };
    }

    public function map($record): array
    {
        return match ($this->exportType) {
            'orders' => [
                $record->order_number ?? 'N/A',
                $record->store->name ?? 'N/A',
                $this->formatOrderStatus($record->status),
                '$' . number_format($record->total ?? 0, 2),
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A'
            ],
            'order_items' => [
                $record->order->order_number ?? 'N/A',
                $record->product->name ?? 'N/A',
                $record->product->sku ?? 'N/A',
                $record->quantity ?? 0,
                '$' . number_format($record->price ?? 0, 2),
                '$' . number_format($record->total ?? 0, 2),
                $this->formatOrderStatus($record->order->status ?? ''),
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A'
            ],
            'fulfillment_cost' => [
                $record->order->order_number ?? 'N/A',
                $record->supplier->name ?? 'N/A',
                $this->formatSupplierOrderStatus($record->status),
                '$' . number_format($record->base_cost ?? 0, 2),
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A'
            ],
            'advertising_cost' => [
                '$' . number_format($record->amount ?? 0, 2),
                $this->formatAdvertisingStatus($record->status),
                $record->description ?? 'N/A',
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A'
            ],
            'design_cost' => [
                $record->design->name ?? 'N/A',
                $record->designer->name ?? 'N/A',
                '$' . number_format($record->price ?? 0, 2),
                '$' . number_format($record->rush_fee ?? 0, 2),
                '$' . number_format(($record->price ?? 0) + ($record->rush_fee ?? 0), 2),
                $this->formatDesignStatus($record->status),
                $record->completed_at ? $record->completed_at->format('d/m/Y H:i') : 'N/A'
            ],
            'printing_cost' => [
                $record->blank->name ?? 'N/A',
                $record->quantity ?? 0,
                '$' . number_format($record->unit_price ?? 0, 2),
                '$' . number_format($record->calculateProductionCost(), 2),
                $this->formatPrintingStatus($record->status),
                $record->created_at ? $record->created_at->format('d/m/Y H:i') : 'N/A'
            ],
            'bank_payout' => [
                $this->formatPayoutType($record->type),
                '$' . number_format($record->amount ?? 0, 2),
                '****' . substr($record->card_no ?? '', -4),
                $this->formatPayoutStatus($record->status),
                $record->time ? Carbon::parse($record->time)->format('d/m/Y H:i') : 'N/A'
            ],
            'previous_month_loss' => [
                $record->month ? Carbon::parse($record->month)->format('m/Y') : 'N/A',
                '$' . number_format($record->gross_revenue ?? 0, 2),
                '$' . number_format($record->platform_fees ?? 0, 2),
                '$' . number_format($record->net_revenue ?? 0, 2),
                '$' . number_format($record->total_cost ?? 0, 2),
                '$' . number_format($record->gross_profit ?? 0, 2),
                '$' . number_format($record->net_profit ?? 0, 2),
                '$' . number_format($record->previous_loss ?? 0, 2),
                '$' . number_format($record->adjusted_profit ?? 0, 2),
                '$' . number_format($record->base_salary ?? 0, 2),
                '$' . number_format($record->commission ?? 0, 2),
                '$' . number_format($record->total_salary ?? 0, 2),
                '$' . number_format($record->payout_on_hold ?? 0, 2),
                $this->formatFinanceStatus($record->status ?? 'pending'),
                $record->created_at ? Carbon::parse($record->created_at)->format('d/m/Y H:i') : 'N/A',
                $record->updated_at ? Carbon::parse($record->updated_at)->format('d/m/Y H:i') : 'N/A'
            ],
            default => []
        };
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4F46E5'], // Indigo-600
                ],
            ],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();
                $lastRow = $sheet->getHighestRow();
                $lastColumn = $sheet->getHighestColumn();

                // Thêm summary row
                $this->addSummaryRow($sheet, $lastRow);

                // Định dạng borders
                $sheet->getStyle("A1:{$lastColumn}" . ($lastRow + 2))->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['rgb' => 'E5E7EB'],
                        ],
                    ],
                ]);

                // Auto-size columns
                foreach (range('A', $lastColumn) as $column) {
                    $sheet->getColumnDimension($column)->setAutoSize(true);
                }
            },
        ];
    }

    protected function addSummaryRow(Worksheet $sheet, int $lastRow): void
    {
        $summaryRow = $lastRow + 2;

        match ($this->exportType) {
            'orders' => $this->addOrdersSummary($sheet, $summaryRow, $lastRow),
            'fulfillment_cost' => $this->addFulfillmentCostSummary($sheet, $summaryRow, $lastRow),
            'advertising_cost' => $this->addAdvertisingCostSummary($sheet, $summaryRow, $lastRow),
            'design_cost' => $this->addDesignCostSummary($sheet, $summaryRow, $lastRow),
            'printing_cost' => $this->addPrintingCostSummary($sheet, $summaryRow, $lastRow),
            'bank_payout' => $this->addBankPayoutSummary($sheet, $summaryRow, $lastRow),
            'previous_month_loss' => $this->addPreviousMonthLossSummary($sheet, $summaryRow, $lastRow),
            default => null
        };
    }

    protected function addOrdersSummary(Worksheet $sheet, int $summaryRow, int $lastRow): void
    {
        $sheet->setCellValue('A' . $summaryRow, 'TỔNG CỘNG');
        $sheet->setCellValue('C' . $summaryRow, $lastRow - 1 . ' đơn hàng');
        $sheet->setCellValue('D' . $summaryRow, '=SUM(D2:D' . $lastRow . ')');

        // Format summary row
        $sheet->getStyle('A' . $summaryRow . ':E' . $summaryRow)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F3F4F6'], // Gray-100
            ],
        ]);
    }

    protected function addFulfillmentCostSummary(Worksheet $sheet, int $summaryRow, int $lastRow): void
    {
        $sheet->setCellValue('A' . $summaryRow, 'TỔNG CỘNG');
        $sheet->setCellValue('C' . $summaryRow, $lastRow - 1 . ' đơn');
        $sheet->setCellValue('D' . $summaryRow, '=SUM(D2:D' . $lastRow . ')');

        $sheet->getStyle('A' . $summaryRow . ':E' . $summaryRow)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F3F4F6'],
            ],
        ]);
    }

    protected function addAdvertisingCostSummary(Worksheet $sheet, int $summaryRow, int $lastRow): void
    {
        $sheet->setCellValue('A' . $summaryRow, '=SUM(A2:A' . $lastRow . ')');
        $sheet->setCellValue('B' . $summaryRow, $lastRow - 1 . ' yêu cầu');

        $sheet->getStyle('A' . $summaryRow . ':D' . $summaryRow)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F3F4F6'],
            ],
        ]);
    }

    protected function addDesignCostSummary(Worksheet $sheet, int $summaryRow, int $lastRow): void
    {
        $sheet->setCellValue('A' . $summaryRow, 'TỔNG CỘNG');
        $sheet->setCellValue('B' . $summaryRow, $lastRow - 1 . ' job');
        $sheet->setCellValue('E' . $summaryRow, '=SUM(E2:E' . $lastRow . ')');

        $sheet->getStyle('A' . $summaryRow . ':G' . $summaryRow)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F3F4F6'],
            ],
        ]);
    }

    protected function addPrintingCostSummary(Worksheet $sheet, int $summaryRow, int $lastRow): void
    {
        $sheet->setCellValue('A' . $summaryRow, 'TỔNG CỘNG');
        $sheet->setCellValue('B' . $summaryRow, '=SUM(B2:B' . $lastRow . ')');
        $sheet->setCellValue('D' . $summaryRow, '=SUM(D2:D' . $lastRow . ')');

        $sheet->getStyle('A' . $summaryRow . ':F' . $summaryRow)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F3F4F6'],
            ],
        ]);
    }

    protected function addBankPayoutSummary(Worksheet $sheet, int $summaryRow, int $lastRow): void
    {
        $sheet->setCellValue('A' . $summaryRow, 'TỔNG CỘNG');
        $sheet->setCellValue('B' . $summaryRow, '=SUM(B2:B' . $lastRow . ')');
        $sheet->setCellValue('C' . $summaryRow, $lastRow - 1 . ' giao dịch');

        $sheet->getStyle('A' . $summaryRow . ':E' . $summaryRow)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F3F4F6'],
            ],
        ]);
    }

    protected function addPreviousMonthLossSummary(Worksheet $sheet, int $summaryRow, int $lastRow): void
    {
        $sheet->setCellValue('A' . $summaryRow, 'TỔNG CỘNG');
        $sheet->setCellValue('B' . $summaryRow, '=SUM(B2:B' . $lastRow . ')'); // Doanh thu gộp
        $sheet->setCellValue('E' . $summaryRow, '=SUM(E2:E' . $lastRow . ')'); // Tổng chi phí
        $sheet->setCellValue('I' . $summaryRow, '=SUM(I2:I' . $lastRow . ')'); // Lợi nhuận điều chỉnh

        $sheet->getStyle('A' . $summaryRow . ':P' . $summaryRow)->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F3F4F6'],
            ],
        ]);
    }

    // Status formatting methods
    protected function formatOrderStatus($status): string
    {
        $statusValue = $status instanceof \App\Enums\OrderStatus ? $status->value : (string) $status;
        return match ($statusValue) {
            'Processing' => 'Đang xử lý',
            'AwaitingShipment' => 'Chờ vận chuyển',
            'Completed' => 'Hoàn thành',
            'OnHold' => 'Tạm giữ',
            'Refunded' => 'Hoàn tiền',
            'Cancelled' => 'Đã hủy',
            default => $statusValue,
        };
    }

    protected function formatSupplierOrderStatus($status): string
    {
        $statusValue = $status instanceof \App\Enums\SupplierOrderStatus ? $status->value : (string) $status;
        return match ($statusValue) {
            'Pending' => 'Chờ xử lý',
            'AwaitingShipment' => 'Chờ vận chuyển',
            'InProducing' => 'Đang sản xuất',
            'Completed' => 'Hoàn thành',
            'OnHold' => 'Tạm giữ',
            'Cancelled' => 'Đã hủy',
            'Refunded' => 'Hoàn tiền',
            default => $statusValue,
        };
    }

    protected function formatAdvertisingStatus($status): string
    {
        return match ((string) $status) {
            'pending' => 'Chờ duyệt',
            'approved' => 'Đã duyệt',
            'rejected' => 'Từ chối',
            default => (string) $status,
        };
    }

    protected function formatDesignStatus($status): string
    {
        $statusValue = $status instanceof \App\Enums\DesignJobStatus ? $status->value : (string) $status;
        return match ($statusValue) {
            'pending' => 'Chờ xử lý',
            'assigned' => 'Đã phân công',
            'in_progress' => 'Đang thực hiện',
            'under_review' => 'Chờ duyệt',
            'needs_revision' => 'Cần sửa',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy',
            default => $statusValue,
        };
    }

    protected function formatPrintingStatus($status): string
    {
        $statusValue = is_object($status) && method_exists($status, '__toString') ? (string) $status : (string) $status;
        return match ($statusValue) {
            'pending' => 'Chờ xử lý',
            'in_production' => 'Đang sản xuất',
            'completed' => 'Hoàn thành',
            'rejected' => 'Từ chối',
            default => $statusValue,
        };
    }

    protected function formatPayoutType($type): string
    {
        return match ((string) $type) {
            'Receive' => 'Nhận tiền',
            'Send' => 'Gửi tiền',
            default => (string) $type,
        };
    }

    protected function formatPayoutStatus($status): string
    {
        return match ((string) $status) {
            'Success' => 'Thành công',
            'Pending' => 'Chờ xử lý',
            'Failed' => 'Thất bại',
            default => (string) $status,
        };
    }

    protected function formatFinanceStatus($status): string
    {
        return match ((string) $status) {
            'pending' => 'Chờ xử lý',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy',
            default => (string) $status,
        };
    }

    public function getFileName(): string
    {
        $tableNames = [
            'orders' => 'don_hang',
            'order_items' => 'chi_tiet_don_hang',
            'fulfillment_cost' => 'chi_phi_di_don',
            'advertising_cost' => 'chi_phi_quang_cao',
            'design_cost' => 'chi_phi_thiet_ke',
            'printing_cost' => 'chi_phi_in_ao',
            'bank_payout' => 'bank_payout',
            'previous_month_loss' => 'lo_thang_truoc',
        ];

        $tableName = $tableNames[$this->exportType] ?? $this->exportType;
        $sellerName = str_replace(' ', '_', $this->seller->name);

        return "{$tableName}_{$sellerName}_{$this->dateRange}.xlsx";
    }
}
