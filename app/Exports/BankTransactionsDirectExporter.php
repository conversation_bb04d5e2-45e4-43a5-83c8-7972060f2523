<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Database\Eloquent\Collection;

class BankTransactionsDirectExporter implements FromCollection, WithHeadings
{
    protected $transactions;

    public function __construct(Collection $transactions)
    {
        $this->transactions = $transactions;
    }

    public function collection()
    {
        return $this->transactions->map(function ($transaction) {
            return [
                'id' => $transaction->id,
                'transaction_id' => $transaction->transaction_id,
                'time' => $transaction->time ? \Carbon\Carbon::parse($transaction->time)->format('d/m/Y H:i') : 'N/A',
                'amount' => $transaction->amount,
                'type' => $transaction->type?->value ?? $transaction->type,
                'status' => $transaction->status?->value ?? $transaction->status,
                'store_name' => $transaction->store?->name ?? '',
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Transaction ID',
            'Reference ID',
            'Transaction Time',
            'Amount',
            'Transaction Type',
            'Status',
            'Store Name',
        ];
    }
}
