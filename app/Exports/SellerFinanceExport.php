<?php

namespace App\Exports;

use App\Models\SellerFinance;
use App\Services\SellerService;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class SellerFinanceExport extends Exporter
{
    protected static ?string $model = SellerFinance::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('seller.name')
                ->label('Tên Seller'),

            ExportColumn::make('seller.email')
                ->label('Email'),

            ExportColumn::make('seller.teams.name')
                ->label('Team')
                ->state(function (SellerFinance $record): string {
                    return $record->seller->teams->pluck('name')->join(', ') ?: 'N/A';
                }),

            ExportColumn::make('month')
                ->label('Tháng')
                ->formatStateUsing(fn ($state) => $state ? $state->format('m/Y') : 'N/A'),

            ExportColumn::make('gross_revenue')
                ->label('Doanh Thu Gộp')
                ->formatStateUsing(fn ($state) => $state ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('platform_fees')
                ->label('Phí Nền Tảng')
                ->formatStateUsing(fn ($state) => $state ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('net_revenue')
                ->label('Doanh Thu Ròng')
                ->formatStateUsing(fn ($state) => $state ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('total_cost')
                ->label('Tổng Chi Phí')
                ->formatStateUsing(fn ($state) => $state ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('previous_loss')
                ->label('Lỗ Tháng Trước')
                ->formatStateUsing(fn ($state) => $state > 0 ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('gross_profit')
                ->label('Lợi Nhuận Gộp')
                ->formatStateUsing(fn ($state) => '$' . number_format($state, 2)),

            ExportColumn::make('net_profit')
                ->label('Lợi Nhuận Ròng')
                ->formatStateUsing(fn ($state) => '$' . number_format($state, 2)),

            ExportColumn::make('adjusted_profit')
                ->label('Lợi Nhuận Điều Chỉnh')
                ->formatStateUsing(fn ($state) => '$' . number_format($state, 2)),

            ExportColumn::make('bank_payout')
                ->label('Bank Payout')
                ->state(function (SellerFinance $record): string {
                    $bankPayout = SellerService::getBankPayoutFromRecord($record);
                    return '$' . number_format($bankPayout, 2);
                }),

            ExportColumn::make('base_salary')
                ->label('Lương Cơ Bản')
                ->formatStateUsing(fn ($state) => $state ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('total_bonus')
                ->label('Tổng Hoa Hồng')
                ->formatStateUsing(fn ($state) => $state ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('total_salary')
                ->label('Tổng Thu Nhập')
                ->formatStateUsing(fn ($state) => $state ? '$' . number_format($state, 2) : '$0.00'),

            ExportColumn::make('status')
                ->label('Trạng Thái')
                ->formatStateUsing(fn (string $state): string => match ($state) {
                    'pending' => 'Chờ xử lý',
                    'completed' => 'Hoàn thành',
                    'cancelled' => 'Đã hủy',
                    default => $state,
                }),

            ExportColumn::make('created_at')
                ->label('Ngày Tạo')
                ->formatStateUsing(fn ($state) => $state ? $state->format('d/m/Y H:i') : 'N/A'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        return 'Xuất dữ liệu báo cáo tài chính đã hoàn tất và sẵn sàng để tải xuống.';
    }

    // Tắt xử lý queue để tải xuống trực tiếp
    public function getChunkSize(): int
    {
        return 5000; // Giảm kích thước chunk để tránh timeout trên server
    }

    public function getJobConnection(): ?string
    {
        return 'sync'; // Sử dụng kết nối sync thay vì queue
    }

    // Cấu hình disk lưu trữ cho export
    public function getFileDisk(): string
    {
        // Luôn sử dụng local disk để tránh vấn đề với external storage
        return 'local';
    }

    // Cấu hình timeout cho export
    public function getJobTimeout(): ?int
    {
        return 300; // 5 phút timeout
    }

    protected function getQuery(): Builder
    {
        return SellerFinance::query()
            ->with(['seller.teams'])
            ->orderBy('month', 'desc');
    }

    public function setUp($export): void
    {
        $export->registerEvents([
            \Maatwebsite\Excel\Events\AfterSheet::class => function (\Maatwebsite\Excel\Events\AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // Định dạng tiêu đề
                $sheet->getStyle('A1:T1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 12,
                        'color' => [
                            'rgb' => 'FFFFFF',
                        ],
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => '4F46E5', // Indigo-600
                        ],
                    ],
                ]);

                // Định dạng các cột
                $lastRow = $sheet->getHighestRow();

                // Định dạng cột trạng thái (cột S - status)
                $statusColumn = 'S';
                for ($row = 2; $row <= $lastRow; $row++) {
                    $status = $sheet->getCell($statusColumn . $row)->getValue();

                    if ($status === 'Hoàn thành') {
                        $sheet->getStyle($statusColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'DCFCE7'], // Green-100
                            ],
                            'font' => [
                                'color' => ['rgb' => '16A34A'], // Green-600
                            ],
                        ]);
                    } elseif ($status === 'Chờ xử lý') {
                        $sheet->getStyle($statusColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'FEF9C3'], // Yellow-100
                            ],
                            'font' => [
                                'color' => ['rgb' => 'CA8A04'], // Yellow-600
                            ],
                        ]);
                    } elseif ($status === 'Đã hủy') {
                        $sheet->getStyle($statusColumn . $row)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'FEE2E2'], // Red-100
                            ],
                            'font' => [
                                'color' => ['rgb' => 'DC2626'], // Red-600
                            ],
                        ]);
                    }
                }

                // Định dạng cột lợi nhuận (cột I, J, K - gross_profit, net_profit, adjusted_profit)
                $profitColumns = ['I', 'J', 'K'];
                foreach ($profitColumns as $column) {
                    for ($row = 2; $row <= $lastRow; $row++) {
                        $value = $sheet->getCell($column . $row)->getValue();

                        // Lấy giá trị số từ chuỗi (loại bỏ $ và dấu phẩy)
                        $numericValue = (float) str_replace(['$', ','], '', $value);

                        if ($numericValue >= 0) {
                            // Lợi nhuận dương - Màu xanh lá
                            $sheet->getStyle($column . $row)->applyFromArray([
                                'fill' => [
                                    'fillType' => Fill::FILL_SOLID,
                                    'startColor' => ['rgb' => 'DCFCE7'], // Green-100
                                ],
                                'font' => [
                                    'color' => ['rgb' => '16A34A'], // Green-600
                                ],
                            ]);
                        } else {
                            // Lợi nhuận âm - Màu đỏ
                            $sheet->getStyle($column . $row)->applyFromArray([
                                'fill' => [
                                    'fillType' => Fill::FILL_SOLID,
                                    'startColor' => ['rgb' => 'FEE2E2'], // Red-100
                                ],
                                'font' => [
                                    'color' => ['rgb' => 'DC2626'], // Red-600
                                ],
                            ]);
                        }
                    }
                }

                // Định dạng tất cả các ô
                $sheet->getStyle('A1:T' . $lastRow)->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['rgb' => 'E5E7EB'], // Gray-200
                        ],
                    ],
                ]);

                // Tự động điều chỉnh chiều rộng cột
                foreach (range('A', 'T') as $column) {
                    $sheet->getColumnDimension($column)->setAutoSize(true);
                }
            },
        ]);
    }
}