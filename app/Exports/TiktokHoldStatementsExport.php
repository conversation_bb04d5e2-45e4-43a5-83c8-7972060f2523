<?php

namespace App\Exports;

use App\Models\TiktokHoldStatement;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;

class TiktokHoldStatementsExport extends Exporter
{
    protected static ?string $model = TiktokHoldStatement::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('ID'),

            ExportColumn::make('store.id')
                ->label('Store ID'),

            ExportColumn::make('store.name')
                ->label('Store Name'),

            ExportColumn::make('store.tiktok_shop_name')
                ->label('TikTok Shop Name'),

            ExportColumn::make('store.seller.name')
                ->label('Seller Name'),

            ExportColumn::make('statement_id')
                ->label('Statement ID'),

            ExportColumn::make('payment_id')
                ->label('Payment ID'),

            ExportColumn::make('statement_time')
                ->label('Statement Time')
                ->formatStateUsing(fn ($state) => $state ? $state->format('Y-m-d H:i:s') : null),

            ExportColumn::make('settlement_amount')
                ->label('Settlement Amount'),

            ExportColumn::make('currency')
                ->label('Currency'),

            ExportColumn::make('payment_status')
                ->label('Payment Status'),

            ExportColumn::make('created_at')
                ->label('Created At')
                ->formatStateUsing(fn ($state) => $state ? $state->format('Y-m-d H:i:s') : null),

            ExportColumn::make('updated_at')
                ->label('Updated At')
                ->formatStateUsing(fn ($state) => $state ? $state->format('Y-m-d H:i:s') : null),
        ];
    }

    public static function getQueryUsing(): ?Builder
    {
        return TiktokHoldStatement::query()
            ->with(['store', 'store.seller']);
    }

    /**
     * Thông báo khi xuất dữ liệu hoàn tất
     *
     * @param Export $export Đối tượng Export
     * @return string Thông báo hoàn tất
     */
    public static function getCompletedNotificationBody(Export $export): string
    {
        return 'Dữ liệu TikTok Hold Statements đã được xuất thành công và sẵn sàng để tải xuống.';
    }

    /**
     * Tắt xử lý queue để tải xuống trực tiếp
     * Tăng kích thước chunk để xử lý nhiều dữ liệu hơn trong một lần
     */
    public function getChunkSize(): int
    {
        return 10000;
    }

    /**
     * Sử dụng kết nối sync thay vì queue để tải xuống trực tiếp
     */
    public function getJobConnection(): ?string
    {
        return 'sync';
    }
}
