<?php

namespace App\Exports;

use App\Models\Product;
use App\Models\OrderItem;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProductStatisticsCsvExport implements FromQuery, WithHeadings, WithMapping
{
    protected $filters;
    protected $dateRange;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
        $this->dateRange = now()->format('d-m-Y');
    }

    public function query()
    {
        $query = OrderItem::select(
            'products.id',
            'products.name',
            'products.sku',
            'products.price',
            'products.type',
            'products.tiktok_product_id',
            'users.name as seller_name',
            'users.telegram_id',
            'stores.name as store_name',
            DB::raw('SUM(order_items.quantity) as total_ordered'),
            DB::raw('MAX(orders.created_at) as latest_order_date'),
            DB::raw('MIN(orders.created_at) as first_order_date')
        )
        ->join('products', 'order_items.product_id', '=', 'products.id')
        ->join('orders', 'order_items.order_id', '=', 'orders.id')
        ->leftJoin('users', 'products.seller_id', '=', 'users.id')
        ->leftJoin('stores', 'products.store_id', '=', 'stores.id')
        ->whereNotIn('orders.status', ['canceled', 'returned'])
        ->groupBy(
            'products.id',
            'products.name',
            'products.sku',
            'products.price',
            'products.type',
            'products.tiktok_product_id',
            'users.name',
            'users.telegram_id',
            'stores.name'
        )
        ->orderBy('total_ordered', 'desc');

        // Apply date filter if provided
        if (isset($this->filters['date']) && $this->filters['date']) {
            $dateRange = $this->filters['date'];
            $sevenDaysAgo = Carbon::now()->subDays(7)->startOfDay();
            $today = Carbon::now()->endOfDay();
            
            $startDate = $sevenDaysAgo;
            $endDate = $today;
            
            if (!empty($dateRange)) {
                $dates = explode(' - ', $dateRange);
                
                if (count($dates) === 2) {
                    try {
                        $startDate = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                        $endDate = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    } catch (\Exception $e) {
                        // Use default if error
                    }
                }
            }
            
            $query->whereBetween('orders.created_at', [$startDate, $endDate]);
        }

        // Apply type filter if provided
        if (isset($this->filters['type']) && !empty($this->filters['type'])) {
            $query->whereIn('products.type', $this->filters['type']);
        }

        // Apply seller filter if provided
        if (isset($this->filters['seller_id']) && $this->filters['seller_id']) {
            $query->where('products.seller_id', $this->filters['seller_id']);
        }

        return $query;
    }

    public function headings(): array
    {
        return [
            'Thu hang',
            'ID san pham',
            'Ten san pham',
            'SKU',
            'Gia (VND)',
            'Loai san pham',
            'TikTok Product ID',
            'Nguoi ban',
            'Telegram ID',
            'Cua hang',
            'Tong so luong dat hang',
            'Don hang moi nhat',
            'Don hang dau tien',
            'Khoang thoi gian (ngay)'
        ];
    }

    public function map($record): array
    {
        static $index = 0;
        $index++;

        // Calculate order period days
        $orderPeriodDays = 0;
        if ($record->first_order_date && $record->latest_order_date) {
            $first = Carbon::parse($record->first_order_date);
            $latest = Carbon::parse($record->latest_order_date);
            $orderPeriodDays = $first->diffInDays($latest);
        }

        return [
            $index,
            $record->id ?? 'N/A',
            $record->name ?? 'N/A',
            $record->sku ?? 'N/A',
            $record->price ? number_format($record->price, 0) : '0',
            $record->type ?? 'N/A',
            $record->tiktok_product_id ?? 'N/A',
            $record->seller_name ?? 'N/A',
            $record->telegram_id ?? 'N/A',
            $record->store_name ?? 'N/A',
            number_format($record->total_ordered ?? 0, 0),
            $record->latest_order_date ? Carbon::parse($record->latest_order_date)->format('d/m/Y H:i') : 'N/A',
            $record->first_order_date ? Carbon::parse($record->first_order_date)->format('d/m/Y H:i') : 'N/A',
            $orderPeriodDays
        ];
    }

    public function getFileName(): string
    {
        return "product_statistics_{$this->dateRange}.csv";
    }
}
