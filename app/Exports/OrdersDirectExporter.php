<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Database\Eloquent\Collection;

class OrdersDirectExporter implements FromCollection, WithHeadings
{
    protected $orders;

    public function __construct(Collection $orders)
    {
        $this->orders = $orders;
    }

    public function collection()
    {
        return $this->orders->map(function ($order) {
            return [
                'id' => $order->id,
                'store_name' => $order->store?->name ?? '',
                'seller_name' => $order->seller?->name ?? '',
                'status' => $order->status?->value ?? $order->status,
                'total' => $order->total,
                'created_at' => $order->created_at ? $order->created_at->format('d/m/Y H:i') : 'N/A',
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Order ID',
            'Store Name', 
            'Seller Name',
            'Status',
            'Total Amount',
            'Order Date',
        ];
    }
}
