<?php

namespace App\Events;

use App\Models\Order;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
class OrderCreated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order;

    /**
     * Create a new event instance.
     */
    public function __construct($order)
    {
        $this->order = $order;
        Log::info('🎯 OrderCreated event constructed', [
            'order_id' => $this->order->id
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        Log::info('🎯 Broadcasting on orders channel');
        return [
            new Channel('orders'),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        $data = [
            'id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'total' => $this->order->total,
            'seller_name' => $this->order->store->owner->name,
            'store_name' => $this->order->store->name,
            'created_at' => now()->toDateTimeString(),
        ];

        Log::info('🎯 Broadcasting data:', $data);
        
        return $data;
    }

    public function broadcastAs()
    {
        return 'OrderCreated';
    }
} 