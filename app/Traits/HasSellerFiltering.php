<?php

namespace App\Traits;

use App\Models\Order;
use App\Models\SupplierOrder;
use App\Models\Store;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

trait HasSellerFiltering
{
    /**
     * Tạo base query cho Order model với seller filtering
     * 
     * @param int|null $sellerId - ID của seller cụ thể, null để hiển thị cả team
     * @return Builder
     */
    protected function getOrderBaseQuery(?int $sellerId = null): Builder
    {
        if ($sellerId) {
            // Khi chọn seller cụ thể, bypass global scope và filter theo seller đó
            return Order::withoutGlobalScope('userAccess')->where('seller_id', $sellerId);
        } else {
            // Khi hiển thị cả team, dùng global scope tự động
            return Order::query();
        }
    }

    /**
     * Tạo base query cho SupplierOrder model với seller filtering
     * 
     * @param int|null $sellerId - ID của seller cụ thể, null để hiển thị cả team
     * @return Builder
     */
    protected function getSupplierOrderBaseQuery(?int $sellerId = null): Builder
    {
        if ($sellerId) {
            // Khi chọn seller cụ thể, bypass global scope và filter theo seller đó
            return SupplierOrder::withoutGlobalScope('accessScope')->where('seller_id', $sellerId);
        } else {
            // Khi hiển thị cả team, dùng global scope tự động
            return SupplierOrder::query();
        }
    }

    /**
     * Tạo base query cho bất kỳ model nào với seller filtering
     * 
     * @param string $modelClass - Class name của model (ví dụ: Order::class)
     * @param string $globalScopeName - Tên của global scope cần bypass
     * @param int|null $sellerId - ID của seller cụ thể, null để hiển thị cả team
     * @return Builder
     */
    protected function getModelBaseQuery(string $modelClass, string $globalScopeName, ?int $sellerId = null): Builder
    {
        if ($sellerId) {
            // Khi chọn seller cụ thể, bypass global scope và filter theo seller đó
            return $modelClass::withoutGlobalScope($globalScopeName)->where('seller_id', $sellerId);
        } else {
            // Khi hiển thị cả team, dùng global scope tự động
            return $modelClass::query();
        }
    }

    /**
     * Shortcut method để lấy base query cho Order với sellerId từ property
     * 
     * @return Builder
     */
    protected function orderQuery(): Builder
    {
        return $this->getOrderBaseQuery($this->sellerId ?? null);
    }

    /**
     * Shortcut method để lấy base query cho SupplierOrder với sellerId từ property
     *
     * @return Builder
     */
    protected function supplierOrderQuery(): Builder
    {
        return $this->getSupplierOrderBaseQuery($this->sellerId ?? null);
    }

    /**
     * Tạo base query cho Store model với seller filtering
     *
     * @param int|null $sellerId - ID của seller cụ thể, null để hiển thị cả team
     * @return Builder
     */
    protected function getStoreBaseQuery(?int $sellerId = null): Builder
    {
        $targetSellerId = $sellerId ?? $this->sellerId ?? null;

        if ($targetSellerId) {
            // Khi chọn seller cụ thể, chỉ lấy stores của seller đó
            return Store::where('owner_id', $targetSellerId);
        } else {
            // Khi hiển thị cả team, sử dụng logic phân quyền hiện tại
            $user = auth()->user();

            if ($user && $user->hasRole('super_admin')) {
                return Store::query();
            } elseif ($user && $user->hasRole('Leader')) {
                // Leader thấy stores của sellers trong team (đã bao gồm chính mình nếu là Seller)
                $managedSellers = $user->leaderManagedSellers();
                $sellerIds = $managedSellers->pluck('id')->toArray();
                return Store::whereIn('owner_id', $sellerIds);
            } elseif ($user && $user->hasRole('Seller')) {
                // Seller chỉ thấy stores của mình
                return Store::where('owner_id', $user->id);
            }

            // Mặc định không thấy gì
            return Store::whereRaw('1 = 0');
        }
    }

    /**
     * Tạo base query cho User model (Sellers) với seller filtering
     *
     * @param int|null $sellerId - ID của seller cụ thể, null để hiển thị cả team
     * @return Builder
     */
    protected function getUserBaseQuery(?int $sellerId = null): Builder
    {
        $targetSellerId = $sellerId ?? $this->sellerId ?? null;

        if ($targetSellerId) {
            // Khi chọn seller cụ thể, chỉ lấy seller đó
            return User::where('id', $targetSellerId)
                ->whereHas('roles', function ($query) {
                    $query->whereIn('name', ['Seller']);
                });
        } else {
            // Khi hiển thị cả team, sử dụng logic phân quyền hiện tại
            $user = auth()->user();

            if ($user && $user->hasRole('super_admin')) {
                return User::whereHas('roles', function ($query) {
                    $query->whereIn('name', ['Seller']);
                });
            } elseif ($user && $user->hasRole('Leader')) {
                // Leader thấy sellers trong team (đã bao gồm chính mình nếu là Seller)
                $managedSellers = $user->leaderManagedSellers();
                $sellerIds = $managedSellers->pluck('id')->toArray();

                return User::whereIn('id', $sellerIds)
                    ->whereHas('roles', function ($query) {
                        $query->whereIn('name', ['Seller']);
                    });
            } elseif ($user && $user->hasRole('Seller')) {
                // Seller chỉ thấy mình
                return User::where('id', $user->id)
                    ->whereHas('roles', function ($query) {
                        $query->whereIn('name', ['Seller']);
                    });
            }

            // Mặc định không thấy gì
            return User::whereRaw('1 = 0');
        }
    }

    /**
     * Shortcut method để lấy base query cho Store với sellerId từ property
     *
     * @return Builder
     */
    protected function storeQuery(): Builder
    {
        return $this->getStoreBaseQuery($this->sellerId ?? null);
    }

    /**
     * Shortcut method để lấy base query cho User với sellerId từ property
     *
     * @return Builder
     */
    protected function userQuery(): Builder
    {
        return $this->getUserBaseQuery($this->sellerId ?? null);
    }
}
