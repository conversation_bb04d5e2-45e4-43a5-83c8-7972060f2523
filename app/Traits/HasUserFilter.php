<?php

namespace App\Traits;

use App\Models\User;
use App\Models\Store;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Select;
use Filament\Tables\Filters\SelectFilter;

trait HasUserFilter
{
    /**
     * L<PERSON>y danh sách User IDs mà user hiện tại có quyền truy cập
     */
    protected static function getAccessibleUserIds()
    {
        $user = Auth::user();

        // Super admin, Fulfillment Manager thấy tất cả sellers và leaders
        if ($user->hasAnyRole(['super_admin', 'Developer', 'User Manager', 'Fullfillment Manager'])) {
            return User::role(['Seller', 'Leader'])->pluck('id')->toArray();
        }

        // Leader có ưu tiên cao hơn Fulfillment
        if ($user->hasRole('Leader')) {
            $managedSellers = $user->leaderManagedSellers(); // Đã được sửa để filter Seller role
            $sellerIds = $managedSellers->pluck('id')->toArray();
            $sellerIds[] = $user->id; // Thêm chính leader
            return $sellerIds;
        }

        // Fulfillment thấy sellers được quản lý + chính mình (chỉ khi không có role Leader)
        if ($user->hasRole('Fulfillment')) {
            $managedSellers = $user->fulfillmentManagedSellers();
            $sellerIds = $managedSellers->pluck('users.id')->toArray();
            $sellerIds[] = $user->id; // Thêm chính fulfillment user
            return $sellerIds;
        }

        // Seller chỉ thấy chính mình
        if ($user->hasRole('Seller')) {
            return [$user->id];
        }

        // Mặc định không thấy ai
        return [];
    }

    /**
     * Lấy danh sách Store owner IDs mà user hiện tại có quyền truy cập
     */
    protected static function getAccessibleStoreOwnerIds()
    {
        $user = Auth::user();

        // Super admin, Fulfillment Manager thấy tất cả stores
        if ($user->hasAnyRole(['super_admin', 'Developer', 'User Manager', 'Fullfillment Manager'])) {
            return Store::pluck('owner_id')->unique()->toArray();
        }

        // Leader có ưu tiên cao hơn Fulfillment
        if ($user->hasRole('Leader')) {
            $managedSellers = $user->leaderManagedSellers(); // Đã được sửa để filter Seller role
            $sellerIds = $managedSellers->pluck('id')->toArray();
            $sellerIds[] = $user->id; // Thêm chính leader
            return $sellerIds;
        }

        // Fulfillment thấy stores của sellers được quản lý + chính mình (chỉ khi không có role Leader)
        if ($user->hasRole('Fulfillment')) {
            $managedSellers = $user->fulfillmentManagedSellers();
            $sellerIds = $managedSellers->pluck('users.id')->toArray();
            $sellerIds[] = $user->id; // Thêm chính fulfillment user
            return $sellerIds;
        }

        // Seller chỉ thấy stores của mình
        if ($user->hasRole('Seller')) {
            return [$user->id];
        }

        // Mặc định không thấy ai
        return [];
    }

    /**
     * Lấy options cho User filter dựa trên quyền của user
     */
    public static function getUserOptionsForFilter()
    {
        $userIds = static::getAccessibleUserIds();

        if (empty($userIds)) {
            return collect();
        }

        return User::whereIn('id', $userIds)->pluck('name', 'id');
    }

    /**
     * Lấy options cho Store filter dựa trên quyền của user
     */
    public static function getStoreOptionsForFilter()
    {
        $ownerIds = static::getAccessibleStoreOwnerIds();

        if (empty($ownerIds)) {
            return collect();
        }

        return Store::whereIn('owner_id', $ownerIds)->pluck('name', 'id');
    }

    /**
     * Tạo User filter với logic phân quyền
     */
    public static function getUserFilter($fieldName = 'owner_id', $label = 'Seller')
    {
        return SelectFilter::make($fieldName)
            ->options(fn() => static::getUserOptionsForFilter())
            ->searchable()
            ->preload()
            ->label($label);
    }

    /**
     * Tạo Store filter với logic phân quyền
     */
    public static function getStoreFilter($fieldName = 'store_id', $label = 'Store', $relationship = null)
    {
        if ($relationship) {
            // Sử dụng relationship với modifyQueryUsing
            return SelectFilter::make($fieldName)
                ->relationship($relationship, 'name', function (Builder $query) {
                    return static::applyStoreFilterQuery($query);
                })
                ->searchable()
                ->preload()
                ->label($label);
        } else {
            // Sử dụng options trực tiếp
            return SelectFilter::make($fieldName)
                ->options(fn() => static::getStoreOptionsForFilter())
                ->searchable()
                ->preload()
                ->label($label);
        }
    }

    /**
     * Áp dụng logic filter cho Store query
     */
    public static function applyStoreFilterQuery(Builder $query)
    {
        $ownerIds = static::getAccessibleStoreOwnerIds();

        if (empty($ownerIds)) {
            return $query->whereRaw('1 = 0');
        }

        return $query->whereIn('owner_id', $ownerIds);
    }

    /**
     * Áp dụng logic filter cho User query (cho form fields)
     */
    public static function applyUserFilterQuery(Builder $query)
    {
        $userIds = static::getAccessibleUserIds();

        if (empty($userIds)) {
            return $query->whereRaw('1 = 0');
        }

        return $query->whereIn('id', $userIds);
    }

    /**
     * Áp dụng user filter cho query (instance method)
     */
    public function applyUserFilter(Builder $query)
    {
        return static::applyUserFilterQuery($query);
    }
}