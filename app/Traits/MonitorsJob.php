<?php

namespace App\Traits;

use App\Models\JobMonitor;
use Illuminate\Support\Str;

trait MonitorsJob
{
    protected ?string $jobMonitorId = null;

    public function monitor(): void
    {
        $this->jobMonitorId = (string) Str::uuid();

        JobMonitor::create([
            'job_id' => $this->jobMonitorId,
            'name' => class_basename($this),
            'queue' => $this->queue ?? 'default',
            'status' => 'queued',
            'input' => $this->getJobInput(),
        ]);
    }

    public function started(): void
    {
        if (!$this->jobMonitorId) return;

        JobMonitor::where('job_id', $this->jobMonitorId)->update([
            'status' => 'running',
            'started_at' => now(),
        ]);
    }

    public function finished(mixed $output = null): void
    {
        if (!$this->jobMonitorId) return;

        JobMonitor::where('job_id', $this->jobMonitorId)->update([
            'status' => 'finished',
            'output' => $output,
            'finished_at' => now(),
        ]);
    }

    public function failed(\Throwable $exception): void
    {
        if (!$this->jobMonitorId) return;

        JobMonitor::where('job_id', $this->jobMonitorId)->update([
            'status' => 'failed',
            'error' => $exception->getMessage(),
            'finished_at' => now(),
        ]);
    }

    protected function getJobInput(): array
    {
        return [];
    }
}
