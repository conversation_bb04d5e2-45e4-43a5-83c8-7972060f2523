<?php

namespace App\Traits;

use App\Helpers\DateRangePickerHelper;
use Carbon\Carbon;
use Livewire\Attributes\Url;

trait HasDateRangeFilter
{
    /**
     * Date range properties
     */
    #[Url(except: '')]
    public $dateRange = null;

    protected $startDate;
    protected $endDate;

    /**
     * Khởi tạo date range với giá trị mặc định
     */
    public function initializeDateRange(string $defaultType = 'current_month'): void
    {
        if (empty($this->dateRange)) {
            $this->dateRange = DateRangePickerHelper::getDefaultDateRangeString($defaultType);
        }
        $this->parseDateRange($this->dateRange);
    }

    /**
     * Parse date range string thành startDate và endDate
     */
    protected function parseDateRange(?string $dateRange = null, string $defaultType = 'current_month'): void
    {
        $result = DateRangePickerHelper::parseDateRange($dateRange, $defaultType);
        $this->startDate = $result['startDate'];
        $this->endDate = $result['endDate'];
    }

    /**
     * Lấy formatted date range để hiển thị
     */
    public function getFormattedDateRange(): string
    {
        return DateRangePickerHelper::getFormattedDateRange($this->startDate, $this->endDate);
    }

    /**
     * Tạo DateRangePicker field cho form
     */
    protected function createDateRangeField(
        string $name = 'dateRange',
        string $label = 'Khoảng thời gian',
        array $ranges = null,
        int $columnSpan = 2
    ) {
        return DateRangePickerHelper::createDateRangeField(
            $name,
            $label,
            function ($state) {
                $this->dateRange = $state;
                $this->parseDateRange($this->dateRange);
                $this->onDateRangeUpdated();
            },
            $ranges,
            $columnSpan
        );
    }

    /**
     * Callback khi date range được cập nhật
     * Override method này trong class sử dụng trait
     */
    protected function onDateRangeUpdated(): void
    {
        // Dispatch events nếu cần
        if (method_exists($this, 'dispatchFilterEvents')) {
            $this->dispatchFilterEvents();
        }

        // Reset table nếu có
        if (method_exists($this, 'resetTable')) {
            $this->resetTable();
        }

        // Force refresh page data
        if (method_exists($this, 'dispatch')) {
            $this->dispatch('$refresh');
        }
    }

    /**
     * Lấy start date
     */
    public function getStartDate(): ?Carbon
    {
        return $this->startDate;
    }

    /**
     * Lấy end date
     */
    public function getEndDate(): ?Carbon
    {
        return $this->endDate;
    }

    /**
     * Kiểm tra xem có date range hợp lệ không
     */
    public function hasValidDateRange(): bool
    {
        return $this->startDate && $this->endDate;
    }

    /**
     * Đảm bảo có date range hợp lệ
     */
    protected function ensureDateRange(string $defaultType = 'current_month'): void
    {
        if (!$this->hasValidDateRange()) {
            $this->parseDateRange($this->dateRange, $defaultType);
        }
    }

    /**
     * Lấy date range cho query database
     */
    public function getDateRangeForQuery(): array
    {
        $this->ensureDateRange();
        
        return [
            'start' => $this->startDate,
            'end' => $this->endDate,
        ];
    }

    /**
     * Tạo where clause cho date range
     */
    protected function applyDateRangeToQuery($query, string $dateColumn = 'created_at')
    {
        $this->ensureDateRange();
        
        return $query->whereBetween($dateColumn, [$this->startDate, $this->endDate]);
    }
}
