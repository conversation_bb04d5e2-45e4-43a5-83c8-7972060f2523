<?php

namespace App\Traits;

use Illuminate\Support\Str;
use App\Enums\ColorMap;

trait StyleNormalization
{
    private $styleMapping = [
        'T-SHIRT' => 'T-shirt',
        'TSHIRT' => 'T-shirt',
        'SHIRT' => 'T-shirt',
        'HOODIE' => 'Hoodie',
        'SWEATSHIRT' => 'Sweatshirt',
        'TANK TOP' => 'Tank Top',
        'TANKTOP' => 'Tank Top',
        'TOTE BAG' => 'Tote Bag',
        'TOTEBAG' => 'Tote Bag',
        'LONG SLEEVE' => 'Long Sleeve',
        'V-NECK' => 'V-neck',
        'VNECK' => 'V-neck'
    ];

    private $sizeMapping = [
        'SMALL' => 'S',
        'MEDIUM' => 'M',
        'LARGE' => 'L',
        'XLARGE' => 'XL',
        'EXTRA LARGE' => 'XL',
        '2XLARGE' => '2XL',
        'XXLARGE' => '2XL',
        'XXL' => '2XL',
        '3XLARGE' => '3XL',
        'XXXLARGE' => '3XL',
        'XXXL' => '3XL',
        '4XLARGE' => '4XL',
        'XXXXLARGE' => '4XL',
        'XXXXL' => '4XL',
        '5XLARGE' => '5XL',
        'XXXXXLARGE' => '5XL',
        'XXXXXL' => '5XL',
        'S' => 'S',
        'M' => 'M',
        'L' => 'L',
        'XL' => 'XL',
        '2XL' => '2XL',
        '3XL' => '3XL',
        '4XL' => '4XL',
        '5XL' => '5XL',
        'F' => 'OS',
        'FREE' => 'OS',
        'ONE SIZE' => 'OS',
        'ONESIZE' => 'OS',
        'OS' => 'OS',
    ];

    private $unknownStyles = [];
    private $unknownColors = [];
    private $unknownSizes = [];

    private function normalizeStyle($style)
    {
        if (empty($style)) {
            return 'T-shirt';
        }

        $normalizedInput = Str::of($style)
            ->trim()
            ->upper()
            ->replace(['-', '_', ' '], '')
            ->toString();

        foreach ($this->styleMapping as $key => $value) {
            if (Str::of($key)->upper()->replace(['-', '_', ' '], '') == $normalizedInput) {
                return $value;
            }
        }

        if (!in_array($style, $this->unknownStyles)) {
            $this->unknownStyles[] = $style;
            $this->warn("Found unknown style: {$style}");
        }

        return $style;
    }

    private function normalizeColor($color)
    {
        if (empty($color)) return '';

        $normalizedInput = Str::of($color)
            ->trim()
            ->upper()
            ->replace(['-', '_', ' '], '_')
            ->toString();

        $specialCases = [
            'ASH' => 'ASH_GREY',
            'ASHGREY' => 'ASH_GREY',
            'SPORTGREY' => 'SPORT_GREY',
            'DARKHEATHER' => 'DARK_HEATHER',
            'DARKGREYHEATHER' => 'DARK_HEATHER',
            'FOREST' => 'FOREST_GREEN',
            'FORESTGREEN' => 'FOREST_GREEN',
            'MILITARYGREEN' => 'MILITARY_GREEN',
            'ROYALBLUE' => 'ROYAL_BLUE',
            'LIGHTBLUE' => 'LIGHT_BLUE',
            'LIGHTPINK' => 'LIGHT_PINK',
            'CARDINALRED' => 'CARDINAL_RED',
            'HEATHERNAVY' => 'HEATHER_NAVY',
            'HEATHERSAPPHIRE' => 'HEATHER_SAPPHIRE',
            'HEATHERINDIGO' => 'HEATHER_INDIGO',
            'HEATHERCARDINAL' => 'HEATHER_CARDINAL',
            'ICEGREY' => 'ICE_GREY',
            'NATURAL' => 'SAND',
            'PEPPER' => 'CHARCOAL',
            'IVORY' => 'WHITE',
            'PINK' => 'LIGHT_PINK',
            'DARK_CHOCOLATE' => 'CHARCOAL',
            'DARKCHOCOLATE' => 'CHARCOAL',
        ];

        if (isset($specialCases[$normalizedInput])) {
            $normalizedInput = $specialCases[$normalizedInput];
        }

        if (ColorMap::isValidColor($normalizedInput)) {
            return str_replace('_', ' ', $normalizedInput);
        }

        if (!in_array($color, $this->unknownColors)) {
            $this->unknownColors[] = $color;
            $this->warn("Found unknown color: {$color}");
        }

        return $color;
    }

    private function normalizeSize($size)
    {
        if (empty($size)) return '';

        $normalizedInput = Str::of($size)
            ->trim()
            ->upper()
            ->toString();

        foreach ($this->sizeMapping as $key => $value) {
            if ($normalizedInput === Str::upper($key)) {
                return $value;
            }
        }

        if (!in_array($size, $this->unknownSizes)) {
            $this->unknownSizes[] = $size;
            $this->warn("Found unknown size: {$size}");
        }

        return $normalizedInput;
    }

    private function reportUnknownValues()
    {
        if (!empty($this->unknownStyles)) {
            $this->warn("\nFound unknown styles that need mapping:");
            foreach ($this->unknownStyles as $style) {
                $this->warn("- {$style}");
            }
        }

        if (!empty($this->unknownColors)) {
            $this->warn("\nFound unknown colors that need mapping:");
            foreach ($this->unknownColors as $color) {
                $this->warn("- {$color}");
            }
        }

        if (!empty($this->unknownSizes)) {
            $this->warn("\nFound unknown sizes that need mapping:");
            foreach ($this->unknownSizes as $size) {
                $this->warn("- {$size}");
            }
        }

        if (!empty($this->unknownStyles) || !empty($this->unknownColors) || !empty($this->unknownSizes)) {
            $this->warn("\nPlease update the mappings accordingly.");
        }
    }
}