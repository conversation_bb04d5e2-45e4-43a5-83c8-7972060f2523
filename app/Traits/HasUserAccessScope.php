<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

trait HasUserAccessScope
{
    /**
     * Boot the trait and add global scope
     */
    protected static function bootHasUserAccessScope(): void
    {
        static::addGlobalScope('userAccess', function (Builder $builder) {
            static::applyUserAccessScope($builder);
        });
    }

    /**
     * Apply user access scope to query
     *
     * HÀM NÀY LÀM GÌ:
     * - Tự động lọc dữ liệu dựa trên role của user đang đăng nhập
     * - Đả<PERSON> bảo mỗi user chỉ thấy data họ có quyền truy cập
     * - Áp dụng cho tất cả models sử dụng trait này
     *
     * LOGIC PHÂN QUYỀN:
     * 1. Super Admin/Developer → Xem tất cả
     * 2. Fulfillment Manager → Xem tất cả
     * 3. Leader → Xem của mình + sellers được quản lý
     * 4. <PERSON><PERSON> → Chỉ xem của mình
     * 5. Không có role → Không thấy gì
     *
     * VÍ DỤ SỬ DỤNG:
     * - Order::all() → Tự động lọc theo quyền
     * - SellerFinance::where('month', '2024-01')->get() → Vẫn bị lọc
     * - Model::withoutUserAccess()->get() → Bỏ qua lọc
     */
    public static function applyUserAccessScope(Builder $builder): void
    {
        // BƯỚC 1: Kiểm tra user đã đăng nhập chưa
        if (!Auth::check()) {
            return; // Chưa đăng nhập → không áp dụng scope
        }

        $user = Auth::user();

        // BƯỚC 2: Super Admin, Developer và Accountant → Full access
        if ($user->hasAnyRole(['super_admin', 'Super Accountant', 'Developer', 'Accountant', 'User Manager'])) {
            return; // Không thêm điều kiện WHERE nào → thấy tất cả
        }

        // BƯỚC 3: Fulfillment Manager → Full access
        if ($user->hasRole(['Fullfillment Manager'])) {
            return; // Không thêm điều kiện WHERE nào → thấy tất cả
        }

        // BƯỚC 4: Thu thập danh sách seller_id được phép xem
        $sellerIds = [];

        // BƯỚC 4a: Leader logic
        if ($user->hasRole('Leader')) {
            $sellerIds[] = $user->id; // Leader thấy records của chính mình

            // Lấy danh sách sellers được quản lý
            $leaderSellerIds = $user->leaderManagedSellers()->pluck('id')->toArray();
            $sellerIds = array_merge($sellerIds, $leaderSellerIds);
        }

        // BƯỚC 4b: Seller logic
        if ($user->hasRole('Seller')) {
            $sellerIds[] = $user->id; // Seller chỉ thấy records của mình
        }

        // BƯỚC 5: Loại bỏ duplicate IDs
        $sellerIds = array_unique($sellerIds);

        // BƯỚC 6: Áp dụng điều kiện WHERE
        if (!empty($sellerIds)) {
            // Có quyền → WHERE seller_id IN (1,2,3,...)
            $builder->whereIn('seller_id', $sellerIds);
        } else {
            // Không có quyền → WHERE 1 = 0 (không trả về gì)
            $builder->whereRaw('1 = 0');
        }
    }

    /**
     * Scope query without user access restrictions
     */
    public function scopeWithoutUserAccess(Builder $query): Builder
    {
        return $query->withoutGlobalScope('userAccess');
    }

    /**
     * Get query with user access applied manually
     */
    public function scopeWithUserAccess(Builder $query): Builder
    {
        static::applyUserAccessScope($query);
        return $query;
    }
}
