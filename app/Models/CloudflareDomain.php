<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CloudflareDomain extends Model
{
    public $timestamps = false;
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $keyType = 'string';
    
    protected $fillable = [
        'id',
        'name',
        'status',
        'nameservers',
        'ssl_status',
        'email_enabled',
        'cloudflare_api_key_id'
    ];

    protected $casts = [
        'nameservers' => 'array',
        'ssl_status' => 'boolean',
        'email_enabled' => 'boolean'
    ];

    public function apiKey()
    {
        return $this->belongsTo(CloudflareApiKey::class, 'cloudflare_api_key_id');
    }
}