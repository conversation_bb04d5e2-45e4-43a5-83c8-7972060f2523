<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;


class Proxy extends Model
{
    use HasFactory;

    protected $fillable = [
        'proxy', 'proxy_type', 'proxy_info', 'note', 'user_id', 'share_id', 'store_id','status'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function share()
    {
        return $this->belongsTo(User::class, 'share_id');
    }

    public function store()
    {
        return $this->hasOne(Store::class);
    }

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($proxy) {
            if (Auth::check()) {
                $proxy->user_id = Auth::id();
            }
       
        });
        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }
            return $builder->where(function ($query) {
                $query->where('user_id', Auth::id())
                      ->orWhere('share_id', Auth::id());
            });
            
        });
    }

}