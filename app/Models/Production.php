<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Production extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'seller_id',
        'blank_id',
        'product_id',
        'design_files',
        'status',
        'is_ready_for_video',
        'notes',
        'assigned_to',
        'quantity',
    ];

    protected $casts = [
        'design_files' => 'array',
        'is_ready_for_video' => 'boolean',
        'production_started_at' => 'datetime',
        'production_completed_at' => 'datetime',
        'quantity' => 'integer',
    ];

    protected $appends = ['design_files_urls'];

    // Thêm accessor cho design_files
    protected function designFilesUrls(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (!$this->design_files) return [];
                return array_map(function($file) {
                    return $file && is_string($file) ? asset('storage/' . $file) : null;
                }, $this->design_files);
            }
        );
    }

    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function blank()
    {
        return $this->belongsTo(Blank::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function assignedUser()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function mediaRequests()
    {
        return $this->hasMany(MediaRequest::class);
    }

    public function pendingMediaRequests()
    {
        return $this->mediaRequests()->whereIn('status', ['pending', 'in_progress', 'review']);
    }

    public function completedMediaRequests()
    {
        return $this->mediaRequests()->where('status', 'completed');
    }

    public function calculateProductionCost()
    {
        if (!$this->blank || !$this->quantity) {
            return 0;
        }

        $blankCost = $this->blank->cost ?? 0;
        $processingCost = $this->blank->processing_cost ?? 0;
        
        // Chi phí = (giá blank + chi phí gia công) * số lượng
        return ($blankCost + $processingCost) * $this->quantity;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($production) {
            // Tự động set seller_id từ product nếu có
            // if ($production->product_id && !$production->seller_id) {
            //     $production->seller_id = $production->product->seller_id;
            // }

            // Hoặc set seller_id là user hiện tại nếu không có product
            if (!$production->seller_id) {
                $production->seller_id = auth()->id();
            }
        });

        // Thêm global scope để phân quyền
        static::addGlobalScope('accessScope', function (Builder $query) {
            if (!auth()->check()) {
                return $query;
            }

            $user = auth()->user();

            // Super admin, User Manager, Developer, Fulfillment Manager, Accountant thấy tất cả
            if ($user->hasRole(['super_admin', 'User Manager', 'Developer', 'Fullfillment Manager', 'Fulfillment', 'Accountant'])) {
                return $query;
            }

            // Media Manager thấy tất cả productions có trong media request
            if ($user->hasRole('Media Manager')) {
                return $query;
            }

            // Production team thấy tất cả productions 
            if ($user->hasRole('Production')) {
                return $query;
            }

            $sellerIds = [];

            // Seller chỉ thấy productions của họ
            if ($user->hasRole('Seller')) {
                $sellerIds[] = $user->id;
            }

            // Leader có ưu tiên cao hơn Fulfillment
            if ($user->hasRole('Leader')) {
                $sellerIds[] = $user->id;
                $leaderSellerIds = $user->leaderManagedSellers()->pluck('users.id')->toArray();
                $sellerIds = array_merge($sellerIds, $leaderSellerIds);
            }
            // Fulfillment thấy productions của sellers được quản lý (chỉ khi không có role Leader)
            elseif ($user->hasRole('Fulfillment')) {
                $fulfillmentSellerIds = $user->fulfillmentManagedSellers()->pluck('users.id')->toArray();
                $sellerIds = array_merge($sellerIds, $fulfillmentSellerIds);
            }



            $sellerIds = array_unique($sellerIds);

            if (!empty($sellerIds)) {
                return $query->whereIn('seller_id', $sellerIds);
            }

            // Các role khác không thấy gì
            return $query->whereRaw('1 = 0');
        });
    }
}
