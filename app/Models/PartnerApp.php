<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;

class PartnerApp extends Model
{
    use HasFactory;
    protected $fillable = [
        'app_name',
        'app_key',
        'app_secret',
        'auth_link',
        'seller_id',
        'status',
        'proxy',
        'webhook_domain',
        'count_shop_connect'
    ];
    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }
    public function store()
    {
        return $this->hasMany(Store::class,'app_partner_id','id');
    }


    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }
            $user = Auth::user();
            if ($user->hasRole(['super_admin', 'User Manager', 'Developer', 'Fullfillment Manager', 'Fulfillment', 'Seller'])) {
                return $builder;
            }

            // Chỉ filter theo seller_id nếu user có role Seller
            if ($user->hasRole('Seller')) {
                return $builder->where('seller_id', $user->id);
            }

            // Nếu không có role phù hợp, không trả về PartnerApp nào
            return $builder->whereRaw('1 = 0');
        });
    }
}
