<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Lesson extends Model
{
    use HasFactory;

    protected $fillable = ['title', 'description', 'link', 'minutes', 'order', 'session_id'];

    public function session()
    {
        return $this->belongsTo(Session::class);
    }
    public function completions()
{
    return $this->belongsToMany(User::class, 'lesson_completions')->withTimestamps();
}
    public function completedByUsers()
    {
        return $this->belongsToMany(User::class, 'lesson_completions')->withTimestamps();
    }
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($lesson) {
            if (preg_match('/<iframe.*src="([^"]+)"/', $lesson->link, $matches)) {
                $lesson->link = $matches[1];
            }
        });
    }

    public function getPreviousLesson()
    {
        return Lesson::where('session_id', $this->session_id)
            ->where('order', '<', $this->order)
            ->orderBy('order', 'desc')
            ->first();
    }

    public function getNextLesson()
    {
        return Lesson::where('session_id', $this->session_id)
            ->where('order', '>', $this->order)
            ->orderBy('order', 'asc')
            ->first();
    }
}