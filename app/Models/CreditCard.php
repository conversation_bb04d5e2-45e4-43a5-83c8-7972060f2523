<?php

namespace App\Models;

use App\Enums\CardBonStatus;
use App\Enums\CardResult;
use App\Enums\CardStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;

class CreditCard extends Model
{
    use HasFactory;

    protected $fillable = [
        'active','checked',
        'status', 'result', 'status_bon', 'note', 'user_id', 'bin', 'bin_base', 'card', 'tags','price','groups'
    ];
    protected $casts = [
        'tags' => 'array',
        'groups' => 'array',
        'status' => CardStatus::class,
        'result' => CardResult::class,
        'status_bon' => CardBonStatus::class,
        'active' => 'boolean',
        'checked' => 'boolean',
    ];
    protected static function booted()
    {
        static::creating(function ($creditCard) {
            // Tự động lấy 6 ký tự đầu của card cho bin
            $creditCard->bin = substr($creditCard->card, 0, 6);

            // Tự động lấy 12 ký tự đầu của card cho bin_base
            $creditCard->bin_base = substr($creditCard->card, 0, 12);

            // Tự động gán user_id của người tạo


            if (is_null($creditCard->user_id)) {
                $creditCard->user_id = Auth::id();
            }

            if (is_null($creditCard->status)) {
                $creditCard->status = 'unused';
            }

            if (is_null($creditCard->status_bon)) {
                $creditCard->status_bon = 'not_started';
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }


    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }

            $user = Auth::user();
            if ($user->hasRole(['super_admin', 'User Manager', 'Developer'])) {
                return $builder;
            }


            return $builder->where('user_id',$user->id)->where('active',true);
        });
    }
}