<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class BusinessIntelligenceReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'period',
        'from_date',
        'to_date',
        'analysis_data',
        'ai_summary',
        'raw_prompt',
        'raw_embedding_data',
        'raw_embedding_prompt',
        'pdf_url',
        'pdf_filename',
        'total_products',
        'total_orders',
        'total_suppliers',
        'total_stores',
        'total_reports',
        'total_revenue',
        'avg_order_value',
        'status',
        'error_message',
        'created_by'
    ];

    protected $casts = [
        'analysis_data' => 'array',
        'from_date' => 'date',
        'to_date' => 'date',
        'total_revenue' => 'decimal:2',
        'avg_order_value' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Relationship với User (người tạo báo cáo)
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope để lọc theo period
     */
    public function scopeByPeriod($query, string $period)
    {
        return $query->where('period', $period);
    }

    /**
     * Scope để lọc theo date range
     */
    public function scopeByDateRange($query, $fromDate, $toDate)
    {
        return $query->whereBetween('from_date', [$fromDate, $toDate])
                    ->orWhereBetween('to_date', [$fromDate, $toDate]);
    }

    /**
     * Scope để lọc báo cáo hoàn thành
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Accessor để format period text
     */
    public function getPeriodTextAttribute(): string
    {
        return match($this->period) {
            'daily' => 'Hàng ngày',
            'weekly' => 'Hàng tuần',
            'monthly' => 'Hàng tháng',
            'custom' => 'Tùy chỉnh',
            default => ucfirst($this->period)
        };
    }

    /**
     * Accessor để format date range
     */
    public function getDateRangeAttribute(): string
    {
        return $this->from_date->format('d/m/Y') . ' - ' . $this->to_date->format('d/m/Y');
    }

    /**
     * Check if report is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if report failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if report is generating
     */
    public function isGenerating(): bool
    {
        return $this->status === 'generating';
    }
}
