<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'banner', 'description'];

    public function sessions()
    {
        return $this->hasMany(Session::class);
    }

    public function lessons()
    {
        return $this->hasManyThrough(
            Lesson::class, 
            Session::class
        )->select('lessons.*'); // Chọn tất cả các cột từ bảng lessons
    }
}