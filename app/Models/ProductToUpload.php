<?php

namespace App\Models;

use App\Enums\ProductUploadStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ProductToUpload extends Model
{
    use HasFactory;

    protected $fillable = [
        'task_id',
        'store_id',
        'template_id',
        'product_title',
        'description',
        'warehouse_id',
        'save_mode',
        'size_chart_url',
        'status',
        'scheduled_at',
        'uploaded_at',
        'user_id',
        'product_id',
        'public_product_id',
        'error_message',
        'images',
        'product_source_id'
    ];

    protected $casts = [
        'status' => ProductUploadStatus::class,
        'scheduled_at' => 'datetime',
        'uploaded_at' => 'datetime',
        'images' => 'array'
    ];

    // Relationships
    public function productSource()
    {
        return $this->belongsTo(ProductSource::class);
    }
    public function task()
    {
        return $this->belongsTo(ProductUploadTask::class, 'task_id');
    }

    public function template()
    {
        return $this->belongsTo(Template::class, 'template_id');
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // Scopes
    public function scopeReadyToUpload($query)
    {
        return $query->where('status', ProductUploadStatus::Pending)
            ->where('scheduled_at', '<=', now());
    }

    public function scopePending($query)
    {
        return $query->where('status', ProductUploadStatus::Pending)
            ->where('scheduled_at', '>', now());
    }

    // Helper Methods
    public static function createFromForm($formData, $store)
    {
        return self::create([
            'store_id' => $store->id,
            'template_id' => $formData['templateId'],
            'product_title' => $formData['productTitle'],
            'description' => $formData['productDescription'],
            'warehouse_id' => $formData['warehouseId'],
            'save_mode' => $formData['saveMode'],
            'size_chart_url' => $formData['sizeChartImage'],
            'status' => ProductUploadStatus::Pending,
            'scheduled_at' => $formData['scheduled_at'] ?? now(),
        ]);
    }

    // Lấy SKUs từ template
    public function getSkus()
    {
        return $this->template->skus ?? [];
    }

    // Lấy images từ template
    public function getImages()
    {
        return $this->template->images ?? [];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->user_id && $model->store_id) {
                $store = Store::find($model->store_id);
                if ($store) {
                    $model->user_id = $store->owner_id;
                }
            }
        });

        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }
            $user = Auth::user();
            if ($user->hasRole(['super_admin', 'User Manager', 'Developer'])) {
                return $builder;
            }
            return $builder->where('user_id', Auth::id());
        });
    }
}
