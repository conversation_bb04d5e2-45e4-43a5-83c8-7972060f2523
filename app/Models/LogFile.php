<?php

namespace App\Models;

use Illuminate\Support\Facades\File;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class LogFile
{
    public $id;
    public $name;
    public $size;
    public $modified;
    public $path;

    public function __construct($file)
    {
        $this->id = $file->getPathname();
        $this->name = $file->getFilename();
        $this->size = number_format($file->getSize() / 1024, 2) . ' KB';
        $this->modified = date('Y-m-d H:i:s', $file->getMTime());
        $this->path = $file->getPathname();
    }

    public static function all(): Collection
    {
        $logs_path = storage_path('logs');
        return collect(File::files($logs_path))
            ->filter(fn($file) => File::extension($file) === 'log')
            ->map(fn($file) => new static($file));
    }
}
