<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class MockupTemplateGroup extends Model
{
    protected $fillable = [
        'name',
        'description',
        'user_id',
        'is_public'
    ];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (auth()->check()) {
                $model->user_id = auth()->id();
            }
        });

        // Thêm Global Scope để tự động lọc
        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (auth()->check()) {
                $user = auth()->user();

                // Super admin thấy hết
                if ($user->hasRole(['super_admin', 'User Manager'])) {
                    return;
                }

                // User thường chỉ thấy public hoặc của mình
                $builder->where(function ($query) use ($user) {
                    $query->where('is_public', true)
                        ->orWhere('user_id', $user->id);
                });
            } else {
                // Guest chỉ thấy public
                $builder->where('is_public', true);
            }
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function configs(): BelongsToMany
    {
        return $this->belongsToMany(
            MockupTemplateConfig::class,
            'mockup_template_mockup_template_group',
            'mockup_template_group_id',
            'mockup_template_id'
        )->withTimestamps()->withoutGlobalScopes();
    }
}
