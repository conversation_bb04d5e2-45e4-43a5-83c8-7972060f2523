<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use App\Models\SellerFinance;
use App\Services\SellerService;
use Carbon\Carbon;

class Invoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'invoice_number',
        'issue_date',
        'due_date',
        'billing_month',
        'total_amount',
        'paid_amount',
        'status',
        'payment_method',
        'payment_date',
        'reference_number',
        'evidence_url',
        'notes',
        'payment_notes',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'issue_date' => 'date',
        'due_date' => 'date',
        'billing_month' => 'date',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'approved_at' => 'datetime',
        'payment_date' => 'datetime'
    ];

    // Seller được thanh toán
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Admin tạo hoá đơn
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Admin duyệt hoá đơn
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Items của hoá đơn
    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    // Finance items
    public function financeItems(): HasMany
    {
        return $this->hasMany(InvoiceItem::class)
            ->where('type', 'finance');
    }

    // Regular items
    public function regularItems(): HasMany
    {
        return $this->hasMany(InvoiceItem::class)
            ->where('type', 'regular');
    }

    /**
     * Tính lại số tiền đã thanh toán
     */
    protected function paidAmount(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                // Nếu đã thanh toán đầy đủ, trả về tổng số tiền
                if ($this->status === 'paid') {
                    return $this->total_amount;
                }

                // Nếu đã hủy, trả về 0
                if ($this->status === 'cancelled') {
                    return 0;
                }

                // Nếu chưa thanh toán hoặc đã có thanh toán một phần, trả về giá trị đã lưu
                return $value ?? 0;
            },
            set: fn ($value) => $value,
        );
    }

    /**
     * Tính tổng số tiền từ items
     */
    protected function totalAmount(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                // Nếu đã có giá trị trong database và không có items mới
                if ($value > 0 && !$this->relationLoaded('items')) {
                    return $value;
                }

                // Tính tổng từ items
                $total = $this->items->sum('amount');


                // Nếu có items thì lấy tổng mới, không thì giữ giá trị cũ
                return $total > 0 ? $total : ($value ?? 0);
            },
            set: function ($value) {
                // Log để debug việc set giá trị

                return $value;
            },
        );
    }

    /**
     * Trạng thái thanh toán
     */
    public function getPaymentStatusAttribute(): string
    {
        if ($this->status === 'cancelled') return 'cancelled';
        if ($this->status === 'paid') return 'paid';
        if ($this->paid_amount <= 0) return 'unpaid';
        if ($this->paid_amount < $this->total_amount) return 'partial';
        return 'paid';
    }

    /**
     * Số tiền còn lại cần thanh toán
     */
    public function getRemainingAmount(): float
    {
        if ($this->status === 'cancelled') return 0;
        if ($this->status === 'paid') return 0;
        return $this->total_amount - $this->paid_amount;
    }

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }

            $user = Auth::user();

           // Super admin, Super Accountant và Developer có thể xem tất cả
            if ($user->hasRole(['super_admin', 'Super Accountant', 'Developer', 'Accountant'])) {
                return $builder;
            }


            if ($user->hasRole('Leader')) {
                $sellerIds = $user->leaderManagedSellers()->pluck('id')->toArray();
                $sellerIds[] = $user->id; // Leader cũng thấy invoice của chính mình
                return $builder->whereIn('user_id', $sellerIds);
            }

            // Accountant xem được invoice của các seller họ quản lý
            // if ($user->hasRole('Accountant')) {
            //     $sellerIds = $user->leaderManagedSellers()->pluck('id');
            //     return $builder->whereIn('user_id', $sellerIds);
            // }

            // Seller chỉ xem được invoice của mình
            if ($user->hasRole('Seller')) {
                return $builder->where('user_id', $user->id);
            }
            // Nếu không có role phù hợp, không thấy gì cả
            return $builder->whereRaw('1 = 0');
        });

        static::creating(function ($invoice) {
            // Set người tạo
            if (!$invoice->created_by) {
                $invoice->created_by = auth()->id();
            }

            // Tạo invoice number nếu chưa có
            if (!$invoice->invoice_number) {
                $prefix = 'INV-' . now()->format('Ymd');
                $lastInvoice = self::where('invoice_number', 'like', $prefix . '%')
                    ->orderBy('invoice_number', 'desc')
                    ->first();

                $number = $lastInvoice
                    ? (int)substr($lastInvoice->invoice_number, -4) + 1
                    : 1;

                $invoice->invoice_number = $prefix . '-' . str_pad($number, 4, '0', STR_PAD_LEFT);
            }
        });

        static::updating(function ($invoice) {
            // Khi chuyển trạng thái sang approved
            if ($invoice->isDirty('status') && $invoice->status === 'approved') {
                $invoice->approved_by = auth()->id();
                $invoice->approved_at = now();
            }

            // Khi chuyển trạng thái
            if ($invoice->isDirty('status')) {
                switch($invoice->status) {
                    case 'paid':
                        $invoice->payment_date = $invoice->payment_date ?? now();
                        $invoice->paid_amount = $invoice->total_amount;
                        break;
                    case 'cancelled':
                        $invoice->paid_amount = 0;
                        break;
                    // Không reset paid_amount cho các trạng thái khác để giữ giá trị thanh toán một phần
                }
            }
        });

        static::saved(function ($invoice) {
            // Tính lại tổng sau khi lưu invoice và items
            $newTotal = $invoice->items()->sum('amount');



            // Chỉ cập nhật nếu có thay đổi
            if ($newTotal != $invoice->total_amount) {
                $invoice->forceFill(['total_amount' => $newTotal])->saveQuietly();
            }
        });
    }

    /**
     * Kiểm tra xem user có quyền quản lý hóa đơn không
     *
     * @param  \App\Models\User|null  $user
     * @return bool
     */
    public static function canManageInvoices(?User $user = null): bool
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            return false;
        }

        return $user->hasRole(['super_admin', 'Super Accountant', 'accountant']);
    }

    /**
     * Kiểm tra xem user có quyền xem hóa đơn này không
     *
     * @param  \App\Models\User|null  $user
     * @return bool
     */
    public function canView(?User $user = null): bool
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            return false;
        }

        // Super admin và Super Accountant có thể xem tất cả
        if ($user->hasRole(['super_admin', 'Super Accountant'])) {
            return true;
        }

        // Leader xem được invoice của các seller dưới quyền
        if ($user->hasRole('Leader')) {
            $managedSellers = $user->leaderManagedSellers();
            $sellerIds = $managedSellers->pluck('id')->toArray();
            $sellerIds[] = $user->id; // Leader cũng thấy invoice của chính mình
            return in_array($this->user_id, $sellerIds);
        }

        // Accountant xem được invoice của các seller họ quản lý
        if ($user->hasRole('accountant')) {
            $managedSellers = $user->accountantManagedSellers();
            $sellerIds = $managedSellers->pluck('id')->toArray();
            $sellerIds[] = $user->id; // Accountant cũng thấy invoice của chính mình
            return in_array($this->user_id, $sellerIds);
        }

        // Seller chỉ xem được invoice của mình
        if ($user->hasRole('Seller')) {
            return $this->user_id === $user->id;
        }

        return false;
    }

    /**
     * Kiểm tra xem user có quyền duyệt hóa đơn này không
     *
     * @param  \App\Models\User|null  $user
     * @return bool
     */
    public function canApprove(?User $user = null): bool
    {
        return $this->status === 'pending' && static::canManageInvoices($user);
    }

    /**
     * Kiểm tra xem user có quyền xác nhận hóa đơn này không
     *
     * @param  \App\Models\User|null  $user
     * @return bool
     */
    public function canConfirm(?User $user = null): bool
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            return false;
        }

        return $this->status === 'approved' &&
            ($this->user_id === $user->id || $user->hasRole('super_admin') || $user->hasRole('Accountant'));
    }

    /**
     * Kiểm tra xem user có quyền thanh toán hóa đơn này không
     *
     * @param  \App\Models\User|null  $user
     * @return bool
     */
    public function canPay(?User $user = null): bool
    {
        return $this->status === 'confirmed' && static::canManageInvoices($user);
    }

    /**
     * Chuẩn bị dữ liệu cho việc in hóa đơn, đồng bộ với định dạng từ SellerInvoice
     *
     * @return array
     */
    public function getPrintData(): array
    {
        // Lấy thông tin seller
        $seller = $this->user;

        // Tìm báo cáo tài chính liên quan đến tháng của hóa đơn này
        $sellerFinance = null;
        if ($this->billing_month) {
            $sellerFinance = SellerFinance::where('seller_id', $seller->id)
                ->whereYear('month', $this->billing_month->year)
                ->whereMonth('month', $this->billing_month->month)
                ->first();
        }

        // Lấy thông tin thanh toán và đơn hàng
        $ordersCount = 0;
        $completedOrdersCount = 0;
        $pendingOrdersCount = 0;
        $cancelledOrdersCount = 0;


        if ($this->billing_month) {
            $startDate = Carbon::parse($this->billing_month)->startOfMonth();
            $endDate = Carbon::parse($this->billing_month)->endOfMonth();

            $ordersQuery = $seller->orders()->whereBetween('created_at', [$startDate, $endDate]);
            $ordersCount = $ordersQuery->count();
            $completedOrdersCount = (clone $ordersQuery)->where('status', 'Completed')->count();
            $pendingOrdersCount = (clone $ordersQuery)->where('status', 'Pending')->count();
            $cancelledOrdersCount = (clone $ordersQuery)->where('status', 'Cancelled')->count();
        }

        // Lấy thông tin thanh toán
        $payments = $this->items()->get()->map(function ($item) {
            return [
                'description' => $item->description ?: 'Thu nhập tháng ' . ($this->billing_month ? $this->billing_month->format('m/Y') : ''),
                'amount' => $item->amount,
            ];
        })->toArray();


        if (count($payments) === 0) {
            $payments[] = [
                'description' => 'Thu nhập tháng ' . ($this->billing_month ? $this->billing_month->format('m/Y') : ''),
                'amount' => $this->total_amount,
            ];

        }

        $bankPayout = $sellerFinance->commission_details['bank_payments']['sum'];
        $totalCosts = $sellerFinance->total_cost;
        $previousMonthLoss = $sellerFinance->previous_loss;

        //
        // Lãi trước thu nhập seller = Bank Payout - Tổng chi phí - Lỗ tháng trước
        $realProfitBeforeSellerIncome = $bankPayout - $totalCosts - $previousMonthLoss;

        // Tính dự tính tổng tiền chưa về của seller
        $pendingRevenue = 0;
        try {
            // Tạo SellerService để tính pending revenue (không cần thời gian vì hàm lấy tất cả dữ liệu)
            $sellerService = new SellerService($seller, now()->startOfMonth(), now()->endOfMonth());
            $pendingRevenue = $sellerService->calculatePendingRevenue();
        } catch (\Exception $e) {
            \Log::error('Error calculating pending revenue in invoice: ' . $e->getMessage(), [
                'invoice_id' => $this->id,
                'seller_id' => $seller->id
            ]);
        }


        // Chuẩn bị dữ liệu theo format trong SellerInvoice
        $data = [
            'invoiceNumber' => $this->invoice_number,
            'issueDate' => $this->issue_date ? $this->issue_date->format('d/m/Y') : '',
            'dueDate' => $this->due_date ? $this->due_date->format('d/m/Y') : '',
            'status' => $this->status,
            'reportPeriod' => $this->billing_month ? $this->billing_month->format('m/Y') : '',

            // Thông tin seller
            'seller' => [
                'name' => $seller->name,
                'email' => $seller->email,
                'phone' => $seller->phone_number ?? 'N/A',
                'bankName' => $seller->bank_name ?? 'N/A',
                'bankAccount' => $seller->bank_account_number ?? 'N/A',
                'level' => $seller->sellerLevel ? $seller->sellerLevel->name : 'Basic',
            ],

            // Dự tính tổng tiền chưa về
            'pendingRevenue' => $pendingRevenue,

            // Doanh thu
            'revenue' => [
                'grossRevenue' => $sellerFinance ? $sellerFinance->gross_revenue : 0,
                'platformFee' => $sellerFinance ? $sellerFinance->platform_fees : 0,
                'netRevenue' => $sellerFinance ? $sellerFinance->net_revenue : 0,
                'onHold' => $sellerFinance ? ($sellerFinance->payout_on_hold ?? 0) : 0,
            ],

            // Chi phí
            'costs' => $sellerFinance->costs,

            // Tổng chi phí
            'totalCost' => $sellerFinance->total_cost,

            // baseSalary
            'baseSalary' => $sellerFinance ? $sellerFinance->base_salary : 0,

            // Thông tin đơn hàng
            'orders' => [
                'totalOrders' => $ordersCount,
                'completedOrders' => $completedOrdersCount,
                'pendingOrders' => $pendingOrdersCount,
                'cancelledOrders' => $cancelledOrdersCount,
            ],

            // Lợi nhuận
            'profit' => [
                'grossProfit' => $sellerFinance ? $sellerFinance->gross_profit : 0,
                'previousMonthLoss' => $sellerFinance ? $sellerFinance->previous_loss : 0,
                'adjustedProfit' => $sellerFinance ? $sellerFinance->adjusted_profit : 0,
                'profitMargin' => $sellerFinance && $sellerFinance->gross_revenue > 0
                    ? ($sellerFinance->gross_profit / $sellerFinance->gross_revenue * 100)
                    : 0,
            ],
            
            'realProfitBeforeSellerIncome' => $realProfitBeforeSellerIncome,

            // Tiktok payout
            'tiktokPayout' => isset($sellerFinance->commission_details['tiktok_payments']) ? $sellerFinance->commission_details['tiktok_payments'] : 0,
            //Bank payout
            'bankPayout' => isset($sellerFinance->commission_details['bank_payments']) ? $sellerFinance->commission_details['bank_payments'] : 0,

            // hoa hồng
            'commissions' => $sellerFinance->commission,

            // Hoa hồng & thu nhập
            'commissions_details' => $sellerFinance->commission_details,


            // Tổng thu nhập
            'totalSalary' => $sellerFinance->total_salary,

            // Thanh toán
            'payments' => $payments,
            'totalPayment' => $this->total_amount,

            // Thông tin thanh toán ngân hàng và TikTok (nếu có)
            'bankPayments' => $sellerFinance && isset($sellerFinance->bank_payments)
                ? $sellerFinance->bank_payments
                : ['sum' => 0, 'count' => 0],

        ];

        // dd($data);

        return $data;
    }

     /**
     * Chuẩn bị dữ liệu cho việc in hóa đơn, đồng bộ với định dạng từ SellerInvoice
     *
     * @return array
     */
    public function getPrintDataForDesign(): array
    {
        // Lấy thông tin seller
        $seller = $this->user;

        dd($seller);

        $data = [
            'invoiceNumber' => $this->invoice_number,
            'issueDate' => $this->issue_date ? $this->issue_date->format('d/m/Y') : '',
            'dueDate' => $this->due_date ? $this->due_date->format('d/m/Y') : '',
            'status' => $this->status,
            'reportPeriod' => $this->billing_month ? $this->billing_month->format('m/Y') : '',

            // Thông tin seller
            'designer' => [
                'name' => $seller->name,
                'email' => $seller->email,
                'phone' => $seller->phone_number ?? 'N/A',
                'bankName' => $seller->bank_name ?? 'N/A',
                'bankAccount' => $seller->bank_account_number ?? 'N/A',
                'level' => $seller->sellerLevel ? $seller->sellerLevel->name : 'Basic',
            ],
        ];

        return $data;
    }

    /**
     * Xử lý route để in hóa đơn
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Invoice  $invoice
     * @return \Illuminate\View\View
     */
    public static function renderPrintView(\Illuminate\Http\Request $request, Invoice $invoice)
    {
        try {
            // Lấy thông tin seller
            $seller = $invoice->user;

            // Lấy dữ liệu in hóa đơn
            $invoiceData = $invoice->getPrintData();

            // Render view với dữ liệu
            return view('invoice.print', [
                'invoice' => $invoiceData,
                'seller' => $seller,
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Lỗi khi in hóa đơn', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            abort(500, 'Lỗi khi in hóa đơn: ' . $e->getMessage());
        }
    }

}