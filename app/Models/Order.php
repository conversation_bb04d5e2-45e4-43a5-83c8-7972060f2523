<?php

namespace App\Models;

use App\Enums\OrderStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Parallax\FilamentComments\Models\Traits\HasFilamentComments;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Telegram\Bot\Api;
use App\Traits\HasUserAccessScope;

class Order extends Model
{
    use HasFilamentComments;
    use LogsActivity;
    use HasUserAccessScope;

    /**
     * The attributes that are mass assignable.
     *
     * Recommended indexes for performance:
     * - seller_id (foreign key, frequently filtered)
     * - store_id (foreign key, frequently joined)
     * - order_number (unique with store_id)
     * - status (frequently filtered)
     * - created_at (date ranges)
     * - handler_id (foreign key)
     * - order_code (lookups)
     */
    protected $fillable = [
        'seller_id', 'store_id', 'order_number', 'total', 'shipping_cost', 'status',
        'shipping_first_name', 'shipping_last_name', 'shipping_email', 'shipping_phone',
        'fulfillment_type',
        'shipping_country', 'shipping_region', 'shipping_address_line1', 'shipping_address_line2',
        'shipping_city', 'shipping_zip', 'total_revenue','store_order_status','label','note','order_code','handler_id','buyer_note',
        'settlement_amount'
    ];
    public function getDescriptionForEvent(string $eventName): string
    {
        return "Order '{$this->order_code}' has been {$eventName}";
    }

    protected $casts = [
        'status' => OrderStatus::class,
    ];


    protected static $logFillable = true;
    protected static $logOnlyDirty = true;
    protected static $submitEmptyLogs = false;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'order_number', 'shipping_address_line1', 'total', 'store_order_status']) // Removed 'orderItems' as it's a relationship, not a column
            ->useLogName('order')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function supplier_orders()
    {
        return $this->hasMany(SupplierOrder::class);
    }
    public static function onlyMe()
    {
        if(Auth::user()->hasRole('super_admin')){
            return self::query();
        }
        return self::query()->where('seller_id', Auth::id());
    }
    public function scopeFilterByRole($query)
    {
        return $query;
        if (Auth::user()->hasRole('super_admin')) {
            return $query; // Super admin xem tất cả đơn hàng
        } else {
            return $query->where('seller_id', Auth::id()); // Các user khác chỉ xem đơn hàng của mình
        }
    }


    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }
    public function SupplierOrders()
    {
        return $this->hasMany(SupplierOrder::class ,'order_id')->withoutGlobalScope('accessScope');
    }

    public function seller()
    {
        return $this->belongsTo(User::class ,'seller_id');
    }

    public function handler()
    {
        return $this->belongsTo(User::class,'handler_id');
    }

    public function designs()
    {
        return $this->hasManyThrough(Design::class, OrderItem::class);
    }

    public function updateTotal()
    {
        //$total_cost = $this->orderItems()->sum('base_cost');
        //$total = $this->orderItems()->sum('total');
        //$this->total = $total;
        //$this->total_revenue = $total_revenue;
       // $this->save();
    }

    protected static function booted()
    {

        static::saving(function ($order) {

            // Chỉ kiểm tra validation nếu order_number hoặc store_id thay đổi
            if ($order->isDirty('store_id')) {
                $store = Store::where('id', $order->id)->first();
                if ($store) {
                    $order->seller_id =  $store->owner_id;
                }
            }
            if ($order->isDirty('order_number') || $order->isDirty('store_id')) {
                $validator = Validator::make($order->only(['order_number', 'store_id']), [
                    'order_number' => [
                        'required',
                        Rule::unique('orders', 'order_number')
                            ->where('store_id', $order->store_id)
                            ->ignore($order->id),
                    ],
                ]);

                if ($validator->fails()) {
                    throw new \Illuminate\Validation\ValidationException($validator);
                    return false;
                }
            }
        });

        static::creating(function ($order) {
            $store = Store::where('id', $order->id)->first();
            if ($store) {
                $order->seller_id =  $store->owner_id;
            }

            if (!$order->total) {
                $order->total = 0;
            }
        });
    }

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($order) {
            $order->order_code = 'K-Seller[' .$order->seller_id . ']-Store[' . $order->store_id . ']-' . $order->order_number ;
        });

        static::created(function ($order) {
            try {
                $seller = $order->seller;
                if (empty($seller->telegram_chat_id)) {
                    \Illuminate\Support\Facades\Log::error("Seller {$seller->id} không có chat_id");
                    return;
                }

                $chatId = $seller->telegram_chat_id;
                $store = $order->store;

                // Tải order items để hiển thị thông tin sản phẩm
                $order->load('orderItems');

                // Kiểm tra xem có orderItems không
                if ($order->orderItems->isEmpty()) {
                    \Illuminate\Support\Facades\Log::error("Đơn hàng {$order->id} không có sản phẩm");
                }

                // Tạo thông báo chi tiết hơn
                $message = "🛍️ *ĐƠN HÀNG MỚI!*\n\n"
                    . "📦 *Thông tin đơn hàng:*\n"
                    . ($store ? "• Cửa hàng: *{$store->name}*\n" : "")
                    . "• Tổng tiền: *" . number_format($order->total, 0, ',', '.') . " VND*\n"
                    . "• Phí ship: *" . number_format($order->shipping_cost, 0, ',', '.') . " VND*\n";

                // Thêm thông tin chi tiết sản phẩm
                $message .= "\n🛒 *Chi tiết sản phẩm:*\n";

                // Dùng foreach và bắt lỗi để đảm bảo không bị lỗi khi duyệt
                try {
                    if ($order->orderItems->count() > 0) {
                        foreach ($order->orderItems as $index => $item) {
                            $message .= ($index + 1) . ". *{$item->name}* - SL: {$item->quantity}\n"
                                . "   Đơn giá: " . number_format($item->price, 0, ',', '.') . " VND\n";
                        }
                    } else {
                        $message .= "Không có sản phẩm nào\n";
                    }
                } catch (\Exception $e) {
                    $message .= "Không thể hiển thị chi tiết sản phẩm\n";
                    \Illuminate\Support\Facades\Log::error("Lỗi khi hiển thị chi tiết sản phẩm: " . $e->getMessage());
                }
                // Thêm đường dẫn đến hệ thống
                $message .= "\n👉 Vui lòng đăng nhập vào hệ thống để xử lý đơn hàng.";

                // Gửi thông báo qua Telegram
                $telegram = new Api(config('telegram.bots.mybot.token'));
                $telegram->sendMessage([
                    'chat_id' => (string) $chatId,
                    'text' => $message,
                    'parse_mode' => 'Markdown'
                ]);
            } catch (\Throwable $th) {
                \Illuminate\Support\Facades\Log::error('Lỗi gửi thông báo Telegram cho đơn hàng mới', [
                    'order_id' => $order->id,
                    'order_code' => $order->order_code,
                    'error' => $th->getMessage()
                ]);
            }
        });
        // Global scope được xử lý bởi HasUserAccessScope trait
    }

    public function tiktokSettlement()
    {
        return $this->hasOne(TiktokSettlement::class, 'tiktok_order_id', 'order_number');
    }
}
