<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Bank extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'account_email',
        'owner_id',
        'account_nickname',
        'account_status',
        'currency',
        'usage',
        'bank_account_number',
        'iban',
        'bank_name',
        'bank_location',
        'account_holders_name',
        'account_type',
        'swift_code',
        'routing_number',
        'sort_code',
        'institution_number',
        'transit_number',
        'zip_code',
        'city',
        'bank_address',
        'note',
    ];

    protected $casts = [
        'account_status' => 'boolean',
    ];

    // Relationship with User/Owner
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }



    // Relationship with Stores based on bank account number
    public function store()
    {
        return $this->hasOne(Store::class, 'bank_account', 'bank_account_number');
    }

    // Helper method to format account number for display
    public function getFormattedAccountNumber()
    {
        if (empty($this->bank_account_number)) {
            return null;
        }
        
        // Only show last 4 digits
        return '****' . substr($this->bank_account_number, -4);
    }

    // Validate IBAN format
    public function isValidIBAN()
    {
        if (empty($this->iban)) {
            return false;
        }
        
        // Remove spaces and convert to uppercase
        $iban = strtoupper(str_replace(' ', '', $this->iban));
        
        // Basic IBAN format check
        return (bool) preg_match('/^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$/', $iban);
    }

    // Scope to get active banks
    public function scopeActive($query)
    {
        return $query->where('account_status', true);
    }

    // Scope to get banks by currency
    public function scopeByCurrency($query, $currency)
    {
        return $query->where('currency', strtoupper($currency));
    }

    // Get total number of linked stores
    public function getLinkedStoresCountAttribute()
    {
        return $this->stores()->count();
    }

    public function getTotalReceivedAttribute()
    {
        return $this->store->payoutTransactions()->where('type', 'Receive')->where('status', 'Success')->sum('amount');
    }
}
