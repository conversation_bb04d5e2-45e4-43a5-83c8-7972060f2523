<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductSource extends Model
{
    protected $fillable = [
        'user_id',
        'title',
        'description',
        'images',
        'source_type',
        'source_url',
        'source_data',
        'status',
        'external_id',
        'metadata',
        'template_id',
        'trademark',
        'store_note'
    ];

    protected $casts = [
        'images' => 'array',
        'source_data' => 'array',
        'metadata' => 'array',
    ];

    protected $with = ['productToUploads'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function productToUploads()
    {
        return $this->hasMany(ProductToUpload::class)->orderBy('id', 'desc');
    }
    public function template()
    {
        return $this->belongsTo(Template::class);
    }
    
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($productSource) {
            if (!$productSource->user_id) {
                $productSource->user_id = auth()->id();
            }
            if (empty($productSource->source_url)) {
                $productSource->source_type = 'manual';
                $productSource->source_data = [
                    'platform' => 'manual',
                    'upload_type' => 'manual',
                    'created_at' => now()->toIso8601String()
                ];
                $productSource->metadata = [
                    'upload_method' => 'manual',
                    'source' => 'direct_upload'
                ];
                $productSource->status = 'active';
            }
        });

        static::addGlobalScope('user', function ($query) {
            $user = auth()->user();
            
            if (!$user || $user->hasRole('super_admin')) {
                return; // Super admin thấy tất cả
            }
            
            // Khởi tạo collection các user_id được phép xem
            $allowedUserIds = collect([$user->id]); // Luôn bao gồm chính mình
            
            // Nếu là Leader, thêm các seller được quản lý
            if ($user->hasRole('leader')) {
                $managedSellerIds = $user->leaderManagedSellers()->pluck('users.id');
                $allowedUserIds = $allowedUserIds->merge($managedSellerIds);
            }
            
            // Nếu là Seller, thêm các thành viên cùng team
            if ($user->hasRole('seller')) {
                $teamUserIds = User::whereHas('teams', function($q) use ($user) {
                    $q->whereIn('teams.id', $user->teams->pluck('id'));
                })->pluck('id');
                
                $allowedUserIds = $allowedUserIds->merge($teamUserIds);
            }
            
            // Áp dụng điều kiện với unique ids
            $query->whereIn('user_id', $allowedUserIds->unique()->values()->all());
        });
    }
}
