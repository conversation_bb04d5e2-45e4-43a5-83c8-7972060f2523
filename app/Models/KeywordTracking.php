<?php

namespace App\Models;

use App\Enums\KeywordTrackingStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Auth;

class KeywordTracking extends Model
{
    protected $fillable = [
        'store_id',
        'seller_id',
        'seasonal',
        'keyword',
        'status',
        'result',
        'rating',
        'note',
        'created_by',
    ];

    protected $casts = [
        'status' => KeywordTrackingStatus::class,

        'result' => 'string',
        'rating' => 'integer',
    ];

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }
    public function ideas(): HasMany
    {
        return $this->hasMany(Idea::class);
    }

    protected static function booting(): void
    {
        static::addGlobalScope('accessScope', function (Builder $query) {
            if (app()->runningInConsole()) {
                return $query;
            }
        
            $user = auth()->user();
        
            if ($user->hasRole(['super_admin', 'User Manager'])) {
                return $query;
            }
        
            // Thêm điều kiện cho Leader
            if ($user->hasRole('Leader')) {
                $managedSellers = $user->leaderManagedSellers();
                $managedSellerIds = $managedSellers->pluck('id')->toArray();
                $managedSellerIds[] = $user->id; // Thêm chính leader

                return $query->where(function ($query) use ($user, $managedSellerIds) {
                    $query->whereIn('seller_id', $managedSellerIds)  // Keywords của sellers được quản lý
                        ->orWhere('created_by', $user->id);        // Hoặc keywords do mình tạo
                });
            }
        
            // Các user khác thấy keywords của mình và do mình tạo
            return $query->where(function ($query) use ($user) {
                $query->where('seller_id', $user->id)
                    ->orWhere('created_by', $user->id);
            });
        });
        static::creating(function ($model) {
            if (auth()->check()) {
                $model->created_by = auth()->id();
                if (!$model->seller_id) {
                    $model->seller_id = auth()->id();
                }
            }
        });
        static::created(function ($model) {});
    }
}