<?php

namespace App\Models;

use App\Enums\FileLocation;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ProductVariant extends Model
{
    protected $fillable = [
        'product_id',
        'seller_id',
        'variant_id',
        'variant_name',
        'variant_fulfill_name',
        'variant_fulfill_id',
        'supplier_id',
        'design_front_url',
        'design_back_url',
        'sleeve_right_design_url',
        'sleeve_left_design_url',
        'mockup_front_url',
        'mockup_back_url',
        'specifications',
        'auto_fulfill',
        'auto_fulfill_enabled_by',
        'auto_fulfill_enabled_at',
    ];

    protected $casts = [
        'specifications' => 'array',
        'auto_fulfill' => 'boolean',
        'auto_fulfill_enabled_at' => 'datetime',
    ];

    protected static function booted()
    {
        static::addGlobalScope('access', function (Builder $builder) {
            if (Auth::check()) {
                if (!Auth::user()->hasRole(['super_admin', 'Fullfillment Manager', 'User Manager'])) {
                    $builder->where('seller_id', Auth::id());
                }
            } else {
                // $builder->whereNull('id'); // Không trả về kết quả nếu không đăng nhập
            }
        });
    }
    public function canAutoFulfill(): bool
    {
        $design = $this->product->design;

        if (empty($design->required_locations)) {
            return true; // Nếu không có vị trí bắt buộc, coi như có thể auto fulfill
        }

        foreach ($design->required_locations as $location) {
            $locationEnum = FileLocation::tryFrom($location);

            if (!$locationEnum) {
                // Log hoặc xử lý trường hợp location không hợp lệ
                continue;
            }
            $databaseField = $locationEnum->getDatabaseField();

            if (empty($this->$databaseField)) {
                return false; // Thiếu URL cho vị trí bắt buộc
            }
        }

        return true;
    }
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function autoFulfillEnabledBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'auto_fulfill_enabled_by');
    }


    public function getDesignUrls()
    {
        $product = $this->product;
        $design = $product->design;

        if (!$design) {
            return null;
        }

        $designFiles = $design->designFiles;
        $color = $this->extractColorFromVariantName();

        $frontDesign = $this->getDesignFileForPosition($designFiles, 'printer_design_front_url', $color);
        $backDesign = $this->getDesignFileForPosition($designFiles, 'printer_design_back_url', $color);
        $rightSleeveDesign = $this->getDesignFileForPosition($designFiles, 'printer_design_right_sleeve_url', $color);
        $leftSleeveDesign = $this->getDesignFileForPosition($designFiles, 'printer_design_left_sleeve_url', $color);

        return [
            'front' => $frontDesign ? $frontDesign->getDesignLinkAttribute() : $product->mockup_front ?? $design->getMockupFrontAttribute(),
            'back' => $backDesign ? $backDesign->getDesignLinkAttribute() : $product->mockup_back ?? $design->mockup_back,
            'right_sleeve' => $rightSleeveDesign ? $rightSleeveDesign->getDesignLinkAttribute() : null,
            'left_sleeve' => $leftSleeveDesign ? $leftSleeveDesign->getDesignLinkAttribute() : null,
        ];
    }

    private function getDesignFileForPosition($designFiles, $position, $color)
    {
        return $designFiles->first(function ($file) use ($position, $color) {
            return $file->location === $position &&
                is_array($file->suitable_colors) &&
                in_array(strtoupper($color), array_map('strtoupper', $file->suitable_colors));
        });
    }

    private function extractColorFromVariantName()
    {
        $variantParts = explode('|', $this->variant_name);
        foreach ($variantParts as $part) {
            list($key, $value) = array_map('trim', explode(':', $part));
            if (strtolower($key) === 'color') {
                return $value;
            }
        }
        return null;
    }
}
