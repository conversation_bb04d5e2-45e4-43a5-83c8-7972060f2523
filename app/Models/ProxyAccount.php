<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ProxyAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'api_url',
        'api_key',
        'csrf_token',
        'cookies',
        'user_agent',
        'headers',
        'raw_request',
        'is_active',
        'is_default',
        'priority',
        'account_info',
        'last_used_at',
        'usage_count',
        'daily_limit',
        'daily_usage',
        'daily_usage_date',
        'balance',
        'notes',
    ];

    protected $casts = [
        'headers' => 'array',
        'account_info' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'last_used_at' => 'datetime',
        'daily_usage_date' => 'date',
        'balance' => 'decimal:2',
    ];

    /**
     * Boot method để tự động reset daily usage
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($proxyAccount) {
            // Reset daily usage nếu ngày đã thay đổi
            if ($proxyAccount->daily_usage_date && 
                $proxyAccount->daily_usage_date->isYesterday()) {
                $proxyAccount->daily_usage = 0;
                $proxyAccount->daily_usage_date = Carbon::today();
            }
        });

        // Đảm bảo chỉ có 1 account default
        static::saving(function ($proxyAccount) {
            if ($proxyAccount->is_default) {
                static::where('id', '!=', $proxyAccount->id)
                      ->update(['is_default' => false]);
            }
        });
    }

    /**
     * Scope để lấy account active
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope để lấy account theo priority
     */
    public function scopeByPriority(Builder $query): Builder
    {
        return $query->orderBy('priority', 'desc')
                    ->orderBy('last_used_at', 'asc');
    }

    /**
     * Scope để lấy account có thể sử dụng (chưa vượt daily limit)
     */
    public function scopeAvailable(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereNull('daily_usage_date')
              ->orWhere('daily_usage_date', '<', Carbon::today())
              ->orWhereRaw('daily_usage < daily_limit');
        });
    }

    /**
     * Lấy account tốt nhất để sử dụng
     */
    public static function getBestAccount(): ?self
    {
        return static::active()
                    ->available()
                    ->byPriority()
                    ->first();
    }

    /**
     * Lấy account default
     */
    public static function getDefaultAccount(): ?self
    {
        return static::active()
                    ->where('is_default', true)
                    ->first();
    }

    /**
     * Increment usage count
     */
    public function incrementUsage(): void
    {
        $today = Carbon::today();
        
        // Reset daily usage nếu ngày mới
        if (!$this->daily_usage_date || $this->daily_usage_date->lt($today)) {
            $this->daily_usage = 0;
            $this->daily_usage_date = $today;
        }

        $this->increment('usage_count');
        $this->increment('daily_usage');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Check xem account có thể sử dụng không
     */
    public function canUse(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $today = Carbon::today();
        
        // Nếu chưa có daily_usage_date hoặc là ngày mới thì có thể sử dụng
        if (!$this->daily_usage_date || $this->daily_usage_date->lt($today)) {
            return true;
        }

        // Kiểm tra daily limit
        return $this->daily_usage < $this->daily_limit;
    }

    /**
     * Lấy số lượng còn lại có thể sử dụng trong ngày
     */
    public function getRemainingDailyUsage(): int
    {
        $today = Carbon::today();
        
        // Nếu chưa có daily_usage_date hoặc là ngày mới
        if (!$this->daily_usage_date || $this->daily_usage_date->lt($today)) {
            return $this->daily_limit;
        }

        return max(0, $this->daily_limit - $this->daily_usage);
    }

    /**
     * Parse cookies từ string thành array
     */
    public function getParsedCookies(): array
    {
        $cookies = [];
        $parts = explode(';', $this->cookies);
        
        foreach ($parts as $part) {
            $part = trim($part);
            if (strpos($part, '=') !== false) {
                [$name, $value] = explode('=', $part, 2);
                $cookies[trim($name)] = trim($value);
            }
        }
        
        return $cookies;
    }

    /**
     * Lấy headers đầy đủ cho API request
     */
    public function getFullHeaders(): array
    {
        $defaultHeaders = [
            'accept' => 'application/json, text/javascript, */*; q=0.01',
            'accept-language' => 'en-US,en;q=0.8',
            'cache-control' => 'no-cache',
            'content-type' => 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin' => 'https://www.proxies.com',
            'pragma' => 'no-cache',
            'priority' => 'u=1, i',
            'referer' => 'https://www.proxies.com/proxy/create',
            'sec-ch-ua' => '"Chromium";v="136", "Brave";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"Linux"',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'same-origin',
            'sec-gpc' => '1',
            'x-requested-with' => 'XMLHttpRequest',
        ];

        $headers = array_merge($defaultHeaders, $this->headers ?? []);

        // Thêm CSRF token và User Agent nếu có
        if ($this->csrf_token) {
            $headers['x-csrf-token'] = $this->csrf_token;
        }

        if ($this->user_agent) {
            $headers['user-agent'] = $this->user_agent;
        }

        return $headers;
    }

    /**
     * Accessor để hiển thị trạng thái
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        if (!$this->canUse()) {
            return 'Limit Reached';
        }

        return 'Active';
    }

    /**
     * Accessor để hiển thị usage info
     */
    public function getUsageInfoAttribute(): string
    {
        $remaining = $this->getRemainingDailyUsage();
        return "{$this->daily_usage}/{$this->daily_limit} (còn {$remaining})";
    }

    /**
     * Test cookie thông qua Service
     */
    public function testCookie(): array
    {
        $service = app(\App\Services\ProxyPurchaseService::class);
        return $service->testProxyAccount($this);
    }

    /**
     * Lấy danh sách proxy từ API v2
     */
    public function getProxies(array $params = []): array
    {
        try {
            if (empty($this->api_key)) {
                throw new \Exception('API Key không được cấu hình');
            }

            $apiUrl = 'https://www.proxies.com/api/v2/proxy';

            // Chuẩn bị query parameters
            $queryParams = array_filter([
                'active' => $params['active'] ?? null,
                'type' => $params['type'] ?? null,
                'dedicated' => $params['dedicated'] ?? null,
                'name' => $params['name'] ?? null,
                'ip' => $params['ip'] ?? null,
                'port' => $params['port'] ?? null,
                'batch' => $params['batch'] ?? null,
                'limit' => $params['limit'] ?? 1000,
                'offset' => $params['offset'] ?? 0,
            ]);

            $response = \Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->api_key,
                'Accept' => 'application/json',
            ])->get($apiUrl, $queryParams);

            if (!$response->successful()) {
                throw new \Exception('API call failed: ' . $response->status() . ' - ' . $response->body());
            }

            $data = $response->json();

            return [
                'success' => true,
                'data' => $data,
                'count' => count($data),
                'message' => 'Lấy danh sách proxy thành công',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Lỗi khi lấy danh sách proxy: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'data' => [],
            ];
        }
    }

    /**
     * Lấy thông tin chi tiết một proxy
     */
    public function getProxyDetails(string $reference): array
    {
        try {
            if (empty($this->api_key)) {
                throw new \Exception('API Key không được cấu hình');
            }

            $apiUrl = "https://www.proxies.com/api/v2/proxy/{$reference}";

            $response = \Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->api_key,
                'Accept' => 'application/json',
            ])->get($apiUrl);

            if (!$response->successful()) {
                throw new \Exception('API call failed: ' . $response->status() . ' - ' . $response->body());
            }

            $data = $response->json();

            return [
                'success' => true,
                'data' => $data,
                'message' => 'Lấy thông tin proxy thành công',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Lỗi khi lấy thông tin proxy: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Lấy thông tin số dư tài khoản
     */
    public function getAccountFunds(): array
    {
        try {
            if (empty($this->api_key)) {
                throw new \Exception('API Key không được cấu hình');
            }

            $apiUrl = 'https://www.proxies.com/api/v2/account/funds';

            $response = \Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->api_key,
                'Accept' => 'application/json',
            ])->get($apiUrl);

            if (!$response->successful()) {
                throw new \Exception('API call failed: ' . $response->status() . ' - ' . $response->body());
            }

            $data = $response->json();

            return [
                'success' => true,
                'amount' => $data['amount'] ?? 0,
                'message' => 'Lấy thông tin số dư thành công',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Lỗi khi lấy thông tin số dư: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'amount' => 0,
            ];
        }
    }

    /**
     * Lấy proxy theo username và password từ tất cả proxy (sort client-side)
     */
    public function getProxyByCredentials(string $username, string $password): array
    {
        try {
            if (empty($this->api_key)) {
                throw new \Exception('API Key không được cấu hình');
            }

            $apiUrl = 'https://www.proxies.com/api/v2/proxy';

            // Thử lấy tất cả proxy để sort client-side
            $response = \Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->api_key,
                'Accept' => 'application/json',
            ])->get($apiUrl, [
                'active' => 1  // Không set limit để lấy tất cả
            ]);

            // Log API call
            \Log::channel('proxy_by_proxies_com')->info('API Request - Get All Proxies for Credentials', [
                'account_id' => $this->id,
                'account_name' => $this->name,
                'username' => $username,
            ]);

            if (!$response->successful()) {
                \Log::channel('proxy_by_proxies_com')->error('API Request Failed', [
                    'status' => $response->status(),
                    'error' => $response->body()
                ]);
                throw new \Exception('API call failed: ' . $response->status() . ' - ' . $response->body());
            }

            $allProxies = $response->json();

            // Sort theo created_at descending để lấy proxy mới nhất trước
            if (!empty($allProxies)) {
                usort($allProxies, function($a, $b) {
                    $timeA = isset($a['created_at']) ? strtotime($a['created_at']) : 0;
                    $timeB = isset($b['created_at']) ? strtotime($b['created_at']) : 0;
                    return $timeB - $timeA; // Descending order (mới nhất trước)
                });
            }

            // Filter client-side theo username và password
            $filteredProxies = array_filter($allProxies, function($proxy) use ($username, $password) {
                return isset($proxy['username']) && $proxy['username'] === $username &&
                       isset($proxy['password']) && $proxy['password'] === $password;
            });

            // Log kết quả quan trọng
            \Log::channel('proxy_by_proxies_com')->info('Get Proxy By Credentials', [
                'username' => $username,
                'password' => $password,
                'total_proxies' => count($allProxies),
                'found_proxies' => count($filteredProxies),
                'success' => count($filteredProxies) > 0
            ]);

            return [
                'success' => true,
                'data' => array_values($filteredProxies),
                'count' => count($filteredProxies),
                'message' => 'Lấy proxy theo username và password thành công từ tất cả proxy (sorted)',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Lỗi khi lấy proxy theo username và password: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'data' => [],
            ];
        }
    }

    /**
     * Lấy proxy theo username từ tất cả proxy (sort client-side)
     * @deprecated Sử dụng getProxyByCredentials() để đảm bảo chính xác hơn
     */
    public function getProxyByUsername(string $username): array
    {
        try {
            if (empty($this->api_key)) {
                throw new \Exception('API Key không được cấu hình');
            }

            $apiUrl = 'https://www.proxies.com/api/v2/proxy';

            // Thử lấy tất cả proxy để sort client-side
            $response = \Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->api_key,
                'Accept' => 'application/json',
            ])->get($apiUrl, [
                'active' => 1  // Không set limit để lấy tất cả
            ]);

            // Log API call
            \Log::channel('proxy_by_proxies_com')->info('API Request - Get All Proxies', [
                'account_id' => $this->id,
                'account_name' => $this->name,
            ]);

            if (!$response->successful()) {
                \Log::channel('proxy_by_proxies_com')->error('API Request Failed', [
                    'status' => $response->status(),
                    'error' => $response->body()
                ]);
                throw new \Exception('API call failed: ' . $response->status() . ' - ' . $response->body());
            }

            $allProxies = $response->json();

            // Sort theo created_at descending để lấy proxy mới nhất trước
            if (!empty($allProxies)) {
                usort($allProxies, function($a, $b) {
                    $timeA = isset($a['created_at']) ? strtotime($a['created_at']) : 0;
                    $timeB = isset($b['created_at']) ? strtotime($b['created_at']) : 0;
                    return $timeB - $timeA; // Descending order (mới nhất trước)
                });
            }

            // Filter client-side theo username
            $filteredProxies = array_filter($allProxies, function($proxy) use ($username) {
                return isset($proxy['username']) && $proxy['username'] === $username;
            });

            // Log kết quả quan trọng
            \Log::channel('proxy_by_proxies_com')->info('Get Proxy By Username', [
                'username' => $username,
                'total_proxies' => count($allProxies),
                'found_proxies' => count($filteredProxies),
                'success' => count($filteredProxies) > 0
            ]);

            return [
                'success' => true,
                'data' => array_values($filteredProxies),
                'count' => count($filteredProxies),
                'message' => 'Lấy proxy theo username thành công từ tất cả proxy (sorted)',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Lỗi khi lấy proxy theo username: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'data' => [],
            ];
        }
    }
}
