<?php

namespace App\Models;

use App\Enums\OrderItemStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class OrderItem extends Model
{
    use HasFactory;
    use LogsActivity;
    protected $fillable = [
        'order_id', 'product_variant_id', 'product_id', 'quantity', 'price',
        'total', 'design_id', 'note', 'label', 'status', 'name', 'link', 'image', 'sku','fulfill_unit_id'
    ];

    protected $casts = [
        'status' => OrderItemStatus::class,
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()->logOnly(['status', 'price', 'quantity', 'label', 'note' ,'note']);
    }
    public function activitiessort()
    {
        return $this->activities()->orderBy('id', 'desc');
    }
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function isAutoFulfill(): bool
    {
        if (!$this->product || !$this->productVariant) {
            return false;
        }
        
        return $this->productVariant->auto_fulfill ?? false;
    }

    public function product()
    {
        return $this->belongsTo(Product::class)->withoutGlobalScope('accessScope');
        return $this->productVariant->product(); // Chú ý: Phương thức này không phải là mối quan hệ Eloquent trực tiếp
    }
    public function design()
    {
        return $this->product->design(); // Giả sử bạn đã có model Design
    }
    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class, 'product_variant_id', 'variant_id');
    }
    protected static function booted()
    {
        static::saving(function ($orderItem) {

        });

        static::saved(function ($item) {
            $item->order->updateTotal();
        });

        static::deleted(function ($item) {
            $item->order->updateTotal();
        });
    }

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }

            $user = Auth::user();

            // Super admin, User Manager, Developer, Accountant có thể xem tất cả
            if ($user->hasRole(['super_admin', 'User Manager', 'Developer', 'Accountant',  'Media Manager', 'Analytic'])) {
                return $builder;
            }

            // Fulfillment Manager có thể xem tất cả order items
            if ($user->hasRole('Fullfillment Manager')) {
                return $builder;
            }

            $sellerIds = [];
            if ($user->hasRole('Seller')) {
                $sellerIds[] = $user->id;
            }

            // Leader có ưu tiên cao hơn Fulfillment
            if ($user->hasRole('Leader')) {
                $sellerIds[] = $user->id;
                $leaderSellerIds = $user->leaderManagedSellers()->pluck('users.id')->toArray();
                $sellerIds = array_merge($sellerIds, $leaderSellerIds);
            }

            $sellerIds = array_unique($sellerIds);

            if (!empty($sellerIds)) {
                return $builder->whereHas('order', function ($query) use ($sellerIds) {
                    $query->whereIn('seller_id', $sellerIds);
                });
            } else {
                return $builder->whereRaw('1 = 0');
            }
        });
    }
}
