<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class GeneratedImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'prompt',
        'original_url',
        's3_path',
        's3_url',
        'filename',
        'file_size',
        'mime_type',
        'width',
        'height',
        'metadata',
        'status',
        'error_message',
        'uploaded_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'uploaded_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship với User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Lấy URL hiển thị ảnh (ưu tiên S3, fallback original)
     */
    public function getDisplayUrlAttribute(): string
    {
        return $this->s3_url ?: $this->original_url;
    }

    /**
     * Lấy URL download ảnh
     */
    public function getDownloadUrlAttribute(): string
    {
        if ($this->s3_path) {
            return Storage::disk('s3')->temporaryUrl($this->s3_path, now()->addHours(1));
        }

        return $this->original_url;
    }

    /**
     * Kiểm tra ảnh đã upload lên S3 chưa
     */
    public function isUploaded(): bool
    {
        return $this->status === 'uploaded' && !empty($this->s3_path);
    }

    /**
     * Kiểm tra ảnh có lỗi không
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Format file size
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'N/A';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unitIndex = 0;

        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }

        return round($size, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * Scope: Chỉ ảnh đã upload thành công
     */
    public function scopeUploaded($query)
    {
        return $query->where('status', 'uploaded');
    }

    /**
     * Scope: Ảnh của user cụ thể
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope: Ảnh được tạo trong ngày hôm nay
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Kiểm tra user đã đạt giới hạn tạo ảnh trong ngày chưa
     *
     * @param int $userId
     * @param int $limit Giới hạn số ảnh mỗi ngày (mặc định 100)
     * @return bool
     */
    public static function hasReachedDailyLimit(int $userId, int $limit = 100): bool
    {
        $todayCount = self::forUser($userId)
            ->today()
            ->count();

        return $todayCount >= $limit;
    }

    /**
     * Lấy số ảnh đã tạo trong ngày của user
     *
     * @param int $userId
     * @return int
     */
    public static function getTodayCount(int $userId): int
    {
        return self::forUser($userId)
            ->today()
            ->count();
    }

    /**
     * Lấy số ảnh còn lại có thể tạo trong ngày
     *
     * @param int $userId
     * @param int $limit Giới hạn số ảnh mỗi ngày (mặc định 100)
     * @return int
     */
    public static function getRemainingCount(int $userId, int $limit = 100): int
    {
        $todayCount = self::getTodayCount($userId);
        return max(0, $limit - $todayCount);
    }
}
