<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ProductUploadTask extends Model
{
    use HasFactory;

    protected $fillable = [
        'status',
        'store_id',
        'template_id',
        'keywords',
        'is_top_trending',
        'sales_today',
        'sales_change',
        'total_sales',
        'max_upload_per_day',
        'product_keywords',
        'upload_interval',  // Thêm trường mới
        'user_id',
        'allow_trademark',
        'source',
        'skip_keywords',
        'clone_shop_ids'
    ];
    protected $casts = [
        'status' => 'boolean',
        'allow_trademark' => 'boolean',
        'clone_shop_ids' => 'array', // Cast thành array
    ];
    public function store()
    {
        return $this->belongsTo(Store::class,'store_id');
    }

    public function template()
    {
        return $this->belongsTo(Template::class);
    }
    // Relationship with ProductToUpload
    public function products()
    {
        return $this->hasMany(ProductToUpload::class, 'task_id');
    }

    // Method to count pending uploads
    public function countPendingUploads()
    {
        return $this->products()->where('status', 'pending')->count();
    }

    // Method to count processing uploads
    public function countProcessingUploads()
    {
        return $this->products()->where('status', 'processing')->count();
    }

    // Method to count successful uploads
    public function countSuccessfulUploads()
    {
        return $this->products()->where('status', 'success')->count();
    }

    // Method to count failed uploads
    public function countFailedUploads()
    {
        return $this->products()->where('status', 'failed')->count();
    }
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($proxy) {
            if (Auth::check()) {
                $proxy->user_id = Auth::id();
            }

        });
        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }
            $user = Auth::user();
            if ($user->hasRole(['super_admin', 'User Manager', 'Developer'])) {
                return $builder;
            }
            return $builder->where(function ($query) {
                $query->where('user_id', Auth::id());
            });

        });
    }
}
