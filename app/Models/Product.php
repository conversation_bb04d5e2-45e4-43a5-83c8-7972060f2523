<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Auth;

class Product extends Model
{
    use HasFactory;
    protected $fillable = ['name', 'description', 'image', 'type', 'active', 'seller_id', 'store_id','link','sku','price' ,'images'];
    protected $casts = [
        'active' => 'boolean',
        'images' => 'array'
    ];

    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }
    public function store()
    {
        return $this->belongsTo(Store::class);
    }
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class );
    }
    public function design()
    {
        return $this->hasOne(Design::class);
    }
    protected static function booted(): void
    {

    }

    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }
    public function deleteAllVariants(): void
    {
        $this->variants()->delete();
    }

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }
            $user = Auth::user();
            if ($user->hasRole(['super_admin', 'User Manager', 'Developer', 'Media Manager', 'Analytic'])) {
                return $builder;
            }

            // Fulfillment Manager có thể xem tất cả products
            if ($user->hasRole('Fullfillment Manager')) {
                return $builder;
            }

          

            $sellerIds = [];
            if ($user->hasRole('Seller')) {
                $sellerIds[] = $user->id;
            }

            if ($user->hasRole('Leader')) {
                $sellerIds[] = $user->id;
                $managedSellers = $user->leaderManagedSellers();
                $leaderSellerIds = $managedSellers->pluck('id')->toArray();
                $sellerIds = array_merge($sellerIds, $leaderSellerIds);
            }

            $sellerIds = array_unique($sellerIds);

            if (!empty($sellerIds)) {
                // Nếu user là seller, leader hoặc fulfillment, cho phép xem sản phẩm dựa trên seller_id
                return $builder->whereIn('seller_id', $sellerIds)
                    // Hoặc cho phép xem sản phẩm nếu user là chủ của store (store_id)
                    ->orWhereIn('store_id', function($query) {
                        $query->select('id')
                            ->from('stores')
                            ->where('owner_id', Auth::id());
                    });
            } else {
                // Instead of aborting, return an empty result set if no valid seller_ids are found
                return $builder->whereRaw('1 = 0'); // This will result in no records being returned
            }
        });
    }
}
