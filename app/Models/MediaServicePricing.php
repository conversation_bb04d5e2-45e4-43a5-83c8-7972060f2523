<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MediaServicePricing extends Model
{
    use HasFactory;

    protected $table = 'media_service_pricing';

    protected $fillable = [
        'service_type',
        'name',
        'description',
        'base_price',
        'min_price',
        'max_price',
        'estimated_hours',
        'max_revisions',
        'delivery_days',
        'rush_fee_percentage',
        'revision_fee',
        'icon',
        'color',
        'sort_order',
        'is_active',
        'is_featured',
        'allowed_roles',
        'features',
        'requirements',
        'deliverables',
        'created_by',
        'updated_by',
        'last_price_update',
    ];

    protected $casts = [
        'base_price' => 'decimal:2',
        'min_price' => 'decimal:2',
        'max_price' => 'decimal:2',
        'rush_fee_percentage' => 'decimal:2',
        'revision_fee' => 'decimal:2',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'allowed_roles' => 'array',
        'features' => 'array',
        'requirements' => 'array',
        'deliverables' => 'array',
        'last_price_update' => 'datetime',
    ];

    // Relationships
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Helper methods
    public function calculateRushPrice(): float
    {
        return $this->base_price * (1 + ($this->rush_fee_percentage / 100));
    }

    public function getPriceWithRevisions(int $extraRevisions = 0): float
    {
        $extraRevisionCost = max(0, $extraRevisions - $this->max_revisions) * $this->revision_fee;
        return $this->base_price + $extraRevisionCost;
    }

    public function isAllowedForRole(string $role): bool
    {
        if (empty($this->allowed_roles)) {
            return true; // Nếu không có giới hạn role thì cho phép tất cả
        }

        return in_array($role, $this->allowed_roles);
    }

    public function getFormattedPrice(): string
    {
        return '$' . number_format($this->base_price, 2);
    }

    public function getFormattedPriceRange(): string
    {
        if ($this->max_price) {
            return '$' . number_format($this->min_price, 2) . ' - $' . number_format($this->max_price, 2);
        }

        return 'From $' . number_format($this->min_price, 2);
    }

    // Static methods
    public static function getActiveServices()
    {
        return static::active()->ordered()->get();
    }

    public static function getServiceByType(string $type)
    {
        return static::where('service_type', $type)->active()->first();
    }

    public static function getFeaturedServices()
    {
        return static::featured()->active()->ordered()->get();
    }
}
