<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Document extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'key',
        'content',
        'file_path',
        'file_type',
        'category',
        'tags',
        'status',
        'is_embedded',
        'embedding_status',
        'embedding_error',
        'word_count',
        'chunk_count',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'tags' => 'array',
        'is_embedded' => 'boolean',
        'word_count' => 'integer',
        'chunk_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship với User (người tạo)
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship với User (người cập nhật)
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Accessor để lấy URL file từ S3
     */
    public function getFileUrlAttribute(): ?string
    {
        if (!$this->file_path) {
            return null;
        }

        return Storage::disk('s3')->url($this->file_path);
    }

    /**
     * Accessor để kiểm tra trạng thái embedding
     */
    public function getIsEmbeddingCompletedAttribute(): bool
    {
        return $this->embedding_status === 'completed' && $this->is_embedded;
    }

    /**
     * Accessor để kiểm tra có thể re-embed không
     */
    public function getCanReEmbedAttribute(): bool
    {
        return in_array($this->embedding_status, ['failed', 'completed']);
    }

    /**
     * Accessor để lấy trạng thái hiển thị
     */
    public function getStatusDisplayAttribute(): string
    {
        return match ($this->status) {
            'draft' => 'Bản nháp',
            'published' => 'Đã xuất bản',
            'archived' => 'Đã lưu trữ',
            default => $this->status,
        };
    }

    /**
     * Accessor để lấy trạng thái embedding hiển thị
     */
    public function getEmbeddingStatusDisplayAttribute(): string
    {
        return match ($this->embedding_status) {
            'pending' => 'Chờ xử lý',
            'processing' => 'Đang xử lý',
            'completed' => 'Hoàn thành',
            'failed' => 'Thất bại',
            default => $this->embedding_status,
        };
    }

    /**
     * Scope để lọc documents đã published
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope để lọc documents đã embedding thành công
     */
    public function scopeEmbedded($query)
    {
        return $query->where('embedding_status', 'completed')
                    ->where('is_embedded', true);
    }

    /**
     * Scope để lọc theo category
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope để tìm kiếm theo title hoặc content
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('content', 'like', "%{$search}%");
        });
    }

    /**
     * Method để đánh dấu bắt đầu embedding
     */
    public function markEmbeddingStarted(): void
    {
        $this->update([
            'embedding_status' => 'processing',
            'embedding_error' => null,
        ]);
    }

    /**
     * Method để đánh dấu embedding thành công
     */
    public function markEmbeddingCompleted(int $chunkCount): void
    {
        $this->update([
            'embedding_status' => 'completed',
            'is_embedded' => true,
            'chunk_count' => $chunkCount,
            'embedding_error' => null,
        ]);
    }

    /**
     * Method để đánh dấu embedding thất bại
     */
    public function markEmbeddingFailed(string $error): void
    {
        $this->update([
            'embedding_status' => 'failed',
            'is_embedded' => false,
            'embedding_error' => $error,
        ]);
    }

    /**
     * Method để reset embedding status
     */
    public function resetEmbeddingStatus(): void
    {
        $this->update([
            'embedding_status' => 'pending',
            'is_embedded' => false,
            'chunk_count' => 0,
            'embedding_error' => null,
        ]);
    }
}
