<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DailyReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'seller_id',
        'report_date',
        'sales_data',
        'work_description',
        'activities',
        'status',
        'submitted_at',
        'reviewed_at',
        'reviewed_by',
        'rejection_reason',
        'rejected_by',
        'rejected_at',
    ];

    /**
     * Boot the model and add global scope
     */
    protected static function booted(): void
    {
        static::addGlobalScope('userAccess', function (Builder $builder) {
            // Kiểm tra user đã đăng nhập chưa
            if (!Auth::check()) {
                return;
            }

            $user = Auth::user();

            // Super Admin, Developer và Accountant → Full access
            if ($user->hasRole(['super_admin', 'User Manager'])) {
                return;
            }

            // Thu thập danh sách seller_id được phép xem
            $sellerIds = [];

            // Leader logic
            if ($user->hasRole('Leader')) {
                $sellerIds[] = $user->id; // Leader thấy records của chính mình

                // Lấy danh sách sellers được quản lý
                $leaderSellerIds = $user->leaderManagedSellers()->pluck('id')->toArray();
                $sellerIds = array_merge($sellerIds, $leaderSellerIds);
            }

            // Seller logic
            if ($user->hasRole('Seller')) {
                $sellerIds[] = $user->id; // Seller chỉ thấy records của mình
            }

            // Loại bỏ duplicate IDs
            $sellerIds = array_unique($sellerIds);

            // Áp dụng điều kiện WHERE
            if (!empty($sellerIds)) {
                // Có quyền → WHERE seller_id IN (1,2,3,...)
                $builder->whereIn('seller_id', $sellerIds);
            } else {
                // Không có quyền → WHERE 1 = 0 (không trả về gì)
                $builder->whereRaw('1 = 0');
            }
        });
    }

    protected $casts = [
        'report_date' => 'date',
        'sales_data' => 'array',
        'submitted_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    /**
     * Relationship với User (Seller)
     */
    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    /**
     * Relationship với User (Reviewer)
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Relationship với User (Rejector)
     */
    public function rejector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }

    /**
     * Scope query without user access restrictions
     */
    public function scopeWithoutUserAccess(Builder $query): Builder
    {
        return $query->withoutGlobalScope('userAccess');
    }

    /**
     * Get query with user access applied manually
     */
    public function scopeWithUserAccess(Builder $query): Builder
    {
        // Global scope đã tự động áp dụng, method này chỉ để tương thích
        return $query;
    }

    /**
     * Scope để lọc báo cáo theo seller
     */
    public function scopeForSeller(Builder $query, int $sellerId): Builder
    {
        return $query->where('seller_id', $sellerId);
    }

    /**
     * Scope để lọc báo cáo theo trạng thái
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope để lọc báo cáo theo khoảng thời gian
     */
    public function scopeDateRange(Builder $query, Carbon $from, Carbon $to): Builder
    {
        return $query->whereBetween('report_date', [$from, $to]);
    }

    /**
     * Scope để lấy báo cáo hôm nay
     */
    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('report_date', today());
    }

    /**
     * Scope để lấy báo cáo tuần này
     */
    public function scopeThisWeek(Builder $query): Builder
    {
        return $query->whereBetween('report_date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Scope để lấy báo cáo tháng này
     */
    public function scopeThisMonth(Builder $query): Builder
    {
        return $query->whereBetween('report_date', [
            now()->startOfMonth(),
            now()->endOfMonth()
        ]);
    }

    /**
     * Kiểm tra xem báo cáo có thể chỉnh sửa không
     */
    public function canEdit(): bool
    {
        return in_array($this->status, ['submitted', 'rejected']);
    }

    /**
     * Kiểm tra xem báo cáo có thể submit không
     */
    public function canSubmit(): bool
    {
        return in_array($this->status, ['rejected']);
    }

    /**
     * Submit báo cáo
     */
    public function submit(): bool
    {
        if (!$this->canSubmit()) {
            return false;
        }

        $updateData = [
            'status' => 'submitted',
            'submitted_at' => now(),
        ];

        // Nếu đang resubmit từ trạng thái rejected, xóa thông tin rejection
        if ($this->status === 'rejected') {
            $updateData['rejection_reason'] = null;
            $updateData['rejected_by'] = null;
            $updateData['rejected_at'] = null;
        }

        $this->update($updateData);

        return true;
    }

    /**
     * Review báo cáo
     */
    public function review(int $reviewerId): bool
    {
        if ($this->status !== 'submitted') {
            return false;
        }

        // Chỉ cho phép duyệt báo cáo trong 2 ngày gần nhất
        if ($this->report_date < now()->subDays(2)->startOfDay()) {
            return false;
        }

        $this->update([
            'status' => 'reviewed',
            'reviewed_at' => now(),
            'reviewed_by' => $reviewerId,
        ]);

        return true;
    }

    /**
     * Reject báo cáo
     */
    public function reject(int $rejectorId, string $reason): bool
    {
        if ($this->status !== 'submitted') {
            return false;
        }

        // Chỉ cho phép không duyệt báo cáo trong 2 ngày gần nhất
        if ($this->report_date < now()->subDays(2)->startOfDay()) {
            return false;
        }

        $this->update([
            'status' => 'rejected',
            'rejected_at' => now(),
            'rejected_by' => $rejectorId,
            'rejection_reason' => $reason,
        ]);

        return true;
    }

    /**
     * Kiểm tra xem báo cáo có thể resubmit không (sau khi bị reject)
     */
    public function canResubmit(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Resubmit báo cáo sau khi bị reject
     */
    public function resubmit(): bool
    {
        if (!$this->canResubmit()) {
            return false;
        }

        $this->update([
            'status' => 'submitted',
            'submitted_at' => now(),
            'rejection_reason' => null,
            'rejected_by' => null,
            'rejected_at' => null,
        ]);

        return true;
    }

    /**
     * Lấy màu status cho UI
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'submitted' => 'warning',
            'reviewed' => 'success',
            'rejected' => 'danger',
            default => 'gray',
        };
    }

    /**
     * Lấy label status cho UI
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'submitted' => 'Đã gửi',
            'reviewed' => 'Đã duyệt',
            'rejected' => 'Không duyệt',
            default => 'Không xác định',
        };
    }
}
