<?php

namespace App\Models;

use App\Enums\IdeaStatus;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Parallax\FilamentComments\Models\Traits\HasFilamentComments;

class Idea extends Model
{
    use HasFactory;
    use HasFilamentComments;


    protected $fillable = [
        'user_id',
        'title',
        'link',
        'tags',
        'niche',
        'product_type',
        'source',
        'status',
        'note',
        'title_rewrite',
        'trademark',
        'has_sold',
        'price',
        'idea_files',
        'design_files',
        'designer_id',
        'reviewer_id',
        'review_notes',
        'design_notes',

        'analyze_keywords',
        'keyword_tracking_id',
        'link_image',

    ];

    protected $casts = [
        'tags' => 'array',
        'niche' => 'array',
        'status' => IdeaStatus::class,
        'price' => 'decimal:2',
        'has_sold' => 'boolean',
        'idea_files' => 'array',
        'design_files' => 'array',
    ];
    public function getIdeaFilesUrls()
    {
        return collect($this->idea_files)->map(function ($file) {
            return asset('storage/' . $file);
        })->toArray();
    }
    public function stores(): BelongsToMany
    {
        return $this->belongsToMany(Store::class, 'idea_store');
    }


    public function getDesignFilesUrls()
    {
        return collect($this->design_files)->map(function ($file) {
            return asset('storage/' . $file);
        })->toArray();
    }
    public function keywordTracking(): BelongsTo
    {
        return $this->belongsTo(KeywordTracking::class);
    }
    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope('accessScope', function (Builder $query) {
            if (app()->runningInConsole()) {
                return $query;
            }

            $user = auth()->user();

            if ($user->hasRole(['super_admin', 'User Manager'])) {
                return $query;
            }


            // Thêm điều kiện cho Leader
            if ($user->hasRole('Leader')) {
                $managedSellers = $user->leaderManagedSellers();
                $managedSellerIds = $managedSellers->pluck('id')->toArray();
                $managedSellerIds[] = $user->id; // Thêm chính leader

                return $query->whereIn('user_id', $managedSellerIds);
            }

            // Các user khác chỉ thấy ideas của mình
            return $query->where('user_id', $user->id);
        });
        static::creating(function ($idea) {
            // Automatically set the user_id to the currently authenticated user
            $idea->user_id = auth()->id();

            // If a link is provided, extract the domain and save it to the source
            if ($idea->link) {
                try {
                    $parsedUrl = parse_url($idea->link);
                    if (isset($parsedUrl['host'])) {
                        $idea->source = $parsedUrl['host'];
                    }
                } catch (Exception $e) {
                }
            }

        });
        static::created(function ($idea) {
            // If keyword_tracking_id exists, attach its store to the idea
            if ($idea->keyword_tracking_id) {
                $keywordTracking = KeywordTracking::find($idea->keyword_tracking_id);
                if ($keywordTracking && $keywordTracking->store_id) {
                    $idea->stores()->attach($keywordTracking->store_id);
                }
            }
        });
    }
    public function designer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'designer_id')->role('Designer');
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewer_id')->role('Leader');
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
