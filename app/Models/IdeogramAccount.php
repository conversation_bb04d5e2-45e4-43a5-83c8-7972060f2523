<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Carbon\Carbon;

class IdeogramAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'authorization',
        'cookie',
        'external_photo_url',
        'location',
        'is_active',
        'is_default',
        'priority',
        'user_info',
        'last_used_at',
        'usage_count',
        'daily_limit',
        'daily_usage',
        'daily_usage_date',
        'notes'
    ];

    protected $casts = [
        'user_info' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'last_used_at' => 'datetime',
        'daily_usage_date' => 'date',
        'usage_count' => 'integer',
        'daily_limit' => 'integer',
        'daily_usage' => 'integer',
        'priority' => 'integer'
    ];

    protected $hidden = [
        // Không ẩn authorization và cookie để Filament có thể hiển thị
        // Sẽ override toArray() method để ẩn khi cần thiết
    ];

    /**
     * Override toArray để ẩn sensitive data khi serialize
     */
    public function toArray()
    {
        $array = parent::toArray();

        // Ẩn sensitive data khi không phải trong admin context
        if (!app()->runningInConsole() && !request()->is('admin/*')) {
            unset($array['authorization'], $array['cookie']);
        }

        return $array;
    }

    /**
     * Boot method để tự động set default account
     */
    protected static function boot()
    {
        parent::boot();

        // Khi tạo account mới với is_default = true, set các account khác thành false
        static::creating(function ($account) {
            // Làm sạch authorization và cookie
            $account->authorization = $account->cleanHeaderValue($account->authorization);
            $account->cookie = $account->cleanHeaderValue($account->cookie);

            if ($account->is_default) {
                static::where('is_default', true)->update(['is_default' => false]);
            }
        });

        // Khi update account với is_default = true, set các account khác thành false
        static::updating(function ($account) {
            // Làm sạch authorization và cookie nếu có thay đổi
            if ($account->isDirty('authorization')) {
                $account->authorization = $account->cleanHeaderValue($account->authorization);
            }
            if ($account->isDirty('cookie')) {
                $account->cookie = $account->cleanHeaderValue($account->cookie);
            }

            if ($account->is_default && $account->isDirty('is_default')) {
                static::where('id', '!=', $account->id)
                      ->where('is_default', true)
                      ->update(['is_default' => false]);
            }
        });
    }

    /**
     * Scope để lấy các account đang hoạt động
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope để lấy account mặc định
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope để sắp xếp theo độ ưu tiên
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc')->orderBy('last_used_at', 'asc');
    }

    /**
     * Scope để lấy account có thể sử dụng (chưa đạt giới hạn hàng ngày)
     */
    public function scopeAvailable($query)
    {
        return $query->active()
                    ->where(function ($q) {
                        $q->where('daily_usage_date', '!=', today())
                          ->orWhere('daily_usage', '<', \DB::raw('daily_limit'))
                          ->orWhereNull('daily_usage_date');
                    });
    }

    /**
     * Lấy account tốt nhất để sử dụng
     */
    public static function getBestAvailable()
    {
        return static::available()
                    ->byPriority()
                    ->first();
    }

    /**
     * Lấy account mặc định hoặc account tốt nhất
     */
    public static function getDefaultOrBest()
    {
        // Thử lấy account mặc định trước
        $default = static::default()->active()->first();
        if ($default && $default->canUseToday()) {
            return $default;
        }

        // Nếu không có hoặc đã đạt giới hạn, lấy account tốt nhất
        return static::getBestAvailable();
    }

    /**
     * Kiểm tra xem account có thể sử dụng hôm nay không
     */
    public function canUseToday(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        // Reset daily usage nếu là ngày mới
        if ($this->daily_usage_date != today()) {
            $this->resetDailyUsage();
        }

        return $this->daily_usage < $this->daily_limit;
    }

    /**
     * Reset daily usage cho ngày mới
     */
    public function resetDailyUsage(): void
    {
        $this->update([
            'daily_usage' => 0,
            'daily_usage_date' => today()
        ]);
    }

    /**
     * Đánh dấu account đã được sử dụng
     */
    public function markAsUsed(): void
    {
        // Reset daily usage nếu là ngày mới
        if ($this->daily_usage_date != today()) {
            $this->resetDailyUsage();
        }

        $this->increment('usage_count');
        $this->increment('daily_usage');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Kiểm tra xem authorization có hợp lệ không
     */
    public function hasValidCredentials(): bool
    {
        return !empty($this->authorization) && !empty($this->cookie);
    }

    /**
     * Lấy thông tin user từ user_info
     */
    public function getUserId(): ?string
    {
        return $this->user_info['user_model']['user_id'] ?? null;
    }

    public function getUserHandle(): ?string
    {
        return $this->user_info['user_model']['display_handle'] ?? null;
    }

    public function getOrgId(): ?string
    {
        return $this->user_info['user_model']['organization_id'] ?? null;
    }

    /**
     * Attribute để hiển thị trạng thái
     */
    protected function status(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (!$this->is_active) {
                    return 'inactive';
                }

                if (!$this->canUseToday()) {
                    return 'limit_reached';
                }

                return 'available';
            }
        );
    }

    /**
     * Attribute để hiển thị usage percentage
     */
    protected function usagePercentage(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ($this->daily_limit == 0) {
                    return 0;
                }

                return round(($this->daily_usage / $this->daily_limit) * 100, 2);
            }
        );
    }

    /**
     * Làm sạch giá trị header
     */
    public function cleanHeaderValue(string $value): string
    {
        if (empty($value)) {
            return $value;
        }

        // Loại bỏ ký tự xuống dòng và các ký tự không hợp lệ
        $value = trim($value);
        $value = str_replace(["\r", "\n", "\t"], '', $value);

        // Loại bỏ các ký tự control characters
        $value = preg_replace('/[\x00-\x1F\x7F]/', '', $value);

        // Loại bỏ khoảng trắng thừa
        $value = preg_replace('/\s+/', ' ', $value);

        return trim($value);
    }
}
