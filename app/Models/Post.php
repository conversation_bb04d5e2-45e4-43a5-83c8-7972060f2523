<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class Post extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'featured_image',
        'status',
        'author_id',
        'published_at',
        'meta_description',
        'tags',
        'views_count',
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'tags' => 'array',
        'views_count' => 'integer',
    ];

    /**
     * Relationship với user (author)
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Scope để lọc bài viết đã publish
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('status', 'published')
                    ->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope để lọc bài viết draft
     */
    public function scopeDraft(Builder $query): Builder
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope để lọc bài viết theo author
     */
    public function scopeByAuthor(Builder $query, int $authorId): Builder
    {
        return $query->where('author_id', $authorId);
    }

    /**
     * Scope để lọc bài viết trong khoảng thời gian
     */
    public function scopeInDateRange(Builder $query, $startDate, $endDate): Builder
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope để tìm kiếm bài viết
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($query) use ($search) {
            $query->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%");
        });
    }

    /**
     * Kiểm tra xem bài viết đã được publish chưa
     */
    public function isPublished(): bool
    {
        return $this->status === 'published' && 
               $this->published_at && 
               $this->published_at <= now();
    }

    /**
     * Kiểm tra xem bài viết có đang draft không
     */
    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Publish bài viết
     */
    public function publish(): void
    {
        $this->update([
            'status' => 'published',
            'published_at' => now(),
        ]);
    }

    /**
     * Unpublish bài viết (chuyển về draft)
     */
    public function unpublish(): void
    {
        $this->update([
            'status' => 'draft',
            'published_at' => null,
        ]);
    }

    /**
     * Tăng view count
     */
    public function incrementViews(): void
    {
        $this->increment('views_count');
    }

    /**
     * Tự động tạo slug từ title
     */
    public function generateSlug(): string
    {
        $slug = Str::slug($this->title);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Boot method để tự động tạo slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($post) {
            if (empty($post->slug) && !empty($post->title)) {
                $post->slug = $post->generateSlug();
            }
        });

        static::updating(function ($post) {
            if ($post->isDirty('title') && !empty($post->title)) {
                // Chỉ tự động update slug nếu slug hiện tại trống hoặc giống với slug cũ của title
                $oldSlug = Str::slug($post->getOriginal('title'));
                if (empty($post->slug) || $post->slug === $oldSlug) {
                    $post->slug = $post->generateSlug();
                }
            }
        });
    }

    /**
     * Get excerpt hoặc tạo từ content
     */
    public function getExcerptAttribute($value): string
    {
        if ($value) {
            return $value;
        }

        // Tạo excerpt từ content nếu không có
        return Str::limit(strip_tags($this->content), 150);
    }

    /**
     * Get URL của featured image
     */
    public function getFeaturedImageUrlAttribute(): ?string
    {
        if ($this->featured_image) {
            // Sử dụng S3 disk để lấy URL
            return \Illuminate\Support\Facades\Storage::disk('s3')->url($this->featured_image);
        }
        return null;
    }

    /**
     * Get formatted published date
     */
    public function getFormattedPublishedDateAttribute(): ?string
    {
        if ($this->published_at) {
            return $this->published_at->format('d/m/Y H:i');
        }
        return null;
    }

    /**
     * Get reading time estimate
     */
    public function getReadingTimeAttribute(): int
    {
        $wordCount = str_word_count(strip_tags($this->content));
        return max(1, ceil($wordCount / 200)); // 200 words per minute
    }
}
