<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;

class PayoutTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'time',
        'currency',
        'amount',
        'transaction_id',
        'card_no',
        'fee',
        'net',
        'type',
        'from_to',
        'status',
        'note',
    ];

    protected $casts = [
        'time' => 'datetime',
        'amount' => 'decimal:2',
        'fee' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            // Validate the attributes
            if (!self::isValid($model)) {
                \Log::warning('PayoutTransaction validation failed during creation', [
                    'transaction_id' => $model->transaction_id ?? 'N/A',
                    'from_to' => $model->from_to ?? 'N/A',
                    'type' => $model->type ?? 'N/A',
                    'status' => $model->status ?? 'N/A'
                ]);
                return false;
            }
        });

        static::updating(function ($model) {
            // Validate the attributes
            if (!self::isValid($model)) {
                return false;
            }
        });

        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }
            $user = Auth::user();
            if ($user->hasRole(['super_admin', 'User Manager', 'Developer', 'Accountant'])) {
                return $builder;
            }

            return $builder->whereHas('store', function ($query) use ($user) {
                $query->where('owner_id', $user->id);
            });
        });
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'card_no', 'bank_account');
    }

    public static function isValid($model)
    {
        // Check for duplicate transaction_id
        if (isset($model->transaction_id) && self::where('transaction_id', $model->transaction_id)->exists()) {
            return false;
        }

        // Check if the required conditions are met
        if (
            isset($model->from_to) &&
            (strpos($model->from_to, 'TikTok Inc') !== false ||
             strpos($model->from_to, 'TikTok Shop') !== false ||
             strpos($model->from_to, 'TikTok') !== false)
        ) {
            // Debug log để xem giá trị thực tế
            \Log::info('PayoutTransaction validation debug', [
                'transaction_id' => $model->transaction_id,
                'type' => $model->type,
                'status' => $model->status,
                'from_to' => $model->from_to,
                'all_attributes' => $model->getAttributes()
            ]);

            return isset($model->type) && $model->type === 'Receive' &&
                isset($model->status) && $model->status === 'Success';
        }
        return false;
    }

}
