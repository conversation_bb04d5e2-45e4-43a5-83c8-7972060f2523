<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class FlashSale extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'store_id',
        'user_id',
        'deal_name',
        'deal_id',
        'start',
        'expire',
        'deal_status',
        'action'
    ];

    protected $casts = [
        'start' => 'datetime',
        'expire' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();
   
        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }
            
            $user = Auth::user();
            if ($user->hasRole(['super_admin', 'User Manager'])) {
                return $builder;
            } 

            $userIds = [];
            if ($user->hasRole('Seller')) {
                $userIds[] = $user->id;
            }
        
            // Leader có ưu tiên cao hơn Fulfillment
            if ($user->hasRole('Leader')) {
                $userIds[] = $user->id;
                $leaderUserIds = $user->leaderManagedSellers()->pluck('users.id')->toArray();
                $userIds = array_merge($userIds, $leaderUserIds);
            }
            // Fulfillment chỉ áp dụng khi không có role Leader
            elseif ($user->hasRole('Fulfillment')) {
                $fulfillmentUserIds = $user->fulfillmentManagedSellers()->pluck('users.id')->toArray();
                $userIds = array_merge($userIds, $fulfillmentUserIds);
            }
        
            $userIds = array_unique($userIds);
            
            if (!empty($userIds)) {
                return $builder->whereIn('user_id', $userIds);
            } else {
                return $builder->whereRaw('1 = 0');
            }
        });

        static::creating(function ($model) {
            if(!$model->user_id) {
                $store = Store::find($model->store_id);
                $model->user_id = $store->owner_id;
            }
        });
    }

    /**
     * Get the store that owns the flash sale.
     */
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * Get the user that owns the flash sale.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include active flash sales.
     */
    public function scopeActive($query)
    {
        return $query->where('deal_status', 'ONGOING');
    }

    /**
     * Scope a query to only include flash sales that need product updates.
     */
    public function scopeNeedsProductUpdate($query)
    {
        return $query->where('deal_status', 'PRODUCT UPDATE');
    }

    /**
     * Scope a query to only include flash sales that need duplication.
     */
    public function scopeNeedsDuplication($query)
    {
        return $query->where('deal_status', 'DUPLICATED');
    }
}
