<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Blank extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'sku',
        'type',
        'description',
        'attributes',
        'stock',
        'cost',
        'supplier',
        'status',
        'image',
        'processing_cost',
    ];

    protected $casts = [
        'attributes' => 'array',
        'stock' => 'integer',
        'cost' => 'decimal:2',
        'processing_cost' => 'decimal:2',
    ];

    public function productions()
    {
        return $this->hasMany(Production::class);
    }

    // Helper method để kiểm tra có đủ stock không
    public function hasEnoughStock(int $quantity): bool
    {
        return $this->stock >= $quantity;
    }

    // Helper method để giảm stock
    public function decreaseStock(int $quantity): void
    {
        if ($this->hasEnoughStock($quantity)) {
            $this->decrement('stock', $quantity);
        }
    }
}
