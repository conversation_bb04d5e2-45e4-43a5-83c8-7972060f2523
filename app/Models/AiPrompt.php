<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AiPrompt extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'system_prompt',
        'requirements',
        'target_metrics',
        'variables',
        'description',
        'is_active',
        'is_default',
        'created_by',
    ];

    protected $casts = [
        'requirements' => 'array',
        'target_metrics' => 'array',
        'variables' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
    ];

    /**
     * Relationship với User (người tạo)
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship với UserAiPrompts
     */
    public function userPrompts(): HasMany
    {
        return $this->hasMany(UserAiPrompt::class);
    }

    /**
     * Scope để lấy prompt active
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope để lấy prompt default theo type
     */
    public function scopeDefaultForType($query, string $type)
    {
        return $query->where('type', $type)
                    ->where('is_default', true)
                    ->where('is_active', true);
    }

    /**
     * Scope để lấy prompt theo type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Lấy danh sách các loại prompt
     */
    public static function getTypes(): array
    {
        return [
            'report' => 'Báo cáo',
            'qa' => 'Hỏi đáp',
            'analysis' => 'Phân tích',
            'summary' => 'Tóm tắt',
            'recommendation' => 'Đề xuất',
            'automation' => 'Tự động hóa',
        ];
    }

    /**
     * Thay thế variables trong system_prompt
     */
    public function renderPrompt(array $customVariables = []): string
    {
        $prompt = $this->system_prompt;
        $variables = array_merge($this->variables ?? [], $customVariables);

        foreach ($variables as $key => $value) {
            $prompt = str_replace("{{$key}}", $value, $prompt);
        }

        return $prompt;
    }

    /**
     * Kiểm tra xem có phải prompt default không
     */
    public function isDefaultForType(): bool
    {
        return $this->is_default && $this->is_active;
    }

    /**
     * Set làm prompt default cho type
     */
    public function setAsDefault(): void
    {
        // Bỏ default của các prompt khác cùng type
        static::where('type', $this->type)
              ->where('id', '!=', $this->id)
              ->update(['is_default' => false]);

        // Set current prompt làm default
        $this->update(['is_default' => true, 'is_active' => true]);
    }
}
