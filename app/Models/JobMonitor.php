<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JobMonitor extends Model
{
    protected $fillable = [
        'job_id',
        'name',
        'queue',
        'status',
        'input',
        'output',
        'error',
        'started_at',
        'finished_at'
    ];

    protected $casts = [
        'input' => 'array',
        'output' => 'array',
        'started_at' => 'datetime',
        'finished_at' => 'datetime'
    ];

    public function scopeQueued($query)
    {
        return $query->where('status', 'queued');
    }

    public function scopeRunning($query)
    {
        return $query->where('status', 'running');
    }

    public function scopeFinished($query)
    {
        return $query->where('status', 'finished');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }
}
