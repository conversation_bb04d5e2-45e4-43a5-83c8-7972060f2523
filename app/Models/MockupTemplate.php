<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MockupTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'apparel_image',
        'preview_image',
        'design_position_x',
        'design_position_y',
        'design_width',
        'design_height',
        'has_back_view',
        'back_design_position_x',
        'back_design_position_y',
        'back_design_width',
        'back_design_height',
    ];

    protected $casts = [
        'has_back_view' => 'boolean',
        'design_position_data' => 'array',
    ];

    public function setDesignPositionDataAttribute($value)
    {
        if (is_string($value)) {
            $data = json_decode($value, true);
        } else {
            $data = $value;
        }

        $this->attributes['design_position_x'] = $data['design_position_x'] ?? $this->attributes['design_position_x'] ?? 0;
        $this->attributes['design_position_y'] = $data['design_position_y'] ?? $this->attributes['design_position_y'] ?? 0;
        $this->attributes['design_width'] = $data['design_width'] ?? $this->attributes['design_width'] ?? 200;
        $this->attributes['design_height'] = $data['design_height'] ?? $this->attributes['design_height'] ?? 200;
        
        if ($this->has_back_view) {
            $this->attributes['back_design_position_x'] = $data['back_design_position_x'] ?? $this->attributes['back_design_position_x'] ?? 0;
            $this->attributes['back_design_position_y'] = $data['back_design_position_y'] ?? $this->attributes['back_design_position_y'] ?? 0;
            $this->attributes['back_design_width'] = $data['back_design_width'] ?? $this->attributes['back_design_width'] ?? 200;
            $this->attributes['back_design_height'] = $data['back_design_height'] ?? $this->attributes['back_design_height'] ?? 200;
        }
    }

    public function getDesignPositionDataAttribute()
    {
        $data = [
            'design_position_x' => $this->design_position_x,
            'design_position_y' => $this->design_position_y,
            'design_width' => $this->design_width,
            'design_height' => $this->design_height,
        ];

        if ($this->has_back_view) {
            $data['back_design_position_x'] = $this->back_design_position_x;
            $data['back_design_position_y'] = $this->back_design_position_y;
            $data['back_design_width'] = $this->back_design_width;
            $data['back_design_height'] = $this->back_design_height;
        }

        return $data;
    }
}