<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use App\Enums\MediaRequestStatus;
use App\Enums\ProductType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MediaRequest extends Model
{


    protected $fillable = [
        'title',
        'video_url',
        'description',
        'seller_id',
        'media_handler_id',
        'status',
        'rejection_reason',
        'price',
        'quantity',
        'total_amount',
        'media_service_type',
        'fund_request_id',
        'production_id',
        'deadline',
        'priority',
        'estimated_hours',
        'revision_notes',
    ];

    protected $casts = [
        'status' => MediaRequestStatus::class,
        'video_url' => 'array',
        'price' => 'decimal:2',
        'quantity' => 'integer',
        'total_amount' => 'decimal:2',
        'deadline' => 'datetime',
        'estimated_hours' => 'integer',
    ];



    // Relationships
    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }
    public function production(): BelongsTo
    {
        return $this->belongsTo(Production::class, 'production_id');
    }

    public function mediaService(): BelongsTo
    {
        return $this->belongsTo(MediaServicePricing::class, 'media_service_type', 'service_type');
    }
    public function mediaHandler()
    {
        return $this->belongsTo(User::class, 'media_handler_id');
    }

    public function fundRequest()
    {
        return $this->hasOne(SellerFundRequest::class, 'id', 'fund_request_id');
    }

    // Static method để generate media handler options
    public static function getMediaHandlerOptions(): array
    {
        try {
            $users = User::whereHas('roles', function ($query) {
                $query->where('name', 'Media');
            })->get();

            if ($users->isEmpty()) {
                return ['no_media' => 'Không có Media Handler nào'];
            }

            return $users->mapWithKeys(function ($user) {
                $workload = $user->mediaRequests()
                    ->whereIn('status', [
                        MediaRequestStatus::Pending->value,
                        MediaRequestStatus::InProgress->value
                    ])
                    ->count();

                $status = $workload == 0 ? '🟢 Rảnh' :
                         ($workload <= 2 ? '🟡 Bận vừa' : '🔴 Bận nhiều');

                return [
                    $user->id => "{$user->name} - {$status} ({$workload} tasks)"
                ];
            })->toArray();
        } catch (\Exception $e) {
            \Log::error('Error generating media handler options: ' . $e->getMessage());
        }
    }

    // Global scope để lọc theo quyền
    protected static function boot()
    {
        parent::boot();

        // Set default seller_id to current user when creating
        static::creating(function ($model) {
            if (auth()->check() && !$model->seller_id) {
                $model->seller_id = auth()->id();
            }

            // Set title from production name if production_id exists
            if ($model->production_id && !$model->title) {
                $model->title = $model->production?->name;
            }

            // Set default media_service_type if not provided
            if (!$model->media_service_type) {
                $model->media_service_type = 'basic_product_video';
            }

            // Set default quantity if not provided
            if (!$model->quantity) {
                $model->quantity = 1;
            }

            // Tự động tính giá và deadline dựa trên media_service_type từ bảng MediaServicePricing
            if ($model->media_service_type) {
                $service = \App\Models\MediaServicePricing::where('service_type', $model->media_service_type)->first();
                if ($service) {
                    $model->price = $service->base_price;

                    // Set estimated_hours nếu chưa có
                    if (!$model->estimated_hours) {
                        $model->estimated_hours = $service->estimated_hours;
                    }

                    // Set deadline dựa trên delivery_days nếu chưa có
                    if (!$model->deadline) {
                        $deliveryDays = $service->delivery_days ?? 1; // Default 1 day nếu không có
                        $model->deadline = now()->addDays($deliveryDays);
                    }
                } else {
                    // Fallback nếu không tìm thấy service
                    $model->price = 10.00;
                    if (!$model->deadline) {
                        $model->deadline = now()->addDay(); // Default 1 day
                    }
                }
            } else {
                // Nếu không có media_service_type, set deadline mặc định 1 ngày
                if (!$model->deadline) {
                    $model->deadline = now()->addDay();
                }
            }

            // Calculate total_amount based on price and quantity
            $model->total_amount = $model->price * $model->quantity;
        });

        // Tạo SellerFundRequest sau khi MediaRequest được tạo
        static::created(function ($model) {
            if ($model->total_amount > 0) {
                $expenseType = \App\Models\ExpenseType::where('code', 'MEDIA_SERVICE_FEE')->first();

                if ($expenseType) {
                    $fundRequest = \App\Models\SellerFundRequest::create([
                        'seller_id' => $model->seller_id,
                        'expense_type_id' => $expenseType->id,
                        'amount' => $model->total_amount,
                        'currency' => 'USD',
                        'status' => 'approved', // Tự động approve cho media service
                        'description' => "Media service fee ({$model->media_service_type}) x{$model->quantity} for: {$model->title}",
                        'additional_fields' => [
                            'media_request_id' => $model->id,
                            'service_type' => $model->media_service_type,
                            'quantity' => $model->quantity,
                            'unit_price' => $model->price
                        ],
                        'approved_by' => auth()->id(),
                        'approved_at' => now()
                    ]);

                    // Update MediaRequest với fund_request_id
                    $model->update(['fund_request_id' => $fundRequest->id]);
                }
            }
        });
        static::updating(function ($model) {
            // Update title if production changes
            if ($model->isDirty('production_id') && $model->production_id) {
                $model->title = $model->production?->name;
            }

            // Recalculate total_amount if price or quantity changes
            if ($model->isDirty(['price', 'quantity'])) {
                $model->total_amount = $model->price * $model->quantity;
            }
        });
        static::addGlobalScope('access', function (Builder $builder) {
            if (auth()->check()) {
                $user = auth()->user();

                // Super admin thấy tất cả
                if ($user->hasRole(['super_admin', 'User Manager', 'Media Manager'])) {
                    return;
                }

                // Media Manager thấy các yêu cầu được assign cho họ
                if ($user->hasRole('Media')) {
                    $builder->where(function ($query) use ($user) {
                        $query->where('media_handler_id', $user->id)
                            ->orWhereNull('media_handler_id');
                    });
                    return;
                }

                // Leader thấy yêu cầu của họ và của sellers họ quản lý
                if ($user->hasRole('Leader')) {
                    $sellerIds = [$user->id];
                    $leaderSellerIds = $user->leaderManagedSellers()->pluck('users.id')->toArray();
                    $sellerIds = array_merge($sellerIds, $leaderSellerIds);

                    $builder->whereIn('seller_id', $sellerIds);
                    return;
                }

                // Seller chỉ thấy yêu cầu của họ
                $builder->where('seller_id', $user->id);
            }
        });
    }
}
