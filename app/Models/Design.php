<?php

namespace App\Models;

use App\Enums\DesignJobStatus;
use App\Enums\DesignJobType;
use App\Enums\DesignStatus;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPanelShield;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Parallax\FilamentComments\Models\Traits\HasFilamentComments;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\Permission\Traits\HasRoles;

class Design extends Model
{
    use HasFilamentComments;

    /**
     * @var array
     */
    protected $fillable = [
        'required_locations',
        'designer_id',
        'created_by',
        'status',
        'design_fee',
        'name',
        'sub_title',
        'desc',
        'type',
        'source',
        'files',
        'completed_at',
        'created_at',
        'updated_at',
        'seller_id',
        'design_front',
        'design_back',
        'mockup_front',
        'mockup_back',
        'mockup',
        'sku',
        'product_id'
        // 'file_chest',
        // 'file_left_arm',
        // 'file_right_arm',
        // 'file_collar',
    ];

    protected static $logFillable = true;
    protected static $logOnlyDirty = true;
    protected static $submitEmptyLogs = false;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'designer_id',
                'created_by',
                'status',
                'design_fee',
                'name',
                'sub_title',
                'desc',
                'type',
                'source',
                'files',
                'completed_at',
                'seller_id',
                'design_front',
                'design_back',
                'mockup_front',
                'mockup_back',
                'mockup',
                'sku',
                'product_id'
            ]) // Customize to log only these attributes
            ->useLogName('design')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
    public function getDescriptionForEvent(string $eventName): string
    {
        return "Design '{$this->id}' has been {$eventName}";
    }
    // public function getMockupFrontAttribute()
    // {
    //     if (!empty($this->attributes['mockup_front'])) {
    //         return $this->attributes['mockup_front'];
    //     }

    //     if (!empty($this->attributes['mockup_back'])) {
    //         return null;
    //     }

    //     return $this->attributes['mockup'] ?? null;
    // }
    public function scopeFilterByRole($query)
    {
        return $query;
        $user = auth()->user();

        if ($user->hasRole('super_admin') || $user->hasRole('Fulfillment')) {
            // Super admin and fulfillment roles see all designs.
            return $query;
        } elseif ($user->hasRole('Seller')) {

            return $query->where('seller_id', $user->id);
        } elseif ($user->hasRole('Designer')) {
            // Designers see only their assigned designs.
            return $query->where('designer_id', $user->id);
        }

        // By default, no designs are returned if none of the above conditions are met.
        return $query->where('id', 0); // Assuming 'id' is always positive.
    }
    protected $casts = [
        'files' => 'array',
        'status' => DesignStatus::class,
        'required_locations' => 'array',
    ];




    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class, 'product_id', 'product_id');
    }

    public function orders()
    {
        return $this->belongsToMany(Order::class, 'order_items', 'product_id', 'order_id', 'product_id')
            ->distinct();
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }
    public function designFiles()
    {
        return $this->hasMany(DesignFile::class);
    }
    public function designFilesGroup()
    {
        return $this->designFiles()->orderBy('location');
    }
    public function designer()
    {
        return $this->belongsTo(User::class, 'designer_id');
    }

    protected static function booting(): void
    {
        static::creating(function ($model) {
            if (auth()->check()) {
                $model->created_by = Auth::id();
            }

            // Đảm bảo name luôn có giá trị
            if (empty($model->name)) {
                $model->name = 'Design #' . time();
            }
        });
        static::created(function ($model) {});
    }
    public function canFulfill(string $color): bool
    {
        if (empty($this->required_locations)) {
            return true; // Nếu không có vị trí bắt buộc, coi như có thể fulfill
        }

        foreach ($this->required_locations as $location) {
            $hasFileForLocation = $this->design_files->contains(function ($designFile) use ($location, $color) {
                return $designFile['location'] === $location && $designFile['color'] === $color;
            });

            if (!$hasFileForLocation) {
                return false;
            }
        }
        return true;
    }
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }
            $user = Auth::user();
            if ($user->hasRole(['super_admin', 'User Manager', 'Developer'])) {
                return $builder;
            }

            // Fulfillment Manager có thể xem tất cả designs
            if ($user->hasRole('Fullfillment Manager')) {
                return $builder;
            }

            $sellerIds = [];
            if ($user->hasRole('Seller')) {
                $sellerIds[] = $user->id;
            }

            // Leader có ưu tiên cao hơn Fulfillment
            if ($user->hasRole('Leader')) {
                $sellerIds[] = $user->id;
                $leaderSellerIds = $user->leaderManagedSellers()->pluck('users.id')->toArray();
                $sellerIds = array_merge($sellerIds, $leaderSellerIds);
            }
            // Fulfillment chỉ áp dụng khi không có role Leader
            elseif ($user->hasRole('Fulfillment')) {
                $fulfillmentSellerIds = $user->fulfillmentManagedSellers()->pluck('users.id')->toArray();
                $sellerIds = array_merge($sellerIds, $fulfillmentSellerIds);
            }

            if ($user->hasRole('Designer')) {
                // Specifically check for designs that belong to the designer
                $builder->orWhere('designer_id', $user->id);
            }

            $sellerIds = array_unique($sellerIds);

            if (!empty($sellerIds)) {
                $builder->whereIn('seller_id', $sellerIds);
            }

            // If no valid ids are found, ensure no records are returned
            if (empty($sellerIds) && !$user->hasRole('Designer')) {
                $builder->whereRaw('1 = 0');
            }

            return $builder;
        });
    }


    public function designJobs()
    {
        return $this->hasMany(DesignJob::class);
    }

    public function getTotalDesignCostAttribute()
    {
        return $this->designJobs()->sum('price');
    }

    public function createJob(DesignJobType $jobType, array $attributes = [])
    {
        $job = new DesignJob([
            'job_type' => $jobType,
            'status' => DesignJobStatus::PENDING,
            'price' => $jobType->getBasePrice(),
            ...$attributes
        ]);

        $this->designJobs()->save($job);
        return $job;
    }

    /**
     * Lấy dữ liệu hóa đơn cho designer dựa trên khoảng thời gian
     * 
     * @param  \Illuminate\Http\Request  $request
     * @param  int|string  $designer_id ID của designer cần tạo báo cáo. Nếu là -1 thì sẽ tạo báo cáo cho tất cả designer
     * @return \Illuminate\View\View
     */
    public function getInvoiceData(\Illuminate\Http\Request $request, $designer_id)
    {
        $summary = $request->input('summary', []);
        $time_range = $summary['time_range']['created_at'] ?? '';
            
            
      
        // Trường hợp 1: time_range là mảng với key 'created_at'
        if (is_array($time_range) && isset($time_range['created_at'])) {
            $dateRange = $time_range['created_at'];
                if (strpos($dateRange, '-') !== false) {
                    [$start, $end] = array_map('trim', explode('-', $dateRange));
                    $startDate = \Carbon\Carbon::createFromFormat('d/m/Y', $start)->startOfDay();
                    $endDate = \Carbon\Carbon::createFromFormat('d/m/Y', $end)->endOfDay();
                    // Đảm bảo time_range ở dạng chuỗi cho hiển thị
                    $time_range = $dateRange;
                } else {
                    // Mặc định nếu không tìm thấy dấu gạch ngang
                    $startDate = \Carbon\Carbon::now()->startOfMonth()->startOfDay();
                    $endDate = \Carbon\Carbon::now()->endOfMonth()->endOfDay();
                    $time_range = $startDate->format('d/m/Y') . ' - ' . $endDate->format('d/m/Y');
                }
            }
            // Trường hợp 2: time_range là chuỗi có dạng "DD/MM/YYYY - DD/MM/YYYY"
            elseif (is_string($time_range) && strpos($time_range, '-') !== false) {
                [$start, $end] = array_map('trim', explode('-', $time_range));
                $startDate = \Carbon\Carbon::createFromFormat('d/m/Y', $start)->startOfDay();
                $endDate = \Carbon\Carbon::createFromFormat('d/m/Y', $end)->endOfDay();
            } 
            // Trường hợp mặc định: Sử dụng tháng hiện tại
            else {
                $startDate = \Carbon\Carbon::now()->startOfMonth()->startOfDay();
                $endDate = \Carbon\Carbon::now()->endOfMonth()->endOfDay();
            $time_range = $startDate->format('d/m/Y') . ' - ' . $endDate->format('d/m/Y');
        }

            

        // Thiết lập thông tin hóa đơn
        $issue_date = now(); // Ngày phát hành: thời điểm hiện tại
        $due_date = now()->addDays(30); // Ngày đến hạn: 30 ngày sau ngày phát hành
        $billing_month = now(); // Tháng thanh toán: tháng hiện tại

        // Xây dựng query tùy thuộc vào designer_id
        $jobsQuery = DesignJob::query()
            ->where('designer_id', $designer_id)
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate]);
      
        // Lấy danh sách jobs để hiển thị chi tiết
        $jobs = $jobsQuery->get();

        foreach ($jobs as $job) {
            $job->seller = User::find($job->created_by);
            $job->designer = User::find($job->designer_id);
        }

        // Nhóm jobs theo seller và tính tổng
        $jobsBySeller = static::getJobsBySeller($designer_id, $startDate, $endDate, true);

        // Tính tổng thu nhập từ các công việc thiết kế đã hoàn thành trong khoảng thời gian đã chọn
        $total_earnings = $jobs->sum('price');
        $total_jobs = $jobs->count();

        $designer = User::find($designer_id);
        


        // Tạo dữ liệu trả về cho hóa đơn
        $data = [
            'time_range' => $time_range, // Khoảng thời gian đã xử lý
            'start_date' => $startDate->format('d/m/Y'), // Ngày bắt đầu đã định dạng
            'end_date' => $endDate->format('d/m/Y'), // Ngày kết thúc đã định dạng
            'total_earnings' => $total_earnings, // Tổng thu nhập
            'total_jobs' => $total_jobs, // Tổng số jobs
            'invoiceNumber' => random_int(1000000000, 9999999999), // Số hóa đơn ngẫu nhiên 10 chữ số
            'issueDate' => $issue_date instanceof \DateTime ? $issue_date->format('d/m/Y') : $issue_date, // Ngày phát hành
            'dueDate' => $due_date instanceof \DateTime ? $due_date->format('d/m/Y') : $due_date, // Ngày đến hạn
            'status' => 'COMPLETED', // Trạng thái hóa đơn
            'reportPeriod' => $billing_month instanceof \DateTime ? $billing_month->format('m/Y') : $billing_month, // Kỳ báo cáo
            'jobs' => $jobs, // Danh sách các jobs chi tiết
            'jobsBySeller' => $jobsBySeller, // Jobs được nhóm theo seller
            'designer' => $designer, // ID của designer đang xem báo cáo
        ];

        return view('invoice-design.print', [
            'invoice' => $data,
        ]);
    }

    /**
     * Group design jobs by seller and sum their prices
     * 
     * @param  int|string  $designer_id ID of the designer whose jobs will be grouped
     * @param  \Carbon\Carbon  $startDate Start date for filtering jobs
     * @param  \Carbon\Carbon  $endDate End date for filtering jobs
     * @return \Illuminate\Support\Collection Collection of jobs grouped by seller
     */
    public function getJobsSummarizedBySeller($designer_id, $startDate = null, $endDate = null)
    {
        // Set default date range if not provided
        if (!$startDate) {
            $startDate = \Carbon\Carbon::now()->startOfMonth()->startOfDay();
        }
        
        if (!$endDate) {
            $endDate = \Carbon\Carbon::now()->endOfMonth()->endOfDay();
        }
        
        // Build the query
        $jobsQuery = DesignJob::query()
            ->where('designer_id', $designer_id)
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate]);
      
        // Fetch all matching jobs
        $jobs = $jobsQuery->get();
        
        // Group jobs by seller ID and sum up their prices
        $jobsBySeller = $jobs->groupBy('created_by')->map(function ($sellerJobs) {
            $seller = User::find($sellerJobs->first()->created_by);
            
            return [
                'seller' => $seller,
                'jobs_count' => $sellerJobs->count(),
                'total_price' => $sellerJobs->sum('price'),
                'jobs' => $sellerJobs, // Include individual jobs if needed
            ];
        });
        
        return $jobsBySeller;
    }

    /**
     * Static method to get design jobs grouped by seller
     * 
     * @param  int|string  $designer_id ID of the designer whose jobs will be grouped
     * @param  \Carbon\Carbon|string|null  $startDate Start date for filtering jobs
     * @param  \Carbon\Carbon|string|null  $endDate End date for filtering jobs
     * @param  bool  $includeDetails Whether to include job details
     * @return \Illuminate\Support\Collection Collection of jobs grouped by seller
     */
    public static function getJobsBySeller($designer_id, $startDate = null, $endDate = null, $includeDetails = true)
    {
        // Convert date strings to Carbon instances if needed
        if (is_string($startDate)) {
            $startDate = \Carbon\Carbon::parse($startDate)->startOfDay();
        } elseif (!$startDate) {
            $startDate = \Carbon\Carbon::now()->startOfMonth()->startOfDay();
        }
        
        if (is_string($endDate)) {
            $endDate = \Carbon\Carbon::parse($endDate)->endOfDay();
        } elseif (!$endDate) {
            $endDate = \Carbon\Carbon::now()->endOfMonth()->endOfDay();
        }
        
        // Build the query
        $jobsQuery = DesignJob::query()
            ->where('designer_id', $designer_id)
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate]);
      
        // Fetch all matching jobs
        $jobs = $jobsQuery->get();
        
        // Group jobs by seller ID and sum up their prices
        $jobsBySeller = $jobs->groupBy('created_by')->map(function ($sellerJobs) use ($includeDetails) {
            $seller = User::find($sellerJobs->first()->created_by);
            
            $result = [
                'seller' => $seller,
                'seller_id' => $seller->id ?? null,
                'seller_name' => $seller->name ?? 'Unknown',
                'jobs_count' => $sellerJobs->count(),
                'total_price' => $sellerJobs->sum('price'),
            ];
            
            // Include job details if requested
            if ($includeDetails) {
                $result['jobs'] = $sellerJobs->values();
            }
            
            return $result;
        })->values();
        
        return $jobsBySeller;
    }

    protected function calculateTotalRevenue()
    {
        return $this->design_fee ?? 0;
    }

    protected function calculateTotalPayment()
    {
        return $this->design_fee ?? 0;
    }

    public static function renderPrintView(Request $request, $designer_id)
    {
        // try {
            return (new static)->getInvoiceData($request, $designer_id);
        // } catch (\Exception $e) {
        //     \Illuminate\Support\Facades\Log::error('Lỗi khi tạo báo cáo hóa đơn designer', [
        //         'error' => $e->getMessage(),
        //         'designer_id' => $designer_id,
        //         'trace' => $e->getTraceAsString(),
        //     ]);
            
        //     abort(500, 'Lỗi khi tạo báo cáo hóa đơn: ' . $e->getMessage());
        // }
    }
}
