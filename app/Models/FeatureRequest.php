<?php

namespace App\Models;

use App\Enums\FeatureRequestStatus;
use App\Enums\FeatureRequestPriority;
use App\Enums\FeatureRequestCategory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;

class FeatureRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'priority',
        'status',
        'category',
        'admin_notes',
        'completed_at',
    ];

    protected $casts = [
        'completed_at' => 'datetime',
        'status' => FeatureRequestStatus::class,
        'priority' => FeatureRequestPriority::class,
        'category' => FeatureRequestCategory::class,
    ];

    /**
     * Boot method để áp dụng global scope
     */
    protected static function boot()
    {
        parent::boot();

        // Áp dụng global scope để kiểm soát quyền truy cập dữ liệu
        static::addGlobalScope('user_access', function (Builder $builder) {
            $user = auth()->user();

            if (!$user) {
                // Nếu chưa đăng nhập, không cho xem gì cả
                return $builder->whereRaw('1 = 0');
            }

            // Super admin và admin có thể xem tất cả
            if ($user->hasAnyRole(['super_admin', 'admin', 'User Manager'])) {
                return $builder;
            }

            // User thường chỉ xem được yêu cầu của họ
            return $builder->where('user_id', $user->id);
        });

        // Tự động set user_id khi tạo mới
        static::creating(function ($model) {
            if (auth()->check() && !$model->user_id) {
                $model->user_id = auth()->id();
            }
        });
    }

    /**
     * Get the user that owns the feature request.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }


}
