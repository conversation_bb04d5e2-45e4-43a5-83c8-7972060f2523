<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Email extends Model
{
    protected $table = 'temp_emails';
    
    protected $fillable = [
        'message_id',
        'from',
        'subject',
        'date',
        'size',
        'body'
    ];

    public $timestamps = false;

    protected $casts = [
        'date' => 'datetime',
        'size' => 'integer',
    ];
}