<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Enums\FileLocation;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;


class DesignFile extends Model
{
    use HasFactory;
    protected $fillable = ['design_id', 'file_url', 'file_path', 'file_type', 'location','suitable_colors','file_style'];
    protected $appends = ['design_thumb'];
    protected $attributes = [
        'file_type' => 'PNG'  // Set giá trị mặc định
    ];
    protected $casts = [
        'location' => FileLocation::class,
        'suitable_colors' => 'array',

    ];
    public function design()
    {
        return $this->belongsTo(Design::class);
    }
    public function getFileUrlAttribute($value)
    {
        if ($this->shouldHideLink($value)) {
            try {
                $encryptedUrl = Crypt::encryptString($value);
                return route('image.show', ['encryptedUrl' => $encryptedUrl]);
            } catch (\Exception $e) {
                return null; // Return null if decryption fails
            }
        }
        return $value;
    }
    public function getDesignLinkAttribute()
    {
        if (!$this->file_path && !$this->file_url) {
            return null; // Return null if both file_path and file_url don't exist
        }

        $url = $this->file_url ?? Storage::disk('s3')->url($this->file_path);
        if ($this->shouldHideLink($url)) {
            $encryptedUrl = Crypt::encryptString($url);
            return route('image.show', ['encryptedUrl' => $encryptedUrl]); // Return route URL
        }
        // Kiểm tra xem URL có hợp lệ hay không
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return null;
        }

        // // Loại bỏ các ký tự đặc biệt
        // $url = preg_replace('/[^A-Za-z0-9\-_:.\/?&=]+/', '', $url);
        $url = str_replace("'", '', $url);
        return $url;
    }


    private function shouldHideLink($url)
    {
        $patterns = ['supoverdesign.', 'supimg.', 'pressify.'];
        foreach ($patterns as $pattern) {
            if (strpos($url, $pattern) !== false) {
                return true;
            }
        }
        return false;
    }


    public function getDesignThumbAttribute()
    {
        if (!$this->file_path && !$this->file_url) {
            return null; // Return null if both file_path and file_url don't exist
        }

        $url = $this->file_url ?? Storage::disk('s3')->url($this->file_path);

        // Kiểm tra xem URL có hợp lệ hay không
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return null;
        }

        // // Loại bỏ các ký tự đặc biệt
        // $url = preg_replace('/[^A-Za-z0-9\-_:.\/?&=]+/', '', $url);
        $url = str_replace("'", '', $url);
        return $this->photon_url($url);
    }
    public static function photon_url($image_url, $width = 300) {
        // List of Photon base URLs
        $photon_base_urls = [
            'https://i0.wp.com/',
            'https://i1.wp.com/',
            'https://i2.wp.com/',
            'https://i3.wp.com/'
        ];
    
        // Select a base URL randomly
        $photon_base_url = $photon_base_urls[array_rand($photon_base_urls)];
        
        // Remove 'https://' or 'http://' from the image URL
        $stripped_image_url = preg_replace('/^https?:\/\//', '', $image_url);
        
        // Construct the Photon URL with the desired width
        $photon_image_url = $photon_base_url . $stripped_image_url . '?w=' . $width;
        
        return $photon_image_url;
    }
}
