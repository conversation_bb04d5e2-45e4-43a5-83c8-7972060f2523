<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ExpenseType extends Model
{
    use HasFactory;

    protected $casts = [
        'limit_amount' => 'decimal:2',
        'requires_approval' => 'boolean',
        'active' => 'boolean',
        'required_fields' => 'array',
        'field_descriptions' => 'array'
    ];
    
    protected $attributes = [
        'required_fields' => '[]',
        'field_descriptions' => '[]'
    ];

    protected $fillable = [
        'name',
        'code',
        'description',
        'category',
        'limit_amount',
        'limit_period',
        'requires_approval',
        'active',
        'required_fields',
        'field_descriptions'
    ];

    // Relationships
    public function fundRequests()
    {
        return $this->hasMany(SellerFundRequest::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    // Methods
    public function getFormattedLimitAttribute()
    {
        if (!$this->limit_amount) return 'No limit';
        return '$' . number_format($this->limit_amount) . ' per ' . $this->limit_period;
    }
    protected static function boot()
    {
        parent::boot();
    }
}