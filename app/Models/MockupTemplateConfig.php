<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class MockupTemplateConfig extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'apparel_image',
        'has_back_view',
        // Front design position
        'design_position_x',
        'design_position_y',
        'design_width',
        'design_height',
        // Back design position
        'back_design_position_x',
        'back_design_position_y',
        'back_design_width',
        'back_design_height',
        'status',
    ];

    protected $casts = [
        'has_back_view' => 'boolean',
        'design_position_x' => 'integer',
        'design_position_y' => 'integer',
        'design_width' => 'integer',
        'design_height' => 'integer',
        'back_design_position_x' => 'integer',
        'back_design_position_y' => 'integer',
        'back_design_width' => 'integer',
        'back_design_height' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (auth()->check()) {
                $model->user_id = auth()->id();
            }

            // Check if all position and dimension fields are 0
            $positionFields = [
                'design_position_x',
                'design_position_y',
                'design_width',
                'design_height',
                'back_design_position_x',
                'back_design_position_y',
                'back_design_width',
                'back_design_height'
            ];

            $allZero = collect($positionFields)->every(function ($field) use ($model) {
                return $model->{$field} === 0 || $model->{$field} === null;
            });

            if ($allZero) {
                foreach ($positionFields as $field) {
                    $model->{$field} = 200;
                }
            }
        });

        // Thêm Global Scope để tự động lọc theo groups
        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (auth()->check()) {
                $user = auth()->user();

                // Super admin thấy hết
                if ($user->is_super_admin) {
                    return;
                }

                // User thường chỉ thấy configs trong groups public hoặc của mình
                $builder->whereHas('groups', function ($query) use ($user) {
                    $query->where(function ($q) use ($user) {
                        $q->where('is_public', true)
                            ->orWhere('user_id', $user->id);
                    });
                });
            } else {
                // Guest chỉ thấy configs trong groups public
                $builder->whereHas('groups', function ($query) {
                    $query->where('is_public', true);
                });
            }
        });
    }

    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(
            MockupTemplateGroup::class,
            'mockup_template_mockup_template_group',
            'mockup_template_id',
            'mockup_template_group_id'
        )->withTimestamps();
    }

    public static function getStatuses(): array
    {
        return [
            'draft' => 'Draft',
            'active' => 'Active',
            'archived' => 'Archived',
        ];
    }

    /**
     * Get the front design position data
     */
    public function getFrontDesignPositionAttribute(): array
    {
        return [
            'x' => $this->design_position_x,
            'y' => $this->design_position_y,
            'width' => $this->design_width,
            'height' => $this->design_height,
        ];
    }

    /**
     * Get the back design position data
     */
    public function getBackDesignPositionAttribute(): array
    {
        return [
            'x' => $this->back_design_position_x,
            'y' => $this->back_design_position_y,
            'width' => $this->back_design_width,
            'height' => $this->back_design_height,
        ];
    }

    /**
     * Check if the template has valid back design position
     */
    public function hasValidBackDesign(): bool
    {
        return $this->has_back_view &&
            ($this->back_design_width > 0 || $this->back_design_height > 0);
    }

    /**
     * Get the design position data for a specific side
     */
    public function getDesignPosition(string $side = 'front'): array
    {
        return $side === 'front'
            ? $this->front_design_position
            : $this->back_design_position;
    }
}
