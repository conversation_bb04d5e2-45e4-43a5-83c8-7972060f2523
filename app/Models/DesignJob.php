<?php

namespace App\Models;

use App\Enums\DesignJobStatus;
use App\Enums\DesignJobType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DesignJob extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'design_id',
        'designer_id',
        'order_id',
        'job_type',
        'status',
        'price',
        'description',
        'designer_note',
        'deadline',
        'completed_at',
        'assigned_at',
        'is_rush',
        'final_files',
        'reference_files',
        'rush_fee',
        'created_by',
        'title'
    ];

    protected $casts = [
        'reference_files' => 'array',
        'final_files' => 'array',
        'job_type' => DesignJobType::class,
        'status' => DesignJobStatus::class,
        'deadline' => 'datetime',
        'completed_at' => 'datetime',
        'is_rush' => 'boolean'
    ];
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    public function design(): BelongsTo
    {
        return $this->belongsTo(Design::class);
    }

    public function designer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'designer_id')
            ->whereHas('roles', function($query) {
                $query->where('name', 'Designer');
            });
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function calculateTotalPrice(): float
    {
        $basePrice = $this->job_type->getBasePrice();
        $rushFee = $this->is_rush ? $this->rush_fee : 0;
        return $basePrice + $rushFee;
    }

    public function assignTo(User $designer)
    {
        $this->designer_id = $designer->id;
        $this->status = DesignJobStatus::ASSIGNED;
        $this->assigned_at = now();
        $this->save();
    }

    public function markAsCompleted()
    {
        DB::beginTransaction();
        try {
            // Cập nhật trạng thái của job hiện tại
            $this->status = DesignJobStatus::COMPLETED;
            $this->completed_at = now();
            $this->save();

            // // Kiểm tra xem còn job nào chưa hoàn thành không
            // $incompleteJobsCount = $this->design->designJobs()
            //     ->where('id', '!=', $this->id) // Loại trừ job hiện tại
            //     ->where('status', '!=', DesignJobStatus::COMPLETED)
            //     ->count();

            // // Nếu không còn job nào chưa hoàn thành
            // if ($incompleteJobsCount === 0) {
            //     $this->design->update([
            //         'design_status' => 'completed',
            //         'design_completed_at' => now()
            //     ]);
            // }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error in markAsCompleted: ' . $e->getMessage());
            throw $e;
        }
    }

    protected static function booting(): void
    {
        static::creating(function ($model) {
            if (auth()->check()) {
                if(!$model->created_by){
                    $model->created_by = Auth::id();
                }
            }

            // Đảm bảo price luôn có giá trị, không được null
            if (is_null($model->price)) {
                $model->price = 0;
            }

            // Đảm bảo rush_fee luôn có giá trị, không được null
            if (is_null($model->rush_fee)) {
                $model->rush_fee = 0;
            }
        });
        static::created(function ($model) {});
    }

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('accessScope', function (Builder $builder) {
            if (!Auth::check()) {
                return $builder;
            }
            $user = Auth::user();
            if ($user->hasRole(['super_admin', 'User Manager', 'Fullfillment Manager', 'Accountant'])) {
                return $builder;
            }
            if ($user->hasRole('Designer')) {
                // Specifically check for designs that belong to the designer
                return $builder->where('designer_id', $user->id);
            }
            $sellerIds = [];
            if ($user->hasRole('Seller')) {
                $sellerIds[] = $user->id;
            }

            if ($user->hasRole('Leader')) {
                $sellerIds[] = $user->id;
                $leaderSellerIds = $user->leaderManagedSellers()->pluck('users.id')->toArray();
                $sellerIds = array_merge($sellerIds, $leaderSellerIds);
            }

            // if ($user->hasRole('Fulfillment')) {
            //     $fulfillmentSellerIds = $user->fulfillmentManagedSellers()->pluck('users.id')->toArray();
            //     $sellerIds = array_merge($sellerIds, $fulfillmentSellerIds);
            // }



            $sellerIds = array_unique($sellerIds);

            if (!empty($sellerIds)) {
                $builder->whereIn('created_by', $sellerIds);
            } else {
                // Nếu không có quyền nào trong những quyền trên, chỉ trả về record của chính user đó
                $builder->where('created_by', $user->id);
            }

            return $builder;
        });
    }
}
