<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class LarkSend extends Model
{
    use HasFactory;

    /**
     * Boot method để áp dụng global scope
     */
    protected static function boot()
    {
        parent::boot();

        // Áp dụng global scope để kiểm soát quyền truy cập dữ liệu
        static::addGlobalScope('user_access', function (Builder $builder) {
            $user = auth()->user();

            if (!$user) {
                // Nếu chưa đăng nhập, không cho xem gì cả
                return $builder->whereRaw('1 = 0');
            }

            // Super admin và user manager có thể xem tất cả
            if ($user->hasAnyRole(['super_admin', 'user_manager'])) {
                return $builder;
            }

            // User thường chỉ xem được tin nhắn mà họ gửi hoặc nhận
            return $builder->where(function ($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('recipient_user_id', $user->id);
            });
        });
    }

    protected $fillable = [
        'user_id',
        'recipient_user_id',
        'message',
        'sent_at',
        'status',
        'error_message',
        'lark_response',
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'lark_response' => 'array',
    ];

    /**
     * Relationship với user gửi tin nhắn
     */
    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Relationship với user nhận tin nhắn
     */
    public function recipient(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recipient_user_id');
    }

    /**
     * Scope để lọc tin nhắn đã gửi thành công
     */
    public function scopeSent(Builder $query): Builder
    {
        return $query->where('status', 'sent');
    }

    /**
     * Scope để lọc tin nhắn đang chờ gửi
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope để lọc tin nhắn gửi thất bại
     */
    public function scopeFailed(Builder $query): Builder
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope để lọc tin nhắn theo người gửi
     */
    public function scopeBySender(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope để lọc tin nhắn theo người nhận
     */
    public function scopeByRecipient(Builder $query, int $userId): Builder
    {
        return $query->where('recipient_user_id', $userId);
    }

    /**
     * Scope để lọc tin nhắn trong khoảng thời gian
     */
    public function scopeInDateRange(Builder $query, $startDate, $endDate): Builder
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Kiểm tra xem tin nhắn đã được gửi chưa
     */
    public function isSent(): bool
    {
        return $this->status === 'sent';
    }

    /**
     * Kiểm tra xem tin nhắn có đang chờ gửi không
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Kiểm tra xem tin nhắn có gửi thất bại không
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Đánh dấu tin nhắn đã gửi thành công
     */
    public function markAsSent(?array $larkResponse = null): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
            'lark_response' => $larkResponse,
        ]);
    }

    /**
     * Đánh dấu tin nhắn gửi thất bại
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }
}
