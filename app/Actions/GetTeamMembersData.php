<?php

namespace App\Actions;

use App\Models\User;
use App\Models\Store;
use App\Models\Order;
use App\Models\Team;
use App\Models\Product;
use App\Models\SellerLevel;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Enums\TiktokShopStatus;

class GetTeamMembersData
{
    
    /**
     * Execute the action to get team members data
     */
    public function execute(int $teamId): array
    {
        // Get team data
        $team = Team::with([
            'users' => function ($query) {
                $query->select('users.id', 'users.name', 'users.email', 'users.avatar_url', 
                    'users.telegram_id', 'users.bank_name', 'users.bank_account_number', 
                    'users.seller_level_id', 'users.created_at', 'users.has_fixed_commission', 
                    'users.fixed_commission_rate', 'users.has_base_salary')
                    ->with([
                        'roles',
                        'sellerLevel'
                    ]);
            }
        ])->find($teamId);
        
        if (!$team) {
            return [];
        }
        
        // Debug logging
        Log::debug("Team Users: " . $team->users->count());
        
        $sellers = $team->users->filter(function($user) {
            return $user && method_exists($user, 'hasRole') && $user->hasRole('Seller');
        });
        
        Log::debug("Filtered Sellers: " . $sellers->count());

        $leaders = $team->users->filter(function($user) {
            return $user && method_exists($user, 'hasRole') && $user->hasRole('Leader');
        });
        
        Log::debug("Filtered Leaders: " . $leaders->count());
        
        // Get team stats for all sellers
        $sellerIds = $sellers->pluck('id')->toArray();
        $teamStats = empty($sellerIds) ? $this->getEmptyStats() : $this->getBasicTeamStats($sellerIds);
        
        // Process each seller to get their individual stats
        $processedSellers = $sellers->map(function($seller) {
            $stats = $this->getBasicSellerStats($seller);
            $storeCounts = $this->getSellerStoreCounts($seller->id);
            
            // Default values for has_fixed_commission and has_base_salary
            $has_fixed_commission = $seller->has_fixed_commission ?? false;
            $fixed_commission_rate = $seller->fixed_commission_rate ?? 0;
            $has_base_salary = $seller->has_base_salary ?? true;
            
            return [
                'id' => $seller->id,
                'name' => $seller->name,
                'email' => $seller->email,
                'avatar_url' => $seller->getFilamentAvatarUrl(),
                'telegram_id' => $seller->telegram_id,
                'bank_name' => $seller->bank_name,
                'bank_account_number' => $seller->bank_account_number,
                'is_leader' => $seller->hasRole('Leader'),
                'has_fixed_commission' => $has_fixed_commission,
                'fixed_commission_rate' => $fixed_commission_rate,
                'has_base_salary' => $has_base_salary,
                'seller_level' => $seller->sellerLevel ? [
                    'id' => $seller->sellerLevel->id,
                    'name' => $seller->sellerLevel->name,
                    'base_salary' => $seller->sellerLevel->base_salary,
                    'bonus_rate' => $seller->sellerLevel->bonus_rate,
                    'commission_type' => $has_fixed_commission ? 'fixed' : 'variable',
                    'commission_rate' => $has_fixed_commission ? $fixed_commission_rate : ($seller->sellerLevel ? $seller->sellerLevel->bonus_rate : 0),
                    'has_base_salary' => $has_base_salary,
                ] : null,
                'working_days' => $stats['working_days'] ?? Carbon::parse($seller->created_at)->diffInDays(Carbon::now()) + 1,
                'is_new_seller' => $stats['is_new_seller'] ?? (Carbon::parse($seller->created_at)->diffInDays(Carbon::now()) + 1) <= 30,
                'yesterday_orders' => $stats['yesterday_orders'] ?? 0,
                'daily_orders' => $stats['daily_orders'] ?? 0,
                'weekly_orders' => $stats['weekly_orders'] ?? 0,
                'monthly_orders' => $stats['monthly_orders'] ?? 0,
                'yesterday_products' => $stats['yesterday_products'] ?? 0,
                'daily_products' => $stats['daily_products'] ?? 0,
                'weekly_products' => $stats['weekly_products'] ?? 0,
                'monthly_products' => $stats['monthly_products'] ?? 0,
                'tiktok_payment_today' => $stats['tiktok_payment_today'] ?? 0,
                'tiktok_payment_month' => $stats['tiktok_payment_month'] ?? 0,
                'tiktok_payout_on_hold' => $stats['tiktok_payout_on_hold'] ?? 0,
                'total_fund_requests' => $stats['total_fund_requests'] ?? 0,
                'store_count' => $storeCounts['total'] ?? 0,
                'active_stores' => $storeCounts['active'] ?? 0,
                'pending_stores' => $storeCounts['pending'] ?? 0,
                'suspended_stores' => $storeCounts['suspended'] ?? 0,
                'inactive_stores' => $storeCounts['inactive'] ?? 0,
                'not_synced_stores' => $storeCounts['not_synced'] ?? 0,
            ];
        })->values();
        
        $result = [
            'id' => $team->id,
            'team_name' => $team->name,
            'stats' => $teamStats,
            'leaders' => $leaders->map(function($leader) {
                return [
                    'id' => $leader->id,
                    'name' => $leader->name,
                    'email' => $leader->email,
                    'avatar_url' => $leader->getFilamentAvatarUrl(),
                    'telegram_id' => $leader->telegram_id,
                ];
            })->values(),
            'sellers' => $processedSellers
        ];
        
        // Always include monthly stats if we have sellers
        if (!empty($sellerIds)) {
            $monthlyStats = $this->getMonthlyTeamStats($sellerIds);
            $result['monthly_stats'] = $monthlyStats;
        }

        return $result;
    }

    /**
     * Get basic statistics for an individual seller
     */
    protected function getBasicSellerStats($seller): array
    {
        $now = Carbon::now();
        $startDate = $seller->created_at;
        $monthStart = max($startDate, $now->copy()->startOfMonth());
        $weekStart = max($startDate, $now->copy()->startOfWeek());
        
        $yesterdayStart = $now->copy()->subDay()->startOfDay();
        $yesterdayEnd = $now->copy()->subDay()->endOfDay();
        $dayStart = $now->copy()->startOfDay();
        $dayEnd = $now->copy()->endOfDay();
        $weekEnd = $now->copy()->endOfWeek();
        $monthEnd = $now->copy()->endOfMonth();
        
        // Get order statistics
        $orderStats = Order::where('seller_id', $seller->id)
            ->selectRaw('
                COUNT(*) as total_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as yesterday_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as daily_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as weekly_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as monthly_orders
            ', [
                $yesterdayStart, $yesterdayEnd,
                $dayStart, $dayEnd,
                $weekStart, $weekEnd,
                $monthStart, $monthEnd
            ])
            ->first();
            
        // Get product statistics
        $productStats = Product::where('seller_id', $seller->id)
            ->selectRaw('
                COUNT(*) as total_products,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as yesterday_products,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as daily_products,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as weekly_products,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as monthly_products
            ', [
                $yesterdayStart, $yesterdayEnd,
                $dayStart, $dayEnd,
                $weekStart, $weekEnd,
                $monthStart, $monthEnd
            ])
            ->first();
        
        // Get TikTok payment data
        $tikTokPayoutOnHold = Store::where('owner_id', $seller->id)
            ->sum('tiktok_payout_on_hold');
        
        $tiktokPaymentToday = \App\Models\PayoutTransaction::whereHas('store', function($q) use ($seller) {
            $q->where('owner_id', $seller->id);
        })
        ->where('status', 'Success')
        ->where('type', 'Receive')
        ->whereBetween('time', [$dayStart, $dayEnd])
        ->sum('amount');
        
        $tiktokPaymentMonth = \App\Models\PayoutTransaction::whereHas('store', function($q) use ($seller) {
            $q->where('owner_id', $seller->id);
        })
        ->where('status', 'Success')
        ->where('type', 'Receive')
        ->whereBetween('time', [$monthStart, $monthEnd])
        ->sum('amount');
        
        // Get fund requests
        $fundRequests = DB::table('seller_fund_requests')
            ->where('seller_id', $seller->id)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->sum('amount');
        
        return [
            'yesterday_orders' => $orderStats->yesterday_orders ?? 0,
            'daily_orders' => $orderStats->daily_orders ?? 0,
            'weekly_orders' => $orderStats->weekly_orders ?? 0,
            'monthly_orders' => $orderStats->monthly_orders ?? 0,
            'yesterday_products' => $productStats->yesterday_products ?? 0,
            'daily_products' => $productStats->daily_products ?? 0,
            'weekly_products' => $productStats->weekly_products ?? 0,
            'monthly_products' => $productStats->monthly_products ?? 0,
            'tiktok_payment_today' => $tiktokPaymentToday ?? 0,
            'tiktok_payment_month' => $tiktokPaymentMonth ?? 0,
            'tiktok_payout_on_hold' => $tikTokPayoutOnHold ?? 0,
            'total_fund_requests' => $fundRequests ?? 0,
            'working_days' => Carbon::parse($seller->created_at)->diffInDays(Carbon::now()) + 1,
            'is_new_seller' => Carbon::parse($seller->created_at)->diffInDays(Carbon::now()) + 1 <= 30,
        ];
    }

    /**
     * Get store counts for a seller
     */
    protected function getSellerStoreCounts($sellerId): array
    {
        // Get store counts by status
        $storeStats = Store::where('owner_id', $sellerId)
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN tiktok_shop_status = ? THEN 1 ELSE 0 END) as active,
                SUM(CASE WHEN tiktok_shop_status = ? THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN tiktok_shop_status = ? THEN 1 ELSE 0 END) as suspended,
                SUM(CASE WHEN status = "inactive" THEN 1 ELSE 0 END) as inactive
            ', [
                TiktokShopStatus::Live,
                TiktokShopStatus::NotConnected,
                TiktokShopStatus::Suspended
            ])
            ->first();
            
        // Calculate stores not synced for over 7 days
        $notSyncedThreshold = Carbon::now()->subDays(7);
        $notSyncedStores = Store::where('owner_id', $sellerId)
            ->where('tiktok_shop_status', TiktokShopStatus::Live)
            ->where(function($query) use ($notSyncedThreshold) {
                $query->whereNull('last_sync_tiktok')
                    ->orWhere('last_sync_tiktok', '<', $notSyncedThreshold);
            })
            ->count();
        
        return [
            'total' => $storeStats->total ?? 0,
            'active' => $storeStats->active ?? 0,
            'pending' => $storeStats->pending ?? 0,
            'suspended' => $storeStats->suspended ?? 0,
            'inactive' => $storeStats->inactive ?? 0,
            'not_synced' => $notSyncedStores ?? 0,
        ];
    }

    /**
     * Get basic team statistics
     */
    protected function getBasicTeamStats($sellerIds): array
    {
        if (empty($sellerIds)) {
            return $this->getEmptyStats();
        }
        
        $now = Carbon::now();
        $monthStart = $now->copy()->startOfMonth();
        $monthEnd = $now->copy()->endOfMonth();
        $yesterdayStart = $now->copy()->subDay()->startOfDay();
        $yesterdayEnd = $now->copy()->subDay()->endOfDay();
        $dayStart = $now->copy()->startOfDay();
        $dayEnd = $now->copy()->endOfDay();
        $weekStart = $now->copy()->startOfWeek();
        $weekEnd = $now->copy()->endOfWeek();
        
        // Get order counts
        $orderStats = Order::whereIn('seller_id', $sellerIds)
            ->selectRaw('
                COUNT(*) as total_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as yesterday_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as daily_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as weekly_orders,
                SUM(CASE WHEN created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as monthly_orders
            ', [
                $yesterdayStart, $yesterdayEnd,
                $dayStart, $dayEnd,
                $weekStart, $weekEnd,
                $monthStart, $monthEnd
            ])
            ->first();
        
        // Get TikTok payment data
        $tikTokPayoutOnHold = Store::whereIn('owner_id', $sellerIds)
            ->sum('tiktok_payout_on_hold');
        
        $tiktokPayments = \App\Models\PayoutTransaction::whereHas('store', function($q) use ($sellerIds) {
            $q->whereIn('owner_id', $sellerIds);
        })
        ->where('status', 'Success')
        ->where('type', 'Receive')
        ->whereBetween('time', [$monthStart, $monthEnd])
        ->sum('amount');
        
        // Get supplier order data
        $supplierOrders = DB::table('supplier_orders')
            ->whereIn('seller_id', $sellerIds)
            ->where('status', 'Completed')
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->selectRaw('COUNT(*) as count, SUM(base_cost) as total_cost')
            ->first();
        
        // Get fund requests
        $fundRequests = DB::table('seller_fund_requests')
            ->whereIn('seller_id', $sellerIds)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$monthStart, $monthEnd])
            ->sum('amount');
        
        return [
            'yesterday_orders' => $orderStats->yesterday_orders ?? 0,
            'daily_orders' => $orderStats->daily_orders ?? 0,
            'weekly_orders' => $orderStats->weekly_orders ?? 0,
            'monthly_orders' => $orderStats->monthly_orders ?? 0,
            'tiktok_payments' => $tiktokPayments ?? 0,
            'tiktok_payout_on_hold' => $tikTokPayoutOnHold ?? 0,
            'supplier_orders' => [
                'count' => $supplierOrders->count ?? 0,
                'cost' => $supplierOrders->total_cost ?? 0
            ],
            'fund_requests' => $fundRequests ?? 0
        ];
    }

    /**
     * Get empty statistics structure
     */
    protected function getEmptyStats(): array
    {
        return [
            'yesterday_orders' => 0,
            'daily_orders' => 0,
            'weekly_orders' => 0,
            'monthly_orders' => 0,
            'tiktok_payments' => 0,
            'tiktok_payout_on_hold' => 0,
            'supplier_orders' => [
                'count' => 0,
                'cost' => 0
            ],
            'fund_requests' => 0
        ];
    }

    /**
     * Get monthly team stats
     */
    protected function getMonthlyTeamStats($sellerIds): array
    {
        $now = Carbon::now();
        $result = [];
        
        // Lấy tháng hiện tại làm mốc
        $currentMonth = $now->copy()->startOfMonth();
        
        // Tối ưu: Lấy dữ liệu cho 3 tháng trong một lần query
        $monthRanges = [];
        for ($i = 0; $i < 3; $i++) {
            $monthStart = $currentMonth->copy()->subMonths($i)->startOfMonth();
            $monthEnd = $monthStart->copy()->endOfMonth();
            $monthRanges[] = [
                'start' => $monthStart,
                'end' => $monthEnd,
                'key' => $monthStart->format('Y-m')
            ];
        }

        // Tối ưu: Lấy tất cả dữ liệu TikTok cho 3 tháng trong một lần query
        $tiktokData = \App\Models\PayoutTransaction::whereHas('store', function($q) use ($sellerIds) {
                $q->whereIn('owner_id', $sellerIds);
            })
            ->where('status', 'Success')
            ->where('type', 'Receive')
            ->whereBetween('time', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->select('time', 'amount')
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->time)->format('Y-m');
            });

        // Tối ưu: Lấy tất cả supplier orders cho 3 tháng trong một lần query
        $supplierOrders = \App\Models\SupplierOrder::whereIn('seller_id', $sellerIds)
            ->where('status', 'Completed')
            ->whereBetween('created_at', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->select('created_at', 'base_cost')
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->created_at)->format('Y-m');
            });

        // Tối ưu: Lấy tất cả fund requests cho 3 tháng trong một lần query
        $fundRequests = \App\Models\SellerFundRequest::whereIn('seller_id', $sellerIds)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->select('created_at', 'amount')
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->created_at)->format('Y-m');
            });

        // Tối ưu: Lấy tất cả invoices cho 3 tháng trong một lần query
        $invoices = \App\Models\Invoice::whereIn('user_id', $sellerIds)
            ->whereBetween('billing_month', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->with(['items'])
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->billing_month)->format('Y-m');
            });

        // Tối ưu: Lấy tất cả orders cho 3 tháng trong một lần query
        $orders = \App\Models\SupplierOrder::whereIn('seller_id', $sellerIds)
            ->whereBetween('created_at', [$monthRanges[2]['start'], $monthRanges[0]['end']])
            ->select('created_at', 'status')
            ->get()
            ->groupBy(function($item) {
                return Carbon::parse($item->created_at)->format('Y-m');
            });

        foreach ($monthRanges as $i => $range) {
            $monthKey = $range['key'];
            $monthStart = $range['start'];
            $monthEnd = $range['end'];

            // Tính toán từ dữ liệu đã cache
            $monthTiktokData = $tiktokData->get($monthKey, collect());
            $monthSupplierOrders = $supplierOrders->get($monthKey, collect());
            $monthFundRequests = $fundRequests->get($monthKey, collect());
            $monthInvoices = $invoices->get($monthKey, collect());
            $monthOrders = $orders->get($monthKey, collect());

            $revenue = $monthTiktokData->sum('amount');
            $supplierCost = $monthSupplierOrders->sum('base_cost');
            $fundRequestCost = $monthFundRequests->sum('amount');
            $totalCost = $supplierCost + $fundRequestCost;
            $grossProfit = $revenue - $totalCost;
            $totalSalary = $monthInvoices->sum('total_amount');
            $netProfit = $grossProfit - $totalSalary;

            $result[] = [
                'month' => $i === 0 
                    ? 'Current Month (' . $monthStart->format('F Y') . ')' 
                    : $monthStart->format('F Y'),
                'month_key' => $monthKey,
                'date_range' => $monthStart->format('d/m/Y') . ' - ' . $monthEnd->format('d/m/Y'),
                'is_current' => $i === 0,
                'revenue' => $revenue,
                'total_cost' => $totalCost,
                'supplier_cost' => $supplierCost,
                'fund_requests' => $fundRequestCost,
                'gross_profit' => $grossProfit,
                'gross_profit_status' => $grossProfit >= 0 ? 'profit' : 'loss',
                'net_profit' => $netProfit,
                'net_profit_status' => $netProfit >= 0 ? 'profit' : 'loss',
                'profit' => [
                    'gross' => $grossProfit,
                    'net' => $netProfit,
                    'margin' => $revenue > 0 ? ($netProfit / $revenue * 100) : 0,
                    'net_profit_status' => $netProfit >= 0 ? 'profit' : 'loss'
                ],
                'invoices' => [
                    'total' => $monthInvoices->count(),
                    'paid' => $monthInvoices->where('status', 'paid')->count(),
                    'partial' => $monthInvoices->where('status', 'partial')->count(),
                    'unpaid' => $monthInvoices->whereIn('status', ['pending', 'approved', 'confirmed'])->count(),
                    'total_amount' => $totalSalary,
                    'paid_amount' => $monthInvoices->where('status', 'paid')->sum('total_amount'),
                    'remaining_amount' => $totalSalary - $monthInvoices->where('status', 'paid')->sum('total_amount'),
                ],
                'order_count' => $monthOrders->count(),
                'orders' => [
                    'total' => $monthOrders->count(),
                    'completed' => $monthOrders->where('status', 'Completed')->count(),
                    'cancelled' => $monthOrders->where('status', 'Cancelled')->count(),
                ],
                'kpis' => [
                    'revenue_per_order' => $monthOrders->where('status', 'Completed')->count() > 0 
                        ? ($revenue / $monthOrders->where('status', 'Completed')->count()) 
                        : 0,
                    'cost_per_order' => $monthOrders->where('status', 'Completed')->count() > 0 
                        ? ($totalCost / $monthOrders->where('status', 'Completed')->count()) 
                        : 0,
                    'profit_per_order' => $monthOrders->where('status', 'Completed')->count() > 0 
                        ? ($netProfit / $monthOrders->where('status', 'Completed')->count()) 
                        : 0,
                    'completion_rate' => $monthOrders->count() > 0 
                        ? ($monthOrders->where('status', 'Completed')->count() / $monthOrders->count() * 100) 
                        : 0,
                    'cancellation_rate' => $monthOrders->count() > 0 
                        ? ($monthOrders->where('status', 'Cancelled')->count() / $monthOrders->count() * 100) 
                        : 0
                ]
            ];
        }
        
        return $result;
    }
} 