<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueAttributes implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
    
    }
    public function passes($attribute, $value)
    {
        // Kiểm tra sự trùng lặp
        $attributeIds = array_column($value, 'attribute_id');
        return count($attributeIds) === count(array_unique($attributeIds));
    }

    public function message()
    {
        return 'The attributes must not be duplicated.';
    }
}
