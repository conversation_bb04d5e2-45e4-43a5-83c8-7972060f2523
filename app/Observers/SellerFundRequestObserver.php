<?php

namespace App\Observers;

use App\Models\SellerFundRequest;

class SellerFundRequestObserver
{
    public function updating(SellerFundRequest $fundRequest)
    {
        // Nếu status thay đ<PERSON><PERSON> sang approved hoặc rejected
        if ($fundRequest->isDirty('status') && in_array($fundRequest->status, ['approved', 'rejected'])) {
            $fundRequest->approved_by = auth()->id();
            $fundRequest->approved_at = now();
        }
    }
}