<?php

namespace App\Observers;

use App\Models\SuspendedProduct;
use App\Services\TelegramNotificationService;

class SuspendedProductObserver
{
    protected $telegramService;

    public function __construct(TelegramNotificationService $telegramService)
    {
        $this->telegramService = $telegramService;
    }

    public function created(SuspendedProduct $suspendedProduct)
    {
        $message = $this->telegramService->formatSuspendedProductNotification($suspendedProduct);
        $this->telegramService->sendSuspendedProductNotification($message);
    }
}