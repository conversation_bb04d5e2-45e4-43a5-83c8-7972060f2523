<?php

namespace App\Observers;

use App\Models\Order;
use App\Services\TelegramNotificationService;
use App\Enums\OrderStatus;
use Illuminate\Support\Facades\Log;
use App\Events\OrderCreated;

class OrderObserver
{
    protected $telegramService;

    public function __construct(TelegramNotificationService $telegramService)
    {
        $this->telegramService = $telegramService;
    }

    /**
     * Handle the Order "created" event.
     */
    public function created(Order $order): void
    {
        if (app()->environment('local', 'development')) {
            return;
        }
        
        $message = $this->formatNewOrderNotification($order);
        $this->telegramService->sendNewOrderNotification($message);
        
        // Broadcast event không có toOthers()
        //broadcast(new OrderCreated($order));
    }

    /**
     * Handle the Order "updated" event.
     */
    public function updated(Order $order): void
    {
        // if ($order->isDirty('status')) {
        //     $message = $this->formatOrderStatusChangeNotification($order);
        //     $this->telegramService->sendOrderNotification($message);
        // }
    }

    /**
     * Handle the Order "deleted" event.
     */
    public function deleted(Order $order): void
    {
        if (app()->environment('local', 'development')) {
            return;
        }
        
        $message = $this->formatOrderDeletionNotification($order);
        $this->telegramService->sendOrderNotification($message);
    }

    /**
     * Format notification for new order.
     */
    private function formatNewOrderNotification(Order $order): string
    {
        $storeName = $order->store->name ?? 'Unknown Store';
        

        return "
<b>🛍 New Order Received!</b>

Order Number: <code>{$order->order_number}</code>
Store: {$storeName}
Seller: {$order->store->owner->name}
Total: $" . number_format($order->total, 2) . "

<a href='" . route('filament.app.resources.orders.index', ['activeTab' => 'all', 'tableSearch' => $order->order_number]) . "'>View Order</a>
        ";
    }

    /**
     * Format notification for order status change.
     */
    private function formatOrderStatusChangeNotification(Order $order): string
    {
        $oldStatus = $this->getStatusName($order->getOriginal('status'));
        $newStatus = $this->getStatusName($order->status);

        return "
<b>🔄 Order Status Changed</b>

Order Number: <code>{$order->order_number}</code>
Old Status: <b>{$oldStatus}</b>
New Status: <b>{$newStatus}</b>

<a href='" . route('filament.app.resources.orders.index', ['activeTab' => 'all', 'tableSearch' => $order->order_number]) . "'>View Order</a>
        ";
    }

    /**
     * Format notification for order deletion.
     */
    private function formatOrderDeletionNotification(Order $order): string
    {
        return "
<b>❌ Order Deleted</b>

Order Number: <code>{$order->order_number}</code>
Total: $" . number_format($order->total, 2) . "
        ";
    }

    /**
     * Get the string representation of the OrderStatus enum.
     */
    private function getStatusName($status): string
    {
        if ($status instanceof OrderStatus) {
            return $status->name;
        }

        try {
            return OrderStatus::from($status)->name;
        } catch (\ValueError $e) {
            Log::warning("Invalid OrderStatus value: $status");
            return 'Unknown';
        }
    }
}