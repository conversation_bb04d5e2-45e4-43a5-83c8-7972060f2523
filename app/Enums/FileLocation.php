<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum FileLocation: string implements  HasLabel
{
    case printer_design_front_url = 'printer_design_front_url';
    
    case printer_design_back_url = 'printer_design_back_url';

    case printer_design_sleeve_right_url = 'printer_design_sleeve_right_url';

    case printer_design_sleeve_left_url = 'printer_design_sleeve_left_url';

    case no_select = 'non_selected';



    public function getLabel(): string
    {
        return match ($this) {
            self::printer_design_front_url => 'Design Front',
            self::printer_design_back_url => 'Design Back',
            self::printer_design_sleeve_right_url => 'Design Sleeve Right',
            self::printer_design_sleeve_left_url => 'Design Sleeve Left',
            self::no_select => 'Non-Selected',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::printer_design_front_url => 'info',
            self::printer_design_back_url => 'warning',
            self::printer_design_sleeve_right_url => 'success',
            self::printer_design_sleeve_left_url => 'success',
            self::no_select => 'success',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {


            self::printer_design_front_url => 'heroicon-m-sparkles',
            self::printer_design_back_url => 'heroicon-m-arrow-path',
            self::printer_design_sleeve_right_url => 'heroicon-m-arrow-path',
            self::printer_design_sleeve_left_url => 'heroicon-m-arrow-path',
            self::no_select => 'heroicon-m-arrow-path',

        };
    }

    public function getDatabaseField(): string
    {
        return match($this) {
            self::printer_design_front_url => 'design_front_url',
            self::printer_design_back_url => 'design_back_url',
            self::printer_design_sleeve_right_url => 'sleeve_right_design_url',
            self::printer_design_sleeve_left_url => 'sleeve_left_design_url',
            default => $this->value,
        };
    }
}