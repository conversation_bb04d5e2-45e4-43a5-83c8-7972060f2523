<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum CardResult: string implements HasColor, HasIcon, HasLabel
{
    case PURCHASED = 'purchased';
    case NOT_PURCHASED = 'not_purchased';
    case SMS = 'sms';
    case INSUFFICIENT_FUNDS = 'insufficient_funds';
    case NO_SELECT =''; 

    public function getLabel(): string
    {
        return match ($this) {
            self::PURCHASED => 'Mua Được',
            self::NOT_PURCHASED => 'Không mua được',
            self::SMS => 'SMS',
            self::INSUFFICIENT_FUNDS => 'Không đủ tiền',
            self::NO_SELECT => 'Chưa Sài',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::PURCHASED => 'success',
            self::NOT_PURCHASED => 'danger',
            self::SMS => 'info',
            self::INSUFFICIENT_FUNDS => 'warning',
            self::NO_SELECT => '',
        };
    }
    public function getCss(): string | array | null
    {
        return match ($this) {
            self::PURCHASED => 'select_succes',
            self::NOT_PURCHASED => 'select_red',
            self::SMS => 'select_w',
            self::INSUFFICIENT_FUNDS => 'select_w2',
            self::NO_SELECT => '',
            
        };
    }
    public function getIcon(): ?string
    {
        return match ($this) {
            self::PURCHASED => 'heroicon-o-check-circle',
            self::NOT_PURCHASED => 'heroicon-o-x-circle',
            self::SMS => 'heroicon-o-chat-bubble-oval-left-ellipsis',
            self::INSUFFICIENT_FUNDS => 'heroicon-o-exclamation-circle',
        };
    }
}
