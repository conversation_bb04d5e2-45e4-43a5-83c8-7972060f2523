<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum StoreType: string implements HasColor, HasIcon, HasLabel
{
    case None = 'None';
    case Tiktok = 'Tiktok';
    case Amazon = 'Amazon';
    case Woocommerce = 'Woocommerce';

    public function getLabel(): string
    {
        return match ($this) {
            self::None => 'None',
            self::Tiktok => 'Tiktok',
            self::Amazon => 'Amazon',
            self::Woocommerce => 'Woocommerce',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::None => 'info',
            self::Tiktok => 'warning',
            self::Amazon => 'success',
            self::Woocommerce => 'danger',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::None => 'heroicon-m-sparkles',
            self::Tiktok => 'heroicon-m-arrow-path',
            self::Amazon => 'heroicon-m-truck',
            self::Woocommerce => 'heroicon-m-check-badge',
        };
    }
}