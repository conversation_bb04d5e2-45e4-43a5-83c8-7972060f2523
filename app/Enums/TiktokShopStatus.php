<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum TiktokShopStatus: string implements HasColor, HasIcon, HasLabel
{
    //case New = 'New';
    
    case NotConnected = '-1';

    case Live = '2';

    case Suspended = '3';




    
    public function getLabel(): string
    {
        return match ($this) {
           // self::New => 'New',
            self::NotConnected => 'NotConnected',
            self::Live => 'Live',
            self::Suspended => 'Suspended',
           
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            //self::New => 'info',
            self::NotConnected => 'warning',
            self::Live => 'success',
            self::Suspended => 'danger',
      
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            //self::New => 'heroicon-o-sparkles',
            self::NotConnected => 'heroicon-o-link',
            self::Live => 'heroicon-o-check-circle',
            self::Suspended => 'heroicon-o-x-circle',
        };
    }
}