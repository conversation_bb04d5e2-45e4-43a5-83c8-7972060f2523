<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;
use Mokhosh\FilamentKanban\Concerns\IsKanbanStatus;

enum IdeaStatus: string implements HasColor, HasIcon, HasLabel
{
    use IsKanbanStatus;

    case New = 'new';
    case UnderReview = 'under_review';
    case Approved = 'approved';       // Leader approved
    case InDesign = 'in_design';
    case Completed = 'completed';
    case Rejected = 'rejected';

    public function getTitle(): string
    {
        return $this->getLabel();
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::New => 'New',
            self::UnderReview => 'Under Review',
            self::Approved => 'Approved',
            self::InDesign => 'In Design',
            self::Completed => 'Completed',
            self::Rejected => 'Rejected',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::New => 'blue',
            self::UnderReview => 'amber',
            self::Approved => 'lime',
            self::InDesign => 'purple',
            self::Completed => 'green',
            self::Rejected => 'red',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::New => 'heroicon-o-sparkles',
            self::UnderReview => 'heroicon-o-magnifying-glass',
            self::Approved => 'heroicon-o-check',
            self::InDesign => 'heroicon-o-pencil',
            self::Completed => 'heroicon-o-check-circle',
            self::Rejected => 'heroicon-o-x-circle',
        };
    }

    public function getColumnLabel(): string
    {
        return $this->getLabel();
    }
}