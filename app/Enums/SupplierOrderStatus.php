<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum SupplierOrderStatus: string implements HasColor, HasIcon, HasLabel
{
    //case New = 'New';
    case Pending = 'Pending';

    case AwaitingShipment = 'AwaitingShipment';

    case Completed = 'Completed';

    case Cancelled = 'Cancelled';

    case OnHold = 'OnHold';

    case Refunded = 'Refunded';

    case InProducing = 'InProducing';
    case Draft = 'Draft';
    public function getLabel(): string
    {
        return match ($this) {
           // self::New => 'New',
           self::Draft => 'gray',
           self::Pending => 'Pending',
            self::AwaitingShipment => 'AwaitingShipment',
            self::Completed => 'Completed',
            self::InProducing =>'InProducing',
            self::OnHold => 'On-hold',
            self::Refunded => 'Refunded',
            self::Cancelled => 'Cancelled',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::Draft => 'gray',
            self::Pending => 'info',
            self::AwaitingShipment => 'warning',
            self::Completed => 'success',
            self::InProducing =>'info',
            self::OnHold => 'danger',
            self::Refunded => 'danger',
            self::Cancelled => 'warning',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Draft => 'heroicon-m-sparkles',
            self::Pending => 'heroicon-m-sparkles',
            self::AwaitingShipment => 'heroicon-m-arrow-path',
            self::Completed => 'heroicon-m-truck',
            self::InProducing => 'heroicon-o-printer',
            self::OnHold => 'heroicon-m-x-circle',
            self::Refunded => 'heroicon-m-x-circle',
            self::Cancelled => 'heroicon-m-x-circle',
        };
    }
}