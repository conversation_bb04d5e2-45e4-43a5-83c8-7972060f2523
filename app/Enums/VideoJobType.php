<?php

namespace App\Enums;

enum VideoJobType: string
{
    case PRODUCT_VIDEO = 'product_video';
    case PROMOTIONAL_VIDEO = 'promotional_video';
    case TUTORIAL_VIDEO = 'tutorial_video';
    case UNBOXING_VIDEO = 'unboxing_video';
    case TESTIMONIAL_VIDEO = 'testimonial_video';
    case ANIMATION = 'animation';
    case MOTION_GRAPHICS = 'motion_graphics';
    case VIDEO_EDITING = 'video_editing';
    case SLIDESHOW = 'slideshow';
    case SOCIAL_MEDIA_VIDEO = 'social_media_video';

    public function getLabel(): string
    {
        return match($this) {
            self::PRODUCT_VIDEO => 'Product Video',
            self::PROMOTIONAL_VIDEO => 'Promotional Video',
            self::TUTORIAL_VIDEO => 'Tutorial Video',
            self::UNBOXING_VIDEO => 'Unboxing Video',
            self::TESTIMONIAL_VIDEO => 'Testimonial Video',
            self::ANIMATION => 'Animation',
            self::MOTION_GRAPHICS => 'Motion Graphics',
            self::VIDEO_EDITING => 'Video Editing',
            self::SLIDESHOW => 'Slideshow',
            self::SOCIAL_MEDIA_VIDEO => 'Social Media Video',
        };
    }

    public function getBasePrice(): float
    {
        return match($this) {
            self::PRODUCT_VIDEO => 15.00,
            self::PROMOTIONAL_VIDEO => 25.00,
            self::TUTORIAL_VIDEO => 30.00,
            self::UNBOXING_VIDEO => 12.00,
            self::TESTIMONIAL_VIDEO => 18.00,
            self::ANIMATION => 40.00,
            self::MOTION_GRAPHICS => 35.00,
            self::VIDEO_EDITING => 20.00,
            self::SLIDESHOW => 10.00,
            self::SOCIAL_MEDIA_VIDEO => 15.00,
        };
    }

    public function getEstimatedHours(): int
    {
        return match($this) {
            self::PRODUCT_VIDEO => 4,
            self::PROMOTIONAL_VIDEO => 6,
            self::TUTORIAL_VIDEO => 8,
            self::UNBOXING_VIDEO => 3,
            self::TESTIMONIAL_VIDEO => 4,
            self::ANIMATION => 12,
            self::MOTION_GRAPHICS => 10,
            self::VIDEO_EDITING => 5,
            self::SLIDESHOW => 2,
            self::SOCIAL_MEDIA_VIDEO => 3,
        };
    }

    public function getDescription(): string
    {
        return match($this) {
            self::PRODUCT_VIDEO => 'Video giới thiệu sản phẩm chi tiết',
            self::PROMOTIONAL_VIDEO => 'Video quảng cáo, marketing',
            self::TUTORIAL_VIDEO => 'Video hướng dẫn sử dụng sản phẩm',
            self::UNBOXING_VIDEO => 'Video mở hộp sản phẩm',
            self::TESTIMONIAL_VIDEO => 'Video review, đánh giá từ khách hàng',
            self::ANIMATION => 'Video hoạt hình, animation 2D/3D',
            self::MOTION_GRAPHICS => 'Video motion graphics, hiệu ứng chuyển động',
            self::VIDEO_EDITING => 'Chỉnh sửa video có sẵn',
            self::SLIDESHOW => 'Video slideshow từ hình ảnh',
            self::SOCIAL_MEDIA_VIDEO => 'Video tối ưu cho mạng xã hội',
        };
    }
}
