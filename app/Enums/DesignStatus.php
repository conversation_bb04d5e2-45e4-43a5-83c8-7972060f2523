<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum DesignStatus: string implements HasColor, HasIcon, HasLabel
{
    case Spy = 'Spy';
    case Design = 'Design';
    case Uploaded = 'Uploaded';
    case Cancelled = 'Cancelled';
    case Clone = 'Clone';
    case Archived = 'Archived';

    
    public function getLabel(): string
    {
        return match ($this) {
            self::Spy => 'Spy',
            self::Design => 'Design',
            self::Uploaded => 'Uploaded',
            self::Cancelled => 'Cancelled',
            self::Clone => 'Clone',
            self::Archived => 'Archived',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::Spy => 'info',
            self::Design => 'danger',
            self::Uploaded => 'success',
            self::Cancelled => 'danger',
            self::Clone => 'yellow',
            self::Archived => 'green',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Spy => 'heroicon-m-sparkles',
            self::Design => 'heroicon-m-arrow-path',
            self::Uploaded => 'heroicon-m-truck',
            self::Cancelled => 'heroicon-m-check-badge',
            self::Clone => 'heroicon-m-check-badge',
            self::Archived => 'heroicon-m-archive-box',
        };
    }
}