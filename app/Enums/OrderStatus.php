<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum OrderStatus: string implements HasColor, HasIcon, HasLabel
{
    case Processing = 'Processing';
    case AwaitingShipment = 'AwaitingShipment';
    case Completed = 'Completed';
    case OnHold = 'OnHold';
    case Refunded = 'Refunded';
    case Cancelled = 'Cancelled';
    
    public function getLabel(): string
    {
        return match ($this) {
            self::Processing => 'Processing',
            self::AwaitingShipment => 'Awaiting Shipment',
            self::Completed => 'Completed',
            self::OnHold => 'On Hold',
            self::Refunded => 'Refunded',
            self::Cancelled => 'Cancelled',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::Processing => 'amber', // A strong yellowish color for ongoing status.
            self::AwaitingShipment => 'blue', // Blue to signify "in transit" or shipping.
            self::Completed => 'green', // Green to indicate success/completion.
            self::OnHold => 'orange', // Orange to signify a paused or pending state.
            self::Refunded => 'purple', // Purple as a distinct color for financial actions like refund.
            self::Cancelled => 'red', // Red to indicate a negative or stopped status.
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Processing => 'heroicon-o-arrow-path', // Thay đổi tên biểu tượng cho phù hợp.
            self::AwaitingShipment => 'heroicon-o-truck',
            self::Completed => 'heroicon-o-check-circle',
            self::OnHold => 'heroicon-o-pause-circle',
            self::Refunded => 'heroicon-o-arrow-uturn-left',
            self::Cancelled => 'heroicon-o-x-circle',
        };
    }
}
