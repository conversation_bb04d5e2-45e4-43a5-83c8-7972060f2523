<?php

namespace App\Enums;

enum FeatureRequestCategory: string
{
    case UI_UX = 'ui_ux';
    case FUNCTIONALITY = 'functionality';
    case PERFORMANCE = 'performance';
    case BUG_FIX = 'bug_fix';
    case OTHER = 'other';

    public function getLabel(): string
    {
        return match ($this) {
            self::UI_UX => '<PERSON>iao diện/Trải nghiệm',
            self::FUNCTIONALITY => 'Chức năng',
            self::PERFORMANCE => 'Hiệu suất',
            self::BUG_FIX => 'Sửa lỗi',
            self::OTHER => 'Khác',
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::UI_UX => 'info',
            self::FUNCTIONALITY => 'primary',
            self::PERFORMANCE => 'warning',
            self::BUG_FIX => 'danger',
            self::OTHER => 'gray',
        };
    }

    public static function getOptions(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
            ->toArray();
    }
}
