<?php

namespace App\Enums;

enum MediaRequestStatus: string
{
    case Pending = 'Pending';
    case InProgress = 'In_Progress';
    case Review = 'Review';
    case Completed = 'Completed';
    case Rejected = 'Rejected';

    public function getLabel(): string
    {
        return match($this) {
            self::Pending => 'Chờ xử lý',
            self::InProgress => 'Đang thực hiện',
            self::Review => 'Chờ duyệt',
            self::Completed => 'Hoàn thành',
            self::Rejected => 'Từ chối',
        };
    }

    public function getIcon(): string
    {
        return match($this) {
            self::Pending => 'heroicon-o-clock',
            self::InProgress => 'heroicon-o-arrow-path',
            self::Review => 'heroicon-o-eye',
            self::Completed => 'heroicon-o-check-circle',
            self::Rejected => 'heroicon-o-x-circle',
        };
    }

    public function getColor(): string
    {
        return match($this) {
            self::Pending => 'gray',
            self::InProgress => 'warning',
            self::Review => 'info',
            self::Completed => 'success',
            self::Rejected => 'danger',
        };
    }

    public function getDescription(): string
    {
        return match($this) {
            self::Pending => 'Yêu cầu mới, chờ xử lý',
            self::InProgress => 'Media team đang thực hiện',
            self::Review => 'Chờ seller duyệt video',
            self::Completed => 'Yêu cầu đã hoàn thành',
            self::Rejected => 'Yêu cầu bị từ chối',
        };
    }
} 