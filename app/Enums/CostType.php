<?php

namespace App\Enums;

enum CostType: string
{
    case Fulfillment = 'fulfillment';
    case Ads = 'ads';
    case Production = 'production';
    case DirectDesign = 'direct_design';
    case SharedDesign = 'shared_design';
    case BaseSalary = 'base_salary';

    public function label(): string
    {
        return match($this) {
            self::Fulfillment => 'Fulfillment',
            self::Ads => 'Quảng cáo',
            self::Production => 'Sản xuất', 
            self::DirectDesign => 'Design trực tiếp',
            self::SharedDesign => 'Design chung',
            self::BaseSalary => 'Lương cơ bản',
        };
    }

    public function group(): string 
    {
        return match($this) {
            self::BaseSalary => 'salary',
            default => 'operational'
        };
    }
} 