<?php

namespace App\Enums;

enum ProductType: string
{
    case Hoodie = 'hoodie';
    case TShirt = 't-shirt';
    case Sweatshirt = 'sweatshirt';
    case TankTop = 'tank-top';

    public function getLabel(): string
    {
        return match($this) {
            self::Hoodie => 'Hoodie',
            self::TShirt => 'T-Shirt',
            self::Sweatshirt => 'Sweatshirt',
            self::TankTop => 'Tank Top',
        };
    }

    public function getIcon(): string
    {
        return match($this) {
            self::Hoodie => 'heroicon-o-user',
            self::TShirt => 'heroicon-o-rectangle-stack',
            self::Sweatshirt => 'heroicon-o-squares-2x2',
            self::TankTop => 'heroicon-o-square-2-stack',
        };
    }

    public function getColor(): string
    {
        return match($this) {
            self::Hoodie => 'primary',
            self::TShirt => 'success',
            self::Sweatshirt => 'warning',
            self::TankTop => 'danger',
        };
    }
} 