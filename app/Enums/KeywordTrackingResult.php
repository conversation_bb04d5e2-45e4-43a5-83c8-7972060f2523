<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum KeywordTrackingResult: string implements HasColor, HasIcon, HasLabel
{
    case HasOrders = 'has_orders';
    case NoOrders = 'no_orders';

    public function getLabel(): string
    {
        return match($this) {
            self::HasOrders => 'Has Orders',
            self::NoOrders => 'No Orders',
        };
    }

    public function getColor(): string|array|null
    {
        return match($this) {
            self::HasOrders => 'success',
            self::NoOrders => 'danger',
        };
    }

    public function getIcon(): ?string
    {
        return match($this) {
            self::HasOrders => 'heroicon-o-check',
            self::NoOrders => 'heroicon-o-x-mark',
        };
    }
}