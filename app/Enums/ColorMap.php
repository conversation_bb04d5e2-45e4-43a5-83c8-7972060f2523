<?php

namespace App\Enums;

enum ColorMap: string
{


    case WHITE = '#F5F5F5'; // Slightly off-white color (very light gray)
    case BLACK = '#202425';
    // case ANTIQUE_CHERRY_RED = '#971b2f';
    case ANTIQUE_ROYAL = '#2A4E9A';
    case ASH_GREY = '#D2D2D4';
    // case AZALEA = '#DD74A1';
    // case BLUE_DUSK = '#29465D';
    case CARDINAL_RED = '#8A1538';
    // case CAROLINA_BLUE = '#7BA4DB';
    case CHARCOAL = '#66676C';
    // case CORNSILK = '#f0ec74';
    // case DAISY = '#FFE512';
    // case DARK_CHOCOLATE = '#382F2D';
    case DARK_HEATHER = '#343E40';
    case FOREST_GREEN = '#183028';
    // case GALAPAGOS_BLUE = '#005D6F';
    // case GOLD = '#FFB81C';
    case HEATHER_CARDINAL = '#BA657C';
    case HEATHER_INDIGO = '#6D7F97';
    case HEATHER_NAVY = '#444C59';
    case HEATHER_SAPPHIRE = '#5E9CD5';
    // case HELICONIA = '#E31C79';
    case ICE_GREY = '#D7D2CB';
    // case INDIGO_BLUE = '#486D87';
    // case IRIS = '#3975B7';
    // case IRISH_GREEN = '#00A74A';
    // case JADE_DOME = '#008E85';
    // case KELLY_GREEN = '#007B5F';
    // case KIWI = '#89A84F';
    case LIGHT_BLUE = '#A4C8E1';
    case LIGHT_PINK = '#E4C6D4';
    // case LIME = '#78D64B';
    case MAROON = '#582D40';
    // case METRO_BLUE = '#464E7E';
    case MILITARY_GREEN = '#5E7461';
    // case MINT_GREEN = '#A0CFA8';
    // case NATURAL = '#E7CEB5';
    case NAVY = '#263147';
    // case OLIVE = '#4A412A';
    case ORANGE = '#F4633A';
    // case PFD_WHITE = '#F5F6F8';
    // case PRAIRIE_DUST = '#7A7256';
    case PURPLE = '#464E7E';
    case RED = '#D50032';
    // case ROYAL = '#224D8F';
    // case S_ORANGE = '#E5801C';
    // case SAFETY_GREEN = '#DCF402';
    // case SAFETY_PINK = '#E16F8F';
    case SAND = '#CABFAD';
    // case SAPPHIRE = '#0077B5';
    // case SKY = '#71c5e8';
    case SPORT_GREY = '#B6B7B9';
    // case STONE_BLUE = '#7E93A7';
    // case TAN = '#BD9A7A';
    // case VEGAS_GOLD = '#F4D1A1';
    case ROYAL_BLUE = '#4169E1'; // Giữ nguyên

    public static function fromName(string $name): ?self
    {
        $formattedName = strtoupper(str_replace(' ', '_', trim($name)));

        // Kiểm tra xem tên đã được định dạng đúng chưa
        foreach (self::cases() as $case) {
            if ($case->name === $formattedName) {
                return $case;
            }
        }

        return null;
    }

    public static function toArray(): array
    {
        return array_combine(
            array_map(fn($case) => str_replace('_', ' ', $case->name), self::cases()),
            array_column(self::cases(), 'value')
        );
    }
    public static function getColorData(string $color): array
    {
        $colorEnum = self::fromName($color);
      
        if (!$colorEnum) {
            return ['hex' => '#000000', 'backgroundImage' => null];
        }

        $hex = $colorEnum->value;
        $backgroundImage = self::getBackgroundImage($colorEnum);

        return [
            'hex' => $hex,
            'backgroundImage' => $backgroundImage
        ];
    }
    public static function getBackgroundImage(self $color): ?string
    {
        $backgroundImages = [
            'ANTIQUE_ROYAL' => url('img/color/82-antiqueroyal.jpg'),
            'ASH_GREY' => url('img/color/02-ash-grey.jpg'),
            'DARK_HEATHER' => url('img/color/48-dark-heather.jpg'),
            'HEATHER_CARDINAL' => url('img/color/71-heather-cardinal.jpg'),
            'HEATHER_INDIGO' => url('img/color/46-heather-indigo.jpg'),
            'HEATHER_NAVY' => url('img/color/85-heather-navy.jpg'),
            'HEATHER_SAPPHIRE' => url('img/color/97-heather-sapphire.jpg'),
            'SPORT_GREY' => url('img/color/49-sport-grey.jpg'),
        ];
    
        return $backgroundImages[$color->name] ?? null;
    }

    public static function hasBackgroundImage(self $color): bool
    {
        return self::getBackgroundImage($color) !== null;
    }
    public static function getDarkColors(): array
    {
        return [
            self::BLACK,
            self::NAVY,
            self::FOREST_GREEN,
            self::MAROON,
            self::CHARCOAL,
            self::DARK_HEATHER,
            self::PURPLE,
        ];
    }

    public static function getLightColors(): array
    {
        return [
            self::WHITE,
            self::SPORT_GREY,
            self::SAND,
            self::LIGHT_PINK,
            self::ASH_GREY,
            self::LIGHT_BLUE,
        ];
    }

    public static function getNeutralColors(): array
    {
        return [
            self::RED,
            self::ROYAL_BLUE,
            self::MILITARY_GREEN,
            self::ORANGE,
        ];
    }

    public static function getAllColors(): array
    {
        return self::cases();
    }

    public static function getColorValue(string $colorName): ?string
    {
        $color = self::fromName($colorName);
        return $color ? $color->value : null;
    }

    public static function isValidColor(string $colorName): bool
    {
        return self::fromName($colorName) !== null;
    }
    public static function getValue($name): string
    {
        return constant("self::$name")->value;
    }
}
