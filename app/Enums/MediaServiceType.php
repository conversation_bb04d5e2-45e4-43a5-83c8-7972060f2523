<?php

namespace App\Enums;

enum MediaServiceType: string
{
    case BASIC_PRODUCT_VIDEO = 'basic_product_video';
    case PREMIUM_PRODUCT_VIDEO = 'premium_product_video';
    case SOCIAL_MEDIA_VIDEO = 'social_media_video';
    case PROMOTIONAL_VIDEO = 'promotional_video';
    case SLIDESHOW_VIDEO = 'slideshow_video';

    public function getLabel(): string
    {
        return match($this) {
            self::BASIC_PRODUCT_VIDEO => 'Video sản phẩm cơ bản',
            self::PREMIUM_PRODUCT_VIDEO => 'Video sản phẩm cao cấp',
            self::SOCIAL_MEDIA_VIDEO => 'Video mạng xã hội',
            self::PROMOTIONAL_VIDEO => 'Video quảng cáo',
            self::SLIDESHOW_VIDEO => 'Video slideshow',
        };
    }

    public function getBasePrice(): float
    {
        return match($this) {
            self::BASIC_PRODUCT_VIDEO => 10.00,
            self::PREMIUM_PRODUCT_VIDEO => 20.00,
            self::SOCIAL_MEDIA_VIDEO => 15.00,
            self::PROMOTIONAL_VIDEO => 25.00,
            self::SLIDESHOW_VIDEO => 8.00,
        };
    }

    public function getDescription(): string
    {
        return match($this) {
            self::BASIC_PRODUCT_VIDEO => 'Video giới thiệu sản phẩm đơn giản, thời lượng 30-60s',
            self::PREMIUM_PRODUCT_VIDEO => 'Video sản phẩm chuyên nghiệp với hiệu ứng, thời lượng 60-120s',
            self::SOCIAL_MEDIA_VIDEO => 'Video tối ưu cho TikTok, Instagram, Facebook',
            self::PROMOTIONAL_VIDEO => 'Video quảng cáo marketing với kịch bản phức tạp',
            self::SLIDESHOW_VIDEO => 'Video slideshow từ hình ảnh có sẵn',
        };
    }

    public function getEstimatedHours(): int
    {
        return match($this) {
            self::BASIC_PRODUCT_VIDEO => 2,
            self::PREMIUM_PRODUCT_VIDEO => 4,
            self::SOCIAL_MEDIA_VIDEO => 3,
            self::PROMOTIONAL_VIDEO => 6,
            self::SLIDESHOW_VIDEO => 1,
        };
    }

    public function getIcon(): string
    {
        return match($this) {
            self::BASIC_PRODUCT_VIDEO => 'heroicon-m-cube',
            self::PREMIUM_PRODUCT_VIDEO => 'heroicon-m-star',
            self::SOCIAL_MEDIA_VIDEO => 'heroicon-m-share',
            self::PROMOTIONAL_VIDEO => 'heroicon-m-megaphone',
            self::SLIDESHOW_VIDEO => 'heroicon-m-photo',
        };
    }

    public function getColor(): string
    {
        return match($this) {
            self::BASIC_PRODUCT_VIDEO => 'primary',
            self::PREMIUM_PRODUCT_VIDEO => 'warning',
            self::SOCIAL_MEDIA_VIDEO => 'success',
            self::PROMOTIONAL_VIDEO => 'danger',
            self::SLIDESHOW_VIDEO => 'info',
        };
    }
}
