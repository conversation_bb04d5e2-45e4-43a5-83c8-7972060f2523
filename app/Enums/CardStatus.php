<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum CardStatus: string implements HasColor, HasIcon, HasLabel
{
    case ADD = 'add';
    case NOT_ADD = 'not_add';
    case SMS = 'sms';
    case UNUSED = 'unused'; // Thêm trạng thái "Chưa sài"
    

    public function getLabel(): string
    {
        return match ($this) {
            self::ADD => 'Add Được',
            self::NOT_ADD => 'Không Add Được',
            self::SMS => 'SMS',
            self::UNUSED => 'Chưa sài',
        };
    }
    public function getCss(): string | array | null
    {
        return match ($this) {
            self::ADD => 'select_succes',
            self::NOT_ADD => 'select_red',
            self::SMS => 'select_w',
            self::UNUSED => '',
        };
    }
    public function getColor(): string | array | null
    {
        return match ($this) {
            self::ADD => 'success',
            self::NOT_ADD => 'danger',
            self::SMS => 'info',
            self::UNUSED => 'secondary',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::ADD => 'heroicon-o-check-circle',
            self::NOT_ADD => 'heroicon-o-x-circle',
            self::SMS => 'heroicon-o-chat-bubble-oval-left-ellipsis',
            self::UNUSED => 'heroicon-o-clock',
        };
    }
}
