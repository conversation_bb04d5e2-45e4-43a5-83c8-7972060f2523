<?php

namespace App\Enums;

enum DesignJobType: string
{
    case CLONE = 'clone';
    case BACKGROUND_REMOVE = 'background_remove';
    case MOCKUP = 'mockup';
    case ORIGINAL = 'original';
    case EDIT = 'edit';
    case COLOR_VARIATION = 'color_variation';
    case SIZE_ADJUSTMENT = 'size_adjustment';

    public function getLabel(): string
    {
        return match($this) {
            self::CLONE => 'Clone Design',
            self::BACKGROUND_REMOVE => 'Background Remove',
            self::MOCKUP => 'Create Mockup',
            self::ORIGINAL => 'Original Design',
            self::EDIT => 'Edit Design',
            self::COLOR_VARIATION => 'Color Variation',
            self::SIZE_ADJUSTMENT => 'Size Adjustment'
        };
    }

    public function getBasePrice(): float
    {
        return match($this) {
            self::CLONE => 2.00,
            self::BACKGROUND_REMOVE => 1.00,
            self::MOCKUP => 3.00,
            self::ORIGINAL => 10.00,
            self::EDIT => 5.00,
            self::COLOR_VARIATION => 2.50,
            self::SIZE_ADJUSTMENT => 1.50
        };
    }

    public function getEstimatedHours(): int
    {
        return match($this) {
            self::CLONE => 1,
            self::BACKGROUND_REMOVE => 1,
            self::MOCKUP => 2,
            self::ORIGINAL => 8,
            self::EDIT => 3,
            self::COLOR_VARIATION => 1,
            self::SIZE_ADJUSTMENT => 1
        };
    }
}