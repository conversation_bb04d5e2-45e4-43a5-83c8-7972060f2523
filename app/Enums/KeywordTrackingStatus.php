<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum KeywordTrackingStatus: string implements HasColor, HasIcon, HasLabel
{
    case Pending = 'pending';
    case Listing = 'listing';
    case Listed = 'listed';
    case Failed = 'failed';

    public function getLabel(): string
    {
        return match($this) {
            self::Pending => 'Pending',
            self::Listing => 'Listing',
            self::Listed => 'Listed',
            self::Failed => 'Failed',
        };
    }

    public function getColor(): string|array|null
    {
        return match($this) {
            self::Pending => 'gray',
            self::Listing => 'warning',
            self::Listed => 'success',
            self::Failed => 'danger',
        };
    }

    public function getIcon(): ?string
    {
        return match($this) {
            self::Pending => 'heroicon-o-clock',
            self::Listing => 'heroicon-o-arrow-path',
            self::Listed => 'heroicon-o-check-circle',
            self::Failed => 'heroicon-o-x-circle',
        };
    }
}