<?php

namespace App\Enums;

enum DesignJobStatus: string
{
    case PENDING = 'pending';
    case ASSIGNED = 'assigned';
    case IN_PROGRESS = 'in_progress';
    case UNDER_REVIEW = 'under_review';
    case NEEDS_REVISION = 'needs_revision';
    case COMPLETED = 'completed';
    case CANCELLED = 'cancelled';

    public function getLabel(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::ASSIGNED => 'Assigned',
            self::IN_PROGRESS => 'In Progress',
            self::UNDER_REVIEW => 'Under Review',
            self::NEEDS_REVISION => 'Needs Revision',
            self::COMPLETED => 'Completed',
            self::CANCELLED => 'Cancelled',
        };
    }

    public function getIcon(): string
    {
        return match ($this) {
            self::PENDING => 'heroicon-o-clock',
            self::ASSIGNED => 'heroicon-o-user-plus',
            self::IN_PROGRESS => 'heroicon-o-arrow-path',
            self::UNDER_REVIEW => 'heroicon-o-eye',
            self::NEEDS_REVISION => 'heroicon-o-pencil',
            self::COMPLETED => 'heroicon-o-check-circle',
            self::CANCELLED => 'heroicon-o-x-circle',
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::ASSIGNED => 'info',
            self::IN_PROGRESS => 'primary',
            self::UNDER_REVIEW => 'purple',
            self::NEEDS_REVISION => 'orange',
            self::COMPLETED => 'success',
            self::CANCELLED => 'danger',
        };
    }

    public function canTransitionTo(array|string $roles): array
    {
        // Chuyển đổi string thành array nếu chỉ có 1 role
        $roles = is_string($roles) ? [$roles] : $roles;

    

        // Lấy tất cả các trạng thái có thể chuyển đổi dựa trên các roles
        $availableStatuses = collect($roles)->flatMap(function ($role) {
            return match ($this) {
                self::PENDING => match ($role) {
                    'Seller', 'Fulfillment','super_admin' => [self::ASSIGNED, self::CANCELLED],
                    default => []
                },
                self::ASSIGNED => match ($role) {
                    'Designer','super_admin' => [self::IN_PROGRESS],
                    'Seller', 'Fulfillment','super_admin' => [self::CANCELLED],
                    default => []
                },
                self::IN_PROGRESS => match ($role) {
                    'Designer','super_admin' => [self::UNDER_REVIEW],
                    'Seller', 'Fulfillment','super_admin' => [self::CANCELLED],
                    default => []
                },
                self::UNDER_REVIEW => match ($role) {
                    'Seller', 'Fulfillment','super_admin' => [self::COMPLETED, self::NEEDS_REVISION, self::CANCELLED],
                    default => []
                },
                self::NEEDS_REVISION => match ($role) {
                    'Designer','super_admin' => [self::IN_PROGRESS],
                    'Seller', 'Fulfillment','super_admin' => [self::CANCELLED],
                    default => []
                },
                self::COMPLETED => match ($role) {
                    'Seller', 'Fulfillment','super_admin' => [self::NEEDS_REVISION],
                    default => []
                },
                self::CANCELLED => [],
            };
        })->unique()->values()->toArray();

        return $availableStatuses;
    }
}
