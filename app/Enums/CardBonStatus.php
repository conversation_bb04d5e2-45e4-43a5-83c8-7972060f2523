<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum CardBonStatus: string implements HasColor, HasIcon, HasLabel
{
    case NOT_STARTED = 'not_started';
    case NOT_POSSIBLE = 'not_possible';
    case IN_PROGRESS = 'in_progress';
    case COMPLETED = 'completed';

    public function getLabel(): string
    {
        return match ($this) {
            self::NOT_STARTED => 'Chưa bòn',
            self::NOT_POSSIBLE => 'Không bòn được',
            self::IN_PROGRESS => 'Đang bòn',
            self::COMPLETED => 'Đã bòn xong',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::NOT_STARTED => 'secondary',
            self::NOT_POSSIBLE => 'danger',
            self::IN_PROGRESS => 'warning',
            self::COMPLETED => 'success',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::NOT_STARTED => 'heroicon-o-clock',
            self::NOT_POSSIBLE => 'heroicon-o-x-circle',
            self::IN_PROGRESS => 'heroicon-s-arrow-path',
            self::COMPLETED => 'heroicon-o-check-circle',
        };
    }
}
