<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum OrderItemStatus: string implements HasColor, HasIcon, HasLabel
{
    case New = 'New';

    case PrintedLabel = 'PrintedLabel';

    case InProduction = 'InProduction';

    case HasProduced = 'HasProduced';

    case Packing = 'Packing';

    case Packaged = 'Packaged';

    case Shipped = 'Shipped';

    case Cancelled = 'Cancelled';

    public function getLabel(): string
    {
        return match ($this) {
            self::New => 'New (Mới)',
            self::PrintedLabel => 'PrintedLabel (Đã in Label)',
            self::InProduction => 'InProduction (Đang sản xuất)',
            self::HasProduced => 'HasProduced (Đã sản xuất)',
            self::Packing => 'Packing (Đang đóng gói)',
            self::Packaged => 'Packaged (Đã đóng gói)',
            self::Shipped => 'Shipped (Đã ship)',
            self::Cancelled => 'Cancelled (<PERSON><PERSON><PERSON> đơn)',
   
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::New => 'info',
            self::PrintedLabel => 'warning',
            self::InProduction => 'warning',
            self::HasProduced => 'warning',
            self::Packing => 'primary',
            self::Packaged => 'primary',
            self::Shipped => 'success',
            self::Cancelled => 'danger',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {


            self::New => 'heroicon-m-sparkles',
            self::PrintedLabel => 'heroicon-m-arrow-path',
            self::InProduction => 'heroicon-m-arrow-path',
            self::HasProduced => 'heroicon-m-arrow-path',
            self::Packing => 'heroicon-m-arrow-path',
            self::Packaged => 'heroicon-m-arrow-path',
            self::Shipped => 'heroicon-m-check-badge',
            self::Cancelled => 'heroicon-m-x-circle',

        };
    }
}
