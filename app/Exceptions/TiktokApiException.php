<?php

namespace App\Exceptions;

use Exception;

class TiktokApiException extends Exception
{
    protected $errorCode;
    protected $errorType;
    protected $requestId;
    protected $rawResponse;
    protected $suggestedAction;

    /**
     * Các mã lỗi phổ biến từ TikTok API
     */
    const ERROR_CODES = [
        // Lỗi xác thực
        'UNAUTHORIZED' => 'Không được phép truy cập, token không hợp lệ hoặc đã hết hạn',
        'ACCESS_TOKEN_EXPIRED' => 'Access token đã hết hạn, cần refresh token',
        'REFRESH_TOKEN_EXPIRED' => 'Refresh token đã hết hạn, cần xác thực lại',
        'INVALID_ACCESS' => 'Không có quyền truy cập vào tài nguyên này',
        'PERMISSION_DENIED' => 'Không có quyền thực hiện hành động này',

        // Lỗi tham số
        'INVALID_PARAM' => 'Tham số không hợp lệ',
        'MISSING_PARAM' => 'Thiếu tham số bắt buộc',
        'INVALID_TIMESTAMP' => 'Timestamp không hợp lệ',

        // Lỗi giới hạn
        'RATE_LIMIT_EXCEEDED' => 'Vượt quá giới hạn số lượng request',
        'DAILY_LIMIT_EXCEEDED' => 'Vượt quá giới hạn số lượng request trong ngày',

        // Lỗi dữ liệu
        'RESOURCE_NOT_FOUND' => 'Không tìm thấy tài nguyên',
        'DATA_NOT_FOUND' => 'Không tìm thấy dữ liệu',

        // Lỗi hệ thống
        'SYSTEM_ERROR' => 'Lỗi hệ thống',
        'SERVICE_UNAVAILABLE' => 'Dịch vụ không khả dụng',
        'INTERNAL_ERROR' => 'Lỗi nội bộ',

        // Lỗi shop
        'SHOP_NOT_AUTHORIZED' => 'Shop chưa được xác thực',
        'SHOP_SUSPENDED' => 'Shop đã bị tạm ngưng',
    ];

    /**
     * Các hành động đề xuất cho từng loại lỗi
     */
    const SUGGESTED_ACTIONS = [
        'ACCESS_TOKEN_EXPIRED' => 'Thử refresh token và gọi lại API',
        'REFRESH_TOKEN_EXPIRED' => 'Cần xác thực lại với TikTok Shop',
        'RATE_LIMIT_EXCEEDED' => 'Giảm tần suất gọi API và thử lại sau ít phút',
        'DAILY_LIMIT_EXCEEDED' => 'Đã đạt giới hạn API trong ngày, thử lại vào ngày mai',
        'SHOP_SUSPENDED' => 'Kiểm tra trạng thái shop trên TikTok Shop Seller Center',
    ];

    /**
     * Tạo một exception mới từ response của TikTok API
     *
     * @param array|string $response Response từ TikTok API
     * @param string $defaultMessage Thông báo mặc định nếu không thể phân tích response
     * @return self
     */
    public static function fromResponse($response, $defaultMessage = 'Lỗi không xác định từ TikTok API')
    {
        if (is_string($response)) {
            return new self($response);
        }

        $errorCode = $response['code'] ?? null;
        $errorMessage = $response['message'] ?? $defaultMessage;
        $requestId = $response['request_id'] ?? null;

        // Xác định loại lỗi dựa trên mã lỗi hoặc thông báo
        $errorType = self::determineErrorType($errorCode, $errorMessage);

        // Tạo exception
        $exception = new self($errorMessage);
        $exception->errorCode = $errorCode;
        $exception->errorType = $errorType;
        $exception->requestId = $requestId;
        $exception->rawResponse = $response;
        $exception->suggestedAction = self::SUGGESTED_ACTIONS[$errorType] ?? null;

        return $exception;
    }

    /**
     * Xác định loại lỗi dựa trên mã lỗi và thông báo
     *
     * @param int|null $errorCode
     * @param string $errorMessage
     * @return string
     */
    protected static function determineErrorType($errorCode, $errorMessage)
    {
        // Xác định loại lỗi dựa trên mã lỗi
        if ($errorCode === 401) {
            return 'UNAUTHORIZED';
        }

        if ($errorCode === 429) {
            return 'RATE_LIMIT_EXCEEDED';
        }

        // Xác định loại lỗi dựa trên thông báo
        $errorMessage = strtolower($errorMessage);

        if (strpos($errorMessage, 'access token is expired') !== false ||
            strpos($errorMessage, 'token expired') !== false ||
            strpos($errorMessage, 'expired credentials') !== false) {
            return 'ACCESS_TOKEN_EXPIRED';
        }

        if (strpos($errorMessage, 'refresh token is expired') !== false) {
            return 'REFRESH_TOKEN_EXPIRED';
        }

        if (strpos($errorMessage, 'rate limit') !== false) {
            return 'RATE_LIMIT_EXCEEDED';
        }

        if (strpos($errorMessage, 'permission denied') !== false) {
            return 'PERMISSION_DENIED';
        }

        if (strpos($errorMessage, 'not found') !== false) {
            return 'RESOURCE_NOT_FOUND';
        }

        if (strpos($errorMessage, 'shop is suspended') !== false) {
            return 'SHOP_SUSPENDED';
        }

        return 'SYSTEM_ERROR';
    }

    /**
     * Lấy thông báo lỗi chi tiết
     *
     * @return string
     */
    public function getDetailedMessage()
    {
        $message = $this->getMessage();
        $errorType = $this->errorType;
        $errorDescription = self::ERROR_CODES[$errorType] ?? 'Lỗi không xác định';
        $suggestedAction = $this->suggestedAction;

        $detailedMessage = "Lỗi TikTok API: {$message}\n";
        $detailedMessage .= "Loại lỗi: {$errorType} - {$errorDescription}";

        if ($this->errorCode !== null) {
            $detailedMessage .= "\nMã lỗi: {$this->errorCode}";
        }

        if ($this->requestId !== null) {
            $detailedMessage .= "\nRequest ID: {$this->requestId}";
        }

        if ($suggestedAction !== null) {
            $detailedMessage .= "\nĐề xuất: {$suggestedAction}";
        }

        return $detailedMessage;
    }

    /**
     * Lấy thông báo lỗi ngắn gọn cho command line
     *
     * @return string
     */
    public function getCommandMessage()
    {
        $errorType = $this->errorType;
        $errorDescription = self::ERROR_CODES[$errorType] ?? 'Lỗi không xác định';
        $suggestedAction = $this->suggestedAction;

        $message = "🔴 Lỗi: {$errorDescription}";

        if ($suggestedAction !== null) {
            $message .= " - Đề xuất: {$suggestedAction}";
        }

        return $message;
    }

    /**
     * Kiểm tra xem lỗi có thể thử lại không
     *
     * @return bool
     */
    public function isRetryable()
    {
        $retryableErrors = [
            'ACCESS_TOKEN_EXPIRED',
            'RATE_LIMIT_EXCEEDED',
            'SYSTEM_ERROR',
            'SERVICE_UNAVAILABLE',
            'INTERNAL_ERROR'
        ];

        return in_array($this->errorType, $retryableErrors);
    }
}
