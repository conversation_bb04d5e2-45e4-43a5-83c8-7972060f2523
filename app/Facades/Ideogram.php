<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static array createImage(string $prompt, array $options = [])
 * @method static array login()
 * @method static array submit()
 * @method static array sample(string $prompt, array $options = [])
 * @method static array retrieveRequests(string $requestId)
 * @method static array pollForCompletion(string $requestId, int $maxWaitTime = null)
 * @method static array getUserGallery()
 * @method static array getStatus()
 * @method static \App\Services\IdeogramService setAccount(\App\Models\IdeogramAccount $account)
 * @method static \App\Models\IdeogramAccount|null getCurrentAccount()
 * @method static \Illuminate\Database\Eloquent\Collection getAllAccounts()
 * @method static \Illuminate\Database\Eloquent\Collection getAvailableAccounts()
 * @method static \App\Services\IdeogramService withAccount(int $accountId)
 * @method static \App\Services\IdeogramService withBestAccount()
 * @method static \App\Services\IdeogramService withDefaultAccount()
 * @method static int resetAllDailyUsage()
 * @method static array getUsageStats()
 *
 * @see \App\Services\IdeogramService
 */
class Ideogram extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'ideogram';
    }
}
