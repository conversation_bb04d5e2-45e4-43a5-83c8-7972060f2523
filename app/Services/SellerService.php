<?php

namespace App\Services;

use App\Enums\DesignJobStatus;
use App\Enums\OrderStatus;
use App\Enums\SupplierOrderStatus;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\SupplierOrder;
use App\Models\SellerFundRequest;
use App\Models\Production;
use App\Models\Store;
use App\Models\DesignJob;
use App\Models\TikTokPayment;
use App\Models\PayoutTransaction;
use App\Models\SellerFinance;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * SellerService - Service tính toán tài chính cho Seller
 *
 * =============================================================================
 * TỔNG QUAN CÔNG THỨC TÍNH TOÁN TÀI CHÍNH
 * =============================================================================
 *
 * 1. DOANH THU (REVENUE)
 *    - Nguồn: Orders với status = 'completed' hoặc 'processing'
 *    - Thời gian: Dựa trên orders.created_at
 *    - Tính từ: order_items.total
 *    - Công thức: SUM(order_items.total) WHERE orders.status IN ('completed', 'processing')
 *
 * 2. CHI PHÍ ĐI ĐƠN (FULFILLMENT COST)
 *    - Nguồn: SupplierOrders
 *    - Điều kiện: Loại trừ status = 'cancelled'
 *    - Thời gian: Dựa trên supplier_orders.created_at
 *    - Công thức: SUM(base_cost) WHERE status != 'cancelled'
 *    - Lý do: Chi phí phát sinh khi tạo đơn, không phải khi hoàn thành
 *
 * 3. CHI PHÍ QUẢNG CÁO (ADVERTISING COST)
 *    - Nguồn: SellerFundRequests
 *    - Điều kiện: Chỉ tính status = 'approved'
 *    - Thời gian: Dựa trên fund_requests.created_at
 *    - Công thức: SUM(amount) WHERE status = 'approved'
 *    - Lý do: Chỉ có request được duyệt mới phát sinh chi phí thực tế
 *
 * 4. CHI PHÍ THIẾT KẾ (DESIGN COST)
 *    - Nguồn: DesignJobs (relationship: createdDesignJobs)
 *    - Điều kiện: Chỉ tính status = 'COMPLETED'
 *    - Thời gian: Dựa trên design_jobs.completed_at
 *    - Công thức: SUM(price + rush_fee) WHERE status = 'COMPLETED'
 *    - Lý do: Chi phí chỉ phát sinh khi design job hoàn thành (tránh tính sai cho job bị hủy sau)
 *    - Lưu ý: Sử dụng createdDesignJobs() (created_by) chứ không phải designJobs() (designer_id)
 *
 * 5. CHI PHÍ IN ÁO (PRINT COST)
 *    - Nguồn: Productions
 *    - Điều kiện: Loại trừ status = 'rejected'
 *    - Thời gian: Dựa trên productions.created_at
 *    - Công thức: SUM(calculateProductionCost()) WHERE status != 'rejected'
 *    - Lý do: Tính tất cả status trừ rejected (pending, in_production, completed)
 *
 * 6. THANH TOÁN NGÂN HÀNG (BANK PAYOUT)
 *    - Nguồn: PayoutTransactions
 *    - Điều kiện: type = 'Receive' AND status = 'Success'
 *    - Thời gian: Dựa trên payout_transactions.time
 *    - Liên kết: Qua stores.bank_account = payout_transactions.card_no
 *
 * 7. THANH TOÁN TIKTOK (TIKTOK PAYOUT)
 *    - Nguồn: TikTokPayments
 *    - Điều kiện: status = 'PAID'
 *    - Thời gian: Dựa trên tiktok_payments.paid_time
 *    - Liên kết: Qua stores.id = tiktok_payments.store_id
 *
 * 8. TỔNG CHI PHÍ (TOTAL EXPENSES)
 *    - Công thức: fulfillment_cost + advertising_cost + design_cost + print_cost + platform_fee
 *    - Không bao gồm: base_salary, previous_month_loss (chỉ để hiển thị)
 *
 * 9. LỢI NHUẬN THỰC TẾ (NET PROFIT)
 *    - Công thức: Bank Payout - Total Expenses
 *    - Lý do: Dựa trên tiền thực tế nhận được, không phải doanh thu gộp
 *
 * 10. HOA HỒNG (COMMISSION)
 *     - Công thức: Net Profit * commission_rate / 100
 *     - Điều kiện: Nếu Net Profit < 0 thì commission = 0
 *     - Thu nhập seller: base_salary + commission
 *
 * =============================================================================
 * LƯU Ý QUAN TRỌNG
 * =============================================================================
 *
 * - Relationship designJobs() dành cho designer (designer_id)
 * - Relationship createdDesignJobs() dành cho seller (created_by)
 * - Global Scope có thể ảnh hưởng đến truy vấn dữ liệu
 * - Sử dụng withoutGlobalScopes() nếu cần bỏ qua access control
 * - Tất cả tính toán dựa trên khoảng thời gian startDate - endDate
 *
 * =============================================================================
 */
class SellerService extends BaseFinancialService
{
    /**
     * @var User Seller instance
     */
    protected User $seller;

    /**
     * Constructor
     *
     * @param User $seller
     * @param Carbon $startDate
     * @param Carbon $endDate
     */
    public function __construct(User $seller, Carbon $startDate, Carbon $endDate)
    {
        parent::__construct($startDate, $endDate);
        $this->seller = $seller;
    }

    /**
     * Tính toán doanh thu
     *
     * @return array
     */
    public function calculateRevenue(): array
    {
        // Lấy doanh thu từ đơn hàng hoàn thành và đang xử lý trong khoảng thời gian
        $grossRevenue = $this->getRevenue();

        return [
            'grossRevenue' => $grossRevenue,
        ];
    }

    /**
     * Lấy doanh thu từ database
     * Bao gồm cả đơn hàng Completed và Processing
     *
     * @return float
     */
    public function getRevenue(): float
    {
        return $this->seller->orders()
            ->whereIn('orders.status', [
                OrderStatus::Completed->value,
                OrderStatus::Processing->value
            ])
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate])
            ->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->sum('order_items.total');
    }

    /**
     * Tính toán thông tin đơn hàng
     *
     * @return array
     */
    public function getOrders(): array
    {
        // Tạo query base với điều kiện user_id
        $baseQuery = Order::query()
            ->where('seller_id', $this->seller->id)
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate]);

        // Clone query base để đếm tổng số đơn hàng
        $totalOrders = (clone $baseQuery)->count();

        // Sử dụng một query duy nhất với GROUP BY để lấy số lượng theo trạng thái
        $ordersByStatus = (clone $baseQuery)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Lấy số lượng đơn theo từng trạng thái, mặc định là 0 nếu không có
        $completedOrders = $ordersByStatus[OrderStatus::Completed->value] ?? 0;
        $processingOrders = $ordersByStatus[OrderStatus::Processing->value] ?? 0;
        $cancelledOrders = $ordersByStatus[OrderStatus::Cancelled->value] ?? 0;

        // Tính số đơn ở trạng thái khác (nếu có)
        $otherStatusOrders = $totalOrders - ($completedOrders + $processingOrders + $cancelledOrders);

        // Tính tổng doanh thu từ đơn hàng hoàn thành và đang xử lý
        $totalRevenue = (clone $baseQuery)
            ->whereIn('orders.status', [
                OrderStatus::Completed->value,
                OrderStatus::Processing->value
            ])
            ->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->sum('order_items.total');

        return [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'processing_orders' => $processingOrders,
            'cancelled_orders' => $cancelledOrders,
            'other_status_orders' => $otherStatusOrders,
            'total_revenue' => $totalRevenue,
            'status_breakdown' => $ordersByStatus,
            'period' => [
                'start_date' => $this->startDate->toDateString(),
                'end_date' => $this->endDate->toDateString()
            ],
            // Backward compatibility
            'totalOrders' => $totalOrders,
            'completedOrders' => $completedOrders,
            'processingOrders' => $processingOrders,
            'cancelledOrders' => $cancelledOrders,
            'otherStatusOrders' => $otherStatusOrders,
            'statusBreakdown' => $ordersByStatus
        ];
    }

    /**
     * Tính toán chi phí (không bao gồm baseSalary)
     *
     * @param float $platformFee
     * @param float $feeDesign
     * @param float $baseSalary
     * @param float $previousMonthLoss
     * @return array
     */
    public function calculateExpenses(
        float $platformFee = 0,
        float $feeDesign = 0,
        float $baseSalary = 0,
        float $previousMonthLoss = 0
    ): array {
        // Chi phí đi đơn
        $fulfillmentCost = $this->calculateFulfillment();

        // Chi phí quảng cáo
        $advertisingCost = $this->calculateFund();

        // Chi phí design
        $directDesignCost = $this->calculateFeeDesign($feeDesign);

        //Chi phí in áo
        $printCost = $this->calculatePrintCost();

        // Tổng chi phí (không bao gồm baseSalary và previousMonthLoss)
        $totalExpenses = $fulfillmentCost + $advertisingCost + $directDesignCost + $platformFee + $printCost;

        return [
            'fulfillmentCost' => $fulfillmentCost,
            'advertisingCost' => $advertisingCost,
            'directDesignCost' => $directDesignCost,
            'baseSalary' => $baseSalary, // Vẫn trả về để hiển thị nhưng không tính vào tổng
            'previousMonthLoss' => $previousMonthLoss,
            'platformFee' => $platformFee,
            'printCost' => $printCost,
            'totalExpenses' => $totalExpenses,
        ];
    }

    /**
     * Tính chi phí đi đơn (phiên bản cũ - giữ để backward compatibility)
     *
     * @return float
     */
    public function calculateFulfillment(): float
    {
        $query = $this->seller->supplierOrders()
            ->where('status', '!=', SupplierOrderStatus::Cancelled->value)
            ->whereBetween('created_at', [$this->startDate, $this->endDate]);
        return $query->sum('base_cost');
    }



    /**
     * Lấy dữ liệu doanh thu và chi phí (giữ nguyên logic fulfillment cũ)
     *
     * @return array
     */
    public function getRevenueAndCostConsistency(): array
    {
        $validOrderStatuses = [
            OrderStatus::Completed->value,
            OrderStatus::Processing->value
        ];

        // Doanh thu từ đơn hàng hợp lệ
        $revenue = $this->seller->orders()
            ->whereIn('orders.status', $validOrderStatuses)
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate])
            ->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->sum('order_items.total');

        // Chi phí fulfillment - GIỮ NGUYÊN LOGIC CŨ (chỉ loại bỏ cancelled)
        $fulfillmentCost = $this->calculateFulfillment();

        // Đếm số đơn hàng được match
        $matchedOrdersCount = $this->seller->orders()
            ->whereIn('orders.status', $validOrderStatuses)
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate])
            ->count();

        // Tính các chi phí khác
        $advertisingCost = $this->calculateFund();
        $designCost = $this->calculateFeeDesign();
        $printCost = $this->calculatePrintCost();

        $totalConsistentCost = $fulfillmentCost + $advertisingCost + $designCost + $printCost;

        return [
            'revenue' => $revenue,
            'fulfillment_cost' => $fulfillmentCost,
            'advertising_cost' => $advertisingCost,
            'design_cost' => $designCost,
            'print_cost' => $printCost,
            'total_consistent_cost' => $totalConsistentCost,
            'matched_orders_count' => $matchedOrdersCount,
            'gross_profit_consistent' => $revenue - $totalConsistentCost,
            'valid_order_statuses' => $validOrderStatuses,
            'note' => 'Fulfillment cost sử dụng logic cũ (tất cả supplier orders trừ cancelled)'
        ];
    }

    /**
     * Tính chi phí quảng cáo
     *
     * @return float
     */
    public function calculateFund(): float
    {
        return $this->seller->fundRequests()
            ->where('status', 'approved')
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->sum('amount');
    }

    /**
     * Tính phí thiết kế
     *
     * @param float $manualFeeDesign Phí thiết kế nhập thủ công
     * @return float
     */
  public function calculateFeeDesign(float $manualFeeDesign = 0): float
    {
        // Sử dụng relationship createdDesignJobs() cho seller (thay vì designJobs() dành cho designer)
        // Chỉ tính design jobs đã hoàn thành (COMPLETED) vì chi phí chỉ phát sinh khi hoàn thành
        // Sử dụng completed_at để đảm bảo tính chính xác về thời gian phát sinh chi phí
        return $this->seller->createdDesignJobs()
            ->where('status', DesignJobStatus::COMPLETED)
            ->whereBetween('completed_at', [$this->startDate, $this->endDate])
            ->sum(DB::raw('COALESCE(price, 0) + COALESCE(rush_fee, 0)'));
    }
    /**
     * Tính chi phí in áo
     *
     * @return float
     */
    public function calculatePrintCost(): float
    {
        // Lấy Productions của seller, loại trừ rejected, tính tất cả status khác
        return Production::query()
            ->where('seller_id', $this->seller->id)
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->where('status', '!=', 'rejected') // ← Chỉ loại trừ rejected
            ->with('blank') // ← Eager load để tránh N+1 query
            ->get()
            ->sum(function ($production) {
                return $production->calculateProductionCost();
            });
    }
    /**
     * Tính toán tiền về tài khoản ngân hàng
     *
     * @return array
     */
    public function calculatePayoutTransactions(): array
    {
        $storeIds = $this->seller->stores()->pluck('bank_account')->filter()->toArray();

        if (empty($storeIds)) {
            return [
                'sum' => 0,
                'count' => 0
            ];
        }

        $payoutTransactionsQuery = PayoutTransaction::query()
            ->whereIn('card_no', $storeIds)
            ->where('type', 'Receive')
            ->where('status', 'Success')
            ->whereBetween('time', [$this->startDate, $this->endDate]);

        return [
            'sum' => $payoutTransactionsQuery->sum('amount'),
            'count' => $payoutTransactionsQuery->count(),
        ];
    }

    /**
     * Tính toán tiền về tiktok
     *
     * @return array
     */
    public function calculateTiktokPayout(): array
    {
        $storeIds = $this->seller->stores()->pluck('id')->toArray();

        if (empty($storeIds)) {
            return [
                'sum' => 0,
                'count' => 0
            ];
        }

        $tiktokPayoutQuery = TikTokPayment::query()
            ->whereIn('store_id', $storeIds)
            ->whereBetween('paid_time', [$this->startDate, $this->endDate])
            ->where('status', 'PAID');

        return [
            'sum' => $tiktokPayoutQuery->sum('settlement_amount'),
            'count' => $tiktokPayoutQuery->count(),
        ];
    }

    /**
     * Tính toán số tiền TikTok đang giữ (on hold)
     *
     * @param float $manualOnHold Số tiền on hold nhập thủ công
     * @return float
     */
    public function calculateOnHold(float $manualOnHold = 0): float
    {
        try {
            // Lấy danh sách store IDs của seller
            $storeIds = $this->seller->stores()->pluck('id')->toArray();

            if (empty($storeIds)) {
                return 0;
            }

            // Nếu đã có giá trị nhập tay và khác 0, sử dụng giá trị đó
            if ($manualOnHold > 0) {
                return $manualOnHold;
            }

            // Lấy tổng số tiền tiktok_payout_on_hold từ tất cả stores của seller
            $totalOnHold = Store::whereIn('id', $storeIds)
                ->sum('tiktok_payout_on_hold');

            return $totalOnHold;

        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Lấy dữ liệu thanh toán ngân hàng cho API
     *
     * @return array
     */
    public function getBankPayments(): array
    {
        $payoutData = $this->calculatePayoutTransactions();
        $tiktokData = $this->calculateTiktokPayout();

        return [
            'bank_payout' => [
                'total_amount' => $payoutData['sum'] ?? 0,
                'transaction_count' => $payoutData['count'] ?? 0,
                'currency' => 'USD'
            ],
            'tiktok_payout' => [
                'total_amount' => $tiktokData['sum'] ?? 0,
                'transaction_count' => $tiktokData['count'] ?? 0,
                'currency' => 'USD'
            ],
            'on_hold_amount' => $this->calculateOnHold(),
            'period' => [
                'start_date' => $this->startDate->toDateString(),
                'end_date' => $this->endDate->toDateString()
            ]
        ];
    }

    /**
     * Lấy dữ liệu chi phí cho API
     *
     * @return array
     */
    public function getExpenses(): array
    {
        $expenses = $this->calculateExpenses();

        return [
            'fulfillment_cost' => $expenses['fulfillmentCost'],
            'advertising_cost' => $expenses['advertisingCost'],
            'design_cost' => $expenses['directDesignCost'],
            'print_cost' => $expenses['printCost'],
            'platform_fee' => $expenses['platformFee'],
            'total_expenses' => $expenses['totalExpenses'],
            'breakdown' => [
                'fulfillment' => [
                    'amount' => $expenses['fulfillmentCost'],
                    'description' => 'Chi phí đi đơn (fulfillment)'
                ],
                'advertising' => [
                    'amount' => $expenses['advertisingCost'],
                    'description' => 'Chi phí quảng cáo'
                ],
                'design' => [
                    'amount' => $expenses['directDesignCost'],
                    'description' => 'Chi phí thiết kế'
                ],
                'printing' => [
                    'amount' => $expenses['printCost'],
                    'description' => 'Chi phí in áo'
                ],
                'platform' => [
                    'amount' => $expenses['platformFee'],
                    'description' => 'Phí nền tảng'
                ]
            ],
            'period' => [
                'start_date' => $this->startDate->toDateString(),
                'end_date' => $this->endDate->toDateString()
            ]
        ];
    }

    /**
     * Tính hoa hồng
     *
     * @param float $income Thu nhập
     * @param float $commissionRate Tỷ lệ hoa hồng (%)
     * @param float $baseSalary Lương cơ bản
     * @return array
     */
    public function calculateCommissions(float $income, float $commissionRate, float $baseSalary): array
    {
        // Tính hoa hồng dựa trên tỷ lệ của thu nhập
        $commission = $income * ($commissionRate / 100);

        // Đảm bảo giá trị hợp lệ
        $commission = max(0, $commission); // Không cho phép hoa hồng âm

        // Doanh thu rồng
        $totalIncome = $income;

        // Nếu doanh thu ròng âm -> set hoa hồng = 0
        if ($income < 0) {
            $commission = 0;
        }

        // Thu nhập của seller
        $totalIncomeSeller = $baseSalary + $commission;

        return [
            'commission' => $commission,
            'commissionRate' => $commissionRate,
            'baseSalary' => $baseSalary,
            'totalIncome' => $totalIncome,
            'totalIncomeSeller' => $totalIncomeSeller,
            'isNegativeIncome' => $income < 0, // Flag để biết có cần reset commission rate không
        ];
    }

    /**
     * Lấy lỗ từ tháng trước
     *
     * @param string $dateRange Khoảng thời gian hiện tại (format: 'd/m/Y - d/m/Y')
     * @return float
     */
    public function getPreviousMonthLoss(string $dateRange): float
    {
        // Tìm báo cáo của tháng trước
        $currentDate = Carbon::createFromFormat('d/m/Y', explode(' - ', $dateRange)[0]);
        $previousMonth = $currentDate->copy()->subMonth();

        // Tìm báo cáo của tháng trước
        $previousReport = SellerFinance::where('seller_id', $this->seller->id)
            ->whereYear('month', $previousMonth->year)
            ->whereMonth('month', $previousMonth->month)
            ->first();

        // dd($previousReport->adjusted_profit);

        if ($previousReport) {
            return $previousReport->adjusted_profit > 0 ? 0 : -$previousReport->adjusted_profit;
        }

        return 0.0;
    }

    /**
     * Tính toán các loại profit margin khác nhau
     *
     * @param array $params Tham số tính toán
     * @return array
     */
    public function getProfitMargins(array $params = []): array
    {
        $consistency = $this->getRevenueAndCostConsistency();
        $revenue = $consistency['revenue'];
        $totalCost = $consistency['total_consistent_cost'];

        // Lấy bank payout để tính net margin
        $bankPayment = $this->calculatePayoutTransactions();
        $actualPayout = $bankPayment['sum'] ?? 0;

        // Lấy thông tin lương và platform fee
        $baseSalary = $params['baseSalary'] ?? 0;
        $platformFee = $params['platformFee'] ?? 0;

        // Gross Margin (dựa trên doanh thu gộp)
        $grossProfit = $revenue - $totalCost;
        $grossMargin = $revenue > 0 ? ($grossProfit / $revenue * 100) : 0;

        // Net Margin (dựa trên actual payout)
        $netProfit = $actualPayout - $totalCost;
        $netMargin = $actualPayout > 0 ? ($netProfit / $actualPayout * 100) : 0;

        // Operating Margin (loại trừ lương và platform fee)
        $operatingCost = $totalCost + $baseSalary + $platformFee;
        $operatingProfit = $actualPayout - $operatingCost;
        $operatingMargin = $actualPayout > 0 ? ($operatingProfit / $actualPayout * 100) : 0;

        // Contribution Margin (chỉ tính chi phí biến đổi)
        $variableCosts = $consistency['fulfillment_cost'] + $consistency['print_cost'];
        $contributionProfit = $revenue - $variableCosts;
        $contributionMargin = $revenue > 0 ? ($contributionProfit / $revenue * 100) : 0;

        return [
            'gross_margin' => round($grossMargin, 2),
            'net_margin' => round($netMargin, 2),
            'operating_margin' => round($operatingMargin, 2),
            'contribution_margin' => round($contributionMargin, 2),
            'breakdown' => [
                'gross' => [
                    'profit' => $grossProfit,
                    'margin' => round($grossMargin, 2),
                    'description' => 'Doanh thu - Tổng chi phí'
                ],
                'net' => [
                    'profit' => $netProfit,
                    'margin' => round($netMargin, 2),
                    'description' => 'Bank Payout - Tổng chi phí'
                ],
                'operating' => [
                    'profit' => $operatingProfit,
                    'margin' => round($operatingMargin, 2),
                    'description' => 'Bank Payout - (Chi phí + Lương + Platform Fee)'
                ],
                'contribution' => [
                    'profit' => $contributionProfit,
                    'margin' => round($contributionMargin, 2),
                    'description' => 'Doanh thu - Chi phí biến đổi'
                ]
            ],
            'margin_trend' => $this->getMarginTrend()
        ];
    }

    /**
     * Lấy xu hướng margin so với tháng trước
     *
     * @return array
     */
    private function getMarginTrend(): array
    {
        try {
            $previousMonth = $this->startDate->copy()->subMonth();
            $previousMonthStart = $previousMonth->startOfMonth();
            $previousMonthEnd = $previousMonth->endOfMonth();

            // Tạo service cho tháng trước
            $previousService = new static($this->seller, $previousMonthStart, $previousMonthEnd);
            $previousMargins = $previousService->getProfitMargins();

            // So sánh với tháng hiện tại
            $currentMargins = $this->getProfitMargins();

            return [
                'gross_margin_change' => $currentMargins['gross_margin'] - $previousMargins['gross_margin'],
                'net_margin_change' => $currentMargins['net_margin'] - $previousMargins['net_margin'],
                'trend_direction' => $this->getTrendDirection($currentMargins['gross_margin'], $previousMargins['gross_margin'])
            ];
        } catch (\Exception $e) {
            return [
                'gross_margin_change' => 0,
                'net_margin_change' => 0,
                'trend_direction' => 'stable',
                'error' => 'Không thể tính xu hướng: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Xác định hướng xu hướng
     *
     * @param float $current
     * @param float $previous
     * @return string
     */
    private function getTrendDirection(float $current, float $previous): string
    {
        $difference = $current - $previous;

        if (abs($difference) < 1) {
            return 'stable';
        }

        return $difference > 0 ? 'improving' : 'declining';
    }

    /**
     * Kiểm tra tính hợp lý của dữ liệu tài chính
     *
     * @param array $params Tham số tính toán
     * @return array
     */
    public function validateFinancialData(array $params = []): array
    {
        $errors = [];
        $warnings = [];
        $info = [];

        // Lấy dữ liệu để kiểm tra
        $consistency = $this->getRevenueAndCostConsistency();
        $bankPayment = $this->calculatePayoutTransactions();
        $margins = $this->getProfitMargins($params);

        // 1. Kiểm tra doanh thu và chi phí (lưu ý: fulfillment cost không hoàn toàn nhất quán với revenue)
        if ($consistency['revenue'] > 0 && $consistency['fulfillment_cost'] == 0) {
            $info[] = 'Có doanh thu (' . number_format($consistency['revenue'], 2) . ') nhưng không có chi phí fulfillment - có thể do chưa có supplier orders';
        }

        if ($consistency['revenue'] == 0 && $consistency['fulfillment_cost'] > 0) {
            $info[] = 'Có chi phí fulfillment (' . number_format($consistency['fulfillment_cost'], 2) . ') nhưng không có doanh thu - có thể do orders chưa completed/processing';
        }

        // 2. Kiểm tra cash flow
        $netCashFlow = $bankPayment['sum'] - $consistency['total_consistent_cost'];
        if ($netCashFlow < 0) {
            $warnings[] = 'Cash flow âm (' . number_format($netCashFlow, 2) . ') - chi phí vượt quá thu nhập';
        }

        // 3. Kiểm tra conversion ratio (Bank Payout vs Revenue)
        $conversionRatio = $consistency['revenue'] > 0 ? ($bankPayment['sum'] / $consistency['revenue'] * 100) : 0;
        if ($conversionRatio < 50 && $consistency['revenue'] > 0) {
            $warnings[] = 'Tỷ lệ chuyển đổi tiền mặt thấp (' . round($conversionRatio, 1) . '%) - cần kiểm tra payment processing';
        }

        if ($conversionRatio > 120) {
            $warnings[] = 'Tỷ lệ chuyển đổi tiền mặt cao bất thường (' . round($conversionRatio, 1) . '%) - có thể có sai sót dữ liệu';
        }

        // 4. Kiểm tra margin
        if ($margins['gross_margin'] < 0) {
            $errors[] = 'Gross margin âm (' . $margins['gross_margin'] . '%) - chi phí vượt quá doanh thu';
        }

        if ($margins['gross_margin'] > 0 && $margins['gross_margin'] < 10) {
            $warnings[] = 'Gross margin thấp (' . $margins['gross_margin'] . '%) - cần tối ưu hóa chi phí';
        }

        // 5. Kiểm tra số lượng đơn hàng
        if ($consistency['matched_orders_count'] == 0) {
            $info[] = 'Không có đơn hàng nào trong khoảng thời gian này';
        }

        // 6. Kiểm tra tỷ lệ chi phí
        $costRatios = $this->analyzeCostRatios($consistency);
        if ($costRatios['fulfillment_ratio'] > 70) {
            $warnings[] = 'Chi phí fulfillment chiếm tỷ lệ cao (' . round($costRatios['fulfillment_ratio'], 1) . '%) trong tổng chi phí';
        }

        if ($costRatios['advertising_ratio'] > 30) {
            $warnings[] = 'Chi phí quảng cáo chiếm tỷ lệ cao (' . round($costRatios['advertising_ratio'], 1) . '%) trong tổng chi phí';
        }

        // 7. Kiểm tra xu hướng
        if (isset($margins['margin_trend']['trend_direction']) && $margins['margin_trend']['trend_direction'] === 'declining') {
            $warnings[] = 'Xu hướng margin đang giảm so với tháng trước';
        }

        return [
            'is_valid' => empty($errors),
            'has_warnings' => !empty($warnings),
            'errors' => $errors,
            'warnings' => $warnings,
            'info' => $info,
            'summary' => [
                'total_issues' => count($errors) + count($warnings),
                'error_count' => count($errors),
                'warning_count' => count($warnings),
                'info_count' => count($info)
            ],
            'metrics' => [
                'conversion_ratio' => round($conversionRatio, 2),
                'net_cash_flow' => $netCashFlow,
                'cost_ratios' => $costRatios
            ]
        ];
    }

    /**
     * Phân tích tỷ lệ các loại chi phí
     *
     * @param array $consistency
     * @return array
     */
    private function analyzeCostRatios(array $consistency): array
    {
        $totalCost = $consistency['total_consistent_cost'];

        if ($totalCost == 0) {
            return [
                'fulfillment_ratio' => 0,
                'advertising_ratio' => 0,
                'design_ratio' => 0,
                'print_ratio' => 0
            ];
        }

        return [
            'fulfillment_ratio' => ($consistency['fulfillment_cost'] / $totalCost) * 100,
            'advertising_ratio' => ($consistency['advertising_cost'] / $totalCost) * 100,
            'design_ratio' => ($consistency['design_cost'] / $totalCost) * 100,
            'print_ratio' => ($consistency['print_cost'] / $totalCost) * 100
        ];
    }

    /**
     * Tính toán tất cả dữ liệu tài chính cho seller
     *
     * Công thức tính lãi thực tế: Bank Payout - Tổng chi phí
     *
     * @param array $params Các tham số bổ sung
     * @return array
     */
    public function calculateFinancialData(array $params = []): array
    {
        // Lấy các tham số
        $platformFee = $params['platformFee'] ?? 0;
        $feeDesign = $params['feeDesign'] ?? 0;
        $onHold = $params['onHold'] ?? 0;
        $commissionRate = $params['commission'] ?? 0; // Tỷ lệ hoa hồng từ form
        $baseSalary = $params['baseSalary'] ?? 0;
        $previousMonthLoss = $params['previousMonthLoss'] ?? 0;
        $dateRange = $params['dateRange'] ?? '';


        // Tính toán chi phí
        $expenses = $this->calculateExpenses($platformFee, $feeDesign, $baseSalary, $previousMonthLoss);

        // Tính toán thanh toán ngân hàng
        $bankPayment = $this->calculatePayoutTransactions();
        if (!is_array($bankPayment)) {
            $bankPayment = ['sum' => 0, 'count' => 0];
        }

        // Tính toán thanh toán TikTok
        $tiktokPayout = $this->calculateTiktokPayout();
        if (!is_array($tiktokPayout)) {
            $tiktokPayout = ['sum' => 0, 'count' => 0];
        }

        // Tính toán số tiền on hold
        $onHoldAmount = $this->calculateOnHold($onHold);

        // Tính toán doanh thu từ đơn hàng
        $revenue = $this->calculateRevenue();

        // Gross Profit = Doanh thu gộp - Tổng chi phí
        $grossProfit = $revenue['grossRevenue'] - $expenses['totalExpenses'];

        // Net Profit = Gross Profit - Lỗ tháng trước
        $netProfit = $grossProfit - $previousMonthLoss;

        // Lãi trước thu nhập seller = Bank Payout - Tổng chi phí - lỗ tháng trước ( nếu có )
        $realProfitBeforeSellerIncome = $bankPayment['sum'] - $expenses['totalExpenses'] - $previousMonthLoss;

        // Lãi trước hoa hồng = Bank Payout - Tổng chi phí - Lương cơ bản
        $profitBeforeCommission = $realProfitBeforeSellerIncome - $expenses['baseSalary'];

        // Tính hoa hồng (nếu lãi trước hoa hồng > 0)
        $commissionData = $this->calculateCommission($profitBeforeCommission, $commissionRate);

        // Tổng thu nhập seller = Lương cơ bản + Hoa hồng
        $totalSellerIncome = $expenses['baseSalary'] + $commissionData['commission'];

        // Lãi thực tế cuối cùng = Lãi trước thu nhập seller - Tổng thu nhập seller tức là  (Bank Payout - Chi phí - Lỗ tháng trước - Thu nhập seller)
        $finalRealProfit = $realProfitBeforeSellerIncome - $totalSellerIncome;

        // Tính toán biên độ lợi nhuận dựa trên doanh thu
        $totalProductionCostForMargin = $expenses['fulfillmentCost'] + $expenses['advertisingCost'] + $expenses['directDesignCost'] + $expenses['printCost'];
        $profitForMargin = $revenue['grossRevenue'] - $totalProductionCostForMargin;

        // Fix division by zero error
        $profitMargin = $revenue['grossRevenue'] > 0 ? ($profitForMargin / $revenue['grossRevenue'] * 100) : 0;

        // Tính thu nhập = payment bank - tổng chi phí
        $income = $bankPayment['sum'] - $expenses['totalExpenses'];

        return [
            'revenue' => [
                'grossRevenue' => $revenue['grossRevenue'],
                'platformFee' => $platformFee,
                'netRevenue' => $revenue['grossRevenue'] - $platformFee,
                'onHold' => $onHoldAmount,
            ],
            'expenses' => $expenses,
            'orders' => $this->getOrders(),
            'bankPayments' => $bankPayment,
            'tiktokPayout' => $tiktokPayout,
            'profit' => [
                'grossProfit' => $grossProfit, // Gross Profit = Doanh thu gộp - Tổng chi phí
                'netProfit' => $netProfit, // Net Profit = Gross Profit - Lỗ tháng trước
                'profitBeforeCommission' => $profitBeforeCommission, // Lãi trước hoa hồng = Bank Payout - Tổng chi phí - Lương cơ bản
                'realProfitBeforeSellerIncome' => $realProfitBeforeSellerIncome, // Lãi trước thu nhập seller = Bank Payout - Tổng chi phí - Lỗ tháng trước
                'totalSellerIncome' => $totalSellerIncome, // Tổng thu nhập seller = Lương cơ bản + Hoa hồng
                'finalRealProfit' => $finalRealProfit, // Lãi thực tế cuối cùng = Lãi trước thu nhập seller - Tổng thu nhập seller

                'profitMargin' => is_finite($profitMargin) ? $profitMargin : 0,
                'previousMonthLoss' => $previousMonthLoss,
                'adjustedProfit' => $finalRealProfit,

                'canEditCommission' => $profitBeforeCommission > 0, // Cho phép chỉnh sửa hoa hồng nếu lãi trước hoa hồng > 0
                'commission' => $commissionData['commission'], // Thêm dữ liệu commission
                'commissionRate' => $commissionData['commissionRate'] ?? 0, // Thêm tỷ lệ hoa hồng
            ],
            'income' => $income,
        ];
    }

    /**
     * Tính toán dữ liệu tài chính cải tiến với tính nhất quán và validation
     *
     * @param array $params Các tham số bổ sung
     * @return array
     */
    public function calculateEnhancedFinancialData(array $params = []): array
    {
        // Lấy dữ liệu cơ bản
        $baseData = $this->calculateFinancialData($params);

        // Thêm dữ liệu cải tiến
        $consistency = $this->getRevenueAndCostConsistency();
        $margins = $this->getProfitMargins($params);
        $validation = $this->validateFinancialData($params);

        // Merge tất cả dữ liệu
        return array_merge($baseData, [
            'consistency' => $consistency,
            'enhanced_margins' => $margins,
            'validation' => $validation,
            'improvements' => [
                'consistent_revenue' => $consistency['revenue'],
                'consistent_fulfillment_cost' => $consistency['fulfillment_cost'],
                'consistent_gross_profit' => $consistency['gross_profit_consistent'],
                'matched_orders_count' => $consistency['matched_orders_count']
            ],
            'recommendations' => $this->getRecommendations($validation, $margins)
        ]);
    }

    /**
     * Đưa ra khuyến nghị dựa trên validation và margins
     *
     * @param array $validation
     * @param array $margins
     * @return array
     */
    private function getRecommendations(array $validation, array $margins): array
    {
        $recommendations = [];

        // Khuyến nghị dựa trên margin
        if ($margins['gross_margin'] < 20) {
            $recommendations[] = [
                'type' => 'cost_optimization',
                'priority' => 'high',
                'message' => 'Cần tối ưu hóa chi phí để cải thiện gross margin',
                'action' => 'Xem xét giảm chi phí fulfillment hoặc tăng giá bán'
            ];
        }

        if ($margins['net_margin'] < 10) {
            $recommendations[] = [
                'type' => 'cash_flow',
                'priority' => 'medium',
                'message' => 'Net margin thấp, cần cải thiện cash flow',
                'action' => 'Tối ưu hóa payment processing và giảm chi phí vận hành'
            ];
        }

        // Khuyến nghị dựa trên validation
        if (isset($validation['metrics']['conversion_ratio']) && $validation['metrics']['conversion_ratio'] < 70) {
            $recommendations[] = [
                'type' => 'payment_processing',
                'priority' => 'high',
                'message' => 'Tỷ lệ chuyển đổi payment thấp',
                'action' => 'Kiểm tra và cải thiện quy trình thanh toán'
            ];
        }

        // Khuyến nghị dựa trên xu hướng
        if (isset($margins['margin_trend']['trend_direction']) && $margins['margin_trend']['trend_direction'] === 'declining') {
            $recommendations[] = [
                'type' => 'trend_analysis',
                'priority' => 'medium',
                'message' => 'Xu hướng margin đang giảm',
                'action' => 'Phân tích nguyên nhân và điều chỉnh chiến lược'
            ];
        }

        return $recommendations;
    }

    /**
     * Tính hoa hồng dựa trên lãi trước hoa hồng
     * Chỉ tính hoa hồng nếu (Bank Payout - Tổng chi phí - Lương cơ bản) > 0
     *
     * @param float $profitBeforeCommission Lãi trước hoa hồng (Bank Payout - Tổng chi phí - Lương cơ bản)
     * @param float $commissionRate Tỷ lệ hoa hồng từ form (%)
     * @return array
     */
    private function calculateCommission(float $profitBeforeCommission, float $commissionRate = 0): array
    {
        if ($profitBeforeCommission <= 0) {
            return [
                'commission' => 0,
                'commissionRate' => 0,
                'canEdit' => false,
                'reason' => 'Bank Payout - Tổng chi phí - Lương cơ bản phải > 0 để có hoa hồng'
            ];
        }

        // Chuyển đổi tỷ lệ % thành decimal (ví dụ: 10% -> 0.1)
        $commissionRateDecimal = $commissionRate / 100;
        $commission = $profitBeforeCommission * $commissionRateDecimal;

        return [
            'commission' => $commission,
            'commissionRate' => $commissionRateDecimal,
            'canEdit' => true,
            'maxCommission' => $profitBeforeCommission, // Hoa hồng tối đa = lãi trước hoa hồng
            'reason' => 'Có thể chỉnh sửa hoa hồng'
        ];
    }

    /**
     * Format chi tiết chi phí thành HTML để hiển thị trong table
     *
     * @param mixed $costs Dữ liệu chi phí (có thể là string JSON hoặc array)
     * @return \Illuminate\Support\HtmlString
     */
    public static function formatCostDetails($costs): \Illuminate\Support\HtmlString
    {
        // Xử lý costs có thể là string JSON hoặc array
        if (is_string($costs)) {
            $costs = json_decode($costs, true) ?? [];
        } elseif (!is_array($costs)) {
            $costs = [];
        }

        $details = [];

        // Lấy tất cả các trường có trong costs và hiển thị
        foreach ($costs as $key => $value) {
            // Chỉ xử lý nếu value là số và > 0
            if (is_numeric($value) && $value > 0) {
                $details[] = ucfirst(str_replace('_', ' ', $key)) . ': $' . number_format((float)$value, 2);
            }
        }

        $htmlContent = empty($details) ? 'Không có chi tiết' : implode('<br>', $details);

        return new \Illuminate\Support\HtmlString($htmlContent);
    }

    /**
     * Xử lý và lấy Bank Payout từ commission_details của SellerFinance record
     *
     * @param SellerFinance $record
     * @return float
     */
    public static function getBankPayoutFromRecord(SellerFinance $record): float
    {
        try {
            // Xử lý commission_details có thể là string JSON hoặc array
            $commissionDetails = $record->commission_details;

            if (is_string($commissionDetails)) {
                $commissionDetails = json_decode($commissionDetails, true) ?? [];
            } elseif (!is_array($commissionDetails)) {
                $commissionDetails = [];
            }

            $bankPayoutFromRecord = $commissionDetails['bank_payments']['sum'] ?? 0;

            // Nếu bank payout bằng 0, tính toán từ SellerService
            if ($bankPayoutFromRecord == 0) {
                return static::calculateBankPayoutForRecord($record);
            }

            return $bankPayoutFromRecord;

        } catch (\Exception $e) {
            \Log::error('Error getting bank payout from record: ' . $e->getMessage(), [
                'record_id' => $record->id,
                'seller_id' => $record->seller_id,
                'month' => $record->month
            ]);
            return 0;
        }
    }

    /**
     * Tính toán Bank Payout cho một record cụ thể từ SellerService
     *
     * @param SellerFinance $record
     * @return float
     */
    public static function calculateBankPayoutForRecord(SellerFinance $record): float
    {
        try {
            // Tạo SellerService cho tháng của record
            $monthStart = Carbon::parse($record->month)->startOfMonth()->startOfDay();
            $monthEnd = Carbon::parse($record->month)->endOfMonth()->endOfDay();

            $seller = User::find($record->seller_id);
            if (!$seller) {
                return 0;
            }

            $sellerService = new static($seller, $monthStart, $monthEnd);

            // Tính toán bank payout từ SellerService
            $bankPayments = $sellerService->calculatePayoutTransactions();

            return $bankPayments['sum'] ?? 0;

        } catch (\Exception $e) {
            \Log::error('Error calculating bank payout for record: ' . $e->getMessage(), [
                'record_id' => $record->id,
                'seller_id' => $record->seller_id,
                'month' => $record->month
            ]);
            return 0;
        }
    }

    /**
     * Dự tính tổng tiền chưa về của seller
     * Công thức: Tổng Order - Tổng Bank Payout
     * Lấy tất cả dữ liệu không theo bộ lọc thời gian
     *
     * @return float
     */
    public function calculatePendingRevenue(): float
    {
        try {
            // Tính tổng doanh thu từ tất cả orders (Completed + Processing) - KHÔNG theo thời gian
            $totalOrderRevenue = $this->seller->orders()
                ->whereIn('orders.status', [
                    OrderStatus::Completed->value,
                    OrderStatus::Processing->value
                ])
                ->join('order_items', 'orders.id', '=', 'order_items.order_id')
                ->sum('order_items.total');

            // Tính tổng bank payout từ tất cả thời gian - KHÔNG theo thời gian
            $storeIds = $this->seller->stores()->pluck('bank_account')->filter()->toArray();

            $totalBankPayout = 0;
            if (!empty($storeIds)) {
                $totalBankPayout = PayoutTransaction::query()
                    ->whereIn('card_no', $storeIds)
                    ->where('type', 'Receive')
                    ->where('status', 'Success')
                    ->sum('amount');
            }

            // Tiền chưa về = Tổng Order - Tổng Bank
            return $totalOrderRevenue - $totalBankPayout;

        } catch (\Exception $e) {
            \Log::error('Error calculating pending revenue: ' . $e->getMessage(), [
                'seller_id' => $this->seller->id,
                'seller_name' => $this->seller->name ?? 'Unknown'
            ]);

            return 0;
        }
    }
}
