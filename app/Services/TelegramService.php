<?php

namespace App\Services;

use App\Models\User;
use App\Models\TelegramSend;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class TelegramService
{
    protected function getBotManagementService(): TelegramBotManagementService
    {
        return app(TelegramBotManagementService::class);
    }

    /**
     * Gửi tin nhắn Telegram đến user - Ưu tiên sử dụng bot chung
     */
    public function sendMessage(User $sender, User $recipient, string $message): TelegramSend
    {
        // Ưu tiên sử dụng bot management service (bot chung)
        $result = $this->getBotManagementService()->sendMessageToUser($sender, $recipient, $message);

        if ($result) {
            return $result;
        }

        // Fallback: sử dụng cách cũ nếu user chưa đăng ký bot chung
        return $this->sendMessageLegacy($sender, $recipient, $message);
    }

    /**
     * Phương thức cũ - sử dụng bot riêng của user (deprecated)
     */
    private function sendMessageLegacy(User $sender, User $recipient, string $message): TelegramSend
    {
        // Tạo record trong database
        $telegramSend = TelegramSend::create([
            'user_id' => $sender->id,
            'recipient_user_id' => $recipient->id,
            'message' => $message,
            'status' => 'pending',
        ]);

        try {
            // Kiểm tra cấu hình telegram bot của recipient
            if (!$recipient->hasTelegramBotConfigured()) {
                throw new Exception('Recipient does not have Telegram bot configured and no active telegram user found');
            }

            $botToken = $recipient->getTelegramBotToken();
            $chatId = $recipient->getTelegramChatId();

            // Gửi tin nhắn qua Telegram API
            $response = Http::post("https://api.telegram.org/bot{$botToken}/sendMessage", [
                'chat_id' => $chatId,
                'text' => $message,
                'parse_mode' => 'HTML',
            ]);

            if ($response->successful()) {
                $telegramSend->markAsSent($response->json());
                Log::info('Telegram message sent successfully (legacy)', [
                    'sender_id' => $sender->id,
                    'recipient_id' => $recipient->id,
                    'message_id' => $telegramSend->id,
                ]);
            } else {
                $errorMessage = $response->json()['description'] ?? 'Unknown error';
                $telegramSend->markAsFailed($errorMessage);
                Log::error('Failed to send Telegram message (legacy)', [
                    'sender_id' => $sender->id,
                    'recipient_id' => $recipient->id,
                    'error' => $errorMessage,
                    'response' => $response->json(),
                ]);
            }
        } catch (Exception $e) {
            $telegramSend->markAsFailed($e->getMessage());
            Log::error('Exception while sending Telegram message (legacy)', [
                'sender_id' => $sender->id,
                'recipient_id' => $recipient->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $telegramSend;
    }

    /**
     * Gửi tin nhắn đến nhiều users
     */
    public function sendBulkMessage(User $sender, array $recipientIds, string $message): array
    {
        // Sử dụng bot management service cho bulk message
        return $this->getBotManagementService()->sendBulkMessage($sender, $recipientIds, $message);
    }

    /**
     * Gửi tin nhắn đến users theo role
     */
    public function sendMessageToRole(User $sender, string $role, string $message): array
    {
        return $this->getBotManagementService()->sendMessageToRole($sender, $role, $message);
    }

    /**
     * Gửi thông báo hệ thống
     */
    public function sendSystemNotification(string $message, ?User $sender = null): array
    {
        return $this->getBotManagementService()->sendSystemNotification($message, $sender);
    }

    /**
     * Test kết nối Telegram bot
     */
    public function testBotConnection(string $botToken): array
    {
        try {
            $response = Http::get("https://api.telegram.org/bot{$botToken}/getMe");
            
            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->json()['description'] ?? 'Unknown error',
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Lấy thông tin chat
     */
    public function getChatInfo(string $botToken, string $chatId): array
    {
        try {
            $response = Http::get("https://api.telegram.org/bot{$botToken}/getChat", [
                'chat_id' => $chatId,
            ]);
            
            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->json()['description'] ?? 'Unknown error',
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validate telegram bot configuration
     */
    public function validateBotConfig(array $config): array
    {
        $errors = [];

        if (empty($config['bot_token'])) {
            $errors[] = 'Bot token is required';
        } else {
            // Test bot token
            $testResult = $this->testBotConnection($config['bot_token']);
            if (!$testResult['success']) {
                $errors[] = 'Invalid bot token: ' . $testResult['error'];
            }
        }

        if (empty($config['chat_id'])) {
            $errors[] = 'Chat ID is required';
        } else if (!empty($config['bot_token'])) {
            // Test chat ID
            $chatResult = $this->getChatInfo($config['bot_token'], $config['chat_id']);
            if (!$chatResult['success']) {
                $errors[] = 'Invalid chat ID: ' . $chatResult['error'];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }
}
