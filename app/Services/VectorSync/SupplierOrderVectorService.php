<?php

namespace App\Services\VectorSync;

use Illuminate\Support\Facades\DB;
use App\Services\ZillizVectorService;
use App\Services\OpenAIService;

class SupplierOrderVectorService
{
    private $zilliz;
    private $openAI;

    public function __construct(ZillizVectorService $zilliz, OpenAIService $openAI)
    {
        $this->zilliz = $zilliz;
        $this->openAI = $openAI;
    }

    /**
     * Sync supplier orders to vector database
     */
    public function syncSupplierOrders($batchSize, $isDryRun, $fromDate, $toDate, $output, $saveText = false)
    {
        $output->info('🏭 Đang đồng bộ Đơn hàng Nhà cung cấp...');

        $query = DB::table('supplier_orders')
            ->select('id', 'order_id', 'supplier_id', 'status', 'created_at');

        if ($fromDate) {
            $query->where('created_at', '>=', $fromDate);
        }
        if ($toDate) {
            $query->where('created_at', '<=', $toDate);
        }

        $orders = $query->get();

        if ($orders->isEmpty()) {
            $output->writeln('<comment>Không tìm thấy đơn hàng nhà cung cấp nào</comment>');
            return $saveText ? [] : null;
        }

        $this->createCollectionIfNotExists('supplier_orders_embeddings');

        $bar = $output->createProgressBar($orders->count());
        $batches = $orders->chunk($batchSize);

        $allTexts = []; // Collect all texts for return

        foreach ($batches as $batch) {
            $vectors = [];

            foreach ($batch as $order) {
                $enhancedContext = $this->generateEnhancedSupplierOrderContext($order);
                $text = $this->generateSupplierOrderContextText($order, $enhancedContext);

                // Collect text if save option enabled
                if ($saveText) {
                    $allTexts[] = [
                        'supplier_order_id' => $order->id,
                        'order_id' => $order->order_id,
                        'supplier_id' => $order->supplier_id,
                        'status' => $order->status,
                        'text' => $text,
                        'enhanced_context' => $enhancedContext,
                        'timestamp' => now()->toISOString()
                    ];
                }

                $embedding = $this->createEmbedding($text);
                if (!$embedding) continue;

                $vectors[] = [
                    'vector' => $embedding,
                    'metadata' => json_encode([
                        'supplier_order_id' => $order->id,
                        'order_id' => $order->order_id,
                        'supplier_id' => $order->supplier_id,
                        'status' => $order->status,
                        'created_at' => $order->created_at,
                        'business_metrics' => $enhancedContext['business_metrics'] ?? null,
                        'performance_indicators' => $enhancedContext['performance_indicators'] ?? null,
                        'text' => $text
                    ])
                ];

                $bar->advance();
            }

            if (!$isDryRun && !empty($vectors)) {
                $this->zilliz->insertVectors($vectors, 'supplier_orders_embeddings');
            }
        }

        $bar->finish();
        $output->newLine();

        // Return collected texts if save option enabled
        return $saveText ? $allTexts : [];
    }

    /**
     * Tạo ngữ cảnh nâng cao cho đơn hàng nhà cung cấp
     */
    private function generateEnhancedSupplierOrderContext($order)
    {
        $context = [];

        // Phân tích metrics chuỗi cung ứng
        $context['business_metrics'] = $this->analyzeSupplierOrderMetrics($order);
        
        // Chỉ báo hiệu suất
        $context['performance_indicators'] = $this->generateSupplierPerformanceIndicators($order);
        
        // Ngữ cảnh thời gian
        $context['temporal_context'] = $this->generateSupplierTemporalContext($order);

        return $context;
    }

    /**
     * Phân tích metrics đơn hàng nhà cung cấp
     */
    private function analyzeSupplierOrderMetrics($order)
    {
        $metrics = [];

        // Phân tích trạng thái chuỗi cung ứng
        $statusAnalysis = $this->getSupplierStatusAnalysis($order->status);
        $metrics[] = "Trạng thái chuỗi cung ứng: {$statusAnalysis}";

        // Thông tin liên kết
        $metrics[] = "Đơn hàng chuỗi cung ứng {$order->id} liên kết đơn khách hàng {$order->order_id} với nhà cung cấp {$order->supplier_id}";

        // Phân tích hiệu suất thực hiện
        $fulfillmentAnalysis = $this->analyzeFulfillmentPerformance($order);
        $metrics[] = $fulfillmentAnalysis;

        return implode('. ', $metrics) . '.';
    }

    /**
     * Tạo chỉ báo hiệu suất nhà cung cấp
     */
    private function generateSupplierPerformanceIndicators($order)
    {
        $indicators = [];

        // Hiệu suất nhà cung cấp trong tháng
        $monthlySupplierOrders = DB::table('supplier_orders')
            ->where('supplier_id', $order->supplier_id)
            ->whereMonth('created_at', date('m', strtotime($order->created_at)))
            ->whereYear('created_at', date('Y', strtotime($order->created_at)))
            ->count();

        $indicators[] = "Hiệu suất nhà cung cấp tháng: {$monthlySupplierOrders} đơn hàng";

        // Tỷ lệ hoàn thành
        $completedOrders = DB::table('supplier_orders')
            ->where('supplier_id', $order->supplier_id)
            ->where('status', 'Completed')
            ->whereMonth('created_at', date('m', strtotime($order->created_at)))
            ->count();

        if ($monthlySupplierOrders > 0) {
            $completionRate = round(($completedOrders / $monthlySupplierOrders) * 100);
            $indicators[] = "Tỷ lệ hoàn thành: {$completionRate}%";
        }

        // Đánh giá độ tin cậy
        $reliabilityScore = $this->calculateSupplierReliability($order->supplier_id);
        $indicators[] = "Điểm tin cậy nhà cung cấp: {$reliabilityScore}";

        return implode('. ', $indicators) . '.';
    }

    /**
     * Tạo ngữ cảnh thời gian cho đơn hàng nhà cung cấp
     */
    private function generateSupplierTemporalContext($order)
    {
        $date = new \DateTime($order->created_at);
        $dayOfWeek = $this->getVietnameseDayOfWeek($date->format('w'));
        
        // Phân tích thời gian xử lý
        $processingTimeContext = $this->getProcessingTimeContext($order);
        
        return "Đơn hàng nhà cung cấp được khởi tạo vào {$dayOfWeek}, {$processingTimeContext}.";
    }

    /**
     * Tạo text ngữ cảnh cho đơn hàng nhà cung cấp
     */
    private function generateSupplierOrderContextText($order, $additional = [])
    {
        $prefix = 'Phân tích Chuỗi cung ứng:';
        $context = 'Theo dõi hiệu suất thực hiện của nhà cung cấp, thời gian giao hàng, kiểm soát chất lượng và các điểm nghẽn vận hành trong chuỗi cung ứng.';
        
        $specificContext = "Đơn hàng chuỗi cung ứng {$order->id} liên kết đơn khách hàng {$order->order_id} với nhà cung cấp {$order->supplier_id}.";
        
        $specificContext .= " Trạng thái thực hiện: {$order->status}.";
        
        // Thêm ngữ cảnh nâng cao
        if (!empty($additional['business_metrics'])) {
            $specificContext .= " " . $additional['business_metrics'];
        }

        if (!empty($additional['performance_indicators'])) {
            $specificContext .= " " . $additional['performance_indicators'];
        }

        if (!empty($additional['temporal_context'])) {
            $specificContext .= " " . $additional['temporal_context'];
        }
        
        $suffix = 'Sử dụng để đánh giá nhà cung cấp, tối ưu hóa thực hiện đơn hàng, đảm bảo chất lượng và quản lý rủi ro chuỗi cung ứng.';
        
        return "{$prefix} {$context} {$specificContext} {$suffix}";
    }

    // Helper methods
    private function getSupplierStatusAnalysis($status)
    {
        $analyses = [
            'Awaiting' => 'Chờ nhà cung cấp xử lý, cần theo dõi tiến độ thực hiện',
            'Processing' => 'Đang thực hiện tích cực với sự tham gia của nhà cung cấp',
            'Completed' => 'Hoàn thành thành công bởi nhà cung cấp, đóng góp vào chỉ số chuỗi cung ứng tích cực',
            'Failed' => 'Thất bại trong thực hiện, cần đánh giá hiệu suất nhà cung cấp',
            'Cancelled' => 'Bị hủy, cần phân tích nguyên nhân và tác động đến chuỗi cung ứng'
        ];

        return $analyses[$status] ?? "Trạng thái chuỗi cung ứng cần được chú ý vận hành";
    }

    private function analyzeFulfillmentPerformance($order)
    {
        // Phân tích dựa trên thời gian tạo và trạng thái
        $createdDate = new \DateTime($order->created_at);
        $now = new \DateTime();
        $daysDiff = $now->diff($createdDate)->days;

        if ($order->status === 'Completed' && $daysDiff <= 3) {
            return "Thực hiện nhanh chóng trong {$daysDiff} ngày";
        } elseif ($order->status === 'Processing' && $daysDiff > 7) {
            return "Thực hiện chậm, đã {$daysDiff} ngày chưa hoàn thành";
        } elseif ($order->status === 'Awaiting' && $daysDiff > 2) {
            return "Chờ xử lý quá lâu, {$daysDiff} ngày chưa bắt đầu";
        }

        return "Thời gian xử lý bình thường";
    }

    private function calculateSupplierReliability($supplierId)
    {
        $totalOrders = DB::table('supplier_orders')
            ->where('supplier_id', $supplierId)
            ->count();

        $completedOrders = DB::table('supplier_orders')
            ->where('supplier_id', $supplierId)
            ->where('status', 'Completed')
            ->count();

        if ($totalOrders === 0) return "Chưa đánh giá";

        $reliability = round(($completedOrders / $totalOrders) * 100);
        
        if ($reliability >= 90) return "Rất cao ({$reliability}%)";
        if ($reliability >= 75) return "Cao ({$reliability}%)";
        if ($reliability >= 60) return "Trung bình ({$reliability}%)";
        return "Thấp ({$reliability}%)";
    }

    private function getProcessingTimeContext($order)
    {
        $hour = (int)(new \DateTime($order->created_at))->format('H');
        
        if ($hour >= 8 && $hour < 17) {
            return "trong giờ làm việc, thời gian xử lý tối ưu";
        } elseif ($hour >= 17 && $hour < 20) {
            return "cuối giờ làm việc, có thể ảnh hưởng thời gian xử lý";
        } else {
            return "ngoài giờ làm việc, xử lý có thể chậm hơn";
        }
    }

    private function getVietnameseDayOfWeek($dayNumber)
    {
        $days = [
            0 => 'Chủ nhật', 1 => 'Thứ hai', 2 => 'Thứ ba', 3 => 'Thứ tư',
            4 => 'Thứ năm', 5 => 'Thứ sáu', 6 => 'Thứ bảy'
        ];
        
        return $days[$dayNumber] ?? 'Không xác định';
    }

    private function createEmbedding($text)
    {
        $result = $this->openAI->createEmbedding($text);
        return $result['success'] ? $result['embedding'] : null;
    }

    private function createCollectionIfNotExists($collectionName)
    {
        $collections = $this->zilliz->listCollections();
        $exists = collect($collections)->pluck('collectionName')->contains($collectionName);

        if (!$exists) {
            $this->zilliz->createCollection($collectionName, 1536, "Auto-created collection for {$collectionName}");
        }
    }
}
