<?php

namespace App\Services\VectorSync;

use App\Models\Document;
use App\Services\ZillizVectorService;
use App\Services\OpenAIService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DocumentVectorService
{
    private $zilliz;
    private $openAI;
    private $collectionName = 'docs_embeddings';

    public function __construct(ZillizVectorService $zilliz, OpenAIService $openAI)
    {
        $this->zilliz = $zilliz;
        $this->openAI = $openAI;

        // Đảm bảo collection tồn tại khi khởi tạo service
        $this->ensureCollectionExists();
    }

    /**
     * Sync documents to vector database
     */
    public function syncDocuments($batchSize, $isDryRun, $fromDate, $toDate, $output, $saveText = false)
    {
        $output->info('📄 Đang đồng bộ Documents...');

        $query = Document::query()
            ->where('status', 'published')
            ->where(function ($q) {
                $q->where('embedding_status', 'pending')
                  ->orWhere('embedding_status', 'failed');
            });

        if ($fromDate) {
            $query->where('created_at', '>=', $fromDate);
        }
        if ($toDate) {
            $query->where('created_at', '<=', $toDate);
        }

        $documents = $query->get();

        if ($documents->isEmpty()) {
            $output->writeln('<comment>Không tìm thấy document nào cần đồng bộ</comment>');
            return []; // Luôn trả về array để tránh lỗi count()
        }

        $this->createCollectionIfNotExists();

        $bar = $output->createProgressBar($documents->count());
        $batches = $documents->chunk($batchSize);

        $allTexts = []; // Collect all texts for return
        $totalProcessed = 0;
        $totalSuccess = 0;
        $totalFailed = 0;

        foreach ($batches as $batch) {
            $vectors = [];

            foreach ($batch as $document) {
                try {
                    if (!$isDryRun) {
                        $document->markEmbeddingStarted();

                        // Xóa vectors cũ của document này trước khi tạo mới
                        $this->deleteDocumentVectorsByDocumentId($document->id, $output);
                    }

                    // Tạo chunks từ document content
                    $chunks = $this->createDocumentChunks($document);
                    
                    if (empty($chunks)) {
                        $output->writeln("\n⚠️ Document ID {$document->id} không có content để embedding");
                        if (!$isDryRun) {
                            $document->markEmbeddingFailed('Không có content để embedding');
                        }
                        $totalFailed++;
                        continue;
                    }

                    $chunkVectors = [];
                    $chunkTexts = [];

                    foreach ($chunks as $index => $chunk) {
                        $embedding = $this->createEmbedding($chunk['text']);
                        if (!$embedding) {
                            $output->writeln("\n❌ Không thể tạo embedding cho chunk {$index} của document {$document->id}");
                            continue;
                        }

                        // Không cần ID vì collection có autoId, chỉ cần document_id để track
                        $chunkVectors[] = [
                            'vector' => $embedding,
                            'document_id' => $document->id,
                            'title' => $document->title,
                            'chunk_index' => $index,
                            'chunk_text' => $chunk['text'], // Text đầy đủ
                            'category' => $document->category ?? '',
                            'tags' => $document->tags ?? [],
                            'word_count' => str_word_count($chunk['text']),
                        ];

                        // Collect text for saving if option enabled
                        if ($saveText) {
                            $chunkTexts[] = [
                                'document_id' => $document->id,
                                'title' => $document->title,
                                'chunk_index' => $index,
                                'text' => $chunk['text'],
                                'category' => $document->category,
                                'tags' => $document->tags ?? [],
                                'file_type' => $document->file_type,
                                'word_count' => str_word_count($chunk['text']),
                            ];
                        }
                    }

                    if (!empty($chunkVectors)) {
                        $vectors = array_merge($vectors, $chunkVectors);
                        $allTexts = array_merge($allTexts, $chunkTexts);

                        if (!$isDryRun) {
                            $document->markEmbeddingCompleted(count($chunkVectors));
                        }
                        $totalSuccess++;
                    } else {
                        if (!$isDryRun) {
                            $document->markEmbeddingFailed('Không thể tạo embedding cho bất kỳ chunk nào');
                        }
                        $totalFailed++;
                    }

                    $totalProcessed++;

                } catch (\Exception $e) {
                    $output->writeln("\n❌ Lỗi xử lý document ID {$document->id}: " . $e->getMessage());
                    if (!$isDryRun) {
                        $document->markEmbeddingFailed($e->getMessage());
                    }
                    $totalFailed++;
                }

                $bar->advance();
            }

            // Insert vectors to Zilliz (sau khi đã xóa vectors cũ)
            if (!$isDryRun && !empty($vectors)) {
                try {
                    $output->writeln("\n🔄 Inserting " . count($vectors) . " vectors to Zilliz collection: {$this->collectionName}");

                    // Debug: Log first vector structure
                    if (!empty($vectors)) {
                        $output->writeln("📋 First vector structure:");
                        $firstVector = $vectors[0];
                        $debugVector = $firstVector;
                        $debugVector['vector'] = '[' . count($firstVector['vector']) . ' dimensions]'; // Don't log full vector
                        $output->writeln(json_encode($debugVector, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                    }

                    $result = $this->zilliz->insertVectors($vectors, $this->collectionName);

                    if (is_array($result)) {
                        if ($result['success']) {
                            $insertCount = $result['insertCount'] ?? 0;
                            $output->writeln("✅ Successfully inserted {$insertCount} vectors");
                        } else {
                            $output->writeln("❌ Failed to insert vectors: " . ($result['error'] ?? 'Unknown error'));
                            $output->writeln("Full result: " . json_encode($result));
                        }
                    } else {
                        // Legacy boolean check
                        if ($result) {
                            $output->writeln("✅ Successfully inserted " . count($vectors) . " vectors");
                        } else {
                            $output->writeln("❌ Failed to insert vectors to Zilliz");
                        }
                    }
                } catch (\Exception $e) {
                    $output->writeln("❌ Error inserting vectors: " . $e->getMessage());
                    Log::error("DocumentVectorService insert error", [
                        'collection' => $this->collectionName,
                        'vector_count' => count($vectors),
                        'error' => $e->getMessage()
                    ]);
                }
            } elseif ($isDryRun && !empty($vectors)) {
                $output->writeln("\n🧪 DRY RUN: Would insert " . count($vectors) . " vectors");
            }
        }

        $bar->finish();

        // Summary
        $output->info("📊 Tổng kết:");
        $output->info("   - Tổng documents xử lý: {$totalProcessed}");
        $output->info("   - Thành công: {$totalSuccess}");
        $output->info("   - Thất bại: {$totalFailed}");

        // Return collected texts if save option enabled
        return $saveText ? $allTexts : [];
    }

    /**
     * Đảm bảo collection tồn tại (được gọi trong constructor)
     */
    private function ensureCollectionExists()
    {
        try {
            $collections = $this->zilliz->listCollections();
            $exists = false;

            // Kiểm tra collection có tồn tại
            if (is_array($collections)) {
                foreach ($collections as $collection) {
                    $collectionName = is_array($collection) ?
                        ($collection['collectionName'] ?? '') : $collection;
                    if ($collectionName === $this->collectionName) {
                        $exists = true;
                        break;
                    }
                }
            }

            if (!$exists) {
                Log::info("Creating collection {$this->collectionName}");
                $result = $this->zilliz->createCollection(
                    $this->collectionName,
                    1536, // OpenAI embedding dimension
                    'Document embeddings collection for knowledge base'
                );

                if ($result) {
                    Log::info("Successfully created collection {$this->collectionName}");
                } else {
                    Log::error("Failed to create collection {$this->collectionName}");
                }
            }
        } catch (\Exception $e) {
            Log::error("Error ensuring collection {$this->collectionName} exists: " . $e->getMessage());
        }
    }

    /**
     * Tạo collection nếu chưa tồn tại (legacy method - giữ để backward compatibility)
     */
    private function createCollectionIfNotExists()
    {
        $this->ensureCollectionExists();
    }

    /**
     * Tạo embedding từ text
     */
    private function createEmbedding(string $text): ?array
    {
        try {
            $result = $this->openAI->createEmbedding($text);
            return $result['success'] ? $result['embedding'] : null;
        } catch (\Exception $e) {
            Log::error("Error creating embedding: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Tạo chunks từ document content
     */
    private function createDocumentChunks(Document $document): array
    {
        $content = $document->content;
        if (empty($content)) {
            return [];
        }

        // Smart chunking: chia theo paragraph và giữ context
        $chunks = [];
        $maxChunkSize = 1000; // words
        $overlapSize = 100; // words overlap

        // Chia theo paragraph trước
        $paragraphs = preg_split('/\n\s*\n/', $content);
        $currentChunk = '';
        $currentWordCount = 0;
        $chunkIndex = 0;

        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            if (empty($paragraph)) continue;

            $paragraphWordCount = str_word_count($paragraph);
            
            // Nếu paragraph quá dài, chia nhỏ hơn
            if ($paragraphWordCount > $maxChunkSize) {
                // Lưu chunk hiện tại nếu có
                if (!empty($currentChunk)) {
                    $chunks[] = [
                        'text' => trim($currentChunk),
                        'start' => 0,
                        'end' => strlen($currentChunk),
                        'index' => $chunkIndex++
                    ];
                    $currentChunk = '';
                    $currentWordCount = 0;
                }

                // Chia paragraph dài thành nhiều chunks
                $sentences = preg_split('/[.!?]+/', $paragraph);
                $sentenceChunk = '';
                $sentenceWordCount = 0;

                foreach ($sentences as $sentence) {
                    $sentence = trim($sentence);
                    if (empty($sentence)) continue;

                    $sentenceWords = str_word_count($sentence);
                    
                    if ($sentenceWordCount + $sentenceWords > $maxChunkSize && !empty($sentenceChunk)) {
                        $chunks[] = [
                            'text' => trim($sentenceChunk),
                            'start' => 0,
                            'end' => strlen($sentenceChunk),
                            'index' => $chunkIndex++
                        ];
                        $sentenceChunk = $sentence;
                        $sentenceWordCount = $sentenceWords;
                    } else {
                        $sentenceChunk .= ($sentenceChunk ? '. ' : '') . $sentence;
                        $sentenceWordCount += $sentenceWords;
                    }
                }

                if (!empty($sentenceChunk)) {
                    $chunks[] = [
                        'text' => trim($sentenceChunk),
                        'start' => 0,
                        'end' => strlen($sentenceChunk),
                        'index' => $chunkIndex++
                    ];
                }
            } else {
                // Paragraph bình thường
                if ($currentWordCount + $paragraphWordCount > $maxChunkSize && !empty($currentChunk)) {
                    $chunks[] = [
                        'text' => trim($currentChunk),
                        'start' => 0,
                        'end' => strlen($currentChunk),
                        'index' => $chunkIndex++
                    ];
                    $currentChunk = $paragraph;
                    $currentWordCount = $paragraphWordCount;
                } else {
                    $currentChunk .= ($currentChunk ? "\n\n" : '') . $paragraph;
                    $currentWordCount += $paragraphWordCount;
                }
            }
        }

        // Lưu chunk cuối cùng
        if (!empty($currentChunk)) {
            $chunks[] = [
                'text' => trim($currentChunk),
                'start' => 0,
                'end' => strlen($currentChunk),
                'index' => $chunkIndex
            ];
        }

        return $chunks;
    }

    /**
     * Re-embed một document cụ thể
     */
    public function reEmbedDocument(Document $document, $output = null): bool
    {
        try {
            if ($output) {
                $output->info("🔄 Re-embedding document: {$document->title}");
            }

            // Xóa vectors cũ trước
            $this->deleteDocumentVectors($document->id);

            // Reset status
            $document->resetEmbeddingStatus();

            // Sync lại
            $result = $this->syncDocuments(1, false, null, null, $output ?: new \Symfony\Component\Console\Output\NullOutput());

            return $document->fresh()->is_embedding_completed;

        } catch (\Exception $e) {
            if ($output) {
                $output->error("❌ Lỗi re-embed document {$document->id}: " . $e->getMessage());
            }
            $document->markEmbeddingFailed($e->getMessage());
            return false;
        }
    }

    /**
     * Xóa tất cả vectors của một document theo document_id
     */
    private function deleteDocumentVectorsByDocumentId(int $documentId, $output = null): bool
    {
        try {
            if ($output) {
                $output->writeln("🗑️ Deleting existing vectors for document_id {$documentId}...");
            }

            // Sử dụng filter để xóa tất cả vectors có document_id này
            $filter = "document_id == {$documentId}";

            if ($output) {
                $output->writeln("🔍 Using filter: {$filter}");
            }

            $result = $this->zilliz->deleteByFilter($filter, $this->collectionName);

            if ($output) {
                $output->writeln("📋 Delete result: " . json_encode($result));
            }

            if ($result['success']) {
                $deleteCount = $result['deleteCount'] ?? 0;
                if ($output) {
                    if ($deleteCount > 0) {
                        $output->writeln("✅ Deleted {$deleteCount} existing vectors");
                    } else {
                        $output->writeln("ℹ️ No existing vectors found (new document)");
                    }
                }
                Log::info("Deleted {$deleteCount} vectors for document_id {$documentId}");
                return true;
            } else {
                $error = $result['error'] ?? 'Unknown error';
                if ($output) {
                    $output->writeln("⚠️ Delete failed: {$error}");
                }
                Log::warning("Failed to delete vectors for document_id {$documentId}: {$error}");
                return false;
            }
        } catch (\Exception $e) {
            if ($output) {
                $output->writeln("❌ Error deleting vectors: " . $e->getMessage());
            }
            Log::error("Error deleting vectors for document_id {$documentId}: " . $e->getMessage());
            return false;
        }
    }
}
