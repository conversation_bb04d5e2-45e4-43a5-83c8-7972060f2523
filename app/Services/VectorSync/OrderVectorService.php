<?php

namespace App\Services\VectorSync;

use Illuminate\Support\Facades\DB;
use App\Services\ZillizVectorService;
use App\Services\OpenAIService;

class OrderVectorService
{
    private $zilliz;
    private $openAI;

    public function __construct(ZillizVectorService $zilliz, OpenAIService $openAI)
    {
        $this->zilliz = $zilliz;
        $this->openAI = $openAI;
    }

    /**
     * Sync orders to vector database
     */
    public function syncOrders($batchSize, $isDryRun, $fromDate, $toDate, $output, $saveText = false)
    {
        $output->info('🛒 Đang đồng bộ Đơn hàng...');

        $query = DB::table('orders')
            ->select('id', 'seller_id', 'order_code', 'total', 'status', 'created_at');

        if ($fromDate) {
            $query->where('created_at', '>=', $fromDate);
        }
        if ($toDate) {
            $query->where('created_at', '<=', $toDate);
        }

        $orders = $query->get();

        if ($orders->isEmpty()) {
            $output->writeln('<comment>Không tìm thấy đơn hàng nào</comment>');
            return $saveText ? [] : null;
        }

        $this->createCollectionIfNotExists('orders_embeddings');

        $bar = $output->createProgressBar($orders->count());
        $batches = $orders->chunk($batchSize);

        $allTexts = []; // Collect all texts for return

        foreach ($batches as $batch) {
            $vectors = [];

            foreach ($batch as $order) {
                $enhancedContext = $this->generateEnhancedOrderContext($order);
                $text = $this->generateOrderContextText($order, $enhancedContext);

                // Collect text if save option enabled
                if ($saveText) {
                    $allTexts[] = [
                        'order_id' => $order->id,
                        'seller_id' => $order->seller_id,
                        'order_code' => $order->order_code,
                        'total' => $order->total,
                        'status' => $order->status,
                        'text' => $text,
                        'enhanced_context' => $enhancedContext,
                        'timestamp' => now()->toISOString()
                    ];
                }

                $embedding = $this->createEmbedding($text);
                if (!$embedding) continue;

                $vectors[] = [
                    'vector' => $embedding,
                    'metadata' => json_encode([
                        'order_id' => $order->id,
                        'seller_id' => $order->seller_id,
                        'order_code' => $order->order_code,
                        'total' => $order->total,
                        'status' => $order->status,
                        'created_at' => $order->created_at,
                        'business_metrics' => $enhancedContext['business_metrics'] ?? null,
                        'performance_indicators' => $enhancedContext['performance_indicators'] ?? null,
                        'text' => $text
                    ])
                ];

                $bar->advance();
            }

            if (!$isDryRun && !empty($vectors)) {
                $this->zilliz->insertVectors($vectors, 'orders_embeddings');
            }
        }

        $bar->finish();
        $output->newLine();

        // Return collected texts if save option enabled
        return $saveText ? $allTexts : [];
    }

    /**
     * Tạo ngữ cảnh nâng cao cho đơn hàng
     */
    private function generateEnhancedOrderContext($order)
    {
        $context = [];

        // Phân tích metrics kinh doanh
        $context['business_metrics'] = $this->analyzeOrderMetrics($order);
        
        // Chỉ báo hiệu suất
        $context['performance_indicators'] = $this->generateOrderPerformanceIndicators($order);
        
        // Ngữ cảnh thời gian
        $context['temporal_context'] = $this->generateOrderTemporalContext($order);

        return $context;
    }

    /**
     * Phân tích metrics đơn hàng
     */
    private function analyzeOrderMetrics($order)
    {
        $metrics = [];

        // Phân loại giá trị đơn hàng
        $valueCategory = $this->categorizeOrderValue($order->total);
        $metrics[] = "Phân loại giá trị: {$valueCategory}";

        // Phân tích trạng thái
        $statusAnalysis = $this->getOrderStatusAnalysis($order->status);
        $metrics[] = "Phân tích trạng thái: {$statusAnalysis}";

        // Thông tin seller
        $metrics[] = "Đơn hàng của seller {$order->seller_id} với mã {$order->order_code}";

        return implode('. ', $metrics) . '.';
    }

    /**
     * Tạo chỉ báo hiệu suất đơn hàng
     */
    private function generateOrderPerformanceIndicators($order)
    {
        $indicators = [];

        // Hiệu suất seller trong tháng
        $monthlyOrders = DB::table('orders')
            ->where('seller_id', $order->seller_id)
            ->whereMonth('created_at', date('m', strtotime($order->created_at)))
            ->whereYear('created_at', date('Y', strtotime($order->created_at)))
            ->count();

        $indicators[] = "Hiệu suất tháng: {$monthlyOrders} đơn hàng của seller";

        // Tổng giá trị tháng
        $monthlyRevenue = DB::table('orders')
            ->where('seller_id', $order->seller_id)
            ->whereMonth('created_at', date('m', strtotime($order->created_at)))
            ->whereYear('created_at', date('Y', strtotime($order->created_at)))
            ->sum('total');

        $indicators[] = "Doanh thu tháng: " . number_format($monthlyRevenue) . " VND";

        return implode('. ', $indicators) . '.';
    }

    /**
     * Tạo ngữ cảnh thời gian cho đơn hàng
     */
    private function generateOrderTemporalContext($order)
    {
        $date = new \DateTime($order->created_at);
        $dayOfWeek = $this->getVietnameseDayOfWeek($date->format('w'));
        $hour = (int)$date->format('H');
        
        $timeContext = $this->getBusinessTimeContext($hour);
        
        return "Đơn hàng được tạo vào {$dayOfWeek}, {$timeContext}.";
    }

    /**
     * Tạo text ngữ cảnh cho đơn hàng
     */
    private function generateOrderContextText($order, $additional = [])
    {
        $prefix = 'Báo cáo Hiệu suất Đơn hàng:';
        $context = 'Đây là giao dịch khách hàng với tác động kinh doanh đến doanh thu, hiệu suất seller, sự hài lòng của khách hàng và hiệu quả vận hành.';
        
        $specificContext = "Đơn hàng {$order->order_code} (ID: {$order->id}) được xử lý bởi seller {$order->seller_id} với tổng giá trị " . number_format($order->total) . " VND.";
        
        $specificContext .= " Trạng thái giao dịch: {$order->status}.";
        
        // Thêm ngữ cảnh nâng cao
        if (!empty($additional['business_metrics'])) {
            $specificContext .= " " . $additional['business_metrics'];
        }

        if (!empty($additional['performance_indicators'])) {
            $specificContext .= " " . $additional['performance_indicators'];
        }

        if (!empty($additional['temporal_context'])) {
            $specificContext .= " " . $additional['temporal_context'];
        }
        
        $suffix = 'Phân tích xu hướng doanh thu, xếp hạng seller, hành vi khách hàng và tối ưu hóa quy trình thực hiện đơn hàng.';
        
        return "{$prefix} {$context} {$specificContext} {$suffix}";
    }

    // Helper methods
    private function categorizeOrderValue($total)
    {
        if ($total < 100000) return "Đơn hàng giá trị thấp";
        if ($total < 500000) return "Đơn hàng giá trị trung bình";
        if ($total < 2000000) return "Đơn hàng giá trị cao";
        return "Đơn hàng giá trị cao cấp";
    }

    private function getOrderStatusAnalysis($status)
    {
        $analyses = [
            'Completed' => 'Giao dịch hoàn thành thành công, đóng góp vào doanh thu tích cực',
            'Pending' => 'Giao dịch đang xử lý, cần theo dõi tiến độ thực hiện',
            'Cancelled' => 'Giao dịch bị hủy, cần phân tích nguyên nhân để cải thiện',
            'Refunded' => 'Giao dịch hoàn tiền, có thể có vấn đề về chất lượng hoặc dịch vụ'
        ];

        return $analyses[$status] ?? "Trạng thái giao dịch cần được xem xét vận hành";
    }

    private function getVietnameseDayOfWeek($dayNumber)
    {
        $days = [
            0 => 'Chủ nhật', 1 => 'Thứ hai', 2 => 'Thứ ba', 3 => 'Thứ tư',
            4 => 'Thứ năm', 5 => 'Thứ sáu', 6 => 'Thứ bảy'
        ];
        
        return $days[$dayNumber] ?? 'Không xác định';
    }

    private function getBusinessTimeContext($hour)
    {
        if ($hour >= 6 && $hour < 12) return "buổi sáng với hoạt động mua sắm tích cực";
        if ($hour >= 12 && $hour < 18) return "buổi chiều với nhu cầu mua sắm cao";
        if ($hour >= 18 && $hour < 22) return "buổi tối với hoạt động thương mại trực tuyến mạnh";
        return "ngoài giờ kinh doanh chính";
    }

    private function createEmbedding($text)
    {
        $result = $this->openAI->createEmbedding($text);
        return $result['success'] ? $result['embedding'] : null;
    }

    private function createCollectionIfNotExists($collectionName)
    {
        $collections = $this->zilliz->listCollections();
        $exists = collect($collections)->pluck('collectionName')->contains($collectionName);

        if (!$exists) {
            $this->zilliz->createCollection($collectionName, 1536, "Auto-created collection for {$collectionName}");
        }
    }
}
