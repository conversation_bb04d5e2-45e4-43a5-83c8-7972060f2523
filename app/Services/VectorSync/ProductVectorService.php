<?php

namespace App\Services\VectorSync;

use Illuminate\Support\Facades\DB;
use App\Services\ZillizVectorService;
use App\Services\OpenAIService;

class ProductVectorService
{
    private $zilliz;
    private $openAI;

    public function __construct(ZillizVectorService $zilliz, OpenAIService $openAI)
    {
        $this->zilliz = $zilliz;
        $this->openAI = $openAI;
    }

    /**
     * Sync products to vector database
     */
    public function syncProducts($batchSize, $isDryRun, $fromDate, $toDate, $output, $saveText = false)
    {
        $output->info('📦 Đang đồng bộ Sản phẩm...');

        $query = DB::table('products')->select('id', 'name', 'description', 'price', 'seller_id', 'created_at');

        if ($fromDate) {
            $query->where('created_at', '>=', $fromDate);
        }
        if ($toDate) {
            $query->where('created_at', '<=', $toDate);
        }

        $products = $query->get();

        if ($products->isEmpty()) {
            $output->writeln('<comment>Không tìm thấy sản phẩm nào</comment>');
            return $saveText ? [] : null;
        }

        $this->createCollectionIfNotExists('products_embeddings');

        $bar = $output->createProgressBar($products->count());
        $batches = $products->chunk($batchSize);

        $allTexts = []; // Collect all texts for return

        foreach ($batches as $batch) {
            $vectors = [];

            foreach ($batch as $product) {
                $enhancedContext = $this->generateEnhancedProductContext($product);
                $text = $this->generateProductContextText($product, $enhancedContext);

                // Collect text if save option enabled
                if ($saveText) {
                    $allTexts[] = [
                        'product_id' => $product->id,
                        'name' => $product->name,
                        'seller_id' => $product->seller_id,
                        'price' => $product->price,
                        'text' => $text,
                        'enhanced_context' => $enhancedContext,
                        'timestamp' => now()->toISOString()
                    ];
                }

                $embedding = $this->createEmbedding($text);
                if (!$embedding) continue;

                $vectors[] = [
                    'vector' => $embedding,
                    'metadata' => json_encode([
                        'product_id' => $product->id,
                        'name' => $product->name,
                        'price' => $product->price,
                        'seller_id' => $product->seller_id,
                        'created_at' => $product->created_at,
                        'business_metrics' => $enhancedContext['business_metrics'] ?? null,
                        'performance_indicators' => $enhancedContext['performance_indicators'] ?? null,
                        'text' => $text
                    ])
                ];

                $bar->advance();
            }

            if (!$isDryRun && !empty($vectors)) {
                $this->zilliz->insertVectors($vectors, 'products_embeddings');
            }
        }

        $bar->finish();
        $output->newLine();

        // Return collected texts if save option enabled
        return $saveText ? $allTexts : [];
    }

    /**
     * Tạo ngữ cảnh nâng cao cho sản phẩm
     */
    private function generateEnhancedProductContext($product)
    {
        $context = [];

        // Phân tích metrics kinh doanh
        $context['business_metrics'] = $this->analyzeProductMetrics($product);
        
        // Chỉ báo hiệu suất
        $context['performance_indicators'] = $this->generateProductPerformanceIndicators($product);
        
        // Ngữ cảnh thị trường
        $context['market_context'] = $this->generateMarketContext($product);

        return $context;
    }

    /**
     * Phân tích metrics sản phẩm
     */
    private function analyzeProductMetrics($product)
    {
        $metrics = [];

        // Phân loại giá sản phẩm
        $priceCategory = $this->categorizeProductPrice($product->price);
        $metrics[] = "Phân loại giá: {$priceCategory}";

        // Thông tin cơ bản
        $metrics[] = "Sản phẩm '{$product->name}' (ID: {$product->id}) với giá " . number_format($product->price) . " VND";

        // Phân tích mô tả
        if (!empty($product->description)) {
            $descriptionLength = strlen($product->description);
            $descriptionQuality = $this->analyzeDescriptionQuality($descriptionLength);
            $metrics[] = "Chất lượng mô tả: {$descriptionQuality}";
        } else {
            $metrics[] = "Chưa có mô tả sản phẩm";
        }

        // Seller context
        $metrics[] = "Thuộc seller {$product->seller_id}";

        return implode('. ', $metrics) . '.';
    }

    /**
     * Tạo chỉ báo hiệu suất sản phẩm
     */
    private function generateProductPerformanceIndicators($product)
    {
        $indicators = [];

        // Số lượng sản phẩm của seller
        $sellerProductCount = DB::table('products')
            ->where('seller_id', $product->seller_id)
            ->count();

        $indicators[] = "Portfolio seller: {$sellerProductCount} sản phẩm";

        // Vị trí giá trong portfolio seller
        $sellerProducts = DB::table('products')
            ->where('seller_id', $product->seller_id)
            ->pluck('price')
            ->sort();

        if ($sellerProducts->count() > 1) {
            $position = $this->getPricePosition($product->price, $sellerProducts->toArray());
            $indicators[] = "Vị trí giá trong portfolio: {$position}";
        }

        // Thời gian tồn tại
        $createdDate = new \DateTime($product->created_at);
        $now = new \DateTime();
        $daysSinceCreated = $now->diff($createdDate)->days;
        
        if ($daysSinceCreated < 7) {
            $indicators[] = "Sản phẩm mới ({$daysSinceCreated} ngày)";
        } elseif ($daysSinceCreated < 30) {
            $indicators[] = "Sản phẩm gần đây ({$daysSinceCreated} ngày)";
        } else {
            $indicators[] = "Sản phẩm đã có kinh nghiệm ({$daysSinceCreated} ngày)";
        }

        return implode('. ', $indicators) . '.';
    }

    /**
     * Tạo ngữ cảnh thị trường
     */
    private function generateMarketContext($product)
    {
        $context = [];

        // Phân tích cạnh tranh giá
        $similarPriceProducts = DB::table('products')
            ->whereBetween('price', [$product->price * 0.8, $product->price * 1.2])
            ->where('id', '!=', $product->id)
            ->count();

        $context[] = "Cạnh tranh thị trường: {$similarPriceProducts} sản phẩm cùng tầm giá";

        // Định vị thị trường
        $marketPosition = $this->getMarketPosition($product->price);
        $context[] = "Định vị thị trường: {$marketPosition}";

        // Tiềm năng bán hàng
        $salesPotential = $this->analyzeSalesPotential($product);
        $context[] = "Tiềm năng bán hàng: {$salesPotential}";

        return implode('. ', $context) . '.';
    }

    /**
     * Tạo text ngữ cảnh cho sản phẩm
     */
    private function generateProductContextText($product, $additional = [])
    {
        $prefix = 'Phân tích Sản phẩm Kinh doanh:';
        $context = 'Đây là sản phẩm trên nền tảng thương mại điện tử. Các chỉ số kinh doanh chính bao gồm chiến lược định giá, hiệu suất seller, vị trí thị trường và xu hướng nhu cầu khách hàng.';
        
        $specificContext = "Sản phẩm '{$product->name}' (ID: {$product->id}) với giá " . number_format($product->price) . " VND của seller {$product->seller_id}.";
        
        if (!empty($product->description)) {
            $specificContext .= " Mô tả: {$product->description}.";
        }
        
        // Thêm ngữ cảnh nâng cao
        if (!empty($additional['business_metrics'])) {
            $specificContext .= " " . $additional['business_metrics'];
        }

        if (!empty($additional['performance_indicators'])) {
            $specificContext .= " " . $additional['performance_indicators'];
        }

        if (!empty($additional['market_context'])) {
            $specificContext .= " " . $additional['market_context'];
        }
        
        $suffix = 'Sử dụng để đề xuất sản phẩm, lập kế hoạch tồn kho, tối ưu hóa giá cả và phân tích thị trường.';
        
        return "{$prefix} {$context} {$specificContext} {$suffix}";
    }

    // Helper methods
    private function categorizeProductPrice($price)
    {
        if ($price < 50000) return "Giá rẻ, phù hợp đại chúng";
        if ($price < 200000) return "Giá trung bình, thị trường chính";
        if ($price < 500000) return "Giá cao, phân khúc premium";
        return "Giá cao cấp, thị trường luxury";
    }

    private function analyzeDescriptionQuality($length)
    {
        if ($length < 50) return "Mô tả ngắn, cần bổ sung";
        if ($length < 200) return "Mô tả cơ bản, đủ thông tin";
        if ($length < 500) return "Mô tả chi tiết, chất lượng tốt";
        return "Mô tả rất chi tiết, chuyên nghiệp";
    }

    private function getPricePosition($price, $prices)
    {
        $total = count($prices);
        $position = array_search($price, $prices) + 1;
        $percentage = round(($position / $total) * 100);
        
        if ($percentage <= 25) return "Giá thấp trong portfolio";
        if ($percentage <= 50) return "Giá trung bình thấp";
        if ($percentage <= 75) return "Giá trung bình cao";
        return "Giá cao trong portfolio";
    }

    private function getMarketPosition($price)
    {
        // Lấy giá trung bình thị trường
        $avgPrice = DB::table('products')->avg('price');
        
        if ($price < $avgPrice * 0.7) return "Thị trường giá rẻ";
        if ($price < $avgPrice * 1.3) return "Thị trường chính";
        if ($price < $avgPrice * 2) return "Thị trường cao cấp";
        return "Thị trường luxury";
    }

    private function analyzeSalesPotential($product)
    {
        $priceCategory = $this->categorizeProductPrice($product->price);
        $hasDescription = !empty($product->description);
        
        if (str_contains($priceCategory, 'rẻ') && $hasDescription) {
            return "Cao - giá cạnh tranh với mô tả đầy đủ";
        } elseif (str_contains($priceCategory, 'trung bình')) {
            return "Trung bình - phân khúc thị trường chính";
        } elseif (str_contains($priceCategory, 'cao')) {
            return "Chọn lọc - phân khúc premium";
        }
        
        return "Cần đánh giá thêm";
    }

    private function createEmbedding($text)
    {
        $result = $this->openAI->createEmbedding($text);
        return $result['success'] ? $result['embedding'] : null;
    }

    private function createCollectionIfNotExists($collectionName)
    {
        $collections = $this->zilliz->listCollections();
        $exists = collect($collections)->pluck('collectionName')->contains($collectionName);

        if (!$exists) {
            $this->zilliz->createCollection($collectionName, 1536, "Auto-created collection for {$collectionName}");
        }
    }

    /**
     * Save batch texts to S3
     */
    private function saveBatchTextsToS3($texts, $entityType, $output)
    {
        try {
            $timestamp = now()->format('Y-m-d_H-i-s');
            $filename = "vector-texts/batches/{$entityType}_batch_{$timestamp}.json";

            $batchData = [
                'entity_type' => $entityType,
                'timestamp' => $timestamp,
                'count' => count($texts),
                'texts' => $texts
            ];

            // Save batch to S3
            $saved = \Storage::disk('s3')->put($filename, json_encode($batchData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

            if ($saved) {
                $url = \Storage::disk('s3')->url($filename);
                $output->info("💾 Batch texts saved to S3: {$filename} ({$batchData['count']} items)");
                return $url;
            }

            $output->warn("❌ Failed to save batch texts to S3 for {$entityType}");
            return null;

        } catch (\Exception $e) {
            $output->error("❌ Error saving batch texts to S3: " . $e->getMessage());
            return null;
        }
    }
}
