<?php

namespace App\Services\VectorSync;

use Illuminate\Support\Facades\DB;

class BusinessSummaryService
{
    /**
     * Tạo tóm tắt kinh doanh tổng hợp thay vì từng record riêng lẻ
     */
    public function generateBusinessSummary($fromDate, $toDate)
    {
        $summary = [
            'period' => [
                'from_date' => $fromDate,
                'to_date' => $toDate,
                'duration_days' => $this->calculateDays($fromDate, $toDate)
            ],
            'orders_summary' => $this->getOrdersSummary($fromDate, $toDate),
            'supplier_orders_summary' => $this->getSupplierOrdersSummary($fromDate, $toDate),
            'products_summary' => $this->getProductsSummary($fromDate, $toDate),
            'stores_summary' => $this->getStoresSummary($fromDate, $toDate),
            'daily_reports_summary' => $this->getDailyReportsSummary($fromDate, $toDate),
            'business_analysis' => $this->generateBusinessAnalysis($fromDate, $toDate)
        ];

        // Thêm summary_text tối ưu cho embedding
        $summary['summary_text'] = $this->generateOptimizedSummaryText($summary);

        return $summary;
    }

    /**
     * Tạo summary text tối ưu cho embedding (theo ChatGPT best practices)
     */
    private function generateOptimizedSummaryText($summary)
    {
        $textParts = [];

        // 1. System Context
        $textParts[] = "E-commerce Multi-vendor Platform Business Intelligence Report";

        // 2. Period Context
        $period = $summary['period'];
        $textParts[] = "Phân tích từ {$period['from_date']} đến {$period['to_date']} ({$period['duration_days']} ngày)";

        // 3. Revenue Intelligence (từ orders_intelligence.business_interpretation)
        $orders = $summary['orders_summary'];
        if ($orders['total_orders'] > 0) {
            $revenueAssessment = "Doanh thu: {$orders['total_revenue']} VND từ {$orders['total_orders']} đơn hàng, AOV {$orders['average_order_value']} VND";

            if ($orders['average_order_value'] < 20) {
                $revenueAssessment .= " - Cảnh báo AOV thấp, cần strategy tăng giá trị đơn hàng";
            }

            $textParts[] = $revenueAssessment;
        } else {
            $textParts[] = "Cảnh báo: Không có doanh thu, cần hành động khẩn cấp";
        }

        // 4. Supply Chain Critical Analysis
        $suppliers = $summary['supplier_orders_summary'];
        if ($suppliers['total_supplier_orders'] > 0) {
            $completionRate = $suppliers['completion_rate'] ?? 0;
            $supplyChainText = "Chuỗi cung ứng: {$suppliers['total_supplier_orders']} đơn supplier, completion rate {$completionRate}%";

            if ($completionRate < 50) {
                $supplyChainText .= " - CRITICAL: Supply chain failure nghiêm trọng, cần audit suppliers ngay";
            } elseif ($completionRate < 75) {
                $supplyChainText .= " - WARNING: Performance dưới mức chấp nhận, cần improve management";
            }

            $textParts[] = $supplyChainText;
        }

        // 5. Business Health Assessment
        $health = $summary['business_analysis']['business_health_score'];
        $healthText = "Business Health Score: {$health['score']}/100 ({$health['rating']})";

        if ($health['score'] < 40) {
            $healthText .= " - Cần intervention ngay: Fix supply chain, improve sellers, enhance acquisition";
        } elseif ($health['score'] < 70) {
            $healthText .= " - Có potential: Focus operational efficiency, seller development, retention";
        } else {
            $healthText .= " - Healthy business: Scale operations, market expansion, innovation";
        }

        $textParts[] = $healthText;

        // 6. Strategic Recommendations (actionable insights)
        $actionableParts = [];

        $supplierCompletionRate = $suppliers['completion_rate'] ?? 0;
        if ($supplierCompletionRate < 70) {
            $actionableParts[] = "audit suppliers";
        }

        if (($orders['average_order_value'] ?? 0) < 25) {
            $actionableParts[] = "implement upselling";
        }

        if (count($orders['top_sellers'] ?? []) < 3) {
            $actionableParts[] = "recruit active sellers";
        }

        if (!empty($actionableParts)) {
            $textParts[] = "Khuyến nghị: " . implode(', ', $actionableParts);
        }

        // 7. Context for AI
        $textParts[] = "Sử dụng để phân tích profit optimization, risk management, strategic decisions cho e-commerce marketplace";

        return implode('. ', $textParts) . '.';
    }

    /**
     * Tóm tắt đơn hàng - chỉ cần metrics chính
     */
    private function getOrdersSummary($fromDate, $toDate)
    {
        $query = DB::table('orders');
        
        if ($fromDate) $query->where('created_at', '>=', $fromDate);
        if ($toDate) $query->where('created_at', '<=', $toDate);

        $orders = $query->get();
        
        if ($orders->isEmpty()) {
            return [
                'total_orders' => 0,
                'total_revenue' => 0,
                'average_order_value' => 0,
                'top_sellers' => [],
                'status_breakdown' => [],
                'analysis' => 'Không có đơn hàng trong khoảng thời gian này'
            ];
        }

        // Tính toán metrics tổng hợp
        $totalOrders = $orders->count();
        $totalRevenue = $orders->sum('total');
        $avgOrderValue = $totalRevenue / $totalOrders;
        
        // Phân tích theo seller
        $sellerStats = $orders->groupBy('seller_id')->map(function ($sellerOrders) {
            return [
                'orders_count' => $sellerOrders->count(),
                'revenue' => $sellerOrders->sum('total'),
                'avg_order_value' => $sellerOrders->sum('total') / $sellerOrders->count()
            ];
        });

        // Phân tích theo trạng thái
        $statusStats = $orders->groupBy('status')->map(function ($statusOrders) {
            return $statusOrders->count();
        });

        return [
            'total_orders' => $totalOrders,
            'total_revenue' => round($totalRevenue, 2),
            'average_order_value' => round($avgOrderValue, 2),
            'top_sellers' => $sellerStats->sortByDesc('revenue')->take(5)->toArray(),
            'status_breakdown' => $statusStats->toArray(),
            'analysis' => "Tổng {$totalOrders} đơn hàng với doanh thu {$totalRevenue} VND, giá trị đơn hàng trung bình {$avgOrderValue} VND"
        ];
    }

    /**
     * Tóm tắt đơn hàng nhà cung cấp
     */
    private function getSupplierOrdersSummary($fromDate, $toDate)
    {
        $query = DB::table('supplier_orders');
        
        if ($fromDate) $query->where('created_at', '>=', $fromDate);
        if ($toDate) $query->where('created_at', '<=', $toDate);

        $supplierOrders = $query->get();
        
        if ($supplierOrders->isEmpty()) {
            return [
                'total_supplier_orders' => 0,
                'completion_rate' => 0,
                'top_suppliers' => [],
                'status_breakdown' => [],
                'analysis' => 'Không có đơn hàng nhà cung cấp trong khoảng thời gian này'
            ];
        }

        $totalSupplierOrders = $supplierOrders->count();
        
        // Phân tích theo supplier
        $supplierStats = $supplierOrders->groupBy('supplier_id')->map(function ($orders) {
            return [
                'orders_count' => $orders->count(),
                'completion_rate' => $orders->where('status', 'Completed')->count() / $orders->count() * 100
            ];
        });

        // Phân tích theo trạng thái
        $statusStats = $supplierOrders->groupBy('status')->map(function ($orders) {
            return $orders->count();
        });

        $completionRate = $supplierOrders->where('status', 'Completed')->count() / $totalSupplierOrders * 100;

        return [
            'total_supplier_orders' => $totalSupplierOrders,
            'completion_rate' => round($completionRate, 2),
            'top_suppliers' => $supplierStats->sortByDesc('orders_count')->take(5)->toArray(),
            'status_breakdown' => $statusStats->toArray(),
            'analysis' => "Tổng {$totalSupplierOrders} đơn hàng nhà cung cấp với tỷ lệ hoàn thành {$completionRate}%"
        ];
    }

    /**
     * Tóm tắt sản phẩm
     */
    private function getProductsSummary($fromDate, $toDate)
    {
        $query = DB::table('products');
        
        if ($fromDate) $query->where('created_at', '>=', $fromDate);
        if ($toDate) $query->where('created_at', '<=', $toDate);

        $products = $query->get();
        
        if ($products->isEmpty()) {
            return [
                'total_products' => 0,
                'average_price' => 0,
                'price_range' => ['min' => 0, 'max' => 0],
                'top_sellers' => [],
                'analysis' => 'Không có sản phẩm mới trong khoảng thời gian này'
            ];
        }

        $totalProducts = $products->count();
        $avgPrice = $products->avg('price');
        $priceRange = [
            'min' => $products->min('price'),
            'max' => $products->max('price')
        ];

        // Phân tích theo seller
        $sellerStats = $products->groupBy('seller_id')->map(function ($sellerProducts) {
            return [
                'products_count' => $sellerProducts->count(),
                'avg_price' => $sellerProducts->avg('price')
            ];
        });

        return [
            'total_products' => $totalProducts,
            'average_price' => round($avgPrice, 2),
            'price_range' => $priceRange,
            'top_sellers' => $sellerStats->sortByDesc('products_count')->take(5)->toArray(),
            'analysis' => "Tổng {$totalProducts} sản phẩm mới với giá trung bình {$avgPrice} VND"
        ];
    }

    /**
     * Tóm tắt cửa hàng
     */
    private function getStoresSummary($fromDate, $toDate)
    {
        $query = DB::table('stores');
        
        if ($fromDate) $query->where('created_at', '>=', $fromDate);
        if ($toDate) $query->where('created_at', '<=', $toDate);

        $stores = $query->get();
        
        if ($stores->isEmpty()) {
            return [
                'total_stores' => 0,
                'analysis' => 'Không có cửa hàng mới trong khoảng thời gian này'
            ];
        }

        $totalStores = $stores->count();
        $statusStats = $stores->groupBy('status')->map(function ($stores) {
            return $stores->count();
        });

        return [
            'total_stores' => $totalStores,
            'status_breakdown' => $statusStats->toArray(),
            'analysis' => "Tổng {$totalStores} cửa hàng mới được tạo"
        ];
    }

    /**
     * Tóm tắt báo cáo hàng ngày
     */
    private function getDailyReportsSummary($fromDate, $toDate)
    {
        $query = DB::table('daily_reports');
        
        if ($fromDate) $query->where('report_date', '>=', $fromDate);
        if ($toDate) $query->where('report_date', '<=', $toDate);

        $reports = $query->get();
        
        if ($reports->isEmpty()) {
            return [
                'total_reports' => 0,
                'analysis' => 'Không có báo cáo hàng ngày trong khoảng thời gian này'
            ];
        }

        $totalReports = $reports->count();
        
        // Phân tích sales_data tổng hợp
        $aggregatedSalesData = $this->aggregateSalesData($reports);
        
        // Phân tích theo seller
        $sellerStats = $reports->groupBy('seller_id')->map(function ($sellerReports) {
            return $sellerReports->count();
        });

        return [
            'total_reports' => $totalReports,
            'active_sellers' => $sellerStats->count(),
            'aggregated_sales_metrics' => $aggregatedSalesData,
            'top_active_sellers' => $sellerStats->sortByDesc(function ($count) { return $count; })->take(5)->toArray(),
            'analysis' => "Tổng {$totalReports} báo cáo từ {$sellerStats->count()} seller hoạt động"
        ];
    }

    /**
     * Tổng hợp sales_data từ tất cả báo cáo
     */
    private function aggregateSalesData($reports)
    {
        $aggregated = [
            'total_products_posted' => 0,
            'total_videos_created' => 0,
            'total_orders_processed' => 0,
            'total_ad_budget' => 0,
            'total_customer_responses' => 0
        ];

        foreach ($reports as $report) {
            if (!empty($report->sales_data)) {
                $salesData = json_decode($report->sales_data, true);
                if ($salesData) {
                    $aggregated['total_products_posted'] += (int)($salesData['dang_san_pham_so_luong'] ?? 0);
                    $aggregated['total_videos_created'] += (int)($salesData['dang_video_so_luong'] ?? 0);
                    $aggregated['total_orders_processed'] += (int)($salesData['don_hang_tong'] ?? 0);
                    
                    // Parse ad budget (remove non-numeric characters)
                    $adBudget = preg_replace('/[^0-9.]/', '', $salesData['chay_quang_cao_ngan_sach'] ?? '0');
                    $aggregated['total_ad_budget'] += (float)$adBudget;
                    
                    $aggregated['total_customer_responses'] += (int)($salesData['khach_hang_phan_hoi'] ?? 0);
                }
            }
        }

        return $aggregated;
    }

    /**
     * Phân tích kinh doanh tổng hợp
     */
    private function generateBusinessAnalysis($fromDate, $toDate)
    {
        // Lấy dữ liệu để phân tích lợi nhuận
        $orders = DB::table('orders');
        if ($fromDate) $orders->where('created_at', '>=', $fromDate);
        if ($toDate) $orders->where('created_at', '<=', $toDate);
        $ordersData = $orders->get();

        $supplierOrders = DB::table('supplier_orders');
        if ($fromDate) $supplierOrders->where('created_at', '>=', $fromDate);
        if ($toDate) $supplierOrders->where('created_at', '<=', $toDate);
        $supplierOrdersData = $supplierOrders->get();

        $totalRevenue = $ordersData->sum('total');
        $totalOrders = $ordersData->count();
        $totalSupplierOrders = $supplierOrdersData->count();
        
        // Tính toán hiệu suất
        $fulfillmentRate = $totalOrders > 0 ? ($supplierOrdersData->where('status', 'Completed')->count() / $totalSupplierOrders * 100) : 0;
        $avgOrderValue = $totalOrders > 0 ? ($totalRevenue / $totalOrders) : 0;

        return [
            'revenue_analysis' => "Doanh thu tổng: {$totalRevenue} VND từ {$totalOrders} đơn hàng",
            'efficiency_analysis' => "Tỷ lệ thực hiện đơn hàng: {$fulfillmentRate}% ({$supplierOrdersData->where('status', 'Completed')->count()}/{$totalSupplierOrders})",
            'profitability_indicators' => [
                'average_order_value' => round($avgOrderValue, 2),
                'order_fulfillment_rate' => round($fulfillmentRate, 2),
                'total_transactions' => $totalOrders
            ],
            'business_health_score' => $this->calculateBusinessHealthScore($totalOrders, $fulfillmentRate, $avgOrderValue)
        ];
    }

    /**
     * Tính điểm sức khỏe kinh doanh
     */
    private function calculateBusinessHealthScore($totalOrders, $fulfillmentRate, $avgOrderValue)
    {
        $score = 0;
        
        // Điểm số đơn hàng (40%)
        if ($totalOrders > 100) $score += 40;
        elseif ($totalOrders > 50) $score += 30;
        elseif ($totalOrders > 10) $score += 20;
        elseif ($totalOrders > 0) $score += 10;
        
        // Điểm tỷ lệ thực hiện (40%)
        if ($fulfillmentRate > 90) $score += 40;
        elseif ($fulfillmentRate > 75) $score += 30;
        elseif ($fulfillmentRate > 50) $score += 20;
        elseif ($fulfillmentRate > 0) $score += 10;
        
        // Điểm giá trị đơn hàng (20%)
        if ($avgOrderValue > 100) $score += 20;
        elseif ($avgOrderValue > 50) $score += 15;
        elseif ($avgOrderValue > 20) $score += 10;
        elseif ($avgOrderValue > 0) $score += 5;
        
        return [
            'score' => $score,
            'rating' => $this->getHealthRating($score)
        ];
    }

    private function getHealthRating($score)
    {
        if ($score >= 80) return 'Xuất sắc';
        if ($score >= 60) return 'Tốt';
        if ($score >= 40) return 'Trung bình';
        return 'Cần cải thiện';
    }

    private function calculateDays($fromDate, $toDate)
    {
        if (!$fromDate || !$toDate) return null;
        
        $from = new \DateTime($fromDate);
        $to = new \DateTime($toDate);
        return $from->diff($to)->days + 1;
    }
}
