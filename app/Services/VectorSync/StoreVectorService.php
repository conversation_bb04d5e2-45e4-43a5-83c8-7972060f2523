<?php

namespace App\Services\VectorSync;

use Illuminate\Support\Facades\DB;
use App\Services\ZillizVectorService;
use App\Services\OpenAIService;

class StoreVectorService
{
    private $zilliz;
    private $openAI;

    public function __construct(ZillizVectorService $zilliz, OpenAIService $openAI)
    {
        $this->zilliz = $zilliz;
        $this->openAI = $openAI;
    }

    /**
     * Sync stores to vector database
     */
    public function syncStores($batchSize, $isDryRun, $fromDate, $toDate, $output, $saveText = false)
    {
        $output->info('🏪 Đang đồng bộ Cửa hàng...');

        $query = DB::table('stores')
            ->select('id', 'name', 'status', 'owner_id', 'created_at');

        if ($fromDate) {
            $query->where('created_at', '>=', $fromDate);
        }
        if ($toDate) {
            $query->where('created_at', '<=', $toDate);
        }

        $stores = $query->get();

        if ($stores->isEmpty()) {
            $output->writeln('<comment><PERSON>hông tìm thấy cửa hàng nào</comment>');
            return $saveText ? [] : null;
        }

        $this->createCollectionIfNotExists('stores_embeddings');

        $bar = $output->createProgressBar($stores->count());
        $batches = $stores->chunk($batchSize);

        $allTexts = []; // Collect all texts for return

        foreach ($batches as $batch) {
            $vectors = [];

            foreach ($batch as $store) {
                $enhancedContext = $this->generateEnhancedStoreContext($store);
                $text = $this->generateStoreContextText($store, $enhancedContext);

                // Collect text if save option enabled
                if ($saveText) {
                    $allTexts[] = [
                        'store_id' => $store->id,
                        'name' => $store->name,
                        'status' => $store->status,
                        'owner_id' => $store->owner_id,
                        'text' => $text,
                        'enhanced_context' => $enhancedContext,
                        'timestamp' => now()->toISOString()
                    ];
                }

                $embedding = $this->createEmbedding($text);
                if (!$embedding) continue;

                $vectors[] = [
                    'vector' => $embedding,
                    'metadata' => json_encode([
                        'store_id' => $store->id,
                        'name' => $store->name,
                        'status' => $store->status,
                        'owner_id' => $store->owner_id,
                        'created_at' => $store->created_at,
                        'business_metrics' => $enhancedContext['business_metrics'] ?? null,
                        'performance_indicators' => $enhancedContext['performance_indicators'] ?? null,
                        'text' => $text
                    ])
                ];

                $bar->advance();
            }

            if (!$isDryRun && !empty($vectors)) {
                $this->zilliz->insertVectors($vectors, 'stores_embeddings');
            }
        }

        $bar->finish();
        $output->newLine();

        // Return collected texts if save option enabled
        return $saveText ? $allTexts : [];
    }

    /**
     * Tạo ngữ cảnh nâng cao cho cửa hàng
     */
    private function generateEnhancedStoreContext($store)
    {
        $context = [];

        // Phân tích metrics kinh doanh
        $context['business_metrics'] = $this->analyzeStoreMetrics($store);
        
        // Chỉ báo hiệu suất
        $context['performance_indicators'] = $this->generateStorePerformanceIndicators($store);
        
        // Ngữ cảnh hoạt động
        $context['operational_context'] = $this->generateOperationalContext($store);

        return $context;
    }

    /**
     * Phân tích metrics cửa hàng
     */
    private function analyzeStoreMetrics($store)
    {
        $metrics = [];

        // Thông tin cơ bản
        $metrics[] = "Cửa hàng '{$store->name}' (ID: {$store->id}) được vận hành bởi owner {$store->owner_id}";

        // Phân tích trạng thái
        $statusAnalysis = $this->getStoreStatusAnalysis($store->status);
        $metrics[] = "Trạng thái hoạt động: {$statusAnalysis}";

        // Số lượng sản phẩm
        $productCount = DB::table('products')
            ->where('seller_id', $store->owner_id)
            ->count();
        $metrics[] = "Portfolio: {$productCount} sản phẩm";

        // Phân tích doanh thu
        $revenueAnalysis = $this->analyzeStoreRevenue($store);
        $metrics[] = $revenueAnalysis;

        return implode('. ', $metrics) . '.';
    }

    /**
     * Tạo chỉ báo hiệu suất cửa hàng
     */
    private function generateStorePerformanceIndicators($store)
    {
        $indicators = [];

        // Hiệu suất đơn hàng
        $monthlyOrders = DB::table('orders')
            ->where('seller_id', $store->owner_id)
            ->whereMonth('created_at', date('m'))
            ->whereYear('created_at', date('Y'))
            ->count();

        $indicators[] = "Hiệu suất tháng: {$monthlyOrders} đơn hàng";

        // Tỷ lệ hoàn thành đơn hàng
        $completedOrders = DB::table('orders')
            ->where('seller_id', $store->owner_id)
            ->where('status', 'Completed')
            ->whereMonth('created_at', date('m'))
            ->count();

        if ($monthlyOrders > 0) {
            $completionRate = round(($completedOrders / $monthlyOrders) * 100);
            $indicators[] = "Tỷ lệ hoàn thành: {$completionRate}%";
        }

        // Thời gian hoạt động
        $createdDate = new \DateTime($store->created_at);
        $now = new \DateTime();
        $monthsActive = $now->diff($createdDate)->m + ($now->diff($createdDate)->y * 12);
        
        if ($monthsActive < 1) {
            $indicators[] = "Cửa hàng mới (dưới 1 tháng)";
        } elseif ($monthsActive < 6) {
            $indicators[] = "Cửa hàng phát triển ({$monthsActive} tháng)";
        } else {
            $indicators[] = "Cửa hàng có kinh nghiệm ({$monthsActive} tháng)";
        }

        // Đánh giá tổng thể
        $overallRating = $this->calculateStoreRating($store);
        $indicators[] = "Đánh giá tổng thể: {$overallRating}";

        return implode('. ', $indicators) . '.';
    }

    /**
     * Tạo ngữ cảnh hoạt động
     */
    private function generateOperationalContext($store)
    {
        $context = [];

        // Phân tích hoạt động gần đây
        $recentActivity = $this->analyzeRecentActivity($store);
        $context[] = $recentActivity;

        // Xu hướng phát triển
        $growthTrend = $this->analyzeGrowthTrend($store);
        $context[] = $growthTrend;

        // Cơ hội cải thiện
        $improvements = $this->identifyImprovementOpportunities($store);
        $context[] = $improvements;

        return implode('. ', $context) . '.';
    }

    /**
     * Tạo text ngữ cảnh cho cửa hàng
     */
    private function generateStoreContextText($store, $additional = [])
    {
        $prefix = 'Tổng quan Hiệu suất Cửa hàng:';
        $context = 'Đại diện cho cửa hàng seller với các chỉ số hiệu suất, trạng thái hoạt động và các chỉ báo sức khỏe kinh doanh.';
        
        $specificContext = "Cửa hàng '{$store->name}' (ID: {$store->id}) được vận hành bởi owner {$store->owner_id}.";
        
        $specificContext .= " Trạng thái hoạt động: {$store->status}.";
        
        // Thêm ngữ cảnh nâng cao
        if (!empty($additional['business_metrics'])) {
            $specificContext .= " " . $additional['business_metrics'];
        }

        if (!empty($additional['performance_indicators'])) {
            $specificContext .= " " . $additional['performance_indicators'];
        }

        if (!empty($additional['operational_context'])) {
            $specificContext .= " " . $additional['operational_context'];
        }
        
        $suffix = 'Đánh giá xếp hạng cửa hàng, so sánh hiệu suất, cơ hội tăng trưởng và nhu cầu hỗ trợ vận hành.';
        
        return "{$prefix} {$context} {$specificContext} {$suffix}";
    }

    // Helper methods
    private function getStoreStatusAnalysis($status)
    {
        $analyses = [
            'active' => 'Hoạt động tích cực, đóng góp vào doanh thu nền tảng',
            'inactive' => 'Không hoạt động, cần kích hoạt lại hoặc đánh giá hiệu suất',
            'suspended' => 'Bị đình chỉ, cần can thiệp tuân thủ hoặc hiệu suất',
            'pending' => 'Đang chờ xử lý, cần theo dõi tiến độ'
        ];

        return $analyses[strtolower($status)] ?? "Trạng thái cần được xem xét vận hành";
    }

    private function analyzeStoreRevenue($store)
    {
        $monthlyRevenue = DB::table('orders')
            ->where('seller_id', $store->owner_id)
            ->whereMonth('created_at', date('m'))
            ->whereYear('created_at', date('Y'))
            ->sum('total');

        if ($monthlyRevenue == 0) {
            return "Chưa có doanh thu tháng này";
        } elseif ($monthlyRevenue < 1000000) {
            return "Doanh thu thấp: " . number_format($monthlyRevenue) . " VND";
        } elseif ($monthlyRevenue < 10000000) {
            return "Doanh thu trung bình: " . number_format($monthlyRevenue) . " VND";
        } else {
            return "Doanh thu cao: " . number_format($monthlyRevenue) . " VND";
        }
    }

    private function calculateStoreRating($store)
    {
        $score = 0;
        
        // Điểm trạng thái
        if ($store->status === 'active') $score += 40;
        elseif ($store->status === 'pending') $score += 20;
        
        // Điểm sản phẩm
        $productCount = DB::table('products')->where('seller_id', $store->owner_id)->count();
        if ($productCount > 50) $score += 30;
        elseif ($productCount > 10) $score += 20;
        elseif ($productCount > 0) $score += 10;
        
        // Điểm đơn hàng
        $orderCount = DB::table('orders')->where('seller_id', $store->owner_id)->count();
        if ($orderCount > 100) $score += 30;
        elseif ($orderCount > 20) $score += 20;
        elseif ($orderCount > 0) $score += 10;
        
        if ($score >= 80) return "Xuất sắc";
        if ($score >= 60) return "Tốt";
        if ($score >= 40) return "Trung bình";
        return "Cần cải thiện";
    }

    private function analyzeRecentActivity($store)
    {
        $recentOrders = DB::table('orders')
            ->where('seller_id', $store->owner_id)
            ->where('created_at', '>=', date('Y-m-d', strtotime('-7 days')))
            ->count();

        if ($recentOrders > 10) {
            return "Hoạt động mạnh với {$recentOrders} đơn hàng tuần qua";
        } elseif ($recentOrders > 0) {
            return "Hoạt động ổn định với {$recentOrders} đơn hàng tuần qua";
        } else {
            return "Không có hoạt động gần đây, cần kích hoạt";
        }
    }

    private function analyzeGrowthTrend($store)
    {
        $thisMonth = DB::table('orders')
            ->where('seller_id', $store->owner_id)
            ->whereMonth('created_at', date('m'))
            ->count();

        $lastMonth = DB::table('orders')
            ->where('seller_id', $store->owner_id)
            ->whereMonth('created_at', date('m', strtotime('-1 month')))
            ->count();

        if ($lastMonth == 0) {
            return "Xu hướng: Chưa đủ dữ liệu để đánh giá";
        }

        $growth = (($thisMonth - $lastMonth) / $lastMonth) * 100;
        
        if ($growth > 20) {
            return "Xu hướng: Tăng trưởng mạnh (" . round($growth) . "%)";
        } elseif ($growth > 0) {
            return "Xu hướng: Tăng trưởng ổn định (" . round($growth) . "%)";
        } elseif ($growth > -20) {
            return "Xu hướng: Giảm nhẹ (" . round($growth) . "%)";
        } else {
            return "Xu hướng: Giảm mạnh, cần can thiệp";
        }
    }

    private function identifyImprovementOpportunities($store)
    {
        $opportunities = [];

        $productCount = DB::table('products')->where('seller_id', $store->owner_id)->count();
        if ($productCount < 10) {
            $opportunities[] = "tăng số lượng sản phẩm";
        }

        $recentOrders = DB::table('orders')
            ->where('seller_id', $store->owner_id)
            ->where('created_at', '>=', date('Y-m-d', strtotime('-30 days')))
            ->count();
        
        if ($recentOrders < 5) {
            $opportunities[] = "cải thiện marketing";
        }

        if (empty($opportunities)) {
            return "Cơ hội: Duy trì hiệu suất tốt";
        }

        return "Cơ hội cải thiện: " . implode(', ', $opportunities);
    }

    private function createEmbedding($text)
    {
        $result = $this->openAI->createEmbedding($text);
        return $result['success'] ? $result['embedding'] : null;
    }

    private function createCollectionIfNotExists($collectionName)
    {
        $collections = $this->zilliz->listCollections();
        $exists = collect($collections)->pluck('collectionName')->contains($collectionName);

        if (!$exists) {
            $this->zilliz->createCollection($collectionName, 1536, "Auto-created collection for {$collectionName}");
        }
    }
}
