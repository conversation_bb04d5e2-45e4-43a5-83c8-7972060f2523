<?php

namespace App\Services\VectorSync;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Services\ZillizVectorService;
use App\Services\OpenAIService;
use App\Models\BusinessIntelligenceReport;
use App\Models\Document;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class DailyReportVectorService
{
    private $zilliz;
    private $openAI;

    public function __construct(ZillizVectorService $zilliz, OpenAIService $openAI)
    {
        $this->zilliz = $zilliz;
        $this->openAI = $openAI;
    }

    /**
     * Sync daily reports to vector database
     */
    public function syncDailyReports($batchSize, $isDryRun, $fromDate, $toDate, $output, $saveText = false)
    {
        $output->info('📊 Đang đồng bộ Báo cáo Hàng ngày...');

        $query = DB::table('daily_reports')
            ->select('id', 'seller_id', 'report_date', 'activities', 'status', 'sales_data');

        if ($fromDate) {
            $query->where('report_date', '>=', $fromDate);
        }
        if ($toDate) {
            $query->where('report_date', '<=', $toDate);
        }

        $reports = $query->get();

        if ($reports->isEmpty()) {
            $output->writeln('<comment>Không tìm thấy báo cáo hàng ngày nào</comment>');
            return []; // Luôn trả về array để tránh lỗi count()
        }

        $this->createCollectionIfNotExists('daily_reports_embeddings');

        $bar = null;
        if (method_exists($output, 'createProgressBar')) {
            $bar = $output->createProgressBar($reports->count());
        }
        $batches = $reports->chunk($batchSize);

        $allTexts = []; // Collect all texts for return

        foreach ($batches as $batch) {
            $vectors = [];

            foreach ($batch as $report) {
                // Lấy thông tin seller
                $seller = $this->getSellerInfo($report->seller_id);
                $sellerName = $seller ? $seller->name : "Seller {$report->seller_id}";

                // Kiểm tra và xóa report cũ nếu đã tồn tại
                if (!$isDryRun) {
                    $this->deleteExistingReport($report->id, 'daily_reports_embeddings');
                }

                // Thêm business intelligence context
                $enhancedContext = $this->generateEnhancedDailyReportContext($report);
                $text = $this->generateDailyReportContextText($report, $enhancedContext);

                // Collect text if save option enabled
                if ($saveText) {
                    $allTexts[] = [
                        'report_id' => $report->id,
                        'seller_id' => $report->seller_id,
                        'seller_name' => $sellerName,
                        'report_date' => $report->report_date,
                        'status' => $report->status,
                        'activities' => $report->activities,
                        'sales_data' => $report->sales_data,
                        'text' => $text,
                        'enhanced_context' => $enhancedContext,
                        'timestamp' => now()->toISOString()
                    ];
                }

                $embedding = $this->createEmbedding($text);
                if (!$embedding) continue;

               $vectors[] = [
                    'vector' => $embedding,
                    // Lưu report_id như field riêng biệt để có thể filter
                    'report_id'        => $report->id,
                    'seller_id'        => $report->seller_id,
                    'seller_name'      => $sellerName,
                    'report_date'      => $report->report_date,
                    'status'           => $report->status,
                    'activities'       => $report->activities,
                    'sales_data'       => $report->sales_data,
                    'report_summary'   => $enhancedContext['report_summary'] ?? null,
                    'seller_activity'  => $enhancedContext['seller_activity'] ?? null,
                    // 'text'             => $text
                ];


                if ($bar) {
                    $bar->advance();
                }
            }

            if (!$isDryRun && !empty($vectors)) {
                $this->zilliz->insertVectors($vectors, 'daily_reports_embeddings');
            }
        }

        if ($bar) {
            $bar->finish();
            $output->newLine();
        }

        // Return collected texts if save option enabled
        return $saveText ? $allTexts : [];
    }

    /**
     * Tạo ngữ cảnh nâng cao cho báo cáo hàng ngày (đơn giản hóa)
     */
    private function generateEnhancedDailyReportContext($report)
    {
        return [
            'report_summary' => $this->generateReportSummary($report),
            'seller_activity' => null // Removed getSellerActivityLevel function
        ];
    }

    /**
     * Tạo tóm tắt báo cáo từ tất cả dữ liệu trong sales_data
     */
    private function generateReportSummary($report)
    {
        $salesData = json_decode($report->sales_data, true) ?? [];

        if (empty($salesData)) {
            return 'Không có dữ liệu sales';
        }

        $activities = [];

        // Xử lý tất cả các key-value pairs trong sales_data
        foreach ($salesData as $key => $value) {
            // Bỏ qua các giá trị rỗng, null, hoặc 0
            if (empty($value) && $value !== '0' && $value !== 0) {
                continue;
            }

            // Chuyển đổi key thành mô tả có ý nghĩa
            $description = $this->formatSalesDataKey($key, $value);

            if ($description) {
                $activities[] = $description;
            }
        }

        return empty($activities) ? 'Không có hoạt động chính' : implode(', ', $activities);
    }

    /**
     * Chuyển đổi key và value từ sales_data thành mô tả có ý nghĩa
     */
    private function formatSalesDataKey($key, $value)
    {
        // Mapping các key thường gặp thành mô tả tiếng Việt
        $keyMappings = [
            'dang_san_pham_so_luong' => 'đăng {value} sản phẩm',
            'dang_video_so_luong' => 'tạo {value} video',
            'don_hang_tong' => 'xử lý {value} đơn hàng',
            'chay_quang_cao_ngan_sach' => 'chạy quảng cáo {value}',
            'khach_hang_phan_hoi' => '{value} phản hồi khách hàng',
            'dang_san_pham_niche' => 'niche: {value}',
            'dang_video_acc' => 'acc video: {value}',
            'don_hang_ghi_chu' => 'ghi chú đơn hàng: {value}',
            'chay_quang_cao_ket_qua' => 'kết quả QC: {value}',
            'khach_hang_feedback' => 'feedback: {value}',
            'kiem_tra_shop_health' => 'shop health: {value}'
        ];

        // Nếu có mapping cụ thể, sử dụng nó
        if (isset($keyMappings[$key])) {
            return str_replace('{value}', $value, $keyMappings[$key]);
        }

        // Nếu không có mapping, tạo mô tả generic
        // Chuyển snake_case thành readable format
        $readableKey = $this->convertSnakeCaseToReadable($key);

        // Nếu value là số, thêm định dạng phù hợp
        if (is_numeric($value)) {
            return "{$readableKey}: {$value}";
        }

        // Nếu value là string và không quá dài
        if (is_string($value) && strlen($value) <= 50) {
            return "{$readableKey}: {$value}";
        }

        // Nếu value quá dài, cắt ngắn
        if (is_string($value)) {
            $shortValue = substr($value, 0, 47) . '...';
            return "{$readableKey}: {$shortValue}";
        }

        // Fallback cho các trường hợp khác
        return "{$readableKey}: " . json_encode($value);
    }

    /**
     * Chuyển đổi snake_case thành readable format
     */
    private function convertSnakeCaseToReadable($snakeCase)
    {
        // Thay thế underscore bằng space và capitalize
        $readable = str_replace('_', ' ', $snakeCase);
        return ucfirst($readable);
    }



    /**
     * Tạo text ngữ cảnh cho báo cáo hàng ngày theo format API
     */
    private function generateDailyReportContextText($report, $additional = [])
    {
        // Lấy thông tin seller
        $seller = $this->getSellerInfo($report->seller_id);
        $sellerName = $seller ? $seller->name : "Seller {$report->seller_id}";

        // Format ngày báo cáo
        $reportDate = date('d/m/Y', strtotime($report->report_date));

        // Trạng thái báo cáo
        $statusText = $this->getStatusText($report->status);

        // Bắt đầu tạo nội dung theo format yêu cầu
        $content = "Seller: {$sellerName}. Ngày báo cáo: {$reportDate}. Trạng thái: {$statusText}.\n\n";

        $content .= "Hoạt động trong ngày:\n";

        // Parse sales data để lấy thông tin chi tiết
        $salesData = json_decode($report->sales_data, true) ?? [];

        // Đăng sản phẩm
        if (isset($salesData['dang_san_pham_so_luong']) && $salesData['dang_san_pham_so_luong']) {
            $productCount = $salesData['dang_san_pham_so_luong'];
            $niche = $salesData['dang_san_pham_niche'] ?? '';

            // Hiển thị niche trực tiếp
            if ($niche) {
                $nicheText = " ở các niche: [{$niche}]";
            } else {
                $nicheText = " ở các niche: [chưa xác định]";
            }

            $content .= "- Đăng {$productCount} sản phẩm{$nicheText}.\n";
        }

        // Đăng video
        if (isset($salesData['dang_video_so_luong']) && $salesData['dang_video_so_luong']) {
            $videoCount = $salesData['dang_video_so_luong'];
            $acc = $salesData['dang_video_acc'] ?? '';
            $accText = $acc ? " (Acc đăng: {$acc})" : " (Acc đăng: chưa xác định)";
            $content .= "- Đăng {$videoCount} video{$accText}.\n";
        }

        // Đơn hàng
        if (isset($salesData['don_hang_tong']) && $salesData['don_hang_tong']) {
            $orderCount = $salesData['don_hang_tong'];
            $note = $salesData['don_hang_ghi_chu'] ?? '';
            $noteText = $note ? ". Ghi chú: {$note}" : '';
            $content .= "- Có {$orderCount} đơn hàng{$noteText}.\n";
        }

        // Chạy quảng cáo
        if (isset($salesData['chay_quang_cao_ngan_sach']) && $salesData['chay_quang_cao_ngan_sach']) {
            $budget = $salesData['chay_quang_cao_ngan_sach'];
            $result = $salesData['chay_quang_cao_ket_qua'] ?? '';
            $resultText = $result ? ", kết quả {$result}" : '';
            $content .= "- Chạy quảng cáo với ngân sách {$budget}{$resultText}.\n";
        }

        // Khách hàng
        if (isset($salesData['khach_hang_phan_hoi']) && $salesData['khach_hang_phan_hoi']) {
            $response = $salesData['khach_hang_phan_hoi'];
            $feedback = $salesData['khach_hang_feedback'] ?? '';
            $feedbackText = $feedback ? ", feedback: {$feedback}" : '';
            $content .= "- Khách hàng: {$response} phản hồi nhận tin{$feedbackText}.\n";
        }

        // Shop Health
        if (isset($salesData['kiem_tra_shop_health']) && $salesData['kiem_tra_shop_health']) {
            $shopHealth = $salesData['kiem_tra_shop_health'];
            $content .= "- Shop Health: {$shopHealth}.\n";
        }

        // Góp ý của seller
        if (!empty($report->activities)) {
            $content .= "\nGóp ý của seller: {$report->activities}.";
        } else {
            $content .= "\nGóp ý của seller: [nội dung góp ý nếu có].";
        }

        return $content;
    }

    // Helper methods
    private function getSellerInfo($sellerId)
    {
        return DB::table('users')->where('id', $sellerId)->first();
    }

    private function getStatusText($status)
    {
        $statusMap = [
            'submitted' => 'Đã gửi',
            'reviewed' => 'Đã duyệt',
            'draft' => 'Bản nháp',
            'pending' => 'Chờ xử lý'
        ];

        return $statusMap[strtolower($status)] ?? ucfirst($status);
    }





    private function createEmbedding($text)
    {
        $result = $this->openAI->createEmbedding($text);
        return $result['success'] ? $result['embedding'] : null;
    }

    private function createCollectionIfNotExists($collectionName)
    {
        $collections = $this->zilliz->listCollections();
        $exists = collect($collections)->pluck('collectionName')->contains($collectionName);

        if (!$exists) {
            $this->zilliz->createCollection($collectionName, 1536, "Auto-created collection for {$collectionName}");
        }
    }

    /**
     * Xóa report đã tồn tại trong vector database sử dụng deleteByFilter
     */
    private function deleteExistingReport($reportId, $collectionName)
    {
        try {
            // Sử dụng filter đơn giản như DocumentVectorService
            $filter = "report_id == {$reportId}";

            $result = $this->zilliz->deleteByFilter($filter, $collectionName);

            if ($result['success']) {
                $deleteCount = $result['deleteCount'] ?? 0;
                if ($deleteCount > 0) {
                    error_log("✅ Deleted {$deleteCount} existing vectors for report_id: {$reportId}");
                } else {
                    // Không có vectors cũ, đây là report mới
                    error_log("ℹ️ No existing vectors found for report_id: {$reportId} (new report)");
                }
                return true;
            } else {
                $error = $result['error'] ?? 'Unknown error';
                error_log("⚠️ Delete failed for report_id {$reportId}: {$error}");
                // Vẫn return true để tiếp tục insert, có thể sẽ có duplicate nhưng không crash
                return true;
            }

        } catch (\Exception $e) {
            // Log error nhưng không dừng quá trình sync
            error_log("❌ Exception deleting existing report {$reportId}: " . $e->getMessage());
            return true; // Return true để tiếp tục insert
        }
    }

    /**
     * Tạo báo cáo tổng hợp từ danh sách report IDs (giống Command)
     *
     * @param array $reportIds Danh sách ID của các báo cáo
     * @param string $period Loại báo cáo (daily, weekly, monthly)
     * @return array
     */
    public function generateSummaryReport($reportIds, $period = 'daily')
    {
        try {
            Log::info("🤖 Bắt đầu tạo báo cáo AI từ Vector Database", [
                'report_ids' => $reportIds,
                'period' => $period,
                'total_reports' => count($reportIds)
            ]);

            if (empty($reportIds)) {
                return [
                    'success' => false,
                    'error' => 'Không có báo cáo nào được chọn',
                    'data' => null
                ];
            }

            // Lấy dữ liệu báo cáo từ database để xác định date range
            $reports = DB::table('daily_reports')
                ->whereIn('id', $reportIds)
                ->get();

            if ($reports->isEmpty()) {
                return [
                    'success' => false,
                    'error' => 'Không tìm thấy báo cáo nào',
                    'data' => null
                ];
            }

            // Xác định date range từ reports
            $fromDate = $reports->min('report_date');
            $toDate = $reports->max('report_date');

            Log::info("📅 Date range determined: {$fromDate} to {$toDate}");

            // Sử dụng logic từ Command để tạo báo cáo AI
            return $this->generateAIReportFromVectors($fromDate, $toDate);

        } catch (\Exception $e) {
            Log::error("❌ Lỗi tạo báo cáo AI từ Vector Database", [
                'error' => $e->getMessage(),
                'report_ids' => $reportIds,
                'period' => $period
            ]);

            return [
                'success' => false,
                'error' => 'Lỗi tạo báo cáo: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Sync các báo cáo được chọn lên vector database
     */
    private function syncSelectedReports($reports)
    {
        foreach ($reports as $report) {
            // Kiểm tra và xóa report cũ nếu đã tồn tại
            $this->deleteExistingReport($report->id, 'daily_reports_embeddings');

            // Tạo enhanced context và text
            $enhancedContext = $this->generateEnhancedDailyReportContext($report);
            $text = $this->generateDailyReportContextText($report, $enhancedContext);

            // Tạo embedding
            $embedding = $this->createEmbedding($text);
            if (!$embedding) continue;

            // Lấy thông tin seller
            $seller = $this->getSellerInfo($report->seller_id);
            $sellerName = $seller ? $seller->name : "Seller {$report->seller_id}";

            $vector = [
                'vector' => $embedding,
                'report_id' => $report->id,
                'seller_id' => $report->seller_id,
                'seller_name' => $sellerName,
                'report_date' => $report->report_date,
                'status' => $report->status,
                'activities' => $report->activities,
                'sales_data' => $report->sales_data,
                'report_summary' => $enhancedContext['report_summary'] ?? null,
                'seller_activity' => $enhancedContext['seller_activity'] ?? null,
            ];

            // Insert vào vector database
            $this->zilliz->insertVectors([$vector], 'daily_reports_embeddings');
        }
    }

    /**
     * Tạo tóm tắt dựa trên vector data
     */
    private function generateVectorBasedSummary($reports, $period)
    {
        $totalReports = $reports->count();
        $sellers = $reports->groupBy('seller_id');
        $dateRange = $this->getDateRangeFromReports($reports);

        $summary = "📊 BÁO CÁO TỔNG HỢP ({$period})\n";
        $summary .= "Khoảng thời gian: {$dateRange}\n";
        $summary .= "Tổng số báo cáo: {$totalReports}\n";
        $summary .= "Số lượng sellers: " . $sellers->count() . "\n\n";

        $summary .= "CHI TIẾT THEO SELLER:\n";
        foreach ($sellers as $sellerId => $sellerReports) {
            $seller = $this->getSellerInfo($sellerId);
            $sellerName = $seller ? $seller->name : "Seller {$sellerId}";

            $summary .= "- {$sellerName}: {$sellerReports->count()} báo cáo\n";

            // Phân tích sales_data
            $totalProducts = 0;
            $totalOrders = 0;
            foreach ($sellerReports as $report) {
                $salesData = json_decode($report->sales_data, true) ?? [];
                $totalProducts += $salesData['dang_san_pham_so_luong'] ?? 0;
                $totalOrders += $salesData['don_hang_tong'] ?? 0;
            }

            if ($totalProducts > 0) {
                $summary .= "  + Đăng {$totalProducts} sản phẩm\n";
            }
            if ($totalOrders > 0) {
                $summary .= "  + Xử lý {$totalOrders} đơn hàng\n";
            }
        }

        return $summary;
    }

    /**
     * Lấy khoảng thời gian từ danh sách báo cáo
     */
    private function getDateRangeFromReports($reports)
    {
        $dates = $reports->pluck('report_date')->unique()->sort();

        if ($dates->count() === 1) {
            return $dates->first();
        } else {
            return $dates->first() . ' đến ' . $dates->last();
        }
    }

    /**
     * Lưu báo cáo vào Business Intelligence Report
     */
    private function saveToBIReport($reports, $summary, $period)
    {
        try {
            $dateRange = $this->getDateRangeFromReports($reports);
            $title = "Vector-based Daily Reports Summary ({$dateRange})";

            $analysisData = [
                'type' => 'vector_daily_report_summary',
                'period' => $period,
                'total_reports' => $reports->count(),
                'date_range' => $dateRange,
                'generated_by' => 'DailyReportVectorService',
                'vector_collection' => 'daily_reports_embeddings'
            ];

            $biReport = BusinessIntelligenceReport::create([
                'title' => $title,
                'period' => $period,
                'from_date' => $reports->min('report_date'),
                'to_date' => $reports->max('report_date'),
                'analysis_data' => $analysisData,
                'ai_summary' => $summary,
                'pdf_url' => '',
                'pdf_filename' => '',
                'total_reports' => $reports->count(),
                'total_products' => 0,
                'total_orders' => 0,
                'total_suppliers' => 0,
                'total_stores' => 0,
                'total_revenue' => 0,
                'avg_order_value' => 0,
                'status' => 'completed',
                'error_message' => null,
                'created_by' => Auth::id() ?? 1
            ]);

            Log::info("✅ Đã lưu báo cáo Vector-based vào Business Intelligence", [
                'bi_report_id' => $biReport->id,
                'total_reports' => $reports->count()
            ]);

            return $biReport;

        } catch (\Exception $e) {
            Log::error("❌ Lỗi lưu báo cáo Vector-based vào BI", [
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Tạo báo cáo AI từ vectors đã embedding (giống Command)
     */
    public function generateAIReportFromVectors($fromDate = null, $toDate = null)
    {
        try {
            Log::info('📊 Collecting embedded vectors from Zilliz...');

            // 1. Thu thập dữ liệu từ vector database
            $vectorData = $this->collectDailyReportVectors($fromDate, $toDate);

            if (empty($vectorData)) {
                Log::warning('⚠️ No vector data found for the specified date range');
                return [
                    'success' => false,
                    'error' => 'No vector data found for the specified date range',
                    'data' => null
                ];
            }

            Log::info("📈 Found " . count($vectorData) . " embedded reports");

            // 2. Tạo AI analysis
            Log::info('🤖 Generating AI analysis...');
            $aiSummary = $this->generateAIAnalysis($vectorData);

            if (!$aiSummary) {
                return [
                    'success' => false,
                    'error' => 'Failed to generate AI analysis',
                    'data' => null
                ];
            }

            // 3. Tạo PDF và upload S3
            Log::info('📄 Generating PDF report...');
            $pdfResult = $this->generateAndUploadPDF($aiSummary, $fromDate, $toDate);

            // 4. Lưu báo cáo vào database
            Log::info('💾 Saving report to database...');
            $reportId = $this->saveVectorReportToDatabase($vectorData, $aiSummary, $pdfResult, $fromDate, $toDate);

            if ($reportId) {
                Log::info("✅ AI Report created successfully! Report ID: {$reportId}");
                return [
                    'success' => true,
                    'data' => [
                        'report_id' => $reportId,
                        'pdf_url' => $pdfResult['url'] ?? null,
                        'summary' => $aiSummary
                    ],
                    'message' => 'AI Report created successfully!'
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Failed to save report to database',
                    'data' => null
                ];
            }

        } catch (\Exception $e) {
            Log::error("❌ Error generating AI report: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error generating AI report: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Thu thập dữ liệu vectors từ Zilliz theo date range (từ Command)
     */
    private function collectDailyReportVectors($fromDate, $toDate)
    {
        try {
            $collectionName = 'daily_reports_embeddings';

            // Đầu tiên kiểm tra collection stats
            Log::info("🔍 Checking collection stats...");
            $stats = $this->zilliz->getCollectionStats($collectionName);
            if ($stats) {
                Log::info("📊 Collection stats: " . json_encode($stats));
            } else {
                Log::warning("⚠️ Could not get collection stats");
            }

            // Thử semantic search với query chung để lấy data
            Log::info("🔍 Using semantic search to collect data...");
            $queries = [
                "daily report seller activity",
                "báo cáo hàng ngày seller"
            ];

            $allData = [];
            $seenIds = [];

            foreach ($queries as $query) {
                Log::info("🔍 Searching with query: {$query}");

                $result = $this->zilliz->semanticSearchV2($query, 50, $collectionName);

                if ($result['success'] && !empty($result['data'])) {
                    foreach ($result['data'] as $item) {
                        $reportId = $item['report_id'] ?? null;

                        // Tránh duplicate
                        if ($reportId && !in_array($reportId, $seenIds)) {
                            // Filter by date range if needed
                            if ($this->isInDateRange($item, $fromDate, $toDate)) {
                                $allData[] = $item;
                                $seenIds[] = $reportId;
                            }
                        }
                    }

                    Log::info("📊 Query '{$query}' returned " . count($result['data']) . " records");
                } else {
                    Log::warning("⚠️ Query '{$query}' failed: " . ($result['error'] ?? 'No data'));
                }

                // Giới hạn để tránh quá tải
                if (count($allData) >= 100) {
                    Log::info("📊 Reached 100 records limit");
                    break;
                }
            }

            Log::info("📊 Total unique records collected: " . count($allData));
            return $allData;

        } catch (\Exception $e) {
            Log::error("Error collecting vector data: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Kiểm tra xem report có trong date range không (từ Command)
     */
    private function isInDateRange($reportData, $fromDate, $toDate)
    {
        $reportDate = $reportData['report_date'] ?? null;

        if (!$reportDate) {
            return true; // Nếu không có date thì include
        }

        if ($fromDate && $reportDate < $fromDate) {
            return false;
        }

        if ($toDate && $reportDate > $toDate) {
            return false;
        }

        return true;
    }

    /**
     * Tạo AI analysis từ vector data với UTF-8 cleaning (từ Command)
     */
    private function generateAIAnalysis($vectorData)
    {
        try {
            // Tạo context từ vector data
            $context = $this->createAnalysisContext($vectorData);

            // Làm sạch context trước khi gửi
            $cleanContext = $this->cleanText($context);

            // Tạo prompt cho ChatGPT
            $prompt = $this->createAnalysisPrompt($cleanContext);
            $cleanPrompt = $this->cleanText($prompt);

            // Validate UTF-8 trước khi gửi
            if (!mb_check_encoding($cleanPrompt, 'UTF-8')) {
                Log::error("Invalid UTF-8 encoding in prompt");
                return null;
            }

            // Gọi ChatGPT
            $messages = [
                ['role' => 'user', 'content' => $cleanPrompt]
            ];

            Log::info("Sending prompt to ChatGPT", [
                'prompt_length' => mb_strlen($cleanPrompt, 'UTF-8'),
                'encoding_valid' => mb_check_encoding($cleanPrompt, 'UTF-8')
            ]);

            $result = $this->openAI->chatCompletion($messages, 'gpt-4o-mini', 3000);

            if ($result['success']) {
                return $result['content'];
            } else {
                Log::error("ChatGPT API error: " . ($result['error'] ?? 'Unknown error'));
                return null;
            }

        } catch (\Exception $e) {
            Log::error("Error generating AI analysis: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Tạo context từ vector data để phân tích (format bảng tối ưu token)
     */
    private function createAnalysisContext($vectorData)
    {
        $context = "DAILY REPORTS ANALYSIS DATA:\n\n";

        // Thống kê tổng quan
        $totalReports = count($vectorData);
        $sellers = array_unique(array_column($vectorData, 'seller_name'));
        $totalSellers = count($sellers);

        $context .= "TỔNG QUAN:\n";
        $context .= "- Tổng số báo cáo: {$totalReports}\n";
        $context .= "- Tổng số sellers: {$totalSellers}\n\n";

        // Hiển thị dữ liệu theo format bảng tối ưu token
        $context .= "CHI TIẾT BÁO CÁO (Format bảng tối ưu):\n\n";

        // Header bảng
        $context .= sprintf("%-6s | %-10s | %-8s | %-50s | %-30s | %s\n",
            "Seller", "Date", "Orders", "Sales Data", "Activities", "Summary");
        $context .= str_repeat('-', 150) . "\n";

        foreach ($vectorData as $report) {
            // Parse sales_data để lấy thông tin quan trọng
            $salesData = [];
            if (!empty($report['sales_data'])) {
                $salesData = is_string($report['sales_data'])
                    ? json_decode($report['sales_data'], true) ?? []
                    : $report['sales_data'];
            }

            // Tạo summary ngắn gọn từ sales_data
            $salesSummary = $this->createSalesSummary($salesData);

            // Cắt ngắn activities và summary với UTF-8 cleaning
            $activities = $this->cleanAndTruncateText($report['activities'] ?? '', 30);
            $summary = $this->cleanAndTruncateText($report['report_summary'] ?? '', 50);
            $sellerName = $this->cleanText($report['seller_name'] ?? 'N/A');

            $context .= sprintf("%-6s | %-10s | %-8d | %-50s | %-30s | %s\n",
                $sellerName,
                $report['report_date'] ?? 'N/A',
                $salesData['don_hang_tong'] ?? 0,
                $salesSummary,
                $activities,
                $summary
            );
        }

        return $context;
    }

    /**
     * Tạo summary ngắn gọn từ sales_data với UTF-8 cleaning
     */
    private function createSalesSummary($salesData)
    {
        if (empty($salesData)) {
            return 'N/A';
        }

        $summary = [];

        // Lấy các thông tin quan trọng với UTF-8 cleaning
        if (!empty($salesData['don_hang_tong'])) {
            $summary[] = "Orders:" . $salesData['don_hang_tong'];
        }

        if (!empty($salesData['chay_quang_cao_ket_qua'])) {
            $summary[] = $this->cleanText($salesData['chay_quang_cao_ket_qua']);
        }

        if (!empty($salesData['dang_san_pham_so_luong'])) {
            $summary[] = "Products:" . $salesData['dang_san_pham_so_luong'];
        }

        if (!empty($salesData['dang_san_pham_niche'])) {
            $summary[] = "Niche:" . $this->cleanText($salesData['dang_san_pham_niche']);
        }

        if (!empty($salesData['chay_quang_cao_ngan_sach'])) {
            $summary[] = "Budget:" . $salesData['chay_quang_cao_ngan_sach'];
        }

        $result = implode(', ', $summary);
        return $this->cleanAndTruncateText($result, 50);
    }

    /**
     * Làm sạch UTF-8 characters
     */
    private function cleanText($text)
    {
        if (empty($text)) {
            return 'N/A';
        }

        // Convert to UTF-8 if needed
        if (!mb_check_encoding($text, 'UTF-8')) {
            $text = mb_convert_encoding($text, 'UTF-8', 'auto');
        }

        // Remove or replace problematic characters
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Remove non-printable characters
        $text = preg_replace('/[^\P{C}]+/u', '', $text);

        // Trim and normalize whitespace
        $text = trim(preg_replace('/\s+/', ' ', $text));

        return $text ?: 'N/A';
    }

    /**
     * Làm sạch và cắt ngắn text
     */
    private function cleanAndTruncateText($text, $maxLength)
    {
        $cleanText = $this->cleanText($text);

        if (mb_strlen($cleanText, 'UTF-8') <= $maxLength) {
            return $cleanText;
        }

        return mb_substr($cleanText, 0, $maxLength - 3, 'UTF-8') . '...';
    }





    /**
     * Tạo prompt cho ChatGPT phân tích (từ Command)
     */
    private function createAnalysisPrompt($context, $fromDate = null, $toDate = null)
    {
        // Xử lý date range cho prompt
        if (!$fromDate && !$toDate) {
            $dateRange = "tất cả báo cáo";
        } else {
            $dateRange = ($fromDate === $toDate)
                ? "ngày {$fromDate}"
                : "từ {$fromDate} đến {$toDate}";
        }

        // Lấy prompt từ database sử dụng key
        $promptDocument = Document::where('key', 'daily-reports-analysis-prompt')
            ->where('status', 'published')
            ->first();

        if (!$promptDocument) {
            Log::warning('⚠️ Không tìm thấy prompt document, sử dụng prompt mặc định');
            return $this->getDefaultAnalysisPrompt($dateRange, $context);
        }

        Log::info('📋 Sử dụng prompt từ database document');

        // Thay thế placeholders trong prompt
        $prompt = $promptDocument->content;
        $prompt = str_replace('[NGÀY/KHOẢNG THỜI GIAN]', $dateRange, $prompt);

        // Thêm context data vào cuối prompt
        $prompt .= "\n\n## DỮ LIỆU CẦN PHÂN TÍCH:\n\n{$context}";

        return $prompt;
    }

    /**
     * Prompt mặc định nếu không tìm thấy trong database (tối ưu cho format bảng)
     */
    private function getDefaultAnalysisPrompt($dateRange, $context)
    {
        return "Bạn là chuyên gia phân tích kinh doanh cho hệ thống POD (Print on Demand).
Hãy tạo báo cáo EXECUTIVE SUMMARY cho sếp về Daily Reports {$dateRange} bằng tiếng Việt.

{$context}

YÊU CẦU BÁO CÁO CHO SẾP:
1. **TỔNG QUAN HIỆU SUẤT** (2-3 dòng tóm tắt từ bảng dữ liệu)
2. **TOP PERFORMERS** (sellers có số đơn cao, ROAS tốt)
3. **SELLERS CẦN CHÚ Ý** (Warning, số đơn thấp, ROAS kém)
4. **PHÂN TÍCH SẢN PHẨM** (categories nổi bật, xu hướng)
5. **HÀNH ĐỘNG CẦN THIẾT** (cụ thể cho từng seller)

HƯỚNG DẪN PHÂN TÍCH:
- Dựa vào bảng dữ liệu để đưa ra insights cụ thể
- So sánh CTR, ROAS giữa các sellers
- Nhận xét về product categories
- Đề xuất hành động cụ thể cho sellers Warning/Cần chú ý

LƯU Ý: Tập trung vào số liệu cụ thể từ bảng, đưa ra actionable insights.";
    }

    /**
     * Tạo PDF và upload lên S3 (từ Command)
     */
    private function generateAndUploadPDF($content, $fromDate, $toDate)
    {
        try {
            $dateRange = $fromDate && $toDate ? "{$fromDate} đến {$toDate}" : "Tùy chỉnh";
            $date = Carbon::now()->format('d/m/Y H:i');

            // Tạo HTML đơn giản với text thuần
            $htmlContent = "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Daily Reports Analysis</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }
        .content {
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class='header'>
        <h3>Daily Reports Analysis</h3>
        <p>Khoảng thời gian: {$dateRange}</p>
        <p>Ngày tạo: {$date}</p>
    </div>
    <div class='content'>{$content}</div>
</body>
</html>";

            // Generate PDF
            $pdf = Pdf::loadHTML($htmlContent)
                ->setPaper('A4', 'portrait')
                ->setOptions([
                    'defaultFont' => 'DejaVu Sans',
                ]);

            $pdfBinary = $pdf->output();

            // Upload to S3
            $timestamp = Carbon::now()->format('d-m-Y_H-i-s');
            $filename = "daily-reports-ai-analysis/ai_report_{$timestamp}.pdf";

            $saved = Storage::disk('s3')->put($filename, $pdfBinary, 'public');

            if ($saved) {
                $url = Storage::disk('s3')->url($filename);
                return [
                    'url' => $url,
                    'filename' => $filename
                ];
            }

            return ['url' => '', 'filename' => ''];

        } catch (\Exception $e) {
            Log::error("Error generating PDF: " . $e->getMessage());
            return ['url' => '', 'filename' => ''];
        }
    }

    /**
     * Lưu báo cáo vector vào database (từ Command)
     */
    private function saveVectorReportToDatabase($vectorData, $aiSummary, $pdfResult, $fromDate, $toDate)
    {
        try {
            if (!$fromDate && !$toDate) {
                $dateRange = "Tất cả báo cáo";
                $title = "AI Analysis - Daily Reports (All Reports)";
                $period = 'all';
            } else {
                $dateRange = $fromDate && $toDate ? "{$fromDate} đến {$toDate}" : "Tùy chỉnh";
                $title = "AI Analysis - Daily Reports ({$dateRange})";
                $period = $fromDate && $toDate ? 'custom' : 'daily';
            }

            $report = BusinessIntelligenceReport::create([
                'title' => $title,
                'period' => $period,
                'from_date' => $fromDate ?: now()->format('Y-m-d'),
                'to_date' => $toDate ?: now()->format('Y-m-d'),
                'analysis_data' => [
                    'type' => 'daily_reports_ai_analysis_from_vectors',
                    'total_vectors' => count($vectorData),
                    'generated_by' => 'DailyReportVectorService',
                    'ai_model' => 'gpt-4o-mini',
                    'vector_collection' => 'daily_reports_embeddings'
                ],
                'ai_summary' => $aiSummary,
                'raw_prompt' => $this->createAnalysisPrompt($this->createAnalysisContext($vectorData), $fromDate, $toDate),
                'raw_embedding_data' => json_encode($vectorData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
                'pdf_url' => $pdfResult['url'] ?? '',
                'pdf_filename' => $pdfResult['filename'] ?? '',
                'total_reports' => count($vectorData),
                'total_products' => 0,
                'total_orders' => 0,
                'total_suppliers' => 0,
                'total_stores' => 0,
                'total_revenue' => 0,
                'avg_order_value' => 0,
                'status' => 'completed',
                'error_message' => null,
                'created_by' => Auth::id() ?? 1
            ]);

            return $report->id;

        } catch (\Exception $e) {
            Log::error("Error saving vector report to database: " . $e->getMessage());
            return null;
        }
    }
}
