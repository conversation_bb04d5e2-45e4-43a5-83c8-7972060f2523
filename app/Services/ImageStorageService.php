<?php

namespace App\Services;

use App\Models\GeneratedImage;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Exception;

class ImageStorageService
{
    /**
     * Tạo record trong database (không upload ngay)
     */
    public function createImageRecord(
        string $imageUrl,
        string $prompt,
        int $userId,
        array $metadata = []
    ): GeneratedImage {
        return GeneratedImage::create([
            'user_id' => $userId,
            'prompt' => $prompt,
            'original_url' => $imageUrl,
            'filename' => $this->generateFilename($imageUrl),
            'metadata' => $metadata,
            'status' => 'pending',
        ]);
    }

    /**
     * Upload ảnh đã tồn tại trong database lên S3
     */
    public function uploadExistingImage(GeneratedImage $generatedImage): array
    {
        try {
            // Download ảnh từ URL
            $imageData = $this->downloadImage($generatedImage->original_url);

            // Upload lên S3
            $s3Result = $this->uploadToS3($imageData, $generatedImage->filename);

            // Lấy thông tin ảnh
            $imageInfo = $this->getImageInfo($imageData);

            // Cập nhật database
            $generatedImage->update([
                's3_path' => $s3Result['path'],
                's3_url' => $s3Result['url'],
                'file_size' => strlen($imageData),
                'mime_type' => $imageInfo['mime_type'],
                'width' => $imageInfo['width'],
                'height' => $imageInfo['height'],
                'status' => 'uploaded',
                'uploaded_at' => now(),
            ]);

            Log::info('Image uploaded successfully', [
                'image_id' => $generatedImage->id,
                's3_path' => $s3Result['path']
            ]);

            return [
                'success' => true,
                's3_path' => $s3Result['path'],
                's3_url' => $s3Result['url'],
            ];

        } catch (Exception $e) {
            // Cập nhật status lỗi
            $generatedImage->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
            ]);

            Log::error('Failed to upload image', [
                'image_id' => $generatedImage->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Lưu ảnh từ URL vào S3 và database (legacy method - deprecated)
     */
    public function storeImageFromUrl(
        string $imageUrl,
        string $prompt,
        int $userId,
        array $metadata = []
    ): GeneratedImage {
        $generatedImage = $this->createImageRecord($imageUrl, $prompt, $userId, $metadata);
        $this->uploadExistingImage($generatedImage);
        return $generatedImage;
    }

    /**
     * Download ảnh từ URL
     */
    private function downloadImage(string $url): string
    {
        $response = Http::timeout(30)->get($url);
        
        if (!$response->successful()) {
            throw new Exception("Failed to download image from URL: {$url}");
        }

        return $response->body();
    }

    /**
     * Upload ảnh lên S3
     */
    private function uploadToS3(string $imageData, string $filename): array
    {
        $path = 'generated-images/' . date('Y/m/d') . '/' . $filename;
        
        $uploaded = Storage::disk('s3')->put($path, $imageData, [
            'visibility' => 'public',
            'ContentType' => 'image/jpeg',
        ]);

        if (!$uploaded) {
            throw new Exception('Failed to upload image to S3');
        }

        return [
            'path' => $path,
            'url' => Storage::disk('s3')->url($path),
        ];
    }

    /**
     * Lấy thông tin ảnh
     */
    private function getImageInfo(string $imageData): array
    {
        $tempFile = tmpfile();
        fwrite($tempFile, $imageData);
        $tempPath = stream_get_meta_data($tempFile)['uri'];

        $info = getimagesize($tempPath);
        $mimeType = $info['mime'] ?? 'image/jpeg';

        fclose($tempFile);

        return [
            'width' => $info[0] ?? null,
            'height' => $info[1] ?? null,
            'mime_type' => $mimeType,
        ];
    }

    /**
     * Tạo filename unique
     */
    private function generateFilename(string $url): string
    {
        $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';
        return Str::uuid() . '.' . $extension;
    }

    /**
     * Xóa ảnh khỏi S3
     */
    public function deleteFromS3(GeneratedImage $image): bool
    {
        if (!$image->s3_path) {
            return true;
        }

        try {
            Storage::disk('s3')->delete($image->s3_path);
            
            $image->update([
                's3_path' => null,
                's3_url' => null,
                'status' => 'pending',
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Failed to delete image from S3', [
                'image_id' => $image->id,
                's3_path' => $image->s3_path,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Tạo nhiều image records (không upload ngay)
     */
    public function createMultipleImageRecords(
        array $imageUrls,
        string $prompt,
        int $userId,
        array $metadata = []
    ): array {
        $results = [];

        foreach ($imageUrls as $index => $url) {
            try {
                $imageMetadata = array_merge($metadata, ['batch_index' => $index]);
                $results[] = $this->createImageRecord($url, $prompt, $userId, $imageMetadata);
            } catch (Exception $e) {
                Log::error('Failed to create image record in batch', [
                    'url' => $url,
                    'index' => $index,
                    'error' => $e->getMessage()
                ]);

                // Tiếp tục với ảnh tiếp theo
                continue;
            }
        }

        return $results;
    }

    /**
     * Batch upload nhiều ảnh (legacy method - deprecated)
     */
    public function storeMultipleImages(
        array $imageUrls,
        string $prompt,
        int $userId,
        array $metadata = []
    ): array {
        $results = [];

        foreach ($imageUrls as $index => $url) {
            try {
                $imageMetadata = array_merge($metadata, ['batch_index' => $index]);
                $results[] = $this->storeImageFromUrl($url, $prompt, $userId, $imageMetadata);
            } catch (Exception $e) {
                Log::error('Failed to store image in batch', [
                    'url' => $url,
                    'index' => $index,
                    'error' => $e->getMessage()
                ]);

                // Tiếp tục với ảnh tiếp theo
                continue;
            }
        }

        return $results;
    }
}
