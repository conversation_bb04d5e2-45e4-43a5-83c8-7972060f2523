<?php

namespace App\Services;

use App\Models\User;
use App\Models\LarkSend;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Exception;

class LarkService
{
    // API Endpoints
    private const LARK_API_BASE_URL = 'https://open.larksuite.com/open-apis';
    private const FEISHU_API_BASE_URL = 'https://open.feishu.cn/open-apis';

    private const ENDPOINTS = [
        'token' => '/auth/v3/tenant_access_token/internal',
        'bot_info' => '/bot/v3/info',
        'send_message' => '/im/v1/messages',
        'chat_info' => '/im/v1/chats',
        'upload_image' => '/image/v4/put/',
    ];

    // Message Types
    private const MESSAGE_TYPES = [
        'TEXT' => 'text',
        'IMAGE' => 'image',
        'RICH_TEXT' => 'rich_text',
        'POST' => 'post',
        'INTERACTIVE' => 'interactive',
    ];

    // Image Configuration
    private const IMAGE_CONFIG = [
        'max_size' => 10 * 1024 * 1024, // 10MB
        'allowed_mimes' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        'storage_path' => 'lark-images',
    ];

    // Status Constants
    private const STATUS = [
        'PENDING' => 'pending',
        'SENT' => 'sent',
        'FAILED' => 'failed',
    ];

    // HTML to Markdown Conversion Rules
    private const HTML_CONVERSION_RULES = [
        // Headers (with line breaks)
        '/<h1[^>]*>(.*?)<\/h1>/i' => "\n# $1\n",
        '/<h2[^>]*>(.*?)<\/h2>/i' => "\n## $1\n",
        '/<h3[^>]*>(.*?)<\/h3>/i' => "\n### $1\n",
        '/<h4[^>]*>(.*?)<\/h4>/i' => "\n#### $1\n",
        '/<h5[^>]*>(.*?)<\/h5>/i' => "\n##### $1\n",
        '/<h6[^>]*>(.*?)<\/h6>/i' => "\n###### $1\n",

        // Bold
        '/<(b|strong)[^>]*>(.*?)<\/(b|strong)>/i' => '**$2**',

        // Italic
        '/<(i|em)[^>]*>(.*?)<\/(i|em)>/i' => '*$2*',

        // Underline
        '/<u[^>]*>(.*?)<\/u>/i' => '<u>$1</u>',

        // Links
        '/<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)<\/a>/i' => '[$2]($1)',

        // Code
        '/<code[^>]*>(.*?)<\/code>/i' => '`$1`',
        '/<pre[^>]*>(.*?)<\/pre>/is' => '```$1```',

        // Line breaks and paragraphs
        '/<br[^>]*>/i' => "\n",
        '/<p[^>]*>/i' => "\n",
        '/<\/p>/i' => "\n",

        // Lists
        '/<ul[^>]*>/i' => "\n",
        '/<\/ul>/i' => "\n",
        '/<ol[^>]*>/i' => "\n",
        '/<\/ol>/i' => "\n",
        '/<li[^>]*>(.*?)<\/li>/i' => "• $1\n",
    ];
    /**
     * Gửi tin nhắn Lark đến user
     */
    public function sendMessage(User $sender, User $recipient, string $message, ?UploadedFile $image = null): LarkSend
    {
        return $this->sendMessageWithType($sender, $recipient, $message, self::MESSAGE_TYPES['TEXT'], $image);
    }

    /**
     * Gửi tin nhắn HTML/Rich Text
     */
    public function sendHtmlMessage(User $sender, User $recipient, string $htmlContent, ?UploadedFile $image = null): LarkSend
    {
        return $this->sendMessageWithType($sender, $recipient, $htmlContent, self::MESSAGE_TYPES['POST'], $image);
    }

    /**
     * Gửi tin nhắn rich text với ảnh và text
     */
    public function sendRichMessage(User $sender, User $recipient, string $message, ?UploadedFile $image = null): LarkSend
    {
        return $this->sendMessageWithType($sender, $recipient, $message, self::MESSAGE_TYPES['INTERACTIVE'], $image);
    }

    /**
     * Core method để gửi tin nhắn với type cụ thể
     */
    private function sendMessageWithType(User $sender, User $recipient, string $content, string $messageType, ?UploadedFile $image = null): LarkSend
    {
        // Tạo record trong database
        $larkSend = $this->createLarkSendRecord($sender, $recipient, $content);

        try {
            // Validate recipient configuration
            $this->validateRecipientConfiguration($recipient);

            // Get webhook URL
            $webhookUrl = $recipient->getLarkWebhookUrl();
            if (!$webhookUrl) {
                throw new Exception('Webhook URL is required');
            }

            // Send message based on type
            $response = $this->dispatchMessage($webhookUrl, $content, $messageType, $image);

            // Handle response
            $this->handleMessageResponse($larkSend, $response, $sender, $recipient, $messageType);

        } catch (Exception $e) {
            $this->handleMessageError($larkSend, $e, $sender, $recipient);
        }

        return $larkSend;
    }

    /**
     * Tạo record LarkSend trong database
     */
    private function createLarkSendRecord(User $sender, User $recipient, string $content): LarkSend
    {
        return LarkSend::create([
            'user_id' => $sender->id,
            'recipient_user_id' => $recipient->id,
            'message' => $content,
            'status' => self::STATUS['PENDING'],
        ]);
    }

    /**
     * Validate cấu hình của recipient
     */
    private function validateRecipientConfiguration(User $recipient): void
    {
        if (!$recipient->hasLarkBotConfigured()) {
            Log::channel('lark_errors')->error('Recipient configuration validation failed', [
                'user_id' => $recipient->id,
                'user_name' => $recipient->name,
                'has_lark_config' => false,
            ]);
            throw new Exception('Recipient does not have Lark bot configured');
        }
    }

    /**
     * Dispatch tin nhắn dựa trên type
     */
    private function dispatchMessage(string $webhookUrl, string $content, string $messageType, ?UploadedFile $image = null): array
    {
        switch ($messageType) {
            case self::MESSAGE_TYPES['TEXT']:
                return $this->sendTextViaWebhook($webhookUrl, $content, $image);

            case self::MESSAGE_TYPES['POST']:
                return $this->sendHtmlViaWebhook($webhookUrl, $content, $image);

            case self::MESSAGE_TYPES['INTERACTIVE']:
                return $this->sendRichCardViaWebhook($webhookUrl, $content, $image);

            default:
                return $this->sendTextViaWebhook($webhookUrl, $content, $image);
        }
    }

    /**
     * Xử lý response thành công
     */
    private function handleMessageResponse(LarkSend $larkSend, array $response, User $sender, User $recipient, string $messageType): void
    {
        if ($response['success']) {
            $larkSend->markAsSent($response['data']);
            Log::channel('lark_messages')->info('Lark message sent successfully', [
                'sender_id' => $sender->id,
                'sender_name' => $sender->name,
                'recipient_id' => $recipient->id,
                'recipient_name' => $recipient->name,
                'message_id' => $larkSend->id,
                'message_type' => $messageType,
                'method' => 'webhook',
            ]);
        } else {
            $larkSend->markAsFailed($response['error']);
            Log::channel('lark_messages')->error('Failed to send Lark message', [
                'sender_id' => $sender->id,
                'sender_name' => $sender->name,
                'recipient_id' => $recipient->id,
                'recipient_name' => $recipient->name,
                'message_type' => $messageType,
                'error' => $response['error'],
                'method' => 'webhook',
            ]);
        }
    }

    /**
     * Xử lý lỗi khi gửi tin nhắn
     */
    private function handleMessageError(LarkSend $larkSend, Exception $e, User $sender, User $recipient): void
    {
        $larkSend->markAsFailed($e->getMessage());
        Log::channel('lark_errors')->error('Exception while sending Lark message', [
            'sender_id' => $sender->id,
            'sender_name' => $sender->name,
            'recipient_id' => $recipient->id,
            'recipient_name' => $recipient->name,
            'message_id' => $larkSend->id,
            'error' => $e->getMessage(),
        ]);
    }

    /**
     * Log thành công
     */
    private function logSuccess(User $sender, User $recipient, LarkSend $larkSend, string $messageType): void
    {
        Log::info('Lark message sent successfully', [
            'sender_id' => $sender->id,
            'recipient_id' => $recipient->id,
            'message_id' => $larkSend->id,
            'message_type' => $messageType,
            'method' => 'webhook',
        ]);
    }

    /**
     * Log lỗi
     */
    private function logError(User $sender, User $recipient, string $error, string $messageType): void
    {
        Log::error('Failed to send Lark message', [
            'sender_id' => $sender->id,
            'recipient_id' => $recipient->id,
            'error' => $error,
            'message_type' => $messageType,
            'method' => 'webhook',
        ]);
    }

    /**
     * Gửi tin nhắn có cả text và ảnh
     */
    public function sendMessageWithImage(User $sender, User $recipient, string $message, UploadedFile $image): LarkSend
    {
        return $this->sendMessage($sender, $recipient, $message, $image);
    }





    /**
     * Upload ảnh lên Lark server và nhận image_key
     */
    public function uploadImage(string $appId, string $appSecret, UploadedFile $image): array
    {
        try {
            // Validate image
            $validation = $this->validateImage($image);
            if (!$validation['valid']) {
                return $this->createErrorResponse(implode(', ', $validation['errors']));
            }

            // Lấy access token
            $accessToken = $this->getAccessToken($appId, $appSecret);
            if (!$accessToken) {
                return $this->createErrorResponse('Failed to get Lark access token');
            }

            // Upload ảnh
            $uploadUrl = self::LARK_API_BASE_URL . self::ENDPOINTS['upload_image'];
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ])->attach(
                'image',
                file_get_contents($image->getPathname()),
                $image->getClientOriginalName()
            )->post($uploadUrl, [
                'image_type' => 'message'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $this->createSuccessResponse($data, [
                    'image_key' => $data['data']['image_key'] ?? null,
                ]);
            } else {
                return $this->createErrorResponse($response->json()['msg'] ?? 'Image upload failed');
            }
        } catch (Exception $e) {
            return $this->createErrorResponse('Image upload error: ' . $e->getMessage());
        }
    }

    /**
     * Validate ảnh upload
     */
    private function validateImage(UploadedFile $image): array
    {
        $errors = [];

        // Kiểm tra file có phải ảnh không
        if (!$image->isValid()) {
            $errors[] = 'File không hợp lệ';
        }

        // Kiểm tra định dạng
        if (!in_array($image->getMimeType(), self::IMAGE_CONFIG['allowed_mimes'])) {
            $errors[] = 'Định dạng ảnh không được hỗ trợ. Chỉ chấp nhận: ' . implode(', ', self::IMAGE_CONFIG['allowed_mimes']);
        }

        // Kiểm tra kích thước
        if ($image->getSize() > self::IMAGE_CONFIG['max_size']) {
            $maxSizeMB = self::IMAGE_CONFIG['max_size'] / (1024 * 1024);
            $errors[] = "Kích thước ảnh quá lớn. Tối đa {$maxSizeMB}MB";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Tạo response thành công
     */
    private function createSuccessResponse(array $data, array $additional = []): array
    {
        return array_merge([
            'success' => true,
            'data' => $data,
        ], $additional);
    }

    /**
     * Tạo response lỗi
     */
    private function createErrorResponse(string $error): array
    {
        return [
            'success' => false,
            'error' => $error,
        ];
    }

    /**
     * Tạo HTTP request với headers chuẩn
     */
    private function createHttpRequest(string $accessToken = null): \Illuminate\Http\Client\PendingRequest
    {
        $headers = ['Content-Type' => 'application/json'];

        if ($accessToken) {
            $headers['Authorization'] = 'Bearer ' . $accessToken;
        }

        return Http::withHeaders($headers);
    }

    /**
     * Build API URL
     */
    private function buildApiUrl(string $endpoint, bool $useFeishu = false): string
    {
        $baseUrl = $useFeishu ? self::FEISHU_API_BASE_URL : self::LARK_API_BASE_URL;
        return $baseUrl . $endpoint;
    }

    /**
     * Mask sensitive parts of webhook URL for logging
     */
    private function maskWebhookUrl(string $url): string
    {
        // Mask the webhook token/key part for security
        return preg_replace('/\/[a-zA-Z0-9_-]{20,}/', '/***MASKED***', $url);
    }



    /**
     * Gửi tin nhắn text qua Webhook URL
     */
    private function sendTextViaWebhook(string $webhookUrl, string $message, ?UploadedFile $image = null): array
    {
        try {
            Log::channel('lark_webhooks')->info('Sending text message via webhook', [
                'webhook_url' => $this->maskWebhookUrl($webhookUrl),
                'has_image' => !is_null($image),
                'message_length' => strlen($message),
            ]);

            // Nếu có ảnh, upload lên server và gửi link trong tin nhắn
            if ($image) {
                $imageUrl = $this->uploadImageToPublicStorage($image);
                if ($imageUrl) {
                    // Thêm link ảnh vào tin nhắn
                    $message .= "\n\n📷 Ảnh đính kèm: " . $imageUrl;
                    Log::channel('lark_images')->info('Image attached to text message', [
                        'image_url' => $imageUrl,
                    ]);
                } else {
                    $message .= "\n\n⚠️ Không thể tải ảnh lên. Vui lòng thử lại.";
                    Log::channel('lark_images')->warning('Failed to upload image for text message');
                }
            }

            // Gửi tin nhắn text qua webhook
            $response = Http::post($webhookUrl, [
                'msg_type' => 'text',
                'content' => [
                    'text' => $message
                ]
            ]);

            if ($response->successful()) {
                Log::channel('lark_webhooks')->info('Text message sent successfully via webhook', [
                    'webhook_url' => $this->maskWebhookUrl($webhookUrl),
                    'response' => $response->json(),
                ]);
                return $this->createSuccessResponse($response->json());
            } else {
                $error = $response->json()['msg'] ?? 'Webhook request failed';
                Log::channel('lark_webhooks')->error('Text message webhook failed', [
                    'webhook_url' => $this->maskWebhookUrl($webhookUrl),
                    'error' => $error,
                    'status_code' => $response->status(),
                ]);
                return $this->createErrorResponse($error);
            }
        } catch (Exception $e) {
            Log::channel('lark_webhooks')->error('Text message webhook exception', [
                'webhook_url' => $this->maskWebhookUrl($webhookUrl),
                'error' => $e->getMessage(),
            ]);
            return $this->createErrorResponse('Webhook error: ' . $e->getMessage());
        }
    }

    /**
     * Upload ảnh lên public storage và trả về URL
     */
    private function uploadImageToPublicStorage(UploadedFile $image): ?string
    {
        try {
            Log::channel('lark_images')->info('Starting image upload to storage', [
                'original_name' => $image->getClientOriginalName(),
                'size' => $image->getSize(),
                'mime_type' => $image->getMimeType(),
            ]);

            // Validate ảnh
            $validation = $this->validateImage($image);
            if (!$validation['valid']) {
                Log::channel('lark_images')->error('Image validation failed', [
                    'errors' => $validation['errors'],
                    'file_name' => $image->getClientOriginalName(),
                ]);
                return null;
            }

            // Tạo tên file unique
            $filename = $this->generateUniqueFilename($image);

            // Upload vào thư mục public storage
            $path = $image->storeAs(self::IMAGE_CONFIG['storage_path'], $filename, 'public');

            if ($path) {
                $imageUrl = url('storage/' . $path);
                Log::channel('lark_images')->info('Image uploaded successfully to storage', [
                    'original_name' => $image->getClientOriginalName(),
                    'stored_filename' => $filename,
                    'storage_path' => $path,
                    'public_url' => $imageUrl,
                ]);
                return $imageUrl;
            }

            Log::channel('lark_images')->error('Failed to store image file', [
                'filename' => $filename,
                'storage_path' => self::IMAGE_CONFIG['storage_path'],
            ]);
            return null;
        } catch (Exception $e) {
            Log::channel('lark_images')->error('Exception during image upload to storage', [
                'error' => $e->getMessage(),
                'file_name' => $image->getClientOriginalName() ?? 'unknown',
            ]);
            return null;
        }
    }

    /**
     * Tạo tên file unique
     */
    private function generateUniqueFilename(UploadedFile $image): string
    {
        return time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
    }

    /**
     * Gửi tin nhắn rich card qua webhook (hỗ trợ ảnh)
     */
    private function sendRichCardViaWebhook(string $webhookUrl, string $message, ?UploadedFile $image = null): array
    {
        try {
            $elements = [];

            // Thêm text element
            if (!empty($message)) {
                $elements[] = [
                    'tag' => 'div',
                    'text' => [
                        'content' => $message,
                        'tag' => 'lark_md'
                    ]
                ];
            }

            // Thêm image element nếu có
            if ($image) {
                $imageUrl = $this->uploadImageToPublicStorage($image);
                if ($imageUrl) {
                    $elements[] = [
                        'tag' => 'img',
                        'img_key' => $imageUrl,
                        'alt' => [
                            'content' => 'Ảnh đính kèm',
                            'tag' => 'plain_text'
                        ]
                    ];
                }
            }

            // Gửi interactive card
            $response = Http::post($webhookUrl, [
                'msg_type' => 'interactive',
                'card' => [
                    'elements' => $elements
                ]
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->json()['msg'] ?? 'Rich card webhook request failed',
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Rich card webhook error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Gửi tin nhắn qua App Credentials (phức tạp)
     */
    private function sendViaAppCredentials(User $recipient, string $message, ?UploadedFile $image = null): array
    {
        try {
            $appId = $recipient->getLarkAppId();
            $appSecret = $recipient->getLarkAppSecret();
            $chatId = $recipient->getLarkChatId();

            // Kiểm tra credentials trước khi gọi API
            if (empty($appId) || empty($appSecret) || empty($chatId)) {
                return [
                    'success' => false,
                    'error' => 'Missing Lark credentials: app_id, app_secret, or chat_id is empty',
                ];
            }

            // Lấy access token
            $accessToken = $this->getAccessToken($appId, $appSecret);

            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'Failed to get Lark access token',
                ];
            }

            // Nếu có ảnh, upload ảnh trước
            $imageKey = null;
            if ($image) {
                $uploadResult = $this->uploadImage($appId, $appSecret, $image);
                if (!$uploadResult['success']) {
                    return [
                        'success' => false,
                        'error' => 'Failed to upload image: ' . $uploadResult['error'],
                    ];
                }
                $imageKey = $uploadResult['image_key'];
            }

            // Chuẩn bị nội dung tin nhắn
            $msgType = $imageKey ? 'image' : 'text';
            $content = $imageKey ?
                json_encode(['image_key' => $imageKey]) :
                json_encode(['text' => $message]);

            // Gửi tin nhắn qua Lark API
            $sendMessageUrl = $this->buildApiUrl(self::ENDPOINTS['send_message'], true);

            Log::channel('lark_api')->info('Sending message via Lark API', [
                'endpoint' => self::ENDPOINTS['send_message'],
                'url' => $sendMessageUrl,
                'msg_type' => $msgType,
                'chat_id' => $chatId,
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->post($sendMessageUrl, [
                'receive_id' => $chatId,
                'msg_type' => $msgType,
                'content' => $content,
                'receive_id_type' => 'chat_id',
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->json()['msg'] ?? 'API request failed',
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'App credentials error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Gửi rich text message qua App Credentials
     */
    private function sendRichViaAppCredentials(User $recipient, string $message, ?UploadedFile $image = null): array
    {
        try {
            $appId = $recipient->getLarkAppId();
            $appSecret = $recipient->getLarkAppSecret();
            $chatId = $recipient->getLarkChatId();

            // Kiểm tra credentials trước khi gọi API
            if (empty($appId) || empty($appSecret) || empty($chatId)) {
                return [
                    'success' => false,
                    'error' => 'Missing Lark credentials: app_id, app_secret, or chat_id is empty',
                ];
            }

            // Lấy access token
            $accessToken = $this->getAccessToken($appId, $appSecret);

            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'Failed to get Lark access token',
                ];
            }

            // Nếu có ảnh, upload ảnh trước
            $imageKey = null;
            if ($image) {
                $uploadResult = $this->uploadImage($appId, $appSecret, $image);
                if (!$uploadResult['success']) {
                    return [
                        'success' => false,
                        'error' => 'Failed to upload image: ' . $uploadResult['error'],
                    ];
                }
                $imageKey = $uploadResult['image_key'];
            }

            // Tạo rich text content
            $richContent = [
                [
                    'tag' => 'text',
                    'text' => $message
                ]
            ];

            // Thêm ảnh nếu có
            if ($imageKey) {
                $richContent[] = [
                    'tag' => 'img',
                    'image_key' => $imageKey,
                    'width' => 300,
                    'height' => 300
                ];
            }

            // Gửi tin nhắn rich text
            $sendMessageUrl = $this->buildApiUrl(self::ENDPOINTS['send_message'], true);
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->post($sendMessageUrl, [
                'receive_id' => $chatId,
                'msg_type' => 'rich_text',
                'content' => json_encode([
                    'rich_text' => [
                        'elements' => $richContent
                    ]
                ]),
                'receive_id_type' => 'chat_id',
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->json()['msg'] ?? 'API request failed',
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Rich message error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Lấy access token từ Lark API
     */
    public function getAccessToken(string $appId, string $appSecret): ?string
    {
        try {
            Log::channel('lark_auth')->info('Requesting access token', [
                'app_id' => $appId,
                'endpoint' => self::ENDPOINTS['token'],
            ]);

            $url = $this->buildApiUrl(self::ENDPOINTS['token'], true);

            Log::channel('lark_api')->info('Lark API Request', [
                'endpoint' => self::ENDPOINTS['token'],
                'url' => $url,
                'method' => 'POST',
            ]);
            $response = $this->createHttpRequest()->post($url, [
                'app_id' => $appId,
                'app_secret' => $appSecret,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $token = $data['tenant_access_token'] ?? null;

                if ($token) {
                    Log::channel('lark_auth')->info('Access token obtained successfully', [
                        'app_id' => $appId,
                        'expires_in' => $data['expire'] ?? 'unknown',
                    ]);
                } else {
                    Log::channel('lark_auth')->warning('No access token in response', [
                        'app_id' => $appId,
                        'response' => $data,
                    ]);
                }

                return $token;
            }

            Log::channel('lark_auth')->error('Failed to get access token - API error', [
                'app_id' => $appId,
                'status_code' => $response->status(),
                'response' => $response->json(),
            ]);
            return null;
        } catch (Exception $e) {
            Log::channel('lark_auth')->error('Exception while getting access token', [
                'app_id' => $appId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Test kết nối Lark bot
     */
    public function testBotConnection(string $appId, string $appSecret): array
    {
        try {
            $accessToken = $this->getAccessToken($appId, $appSecret);
            
            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'Failed to get access token',
                ];
            }

            // Test bằng cách lấy thông tin bot
            $botInfoUrl = $this->buildApiUrl(self::ENDPOINTS['bot_info'], true);
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ])->get($botInfoUrl);
            
            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->json()['msg'] ?? 'Unknown error',
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Lấy thông tin chat
     */
    public function getChatInfo(string $appId, string $appSecret, string $chatId): array
    {
        try {
            $accessToken = $this->getAccessToken($appId, $appSecret);
            
            if (!$accessToken) {
                return [
                    'success' => false,
                    'error' => 'Failed to get access token',
                ];
            }

            $chatInfoUrl = $this->buildApiUrl(self::ENDPOINTS['chat_info'] . "/{$chatId}", true);
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ])->get($chatInfoUrl);
            
            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->json()['msg'] ?? 'Invalid chat ID',
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validate lark bot configuration
     */
    public function validateBotConfig(array $config): array
    {
        $errors = [];

        if (empty($config['app_id'])) {
            $errors[] = 'App ID is required';
        }

        if (empty($config['app_secret'])) {
            $errors[] = 'App Secret is required';
        }

        if (!empty($config['app_id']) && !empty($config['app_secret'])) {
            // Test bot connection
            $testResult = $this->testBotConnection($config['app_id'], $config['app_secret']);
            if (!$testResult['success']) {
                $errors[] = 'Invalid app credentials: ' . $testResult['error'];
            }
        }

        if (empty($config['chat_id'])) {
            $errors[] = 'Chat ID is required';
        } else if (!empty($config['app_id']) && !empty($config['app_secret'])) {
            // Test chat ID
            $chatResult = $this->getChatInfo($config['app_id'], $config['app_secret'], $config['chat_id']);
            if (!$chatResult['success']) {
                $errors[] = 'Invalid chat ID: ' . $chatResult['error'];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Gửi HTML/Rich Text qua webhook
     */
    private function sendHtmlViaWebhook(string $webhookUrl, string $htmlContent, ?UploadedFile $image = null): array
    {
        try {
            // Convert HTML to Lark Markdown format
            $larkMarkdown = $this->convertHtmlToLarkMarkdown($htmlContent);

            // Nếu có ảnh, thêm vào content
            if ($image) {
                $imageUrl = $this->uploadImageToPublicStorage($image);
                if ($imageUrl) {
                    $larkMarkdown .= "\n\n![Ảnh đính kèm](" . $imageUrl . ")";
                }
            }

            // Gửi rich text message
            $response = Http::post($webhookUrl, [
                'msg_type' => 'post',
                'content' => [
                    'post' => [
                        'vi_vn' => [
                            'title' => 'Rich Message',
                            'content' => [
                                [
                                    [
                                        'tag' => 'text',
                                        'text' => $larkMarkdown
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response->json()['msg'] ?? 'HTML webhook request failed',
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'HTML webhook error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Convert HTML to Lark Markdown format
     */
    private function convertHtmlToLarkMarkdown(string $html): string
    {
        // Loại bỏ các tag HTML không cần thiết
        $allowedTags = '<b><strong><i><em><u><a><br><p><h1><h2><h3><h4><h5><h6><ul><ol><li><code><pre>';
        $html = strip_tags($html, $allowedTags);

        // Convert HTML tags to Lark Markdown using predefined rules
        foreach (self::HTML_CONVERSION_RULES as $pattern => $replacement) {
            $html = preg_replace($pattern, $replacement, $html);
        }

        // Clean up extra whitespace
        $html = preg_replace('/\n\s*\n/', "\n\n", $html);
        $html = trim($html);

        return $html;
    }
}
