<?php

namespace App\Services;

use App\Models\Order;
use App\Models\SupplierOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class FulfillmentSpeedService
{
    /**
     * Lấy thống kê tốc độ fulfill theo ngày - WITH CACHING
     */
    public function getFulfillmentAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        // Tạo cache key dựa trên date range
        $cacheKey = "fulfillment_analytics_{$startDate->format('Y-m-d')}_{$endDate->format('Y-m-d')}";

        // Cache trong 2 tiếng để tránh query liên tục
        return Cache::remember($cacheKey, 7200, function () use ($startDate, $endDate) {
            $dailyStats = $this->getDailyFulfillmentStats($startDate, $endDate);
            $summary = $this->calculateSummaryStats($dailyStats);
            $weekendAnalysis = $this->analyzeWeekendPerformance($dailyStats);
            $chartData = $this->prepareChartData($dailyStats);

            return [
                'daily_stats' => $dailyStats,
                'summary' => $summary,
                'weekend_analysis' => $weekendAnalysis,
                'chart_data' => $chartData,
                'period' => [
                    'start' => $startDate->format('d/m/Y'),
                    'end' => $endDate->format('d/m/Y'),
                    'days' => $startDate->diffInDays($endDate) + 1
                ]
            ];
        });
    }

    /**
     * Lấy thống kê fulfill theo từng ngày - OPTIMIZED VERSION
     */
    private function getDailyFulfillmentStats(Carbon $startDate, Carbon $endDate): array
    {
        // Tối ưu: Lấy tất cả dữ liệu cần thiết trong 1 query duy nhất
        $dailyFulfillments = $this->getBulkDailyFulfillments($startDate, $endDate);
        $dailyAvgTimes = $this->getBulkDailyAvgTimes($startDate, $endDate);

        $stats = [];
        $currentDate = $startDate->copy();

        while ($currentDate <= $endDate) {
            $dateKey = $currentDate->format('Y-m-d');

            $fulfillments = $dailyFulfillments[$dateKey] ?? 0;
            $avgFulfillTime = $dailyAvgTimes[$dateKey] ?? 0;

            $stats[] = [
                'date' => $dateKey,
                'date_display' => $currentDate->format('d/m/Y'),
                'day_name' => $this->getVietnameseDayName($currentDate->dayOfWeek),
                'day_of_week' => $currentDate->dayOfWeek,
                'is_weekend' => $currentDate->isWeekend(),
                'fulfillments' => $fulfillments,
                'avg_fulfill_time_hours' => round($avgFulfillTime, 2),
                'avg_fulfill_time_display' => $this->formatTimeDisplay($avgFulfillTime),
                'performance_level' => $this->getPerformanceLevel($avgFulfillTime, $fulfillments),
            ];

            $currentDate->addDay();
        }

        return $stats;
    }

    /**
     * Lấy số lượng fulfillments theo ngày trong 1 query
     */
    private function getBulkDailyFulfillments(Carbon $startDate, Carbon $endDate): array
    {
        $results = SupplierOrder::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->groupBy('date')
            ->pluck('count', 'date')
            ->toArray();

        return $results;
    }

    /**
     * Lấy thời gian trung bình fulfill theo ngày trong 1 query
     */
    private function getBulkDailyAvgTimes(Carbon $startDate, Carbon $endDate): array
    {
        $results = SupplierOrder::selectRaw('
                DATE(supplier_orders.created_at) as date,
                AVG(TIMESTAMPDIFF(MINUTE, orders.created_at, supplier_orders.created_at)) / 60 as avg_hours
            ')
            ->join('orders', 'supplier_orders.order_id', '=', 'orders.id')
            ->whereBetween('supplier_orders.created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->whereNotNull('orders.created_at')
            ->whereNotNull('supplier_orders.created_at')
            ->where('supplier_orders.created_at', '>=', DB::raw('orders.created_at'))
            ->groupBy('date')
            ->pluck('avg_hours', 'date')
            ->toArray();

        return array_map(function($value) {
            return (float) $value;
        }, $results);
    }

    // Removed getAverageFulfillTimeForDay - now handled in getBulkDailyAvgTimes
    // Removed getHourlyFulfillmentBreakdown - not needed for main dashboard performance

    /**
     * Tính toán thống kê tổng quan
     */
    private function calculateSummaryStats(array $dailyStats): array
    {
        if (empty($dailyStats)) {
            return [
                'total_fulfillments' => 0,
                'avg_daily_fulfillments' => 0,
                'avg_fulfill_time_hours' => 0,
                'slowest_day' => null,
                'fastest_day' => null,
            ];
        }

        $totalFulfillments = array_sum(array_column($dailyStats, 'fulfillments'));
        $avgDailyFulfillments = round($totalFulfillments / count($dailyStats), 1);

        // Tính thời gian fulfill trung bình (chỉ tính những ngày có fulfill)
        $daysWithFulfillments = array_filter($dailyStats, fn($day) => $day['fulfillments'] > 0);
        $avgFulfillTime = 0;
        if (!empty($daysWithFulfillments)) {
            $avgFulfillTime = array_sum(array_column($daysWithFulfillments, 'avg_fulfill_time_hours')) / count($daysWithFulfillments);
        }

        // Tìm ngày chậm nhất và nhanh nhất
        $slowestDay = null;
        $fastestDay = null;
        
        if (!empty($daysWithFulfillments)) {
            $slowestDay = collect($daysWithFulfillments)->sortByDesc('avg_fulfill_time_hours')->first();
            $fastestDay = collect($daysWithFulfillments)->sortBy('avg_fulfill_time_hours')->first();
        }

        return [
            'total_fulfillments' => $totalFulfillments,
            'avg_daily_fulfillments' => $avgDailyFulfillments,
            'avg_fulfill_time_hours' => round($avgFulfillTime, 2),
            'avg_fulfill_time_display' => $this->formatTimeDisplay($avgFulfillTime),
            'slowest_day' => $slowestDay,
            'fastest_day' => $fastestDay,
        ];
    }

    /**
     * Phân tích hiệu suất cuối tuần vs ngày thường
     */
    private function analyzeWeekendPerformance(array $dailyStats): array
    {
        $weekendDays = array_filter($dailyStats, fn($day) => $day['is_weekend'] && $day['fulfillments'] > 0);
        $weekdayDays = array_filter($dailyStats, fn($day) => !$day['is_weekend'] && $day['fulfillments'] > 0);

        $weekendAvg = 0;
        $weekdayAvg = 0;

        if (!empty($weekendDays)) {
            $weekendAvg = array_sum(array_column($weekendDays, 'avg_fulfill_time_hours')) / count($weekendDays);
        }

        if (!empty($weekdayDays)) {
            $weekdayAvg = array_sum(array_column($weekdayDays, 'avg_fulfill_time_hours')) / count($weekdayDays);
        }

        $weekendSlowerPercentage = 0;
        if ($weekdayAvg > 0) {
            $weekendSlowerPercentage = (($weekendAvg - $weekdayAvg) / $weekdayAvg) * 100;
        }

        return [
            'weekend_avg' => round($weekendAvg, 1),
            'weekday_avg' => round($weekdayAvg, 1),
            'weekend_slower_percentage' => round($weekendSlowerPercentage, 1),
            'weekend_avg_display' => $this->formatTimeDisplay($weekendAvg),
            'weekday_avg_display' => $this->formatTimeDisplay($weekdayAvg),
        ];
    }

    /**
     * Chuẩn bị dữ liệu cho biểu đồ
     */
    private function prepareChartData(array $dailyStats): array
    {
        $labels = [];
        $fulfillments = [];
        $avgTimes = [];

        foreach ($dailyStats as $day) {
            $labels[] = $day['date_display'] . ' (' . $day['day_name'] . ')';
            $fulfillments[] = $day['fulfillments'];
            $avgTimes[] = $day['avg_fulfill_time_hours'];
        }

        return [
            'labels' => $labels,
            'fulfillments' => $fulfillments,
            'avg_time' => $avgTimes,
        ];
    }

    /**
     * Lấy tên ngày trong tuần bằng tiếng Việt
     */
    private function getVietnameseDayName(int $dayOfWeek): string
    {
        $days = [
            0 => 'Chủ nhật',
            1 => 'Thứ 2',
            2 => 'Thứ 3', 
            3 => 'Thứ 4',
            4 => 'Thứ 5',
            5 => 'Thứ 6',
            6 => 'Thứ 7'
        ];

        return $days[$dayOfWeek] ?? 'Không xác định';
    }

    /**
     * Định dạng hiển thị thời gian
     */
    private function formatTimeDisplay(float $hours): string
    {
        if ($hours < 1) {
            return round($hours * 60) . ' phút';
        } elseif ($hours < 24) {
            return round($hours, 1) . ' giờ';
        } else {
            $days = floor($hours / 24);
            $remainingHours = $hours - ($days * 24);

            if ($remainingHours < 0.1) {
                return $days . ' ngày';
            } elseif ($remainingHours < 1) {
                $minutes = round($remainingHours * 60);
                return $days . ' ngày ' . $minutes . ' phút';
            } else {
                $roundedHours = round($remainingHours, 1);
                return $days . ' ngày ' . $roundedHours . ' giờ';
            }
        }
    }

    /**
     * Đánh giá mức độ hiệu suất
     */
    private function getPerformanceLevel(float $avgTime, int $fulfillments): string
    {
        if ($fulfillments == 0) return 'no_data';
        
        if ($avgTime <= 14) return 'excellent';      // <= 2 giờ
        if ($avgTime <= 20) return 'good';           // <= 6 giờ  
        if ($avgTime <= 24) return 'average';       // <= 12 giờ
        if ($avgTime <= 28) return 'slow';          // <= 1 ngày
        return 'very_slow';                         // > 1 ngày
    }
}
