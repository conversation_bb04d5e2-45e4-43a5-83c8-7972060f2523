<?php

namespace App\Services;

use App\Settings\ToolSeting;
use GuzzleHttp\Client;
use GuzzleHttp\Cookie\CookieJar;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class MerchSpyService
{
    private $client;
    private $toolSeting;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://tool.merchintel.com/',
            'timeout'  => 30.0,
        ]);
        $this->toolSeting = app(ToolSeting::class);
    }

    public function getTrendingProducts($page = 1, $limit = 60)
    {
        $today = now();
        $oneMonthAgo = $today->copy()->subMonth();
    
        $query = [
            'page' => $page,
            'status' => 'live',
            'sortField' => 'trends',
            'tTo' => $today->format('d-m-Y'),
            'tFrom' => $oneMonthAgo->format('d-m-Y'),
            'rFrom' => 1,
            'rTo' => 300000,
            'limit' => $limit
        ];

        try {
            $response = $this->client->request('GET', 'products/search', [
                'headers' => $this->getHeaders('https://tool.merchintel.com/a/trending'),
                'query' => $query,
                //'cookies' => $this->createCookieJar()
            ]);
           
            $body = $response->getBody()->getContents();
            $newCookies = $this->extractNewCookie($response);
            if (!empty($newCookies)) {
                $oldCookie = $this->toolSeting->cookie ?? '';
                $mergedCookie = $this->mergeCookies($oldCookie, $newCookies);
                $this->saveCookie($mergedCookie);
             
            } else {
             
            }
           
            return json_decode($body, true);
        } catch (GuzzleException $e) {
            dd( $e->getMessage());
            Log::error('Error fetching trending products: ' . $e->getMessage());
            return null;
        }
    }
    private function extractNewCookie($response)
    {
        $setCookieHeaders = $response->getHeader('Set-Cookie');
        $newCookies = [];
        foreach ($setCookieHeaders as $header) {
            $parts = explode(';', $header);
            $cookiePart = explode('=', trim($parts[0]), 2);
            if (count($cookiePart) == 2) {
                $newCookies[$cookiePart[0]] = $cookiePart[1];
            }
        }
        return $newCookies;
    }
    
    private function mergeCookies($oldCookie, $newCookies)
    {
        $oldCookieParts = explode('; ', $oldCookie);
        $oldCookieArray = [];
        foreach ($oldCookieParts as $part) {
            $cookiePart = explode('=', $part, 2);
            if (count($cookiePart) == 2) {
                $oldCookieArray[$cookiePart[0]] = $cookiePart[1];
            }
        }
    
        // Merge new cookies into old cookies, overwriting if necessary
        $mergedCookies = array_merge($oldCookieArray, $newCookies);
    
        // Convert back to string
        $cookieString = '';
        foreach ($mergedCookies as $name => $value) {
            $cookieString .= $name . '=' . $value . '; ';
        }
        return rtrim($cookieString, '; ');
    }
    private function saveCookie(string $cookie): void
    {
        $this->toolSeting->saveCookie($cookie);
    }
    private function getHeaders($referer): array
    {
        return [
            'accept' => 'application/json, text/plain, */*',
            'accept-language' => 'en-US,en;q=0.9',
            'captcha' => $this->toolSeting->captcha,
            'me' => '9866ab7249152dee5edfb8aaf319bb57',
            'referer' => $referer,
            'sec-ch-ua' => '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'same-origin',
            'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36',
            'cookies' => $this->toolSeting->cookie,
        ];
    }

    private function createCookieJar(): CookieJar
    {
        $cookieString = $this->toolSeting->cookie;
        $cookieJar = new CookieJar();
        $cookies = explode(';', $cookieString);
        foreach ($cookies as $cookie) {
            $parts = explode('=', $cookie, 2);
            if (count($parts) == 2) {
                $name = trim($parts[0]);
                $value = trim($parts[1]);
                $cookieJar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                    'Name' => $name,
                    'Value' => $value,
                    'Domain' => 'tool.merchintel.com',
                    'Path' => '/'
                ]));
            }
        }
        return $cookieJar;
    }
}