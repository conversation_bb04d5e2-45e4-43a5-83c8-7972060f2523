<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class TextToSpeechService
{
    protected $apiKey;
    protected $apiUrl = 'https://api.fpt.ai/hmi/tts/v5';

    public function __construct()
    {
        $this->apiKey = config('services.fpt.tts_api_key');
    }

    public function textToSpeech($text)
    {
        try {
            Log::info('🎯 Calling FPT TTS API', ['text' => $text]);

            $curl = curl_init();
            
            curl_setopt_array($curl, array(
                CURLOPT_URL => $this->apiUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $text,
                CURLOPT_HTTPHEADER => array(
                    'api-key: ' . $this->apiKey,
                    'voice: banmai',
                    'speed: -0.5'
                ),
            ));

            $response = curl_exec($curl);
            $err = curl_error($curl);
            
            curl_close($curl);

            if ($err) {
                throw new \Exception('CURL Error: ' . $err);
            }

            $data = json_decode($response, true);
            Log::info('🎯 FPT API Response', ['response' => $data]);

            if (!isset($data['async'])) {
                throw new \Exception('No audio URL in response');
            }

            return $data['async'];

        } catch (\Exception $e) {
            Log::error('❌ Text to Speech Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }
} 