<?php

namespace App\Services\Scrapers\Zyte;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ZyteService
{
    protected $apiKey;
    protected $baseUrl = 'https://api.zyte.com/v1/extract';

    public function __construct()
    {
        $this->apiKey = 'db83b86b0ecc45f8937b3538c21b909a';
    }

    public function scrapeProduct($url)
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, '')
                ->withHeaders(['Accept-Encoding' => 'gzip'])
                ->post($this->baseUrl, [
                    'url' => $url,
                    'product' => true
                ]);

            if (!$response->successful()) {
                Log::error("Zyte API error", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                throw new \Exception("Failed to fetch data from Zyte");
            }

            return $response->json()['product'];
        } catch (\Exception $e) {
            Log::error("Zyte scraping error", [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function scrapeUrl($url)
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, '')
                ->withHeaders([
                    'Content-Type' => 'application/json'
                ])
                ->post($this->baseUrl, [
                    'url' => $url,
                    'httpResponseBody' => true
                ]);

            if (!$response->successful()) {
                Log::error("Zyte API error", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                throw new \Exception('Zyte API request failed: ' . $response->status());
            }

            $data = $response->json();

            if (empty($data['httpResponseBody'])) {
                throw new \Exception('No response body returned from Zyte');
            }

            // Decode base64 response body
            $html = base64_decode($data['httpResponseBody']);

            if (!$html) {
                throw new \Exception('Failed to decode response body');
            }

            return $html;
        } catch (\Exception $e) {
            Log::error("Zyte scraping error", [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function scrapeUniversalProduct($url)
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, '')
                ->withHeaders([
                    'Content-Type' => 'application/json'
                ])
                ->post($this->baseUrl, [
                    'url' => $url,
                    'product' => true,
                    'productOptions' => [
                        'extractFrom' => 'httpResponseBody'
                    ],
                    'httpResponseBody' => true
                ]);

            if (!$response->successful()) {
                Log::error("Zyte API error", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                throw new \Exception('Zyte API request failed: ' . $response->status());
            }

            $data = $response->json();

            // Kiểm tra và trả về dữ liệu sản phẩm
            if (empty($data['product'])) {
                throw new \Exception('No product data returned from Zyte');
            }

            return $data;
        } catch (\Exception $e) {
            Log::error("Zyte scraping error", [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function scrapeWithJS($url) 
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, '')
                ->withHeaders([
                    'Accept-Encoding' => 'gzip'
                ])
                ->post($this->baseUrl, [
                    'url' => $url,
                    'browserHtml' => true,
                
                ]);
    
            if (!$response->successful()) {
                Log::error("Zyte API error", [
                    'status' => $response->status(), 
                    'body' => $response->body()
                ]);
                throw new \Exception('Zyte API request failed: ' . $response->status());
            }
    
            // Parse response
            $data = $response->body();
            return $data;
    
        } catch (\Exception $e) {
            Log::error("Zyte scraping error", [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
