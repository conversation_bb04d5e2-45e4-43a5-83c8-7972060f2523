<?php

namespace App\Services;

use App\Models\SellerFinance;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use Carbon\Carbon;

class InvoiceService
{
    public function generateInvoice(SellerFinance $finance): Invoice
    {
        // Tạo hoá đơn
        $invoice = Invoice::create([
            'seller_finance_id' => $finance->id,
            'user_id' => $finance->seller_id,
            'invoice_number' => 'INV-' . Carbon::now()->format('YmdHis'),
            'issue_date' => Carbon::now(),
            'due_date' => Carbon::now()->addDays(30),
            'status' => 'pending',
            'notes' => "Báo cáo tài chính chi tiết tháng " . $finance->month,
        ]);

        // Thêm các khoản thu nhập
        $incomeItems = [
            [
                'description' => 'Lương cơ bản',
                'amount' => $finance->base_salary,
            ],
            [
                'description' => 'Hoa hồng doanh thu',
                'amount' => $finance->commission,
            ],
            [
                'description' => 'Thưởng vị trí',
                'amount' => $finance->position_bonus,
            ],
            [
                'description' => 'Thưởng hiệu suất',
                'amount' => $finance->performance_bonus,
            ],
        ];

        // Chỉ thêm các khoản có số tiền > 0
        foreach ($incomeItems as $item) {
            if ($item['amount'] > 0) {
                $invoice->items()->create($item);
            }
        }

        // Tính tổng tiền từ các khoản thu nhập
        $totalIncome = $finance->base_salary + 
                      $finance->commission + 
                      $finance->position_bonus + 
                      $finance->performance_bonus;

        // Cập nhật tổng tiền
        $invoice->update([
            'amount' => $totalIncome
        ]);

        return $invoice;
    }

    public function cancel(Invoice $invoice): bool
    {
        if (in_array($invoice->status, ['paid', 'cancelled'])) {
            return false;
        }

        return $invoice->update([
            'status' => 'cancelled'
        ]);
    }

    public function approve(Invoice $invoice): bool
    {
        if ($invoice->status !== 'pending') {
            return false;
        }

        return $invoice->update([
            'status' => 'approved'
        ]);
    }

    public function confirm(Invoice $invoice): bool
    {
        if ($invoice->status !== 'approved') {
            return false;
        }

        return $invoice->update([
            'status' => 'confirmed'
        ]);
    }

    public function pay(Invoice $invoice): bool
    {
        if ($invoice->status !== 'confirmed') {
            return false;
        }

        return $invoice->update([
            'status' => 'paid'
        ]);
    }
} 