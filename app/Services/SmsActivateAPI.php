<?php

namespace App\Services;

class SmsActivateAPI {
    private $api_key;
    private $url = "https://sms-activate.org/stubs/handler_api.php";

    public function __construct(){
        $this->api_key = env('SMS_ACTIVATE_API_KEY');
    }

    private function request($data){
        $url = $this->url . '?' . http_build_query($data);
        $result = file_get_contents($url);
        return $result;
    }

    public function getBalance(){
        $data = [
            'api_key' => $this->api_key,
            'action' => 'getBalance'
        ];
        return $this->request($data);
    }

    public function getNumber($service, $country){
        $data = [
            'api_key' => $this->api_key,
            'action' => 'getNumber',
            'service' => $service,
            'country' => $country
        ];
        return $this->request($data);
    }

    public function getNumberV2($service, $country, $maxPrice){
        $data = [
            'api_key' => $this->api_key,
            'action' => 'getNumberV2',
            'service' => $service,
            'country' => $country,
            'maxPrice' => $maxPrice
        ];
        return $this->request($data);
    }

    public function getRentStatus($rentId){
        $data = [
            'api_key' => $this->api_key,
            'action' => 'getRentStatus',
            'id' => $rentId
        ];
        return $this->request($data);
    }

    public function getActiveActivations(){
        $data = [
            'api_key' => $this->api_key,
            'action' => 'getActiveActivations'
        ];
        return $this->request($data);
    }

    public function cancelActivation($activationId){
        $data = [
            'api_key' => $this->api_key,
            'action' => 'setStatus',
            'status' => 8,
            'id' => $activationId
        ];
        return $this->request($data);
    }
}
