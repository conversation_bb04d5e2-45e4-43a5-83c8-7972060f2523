<?php

namespace App\Services;

use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\SupplierOrder;
use App\Models\SellerFundRequest;
use App\Models\DesignJob;
use App\Models\TikTokPayment;
use App\Models\PayoutTransaction;
use App\Models\Production;
use App\Enums\OrderStatus;
use App\Enums\DesignJobStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

/**
 * Optimized service for seller statistics calculations
 * Reduces N+1 queries and implements efficient caching
 */
class OptimizedSellerStatisticsService
{
    protected Carbon $startDate;
    protected Carbon $endDate;
    protected Collection $sellers;
    protected array $cachedData = [];
    
    // Cache configuration - Optimized for Redis
    const CACHE_TTL = 7200; // 2 hours for Redis (can be longer)
    const CACHE_PREFIX = 'seller_stats_redis_';
    const CACHE_TAG = 'seller_statistics';
    
    public function __construct(Collection $sellers, Carbon $startDate, Carbon $endDate)
    {
        $this->sellers = $sellers;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }
    
    /**
     * Get all seller data in a single optimized call - Redis optimized
     */
    public function getAllSellerData(): array
    {
        $cacheKey = $this->getCacheKey('all_data');

        return Cache::tags([self::CACHE_TAG])->remember($cacheKey, self::CACHE_TTL, function () {
            return $this->calculateAllSellerData();
        });
    }
    
    /**
     * Calculate all seller data using optimized queries
     */
    protected function calculateAllSellerData(): array
    {
        $sellerIds = $this->sellers->pluck('id')->toArray();
        
        if (empty($sellerIds)) {
            return [];
        }
        
        // Get all data in batch queries
        $ordersData = $this->getBatchOrdersData($sellerIds);
        $supplierOrdersData = $this->getBatchSupplierOrdersData($sellerIds);
        $designJobsData = $this->getBatchDesignJobsData($sellerIds);
        $fundRequestsData = $this->getBatchFundRequestsData($sellerIds);
        $paymentsData = $this->getBatchPaymentsData($sellerIds);
        $productionData = $this->getBatchProductionData($sellerIds);
        
        // Combine all data by seller
        $result = [];
        foreach ($sellerIds as $sellerId) {
            $result[$sellerId] = [
                'orders' => $ordersData[$sellerId] ?? $this->getEmptyOrdersData(),
                'supplier_orders' => $supplierOrdersData[$sellerId] ?? $this->getEmptySupplierOrdersData(),
                'design_jobs' => $designJobsData[$sellerId] ?? $this->getEmptyDesignJobsData(),
                'fund_requests' => $fundRequestsData[$sellerId] ?? $this->getEmptyFundRequestsData(),
                'payments' => $paymentsData[$sellerId] ?? $this->getEmptyPaymentsData(),
                'production' => $productionData[$sellerId] ?? $this->getEmptyProductionData(),
            ];
        }
        
        return $result;
    }
    
    /**
     * Get batch orders data for all sellers
     */
    protected function getBatchOrdersData(array $sellerIds): array
    {
        $ordersQuery = Order::whereIn('orders.seller_id', $sellerIds)
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate])
            ->select([
                'orders.seller_id',
                'orders.status',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(CASE WHEN orders.status IN ("Completed", "Processing") THEN 1 ELSE 0 END) as revenue_orders_count')
            ])
            ->groupBy(['orders.seller_id', 'orders.status']);

        $revenueQuery = Order::whereIn('orders.seller_id', $sellerIds)
            ->whereIn('orders.status', [OrderStatus::Completed->value, OrderStatus::Processing->value])
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate])
            ->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->select([
                'orders.seller_id',
                'orders.status',
                DB::raw('SUM(order_items.total) as revenue'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count')
            ])
            ->groupBy(['orders.seller_id', 'orders.status']);
            
        $ordersByStatus = $ordersQuery->get()->groupBy('seller_id');
        $revenueByStatus = $revenueQuery->get()->groupBy('seller_id');
        
        $result = [];
        foreach ($sellerIds as $sellerId) {
            $sellerOrders = $ordersByStatus->get($sellerId, collect());
            $sellerRevenue = $revenueByStatus->get($sellerId, collect());
            
            $totalOrders = $sellerOrders->sum('count');
            $completedOrders = $sellerOrders->where('status', OrderStatus::Completed->value)->sum('count');
            $processingOrders = $sellerOrders->where('status', OrderStatus::Processing->value)->sum('count');
            $cancelledOrders = $sellerOrders->where('status', OrderStatus::Cancelled->value)->sum('count');
            
            // Updated to match SellerInvoice logic: Completed + Processing = completed_revenue
            $completedRevenue = $sellerRevenue->whereIn('status', [OrderStatus::Completed->value, OrderStatus::Processing->value])->sum('revenue');
            $totalRevenue = $sellerRevenue->sum('revenue');
            
            $result[$sellerId] = [
                'total_orders' => $totalOrders,
                'completed_orders' => $completedOrders,
                'processing_orders' => $processingOrders,
                'cancelled_orders' => $cancelledOrders,
                'pending_orders' => max(0, $totalOrders - $completedOrders - $processingOrders - $cancelledOrders),
                'completed_revenue' => $completedRevenue,
                'total_revenue' => $totalRevenue,
                'average_order_value' => $completedOrders > 0 ? $completedRevenue / $completedOrders : 0,
            ];
        }
        
        return $result;
    }
    
    /**
     * Get batch supplier orders data - Updated to match SellerInvoice logic (all except Cancelled)
     */
    protected function getBatchSupplierOrdersData(array $sellerIds): array
    {
        $supplierOrders = SupplierOrder::whereIn('supplier_orders.seller_id', $sellerIds)
            ->whereBetween('supplier_orders.created_at', [$this->startDate, $this->endDate])
            ->select([
                'supplier_orders.seller_id',
                'supplier_orders.status',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(CASE WHEN supplier_orders.status != "Cancelled" THEN supplier_orders.base_cost ELSE 0 END) as completed_cost'),
                DB::raw('SUM(supplier_orders.base_cost) as total_cost')
            ])
            ->groupBy(['supplier_orders.seller_id', 'supplier_orders.status'])
            ->get()
            ->groupBy('seller_id');

        $result = [];
        foreach ($sellerIds as $sellerId) {
            $sellerSupplierOrders = $supplierOrders->get($sellerId, collect());

            $result[$sellerId] = [
                'total_orders' => $sellerSupplierOrders->sum('count'),
                'completed_orders' => $sellerSupplierOrders->where('status', '!=', 'Cancelled')->sum('count'),
                'completed_cost' => $sellerSupplierOrders->sum('completed_cost'),
                'total_cost' => $sellerSupplierOrders->sum('total_cost'),
            ];
        }

        return $result;
    }
    
    /**
     * Get batch design jobs data
     */
    protected function getBatchDesignJobsData(array $sellerIds): array
    {
        $designJobs = DesignJob::whereIn('created_by', $sellerIds)
            ->where('status', DesignJobStatus::COMPLETED)
            ->whereBetween('completed_at', [$this->startDate, $this->endDate])
            ->select([
                'created_by as seller_id',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(COALESCE(price, 0) + COALESCE(rush_fee, 0)) as total_fees')
            ])
            ->groupBy('created_by')
            ->get()
            ->keyBy('seller_id');
            
        $result = [];
        foreach ($sellerIds as $sellerId) {
            $sellerDesignJobs = $designJobs->get($sellerId);
            
            $result[$sellerId] = [
                'count' => $sellerDesignJobs->count ?? 0,
                'total_fees' => $sellerDesignJobs->total_fees ?? 0,
            ];
        }
        
        return $result;
    }
    
    /**
     * Get batch fund requests data
     */
    protected function getBatchFundRequestsData(array $sellerIds): array
    {
        $fundRequests = SellerFundRequest::whereIn('seller_id', $sellerIds)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->select([
                'seller_id',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(amount) as total_amount')
            ])
            ->groupBy('seller_id')
            ->get()
            ->keyBy('seller_id');
            
        $result = [];
        foreach ($sellerIds as $sellerId) {
            $sellerFundRequests = $fundRequests->get($sellerId);
            
            $result[$sellerId] = [
                'count' => $sellerFundRequests->count ?? 0,
                'total_amount' => $sellerFundRequests->total_amount ?? 0,
            ];
        }
        
        return $result;
    }
    
    /**
     * Get batch payments data (TikTok and Bank)
     */
    protected function getBatchPaymentsData(array $sellerIds): array
    {
        // Get all store IDs and bank accounts for these sellers
        $storeData = DB::table('stores')
            ->whereIn('owner_id', $sellerIds)
            ->select('owner_id as seller_id', 'id as store_id', 'bank_account')
            ->get()
            ->groupBy('seller_id');
            
        $result = [];
        foreach ($sellerIds as $sellerId) {
            $sellerStores = $storeData->get($sellerId, collect());
            $storeIds = $sellerStores->pluck('store_id')->toArray();
            $bankAccounts = $sellerStores->pluck('bank_account')->filter()->toArray();
            
            // TikTok payments
            $tiktokPayments = [
                'total_amount' => 0,
                'count' => 0
            ];
            
            if (!empty($storeIds)) {
                $tiktokData = TikTokPayment::whereIn('store_id', $storeIds)
                    ->whereBetween('paid_time', [$this->startDate, $this->endDate])
                    ->where('status', 'PAID')
                    ->selectRaw('SUM(settlement_amount) as total_amount, COUNT(*) as count')
                    ->first();
                    
                $tiktokPayments = [
                    'total_amount' => $tiktokData->total_amount ?? 0,
                    'count' => $tiktokData->count ?? 0
                ];
            }
            
            // Bank payments
            $bankPayments = [
                'total_amount' => 0,
                'count' => 0
            ];
            
            if (!empty($bankAccounts)) {
                $bankData = PayoutTransaction::whereIn('card_no', $bankAccounts)
                    ->where('type', 'Receive')
                    ->where('status', 'Success')
                    ->whereBetween('time', [$this->startDate, $this->endDate])
                    ->selectRaw('SUM(amount) as total_amount, COUNT(*) as count')
                    ->first();
                    
                $bankPayments = [
                    'total_amount' => $bankData->total_amount ?? 0,
                    'count' => $bankData->count ?? 0
                ];
            }
            
            $result[$sellerId] = [
                'tiktok' => $tiktokPayments,
                'bank' => $bankPayments,
            ];
        }
        
        return $result;
    }
    
    /**
     * Get batch production data
     */
    protected function getBatchProductionData(array $sellerIds): array
    {
        $productions = Production::whereIn('seller_id', $sellerIds)
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->where('status', 'completed')
            ->with('blank')
            ->get()
            ->groupBy('seller_id');
            
        $result = [];
        foreach ($sellerIds as $sellerId) {
            $sellerProductions = $productions->get($sellerId, collect());
            
            $totalCost = $sellerProductions->sum(function ($production) {
                return $production->calculateProductionCost();
            });
            
            $result[$sellerId] = [
                'count' => $sellerProductions->count(),
                'total_cost' => $totalCost,
            ];
        }
        
        return $result;
    }
    
    /**
     * Generate cache key - Optimized for Redis
     */
    protected function getCacheKey(string $suffix): string
    {
        // Redis can handle longer keys efficiently
        $sellerIds = $this->sellers->pluck('id')->sort()->implode(',');
        $dateRange = $this->startDate->toDateString() . '_to_' . $this->endDate->toDateString();

        return self::CACHE_PREFIX . $suffix . ':' . md5($sellerIds) . ':' . $dateRange;
    }
    
    // Empty data structures for consistency
    protected function getEmptyOrdersData(): array
    {
        return [
            'total_orders' => 0,
            'completed_orders' => 0,
            'processing_orders' => 0,
            'cancelled_orders' => 0,
            'pending_orders' => 0,
            'completed_revenue' => 0,
            'total_revenue' => 0,
            'average_order_value' => 0,
        ];
    }
    
    protected function getEmptySupplierOrdersData(): array
    {
        return [
            'total_orders' => 0,
            'completed_orders' => 0,
            'completed_cost' => 0,
            'total_cost' => 0,
        ];
    }
    
    protected function getEmptyDesignJobsData(): array
    {
        return [
            'count' => 0,
            'total_fees' => 0,
        ];
    }
    
    protected function getEmptyFundRequestsData(): array
    {
        return [
            'count' => 0,
            'total_amount' => 0,
        ];
    }
    
    protected function getEmptyPaymentsData(): array
    {
        return [
            'tiktok' => ['total_amount' => 0, 'count' => 0],
            'bank' => ['total_amount' => 0, 'count' => 0],
        ];
    }
    
    protected function getEmptyProductionData(): array
    {
        return [
            'count' => 0,
            'total_cost' => 0,
        ];
    }
    
    /**
     * Clear cache for specific sellers - Redis optimized
     */
    public function clearCache(): void
    {
        // Clear specific cache key
        Cache::tags([self::CACHE_TAG])->forget($this->getCacheKey('all_data'));
    }

    /**
     * Clear all seller statistics cache
     */
    public static function clearAllCache(): void
    {
        Cache::tags([self::CACHE_TAG])->flush();
    }
}
