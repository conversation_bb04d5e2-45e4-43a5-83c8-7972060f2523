<?php

namespace App\Services;

use App\Models\Store;
use App\Models\Order;
use App\Models\SupplierOrder;
use App\Models\PayoutTransaction;
use App\Models\TikTokPayment;
use App\Models\Production;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

/**
 * Service class để xử lý các tính toán liên quan đến Store
 *
 * Tái sử dụng logic từ SellerService cho Store calculations
 */
class StoreService extends BaseFinancialService
{
    protected Store $store;

    public function __construct(Store $store, Carbon $startDate, Carbon $endDate)
    {
        parent::__construct($startDate, $endDate);
        $this->store = $store;
    }

    /**
     * Tính tổng số đơn hàng của store
     */
    public function calculateTotalOrders(): int
    {
        $query = $this->store->orders();
        $query = $this->applyDateFilter($query, 'orders.created_at');
        return $query->count();
    }

    /**
     * T<PERSON>h tổng doanh thu của store (từ đơn hàng hoàn thành)
     */
    public function calculateTotalRevenue(): float
    {
        $query = $this->store->orders()
            ->where('orders.status', 'Completed')
            ->join('order_items', 'orders.id', '=', 'order_items.order_id');

        $query = $this->applyDateFilter($query, 'orders.created_at');
        return $query->sum('order_items.total');
    }

    /**
     * Tính tổng chi phí fulfillment của store
     */
    public function calculateFulfillmentCost(): float
    {
        $query = $this->store->orders()
            ->join('supplier_orders', 'orders.id', '=', 'supplier_orders.order_id')
            ->where('supplier_orders.status', '!=', 'Cancelled');

        $query = $this->applyDateFilter($query, 'orders.created_at');
        return $query->sum('supplier_orders.base_cost');
    }

    /**
     * Tính số đơn hàng theo trạng thái
     */
    public function calculateOrdersByStatus(): array
    {
        $baseQuery = $this->store->orders();
        $baseQuery = $this->applyDateFilter($baseQuery, 'orders.created_at');

        $ordersByStatus = (clone $baseQuery)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        return [
            'total' => array_sum($ordersByStatus),
            'completed' => $ordersByStatus['Completed'] ?? 0,
            'failed' => ($ordersByStatus['Cancelled'] ?? 0) + ($ordersByStatus['Failed'] ?? 0) + ($ordersByStatus['Refunded'] ?? 0),
            'pending' => ($ordersByStatus['Pending'] ?? 0) + ($ordersByStatus['Processing'] ?? 0) + ($ordersByStatus['Shipped'] ?? 0),
            'breakdown' => $ordersByStatus
        ];
    }

    /**
     * Tính doanh thu theo trạng thái đơn hàng
     */
    public function calculateRevenueByStatus(): array
    {
        $completedRevenue = $this->calculateRevenueByOrderStatus(['Completed']);
        $pendingRevenue = $this->calculateRevenueByOrderStatus(['Pending', 'Processing', 'Shipped']);

        return [
            'completed' => $completedRevenue,
            'pending' => $pendingRevenue,
            'total' => $completedRevenue + $pendingRevenue
        ];
    }

    /**
     * Helper method để tính doanh thu theo trạng thái cụ thể
     */
    private function calculateRevenueByOrderStatus(array $statuses): float
    {
        $query = $this->store->orders()
            ->whereIn('orders.status', $statuses)
            ->join('order_items', 'orders.id', '=', 'order_items.order_id');

        $query = $this->applyDateFilter($query, 'orders.created_at');
        return $query->sum('order_items.total');
    }

    /**
     * Tính chi phí quảng cáo của store (thông qua seller)
     */
    public function calculateAdvertisingCost(): float
    {
        $seller = $this->store->seller;
        if (!$seller) {
            return 0;
        }

        $query = $seller->fundRequests()->where('status', 'approved');
        $query = $this->applyDateFilter($query, 'created_at');
        return $query->sum('amount');
    }

    /**
     * Tính chi phí thiết kế của store (thông qua seller)
     */
    public function calculateDesignCost(): float
    {
        $seller = $this->store->seller;
        if (!$seller) {
            return 0;
        }

        // Sử dụng relationship createdDesignJobs() cho seller (thay vì designJobs() dành cho designer)
        $query = $seller->createdDesignJobs()->where('status', 'COMPLETED');
        $query = $this->applyDateFilter($query, 'design_jobs.completed_at');
        return $query->sum(\DB::raw('COALESCE(price, 0) + COALESCE(rush_fee, 0)'));
    }

    /**
     * Tính chi phí in ấn của store (thông qua seller)
     */
    public function calculatePrintCost(): float
    {
        $seller = $this->store->seller;
        if (!$seller) {
            return 0;
        }

        $query = Production::query()
            ->where('seller_id', $seller->id)
            ->where('status', 'completed');

        $query = $this->applyDateFilter($query, 'created_at');

        return $query->with('blank')->get()->sum(function ($production) {
            return $production->calculateProductionCost();
        });
    }

    /**
     * Tính bank payout của store
     */
    public function calculateBankPayout(): array
    {
        $bankAccount = $this->store->bank_account;
        if (!$bankAccount) {
            return ['sum' => 0, 'count' => 0];
        }

        $payoutQuery = PayoutTransaction::query()
            ->where('card_no', $bankAccount)
            ->where('type', 'Receive')
            ->where('status', 'Success');

        $payoutQuery = $this->applyDateFilter($payoutQuery, 'time');

        return [
            'sum' => $payoutQuery->sum('amount'),
            'count' => $payoutQuery->count(),
        ];
    }

    /**
     * Tính TikTok payout của store
     */
    public function calculateTiktokPayout(): array
    {
        $tiktokPayoutQuery = TikTokPayment::query()
            ->where('store_id', $this->store->id)
            ->where('status', 'PAID');

        $payoutQuery = $this->applyDateFilter($tiktokPayoutQuery, 'paid_time');

        return [
            'sum' => $payoutQuery->sum('settlement_amount'),
            'count' => $payoutQuery->count(),
        ];
    }

    /**
     * Tính toán tất cả dữ liệu tài chính cho store
     */
    public function calculateFinancialData(): array
    {
        $orders = $this->calculateOrdersByStatus();
        $revenue = $this->calculateRevenueByStatus();

        // Chi phí chi tiết
        $fulfillmentCost = $this->calculateFulfillmentCost();
        $advertisingCost = $this->calculateAdvertisingCost();
        $designCost = $this->calculateDesignCost();
        $printCost = $this->calculatePrintCost();
        $totalCost = $fulfillmentCost + $advertisingCost + $designCost + $printCost;

        // Payout data
        $bankPayout = $this->calculateBankPayout();
        $tiktokPayout = $this->calculateTiktokPayout();

        // Tính toán lợi nhuận
        $grossProfit = $revenue['completed'] - $totalCost;
        $netProfit = $grossProfit; // Store level không có previous loss
        $adjustedProfit = $bankPayout['sum'] - $totalCost;

        // Tính toán các chỉ số
        $successRate = $orders['total'] > 0 ? ($orders['completed'] / $orders['total']) * 100 : 0;
        $profitMargin = $revenue['completed'] > 0 ? ($grossProfit / $revenue['completed']) * 100 : 0;
        $avgOrderValue = $orders['completed'] > 0 ? $revenue['completed'] / $orders['completed'] : 0;

        return [
            'orders' => $orders,
            'revenue' => $revenue,
            'costs' => [
                'fulfillment' => $fulfillmentCost,
                'advertising' => $advertisingCost,
                'design' => $designCost,
                'print' => $printCost,
                'total' => $totalCost,
            ],
            'payout' => [
                'bank' => $bankPayout,
                'tiktok' => $tiktokPayout,
            ],
            'profit' => [
                'gross' => $grossProfit,
                'net' => $netProfit,
                'adjusted' => $adjustedProfit,
                'margin' => $profitMargin,
            ],
            'metrics' => [
                'success_rate' => $successRate,
                'avg_order_value' => $avgOrderValue,
                'on_hold' => $this->store->tiktok_payout_on_hold ?? 0,
            ]
        ];
    }
}
