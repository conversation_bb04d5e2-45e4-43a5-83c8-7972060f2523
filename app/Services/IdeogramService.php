<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\IdeogramAccount;

class IdeogramService
{
    protected $client;
    protected $currentAccount;
    protected $serviceConfig;
    protected $baseUrl = 'https://ideogram.ai/api';

    // Thời gian chờ mặc định
    const DEFAULT_TIMEOUT = 60;
    const MAX_GENERATION_TIMEOUT = 40000; // 40 giây
    const POLL_INTERVAL = 2000; // 2 giây
    const WAIT_SAFETY_MARGIN = 1000; // 1 giây

    public function __construct(IdeogramAccount $account = null)
    {
        $this->client = new Client([
            'timeout' => config('ideogram.api.timeout', self::DEFAULT_TIMEOUT),
            'connect_timeout' => 10,
            'verify' => true,
            'http_errors' => false,
            'allow_redirects' => [
                'max' => 3,
                'strict' => true,
                'referer' => true,
                'protocols' => ['https']
            ],
            'headers' => [
                'Accept-Encoding' => 'gzip, deflate, br',
            ],
            'curl' => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_2_0,
                CURLOPT_ENCODING => '', // Accept all encodings
            ]
        ]);

        // Nếu không truyền account, tự động lấy account tốt nhất
        if ($account) {
            $this->currentAccount = $account;
        } else {
            $this->currentAccount = IdeogramAccount::getDefaultOrBest();
        }

        if (!$this->currentAccount) {
            throw new \Exception('Không tìm thấy tài khoản Ideogram nào khả dụng. Vui lòng thêm tài khoản trong admin panel.');
        }

        if (!$this->currentAccount->hasValidCredentials()) {
            throw new \Exception('Tài khoản Ideogram "' . $this->currentAccount->name . '" không có thông tin xác thực hợp lệ');
        }

        $this->serviceConfig = [
            'user_id' => null,
            'user_handle' => null,
            'org_id' => null,
            'session_id' => null,
        ];
    }

   

    /**
     * Log với channel riêng cho Ideogram
     */
    private function logInfo(string $message, array $context = []): void
    {
        Log::channel('ideogram')->info($message, array_merge($context, [
            'account_id' => $this->currentAccount?->id,
            'account_name' => $this->currentAccount?->name,
            'timestamp' => now()->toISOString()
        ]));
    }

    /**
     * Log error với channel riêng cho Ideogram
     */
    private function logError(string $message, array $context = []): void
    {
        Log::channel('ideogram')->error($message, array_merge($context, [
            'account_id' => $this->currentAccount?->id,
            'account_name' => $this->currentAccount?->name,
            'timestamp' => now()->toISOString()
        ]));
    }

    /**
     * Log debug với channel riêng cho Ideogram
     */
    private function logDebug(string $message, array $context = []): void
    {
        Log::channel('ideogram')->debug($message, array_merge($context, [
            'account_id' => $this->currentAccount?->id,
            'account_name' => $this->currentAccount?->name,
            'timestamp' => now()->toISOString()
        ]));
    }
    
    /**
     * Xử lý response từ API (cải tiến từ tool)
     */
    private function handleResponse($response)
    {
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $contentType = $response->getHeader('Content-Type')[0] ?? '';

        if ($statusCode >= 200 && $statusCode < 300) {
            if (strpos($contentType, 'application/json') !== false) {
                $data = json_decode($body, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $data;
                } else {
                    throw new \Exception('Invalid JSON response: ' . json_last_error_msg());
                }
            } else if (strpos($contentType, 'text/html') !== false) {
                throw new \Exception('Unexpected HTML response: ' . substr($body, 0, 100) . '...');
            } else {
                throw new \Exception('Unexpected response type: ' . $contentType);
            }
        }

        // Kiểm tra Cloudflare protection
        if ($statusCode === 403) {
            $isCloudflare = $body && (
                strpos($body, 'Just a moment') !== false ||
                strpos($body, 'cloudflare') !== false ||
                strpos($body, 'cf-ray') !== false ||
                strpos($body, 'challenge-platform') !== false
            );

            if ($isCloudflare) {
                $this->logError('Cloudflare protection detected', [
                    'status_code' => $statusCode,
                    'account_id' => $this->currentAccount->id
                ]);

                throw new \Exception('Cloudflare protection active. Please update cookies from browser or try different proxy.');
            } else {
                throw new \Exception("HTTP 403 - Access forbidden. Try updating cookies from browser.");
            }
        }

        // Redirect handling
        if ($statusCode >= 300 && $statusCode < 400) {
            $location = $response->getHeader('Location')[0] ?? 'Unknown';
            throw new \Exception("Redirected to: {$location} (HTTP {$statusCode})");
        }

        // 404 handling
        if ($statusCode === 404) {
            throw new \Exception("API endpoint not found (HTTP 404). Check if URL is correct.");
        }

        throw new \Exception("HTTP Error {$statusCode}: " . substr($body, 0, 200));
    }
    
 

    /**
     * Tạo ảnh và chờ kết quả hoàn thành
     */
    public function createImageSync(string $prompt, int $numImages = 1): array
    {
        try {
            // Lấy URL từ account _ngobao_server
            $account = IdeogramAccount::where('name', '_ngobao_server')->first();
            if (!$account || empty($account->notes)) {
                throw new \Exception('Không tìm thấy URL API từ tài khoản _ngobao_server');
            }

            $apiUrl = trim($account->notes);
            if (!filter_var($apiUrl, FILTER_VALIDATE_URL)) {
                throw new \Exception('URL API không hợp lệ trong ghi chú tài khoản _ngobao_server');
            }

            $this->logInfo('Creating image stream', [
                'prompt' => $prompt,
                'num_images' => $numImages,
                'api_url' => $apiUrl
            ]);

            // Tạo request data
            $requestData = [
                'prompt' => $prompt,
                'num_images' => $numImages
            ];

            // Gửi request HTTP thông thường và chờ response
            $response = $this->client->post($apiUrl . '/api/create-image', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'json' => $requestData,
                'timeout' => 420, // 2 phút timeout
                'connect_timeout' => 10
            ]);


            $statusCode = $response->getStatusCode();
            if ($statusCode !== 200) {
                throw new \Exception("HTTP Error: $statusCode " . $response->getBody()->getContents());
            }

            $responseBody = $response->getBody()->getContents();
            $responseData = json_decode($responseBody, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logError('Invalid JSON response', ['response' => $responseBody]);
                throw new \Exception('Invalid JSON response');
            }

            // Kiểm tra response có success và images không
            if (isset($responseData['success']) && $responseData['success'] &&
                isset($responseData['images']) && is_array($responseData['images'])) {

                $this->logInfo('Images created successfully from API', [
                    'images_count' => count($responseData['images']),
                    'message' => $responseData['message'] ?? 'No message'
                ]);

                return [
                    'success' => true,
                    'images' => $responseData['images'],
                    'message' => $responseData['message'] ?? 'Tạo ảnh thành công',
                    'prompt' => $prompt,
                    'num_images' => $numImages,
                    'source' => 'api',
                    'api_response' => $responseData
                ];
            }



            // Nếu response không đúng format, log và trả về mock
            $this->logError('Unexpected API response format', [
                'response' => $responseData,
                'has_success' => isset($responseData['success']),
                'success_value' => $responseData['success'] ?? null,
                'has_images' => isset($responseData['images']),
                'images_type' => isset($responseData['images']) ? gettype($responseData['images']) : 'not_set'
            ]);

            throw new \Exception('API response format không đúng');

        } catch (\Exception $e) {
            $this->logError('Create image sync error: ' . $e->getMessage(), [
                'prompt' => $prompt,
                'num_images' => $numImages,
                'exception' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'is_fallback' => true
            ];

        }
    }


    /**
     * Lấy thống kê sử dụng
     */
    public static function getUsageStats(): array
    {
        $accounts = IdeogramAccount::all();

        return [
            'total_accounts' => $accounts->count(),
            'active_accounts' => $accounts->where('is_active', true)->count(),
            'available_today' => $accounts->filter(fn($account) => $account->canUseToday())->count(),
            'total_usage_today' => $accounts->sum('daily_usage'),
            'total_limit_today' => $accounts->sum('daily_limit'),
            'total_usage_all_time' => $accounts->sum('usage_count'),
            'accounts_detail' => $accounts->map(function ($account) {
                return [
                    'id' => $account->id,
                    'name' => $account->name,
                    'is_active' => $account->is_active,
                    'daily_usage' => $account->daily_usage,
                    'daily_limit' => $account->daily_limit,
                    'usage_percentage' => $account->usage_percentage,
                    'status' => $account->status,
                    'last_used_at' => $account->last_used_at?->format('Y-m-d H:i:s'),
                    'usage_count' => $account->usage_count
                ];
            })
        ];
    }



    /**
     * Upload ảnh URL tới Ideogram server
     *
     * @param string $imageUrl URL của ảnh trên S3
     * @return array
     */
    public function uploadImageUrl(string $imageUrl): array
    {
        try {
            // Lấy URL API từ account notes
            $account = IdeogramAccount::where('name', '_ngobao_server')->first();
            if (!$account) {
                throw new \Exception('Không tìm thấy tài khoản _ngobao_server');
            }

            $apiUrl = trim($account->notes);
            if (!filter_var($apiUrl, FILTER_VALIDATE_URL)) {
                throw new \Exception('URL API không hợp lệ trong ghi chú tài khoản _ngobao_server');
            }

            // Chuẩn bị payload với URL
            $payload = [
                'image_url' => $imageUrl,
                'upload_type' => 'URL'
            ];

            Log::info('Uploading image URL', [
                'account_id' => $account->id,
                'api_url' => $apiUrl,
                'image_url' => $imageUrl
            ]);

            // Gọi API upload
            $response = $this->client->post($apiUrl . '/api/upload-image', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'json' => $payload,
                'timeout' => 30
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();

            Log::info('Upload image URL response', [
                'status_code' => $statusCode,
                'response' => $responseBody
            ]);

            if ($statusCode === 200) {
                $data = json_decode($responseBody, true);

                if ($data && isset($data['success']) && $data['success'] && !empty($data['id'])) {
                    Log::info('Upload URL successful', [
                        'image_id' => $data['id'],
                        'message' => $data['message'] ?? 'No message'
                    ]);

                    return [
                        'success' => true,
                        'image_id' => $data['id'],
                        'message' => $data['message'] ?? 'Upload thành công'
                    ];
                } else {
                    $errorMsg = 'API response invalid: ';
                    if (!isset($data['success']) || !$data['success']) {
                        $errorMsg .= 'success=false';
                    } elseif (empty($data['id'])) {
                        $errorMsg .= 'missing image ID';
                    }
                    $errorMsg .= ' - ' . ($data['message'] ?? json_encode($data));

                    throw new \Exception($errorMsg);
                }
            } else {
                throw new \Exception("HTTP Error: $statusCode - $responseBody");
            }

        } catch (RequestException $e) {
            Log::error('Upload image URL request failed', [
                'error' => $e->getMessage(),
                'api_url' => $apiUrl ?? 'unknown'
            ]);

            return [
                'success' => false,
                'error' => 'Network error: ' . $e->getMessage(),
                'is_fallback' => true
            ];

        } catch (\Exception $e) {
            Log::error('Upload image URL failed', [
                'error' => $e->getMessage(),
                'api_url' => $apiUrl ?? 'unknown'
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'is_fallback' => true
            ];
        }
    }

    /**
     * Tạo description từ ảnh bằng API localhost:8002
     *
     * @param string $imageId ID của ảnh (tạm thời để trống)
     * @param string $captionerModelVersion Version của model (mặc định V_2_0)
     * @return array
     */
    public function describeImage(string $imageId = '', string $captionerModelVersion = 'V_2_0'): array
    {
        try {
            // Lấy URL API từ account notes
            $account = IdeogramAccount::where('name', '_ngobao_server')->first();
            if (!$account) {
                throw new \Exception('Không tìm thấy tài khoản _ngobao_server');
            }

            $apiUrl = trim($account->notes);
            if (!filter_var($apiUrl, FILTER_VALIDATE_URL)) {
                throw new \Exception('URL API không hợp lệ trong ghi chú tài khoản _ngobao_server');
            }


            // Chuẩn bị payload
            $payload = [
                'image_id' => $imageId,
                'captioner_model_version' => $captionerModelVersion
            ];

            Log::info('Describing image with Ideogram API', [
                'api_url' => $apiUrl,
                'image_id' => $imageId,
                'captioner_model_version' => $captionerModelVersion
            ]);

            // Gọi API describe-image
            $response = $this->client->post($apiUrl . '/api/describe-image', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'json' => $payload,
                'timeout' => 30
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();

            Log::info('Describe image response', [
                'status_code' => $statusCode,
                'response' => $responseBody
            ]);

            $data = json_decode($responseBody, true);
            if ($statusCode === 200) {
                if ($data && isset($data['success']) && $data['success'] && !empty($data['description'])) {
                    return [
                        'success' => true,
                        'description' => $data['description'],
                        'image_id' => $data['image_id'] ?? $imageId,
                        'message' => $data['message'] ?? 'Describe thành công'
                    ];
                } else {
                    throw new \Exception('API response invalid: ' . ($data['message'] ?? 'No description returned'));
                }
            } else if ($statusCode == 503 || $data['success'] == false) {
                return [
                    'success' => false,
                    'error' => 'Có lỗi xảy ra, vui lòng liên hệ quản trị',
                    'is_fallback' => true
                ];
            }
            

        } catch (RequestException $e) {
            Log::error('Describe image request failed', [
                'error' => $e->getMessage(),
                'api_url' => 'http://localhost:8002'
            ]);

            return [
                'success' => false,
                'error' => 'Network error: ' . $e->getMessage(),
                'is_fallback' => true
            ];

        } catch (\Exception $e) {
            Log::error('Describe image failed', [
                'error' => $e->getMessage(),
                'api_url' => 'http://localhost:8002'
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'is_fallback' => true
            ];
        }
    }

    /**
     * Redesign ảnh với prompt mới
     *
     * @param string $prompt Prompt mô tả cách redesign
     * @param string $imageId ID của ảnh đã upload
     * @param int $numImages Số lượng ảnh (1-4)
     * @return array
     */
    public function redesignImageSync(string $prompt, string $imageId, int $numImages = 1): array
    {
        try {
            // Lấy URL API từ account notes
            $account = IdeogramAccount::where('name', '_ngobao_server')->first();
            if (!$account) {
                throw new \Exception('Không tìm thấy tài khoản _ngobao_server');
            }

            $apiUrl = trim($account->notes);
            if (!filter_var($apiUrl, FILTER_VALIDATE_URL)) {
                throw new \Exception('URL API không hợp lệ trong ghi chú tài khoản _ngobao_server');
            }

            // Chuẩn bị payload cho redesign
            $payload = [
                'prompt' => $prompt,
                'style_reference_parents' => [['image_id' => $imageId]],
                'num_images' => $numImages,
                'model_version' => 'V_3_1',
                'use_autoprompt_option' => 'ON',
                'style_expert' => 'ILLUSTRATION',
                'private' => true
            ];

            Log::info('Starting redesign image', [
                'account_id' => $account->id,
                'api_url' => $apiUrl,
                'prompt' => $prompt,
                'image_id' => $imageId,
                'num_images' => $numImages
            ]);

            // Gọi API redesign
            $response = $this->client->post($apiUrl . '/api/redesign-image', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'json' => $payload,
                'timeout' => 80 // Redesign có thể mất thời gian lâu hơn
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();

            Log::info('Redesign image response', [
                'status_code' => $statusCode,
                'response' => $responseBody
            ]);

            $data = json_decode($responseBody, true);
            if ($statusCode === 200) {

                if ($data && isset($data['success']) && $data['success']) {
                    // Cập nhật usage cho account nếu cần
                    if ($account && method_exists($this, 'updateAccountUsage')) {
                        $this->updateAccountUsage($numImages);
                    }

                    return [
                        'success' => true,
                        'images' => $data['images'],
                        'message' => $data['message'] ?? "Redesign thành công $numImages ảnh",
                        'source' => 'api_server'
                    ];
                } else {
                    throw new \Exception('API trả về lỗi: ' . ($data['message'] ?? 'Unknown error'));
                }
            } 
            else if ($statusCode == 503 || $data['success'] == false) {
                return [
                    'success' => false,
                    'error' => 'Có lỗi xảy ra, vui lòng liên hệ quản trị',
                    'is_fallback' => true
                ];
            }
            else {
                throw new \Exception("HTTP Error: $statusCode - $responseBody");
            }

        } catch (RequestException $e) {
            Log::error('Redesign image request failed', [
                'error' => $e->getMessage(),
                'api_url' => $apiUrl ?? 'unknown'
            ]);

         
            return [
                'success' => false,
                'error' => 'Network error: ' . $e->getMessage(),
                'is_fallback' => true
            ];

        } catch (\Exception $e) {
            Log::error('Redesign image failed', [
                'error' => $e->getMessage(),
                'api_url' => $apiUrl ?? 'unknown'
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'is_fallback' => true
            ];
        }
    }




}
