<?php

namespace App\Services;

use App\Models\TikTokShopToken;
use Illuminate\Support\Facades\Log;
use Exception;

class TikTokSyncService
{
    /**
     * Xử lý đồng bộ dữ liệu TikTok Shop
     *
     * @param string $shopCode
     * @param string $shopToken
     * @param array $shopData
     * @return array
     */
    public function syncShopData(string $shopCode, string $shopToken, array $shopData): array
    {
        try {
            // Validate shop token
            $validation = TikTokShopToken::validateShopToken($shopCode, $shopToken);

            if ($validation['status'] === 'reject') {
                return [
                    'success' => false,
                    'message' => $validation['message'],
                    'code' => 'TOKEN_MISMATCH'
                ];
            }

            // Process and validate shop data
            $processedData = $this->processShopData($shopData);
            
            if (!$processedData['valid']) {
                return [
                    'success' => false,
                    'message' => $processedData['error'],
                    'code' => 'INVALID_DATA'
                ];
            }

            // Update shop data
            $updated = $validation['record']->updateShopData(
                $processedData['data'],
                'Auto sync from TikTok tool at ' . now()->toDateTimeString()
            );

            if ($updated) {
                // Log successful sync
                Log::info('TikTok Shop Sync Success', [
                    'shop_code' => $shopCode,
                    'data_keys' => array_keys($shopData),
                    'sync_time' => now()->toDateTimeString()
                ]);

                return [
                    'success' => true,
                    'message' => 'Đồng bộ dữ liệu thành công',
                    'data' => [
                        'shop_code' => $shopCode,
                        'last_sync_at' => $validation['record']->fresh()->last_sync_at,
                        'data_summary' => $this->getDataSummary($processedData['data'])
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Không thể cập nhật dữ liệu vào database',
                    'code' => 'DATABASE_ERROR'
                ];
            }

        } catch (Exception $e) {
            Log::error('TikTok Sync Service Error', [
                'shop_code' => $shopCode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Lỗi hệ thống: ' . $e->getMessage(),
                'code' => 'SYSTEM_ERROR'
            ];
        }
    }

    /**
     * Xử lý và validate dữ liệu shop
     *
     * @param array $shopData
     * @return array
     */
    private function processShopData(array $shopData): array
    {
        try {
            // Basic validation
            if (empty($shopData)) {
                return [
                    'valid' => false,
                    'error' => 'Dữ liệu shop không được để trống',
                    'data' => null
                ];
            }

            // Sanitize and process data
            $processedData = $this->sanitizeShopData($shopData);

            // Additional business logic validation
            $businessValidation = $this->validateBusinessRules($processedData);
            
            if (!$businessValidation['valid']) {
                return [
                    'valid' => false,
                    'error' => $businessValidation['error'],
                    'data' => null
                ];
            }

            return [
                'valid' => true,
                'error' => null,
                'data' => $processedData
            ];

        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => 'Lỗi xử lý dữ liệu: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Sanitize shop data
     *
     * @param array $shopData
     * @return array
     */
    private function sanitizeShopData(array $shopData): array
    {
        // Add timestamp
        $shopData['processed_at'] = now()->toISOString();
        $shopData['data_version'] = '1.0';

        // Sanitize strings
        array_walk_recursive($shopData, function (&$value) {
            if (is_string($value)) {
                $value = trim($value);
            }
        });

        return $shopData;
    }

    /**
     * Validate business rules
     *
     * @param array $shopData
     * @return array
     */
    private function validateBusinessRules(array $shopData): array
    {
        // Implement specific business validation rules here
        // Example validations:

        // Check required fields (customize as needed)
        $requiredFields = []; // Add required fields if any
        
        foreach ($requiredFields as $field) {
            if (!isset($shopData[$field]) || empty($shopData[$field])) {
                return [
                    'valid' => false,
                    'error' => "Thiếu thông tin bắt buộc: {$field}"
                ];
            }
        }

        // Add more business rules as needed
        // Example: validate data formats, ranges, etc.

        return ['valid' => true, 'error' => null];
    }

    /**
     * Get summary of synced data
     *
     * @param array $shopData
     * @return array
     */
    private function getDataSummary(array $shopData): array
    {
        return [
            'total_fields' => count($shopData),
            'data_size' => strlen(json_encode($shopData)),
            'main_sections' => array_keys($shopData),
            'processed_at' => $shopData['processed_at'] ?? null
        ];
    }

    /**
     * Get sync statistics
     *
     * @param string|null $shopCode
     * @return array
     */
    public function getSyncStatistics(string $shopCode = null): array
    {
        $query = TikTokShopToken::query();
        
        if ($shopCode) {
            $query->where('shop_code', $shopCode);
        }

        $stats = [
            'total_shops' => $query->count(),
            'active_shops' => $query->where('is_active', true)->count(),
            'synced_today' => $query->whereDate('last_sync_at', today())->count(),
            'synced_this_hour' => $query->where('last_sync_at', '>=', now()->subHour())->count(),
        ];

        if ($shopCode) {
            $shop = $query->first();
            if ($shop) {
                $stats['shop_details'] = [
                    'shop_code' => $shop->shop_code,
                    'last_sync' => $shop->last_sync_format,
                    'is_active' => $shop->is_active,
                    'total_syncs' => 1 // Could be enhanced with a counter field
                ];
            }
        }

        return $stats;
    }
}
