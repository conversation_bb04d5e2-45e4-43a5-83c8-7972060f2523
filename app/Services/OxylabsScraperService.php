<?php

namespace App\Services;

use App\Services\Platforms\AliexpressScraperService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Services\Platforms\AmazonScraperService;
use App\Services\Platforms\EbayScraperService;
use App\Services\Platforms\WalmartScraperService;
use App\Services\Platforms\EtsyScraperService;
use App\Services\Platforms\RedbubbleScraperService;
use App\Services\Platforms\TiktokScraperService;
use App\Services\Platforms\WooCommerceScraperService;
use App\Services\Platforms\ShopifyScraperService;
use App\Services\Platforms\TemuScraperService;
use App\Services\Platforms\UniversalScraperService;

class OxylabsScraperService
{
    protected $username;
    protected $password;
    protected $baseUrl = 'https://realtime.oxylabs.io/v1/';

    // <PERSON><PERSON><PERSON> nghĩa các domain và service tương ứng
    protected $supportedPlatforms = [
        'amazon.com' => AmazonScraperService::class,
        'ebay.com' => EbayScraperService::class,
        'walmart.com' => WalmartScraperService::class,
        'etsy.com' => EtsyScraperService::class,
        'tiktok.com' => TiktokScraperService::class,
        'redbubble.com' => RedbubbleScraperService::class,
        'aliexpress.com' => AliexpressScraperService::class,
        'temu.com' => TemuScraperService::class
    ];

    public function __construct()
    {
        $this->username = config('services.oxylabs.username');
        $this->password = config('services.oxylabs.password');
    }

    protected function detectSource($url)
    {
        try {
            $parsedUrl = parse_url($url);
            if (!isset($parsedUrl['host'])) {
                throw new \Exception('Invalid URL format');
            }

            $domain = str_replace('www.', '', strtolower($parsedUrl['host']));
          
            // Kiểm tra các domain được hỗ trợ trực tiếp
            foreach ($this->supportedPlatforms as $supportedDomain => $service) {
                if (str_contains($domain, $supportedDomain)) {
                    return strtok($supportedDomain, '.'); // Trả về tên platform (e.g., 'amazon' từ 'amazon.com')
                }
            }

            // Thêm kiểm tra Shopify
            $shopifyService = new ShopifyScraperService();
            if ($shopifyService->isShopifyStore($url)) {
                return 'shopify';
            }

            // Kiểm tra WooCommerce 
            if ($this->isWooCommerceStore($url)) {
                return 'woocommerce';
            }

            // Nếu không match với platform nào, trả về domain làm source
            return $domain;
            
        } catch (\Exception $e) {
            Log::error('Error detecting source: ' . $e->getMessage());
            throw $e;
        }
    }

    protected function isWooCommerceStore($url)
    {
        try {
            $service = new WooCommerceScraperService();
            return $service->isWooCommerceStore($url);
        } catch (\Exception $e) {
            return false;
        }
    }

    public function scrapeProduct($url)
    {
        try {
            $source = $this->detectSource($url);
    
            // Khởi tạo service tương ứng
            $service = match($source) {
                'amazon' => new AmazonScraperService(),
                'ebay' => new EbayScraperService(),
                'walmart' => new WalmartScraperService(),
                'etsy' => new EtsyScraperService(),
                'tiktok' => new TiktokScraperService(),
                'woocommerce' => new WooCommerceScraperService(),
                'shopify' => new ShopifyScraperService(),
                'redbubble' => new RedbubbleScraperService(),
                'aliexpress' => new AliexpressScraperService(),
                'temu' => new TemuScraperService(),
                default => new UniversalScraperService() // Use universal scraper for unknown sources
            };
         
            return $service->scrape(['url' => $url]);

        } catch (\Exception $e) {
            Log::error('Product scraping error: ' . $e->getMessage());
            throw $e;
        }
    }

    protected function scrapeWithOxylabs($url, $service)
    {
        try {
            // Lấy request data từ service
            $requestData = [
                'instructions' => [
                    $service->getRequestData($url)
                ]
            ];

            // Log request
            Log::debug("Oxylabs request", [
                'url' => $url,
                'data' => $requestData
            ]);

            $response = Http::withBasicAuth($this->username, $this->password)
                ->timeout(30)
                ->retry(3, 1000)
                ->post($this->baseUrl . 'queries', $requestData);
            
            if (!$response->successful()) {
                Log::error("Oxylabs API error", [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                throw new \Exception("Failed to fetch data from Oxylabs");
            }

            $data = $response->json();
            
            // Log response
            Log::debug("Oxylabs response", [
                'data' => $data
            ]);

            return $service->scrape($data);

        } catch (\Exception $e) {
            Log::error("Error scraping with Oxylabs", [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}