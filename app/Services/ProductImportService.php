<?php

namespace App\Services;

use App\Models\ProductSource;
use Illuminate\Support\Facades\Log;

class ProductImportService
{
    protected $scraperService;

    public function __construct()
    {
        $this->scraperService = new OxylabsScraperService();
    }

    public function importFromUrl($url)
    {
        try {
            $productData = $this->scraperService->scrapeProduct($url);
            
            return [
                'title' => $productData['title'],
                'description' => $productData['description'],
                'images' => $productData['images'],
                'source_type' => $productData['source_type'],
                'external_id' => $productData['external_id'],
                'source_data' => $productData['source_data'],
                'metadata' => $productData['metadata'],
            ];
        } catch (\Exception $e) {
            Log::error('Product import failed: ' . $e->getMessage());
            throw $e;
        }
    }
} 