<?php

namespace App\Services;

use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\TikTokPayment;
use App\Models\PayoutTransaction;
use App\Models\SellerFundRequest;
use App\Models\SupplierOrder;
use App\Models\Production;
use App\Models\DesignJob;
use App\Models\Store;
use App\Enums\OrderStatus;
use App\Enums\SupplierOrderStatus;
use App\Enums\DesignJobStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * Service class riêng cho Seller API
 * 
 * Tham khảo các hàm trong SellerService để tính toán dữ liệu
 * mà không chỉnh sửa SellerService gốc
 */
class SellerApiService
{
    protected Carbon $startDate;
    protected Carbon $endDate;

    public function __construct(Carbon $startDate, Carbon $endDate)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    /**
     * Lấy dữ liệu đơn hàng cho API
     *
     * @param User|null $seller
     * @return array
     */
    public function getOrdersData(?User $seller = null): array
    {
        if ($seller) {
            return $this->getOrdersForSeller($seller);
        } else {
            return $this->getOrdersForAllSellers();
        }
    }

    /**
     * Lấy dữ liệu thanh toán ngân hàng cho API
     *
     * @param User|null $seller
     * @return array
     */
    public function getBankPaymentsData(?User $seller = null): array
    {
        if ($seller) {
            return $this->getBankPaymentsForSeller($seller);
        } else {
            return $this->getBankPaymentsForAllSellers();
        }
    }

    /**
     * Lấy dữ liệu chi phí cho API
     *
     * @param User|null $seller
     * @return array
     */
    public function getExpensesData(?User $seller = null): array
    {
        if ($seller) {
            return $this->getExpensesForSeller($seller);
        } else {
            return $this->getExpensesForAllSellers();
        }
    }

    /**
     * Lấy dữ liệu đơn hàng cho một seller cụ thể
     *
     * @param User $seller
     * @return array
     */
    private function getOrdersForSeller(User $seller): array
    {
        // Tạo query base với điều kiện seller_id
        $baseQuery = Order::query()
            ->where('orders.seller_id', $seller->id)
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate]);

        // Đếm tổng số đơn hàng
        $totalOrders = (clone $baseQuery)->count();

        // Lấy số lượng theo trạng thái
        $ordersByStatus = (clone $baseQuery)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Tính doanh thu từ đơn hàng hoàn thành
        $totalRevenue = (clone $baseQuery)
            ->where('orders.status', OrderStatus::Completed->value)
            ->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->sum('order_items.total');

        // Lấy số lượng đơn theo từng trạng thái
        $completedOrders = $ordersByStatus[OrderStatus::Completed->value] ?? 0;
        $processingOrders = $ordersByStatus[OrderStatus::Processing->value] ?? 0;
        $cancelledOrders = $ordersByStatus[OrderStatus::Cancelled->value] ?? 0;
        $otherStatusOrders = $totalOrders - ($completedOrders + $processingOrders + $cancelledOrders);

        return [
            'seller_id' => $seller->id,
            'seller_name' => $seller->name,
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'processing_orders' => $processingOrders,
            'cancelled_orders' => $cancelledOrders,
            'other_status_orders' => $otherStatusOrders,
            'total_revenue' => $totalRevenue,
            'status_breakdown' => $ordersByStatus,
            'period' => [
                'start_date' => $this->startDate->toDateString(),
                'end_date' => $this->endDate->toDateString()
            ]
        ];
    }

    /**
     * Lấy dữ liệu đơn hàng cho tất cả sellers
     *
     * @return array
     */
    private function getOrdersForAllSellers(): array
    {
        $sellers = User::whereHas('orders', function($query) {
            $query->whereBetween('orders.created_at', [$this->startDate, $this->endDate]);
        })->get();

        $allOrdersData = [];
        foreach ($sellers as $seller) {
            $allOrdersData[] = $this->getOrdersForSeller($seller);
        }

        return $allOrdersData;
    }

    /**
     * Lấy dữ liệu thanh toán ngân hàng cho một seller cụ thể
     *
     * @param User $seller
     * @return array
     */
    private function getBankPaymentsForSeller(User $seller): array
    {
        // Tính toán bank payout transactions (tham khảo từ SellerService::calculatePayoutTransactions)
        $storeIds = $seller->stores()->pluck('bank_account')->filter()->toArray();
        
        $bankPayoutData = ['sum' => 0, 'count' => 0];
        if (!empty($storeIds)) {
            $payoutQuery = PayoutTransaction::query()
                ->whereIn('card_no', $storeIds)
                ->where('type', 'Receive')
                ->where('status', 'Success')
                ->whereBetween('time', [$this->startDate, $this->endDate]);

            $bankPayoutData = [
                'sum' => $payoutQuery->sum('amount'),
                'count' => $payoutQuery->count()
            ];
        }

        // Tính toán TikTok payout (tham khảo từ SellerService::calculateTiktokPayout)
        $storeIdsForTiktok = $seller->stores()->pluck('id')->toArray();
        
        $tiktokPayoutData = ['sum' => 0, 'count' => 0];
        if (!empty($storeIdsForTiktok)) {
            $tiktokQuery = TikTokPayment::query()
                ->whereIn('store_id', $storeIdsForTiktok)
                ->whereBetween('paid_time', [$this->startDate, $this->endDate])
                ->where('status', 'PAID');

            $tiktokPayoutData = [
                'sum' => $tiktokQuery->sum('settlement_amount'),
                'count' => $tiktokQuery->count()
            ];
        }

        // Tính toán on hold amount (tham khảo từ SellerService::calculateOnHold)
        $onHoldAmount = 0;
        if (!empty($storeIdsForTiktok)) {
            $onHoldAmount = Store::whereIn('id', $storeIdsForTiktok)
                ->sum('tiktok_payout_on_hold');
        }

        return [
            'seller_id' => $seller->id,
            'seller_name' => $seller->name,
            'bank_payout' => [
                'total_amount' => $bankPayoutData['sum'],
                'transaction_count' => $bankPayoutData['count'],
                'currency' => 'USD'
            ],
            'tiktok_payout' => [
                'total_amount' => $tiktokPayoutData['sum'],
                'transaction_count' => $tiktokPayoutData['count'],
                'currency' => 'USD'
            ],
            'on_hold_amount' => $onHoldAmount,
            'period' => [
                'start_date' => $this->startDate->toDateString(),
                'end_date' => $this->endDate->toDateString()
            ]
        ];
    }

    /**
     * Lấy dữ liệu thanh toán ngân hàng cho tất cả sellers
     *
     * @return array
     */
    private function getBankPaymentsForAllSellers(): array
    {
        $sellers = User::whereHas('stores')->get();

        $allBankPaymentsData = [];
        foreach ($sellers as $seller) {
            $allBankPaymentsData[] = $this->getBankPaymentsForSeller($seller);
        }

        return $allBankPaymentsData;
    }

    /**
     * Lấy dữ liệu chi phí cho một seller cụ thể
     *
     * @param User $seller
     * @return array
     */
    private function getExpensesForSeller(User $seller): array
    {
        // Tính chi phí fulfillment (tham khảo từ SellerService::calculateFulfillment)
        $fulfillmentCost = $seller->supplierOrders()
            ->where('status', '!=', SupplierOrderStatus::Cancelled->value)
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->sum('base_cost');

        // Tính chi phí quảng cáo (tham khảo từ SellerService::calculateFund)
        $advertisingCost = $seller->fundRequests()
            ->where('status', 'approved')
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->sum('amount');

        // Tính phí thiết kế (tham khảo từ SellerService::calculateFeeDesign)
        // Sử dụng relationship createdDesignJobs() cho seller (thay vì designJobs() dành cho designer)
        // Lấy tất cả design jobs không phân biệt status
        $designCost = $seller->createdDesignJobs()
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->sum(DB::raw('COALESCE(price, 0) + COALESCE(rush_fee, 0)'));

        // Tính chi phí in áo (tham khảo từ SellerService::calculatePrintCost)
        $printCost = Production::query()
            ->where('seller_id', $seller->id)
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->where('status', 'completed')
            ->with('blank')
            ->get()
            ->sum(function ($production) {
                return $production->calculateProductionCost();
            });

        $totalExpenses = $fulfillmentCost + $advertisingCost + $designCost + $printCost;

        return [
            'seller_id' => $seller->id,
            'seller_name' => $seller->name,
            'fulfillment_cost' => $fulfillmentCost,
            'advertising_cost' => $advertisingCost,
            'design_cost' => $designCost,
            'print_cost' => $printCost,
            'total_expenses' => $totalExpenses,
            'breakdown' => [
                'fulfillment' => [
                    'amount' => $fulfillmentCost,
                    'description' => 'Chi phí đi đơn (fulfillment)'
                ],
                'advertising' => [
                    'amount' => $advertisingCost,
                    'description' => 'Chi phí quảng cáo'
                ],
                'design' => [
                    'amount' => $designCost,
                    'description' => 'Chi phí thiết kế'
                ],
                'printing' => [
                    'amount' => $printCost,
                    'description' => 'Chi phí in áo'
                ]
            ],
            'period' => [
                'start_date' => $this->startDate->toDateString(),
                'end_date' => $this->endDate->toDateString()
            ]
        ];
    }

    /**
     * Lấy dữ liệu chi phí cho tất cả sellers
     *
     * @return array
     */
    private function getExpensesForAllSellers(): array
    {
        $sellers = User::whereHas('orders', function($query) {
            $query->whereBetween('orders.created_at', [$this->startDate, $this->endDate]);
        })->get();

        $allExpensesData = [];
        foreach ($sellers as $seller) {
            $allExpensesData[] = $this->getExpensesForSeller($seller);
        }

        return $allExpensesData;
    }
}
