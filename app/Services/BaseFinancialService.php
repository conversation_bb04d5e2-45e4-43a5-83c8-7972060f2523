<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

/**
 * Base Service class cho các tính toán tài chính
 *
 * Chứa các method chung được sử dụng bởi SellerService và StoreService
 */
abstract class BaseFinancialService
{
    protected Carbon $startDate;
    protected Carbon $endDate;

    public function __construct(Carbon $startDate, Carbon $endDate)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    /**
     * Áp dụng bộ lọc thời gian cho query
     * Hỗ trợ cả Builder và Relation
     */
    protected function applyDateFilter($query, string $dateColumn = 'created_at')
    {
        return $query->whereBetween($dateColumn, [$this->startDate, $this->endDate]);
    }

    /**
     * Tính toán tỷ lệ phần trăm an toàn (tránh chia cho 0)
     */
    protected function calculatePercentage(float $numerator, float $denominator): float
    {
        return $denominator > 0 ? ($numerator / $denominator) * 100 : 0;
    }

    /**
     * Đảm bảo giá trị không âm
     */
    protected function ensurePositive(float $value): float
    {
        return max(0, $value);
    }

    /**
     * Format số tiền
     */
    protected function formatMoney(float $amount): string
    {
        return '$' . number_format($amount, 2);
    }

    /**
     * Tính tổng từ array các giá trị
     */
    protected function sumArray(array $values): float
    {
        return array_sum(array_filter($values, 'is_numeric'));
    }

    /**
     * Tính trung bình từ array các giá trị
     */
    protected function averageArray(array $values): float
    {
        $filtered = array_filter($values, 'is_numeric');
        $count = count($filtered);
        return $count > 0 ? array_sum($filtered) / $count : 0;
    }

    /**
     * Chuẩn hóa dữ liệu payout
     */
    protected function normalizePayout($payout): array
    {
        if (!is_array($payout)) {
            return ['sum' => 0, 'count' => 0];
        }

        return [
            'sum' => $payout['sum'] ?? 0,
            'count' => $payout['count'] ?? 0,
        ];
    }

    /**
     * Tính các chỉ số tài chính cơ bản
     */
    protected function calculateBasicMetrics(array $data): array
    {
        $revenue = $data['revenue'] ?? 0;
        $cost = $data['cost'] ?? 0;
        $orders = $data['orders'] ?? 0;
        $completedOrders = $data['completed_orders'] ?? 0;

        $grossProfit = $revenue - $cost;
        $profitMargin = $this->calculatePercentage($grossProfit, $revenue);
        $successRate = $this->calculatePercentage($completedOrders, $orders);
        $avgOrderValue = $completedOrders > 0 ? $revenue / $completedOrders : 0;

        return [
            'gross_profit' => $grossProfit,
            'profit_margin' => $profitMargin,
            'success_rate' => $successRate,
            'avg_order_value' => $avgOrderValue,
        ];
    }

    /**
     * Validate date range
     */
    protected function validateDateRange(): bool
    {
        return $this->startDate instanceof Carbon &&
               $this->endDate instanceof Carbon &&
               $this->startDate <= $this->endDate;
    }

    /**
     * Get date range info
     */
    protected function getDateRangeInfo(): array
    {
        return [
            'start_date' => $this->startDate->format('Y-m-d'),
            'end_date' => $this->endDate->format('Y-m-d'),
            'days' => $this->startDate->diffInDays($this->endDate) + 1,
            'is_current_month' => $this->startDate->isSameMonth(now()) && $this->endDate->isSameMonth(now()),
        ];
    }

    /**
     * Abstract method để implement trong child classes
     */
    abstract public function calculateFinancialData(): array;
}
