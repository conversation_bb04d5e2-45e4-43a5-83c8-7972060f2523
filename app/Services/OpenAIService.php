<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class OpenAIService
{
    protected $client;
    protected $apiKey;
    protected $useLocalFallback = false;

    public function __construct()
    {
        $this->client = new Client();
        $this->apiKey = env('OPENAI_API_KEY') ?? 'your-openai-api-key';
    }

    public function rewriteTitle($title)
    {
        $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'model' => 'gpt-4',
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a TikTok Shop product title optimization expert.'],
                    ['role' => 'user', 'content' => "Optimize this product title for TikTok Shop following these rules:
                        1. Keep main keywords from original title
                        2. Follow format: [Main Keywords] + [Features/Benefits] + [Product Type]
                        3. Max 255 characters but aim for at least 100 characters
                        4. No special characters or emojis
                        5. Use popular search terms
                        6. Include size/color variations if relevant
                        7. Add trending keywords if applicable
                        8. Make it engaging but not clickbait
                        
                        Original title: {$title}"],
                ],
                'temperature' => 0.7,
                'max_tokens' => 150,
            ],
        ]);
    
        $data = json_decode($response->getBody(), true);
        return str_replace('"', '', trim($data['choices'][0]['message']['content'] ?? 'Error'));
    }
    
    public function analyzeKeywords($title)
    {
        $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => [
              'model' => 'gpt-4o',
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                    ['role' => 'user', 'content' => "Bạn hãy phân tích tiêu đề sản phẩm POD sau đây: '{$title}'. Hãy chia tiêu đề thành các từ khóa và cụm từ quan trọng, đánh giá mức độ liên quan của từng từ khóa đến sản phẩm, và đưa ra đề xuất về việc tối ưu hóa tiêu đề để tăng khả năng hiển thị khi tìm kiếm trên các nền tảng như TikTok Shop hoặc Amazon. Xin vui lòng đề cập đến:
Các từ khóa chính có tính mô tả cao nhất
Gợi Ý 5 tiêu đề thay thế đã tối ưu hoá,Viết theo cấu trúc '[Content] , [Scope of application] , [Product type] , [Main function/Feature]', lưu ý không được có ký tự đặc biệt và không được dài quá 255 ký tự nhưng phải đảm bảo độ dài tối thiểu là 100 ký tự"],
                ],
                'max_tokens' => 800, // Increased max_tokens
                'temperature' => 0.7, // Added temperature for controlled response
            ],
        ]);
        $data = json_decode($response->getBody(), true);
        return str_replace('"','',trim($data['choices'][0]['message']['content'] ?? 'Error'));
    }
    public function rewriteDescription($description)
    {
        $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'model' => 'gpt-4',
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a TikTok Shop product description optimization expert.'],
                    ['role' => 'user', 'content' => "Optimize this product description for TikTok Shop following these rules:
                        1. Keep main product features and benefits
                        2. Structure in clear sections:
                           - Opening hook
                           - Key features/benefits (bullet points)
                           - Product details (size, material, etc.)
                           - Usage instructions
                           - Care instructions
                           - Shipping/Returns info
                        3. Use short paragraphs and bullet points
                        4. Include relevant search keywords naturally
                        5. Add line breaks for readability
                        6. Keep tone casual but professional
                        7. Address common customer questions
                        8. Include size guide if applicable
                        9. Highlight unique selling points
                        10. End with call to action
                        
                        Original description: {$description}"],
                ],
                'temperature' => 0.7,
                'max_tokens' => 1000,
            ],
        ]);
    
        $data = json_decode($response->getBody(), true);
        $newDescription = trim($data['choices'][0]['message']['content'] ?? 'Error');
        
        // Ensure proper formatting for RichEditor
        $newDescription = nl2br($newDescription);
        $newDescription = str_replace(['<br />', '<br/>'], "\n", $newDescription);
        
        return $newDescription;
    }

    public function generateShopIntro($shopName)
    {
        $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'model' => 'gpt-3.5-turbo',
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                    ['role' => 'user', 'content' => "Create a shop introduction for TikTok adhering strictly to TikTok's content guidelines. The shop name is {$shopName}.
                
                Here is an example format for the introduction:
                <p><b>Welcome to {{shop_name}}!</b></p>

                <p>If you're looking for <b>soft</b>, <b>comfy</b>, <b>top-quality shirts</b>, you're in the right place! I love what I do and strive to make your shopping experience just right for you. If you have any questions, need a custom design, or want to choose another color for my products, feel free to send a message anytime.</p>

                <p>The highlight of this product is the images printed on the sweatshirt using <b>advanced digital printing technology</b>. These images are not embroidered but are printed directly on the fabric, so they do not peel off or fade over time.</p>
                
                Please ensure the introduction:
                - Uses simple and clear language
                - Is welcoming and friendly
                - Highlights the uniqueness and quality of the products
                - Encourages customers to reach out with questions or custom requests
                
                Example response:
                <p><b>Welcome to MyStore!</b></p>
                <p>If you're on the hunt for <b>unique</b>, <b>stylish</b>, and <b>durable accessories</b>, you've landed in the perfect spot! I'm passionate about fashion and strive to make your shopping experience enjoyable. If you have any questions, need a custom design, or want to choose another color for my products, feel free to send a message anytime.</p>
                <p>One of the outstanding features of our accessories is the craftsmanship using <b>high-quality materials and finishes</b>. Each item is designed with care and precision to ensure it lasts for years. Thank you for visiting and happy shopping!</p>"],
                ],
                'max_tokens' => 150,
            ],
        ]);

        $data = json_decode($response->getBody(), true);
        $intro = $data['choices'][0]['message']['content'] ?? 'Error';

        // Loại bỏ các ký tự đặc biệt
        $intro = str_replace(["\n", '"""'], '', $intro);

        return trim($intro);
    }


    public function checkPODShop($products)
    {
        if (empty($products)) {
            return [
                'result' => 'No',
                'time' => 0
            ];
        }

        $productString = "";
        foreach ($products as $product) {
            $productString .= "Title: " . $product['title'] . "\n";
        }

        $prompt = "Analyze the following list of product titles and images to determine if this shop sells Print on Demand (POD) products. POD products are custom printed on demand and typically include only t-shirts, hoodies, and sweatshirts. They are often characterized by unique, custom designs or graphics that are printed onto the fabric only when an order is placed, as opposed to being pre-made in bulk. Ignore other types of products such as regular retail apparel without custom designs, pre-made items, and non-apparel items. If the shop sells POD products, answer 'YES', otherwise answer 'NO'. Here are some examples:

    Example POD Titles and Images:
    - Title: 'Funny Shirt For Men Awesome Like My Daughter Funny Father's Day Gift - Dad Joke T-Shirt For Men'
      Image: https://i.imgur.com/cZsdPe1.jpeg
    - Title: 'Letter Print Round Neck Graphic Tee, Vintage Trendy Casual Short Sleeve T-shirt for Daily Wear, Made In The 70's Retro Funny Vintage 1970s Party Gifts T-Shirt'
      Image: https://i.imgur.com/3KSHJHX.jpeg
    - Title: 'CHILD OF GOD Hoodie | Christian Faith Unisex Jesus Hoodie'
      Image: https://i.imgur.com/mAGw1oZ.jpeg

    Example Non-POD Titles and Images:
    - Title: 'Women's Plain Sports Sauna Tank Top, Sleeveless Sweat Shirt, Summer Compression Shirts Women, Lady's Sportswear Workout Tops for Gym Fitness Exercise, Athletic Clothes'
      Image: https://i.imgur.com/drNshA4.png
    - Title: 'Men's Solid Long Sleeve V Neck Cycling Top, Casual Plain Thermal Lined Top for Fall & Winter, Men's Sport & Outdoor Clothing for Indoor Outdoor Wear'
      Image: https://i.imgur.com/w0owojx.png
    - Title: 'Men's Solid Round Neck Athletic Sauna Tee, Sporty Short Sleeve Compression T-shirt, Summer Outfits 2024, Men's Top for Workout Fitness Gym'
      Image: https://i.imgur.com/gMqImxz.png

    Respond with exactly one of the following options without any additional text: 'YES', 'NO'. Here are the products:\n{$productString}";

        $startTime = microtime(true);

        $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'model' => 'gpt-3.5-turbo',
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                    ['role' => 'user', 'content' => $prompt],
                ],
                'max_tokens' => 10,
            ],
        ]);

        $endTime = microtime(true);

        $data = json_decode($response->getBody(), true);
        $result = trim($data['choices'][0]['message']['content'] ?? 'Error');
        $result = str_replace('"', '', $result);
        $result = str_replace("'", '', $result);
        $executionTime = $endTime - $startTime;

        if (!in_array($result, ['YES', 'NO'])) {
            $result = 'Error';
        }

        return [
            'result' => $result,
            'time' => $executionTime
        ];
    }

    function extractImagesAndTitles($products)
    {
        $results = [];

        foreach ($products as $product) {
            // Decode the JSON view data
            $viewData = json_decode($product['view'], true);

            // Check if the necessary keys exist in the decoded data
            if (
                isset($viewData['product_data']['components']['1001']['tags'][0]['cover_tag']['image']['url_list'][0]) &&
                isset($viewData['product_data']['components']['1003']['tags'][0]['title_tag']['title'])
            ) {

                // Extract the image URL and title
                $image = $viewData['product_data']['components']['1001']['tags'][0]['cover_tag']['image']['url_list'][0];
                $title = $viewData['product_data']['components']['1003']['tags'][0]['title_tag']['title'];

                // Add to results array
                $results[] = [
                    'title' => $title,
                    'image' => $image
                ];
            }
        }

        return $results;
    }
    public function determineNiche($products)
    {
        $productString = "";
        foreach ($products as $product) {
            $productString .= "Title: " . $product['title'] . "\n";
        }
        $prompt = "Analyze the following list of product titles and determine the most common niche among them. Respond with only one or two words that best describe the niche. Titles: \n{$productString}";

        $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'model' => 'gpt-3.5-turbo',
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                    ['role' => 'user', 'content' => $prompt],
                ],
                'max_tokens' => 50,
            ],
        ]);

        $data = json_decode($response->getBody(), true);
        return trim($data['choices'][0]['message']['content'] ?? 'Error');
    }
    public function determinePrintType($imageUrl)
    {
        $prompt = "Analyze the following image and determine if the product shown is a single-sided or double-sided print. If the image shows both sides of a product, it is double-sided. If it shows only one side, it is single-sided. Here is the image URL: {$imageUrl}";

        $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'model' => 'gpt-4o',
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                    ['role' => 'user', 'content' => $prompt],
                ],
                'max_tokens' => 10,
            ],
        ]);

        $data = json_decode($response->getBody(), true);
        $result = trim($data['choices'][0]['message']['content'] ?? 'Error');
        $result = str_replace('"', '', $result);
        $result = str_replace("'", '', $result);

        if (!in_array($result, ['Single-sided', 'Double-sided'])) {
            $result = 'Error';
        }

        return $result;
    }

    /**
     * Tạo tên sản phẩm, từ khóa và hashtag dựa trên hình ảnh hoặc mô tả
     * 
     * @param string $imageDataUrl URL dữ liệu hình ảnh (base64)
     * @param string $productDescription Mô tả sản phẩm
     * @param string $systemPrompt Prompt hệ thống tùy chỉnh
     * @param string $customUserPrompt Phần user prompt tùy chỉnh
     * @return array Kết quả bao gồm tên sản phẩm, từ khóa và hashtag
     */
    public function generateProductNameWithKeywords(
        $imageDataUrl, 
        $productDescription = '',
        $systemPrompt = null,
        $customUserPrompt = null
    )
    {
        try {
            // Chuẩn bị đầu vào cho API
            $messages = [];
            
            // System message ngắn gọn
            $messages[] = [
                'role' => 'system', 
                'content' => $systemPrompt ?: 'You are a TikTok Shop product naming expert that creates engaging product names, keywords, and hashtags based on the product image or description provided.'
            ];
            
            // Xử lý hình ảnh (nếu có)
            if (!empty($imageDataUrl)) {
                // Đảm bảo gửi hình ảnh đúng cấu trúc
                $messages[] = [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => 'Analyze this product image and identify what it is.'
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => $imageDataUrl
                            ]
                        ]
                    ]
                ];
            }
            
            // Thêm mô tả sản phẩm và yêu cầu
            $userPrompt = "Based on ";
            $userPrompt .= !empty($imageDataUrl) ? "the product shown in the image" : "";
            $userPrompt .= (!empty($imageDataUrl) && !empty($productDescription)) ? " and " : "";
            $userPrompt .= !empty($productDescription) ? "this description: \"$productDescription\"" : "";
            $userPrompt .= ", create product information with the following format:

";
            
            // Sử dụng user prompt tùy chỉnh nếu có
            $userPrompt .= $customUserPrompt ?: '📌 Product Name: (Generate a catchy name 150-175 characters long, including main features and target audience)

🎯 Keywords: (5 important SEO keywords for this product, separated by commas)

🏷 Hashtags: (10 popular hashtags related to this product, including product type, features, and trends)';

            $messages[] = [
                'role' => 'user',
                'content' => $userPrompt
            ];
            
            // Gọi API với cấu trúc message chính xác để xử lý hình ảnh
            $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => 'gpt-4o',
                    'messages' => $messages,
                    'temperature' => 0.7,
                    'max_tokens' => 800,
                ],
            ]);
            
            $data = json_decode($response->getBody(), true);
            $content = $data['choices'][0]['message']['content'] ?? '';
            
            // Parse kết quả
            $result = [
                'product_name' => '',
                'keywords' => '',
                'hashtags' => ''
            ];
            
            // Phương pháp 1: Extract theo emoji (nếu có)
            if (strpos($content, '📌') !== false) {
                // Extract Product Name
                if (preg_match('/📌.*?:(.*?)(?=🎯|\z)/s', $content, $matches)) {
                    $result['product_name'] = str_replace('"', '', trim($matches[1]));
                }
                
                // Extract Keywords
                if (preg_match('/🎯.*?:(.*?)(?=🏷|\z)/s', $content, $matches)) {
                    $result['keywords'] = trim($matches[1]);
                }
                
                // Extract Hashtags
                if (preg_match('/🏷.*?:(.*?)(?=\z)/s', $content, $matches)) {
                    $result['hashtags'] = trim($matches[1]);
                }
            }
            // Phương pháp 2: Extract theo tiêu đề (nếu không có emoji)
            else {
                // Chia nội dung thành các phần dựa trên các tiêu đề thông thường
                $contentLines = explode("\n", $content);
                $currentSection = null;
                $sectionContent = [];
                
                foreach ($contentLines as $line) {
                    $line = trim($line);
                    
                    // Bỏ qua dòng trống
                    if (empty($line)) continue;
                    
                    // Kiểm tra các tiêu đề phần
                    if (stripos($line, 'product name') !== false || stripos($line, 'name:') !== false) {
                        $currentSection = 'product_name';
                        // Nếu có giá trị trên cùng dòng
                        if (strpos($line, ':') !== false) {
                            $sectionContent[$currentSection] = str_replace('"', '', trim(substr($line, strpos($line, ':') + 1)));
                        } else {
                            $sectionContent[$currentSection] = '';
                        }
                    }
                    else if (stripos($line, 'keyword') !== false || stripos($line, 'keywords:') !== false) {
                        $currentSection = 'keywords';
                        // Nếu có giá trị trên cùng dòng
                        if (strpos($line, ':') !== false) {
                            $sectionContent[$currentSection] = trim(substr($line, strpos($line, ':') + 1));
                        } else {
                            $sectionContent[$currentSection] = '';
                        }
                    }
                    else if (stripos($line, 'hashtag') !== false || stripos($line, 'hashtags:') !== false) {
                        $currentSection = 'hashtags';
                        // Nếu có giá trị trên cùng dòng
                        if (strpos($line, ':') !== false) {
                            $sectionContent[$currentSection] = trim(substr($line, strpos($line, ':') + 1));
                        } else {
                            $sectionContent[$currentSection] = '';
                        }
                    }
                    // Thêm vào nội dung phần hiện tại nếu không phải tiêu đề mới
                    else if ($currentSection !== null) {
                        $sectionContent[$currentSection] .= (empty($sectionContent[$currentSection]) ? '' : ' ') . $line;
                    }
                }
                
                // Gán giá trị cho kết quả
                if (!empty($sectionContent['product_name'])) {
                    $result['product_name'] = str_replace('"', '', $sectionContent['product_name']);
                }
                if (!empty($sectionContent['keywords'])) {
                    $result['keywords'] = $sectionContent['keywords'];
                }
                if (!empty($sectionContent['hashtags'])) {
                    $result['hashtags'] = $sectionContent['hashtags'];
                }
                
                // Nếu không tìm thấy cấu trúc rõ ràng, chia nội dung thành 3 phần bằng nhau
                if (empty($result['product_name']) && empty($result['keywords']) && empty($result['hashtags'])) {
                    $paragraphs = array_values(array_filter(array_map('trim', explode("\n\n", $content))));
                    
                    if (count($paragraphs) >= 3) {
                        $result['product_name'] = str_replace('"', '', $paragraphs[0]);
                        $result['keywords'] = $paragraphs[1];
                        $result['hashtags'] = $paragraphs[2];
                    } elseif (count($paragraphs) == 2) {
                        $result['product_name'] = str_replace('"', '', $paragraphs[0]);
                        $result['keywords'] = $paragraphs[1];
                    } elseif (count($paragraphs) == 1) {
                        $result['product_name'] = str_replace('"', '', $paragraphs[0]);
                    }
                }
            }
            
            return $result;
        } catch (ClientException $e) {
            Log::error('OpenAI API Error: ' . $e->getMessage());
            
            $errorResponse = json_decode($e->getResponse()->getBody()->getContents(), true);
            $errorMessage = $errorResponse['error']['message'] ?? 'Unknown API error';
            
            if (strpos($errorMessage, 'exceeded your current quota') !== false || $e->getCode() == 429) {
                return $this->getFallbackProductData($productDescription);
            }
            
            return [
                'product_name' => 'Lỗi API: ' . $errorMessage,
                'keywords' => '',
                'hashtags' => ''
            ];
        } catch (RequestException $e) {
            Log::error('OpenAI Request Error: ' . $e->getMessage());
            return $this->getFallbackProductData($productDescription);
        } catch (\Exception $e) {
            Log::error('OpenAI General Error: ' . $e->getMessage());
            return [
                'product_name' => 'Lỗi hệ thống: ' . $e->getMessage(),
                'keywords' => '',
                'hashtags' => ''
            ];
        }
    }
    
    /**
     * Generate fallback product data when API is unavailable
     */
    private function getFallbackProductData($description = '')
    {
        // Try to extract keywords from the description if available
        $basicKeywords = [];
        if (!empty($description)) {
            // Simple keyword extraction from description
            $words = str_word_count($description, 1);
            $words = array_filter($words, function($word) {
                return strlen($word) > 3; // Filter out short words
            });
            $basicKeywords = array_slice(array_unique($words), 0, 5);
        }
        
        // Default product name based on description or fallback to CLOTHING item (not earbuds)
        $productName = !empty($description) 
            ? substr($description, 0, 150) . (strlen($description) > 150 ? '...' : '')
            : 'Stylish Graphic T-Shirt with Custom Design, Premium Cotton Blend, Comfortable Fit for Casual Wear, Durable Quality Fabric for Everyday Use, Unisex Fashion Tee for All Seasons';
        
        // Loại bỏ ngoặc kép nếu có
        $productName = str_replace('"', '', $productName);
        
        // Default keywords for CLOTHING
        $keywords = !empty($basicKeywords) 
            ? implode(', ', $basicKeywords)
            : 'Graphic T-Shirt, Custom Design, Premium Cotton, Casual Wear, Unisex Fashion';
        
        // Default hashtags for CLOTHING
        $hashtags = '#GraphicTee #CustomTShirt #FashionTee #CasualWear #UnisexStyle #PremiumCotton #TrendyTShirt #ComfortableFit #EverydayStyle #QualityApparel';
        
        return [
            'product_name' => $productName,
            'keywords' => $keywords,
            'hashtags' => $hashtags,
            'is_fallback' => true
        ];
    }
    
    /**
     * Tạo mockup nền trắng chuẩn TikTok Shop từ hình ảnh gốc
     * Sử dụng model gpt-4o-image-vip từ laozhang.ai để xử lý và cải thiện hình ảnh
     */
    public function generateTikTokMockup($imageDataUrl)
    {
        try {
            // Chuẩn bị đầu vào cho API
            $messages = [];
            
            // System message
            $messages[] = [
                'role' => 'system', 
                'content' => 'You are a product mockup generator that creates clean, professional product images on plain white backgrounds for TikTok Shop.'
            ];
            
            // Gửi hình ảnh
            if (!empty($imageDataUrl)) {
                $messages[] = [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => 'Create a professional product mockup from this image. Remove any background and place the product on a pure white background (RGB: 255,255,255). Maintain the original design and details of the product. The image should be square (1:1 ratio) with the product centered. Do not add any text, borders, or decorative elements. Return the image directly without text explanation.'
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => $imageDataUrl
                            ]
                        ]
                    ]
                ];
            }
            
            // Gọi API với gpt-4o-image-vip từ laozhang.ai
            $response = $this->client->post('https://api.laozhang.ai/v1/images/generations', [
                'headers' => [
                    'Authorization' => 'Bearer sk-6oB32CTugqjK359MDc4a2a43058c44A2A535EeB3A5DbAcF2',
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => 'gpt-4o-image-vip',
                    'prompt' => 'Create a clean product mockup of the items shown. Remove any background and replace with pure white (RGB 255,255,255). Maintain all original design details. Create a square 1:1 aspect ratio image with the product centered. The output should be professional quality suitable for e-commerce on TikTok Shop. Do not add text, borders, shadows, or any decorative elements.',
                    'n' => 1,
                    'size' => '1024x1024'
                ],
            ]);
            
            // Parse kết quả
            $data = json_decode($response->getBody(), true);
            
            if (isset($data['data']) && !empty($data['data'])) {
                $imageData = $data['data'][0];
                $mockupUrl = null;
                
                // Xử lý URL format
                if (isset($imageData['url'])) {
                    $mockupUrl = $imageData['url'];
                    Log::info('Mockup URL format found', ['url' => $mockupUrl]);
                }
                // Xử lý base64 format
                elseif (isset($imageData['b64_json'])) {
                    // Convert base64 to data URL
                    $base64Data = $imageData['b64_json'];
                    $mockupUrl = 'data:image/png;base64,' . $base64Data;
                    Log::info('Mockup base64 format found and converted', [
                        'base64_length' => strlen($base64Data)
                    ]);
                }
                
                if ($mockupUrl) {
                    return [
                        'mockup_image' => $mockupUrl,
                        'format' => isset($imageData['url']) ? 'url' : 'base64'
                    ];
                }
            }
            
            // Log lỗi nếu không tìm thấy format nào
            Log::error('gpt-4o-image-vip API Error: Invalid response format');
            Log::error(json_encode($data));
            
            // Sử dụng hình ảnh gốc làm mockup nếu không thể tạo mới
            return [
                'mockup_image' => $imageDataUrl,
                'error' => 'Could not generate a new mockup. Using original image.'
            ];
        } catch (\Exception $e) {
            Log::error('gpt-4o-image-vip Mockup Generation Error: ' . $e->getMessage());
            // Trả về hình ảnh gốc nếu có lỗi
            return [
                'mockup_image' => $imageDataUrl,
                'error' => $e->getMessage()
            ];
        }
    }

  public function generateImageDescriptionByChatGpt($imageDataUrl, $prompt = 'Mô tả ảnh này') {
    try {

        Log::info('Generating image description', [
            'has_image' => !empty($imageDataUrl),
            'prompt' => $prompt
        ]);

        $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'model' => 'gpt-4o',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => [
                            [
                                'type' => 'text',
                                'text' => $prompt
                            ],
                            [
                                'type' => 'image_url',
                                'image_url' => [
                                    'url' => $imageDataUrl
                                ]
                            ]
                        ]
                    ]
                ],
                'max_tokens' => 1000,
            ],
        ]);

        $data = json_decode($response->getBody(), true);

        if ($response->getStatusCode() !== 200) {
            throw new \Exception('HTTP Error: ' . $response->getStatusCode());
        }

        return [
            'description' => $data['choices'][0]['message']['content'] ?? '',
            'api_used' => 'openai.com',
            'model_used' => 'gpt-4o',
            'success' => true
        ];

      
       
    } catch (\Exception $e) {
        Log::error('ChatGPT API Error: ' . $e->getMessage());
        return 'Lỗi hệ thống: ' . $e->getMessage();
    }
}



        /**
     * Tạo mô tả từ ảnh bằng GPT-4 Vision
     * 
     * @param string $imageDataUrl URL ảnh (base64)
     * @param string $prompt Prompt để mô tả ảnh
     * @return array Kết quả bao gồm description
     */
    public function generateImageDescription($imageDataUrl, $prompt = 'Mô tả ảnh này')
    {
        try {
            $client = new Client([
                'timeout' => 60.0,
                'verify' => false,
            ]);

            $apiToken = 'sk-6oB32CTugqjK359MDc4a2a43058c44A2A535EeB3A5DbAcF2';

            Log::info('Generating image description', [
                'has_image' => !empty($imageDataUrl),
                'image_data_length' => strlen($imageDataUrl ?? ''),
                'image_data_prefix' => substr($imageDataUrl ?? '', 0, 50),
                'prompt' => $prompt,
                'api_token_length' => strlen($apiToken)
            ]);

            $response = $client->post('https://api.laozhang.ai/v1/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiToken,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => 'gpt-4o',
                    'stream' => false,
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => [
                                [
                                    'type' => 'text',
                                    'text' => $prompt
                                ],
                                [
                                    'type' => 'image_url',
                                    'image_url' => [
                                        'url' => $imageDataUrl
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'max_tokens' => 800
                ],
                'timeout' => 120,
            ]);

            $responseBody = $response->getBody()->getContents();
            $data = json_decode($responseBody, true);

            Log::info('Image description response', [
                'status' => $response->getStatusCode(),
                'has_choices' => isset($data['choices']),
                'response' => $data
            ]);

            if (!isset($data['choices']) || empty($data['choices'])) {
                throw new \Exception('Không có response từ GPT-4 Vision: ' . $responseBody);
            }

            $description = $data['choices'][0]['message']['content'] ?? '';

            if (empty($description)) {
                throw new \Exception('Description trống từ GPT-4 Vision');
            }

            Log::info('Description generated successfully', [
                'length' => strlen($description)
            ]);

            return [
                'description' => $description,
                'api_used' => 'laozhang.ai',
                'model_used' => 'gpt-4o',
                'success' => true
            ];

        } catch (RequestException $e) {
            $statusCode = $e->hasResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorBody = $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : $e->getMessage();

            Log::error('GPT-4 Vision API Error', [
                'status_code' => $statusCode,
                'error_body' => $errorBody,
                'message' => $e->getMessage(),
                'image_data_length' => strlen($imageDataUrl ?? ''),
                'prompt' => $prompt
            ]);

            // Parse error body để lấy thông tin chi tiết
            $errorDetails = '';
            if ($errorBody) {
                $errorData = json_decode($errorBody, true);
                if (isset($errorData['error']['message'])) {
                    $errorDetails = $errorData['error']['message'];
                } else {
                    $errorDetails = $errorBody;
                }
            }

            return [
                'error' => "Lỗi API GPT-4 Vision (HTTP {$statusCode}): " . $errorDetails,
                'error_code' => $statusCode,
                'error_details' => $errorBody,
                'success' => false
            ];

        } catch (\Exception $e) {
            Log::error('General error in image description', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'image_data_length' => strlen($imageDataUrl ?? ''),
                'prompt' => $prompt
            ]);

            return [
                'error' => 'Lỗi hệ thống: ' . $e->getMessage(),
                'error_details' => $e->getTraceAsString(),
                'success' => false
            ];
        }
    }

    /**
     * Tạo ảnh POD design đơn giản
     * 
     * @param string $originalImageDataUrl URL ảnh gốc (base64) 
     * @param string $prompt Prompt mô tả thiết kế POD
     * @return array Kết quả bao gồm URL ảnh mới
     */
    public function generatePODDesign($originalImageDataUrl, $prompt = '')
    {
        try {
            $client = new Client([
                'timeout' => 60.0,
                'verify' => false,
            ]);

            $apiToken = 'sk-6oB32CTugqjK359MDc4a2a43058c44A2A535EeB3A5DbAcF2';

            // Tạo prompt đơn giản
            if (empty($prompt)) {
                $prompt = 'Create a modern POD t-shirt design with transparent background';
            }
            Log::info('POD Design Prompt: ' . $prompt);
            // Gọi API
            $response = $client->post('https://api.laozhang.ai/v1/images/generations', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiToken,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => 'gpt-image-1',
                    'prompt' => $prompt,
                    'size' => '1024x1024'
                ],
                'timeout' => 120,
            ]);

            $data = json_decode($response->getBody(), true);

            if (!isset($data['data']) || empty($data['data'])) {
                throw new \Exception('Không có data trong response');
            }

            $imageData = $data['data'][0];

            // Xử lý URL hoặc base64
            if (isset($imageData['url'])) {
                $imageUrl = $imageData['url'];
            } elseif (isset($imageData['b64_json'])) {
                $imageUrl = 'data:image/png;base64,' . $imageData['b64_json'];
            } else {
                throw new \Exception('Không tìm thấy ảnh trong response');
            }

            return [
                'image_url' => $imageUrl,
                'success' => true
            ];

        } catch (\Exception $e) {
            Log::error('POD Design Error: ' . $e->getMessage());

            return [
                'is_fallback' => true,
                'error_message' => 'Không thể tạo thiết kế. Vui lòng thử lại sau.'
            ];
        }
    }

    /**
     * Get headers for OpenAI API requests
     */
    private function getHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ];
    }

    /**
     * Create embeddings for text (for Business Intelligence)
     */
    public function createEmbedding(string $text, string $model = 'text-embedding-3-small'): array
    {
        $data = [
            'input' => $text,
            'model' => $model
        ];

        try {
            $response = $this->client->post('https://api.openai.com/v1/embeddings', [
                'headers' => $this->getHeaders(),
                'json' => $data,
                'timeout' => 60
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if (isset($result['data'][0]['embedding'])) {
                return [
                    'success' => true,
                    'embedding' => $result['data'][0]['embedding'],
                    'model' => $result['model'],
                    'usage' => $result['usage'] ?? []
                ];
            }

            return [
                'success' => false,
                'error' => 'Invalid response format from OpenAI API'
            ];

        } catch (RequestException $e) {
            Log::error('OpenAI Embedding API Error: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'OpenAI API request failed: ' . $e->getMessage()
            ];
        } catch (\Exception $e) {
            Log::error('OpenAI Embedding System Error: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'System error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create batch embeddings (for Business Intelligence)
     */
    public function createBatchEmbeddings(array $inputs, string $model = 'text-embedding-3-small'): array
    {
        if (empty($inputs)) {
            return [
                'success' => false,
                'error' => 'Input array cannot be empty'
            ];
        }

        $data = [
            'input' => $inputs,
            'model' => $model
        ];

        try {
            $response = $this->client->post('https://api.openai.com/v1/embeddings', [
                'headers' => $this->getHeaders(),
                'json' => $data,
                'timeout' => 120
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if (isset($result['data']) && is_array($result['data'])) {
                $embeddings = [];
                foreach ($result['data'] as $item) {
                    $embeddings[] = $item['embedding'];
                }

                return [
                    'success' => true,
                    'embeddings' => $embeddings,
                    'model' => $result['model'],
                    'usage' => $result['usage'] ?? []
                ];
            }

            return [
                'success' => false,
                'error' => 'Invalid response format from OpenAI API'
            ];

        } catch (RequestException $e) {
            Log::error('OpenAI Batch Embeddings API Error: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'OpenAI API request failed: ' . $e->getMessage()
            ];
        } catch (\Exception $e) {
            Log::error('OpenAI Batch Embeddings System Error: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'System error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Chat completion for business analysis (for Business Intelligence)
     */
    public function chatCompletion(array $messages, string $model = 'gpt-4o-mini', int $maxTokens = 10000): array
    {
        $data = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => $maxTokens,
            'temperature' => 0.7
        ];

        try {
            $response = $this->client->post('https://api.openai.com/v1/chat/completions', [
                'headers' => $this->getHeaders(),
                'json' => $data,
                'timeout' => 180
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if (isset($result['choices'][0]['message']['content'])) {
                return [
                    'success' => true,
                    'content' => $result['choices'][0]['message']['content'],
                    'model' => $result['model'],
                    'usage' => $result['usage'] ?? []
                ];
            }

            return [
                'success' => false,
                'error' => 'Invalid response format from OpenAI API'
            ];

        } catch (RequestException $e) {
            Log::error('OpenAI Chat Completion API Error: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'OpenAI API request failed: ' . $e->getMessage()
            ];
        } catch (\Exception $e) {
            Log::error('OpenAI Chat Completion System Error: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'System error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate business intelligence summary (specialized method)
     */
    public function generateBusinessSummary(string $businessContext, string $period = ''): array
    {
        $systemPrompt = 'Bạn là một chuyên gia phân tích kinh doanh chuyên về nền tảng thương mại điện tử với 15+ năm kinh nghiệm. Hãy phân tích dữ liệu kinh doanh được cung cấp và tạo một báo cáo tổng hợp CỰC KỲ CHI TIẾT bằng tiếng Việt. Báo cáo phải dài, toàn diện với insights sâu sắc, phân tích xu hướng chi tiết, và khuyến nghị cụ thể có thể thực hiện được. Hãy viết như một báo cáo chuyên nghiệp cho CEO/CTO với đầy đủ số liệu, biểu đồ mô tả, và kế hoạch hành động chi tiết.';

        $userPrompt = "Hãy phân tích dữ liệu kinh doanh thương mại điện tử sau đây và cung cấp một báo cáo tổng hợp CỰC KỲ CHI TIẾT bằng tiếng Việt" . ($period ? " cho khoảng thời gian {$period}" : "") . ":

{$businessContext}

Yêu cầu tạo báo cáo TOÀN DIỆN với ít nhất 8000-10000 từ, bao gồm:

## 1. TÓM TẮT ĐIỀU HÀNH (Executive Summary)
- Tổng quan tình hình kinh doanh (5-7 đoạn văn chi tiết)
- Các điểm nổi bật và thành tựu chính
- Thách thức và cơ hội quan trọng
- Khuyến nghị ưu tiên cao nhất

## 2. PHÂN TÍCH CHỈ SỐ HIỆU SUẤT CHÍNH (KPI Analysis)
### 2.1 Doanh thu và Tăng trưởng:
- Phân tích chi tiết xu hướng doanh thu
- So sánh với các kỳ trước (nếu có data)
- Dự báo xu hướng tăng trưởng
- Phân tích theo từng segment khách hàng

### 2.2 Hiệu suất Sản phẩm:
- Top sản phẩm bán chạy và lý do
- Phân tích pricing strategy
- Product mix optimization
- Lifecycle analysis của sản phẩm

### 2.3 Hiệu quả Đơn hàng:
- Order fulfillment rate và timeline
- Customer satisfaction metrics
- Return/refund analysis
- Shipping performance

## 3. INSIGHTS KINH DOANH SÂU SẮC (Deep Business Insights)
### 3.1 Phân tích Seller Performance:
- Ranking sellers theo multiple metrics
- Seller growth patterns
- Best practices từ top performers
- Support needs cho underperformers

### 3.2 Chuỗi Cung ứng:
- Supplier performance analysis
- Bottleneck identification
- Cost optimization opportunities
- Risk assessment trong supply chain

### 3.3 Thị trường và Cạnh tranh:
- Market positioning analysis
- Competitive advantages
- Market share opportunities
- Customer behavior insights

## 4. PHÂN TÍCH VẬN HÀNH CHI TIẾT (Operational Deep Dive)
### 4.1 Process Efficiency:
- Order-to-delivery timeline analysis
- Resource utilization metrics
- Automation opportunities
- Quality control measures

### 4.2 Technology và Infrastructure:
- System performance analysis
- Scalability assessment
- Integration opportunities
- Digital transformation roadmap

## 5. KHUYẾN NGHỊ CHIẾN LƯỢC CHI TIẾT (Strategic Recommendations)
### 5.1 Ngắn hạn (30 ngày):
- Immediate action items với timeline cụ thể
- Resource requirements
- Expected outcomes
- Success metrics

### 5.2 Trung hạn (3-6 tháng):
- Strategic initiatives
- Investment requirements
- Implementation roadmap
- Risk mitigation plans

### 5.3 Dài hạn (6-12 tháng):
- Vision và strategic goals
- Market expansion plans
- Technology investments
- Organizational development

## 6. ĐÁNH GIÁ RỦI RO TOÀN DIỆN (Comprehensive Risk Assessment)
### 6.1 Operational Risks:
- Supply chain disruptions
- Quality control issues
- Capacity constraints
- Technology failures

### 6.2 Market Risks:
- Competition threats
- Economic factors
- Regulatory changes
- Customer behavior shifts

### 6.3 Financial Risks:
- Cash flow management
- Pricing pressures
- Cost inflation
- Investment risks

## 7. KẾ HOẠCH HÀNH ĐỘNG CHI TIẾT (Detailed Action Plan)
- Priority matrix cho tất cả recommendations
- Timeline implementation
- Resource allocation
- Success metrics và KPIs
- Review và adjustment process

## 8. PHỤ LỤC VÀ DỮ LIỆU BỔ SUNG (Appendix)
- Detailed data tables
- Calculation methodologies
- Assumptions và limitations
- Glossary of terms

Hãy viết báo cáo như một consultant senior với phong cách chuyên nghiệp, sử dụng bullet points, số liệu cụ thể, và insights actionable. Mỗi section phải có ít nhất 3-5 đoạn văn chi tiết với analysis sâu sắc.";

        $messages = [
            [
                'role' => 'system',
                'content' => $systemPrompt
            ],
            [
                'role' => 'user',
                'content' => $userPrompt
            ]
        ];

        return $this->chatCompletion($messages, 'gpt-4o-mini', 10000);
    }
}