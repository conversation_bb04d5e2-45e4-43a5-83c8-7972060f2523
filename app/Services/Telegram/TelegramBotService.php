<?php

namespace App\Services\Telegram;

use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Api;
use Illuminate\Support\Str;
use Telegram\Bot\Exceptions\TelegramSDKException;
use Telegram\Bot\Keyboard\Keyboard;
use Telegram\Bot\Laravel\Facades\Telegram;

class TelegramBotService
{
    private Api $telegram;
    private bool $shouldSendMessages = true; // Mặc định là true để luôn gửi tin nhắn

    public function __construct()
    {
        try {
            $this->telegram = new Api(config('telegram.bots.special_bot.token'));
            // Không đặt giá trị cho shouldSendMessages ở đây để tránh bị ghi đè bởi giá trị từ config
        } catch (TelegramSDKException $e) {
            Log::error('Failed to initialize Telegram Bot API', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Enable or disable message sending
     *
     * @param bool $enable
     * @return void
     */
    public function setSendMessages(bool $enable): void
    {
        $this->shouldSendMessages = $enable;
    }

    /**
     * Check if message sending is enabled
     *
     * @return bool
     */
    public function isSendingEnabled(): bool
    {
        return $this->shouldSendMessages;
    }

    /**
     * Handle start command.
     *
     * @param int|string $chatId
     * @param string $firstName
     * @return void
     */
    public function handleStartCommand(int|string $chatId, string $firstName): void
    {
        Log::info('Handling start command', [
            'chat_id' => $chatId,
            'first_name' => $firstName
        ]);

        $welcomeMessage = "👋 Xin chào *{$firstName}*!\n\n";
        $welcomeMessage .= "Chào mừng bạn đến với Bot thông báo TikTok Shop.\n";
        $welcomeMessage .= "Để bắt đầu sử dụng, vui lòng lấy mã chat ID và cập nhật vào hồ sơ của bạn.\n\n";
        $welcomeMessage .= "*Chat ID của bạn là:* `{$chatId}`\n\n";
        $welcomeMessage .= "*Hướng dẫn sử dụng:*\n";
        $welcomeMessage .= "1. Sao chép mã chat ID ở trên\n";
        $welcomeMessage .= "2. Truy cập vào hồ sơ của bạn trên hệ thống\n";
        $welcomeMessage .= "3. Dán mã này vào trường 'Telegram Chat ID'\n";
        $welcomeMessage .= "4. Lưu thông tin\n\n";
        $welcomeMessage .= "Sau khi hoàn tất, quay lại đây và nhấn nút bên dưới để kiểm tra.";

        $keyboard = Keyboard::make()
            ->inline()
            ->row([
                Keyboard::inlineButton(['text' => '✅ Kiểm tra đăng ký', 'callback_data' => 'check_registration'])
            ]);

        try {
            $this->telegram->sendMessage([
                'chat_id' => $chatId,
                'text' => $welcomeMessage,
                'parse_mode' => 'Markdown',
                'reply_markup' => $keyboard
            ]);
        } catch (TelegramSDKException $e) {
            Log::error('Failed to send welcome message', [
                'chat_id' => $chatId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle callback query.
     *
     * @param array $callbackQuery
     * @return void
     */
    public function handleCallbackQuery(array $callbackQuery): void
    {
        $chatId = $callbackQuery['from']['id'];
        $callbackData = $callbackQuery['data'];
        $callbackId = $callbackQuery['id'];

        // Answer callback query
        try {
            $this->telegram->answerCallbackQuery([
                'callback_query_id' => $callbackId
            ]);
        } catch (TelegramSDKException $e) {
            Log::error('Failed to answer callback query', [
                'callback_query_id' => $callbackId,
                'error' => $e->getMessage()
            ]);
        }

        // Process callback data
        if ($callbackData === 'check_registration') {
            $this->checkRegistration($chatId);
        }
    }

    /**
     * Check if user has registered their chat ID in the system.
     *
     * @param int|string $chatId
     * @return void
     */
    public function checkRegistration(int|string $chatId): void
    {
        $user = User::where('telegram_chat_id', $chatId)->first();

        if ($user) {
            $successMessage = "✅ *Đăng ký thành công!*\n\n";
            $successMessage .= "Chào mừng *{$user->name}* đã kết nối với hệ thống thông báo Telegram.\n\n";
            $successMessage .= "Bạn sẽ nhận được các thông báo sau:\n";
            $successMessage .= "• Đơn hàng mới\n";
            $successMessage .= "• Cập nhật trạng thái đơn hàng\n";
            $successMessage .= "• Thông báo thanh toán\n";
            $successMessage .= "• Báo cáo định kỳ\n\n";
            $successMessage .= "Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!";

            try {
                $this->telegram->sendMessage([
                    'chat_id' => $chatId,
                    'text' => $successMessage,
                    'parse_mode' => 'Markdown'
                ]);
            } catch (TelegramSDKException $e) {
                Log::error('Failed to send success message', [
                    'chat_id' => $chatId,
                    'error' => $e->getMessage()
                ]);
            }
        } else {
            $errorMessage = "❌ *Chưa đăng ký thành công*\n\n";
            $errorMessage .= "Không tìm thấy tài khoản nào được liên kết với Chat ID này.\n\n";
            $errorMessage .= "*Vui lòng kiểm tra:*\n";
            $errorMessage .= "1. Bạn đã nhập đúng Chat ID vào hồ sơ\n";
            $errorMessage .= "2. Bạn đã lưu thông tin hồ sơ\n";
            $errorMessage .= "3. Bạn đã đăng nhập đúng tài khoản\n\n";
            $errorMessage .= "Chat ID của bạn là: `{$chatId}`\n\n";
            $errorMessage .= "Gửi lại /start nếu bạn cần nhận lại hướng dẫn.";

            try {
                $this->telegram->sendMessage([
                    'chat_id' => $chatId,
                    'text' => $errorMessage,
                    'parse_mode' => 'Markdown'
                ]);
            } catch (TelegramSDKException $e) {
                Log::error('Failed to send error message', [
                    'chat_id' => $chatId,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Clean and sanitize message text for Telegram.
     *
     * This function:
     * - Escapes Markdown special characters
     * - Trims excessive whitespace
     * - Ensures message doesn't exceed Telegram's limits
     * - Optionally truncates long messages with an ellipsis
     *
     * @param string $text Raw message text
     * @param bool $escapeMarkdown Whether to escape Markdown special characters
     * @param int $maxLength Maximum message length (Telegram limit is 4096)
     * @param bool $truncate Whether to truncate with ellipsis if text exceeds max length
     * @return string Cleaned message text
     */
    public function cleanMessage(string $text, bool $escapeMarkdown = true, int $maxLength = 4096, bool $truncate = true): string
    {
        // Trim whitespace
        $cleanText = trim($text);

        // Escape Markdown special characters if required
        if ($escapeMarkdown) {
            // Escape Markdown special characters: _ * [ ] ( ) ~ ` > # + - = | { } . !
            $markdownChars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!'];

            foreach ($markdownChars as $char) {
                $cleanText = str_replace($char, '\\' . $char, $cleanText);
            }
        }

        // Replace multiple newlines with just two
        $cleanText = preg_replace('/\n{3,}/', "\n\n", $cleanText);

        // Ensure message doesn't exceed Telegram's limit
        if (Str::length($cleanText) > $maxLength) {
            if ($truncate) {
                $cleanText = Str::limit($cleanText, $maxLength - 3, '...');
            } else {
                $cleanText = Str::limit($cleanText, $maxLength, '');
            }
        }

        return $cleanText;
    }

    /**
     * Send a message to a chat.
     *
     * @param int|string $chatId
     * @param string $text
     * @param array $options
     * @return array
     */
    public function sendMessage(int|string $chatId, string $text, array $options = []): array
    {
        try {
            $response = Telegram::sendMessage(array_merge([
                'chat_id' => $chatId,
                'text' => $text,
                'parse_mode' => 'Markdown'
            ], $options));

            return [
                'success' => true,
                'message_id' => $response->getMessageId()
            ];
        } catch (\Exception $e) {
            Log::error('Error sending Telegram message', [
                'error' => $e->getMessage(),
                'chat_id' => $chatId
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send an order notification to a user.
     *
     * @param mixed $order The order object or ID
     * @return array
     */
    public function sendOrderNotification($order): array
    {
        // Get order details
        $orderId = is_object($order) ? $order->id : $order;
        $orderStatus = is_object($order) ? $order->status : 'new';
        $orderAmount = is_object($order) ? $order->total : 0;

        // Get user and chat ID
        $userId = is_object($order) ? $order->user_id : null;

        if (!$userId) {
            return [
                'success' => false,
                'error' => 'User ID not found in order'
            ];
        }

        $user = User::find($userId);

        if (!$user || !$user->telegram_chat_id) {
            return [
                'success' => false,
                'error' => 'User does not have Telegram chat ID'
            ];
        }

        $chatId = $user->telegram_chat_id;

        // Build message
        $statusEmoji = $this->getStatusEmoji($orderStatus);
        $message = "{$statusEmoji} *ĐƠN HÀNG MỚI #{$orderId}*\n\n";
        $message .= "• Trạng thái: *{$orderStatus}*\n";
        $message .= "• Giá trị: *{$orderAmount}đ*\n";
        $message .= "• Thời gian: *" . now()->format('d/m/Y H:i:s') . "*\n\n";
        $message .= "[Xem chi tiết đơn hàng](https://your-app-url.com/orders/{$orderId})";

        // Send message
        return $this->sendMessage($chatId, $message);
    }

    /**
     * Send a payment notification to a user.
     *
     * @param mixed $payment The payment object or ID
     * @return array
     */
    public function sendPaymentNotification($payment): array
    {
        // Get payment details
        $paymentId = is_object($payment) ? $payment->id : $payment;
        $paymentStatus = is_object($payment) ? $payment->status : 'completed';
        $paymentAmount = is_object($payment) ? $payment->amount : 0;

        // Get user and chat ID
        $userId = is_object($payment) ? $payment->user_id : null;

        if (!$userId) {
            return [
                'success' => false,
                'error' => 'User ID not found in payment'
            ];
        }

        $user = User::find($userId);

        if (!$user || !$user->telegram_chat_id) {
            return [
                'success' => false,
                'error' => 'User does not have Telegram chat ID'
            ];
        }

        $chatId = $user->telegram_chat_id;

        // Build message
        $statusEmoji = $this->getPaymentStatusEmoji($paymentStatus);
        $message = "{$statusEmoji} *THANH TOÁN #{$paymentId}*\n\n";
        $message .= "• Trạng thái: *{$paymentStatus}*\n";
        $message .= "• Số tiền: *{$paymentAmount}đ*\n";
        $message .= "• Thời gian: *" . now()->format('d/m/Y H:i:s') . "*\n\n";
        $message .= "[Xem chi tiết thanh toán](https://your-app-url.com/payments/{$paymentId})";

        // Send message
        return $this->sendMessage($chatId, $message);
    }

    /**
     * Get emoji for order status.
     *
     * @param string $status
     * @return string
     */
    private function getStatusEmoji(string $status): string
    {
        return match (strtolower($status)) {
            'new' => '🆕',
            'processing' => '⏳',
            'shipped' => '🚚',
            'delivered' => '✅',
            'cancelled' => '❌',
            default => '📦',
        };
    }

    /**
     * Get emoji for payment status.
     *
     * @param string $status
     * @return string
     */
    private function getPaymentStatusEmoji(string $status): string
    {
        return match (strtolower($status)) {
            'completed' => '✅',
            'pending' => '⏳',
            'failed' => '❌',
            'refunded' => '↩️',
            default => '💰',
        };
    }

    /**
     * Get information about the current webhook.
     *
     * @return array
     */
    public function getWebhookInfo(): array
    {
        try {
            $response = $this->telegram->getWebhookInfo();
            return [
                'ok' => true,
                'result' => $response->toArray()
            ];
        } catch (TelegramSDKException $e) {
            Log::error('Failed to get webhook info', [
                'error' => $e->getMessage()
            ]);

            return [
                'ok' => false,
                'description' => $e->getMessage()
            ];
        }
    }

    public function editMessageText(string $chatId, int $messageId, string $text, array $options = []): array
    {
        try {
            $response = Telegram::editMessageText(array_merge([
                'chat_id' => $chatId,
                'message_id' => $messageId,
                'text' => $text,
                'parse_mode' => 'Markdown'
            ], $options));

            return [
                'success' => true,
                'message_id' => $response->getMessageId()
            ];
        } catch (\Exception $e) {
            Log::error('Error editing Telegram message', [
                'error' => $e->getMessage(),
                'chat_id' => $chatId,
                'message_id' => $messageId
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function deleteMessage(string $chatId, int $messageId): array
    {
        try {
            Telegram::deleteMessage([
                'chat_id' => $chatId,
                'message_id' => $messageId
            ]);

            return [
                'success' => true
            ];
        } catch (\Exception $e) {
            Log::error('Error deleting Telegram message', [
                'error' => $e->getMessage(),
                'chat_id' => $chatId,
                'message_id' => $messageId
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
