<?php

namespace App\Services\Telegram;

use App\Models\User;
use Illuminate\Support\Facades\Log;

class TelegramVerificationService
{
    protected TelegramBotService $telegramService;

    public function __construct(TelegramBotService $telegramService)
    {
        $this->telegramService = $telegramService;
    }

    public function verifyUser(User $user, string $chatId): array
    {
        try {
            // Check if chat ID is already linked to another user
            $existingChat = User::where('telegram_chat_id', $chatId)
                ->where('id', '!=', $user->id)
                ->first();

            if ($existingChat) {
                return [
                    'success' => false,
                    'message' => 'Chat ID này đã được liên kết với một tài khoản khác.'
                ];
            }

            // Create or update the Telegram chat
            $chat = User::updateOrCreate(
                ['telegram_chat_id' => $chatId],
                [
                    'id' => $user->id,
                    'verified_at' => now(),
                    'last_interaction_at' => now()
                ]
            );

            // Update user's telegram_chat_id
            $user->update(['telegram_chat_id' => $chatId]);

            // Send confirmation message via Telegram
            $this->telegramService->sendMessage(
                $chatId,
                "✅ *Xác thực thành công!*\n\n" .
                "Tài khoản của bạn đã được liên kết thành công. " .
                "Bạn sẽ nhận được thông báo khi có đơn hàng mới.",
                ['parse_mode' => 'Markdown']
            );

            Log::info('Telegram verification successful', [
                'user_id' => $user->id,
                'chat_id' => $chatId
            ]);

            return [
                'success' => true,
                'chat' => $chat
            ];

        } catch (\Exception $e) {
            Log::error('Telegram verification error in service', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'chat_id' => $chatId
            ]);

            return [
                'success' => false,
                'message' => 'Đã xảy ra lỗi trong quá trình xác thực. Vui lòng thử lại sau.'
            ];
        }
    }
}
