<?php

namespace App\Services\Telegram;

use App\Models\User;
use Telegram\Bot\Api;
use Telegram\Bot\Exceptions\TelegramSDKException;
use Illuminate\Support\Facades\Log;

class TelegramNotificationService
{
    protected Api $telegram;
    protected bool $shouldSendMessages;

    public function __construct(Api $telegram)
    {
        $this->telegram = $telegram;
        $this->shouldSendMessages = false;
    }

    /**
     * Enable or disable message sending
     */
    public function setSendMessages(bool $enable): void
    {
        $this->shouldSendMessages = $enable;
    }

    /**
     * Send order notification
     *
     * @param mixed $order
     * @return array
     */
    public function sendOrderNotification($order): array
    {
        if (!$this->shouldSendMessages) {
            return [
                'success' => true,
                'message' => 'Message sending is disabled'
            ];
        }

        try {
            // Get order details
            $orderId = is_object($order) ? $order->id : $order;
            $orderStatus = is_object($order) ? $order->status : 'new';
            $orderAmount = is_object($order) ? $order->total : 0;
            $userId = is_object($order) ? $order->user_id : null;

            if (!$userId) {
                return [
                    'success' => false,
                    'error' => 'User ID not found in order'
                ];
            }

            $user = User::find($userId);

            if (!$user || !$user->telegram_chat_id) {
                return [
                    'success' => false,
                    'error' => 'User does not have Telegram chat ID'
                ];
            }

            // Build message
            $statusEmoji = $this->getStatusEmoji($orderStatus);
            $message = "{$statusEmoji} *ĐƠN HÀNG MỚI #{$orderId}*\n\n";
            $message .= "• Trạng thái: *{$orderStatus}*\n";
            $message .= "• Giá trị: *{$orderAmount}đ*\n";
            $message .= "• Thời gian: *" . now()->format('d/m/Y H:i:s') . "*\n\n";
            $message .= "[Xem chi tiết đơn hàng](" . config('app.url') . "/orders/{$orderId})";

            // Send message
            $response = $this->telegram->sendMessage([
                'chat_id' => $user->telegram_chat_id,
                'text' => $message,
                'parse_mode' => 'Markdown'
            ]);

            return [
                'success' => true,
                'result' => $response->toArray()
            ];

        } catch (TelegramSDKException $e) {
            Log::error('Failed to send order notification', [
                'order_id' => $orderId ?? null,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send payment notification
     *
     * @param mixed $payment
     * @return array
     */
    public function sendPaymentNotification($payment): array
    {
        if (!$this->shouldSendMessages) {
            return [
                'success' => true,
                'message' => 'Message sending is disabled'
            ];
        }

        try {
            // Get payment details
            $paymentId = is_object($payment) ? $payment->id : $payment;
            $paymentStatus = is_object($payment) ? $payment->status : 'completed';
            $paymentAmount = is_object($payment) ? $payment->amount : 0;
            $userId = is_object($payment) ? $payment->user_id : null;

            if (!$userId) {
                return [
                    'success' => false,
                    'error' => 'User ID not found in payment'
                ];
            }

            $user = User::find($userId);

            if (!$user || !$user->telegram_chat_id) {
                return [
                    'success' => false,
                    'error' => 'User does not have Telegram chat ID'
                ];
            }

            // Build message
            $statusEmoji = $this->getPaymentStatusEmoji($paymentStatus);
            $message = "{$statusEmoji} *THANH TOÁN #{$paymentId}*\n\n";
            $message .= "• Trạng thái: *{$paymentStatus}*\n";
            $message .= "• Số tiền: *{$paymentAmount}đ*\n";
            $message .= "• Thời gian: *" . now()->format('d/m/Y H:i:s') . "*\n\n";
            $message .= "[Xem chi tiết thanh toán](" . config('app.url') . "/payments/{$paymentId})";

            // Send message
            $response = $this->telegram->sendMessage([
                'chat_id' => $user->telegram_chat_id,
                'text' => $message,
                'parse_mode' => 'Markdown'
            ]);

            return [
                'success' => true,
                'result' => $response->toArray()
            ];

        } catch (TelegramSDKException $e) {
            Log::error('Failed to send payment notification', [
                'payment_id' => $paymentId ?? null,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get emoji for order status
     */
    protected function getStatusEmoji(string $status): string
    {
        return match (strtolower($status)) {
            'new' => '🆕',
            'processing' => '⏳',
            'shipped' => '🚚',
            'delivered' => '✅',
            'cancelled' => '❌',
            default => '📦',
        };
    }

    /**
     * Get emoji for payment status
     */
    protected function getPaymentStatusEmoji(string $status): string
    {
        return match (strtolower($status)) {
            'completed' => '✅',
            'pending' => '⏳',
            'failed' => '❌',
            'refunded' => '↩️',
            default => '💰',
        };
    }

    /**
     * Send notification to all registered sellers
     *
     * @param string $message The message to send
     * @param array $attachedData Additional data to include in the message
     * @return array
     */
    public function notifyAllSellers(string $message, array $attachedData = []): array
    {
        if (!$this->shouldSendMessages) {
            return [
                'success' => true,
                'message' => 'Message sending is disabled'
            ];
        }

        try {
            // Get all users with role 'seller' and non-null telegram_chat_id
            $sellers = User::where('role', 'seller')
                ->whereNotNull('telegram_chat_id')
                ->get();

            $results = [];
            foreach ($sellers as $seller) {
                try {
                    // Send message to each seller
                    $response = $this->telegram->sendMessage([
                        'chat_id' => $seller->telegram_chat_id,
                        'text' => $message,
                        'parse_mode' => 'Markdown'
                    ]);

                    $results[$seller->id] = [
                        'success' => true,
                        'telegram_chat_id' => $seller->telegram_chat_id
                    ];

                    // Log successful notification
                    Log::info('Sent notification to seller', [
                        'seller_id' => $seller->id,
                        'telegram_chat_id' => $seller->telegram_chat_id
                    ]);
                } catch (\Exception $e) {
                    $results[$seller->id] = [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'telegram_chat_id' => $seller->telegram_chat_id
                    ];

                    // Log failed notification
                    Log::error('Failed to send notification to seller', [
                        'seller_id' => $seller->id,
                        'telegram_chat_id' => $seller->telegram_chat_id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return [
                'success' => true,
                'results' => $results,
                'total_sellers' => $sellers->count()
            ];

        } catch (\Exception $e) {
            Log::error('Failed to notify sellers', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send new order notification to all sellers
     *
     * @param mixed $order
     * @return array
     */
    public function sendNewOrderNotificationToSellers($order): array
    {
        // Get order details
        $orderId = is_object($order) ? $order->id : $order;
        $orderStatus = is_object($order) ? $order->status : 'new';
        $orderAmount = is_object($order) ? $order->total : 0;

        // Build message
        $statusEmoji = $this->getStatusEmoji($orderStatus);
        $message = "{$statusEmoji} *ĐƠN HÀNG MỚI #{$orderId}*\n\n";
        $message .= "• Trạng thái: *{$orderStatus}*\n";
        $message .= "• Giá trị: *{$orderAmount}đ*\n";
        $message .= "• Thời gian: *" . now()->format('d/m/Y H:i:s') . "*\n\n";
        $message .= "[Xem chi tiết đơn hàng](" . config('app.url') . "/orders/{$orderId})";

        // Send notification to all sellers
        return $this->notifyAllSellers($message, [
            'order_id' => $orderId,
            'order_status' => $orderStatus,
            'order_amount' => $orderAmount
        ]);
    }
}
