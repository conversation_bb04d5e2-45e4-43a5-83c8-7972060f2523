<?php

namespace App\Services;

use App\Enums\CostType;

class FinanceCostService
{
    public static function formatCosts(array $rawCosts): array
    {
        $operational = [];
        $salary = [];
        $total = 0;

        // Xử lý shared_design riêng
        $sharedDesign = null;
        if (isset($rawCosts['shared_design_details'])) {
            $sharedDesign = [
                'total' => $rawCosts['shared_design'] ?? 0,
                'details' => $rawCosts['shared_design_details']
            ];
            $total += $sharedDesign['total'];
        }

        // Xử lý các chi phí khác
        foreach ($rawCosts as $key => $value) {
            if ($key === 'shared_design_details' || $key === 'shared_design') {
                continue;
            }

            $costType = CostType::tryFrom($key);
            if (!$costType) continue;

            $amount = is_numeric($value) ? $value : 0;

            if ($costType->group() === 'salary') {
                $salary[$key] = $amount;
            } else {
                $operational[$key] = $amount;
            }
            
            $total += $amount;
        }

        // Thêm shared_design vào operational nếu có
        if ($sharedDesign) {
            $operational['shared_design'] = $sharedDesign;
        }

        return [
            'operational' => $operational,
            'salary' => $salary,
            'total' => $total
        ];
    }

    public static function getCostTypes(): array
    {
        return collect(CostType::cases())
            ->mapWithKeys(fn($type) => [$type->value => $type->label()])
            ->toArray();
    }
} 