<?php

namespace App\Services;

use App\Models\TikTokShop;
use Illuminate\Support\Facades\Http;

class Tokapi
{
    const API_KEY = 'cfed19d60c744db68ec48a85bb8d61cf';
    const PROJECT_NAME = 'tokapi';
    const BASE_URL = 'https://api.tokapi.online/v1/shop/';

    protected function sendRequest($endpoint, $params = [])
    {
        $response = Http::withHeaders([
            'accept' => 'application/json',
            'x-project-name' => self::PROJECT_NAME,
            'x-api-key' => self::API_KEY,
        ])->timeout(60)
          ->get(self::BASE_URL . $endpoint, $params);

        if ($response->failed()) {
            $this->handleError($response);
        }
        dd($response->json());
        return $response->json();
    }

    protected function handleError($response)
    {
        $responseBody = $response->json();
        $errorMessage = isset($responseBody['error']) ? $responseBody['error'] : json_encode($responseBody);
        throw new \Exception('Failed to fetch data: ' . $errorMessage);
    }

    public function getProduct($productID)
    {
        return $this->sendRequest('product/' . $productID, ['region' => 'US']);
    }

    public function searchProductsByKeyword($query, $sortBy = 'BEST_SELLERS', $page = 1)
    {
        $params = [
            'keyword' => $query,
            'region' => 'US',
            'offset' => ($page - 1) * 12,
            'count' => 12,
            'sort_by' => $sortBy,
        ];

        return $this->sendRequest('search', $params);
    }

    public function searchProductsBySellerId($sellerId, $scrollParam = null)
    {
        $params = [
            'seller_id' => $sellerId,
            'region' => 'US',
            'count' => 20,
            'sort_by' => 'RELEVANCE',
        ];

        if ($scrollParam) {
            $params['scroll_param'] = $scrollParam;
        }

        return $this->sendRequest('seller/products/list', $params);
    }

    public function getSellerDetails($sellerId, $scrollParam = null)
    {
        $params = [
            'seller_id' => $sellerId,
            'region' => 'US',
        ];

        if ($scrollParam) {
            $params['scroll_param'] = $scrollParam;
        }

        return $this->sendRequest('seller/details', $params);
    }
    
}
