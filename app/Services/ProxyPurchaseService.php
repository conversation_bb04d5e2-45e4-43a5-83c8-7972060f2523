<?php

namespace App\Services;

use App\Models\User;
use App\Models\ProxyPurchase;
use App\Models\ProxyAccount;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class ProxyPurchaseService
{
    private string $apiUrl;
    private array $defaultHeaders;
    private array $cookies;

    public function __construct()
    {
        $this->apiUrl = 'https://www.proxies.com/proxy';
        $this->loadConfiguration();
    }

    /**
     * Load configuration từ ProxyAccount hoặc cache
     */
    private function loadConfiguration(): void
    {
        // Ưu tiên lấy từ ProxyAccount
        $account = ProxyAccount::getBestAccount();

        if ($account) {
            $this->apiUrl = $account->api_url;
            $this->defaultHeaders = $account->getFullHeaders();
            $this->cookies = $account->getParsedCookies();
            return;
        }

        // Fallback về cache nếu không có account
        $config = Cache::get('proxy_purchase_config', [
            'cookies' => [],
            'headers' => [
                'accept' => 'application/json, text/javascript, */*; q=0.01',
                'accept-language' => 'en-US,en;q=0.8',
                'cache-control' => 'no-cache',
                'content-type' => 'application/x-www-form-urlencoded; charset=UTF-8',
                'origin' => 'https://www.proxies.com',
                'pragma' => 'no-cache',
                'priority' => 'u=1, i',
                'referer' => 'https://www.proxies.com/proxy/create',
                'sec-ch-ua' => '"Chromium";v="136", "Brave";v="136", "Not.A/Brand";v="99"',
                'sec-ch-ua-mobile' => '?0',
                'sec-ch-ua-platform' => '"Linux"',
                'sec-fetch-dest' => 'empty',
                'sec-fetch-mode' => 'cors',
                'sec-fetch-site' => 'same-origin',
                'sec-gpc' => '1',
                'user-agent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'x-requested-with' => 'XMLHttpRequest',
            ]
        ]);

        $this->defaultHeaders = $config['headers'];
        $this->cookies = $config['cookies'];
    }

    /**
     * Validate user có thể mua proxy không
     */
    public function validateUserLimits(User $user, int $quantity = 1, float $estimatedCost = 0): array
    {
        $quota = $user->getRemainingProxyQuota();
        
        $errors = [];

        // Check số lượng
        if ($quota['remaining_count'] < $quantity) {
            $errors[] = "Bạn chỉ có thể mua thêm {$quota['remaining_count']} proxy hôm nay. Đã sử dụng: {$quota['used_count']}/{$user->getProxyDailyLimit()}";
        }

        // Check budget
        if ($quota['remaining_budget'] < $estimatedCost) {
            $errors[] = "Ngân sách còn lại: $" . number_format($quota['remaining_budget'], 2) . ". Chi phí ước tính: $" . number_format($estimatedCost, 2);
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'quota' => $quota
        ];
    }

    /**
     * Mua proxy từ API
     */
    public function purchaseProxy(User $user, array $params): array
    {
        try {
            // Validate input
            $validationResult = $this->validatePurchaseParams($params);
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'message' => 'Tham số không hợp lệ',
                    'errors' => $validationResult['errors']
                ];
            }

            // Validate user limits
            $quantity = $params['numberPorts'] ?? 1;
            $estimatedCost = $this->estimateCost($params);
            
            $limitsCheck = $this->validateUserLimits($user, $quantity, $estimatedCost);
            if (!$limitsCheck['valid']) {
                return [
                    'success' => false,
                    'message' => 'Vượt quá giới hạn cho phép',
                    'errors' => $limitsCheck['errors']
                ];
            }

            // Tạo record pending
            $proxyPurchase = $this->createPendingRecord($user, $params);

            // Gọi API
            $apiResponse = $this->callProxyAPI($params);

            // Xử lý response
            return $this->handleAPIResponse($proxyPurchase, $apiResponse);

        } catch (Exception $e) {
            Log::error('Proxy purchase failed', [
                'user_id' => $user->id,
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi mua proxy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate purchase parameters
     */
    private function validatePurchaseParams(array $params): array
    {
        $errors = [];

        // Required fields
        $required = ['type', 'numberPorts', 'username', 'password'];
        foreach ($required as $field) {
            if (empty($params[$field])) {
                $errors[] = "Thiếu thông tin: {$field}";
            }
        }

        // Validate proxy type
        $allowedTypes = ['dedicated', 'shared'];
        if (!in_array($params['type'] ?? '', $allowedTypes)) {
            $errors[] = "Loại proxy không hợp lệ";
        }

        // Validate number of ports
        $numberPorts = $params['numberPorts'] ?? 0;
        if (!is_numeric($numberPorts) || $numberPorts < 1 || $numberPorts > 10) {
            $errors[] = "Số lượng port phải từ 1-10";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Ước tính chi phí (có thể cần điều chỉnh theo API thực tế)
     */
    private function estimateCost(array $params): float
    {
        $basePrice = 3.0; // $3 per proxy
        $quantity = $params['numberPorts'] ?? 1;

        return $basePrice * $quantity;
    }

    /**
     * Tạo record pending
     */
    private function createPendingRecord(User $user, array $params): ProxyPurchase
    {
        return ProxyPurchase::create([
            'user_id' => $user->id,
            'proxy_type' => $params['type'],
            'proxy_location' => is_array($params['geolocs']) ? implode(',', $params['geolocs']) : $params['geolocs'],
            'number_ports' => $params['numberPorts'],
            'username' => $params['username'],
            'password' => $params['password'],
            'status' => 'pending',
            'proxy_data' => [], // Sẽ được cập nhật sau khi có response từ API
            'api_request_data' => $params,
            'api_response_data' => null,
            'error_message' => null,
            'cost' => null,
            'currency' => 'USD',
            'transaction_id' => null,
            'expires_at' => null,
            'purchase_date' => now(),
        ]);
    }

    /**
     * Gọi API mua proxy
     */
    private function callProxyAPI(array $params): Response
    {
        // Chuẩn bị data cho API
        $postData = $this->prepareAPIData($params);

        // Debug: Log API payload
        Log::info('Proxy API Payload:', [
            'url' => $this->apiUrl,
            'payload' => $postData,
            'headers' => $this->defaultHeaders
        ]);

        // Gọi API với cookies và headers
        $httpClient = Http::withHeaders($this->defaultHeaders);

        // Chỉ thêm cookies nếu có và không rỗng
        if (!empty($this->cookies)) {
            // Convert array cookies thành cookie string
            $cookieString = '';
            foreach ($this->cookies as $name => $value) {
                $cookieString .= $name . '=' . $value . '; ';
            }
            $cookieString = rtrim($cookieString, '; ');

            if ($cookieString) {
                $httpClient = $httpClient->withHeaders(['Cookie' => $cookieString]);
            }
        }

        $response = $httpClient->timeout(30)
                                  ->asForm()
                                  ->post($this->apiUrl, $postData);

        // Debug: Log API response
        Log::info('Proxy API Response:', [
            'status' => $response->status(),
            'headers' => $response->headers(),
            'body' => $response->body()
        ]);

        return $response;
    }

    /**
     * Chuẩn bị data cho API call
     */
    private function prepareAPIData(array $params): array
    {
        $data = [
            'website[]' => '',
            'numberPorts' => $params['numberPorts'],
            'type' => $params['type'],
            'username' => $params['username'],
            'password' => $params['password'],
            'authIps' => $params['authIps'] ?? '',
        ];

        // Thêm geolocation nếu có
        if (!empty($params['geolocs'])) {
            foreach ($params['geolocs'] as $location) {
                $data["geolocs[{$location}]"] = '1';
            }
        }

        return $data;
    }

    /**
     * Xử lý API response
     */
    private function handleAPIResponse(ProxyPurchase $proxyPurchase, Response $response): array
    {
        $responseData = $response->json();
        
        // Update record với response data
        $proxyPurchase->update([
            'api_response_data' => $responseData,
        ]);

        // Kiểm tra response thành công
        if ($response->successful() && $this->isSuccessfulResponse($responseData)) {
            return $this->handleSuccessfulPurchase($proxyPurchase, $responseData);
        } else {
            return $this->handleFailedPurchase($proxyPurchase, $response, $responseData);
        }
    }

    /**
     * Check xem response có thành công không
     */
    private function isSuccessfulResponse(?array $responseData): bool
    {
        // API trả về status: true và isConfirmed: true khi thành công
        return isset($responseData['status']) && $responseData['status'] === true &&
               isset($responseData['isConfirmed']) && $responseData['isConfirmed'] === true;
    }

    /**
     * Xử lý purchase thành công
     */
    private function handleSuccessfulPurchase(ProxyPurchase $proxyPurchase, array $responseData): array
    {
        $proxyPurchase->update([
            'status' => 'success',
            'proxy_data' => $responseData,
            'cost' => $this->estimateCost($proxyPurchase->api_request_data), // Estimate cost vì API không trả về
            'transaction_id' => uniqid('proxy_'), // Generate transaction ID
            'expires_at' => now()->addDays(30), // Default 30 days
        ]);

        return [
            'success' => true,
            'message' => 'Mua proxy thành công! Proxy đã được tạo và sẽ sớm có sẵn.',
            'data' => $proxyPurchase->fresh(),
            'proxy_info' => $responseData,
            'redirect' => $responseData['redirect'] ?? null
        ];
    }

    /**
     * Xử lý purchase thất bại
     */
    private function handleFailedPurchase(ProxyPurchase $proxyPurchase, Response $response, ?array $responseData): array
    {
        // Trả về error message từ response body
        $errorMessage = 'Có lỗi xảy ra khi mua proxy';
        $errors = [];

        // Ưu tiên lấy message từ response body
        if (isset($responseData['message'])) {
            $errorMessage = $responseData['message'];
        } elseif (isset($responseData['error'])) {
            $errorMessage = $responseData['error'];
        } elseif (isset($responseData['errors']) && is_array($responseData['errors'])) {
            foreach ($responseData['errors'] as $error) {
                if (is_array($error)) {
                    $errors = array_merge($errors, $error);
                } else {
                    $errors[] = $error;
                }
            }
            if (!empty($errors)) {
                $errorMessage = implode('; ', $errors);
            }
        } else {
            // Fallback: sử dụng response body nếu có
            $body = $response->body();
            if (!empty($body) && $body !== '{}') {
                $errorMessage = $body;
            } else {
                $errorMessage = "HTTP {$response->status()}: " . $response->reason();
            }
        }

        $proxyPurchase->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);

        return [
            'success' => false,
            'message' => $errorMessage, // Hiển thị lỗi cụ thể thay vì "Mua proxy thất bại"
            'error' => $errorMessage,
            'errors' => $errors,
            'data' => $proxyPurchase->fresh()
        ];
    }

    /**
     * Update configuration (cookies, headers, etc.)
     */
    public function updateConfiguration(array $config): void
    {
        Cache::put('proxy_purchase_config', $config, now()->addDays(7));
        $this->loadConfiguration();
    }

    /**
     * Get current configuration
     */
    public function getConfiguration(): array
    {
        return [
            'headers' => $this->defaultHeaders,
            'cookies' => $this->cookies,
            'api_url' => $this->apiUrl
        ];
    }

    /**
     * Test ProxyAccount bằng cách gọi trang home
     */
    public function testProxyAccount(ProxyAccount $account): array
    {
        try {
            $headers = [
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'accept-language' => 'en-US,en;q=0.8',
                'cache-control' => 'no-cache',
                'pragma' => 'no-cache',
                'priority' => 'u=0, i',
                'referer' => 'https://www.proxies.com/',
                'sec-ch-ua' => '"Chromium";v="136", "Brave";v="136", "Not.A/Brand";v="99"',
                'sec-ch-ua-mobile' => '?0',
                'sec-ch-ua-platform' => '"Linux"',
                'sec-fetch-dest' => 'document',
                'sec-fetch-mode' => 'navigate',
                'sec-fetch-site' => 'same-origin',
                'sec-fetch-user' => '?1',
                'sec-gpc' => '1',
                'upgrade-insecure-requests' => '1',
                'user-agent' => $account->user_agent ?: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            ];

            // Tạo cookie jar từ cookies string
            $cookieJar = new \GuzzleHttp\Cookie\CookieJar();
            $parsedCookies = $account->getParsedCookies();

            foreach ($parsedCookies as $name => $value) {
                $cookieJar->setCookie(new \GuzzleHttp\Cookie\SetCookie([
                    'Name' => $name,
                    'Value' => $value,
                    'Domain' => 'www.proxies.com',
                    'Path' => '/',
                ]));
            }

            $response = Http::withHeaders($headers)
                ->withOptions([
                    'cookies' => $cookieJar,
                    'timeout' => 30,
                    'verify' => false,
                ])
                ->get('https://www.proxies.com/home');

            $statusCode = $response->status();
            $body = $response->body();

            // Kiểm tra xem có đăng nhập thành công không
            $isLoggedIn = false;
            $username = null;

            // Tìm kiếm các dấu hiệu đăng nhập
            if (str_contains($body, 'logout') ||
                str_contains($body, 'dashboard') ||
                str_contains($body, 'account') ||
                str_contains($body, 'profile') ||
                str_contains($body, 'My Account') ||
                str_contains($body, 'Sign Out')) {
                $isLoggedIn = true;

                // Thử extract username từ HTML
                if (preg_match('/welcome[^>]*>([^<]+)</i', $body, $matches)) {
                    $username = trim($matches[1]);
                } elseif (preg_match('/hello[^>]*>([^<]+)</i', $body, $matches)) {
                    $username = trim($matches[1]);
                } elseif (preg_match('/Hi[^>]*>([^<]+)</i', $body, $matches)) {
                    $username = trim($matches[1]);
                }
            }

            // Kiểm tra có redirect về login không
            if (str_contains($body, 'login') && str_contains($body, 'password')) {
                $isLoggedIn = false;
            }

            return [
                'success' => true,
                'status_code' => $statusCode,
                'is_logged_in' => $isLoggedIn,
                'username' => $username,
                'response_size' => strlen($body),
                'cookies_count' => count($parsedCookies),
                'message' => $isLoggedIn ?
                    'Cookie hợp lệ - Đã đăng nhập thành công' . ($username ? " (User: {$username})" : '') :
                    'Cookie có thể đã hết hạn - Không phát hiện trạng thái đăng nhập',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Lỗi khi test cookie: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ];
        }
    }
}
