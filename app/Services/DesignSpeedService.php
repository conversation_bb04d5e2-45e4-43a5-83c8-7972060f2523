<?php

namespace App\Services;

use App\Models\DesignJob;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DesignSpeedService
{
    /**
     * Lấy thống kê tốc độ design theo ngày
     */
    public function getDesignAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        $dailyStats = $this->getDailyDesignStats($startDate, $endDate);
        $summary = $this->calculateSummaryStats($dailyStats);
        $weekendAnalysis = $this->analyzeWeekendPerformance($dailyStats);
        $chartData = $this->prepareChartData($dailyStats);
        $designerStats = $this->getDesignerPerformanceStats($startDate, $endDate);

        return [
            'daily_stats' => $dailyStats,
            'summary' => $summary,
            'weekend_analysis' => $weekendAnalysis,
            'chart_data' => $chartData,
            'designer_stats' => $designerStats,
            'period' => [
                'start' => $startDate->format('d/m/Y'),
                'end' => $endDate->format('d/m/Y'),
                'days' => $startDate->diffInDays($endDate) + 1
            ]
        ];
    }

    /**
     * Lấy thống kê design theo từng ngày
     */
    private function getDailyDesignStats(Carbon $startDate, Carbon $endDate): array
    {
        $stats = [];
        $currentDate = $startDate->copy();

        while ($currentDate <= $endDate) {
            $dayStart = $currentDate->copy()->startOfDay();
            $dayEnd = $currentDate->copy()->endOfDay();

            // Đếm số design jobs hoàn thành trong ngày
            $completedJobs = DesignJob::where('status', 'completed')
                ->whereNotNull('completed_at')
                ->whereBetween('completed_at', [$dayStart, $dayEnd])
                ->count();

            // Tính thời gian trung bình từ tạo job đến hoàn thành trong ngày
            $avgDesignTime = $this->getAverageDesignTimeForDay($dayStart, $dayEnd);

            // Phân tích theo giờ trong ngày
            $hourlyBreakdown = $this->getHourlyDesignBreakdown($dayStart, $dayEnd);

            // Thống kê theo designer
            $designerCount = $this->getActiveDesignersForDay($dayStart, $dayEnd);

            $stats[] = [
                'date' => $currentDate->format('Y-m-d'),
                'date_display' => $currentDate->format('d/m/Y'),
                'day_name' => $this->getVietnameseDayName($currentDate->dayOfWeek),
                'day_of_week' => $currentDate->dayOfWeek,
                'is_weekend' => $currentDate->isWeekend(),
                'completed_jobs' => $completedJobs,
                'avg_design_time_hours' => round($avgDesignTime, 2),
                'avg_design_time_display' => $this->formatTimeDisplay($avgDesignTime),
                'hourly_breakdown' => $hourlyBreakdown,
                'active_designers' => $designerCount,
                'jobs_per_designer' => $designerCount > 0 ? round($completedJobs / $designerCount, 1) : 0,
                'performance_level' => $this->getPerformanceLevel($avgDesignTime, $completedJobs),
            ];

            $currentDate->addDay();
        }

        return $stats;
    }

    /**
     * Tính thời gian trung bình từ tạo job đến hoàn thành cho một ngày
     */
    private function getAverageDesignTimeForDay(Carbon $dayStart, Carbon $dayEnd): float
    {
        $avgTime = DesignJob::where('status', 'completed')
            ->whereNotNull('completed_at')
            ->whereNotNull('created_at')
            ->whereBetween('completed_at', [$dayStart, $dayEnd])
            ->where('completed_at', '>=', DB::raw('created_at'))
            ->select(DB::raw('AVG(TIMESTAMPDIFF(MINUTE, created_at, completed_at)) as avg_minutes'))
            ->value('avg_minutes') ?? 0;

        return $avgTime / 60; // Chuyển từ phút sang giờ
    }

    /**
     * Đếm số designer active trong ngày
     */
    private function getActiveDesignersForDay(Carbon $dayStart, Carbon $dayEnd): int
    {
        return DesignJob::where('status', 'completed')
            ->whereNotNull('completed_at')
            ->whereNotNull('designer_id')
            ->whereBetween('completed_at', [$dayStart, $dayEnd])
            ->distinct('designer_id')
            ->count('designer_id');
    }

    /**
     * Phân tích design theo giờ trong ngày
     */
    private function getHourlyDesignBreakdown(Carbon $dayStart, Carbon $dayEnd): array
    {
        $hourlyData = [];
        
        for ($hour = 0; $hour < 24; $hour++) {
            $hourStart = $dayStart->copy()->hour($hour)->minute(0)->second(0);
            $hourEnd = $hourStart->copy()->addHour()->subSecond();

            $count = DesignJob::where('status', 'completed')
                ->whereNotNull('completed_at')
                ->whereBetween('completed_at', [$hourStart, $hourEnd])
                ->count();
            
            $hourlyData[] = [
                'hour' => $hour,
                'hour_display' => sprintf('%02d:00', $hour),
                'completed_jobs' => $count
            ];
        }

        return $hourlyData;
    }

    /**
     * Tính toán thống kê tổng quan
     */
    private function calculateSummaryStats(array $dailyStats): array
    {
        if (empty($dailyStats)) {
            return [
                'total_completed_jobs' => 0,
                'avg_daily_jobs' => 0,
                'avg_design_time_hours' => 0,
                'slowest_day' => null,
                'fastest_day' => null,
                'total_active_designers' => 0,
            ];
        }

        $totalJobs = array_sum(array_column($dailyStats, 'completed_jobs'));
        $avgDailyJobs = round($totalJobs / count($dailyStats), 1);

        // Tính thời gian design trung bình (chỉ tính những ngày có job hoàn thành)
        $daysWithJobs = array_filter($dailyStats, fn($day) => $day['completed_jobs'] > 0);
        $avgDesignTime = 0;
        if (!empty($daysWithJobs)) {
            $avgDesignTime = array_sum(array_column($daysWithJobs, 'avg_design_time_hours')) / count($daysWithJobs);
        }

        // Tìm ngày chậm nhất và nhanh nhất
        $slowestDay = null;
        $fastestDay = null;
        
        if (!empty($daysWithJobs)) {
            $slowestDay = collect($daysWithJobs)->sortByDesc('avg_design_time_hours')->first();
            $fastestDay = collect($daysWithJobs)->sortBy('avg_design_time_hours')->first();
        }

        // Tổng số designer unique
        $totalActiveDesigners = collect($dailyStats)->sum('active_designers');

        return [
            'total_completed_jobs' => $totalJobs,
            'avg_daily_jobs' => $avgDailyJobs,
            'avg_design_time_hours' => round($avgDesignTime, 2),
            'avg_design_time_display' => $this->formatTimeDisplay($avgDesignTime),
            'slowest_day' => $slowestDay,
            'fastest_day' => $fastestDay,
            'total_active_designers' => $totalActiveDesigners,
        ];
    }

    /**
     * Phân tích hiệu suất cuối tuần vs ngày thường
     */
    private function analyzeWeekendPerformance(array $dailyStats): array
    {
        $weekendDays = array_filter($dailyStats, fn($day) => $day['is_weekend'] && $day['completed_jobs'] > 0);
        $weekdayDays = array_filter($dailyStats, fn($day) => !$day['is_weekend'] && $day['completed_jobs'] > 0);

        $weekendAvg = 0;
        $weekdayAvg = 0;

        if (!empty($weekendDays)) {
            $weekendAvg = array_sum(array_column($weekendDays, 'avg_design_time_hours')) / count($weekendDays);
        }

        if (!empty($weekdayDays)) {
            $weekdayAvg = array_sum(array_column($weekdayDays, 'avg_design_time_hours')) / count($weekdayDays);
        }

        $weekendSlowerPercentage = 0;
        if ($weekdayAvg > 0) {
            $weekendSlowerPercentage = (($weekendAvg - $weekdayAvg) / $weekdayAvg) * 100;
        }

        return [
            'weekend_avg' => round($weekendAvg, 1),
            'weekday_avg' => round($weekdayAvg, 1),
            'weekend_slower_percentage' => round($weekendSlowerPercentage, 1),
            'weekend_avg_display' => $this->formatTimeDisplay($weekendAvg),
            'weekday_avg_display' => $this->formatTimeDisplay($weekdayAvg),
        ];
    }

    /**
     * Chuẩn bị dữ liệu cho biểu đồ
     */
    private function prepareChartData(array $dailyStats): array
    {
        $labels = [];
        $completedJobs = [];
        $avgTimes = [];

        foreach ($dailyStats as $day) {
            $labels[] = $day['date_display'] . ' (' . $day['day_name'] . ')';
            $completedJobs[] = $day['completed_jobs'];
            $avgTimes[] = $day['avg_design_time_hours'];
        }

        return [
            'labels' => $labels,
            'completed_jobs' => $completedJobs,
            'avg_time' => $avgTimes,
        ];
    }

    /**
     * Thống kê hiệu suất designer
     */
    private function getDesignerPerformanceStats(Carbon $startDate, Carbon $endDate): array
    {
        $designerStats = DesignJob::select('designer_id')
            ->selectRaw('COUNT(*) as total_jobs')
            ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, created_at, completed_at)) as avg_time_minutes')
            ->where('status', 'completed')
            ->whereNotNull('completed_at')
            ->whereNotNull('designer_id')
            ->whereBetween('completed_at', [$startDate, $endDate])
            ->groupBy('designer_id')
            ->get();

        $result = [];
        foreach ($designerStats as $stat) {
            $designer = User::find($stat->designer_id);
            $result[] = [
                'designer_id' => $stat->designer_id,
                'designer_name' => $designer->name ?? 'Unknown',
                'total_jobs' => $stat->total_jobs,
                'avg_time_hours' => round($stat->avg_time_minutes / 60, 2),
                'avg_time_display' => $this->formatTimeDisplay($stat->avg_time_minutes / 60),
            ];
        }

        // Sort by total jobs descending
        usort($result, function($a, $b) {
            return $b['total_jobs'] - $a['total_jobs'];
        });

        return $result;
    }

    /**
     * Lấy tên ngày trong tuần bằng tiếng Việt
     */
    private function getVietnameseDayName(int $dayOfWeek): string
    {
        $days = [
            0 => 'Chủ nhật',
            1 => 'Thứ 2',
            2 => 'Thứ 3', 
            3 => 'Thứ 4',
            4 => 'Thứ 5',
            5 => 'Thứ 6',
            6 => 'Thứ 7'
        ];

        return $days[$dayOfWeek] ?? 'Không xác định';
    }

    /**
     * Định dạng hiển thị thời gian
     */
    private function formatTimeDisplay(float $hours): string
    {
        if ($hours < 1) {
            return round($hours * 60) . ' phút';
        } elseif ($hours < 24) {
            return round($hours, 1) . ' giờ';
        } else {
            $days = floor($hours / 24);
            $remainingHours = $hours - ($days * 24);

            if ($remainingHours < 0.1) {
                return $days . ' ngày';
            } elseif ($remainingHours < 1) {
                $minutes = round($remainingHours * 60);
                return $days . ' ngày ' . $minutes . ' phút';
            } else {
                $roundedHours = round($remainingHours, 1);
                return $days . ' ngày ' . $roundedHours . ' giờ';
            }
        }
    }

    /**
     * Đánh giá mức độ hiệu suất
     */
    private function getPerformanceLevel(float $avgTime, int $completedJobs): string
    {
        if ($completedJobs == 0) return 'no_data';
        
        if ($avgTime <= 2) return 'excellent';      // <= 2 giờ
        if ($avgTime <= 6) return 'good';           // <= 6 giờ  
        if ($avgTime <= 12) return 'average';       // <= 12 giờ
        if ($avgTime <= 24) return 'slow';          // <= 1 ngày
        return 'very_slow';                         // > 1 ngày
    }
}
