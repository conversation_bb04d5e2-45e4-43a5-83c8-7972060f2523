<?php

namespace App\Services;

use App\Models\User;
use App\Models\TelegramUser;
use App\Models\TelegramSend;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Laravel\Facades\Telegram;
use Exception;

class TelegramBotManagementService
{
    /**
     * Check if Telegram bot is properly configured
     */
    private function isBotConfigured(): bool
    {
        try {
            $token = config('telegram.bots.special_bot.token');
            return !empty($token) && $token !== 'YOUR-BOT-TOKEN';
        } catch (\Exception $e) {
            Log::error('Error checking bot configuration', ['error' => $e->getMessage()]);
            return false;
        }
    }
    /**
     * Gửi tin nhắn đến một user cụ thể thông qua telegram user đã đăng ký
     */
    public function sendMessageToUser(User $sender, User $recipient, string $message): ?TelegramSend
    {
        // Tìm telegram user của recipient
        $telegramUser = $recipient->activeTelegramUser();
        
        if (!$telegramUser) {
            Log::warning('User does not have active telegram user', [
                'recipient_id' => $recipient->id,
                'recipient_email' => $recipient->email,
            ]);
            return null;
        }

        // Tạo record trong database
        $telegramSend = TelegramSend::create([
            'user_id' => $sender->id,
            'recipient_user_id' => $recipient->id,
            'message' => $message,
            'status' => 'pending',
        ]);

        try {
            // Gửi tin nhắn qua Telegram API sử dụng special_bot
            $response = Telegram::bot('special_bot')->sendMessage([
                'chat_id' => $telegramUser->chat_id,
                'text' => $message,
                'parse_mode' => 'HTML',
            ]);

            $telegramSend->markAsSent([
                'message_id' => $response->getMessageId(),
                'telegram_user_id' => $telegramUser->id,
            ]);

            Log::info('Telegram message sent successfully via bot management', [
                'sender_id' => $sender->id,
                'recipient_id' => $recipient->id,
                'telegram_user_id' => $telegramUser->id,
                'message_id' => $telegramSend->id,
            ]);

            return $telegramSend;

        } catch (Exception $e) {
            $telegramSend->markAsFailed($e->getMessage());
            Log::error('Failed to send Telegram message via bot management', [
                'sender_id' => $sender->id,
                'recipient_id' => $recipient->id,
                'telegram_user_id' => $telegramUser->id,
                'error' => $e->getMessage(),
            ]);

            return $telegramSend;
        }
    }

    /**
     * Gửi tin nhắn đến nhiều users
     */
    public function sendBulkMessage(User $sender, array $recipientIds, string $message): array
    {
        $results = [];
        
        foreach ($recipientIds as $recipientId) {
            $recipient = User::find($recipientId);
            if ($recipient) {
                $result = $this->sendMessageToUser($sender, $recipient, $message);
                if ($result) {
                    $results[] = $result;
                }
            }
        }

        return $results;
    }

    /**
     * Gửi tin nhắn đến users theo role
     */
    public function sendMessageToRole(User $sender, string $role, string $message): array
    {
        $users = User::role($role)
            ->whereHas('telegramUsers', function ($query) {
                $query->active();
            })
            ->get();

        $results = [];
        foreach ($users as $user) {
            $result = $this->sendMessageToUser($sender, $user, $message);
            if ($result) {
                $results[] = $result;
            }
        }

        return $results;
    }

    /**
     * Gửi thông báo hệ thống đến tất cả users đã đăng ký
     */
    public function sendSystemNotification(string $message, ?User $sender = null): array
    {
        $systemUser = $sender ?? User::where('email', '<EMAIL>')->first();
        if (!$systemUser) {
            // Tạo system user nếu chưa có
            $systemUser = User::create([
                'name' => 'System',
                'email' => 'system@' . config('app.domain', 'example.com'),
                'password' => bcrypt('random_password_' . time()),
            ]);
        }

        $telegramUsers = TelegramUser::active()->linked()->get();
        $results = [];

        foreach ($telegramUsers as $telegramUser) {
            if ($telegramUser->user) {
                $result = $this->sendMessageToUser($systemUser, $telegramUser->user, $message);
                if ($result) {
                    $results[] = $result;
                }
            }
        }

        return $results;
    }

    /**
     * Lấy thống kê telegram users
     */
    public function getStatistics(): array
    {
        return [
            'total_telegram_users' => TelegramUser::count(),
            'active_telegram_users' => TelegramUser::active()->count(),
            'linked_telegram_users' => TelegramUser::linked()->count(),
            'unlinked_telegram_users' => TelegramUser::unlinked()->count(),
            'users_with_telegram' => User::whereHas('telegramUsers', function ($query) {
                $query->active();
            })->count(),
        ];
    }

    /**
     * Lấy danh sách telegram users với thông tin chi tiết
     */
    public function getTelegramUsers(array $filters = []): \Illuminate\Database\Eloquent\Collection
    {
        $query = TelegramUser::with('user');

        if (isset($filters['active'])) {
            $query->where('is_active', $filters['active']);
        }

        if (isset($filters['linked'])) {
            if ($filters['linked']) {
                $query->linked();
            } else {
                $query->unlinked();
            }
        }

        if (isset($filters['role'])) {
            $query->whereHas('user', function ($q) use ($filters) {
                $q->role($filters['role']);
            });
        }

        return $query->orderBy('last_interaction_at', 'desc')->get();
    }

    /**
     * Liên kết telegram user với system user
     */
    public function linkTelegramUser(string $telegramId, int $userId): bool
    {
        try {
            $telegramUser = TelegramUser::findByTelegramId($telegramId);
            $user = User::find($userId);

            if (!$telegramUser || !$user) {
                return false;
            }

            $telegramUser->linkToUser($user);
            
            // Gửi thông báo xác nhận
            $message = "🎉 *Liên kết thành công!*\n\n";
            $message .= "Tài khoản Telegram của bạn đã được liên kết với:\n";
            $message .= "• Tên: {$user->name}\n";
            $message .= "• Email: {$user->email}\n\n";
            $message .= "Bạn sẽ bắt đầu nhận thông báo từ hệ thống.";

            Telegram::bot('special_bot')->sendMessage([
                'chat_id' => $telegramUser->chat_id,
                'text' => $message,
                'parse_mode' => 'Markdown'
            ]);

            Log::info('Telegram user linked successfully', [
                'telegram_id' => $telegramId,
                'user_id' => $userId,
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Failed to link telegram user', [
                'telegram_id' => $telegramId,
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Hủy liên kết telegram user
     */
    public function unlinkTelegramUser(string $telegramId): bool
    {
        try {
            $telegramUser = TelegramUser::findByTelegramId($telegramId);
            
            if (!$telegramUser) {
                return false;
            }

            $telegramUser->unlinkFromUser();
            
            // Gửi thông báo
            $message = "🔗 *Hủy liên kết thành công*\n\n";
            $message .= "Tài khoản Telegram của bạn đã được hủy liên kết khỏi hệ thống.\n";
            $message .= "Bạn sẽ không còn nhận thông báo từ hệ thống.";

            Telegram::bot('special_bot')->sendMessage([
                'chat_id' => $telegramUser->chat_id,
                'text' => $message,
                'parse_mode' => 'Markdown'
            ]);

            Log::info('Telegram user unlinked successfully', [
                'telegram_id' => $telegramId,
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Failed to unlink telegram user', [
                'telegram_id' => $telegramId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Kiểm tra trạng thái bot
     */
    public function getBotStatus(): array
    {
        try {
            // Check if bot is configured
            if (!$this->isBotConfigured()) {
                return [
                    'error' => 'Telegram bot is not properly configured. Please check your bot token.',
                    'statistics' => $this->getStatistics(),
                ];
            }

            $botInfo = Telegram::bot('special_bot')->getMe();
            $webhookInfo = Telegram::bot('special_bot')->getWebhookInfo();

            return [
                'bot_info' => [
                    'id' => $botInfo->getId(),
                    'username' => $botInfo->getUsername(),
                    'first_name' => $botInfo->getFirstName(),
                    'is_bot' => $botInfo->isBot(),
                ],
                'webhook_info' => [
                    'url' => $webhookInfo->getUrl(),
                    'has_custom_certificate' => $webhookInfo->hasCustomCertificate(),
                    'pending_update_count' => $webhookInfo->getPendingUpdateCount(),
                    'last_error_date' => $webhookInfo->getLastErrorDate(),
                    'last_error_message' => $webhookInfo->getLastErrorMessage(),
                ],
                'statistics' => $this->getStatistics(),
            ];
        } catch (Exception $e) {
            Log::error('Failed to get bot status', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'error' => $e->getMessage(),
                'statistics' => $this->getStatistics(),
            ];
        }
    }
}
