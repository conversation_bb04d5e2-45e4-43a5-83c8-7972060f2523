<?php

namespace App\Services\Platforms;

use App\Services\Scrapers\Zyte\ZyteService;
use Illuminate\Support\Facades\Log;

class AmazonScraperService
{
    protected $zyteService;

    public function __construct()
    {
        $this->zyteService = new ZyteService();
    }

    public function scrape($data)
    {
        try {
            $product = $this->zyteService->scrapeProduct($data['url']);
            
            return $this->formatProduct($product);
        } catch (\Exception $e) {
            Log::error("Error scraping Amazon product: " . $e->getMessage());
            throw $e;
        }
    }

    public function getRequestData($url)
    {
        return [
            'source' => 'universal',
            'url' => $url,
            'geo_location' => 'United States',
            'parse' => true,
            'render' => 'html',
            'browser_instructions' => [
                'wait_for' => '#productTitle'
            ]
        ];
    }

    protected function formatProduct($product) 
    {
        // Xử lý URLs ảnh
        $imageUrls = collect($product['images'] ?? [])->pluck('url')
            ->map(function ($url) {
                return $this->formatAmazonImageUrl($url);
            })
            ->filter()
            ->values()
            ->toArray();
        
        // Thêm mainImage nếu có và chưa có trong mảng images
        if (!empty($product['mainImage']['url'])) {
            $mainImageUrl = $this->formatAmazonImageUrl($product['mainImage']['url']);
            if ($mainImageUrl && !in_array($mainImageUrl, $imageUrls)) {
                array_unshift($imageUrls, $mainImageUrl);
            }
        }

        return [
            'title' => $product['name'],
            'description' => $product['descriptionHtml'],
            'images' => array_slice($imageUrls, 0, 9),
            'source' => 'amazon',
            'source_type' => 'amazon',
            'external_id' => $product['sku'],
            'url' => $product['canonicalUrl'] ?? $product['url'],
            'source_data' => [
                'url' => $product['url'],
                'platform' => 'amazon',
                'scraped_at' => now()->toIso8601String(),
                'raw_data' => config('app.debug') ? $product : null,
                // 'breadcrumbs' => $product['breadcrumbs'] ?? [],
                // 'variants' => $product['variants'] ?? [],
                // 'features' => $product['features'] ?? [],
                'additional_properties' => collect($product['additionalProperties'] ?? [])->mapWithKeys(function ($item) {
                    return [trim($item['name']) => trim($item['value'])];
                })->toArray()
            ],
            'metadata' => [
                'price' => $product['price'],
                // 'currency' => $product['currency'],
                // 'availability' => $product['availability'],
                // 'color' => $product['color'] ?? null,
                // 'size' => $product['size'] ?? null,
                'rating' => $product['aggregateRating']['ratingValue'] ?? null,
                'reviews_count' => $product['aggregateRating']['reviewCount'] ?? null,
               // 'probability' => $product['metadata']['probability'] ?? null,
                'status' => 'active'
            ]
        ];
    }

    protected function formatAmazonImageUrl($url)
    {
        if (empty($url)) {
            return null;
        }

        // Xử lý các pattern phổ biến của URL ảnh Amazon
        $patterns = [
            '/_AC_[A-Z0-9]+_\./' => '_AC_1000_.',  // Thay thế _AC_XXX_
            '/_SR\d+,\d+_\./' => '_AC_1000_.',     // Thay thế _SRXXX,XXX_
            '/_SX\d+_\./' => '_AC_1000_.',         // Thay thế _SXXX_
            '/_SY\d+_\./' => '_AC_1000_.'          // Thay thế _SYYY_
        ];

        foreach ($patterns as $pattern => $replacement) {
            $url = preg_replace($pattern, $replacement, $url);
        }

        return $url;
    }
} 