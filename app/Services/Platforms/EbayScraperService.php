<?php

namespace App\Services\Platforms;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class EbayScraperService
{
    protected $apiKey;
    protected $apiHost = 'ebay-data-scraper.p.rapidapi.com';

    public function __construct()
    {
        $this->apiKey = 'j1wnlU1rbJmshgHrNTLhetSqBzAKp1SQ9eZjsnRWFfT8j1ryD2';
    }

    public function getEbayId($url)
    {
        // Extract ID from URL patterns
        $patterns = [
            '/itm\/(\d+)/', // Standard item URL
            '/p\/(\d+)/',   // Short URL format
            '/(\d{12})/',   // Direct item ID
            '/(\d{9})/',    // Short item ID format
            '/\/(\d+)(?:\?|$)/' // ID at end of URL
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                return $matches[1];
            }
        }

        // Log error if no ID found
        Log::error('Could not extract eBay item ID', [
            'url' => $url
        ]);
        
        throw new \Exception('Could not extract eBay item ID from URL');
    }

    public function getRequestData($url)
    {
        return [
            'source' => 'universal',
            'url' => $url,
            'parse' => true,
            'render' => 'html',
            'browser_instructions' => [
                'wait_for' => '.x-item-title'
            ]
        ];
    }

    public function scrape($data)
    {
        try {
            // Nếu data là response từ Oxylabs
            if (isset($data['results'])) {
                if (empty($data['results'][0])) {
                    throw new \Exception("Empty results from Oxylabs");
                }
                $content = $data['results'][0]['content'];
                return $this->formatEbayResponse($content);
            }

            // Nếu không, sử dụng RapidAPI
            $itemId = $this->getEbayId($data['url']);
            $response = Http::withHeaders([
                'x-rapidapi-host' => $this->apiHost,
                'x-rapidapi-key' => $this->apiKey
            ])->get("https://{$this->apiHost}/products/{$itemId}", [
                'country' => 'us'
            ]);

            if (!$response->successful()) {
                throw new \Exception('Failed to fetch eBay data: ' . $response->body());
            }

            $rapidApiData = json_decode($response->body(), true);
            if (empty($rapidApiData[0])) {
                throw new \Exception('Empty response from eBay API');
            }

            return $this->formatEbayResponse($rapidApiData[0]);
        } catch (\Exception $e) {
            Log::error('eBay scraping error', [
                'url' => $data['url'] ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    protected function formatEbayResponse($data)
    {
        return [
            'title' => $data['product_name'] ?? '',
            'description' => $data['description'] ?? '',
            'images' => $data['product_images'] ?? [],
            'source_type' => 'ebay',
            'external_id' => $data['product_id'] ?? null,
            'url' => $data['link'] ?? '',
            'metadata' => [
                'price' => $this->parsePrice($data['price'] ?? null),
                'currency' => $this->parseCurrency($data['price'] ?? ''),
                'shipping' => $data['logistics_cost'] ?? null,
                'quantity' => $data['quantity_available'] ?? null,
                'seller' => $data['seller_infos'][0]['seller'] ?? null,
                'seller_rating' => $data['seller_infos'][0]['positive_feedback'] ?? null,
                'seller_items_sold' => $data['seller_infos'][0]['sold_items'] ?? null,
                'upc' => $data['upc'] ?? null,
            ],
            'source_data' => [
                'url' => $data['link'] ?? '',
                'platform' => 'ebay',
                'scraped_at' => now()->toIso8601String(),
                'raw_data' => config('app.debug') ? $data : null
            ]
        ];
    }

    protected function parsePrice($price)
    {
        if (empty($price)) return null;
        
        // Extract numeric value from string like "GBP 15.28"
        preg_match('/[\d,.]+/', $price, $matches);
        return $matches[0] ?? null;
    }

    protected function parseCurrency($price)
    {
        if (empty($price)) return null;
        
        // Extract currency code from string like "GBP 15.28"
        preg_match('/([A-Z]{3})/', $price, $matches);
        return $matches[1] ?? null;
    }
} 