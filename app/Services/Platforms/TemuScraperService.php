<?php

namespace App\Services\Platforms;

use App\Services\Scrapers\Zyte\ZyteService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class TemuScraperService
{
    protected $zyteService;

    public function __construct()
    {
        $this->zyteService = new ZyteService();
    }

    public function scrape($data)
    {
        DB::beginTransaction();
        try {
            $url = $data['url'];
            
            // Scrape bằng Zyte
            $html = $this->zyteService->scrapeWithJS($url);
       
            $dom = new \DOMDocument();
            @$dom->loadHTML($html);
            $xpath = new \DOMXPath($dom);

            // Lấy ID sản phẩm từ URL
            preg_match('/\/(\d+)-/', $url, $matches);
            $externalId = $matches[1] ?? null;

            // Thu thập dữ liệu chi tiết
            $sourceData = [
                'url' => $url,
                'price' => $this->getPrice($xpath),
                'currency' => $this->getCurrency($xpath),
                'variants' => $this->getVariants($xpath),
                'raw_description' => $this->getRawDescription($xpath)
            ];

            $result = [
                'title' => $this->getTitle($xpath),
                'description' => $this->getDescription($xpath),
                'images' => $this->getImages($xpath),
                'source_type' => 'temu',
                'external_id' => $externalId,
                'source_data' => $sourceData,
                'metadata' => [
                    'categories' => $this->getCategories($xpath),
                    'tags' => $this->getTags($xpath),
                    'specifications' => $this->getSpecifications($xpath)
                ]
            ];

            DB::commit();
            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error scraping Temu product', [
                'url' => $url ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    protected function getTitle($xpath)
    {
        $titleNode = $xpath->query("//meta[@property='og:title']/@content")->item(0);
        return $titleNode ? $titleNode->nodeValue : null;
    }

    protected function getDescription($xpath)
    {
        // Lấy mô tả từ các thông số kỹ thuật
        $specs = $this->getSpecifications($xpath);
        return implode("\n", array_map(function($key, $value) {
            return "$key: $value";
        }, array_keys($specs), $specs));
    }

    protected function getImages($xpath)
    {
        $images = [];
        
        // Tìm ảnh với class goods-img-external
        $imageNodes = $xpath->query("//img[contains(@class, 'goods-img-external')]");
        foreach ($imageNodes as $img) {
            if ($img->hasAttribute('src')) {
                $url = $img->getAttribute('src');
                if (strpos($url, 'kwcdn.com') !== false) {
                    // Đảm bảo URL đầy đủ
                    if (strpos($url, 'http') !== 0) {
                        $url = 'https:' . $url;
                    }
                    $images[] = $url;
                }
            }
        }

        // Log để debug
        \Log::info("Found images:", ['images' => $images]);

        return array_unique($images);
    }

    protected function getSpecifications($xpath) 
    {
        $specs = [];
        $specNodes = $xpath->query("//div[contains(@class, 'product-description')]//text()");
        
        foreach ($specNodes as $node) {
            $text = trim($node->nodeValue);
            if (strpos($text, ':') !== false) {
                list($key, $value) = array_map('trim', explode(':', $text, 2));
                $specs[$key] = $value;
            }
        }

        return $specs;
    }

    protected function getCategories($xpath)
    {
        $categories = [];
        $categoryNodes = $xpath->query("//div[contains(@class, 'breadcrumb')]//text()");
        
        foreach ($categoryNodes as $node) {
            $category = trim($node->nodeValue);
            if (!empty($category)) {
                $categories[] = $category;
            }
        }

        return $categories;
    }

    protected function getTags($xpath)
    {
        $tags = [];
        $tagNodes = $xpath->query("//meta[@name='keywords']/@content");
        
        if ($tagNodes->length > 0) {
            $tags = array_map('trim', explode(',', $tagNodes->item(0)->nodeValue));
        }

        return $tags;
    }

    protected function getPrice($xpath)
    {
        $priceNode = $xpath->query("//meta[@property='og:price:amount']/@content")->item(0);
        return $priceNode ? (float)$priceNode->nodeValue : null;
    }

    protected function getCurrency($xpath)
    {
        $currencyNode = $xpath->query("//meta[@property='og:price:currency']/@content")->item(0);
        return $currencyNode ? $currencyNode->nodeValue : 'USD';
    }

    protected function getVariants($xpath)
    {
        $variants = [];
        // Thêm logic lấy các biến thể sản phẩm (size, color, etc.)
        return $variants;
    }

    protected function getRawDescription($xpath)
    {
        $descNode = $xpath->query("//div[contains(@class, 'product-description')]")->item(0);
        return $descNode ? $descNode->nodeValue : null;
    }
} 