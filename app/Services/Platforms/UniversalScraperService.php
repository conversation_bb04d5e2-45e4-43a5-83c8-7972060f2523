<?php

namespace App\Services\Platforms;

use App\Services\Scrapers\Zyte\ZyteService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class UniversalScraperService
{
    protected $zyteService;

    public function __construct()
    {
        $this->zyteService = new ZyteService();
    }

    public function scrape($data)
    {
        DB::beginTransaction();
        try {
            $url = $data['url'];
            
            // Get domain as source type
            $domain = parse_url($url, PHP_URL_HOST);
            $domain = preg_replace('/^www\./', '', $domain);

            // Get product data from Zyte
            $response = $this->zyteService->scrapeUniversalProduct($url);
            
            if (empty($response['product'])) {
                throw new \Exception('No product data returned from Zyte');
            }

            $product = $response['product'];

            // Get all image URLs from product data
            $images = [];
            if (!empty($product['mainImage']['url'])) {
                $images[] = $product['mainImage']['url'];
            }
            if (!empty($product['images'])) {
                foreach ($product['images'] as $image) {
                    if (!empty($image['url'])) {
                        $images[] = $image['url'];
                    }
                }
            }

            $result = [
                'title' => $product['name'] ?? null,
                'description' => $product['description'] ?? null,
                'images' => array_values(array_unique($images)),
                'source_type' => $domain,
                'external_id' => $product['sku'] ?? null,
                'url' => $product['url'] ?? $url,
                'source_data' => [
                    'url' => $url,
                    'platform' => $domain,
                    'scraped_at' => now()->toIso8601String(),
                    'raw_data' => config('app.debug') ? $product : null,
                    'additional_properties' => collect($product['additionalProperties'] ?? [])->mapWithKeys(function ($item) {
                        return [trim($item['name']) => trim($item['value'])];
                    })->toArray()
                ],
                'metadata' => [
                    'price' => $product['price'] ?? null,
                    'currency' => $product['currency'] ?? null,
                    'brand' => $product['brand']['name'] ?? null,
                    'color' => $product['color'] ?? null,
                    'size' => $product['size'] ?? null,
                    'style' => $product['style'] ?? null,
                    'variants' => $product['variants'] ?? [],
                    'features' => $product['features'] ?? [],
                    'status' => 'active'
                ]
            ];

            DB::commit();
            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error scraping universal product', [
                'url' => $url ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
} 