<?php

namespace App\Services\Platforms;

use App\Services\Scrapers\Zyte\ZyteService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class RedbubbleScraperService
{
    protected $zyteService;

    public function __construct()
    {
        $this->zyteService = new ZyteService();
    }

    public function scrape($data)
    {
        DB::beginTransaction();
        try {
            $url = $data['url'];
            
            // Thay thế phần curl bằng Zyte
            $html = $this->zyteService->scrapeUrl($url);
            
            $dom = new \DOMDocument();
            @$dom->loadHTML($html);
            $xpath = new \DOMXPath($dom);

            // Giữ nguyên phần còn lại của logic cũ
            preg_match('/\/(\d+)\./', $url, $matches);
            $productId = $matches[1] ?? null;

            $title = $this->getMetaContent($xpath, "og:title");
            $title = str_replace(' for Sale by', '', $title);

            // Lấy JSON-LD
            $description = '';
            $jsonLdNodes = $xpath->query('//script[@type="application/ld+json"]');
            if ($jsonLdNodes->length > 0) {
                $jsonLd = json_decode($jsonLdNodes[0]->nodeValue, true);
                $description = $jsonLd['description'] ?? '';
            }

            // Lấy hình ảnh
            $images = [];
            $imageNodes = $xpath->query('//meta[@property="og:image"]|//img[contains(@src, "redbubble.net")]');
            foreach ($imageNodes as $node) {
                $imageUrl = $node->getAttribute('content') ?: $node->getAttribute('src');
                if (strpos($imageUrl, '/ssrco,') !== false || 
                    strpos($imageUrl, '/flatlay,') !== false ||
                    strpos($imageUrl, '/flat,') !== false ||
                    strpos($imageUrl, '/front,') !== false ||
                    strpos($imageUrl, '/back,') !== false
                ) {
                    // Đảm bảo lấy kích thước 1000x1000
                    $imageUrl = preg_replace('/,\d+x\d+\./', ',1000x1000.', $imageUrl);
                    $images[] = $imageUrl;
                }
            }

            $result = [
                'title' => $title,
                'description' => $description,
                'images' => array_unique($images),
                'source_type' => 'redbubble',
                'external_id' => $productId,
                'source_data' => [
                    'url' => $url,
                    'platform' => 'redbubble',
                    'scraped_at' => now()->toIso8601String(),
                    'raw_data' => config('app.debug') ? $data : null
                ],
                'metadata' => [
                    'price' => $jsonLd['offers']['price'] ?? null,
                    'currency' => $jsonLd['offers']['priceCurrency'] ?? null,
                    'rating' => $jsonLd['aggregateRating']['ratingValue'] ?? null,
                    'rating_count' => $jsonLd['aggregateRating']['ratingCount'] ?? null
                ]
            ];

            DB::commit();
            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error scraping Redbubble product', [
                'url' => $url ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    protected function getMetaContent($xpath, $property)
    {
        $metaNodes = $xpath->query("//meta[@property='$property']");
        if ($metaNodes->length > 0) {
            return $metaNodes[0]->getAttribute('content');
        }
        return null;
    }

    protected function getHighestQualityImage($imageUrl)
    {
        $imageUrl = str_replace(',x1950,', ',x1950,', $imageUrl);
        $imageUrl = str_replace(',x1860,', ',x1950,', $imageUrl);
        return $imageUrl;
    }
}