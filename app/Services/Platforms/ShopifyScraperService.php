<?php

namespace App\Services\Platforms;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ShopifyScraperService
{
    public function isShopifyStore($url)
    {
        try {
            // Chuyển đổi URL sản phẩm thành URL .json
            $jsonUrl = $this->convertToJsonUrl($url);

            // Thử request đến API endpoint
            $response = Http::get($jsonUrl);

            // Kiểm tra response có phải là JSON Shopify không
            $data = $response->json();
            return isset($data['product']);
        } catch (\Exception $e) {
            Log::error("Error checking Shopify store: " . $e->getMessage());
            return false;
        }
    }

    public function scrape($data)
    {
        try {
            $url = $data['url'];
            $jsonUrl = $this->convertToJsonUrl($url);
            $response = Http::get($jsonUrl);

            if (!$response->successful()) {
                throw new \Exception("Failed to fetch Shopify product data");
            }

            $productData = $response->json()['product'];
            
            // Xử lý description đơn giản
            $description = $productData['body_html'] ?? '';
            $description = strip_tags($description, '<p><ul><li><br><strong><b>'); // Chỉ giữ lại các thẻ cơ bản
            $description = str_replace(['\n', '\r'], '', $description);
            $description = nl2br($description);

            // Chỉ lấy URLs của images
            $images = collect($productData['images'])->pluck('src')->filter()->values()->toArray();

            return [
                'title' => $productData['title'],
                'description' => $description,
                'images' => array_slice($images, 0, 9),
                'source' => 'shopify',
                'source_type' => 'shopify',
                'external_id' => $productData['id'],
                'url' => $url,
                'source_data' => [
                    'url' => $url,
                    'platform' => 'shopify',
                    'scraped_at' => now()->toIso8601String(),
                    'raw_data' => config('app.debug') ? $productData : null,
                    'vendor' => $productData['vendor'],
                    'product_type' => $productData['product_type'],
                    'handle' => $productData['handle'],
                    'variants_count' => count($productData['variants']),
                    'images_count' => count($images)
                ],
                'metadata' => [
                    'tags' => $productData['tags'],
                    'created_at' => $productData['created_at'],
                    'updated_at' => $productData['updated_at'],
                    'published_at' => $productData['published_at'],
                    'template_suffix' => $productData['template_suffix'],
                    'status' => 'active'
                ]
            ];

        } catch (\Exception $e) {
            Log::error("Error scraping Shopify product: " . $e->getMessage(), [
                'url' => $data['url'] ?? null,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    protected function convertToJsonUrl($url)
    {
        // Remove query parameters
        $baseUrl = strtok($url, '?');

        // Ensure URL ends with .json
        if (!Str::endsWith($baseUrl, '.json')) {
            $baseUrl .= '.json';
        }

        return $baseUrl;
    }
}
