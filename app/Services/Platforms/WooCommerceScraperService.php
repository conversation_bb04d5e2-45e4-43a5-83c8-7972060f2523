<?php

namespace App\Services\Platforms;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WooCommerceScraperService
{
    public function scrape($data)
    {
        try {
            $url = $data['url'];
            $productId = $this->getWooCommerceProductId($url);
            $baseUrl = $this->getWooCommerceBaseUrl($url);
            
            $response = Http::withHeaders([
                'accept' => 'application/json',
                'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            ])->get("{$baseUrl}/wp-json/wc/store/v1/products/{$productId}");

            if (!$response->successful()) {
                throw new \Exception('Failed to fetch WooCommerce product');
            }

            $data = $response->json();

            return [
                'title' => $data['name'] ?? '',
                'description' => $data['description'] ?? '',
                'images' => collect($data['images'] ?? [])->take(25)->map(function($image) {
                    return $image['src'];
                })->toArray(),
                'source_type' => 'woocommerce',
                'external_id' => $productId,
                'url' => $url,
                'metadata' => [
                    'price' => $data['prices']['price'] ?? null,
                    'regular_price' => $data['prices']['regular_price'] ?? null,
                    'sale_price' => $data['prices']['sale_price'] ?? null,
                    'currency' => $data['prices']['currency_code'] ?? null,
                    'categories' => collect($data['categories'] ?? [])->pluck('name')->toArray(),
                    'tags' => collect($data['tags'] ?? [])->pluck('name')->toArray(),
                    'sku' => $data['sku'] ?? null,
                    'stock_status' => $data['stock_status'] ?? null,
                ],
                'source_data' => [
                    'url' => $url,
                    'platform' => 'woocommerce',
                    'scraped_at' => now()->toIso8601String(),
                ]
            ];
        } catch (\Exception $e) {
            Log::error('WooCommerce scraping error', [
                'url' => $data['url'] ?? null,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    protected function getWooCommerceProductId($url)
    {
        try {
            // Xóa query parameters trước khi xử lý
            $cleanUrl = preg_replace('/\?.*/', '', $url);
            
            // Các pattern phổ biến cho WooCommerce URL
            $patterns = [
                '/\/product\/([^\/]+)(?:\/)?$/',           // /product/product-name/
                '/\/shop\/([^\/]+)(?:\/)?$/',              // /shop/product-name/
                '/\/products?\/([^\/]+)(?:\/)?$/',         // /products/product-name/
                '/\/([^\/]+)-p-(\d+)(?:\/)?$/',           // /product-name-p-123/
                '/\?(?:.*&)?post=(\d+)/',                 // ?post=123 or ?x=y&post=123
                '/\?(?:.*&)?product=(\d+)/',              // ?product=123
                '/\?(?:.*&)?p=(\d+)/',                    // ?p=123
                '/\/(\d+)-[^\/]+\/?$/',                   // /123-product-name
                '/\/products?\/(\d+)(?:\/|$)/',           // /product/123 or /products/123
            ];

            // Thử tìm ID từ URL path trước
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $url, $matches)) {
                    // Nếu có nhiều matches, lấy match cuối cùng (thường là số ID)
                    return end($matches);
                }
            }

            // Nếu không tìm thấy từ path, thử tìm từ query parameters
            $parsedUrl = parse_url($url);
            if (isset($parsedUrl['query'])) {
                parse_str($parsedUrl['query'], $params);
                
                // Các tham số query phổ biến chứa product ID
                $possibleParams = ['p', 'post', 'product', 'product_id', 'id'];
                
                foreach ($possibleParams as $param) {
                    if (isset($params[$param]) && is_numeric($params[$param])) {
                        return $params[$param];
                    }
                }
            }

            // Nếu vẫn không tìm thấy, thử lấy slug từ URL và query API
            $slug = basename(trim($cleanUrl, '/'));
            if ($slug) {
                $baseUrl = $this->getWooCommerceBaseUrl($url);
                $response = Http::get("{$baseUrl}/wp-json/wc/store/v1/products", [
                    'slug' => $slug
                ]);

                if ($response->successful() && !empty($response->json())) {
                    $products = $response->json();
                    if (isset($products[0]['id'])) {
                        return $products[0]['id'];
                    }
                }
            }

            throw new \Exception('Could not find WooCommerce product ID from URL: ' . $url);
        } catch (\Exception $e) {
            Log::error('Error getting WooCommerce product ID', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    protected function getWooCommerceBaseUrl($url)
    {
        $parsedUrl = parse_url($url);
        return $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
    }

    public function getRequestData($url)
    {
        return [
            'source' => 'universal',
            'url' => $url,
            'geo_location' => 'United States',
            'parse' => true,
            'render' => 'html',
            'browser_instructions' => [
                'wait_for' => '.product_title'
            ]
        ];
    }

    public function isWooCommerceStore($url)
    {
        try {
            $baseUrl = $this->getWooCommerceBaseUrl($url);
            $response = Http::get($baseUrl . '/wp-json/wc/store/v1/products');
            
            // Kiểm tra response và cấu trúc dữ liệu
            if (!$response->successful()) {
                return false;
            }

            $data = $response->json();
            
            // Kiểm tra xem có phải là WooCommerce API response
            if (!is_array($data)) {
                return false;
            }

            // Kiểm tra các thuộc tính đặc trưng của WooCommerce product
            if (empty($data)) {
                // Thử endpoint khác nếu không có sản phẩm
                $response = Http::get($baseUrl . '/wp-json/wc/store/v1/products/categories');
                return $response->successful() && is_array($response->json());
            }

            // Kiểm tra cấu trúc của một sản phẩm
            $firstProduct = $data[0] ?? null;
            return isset($firstProduct['id'], $firstProduct['name'], $firstProduct['type']);

        } catch (\Exception $e) {
            Log::error('Error checking WooCommerce store', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
} 