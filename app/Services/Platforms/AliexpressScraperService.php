<?php

namespace App\Services\Platforms;

use App\Services\Scrapers\Zyte\ZyteService;
use Illuminate\Support\Facades\Log;

class AliexpressScraperService
{
    protected $zyteService;

    public function __construct()
    {
        $this->zyteService = new ZyteService();
    }

    public function scrape($data)
    {
        try {
            // Remove subdomain like 'vi.' from aliexpress url
            $url = preg_replace('/^https?:\/\/[a-z]{2}\./', 'https://', $data['url']);
            $response = $this->zyteService->scrapeUrl($url);
        
            // Parse HTML response để lấy dữ liệu
            $html = $response ?? '';
         
            // Extract window._d_c_.DCData từ script tags
            preg_match('/window\._d_c_\.DCData\s*=\s*({.+?});/s', $html, $matches);
            $dcData = json_decode($matches[1] ?? '{}', true);
            
            // Extract meta tags
            preg_match('/<meta\s+property="og:title"\s+content="([^"]+)"/', $html, $titleMatch);
            preg_match('/<meta\s+property="og:description"\s+content="([^"]+)"/', $html, $descMatch);
            
            return $this->formatProduct([
                'title' => $titleMatch[1] ?? '',
                'description' => $descMatch[1] ?? '',
                'images' => $dcData['imagePathList'] ?? [],
                'url' => $data['url'],
                'raw_data' => $dcData
            ]);

        } catch (\Exception $e) {
            Log::error("Error scraping AliExpress product: " . $e->getMessage());
            throw $e;
        }
    }

    public function getRequestData($url)
    {
        return [
            'url' => $url,
            'httpResponseBody' => true,
            'customHeaders' => [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ],
            'javascript' => true,
            'browserHtml' => true,
            'wait' => 2
        ];
    }

    protected function formatProduct($data) 
    {
        $productId = $this->extractProductId($data['url']);
        
        return [
            'title' => $data['title'],
            'description' => $data['description'],
            'images' => array_slice($data['images'], 0, 9), // Giới hạn 9 ảnh
            'source' => 'aliexpress',
            'source_type' => 'aliexpress',
            'external_id' => $productId,
            'url' => $data['url'],
            'source_data' => [
                'url' => $data['url'],
                'platform' => 'aliexpress',
                'scraped_at' => now()->toIso8601String(),
                'raw_data' => config('app.debug') ? $data['raw_data'] : null,
                'additional_properties' => []
            ],
            'metadata' => [
                'price' => $data['raw_data']['price'] ?? null,
                'rating' => $data['raw_data']['rating'] ?? null,
                'reviews_count' => $data['raw_data']['reviews'] ?? null,
                'status' => 'active'
            ]
        ];
    }

    protected function extractProductId($url)
    {
        preg_match('/item\/(\d+)\.html/', $url, $matches);
        return $matches[1] ?? null;
    }
}
