<?php

namespace App\Services\Platforms;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TiktokScraperService
{
    public function scrape($data)
    {
        try {
            $url = $data['url'];
            $productId = $this->getTiktokProductId($url);
            
            $response = Http::withHeaders([
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            ])->get("https://shop.tiktok.com/view/product/{$productId}?region=US&locale=en");
         
            if (!$response->successful()) {
                throw new \Exception('Failed to fetch TikTok Shop product');
            }

            // Parse HTML content
            $dom = new \DOMDocument();
            @$dom->loadHTML($response->body());
            $xpath = new \DOMXPath($dom);

            // Extract product information
            $title = $this->extractContent($xpath, "//div[contains(@class, 'index-title--AnTxK')]", $dom);
            $description = $this->extractContent($xpath, "//div[contains(@class, 'index-text--cRhk2')]", $dom);
            $price = $this->extractContent($xpath, "//div[contains(@class, 'index-price--hHzq8')]//span", $dom);
            $seller = $this->extractContent($xpath, "//div[contains(@class, 'index-seller--N2k9a')]", $dom);
            $seller = str_replace('Sold by ', '', $seller);

            // Extract images
            $images = [];
            $imageNodes = $xpath->query("//div[contains(@class, 'index-item--XKK77')]//img");
            foreach ($imageNodes as $img) {
                $imgUrl = $img->getAttribute('data-src');
                if ($imgUrl) {
                    $images[] = $imgUrl;
                }
            }
            
            return [
                'title' => $title,
                'description' => $description,
                'images' => array_slice($images, 0, 9), // Take up to 9 images
                'source_type' => 'tiktok',
                'external_id' => $productId,
                'url' => $url,
                'metadata' => [
                    'price' => $price,
                    'currency' => 'USD',
                    'category' => null,
                    'seller' => $seller,
                ],
                'source_data' => [
                    'url' => $url,
                    'platform' => 'tiktok',
                    'scraped_at' => now()->toIso8601String(),
                ]
            ];
        } catch (\Exception $e) {
            Log::error('TikTok scraping error', [
                'url' => $data['url'] ?? null,
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    protected function getTiktokProductId($url)
    {
        $url = preg_replace('/\?.*/', '', $url);
        
        if (!Str::contains($url, 'shop.tiktok.com/view/product/')) {
            throw new \Exception('Invalid TikTok Shop URL');
        }

        if (!preg_match('/product\/(\d+)/', $url, $matches)) {
            throw new \Exception('Could not find TikTok Shop product ID');
        }

        return $matches[1];
    }

    public function getRequestData($url)
    {
        return [
            'source' => 'universal',
            'url' => $url,
            'geo_location' => 'United States',
            'parse' => true,
            'render' => 'html',
            'browser_instructions' => [
                'wait_for' => '.product-title'
            ]
        ];
    }

    // Helper method to extract content
    private function extractContent($xpath, $query, $dom) 
    {
        $nodes = $xpath->query($query);
        if (!$nodes->length) {
            return '';
        }

        // Nếu là title (chứa class index-title--AnTxK)
        if (strpos($query, 'index-title--AnTxK') !== false) {
            return trim($nodes[0]->textContent);
        }

        // Cho description và các nội dung khác giữ nguyên HTML
        $content = '';
        foreach ($nodes as $node) {
            $content .= $dom->saveHTML($node);
        }
        return $content;
    }
} 