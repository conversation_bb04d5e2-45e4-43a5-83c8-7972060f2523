<?php

namespace App\Services\Platforms;

use App\Services\Scrapers\Zyte\ZyteService;
use Illuminate\Support\Facades\Log;

class WalmartScraperService
{
    protected $zyteService;

    public function __construct()
    {
        $this->zyteService = new ZyteService();
    }

    public function scrape($data)
    {
        try {
            $product = $this->zyteService->scrapeProduct($data['url']);
            return $this->formatProduct($product);
        } catch (\Exception $e) {
            Log::error("Error scraping Walmart product: " . $e->getMessage());
            throw $e;
        }
    }

    protected function formatProduct($product) 
    {
        // Xử lý URLs ảnh
        $imageUrls = collect($product['images'] ?? [])->pluck('url')
            ->map(function ($url) {
                return $this->formatWalmartImageUrl($url);
            })
            ->filter()
            ->values()
            ->toArray();
        
        // Thêm mainImage nếu có
        if (!empty($product['mainImage']['url'])) {
            $mainImageUrl = $this->formatWalmartImageUrl($product['mainImage']['url']);
            if ($mainImageUrl && !in_array($mainImageUrl, $imageUrls)) {
                array_unshift($imageUrls, $mainImageUrl);
            }
        }

        // Lấy GTIN (thường là UPC) từ mảng gtin
        $gtin = null;
        if (!empty($product['gtin'])) {
            $gtin = collect($product['gtin'])->pluck('value')->first();
        }

        return [
            'title' => $product['name'],
            'description' => $product['descriptionHtml'],
            'images' => array_slice($imageUrls, 0, 9),
            'source' => 'walmart',
            'source_type' => 'walmart',
            'external_id' => $product['sku'],
            'url' => $product['canonicalUrl'] ?? $product['url'],
            'source_data' => [
                'url' => $product['url'],
                'platform' => 'walmart',
                'scraped_at' => now()->toIso8601String(),
                'raw_data' => config('app.debug') ? $product : null,
                'breadcrumbs' => $product['breadcrumbs'] ?? [],
                'features' => $product['features'] ?? [],
                'additional_properties' => collect($product['additionalProperties'] ?? [])->mapWithKeys(function ($item) {
                    return [trim($item['name']) => trim($item['value'])];
                })->toArray()
            ],
            'metadata' => [
                'price' => $product['price'],
                'regular_price' => $product['regularPrice'] ?? null,
                'currency' => $product['currency'],
                'availability' => $product['availability'],
                'brand' => $product['brand']['name'] ?? null,
                'mpn' => $product['mpn'] ?? null,
                'gtin' => $gtin,
                'size' => $product['size'] ?? null,
                'rating' => $product['aggregateRating']['ratingValue'] ?? null,
                'reviews_count' => $product['aggregateRating']['reviewCount'] ?? null,
                'probability' => $product['metadata']['probability'] ?? null,
                'status' => 'active'
            ]
        ];
    }

    protected function formatWalmartImageUrl($url)
    {
        if (empty($url)) {
            return null;
        }

        // Thay thế kích thước ảnh thành lớn nhất (2000x2000)
        return preg_replace(
            ['/odnHeight=\d+/', '/odnWidth=\d+/'],
            ['odnHeight=2000', 'odnWidth=2000'],
            $url
        );
    }
}