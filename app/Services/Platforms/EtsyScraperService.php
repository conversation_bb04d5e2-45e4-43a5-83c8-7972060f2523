<?php

namespace App\Services\Platforms;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class EtsyScraperService
{
    public function scrape($data)
    {
        try {
            $url = $data['url'];
            $listingId = $this->getEtsyId($url);
            
            $response = Http::withHeaders([
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            ])->get("https://www.etsy.com/api/v3/ajax/public/listings/{$listingId}");

            if (!$response->successful()) {
                throw new \Exception('Failed to fetch Etsy listing');
            }

            $data = $response->json();

            return $this->formatResponse($data, $url);
        } catch (\Exception $e) {
            Log::error('Etsy scraping error', [
                'url' => $data['url'] ?? null,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    protected function getEtsyId($url)
    {
        $url = preg_replace('/\?.*/', '', $url);
        
        if (!preg_match('/listing\/(\d+)/', $url, $matches)) {
            throw new \Exception('Could not find Etsy listing ID');
        }

        return $matches[1];
    }

    public function getRequestData($url)
    {
        return [
            'source' => 'universal',
            'url' => $url,
            'geo_location' => 'United States',
            'parse' => true,
            'render' => 'html',
            'browser_instructions' => [
                'wait_for' => '.listing-page-title'
            ]
        ];
    }

    protected function formatResponse($data, $url)
    {
        $description = $data['description'] ?? '';
        $description = nl2br($description);
        $description = str_replace(['\n', '\r'], '', $description);

        return [
            'title' => $data['title'] ?? '',
            'description' => $description,
            'images' => collect($data['images'] ?? [])->take(25)->toArray(),
            'source_type' => 'etsy',
            'external_id' => $data['listing_id'] ?? null,
            'url' => $url,
            'metadata' => [
                'price' => $data['price'] ?? null,
                'currency' => $data['currency_code'] ?? null,
                'tags' => $data['tags'] ?? [],
                'category' =>  $data['taxonomy_node']['path'] ?? null,
                'shop' => [
                    'name' => $data['shop_name'] ?? null,
                    'id' => $data['shop_id'] ?? null,
                    'avatar' => $data['seller_avatar'] ?? null,
                ],
                'state' => $data['state'] ?? null,
                'quantity' => $data['quantity'] ?? null,
                'is_customizable' => $data['is_customizable'] ?? false,
                'is_digital' => $data['is_digital'] ?? false,
                'is_sold_out' => $data['is_sold_out'] ?? false,
                'is_bestseller' => $data['is_bestseller'] ?? false,
                'is_top_rated' => $data['is_top_rated'] ?? false,
                'favorites' => $data['favorites'] ?? 0,
                'views' => $data['views'] ?? 0,
                'ships_from' => $data['ships_from_country'] ?? null,
                'created_at' => isset($data['create_date']) 
                    ? Carbon::createFromTimestamp($data['create_date'])->toIso8601String() 
                    : null,
                'updated_at' => isset($data['update_date'])
                    ? Carbon::createFromTimestamp($data['update_date'])->toIso8601String()
                    : null,
            ],
            'source_data' => [
                'url' => $url,
                'platform' => 'etsy',
                'scraped_at' => now()->toIso8601String(),
                'raw_data' => config('app.debug') ? $data : null
            ]
        ];
    }
} 