<?php

namespace App\Services;

use App\Models\User;
use App\Models\AiPrompt;
use App\Models\UserAiPrompt;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AiPromptService
{
    /**
     * Lấy prompt đã customize cho user theo type
     */
    public function getUserPrompt(int $userId, string $type): ?string
    {
        $user = User::find($userId);
        if (!$user) {
            return null;
        }

        // 1. Tìm user prompt customization
        $userPrompt = $user->getAiPromptByType($type);
        
        if ($userPrompt) {
            // User đã có customization
            return $userPrompt->renderCustomPrompt();
        }
        
        // 2. Fallback về default prompt
        $defaultPrompt = AiPrompt::defaultForType($type)->first();
        
        return $defaultPrompt?->renderPrompt();
    }

    /**
     * Lấy system prompt cho user theo type
     */
    public function getSystemPrompt(int $userId, string $type): ?string
    {
        $user = User::find($userId);
        if (!$user) {
            return null;
        }

        // 1. Tìm user prompt customization
        $userPrompt = $user->getAiPromptByType($type);
        
        if ($userPrompt && $userPrompt->aiPrompt) {
            // Render system prompt với custom variables
            $systemPrompt = $userPrompt->aiPrompt->system_prompt;
            $customVariables = $userPrompt->custom_variables ?? [];
            
            foreach ($customVariables as $key => $value) {
                $systemPrompt = str_replace("{{$key}}", $value, $systemPrompt);
            }
            
            return $systemPrompt;
        }
        
        // 2. Fallback về default prompt
        $defaultPrompt = AiPrompt::defaultForType($type)->first();
        
        return $defaultPrompt?->system_prompt;
    }

    /**
     * Assign prompt cho user (chỉ super_admin và User Manager)
     */
    public function assignPromptToUser(
        int $userId,
        int $promptId,
        array $customVariables = [],
        array $customRequirements = [],
        array $customTargetMetrics = [],
        ?int $assignedBy = null
    ): UserAiPrompt {
        // Kiểm tra quyền của người thực hiện
        $currentUser = auth()->user();
        if (!$currentUser || (!$currentUser->hasRole('super_admin') && !$currentUser->hasRole('User Manager'))) {
            throw new \Exception('Bạn không có quyền assign AI prompt cho user');
        }

        // Deactivate existing prompts of same type for this user
        $prompt = AiPrompt::find($promptId);
        if ($prompt) {
            UserAiPrompt::where('user_id', $userId)
                ->whereHas('aiPrompt', function ($query) use ($prompt) {
                    $query->where('type', $prompt->type);
                })
                ->update(['is_active' => false]);
        }

        $userPrompt = UserAiPrompt::create([
            'user_id' => $userId,
            'ai_prompt_id' => $promptId,
            'custom_variables' => $customVariables,
            'custom_requirements' => $customRequirements,
            'custom_target_metrics' => $customTargetMetrics,
            'is_active' => true
        ]);

        // Log action
        Log::info('AI Prompt assigned to user', [
            'assigned_by' => $currentUser->id,
            'assigned_by_name' => $currentUser->name,
            'assigned_by_role' => $currentUser->roles->pluck('name')->toArray(),
            'user_id' => $userId,
            'prompt_id' => $promptId,
            'prompt_name' => $prompt->name ?? 'Unknown',
        ]);

        return $userPrompt;
    }

    /**
     * Bulk assign prompt cho nhiều users (chỉ super_admin và User Manager)
     */
    public function bulkAssignPromptToUsers(
        array $userIds,
        int $promptId,
        array $customVariables = [],
        array $customRequirements = [],
        array $customTargetMetrics = []
    ): array {
        $currentUser = auth()->user();
        if (!$currentUser || (!$currentUser->hasRole('super_admin') && !$currentUser->hasRole('User Manager'))) {
            throw new \Exception('Bạn không có quyền assign AI prompt cho users');
        }

        $results = [];
        $prompt = AiPrompt::find($promptId);

        foreach ($userIds as $userId) {
            try {
                $userPrompt = $this->assignPromptToUser(
                    $userId,
                    $promptId,
                    $customVariables,
                    $customRequirements,
                    $customTargetMetrics,
                    $currentUser->id
                );

                $results[] = [
                    'user_id' => $userId,
                    'success' => true,
                    'user_prompt_id' => $userPrompt->id
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'user_id' => $userId,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        Log::info('Bulk AI Prompt assignment completed', [
            'assigned_by' => $currentUser->id,
            'prompt_id' => $promptId,
            'prompt_name' => $prompt->name ?? 'Unknown',
            'total_users' => count($userIds),
            'successful' => count(array_filter($results, fn($r) => $r['success'])),
            'failed' => count(array_filter($results, fn($r) => !$r['success']))
        ]);

        return $results;
    }

    /**
     * Unassign prompt từ user (chỉ super_admin và User Manager)
     */
    public function unassignPromptFromUser(int $userId, string $type): bool
    {
        $currentUser = auth()->user();
        if (!$currentUser || (!$currentUser->hasRole('super_admin') && !$currentUser->hasRole('User Manager'))) {
            throw new \Exception('Bạn không có quyền unassign AI prompt từ user');
        }

        $deleted = UserAiPrompt::forUserAndType($userId, $type)->delete();

        Log::info('AI Prompt unassigned from user', [
            'unassigned_by' => $currentUser->id,
            'unassigned_by_name' => $currentUser->name,
            'user_id' => $userId,
            'type' => $type,
            'deleted_count' => $deleted
        ]);

        return $deleted > 0;
    }

    /**
     * Render prompt với data context
     */
    public function renderPromptWithContext(
        string $promptTemplate, 
        array $contextData = [],
        array $additionalVariables = []
    ): string {
        // Merge default variables với additional variables
        $variables = array_merge([
            'current_date' => Carbon::now()->format('d/m/Y'),
            'current_time' => Carbon::now()->format('H:i'),
            'current_datetime' => Carbon::now()->format('d/m/Y H:i'),
        ], $contextData, $additionalVariables);

        $renderedPrompt = $promptTemplate;
        foreach ($variables as $key => $value) {
            if (is_array($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }
            $renderedPrompt = str_replace("{{$key}}", $value, $renderedPrompt);
        }

        return $renderedPrompt;
    }

    /**
     * Lấy tất cả prompts available cho user theo type
     */
    public function getAvailablePrompts(string $type): array
    {
        return AiPrompt::where('type', $type)
            ->where('is_active', true)
            ->orderBy('is_default', 'desc')
            ->orderBy('name')
            ->get()
            ->map(function ($prompt) {
                return [
                    'id' => $prompt->id,
                    'name' => $prompt->name,
                    'description' => $prompt->description,
                    'is_default' => $prompt->is_default,
                    'variables' => $prompt->variables,
                ];
            })
            ->toArray();
    }

    /**
     * Kiểm tra user có prompt customization không
     */
    public function hasUserCustomization(int $userId, string $type): bool
    {
        return UserAiPrompt::forUserAndType($userId, $type)->exists();
    }

    /**
     * Lấy user customization details
     */
    public function getUserCustomization(int $userId, string $type): ?array
    {
        $userPrompt = UserAiPrompt::forUserAndType($userId, $type)->first();
        
        if (!$userPrompt) {
            return null;
        }

        return [
            'id' => $userPrompt->id,
            'prompt_id' => $userPrompt->ai_prompt_id,
            'prompt_name' => $userPrompt->aiPrompt->name,
            'custom_variables' => $userPrompt->custom_variables,
            'custom_requirements' => $userPrompt->custom_requirements,
            'custom_target_metrics' => $userPrompt->custom_target_metrics,
            'is_active' => $userPrompt->is_active,
            'created_at' => $userPrompt->created_at,
            'updated_at' => $userPrompt->updated_at,
        ];
    }

    /**
     * Xóa customization của user
     */
    public function removeUserCustomization(int $userId, string $type): bool
    {
        $deleted = UserAiPrompt::forUserAndType($userId, $type)->delete();
        
        Log::info('Removed user AI prompt customization', [
            'user_id' => $userId,
            'type' => $type,
            'deleted_count' => $deleted
        ]);

        return $deleted > 0;
    }

    /**
     * Lấy statistics về usage của prompts
     */
    public function getPromptUsageStats(): array
    {
        $totalPrompts = AiPrompt::count();
        $activePrompts = AiPrompt::where('is_active', true)->count();
        $totalCustomizations = UserAiPrompt::where('is_active', true)->count();
        
        $typeStats = AiPrompt::selectRaw('type, COUNT(*) as count')
            ->where('is_active', true)
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        $usageStats = UserAiPrompt::selectRaw('ai_prompt_id, COUNT(*) as usage_count')
            ->where('is_active', true)
            ->groupBy('ai_prompt_id')
            ->with('aiPrompt:id,name,type')
            ->get()
            ->map(function ($item) {
                return [
                    'prompt_id' => $item->ai_prompt_id,
                    'prompt_name' => $item->aiPrompt->name,
                    'prompt_type' => $item->aiPrompt->type,
                    'usage_count' => $item->usage_count,
                ];
            })
            ->sortByDesc('usage_count')
            ->values()
            ->toArray();

        return [
            'total_prompts' => $totalPrompts,
            'active_prompts' => $activePrompts,
            'total_customizations' => $totalCustomizations,
            'type_distribution' => $typeStats,
            'most_used_prompts' => $usageStats,
        ];
    }

    /**
     * Validate prompt template
     */
    public function validatePromptTemplate(string $template): array
    {
        $errors = [];
        $warnings = [];

        // Check for unclosed variables
        if (preg_match_all('/\{\{([^}]+)\}\}/', $template, $matches)) {
            $variables = $matches[1];
            foreach ($variables as $variable) {
                if (empty(trim($variable))) {
                    $errors[] = "Empty variable found: {{}}";
                }
            }
        }

        // Check for unmatched braces
        $openBraces = substr_count($template, '{{');
        $closeBraces = substr_count($template, '}}');
        if ($openBraces !== $closeBraces) {
            $errors[] = "Unmatched braces: {$openBraces} opening, {$closeBraces} closing";
        }

        // Check template length
        if (strlen($template) < 50) {
            $warnings[] = "Template seems too short (< 50 characters)";
        }

        if (strlen($template) > 5000) {
            $warnings[] = "Template is very long (> 5000 characters), consider breaking it down";
        }

        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'variable_count' => count($matches[1] ?? []),
            'variables' => array_unique($matches[1] ?? []),
        ];
    }
}
