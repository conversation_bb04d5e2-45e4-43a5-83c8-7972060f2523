<?php

namespace App\Services;

use App\Models\BusinessIntelligenceReport;
use App\Services\ZillizVectorService;
use App\Services\OpenAIService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class BusinessIntelligenceService
{
    protected $zilliz;
    protected $openAI;

    public function __construct(ZillizVectorService $zilliz, OpenAIService $openAI)
    {
        $this->zilliz = $zilliz;
        $this->openAI = $openAI;
    }

    /**
     * Tạo báo cáo Business Intelligence từ vector data
     */
    public function generateReport(string $fromDate, string $toDate, ?int $userId = null): array
    {
        try {
            $userId = $userId ?? Auth::id();
            
            // 1. Tạo record báo cáo với status generating
            $report = $this->createReportRecord($fromDate, $toDate, $userId);
            
            // 2. <PERSON>hu thập dữ liệu từ vector database
            $businessData = $this->collectBusinessData($fromDate, $toDate);
            
            // 3. Tạo AI analysis
            $aiSummary = $this->generateAIAnalysis($businessData);
            
            // 4. Tạo PDF và upload S3
            $pdfResult = $this->generateAndUploadPDF($aiSummary, $fromDate, $toDate);
            
            // 5. Cập nhật báo cáo với kết quả
            $this->updateReportWithResults($report, $businessData, $aiSummary, $pdfResult);
            
            return [
                'success' => true,
                'report_id' => $report->id,
                'pdf_url' => $pdfResult['url'],
                'summary' => $aiSummary
            ];
            
        } catch (\Exception $e) {
            Log::error('Business Intelligence Report Error: ' . $e->getMessage());
            
            if (isset($report)) {
                $report->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage()
                ]);
            }
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Tạo record báo cáo ban đầu
     */
    private function createReportRecord(string $fromDate, string $toDate, int $userId): BusinessIntelligenceReport
    {
        return BusinessIntelligenceReport::create([
            'title' => "Báo cáo Business Intelligence {$fromDate} - {$toDate}",
            'period' => 'custom',
            'from_date' => $fromDate,
            'to_date' => $toDate,
            'status' => 'generating',
            'created_by' => $userId,
            'analysis_data' => [],
            'ai_summary' => '',
            'pdf_url' => '',
            'pdf_filename' => ''
        ]);
    }

    /**
     * Thu thập dữ liệu business từ vector database
     */
    private function collectBusinessData(string $fromDate, string $toDate): array
    {
        $businessData = [
            'products' => [],
            'orders' => [],
            'supplier_orders' => [],
            'stores' => [],
            'daily_reports' => []
        ];

        // 1. Analyze Products Performance
        $productQueries = [
            "High-performing products with good sales",
            "Premium priced products above 50 dollars",
            "Budget-friendly products under 20 dollars"
        ];

        foreach ($productQueries as $query) {
            $result = $this->zilliz->semanticSearch($query, 5, 'products_embeddings');
            if ($result['success'] && !empty($result['reports'])) {
                $businessData['products'][$query] = $result['reports'];
            }
        }

        // 2. Analyze Orders Performance
        $orderQueries = [
            "High-value completed orders above 100 dollars",
            "Recent successful transactions",
            "Orders with potential issues or cancellations"
        ];

        foreach ($orderQueries as $query) {
            $result = $this->zilliz->semanticSearch($query, 10, 'orders_embeddings');
            if ($result['success'] && !empty($result['reports'])) {
                $businessData['orders'][$query] = $result['reports'];
            }
        }

        // 3. Analyze Supply Chain
        $supplierQueries = [
            "Supplier fulfillment performance and delivery",
            "Supply chain issues and bottlenecks",
            "Successful supplier order completions"
        ];

        foreach ($supplierQueries as $query) {
            $result = $this->zilliz->semanticSearch($query, 5, 'supplier_orders_embeddings');
            if ($result['success'] && !empty($result['reports'])) {
                $businessData['supplier_orders'][$query] = $result['reports'];
            }
        }

        // 4. Analyze Store Performance
        $storeQueries = [
            "Active stores with good performance",
            "Store operational status and health"
        ];

        foreach ($storeQueries as $query) {
            $result = $this->zilliz->semanticSearch($query, 5, 'stores_embeddings');
            if ($result['success'] && !empty($result['reports'])) {
                $businessData['stores'][$query] = $result['reports'];
            }
        }

        // 5. Analyze Daily Reports
        $reportQueries = [
            "Seller performance and daily activities",
            "Business operations and seller status"
        ];

        foreach ($reportQueries as $query) {
            $result = $this->zilliz->semanticSearch($query, 5, 'daily_reports_embeddings');
            if ($result['success'] && !empty($result['reports'])) {
                $businessData['daily_reports'][$query] = $result['reports'];
            }
        }

        return $businessData;
    }

    /**
     * Tạo AI analysis từ business data
     */
    private function generateAIAnalysis(array $businessData): string
    {
        $businessContext = $this->generateBusinessContext($businessData);
        $prompt = $this->createBusinessAnalysisPrompt($businessContext);
        
        return $this->callChatGPT($prompt);
    }

    /**
     * Generate business context từ collected data
     */
    private function generateBusinessContext(array $data): string
    {
        $context = "BUSINESS DATA ANALYSIS FOR E-COMMERCE PLATFORM:\n\n";
        
        // Products analysis
        if (!empty($data['products'])) {
            $context .= "PRODUCT PORTFOLIO:\n";
            foreach ($data['products'] as $query => $products) {
                $context .= "- {$query}: " . count($products) . " products found\n";
                foreach (array_slice($products, 0, 3) as $product) {
                    $productData = $product['seller_data'];
                    $context .= "  * {$productData['name']} - \${$productData['price']} (Seller: {$productData['seller_id']})\n";
                }
            }
            $context .= "\n";
        }
        
        // Orders analysis
        if (!empty($data['orders'])) {
            $context .= "ORDER PERFORMANCE:\n";
            foreach ($data['orders'] as $query => $orders) {
                $context .= "- {$query}: " . count($orders) . " orders found\n";
                $totalValue = 0;
                foreach (array_slice($orders, 0, 5) as $order) {
                    $orderData = $order['seller_data'];
                    $totalValue += $orderData['total'];
                    $context .= "  * Order {$orderData['order_code']} - \${$orderData['total']} ({$orderData['status']})\n";
                }
                $avgValue = count($orders) > 0 ? $totalValue / count(array_slice($orders, 0, 5)) : 0;
                $context .= "  Average order value: \$" . number_format($avgValue, 2) . "\n";
            }
            $context .= "\n";
        }
        
        // Supply chain analysis
        if (!empty($data['supplier_orders'])) {
            $context .= "SUPPLY CHAIN STATUS:\n";
            foreach ($data['supplier_orders'] as $query => $orders) {
                $context .= "- {$query}: " . count($orders) . " supplier orders\n";
            }
            $context .= "\n";
        }
        
        // Store analysis
        if (!empty($data['stores'])) {
            $context .= "STORE NETWORK:\n";
            foreach ($data['stores'] as $query => $stores) {
                $context .= "- {$query}: " . count($stores) . " stores\n";
            }
            $context .= "\n";
        }
        
        return $context;
    }

    /**
     * Tạo prompt cho ChatGPT
     */
    private function createBusinessAnalysisPrompt(string $context): string
    {
        return "Hãy phân tích dữ liệu kinh doanh thương mại điện tử sau đây và cung cấp một báo cáo tổng hợp CỰC KỲ CHI TIẾT bằng tiếng Việt:

{$context}

Yêu cầu tạo báo cáo TOÀN DIỆN với ít nhất 8000-10000 từ, bao gồm:

## 1. TÓM TẮT ĐIỀU HÀNH (Executive Summary)
- Tổng quan tình hình kinh doanh (5-7 đoạn văn chi tiết)
- Các điểm nổi bật và thành tựu chính
- Thách thức và cơ hội quan trọng
- Khuyến nghị ưu tiên cao nhất

## 2. PHÂN TÍCH CHỈ SỐ HIỆU SUẤT CHÍNH (KPI Analysis)
### 2.1 Doanh thu và Tăng trưởng:
- Phân tích chi tiết xu hướng doanh thu
- So sánh với các kỳ trước (nếu có data)
- Dự báo xu hướng tăng trưởng
- Phân tích theo từng segment khách hàng

### 2.2 Hiệu suất Sản phẩm:
- Top sản phẩm bán chạy và lý do
- Phân tích pricing strategy
- Product mix optimization
- Lifecycle analysis của sản phẩm

### 2.3 Hiệu quả Đơn hàng:
- Order fulfillment rate và timeline
- Customer satisfaction metrics
- Return/refund analysis
- Shipping performance

## 3. INSIGHTS KINH DOANH SÂU SẮC (Deep Business Insights)
### 3.1 Phân tích Seller Performance:
- Ranking sellers theo multiple metrics
- Seller growth patterns
- Best practices từ top performers
- Support needs cho underperformers

### 3.2 Chuỗi Cung ứng:
- Supplier performance analysis
- Bottleneck identification
- Cost optimization opportunities
- Risk assessment trong supply chain

### 3.3 Thị trường và Cạnh tranh:
- Market positioning analysis
- Competitive advantages
- Market share opportunities
- Customer behavior insights

## 4. PHÂN TÍCH VẬN HÀNH CHI TIẾT (Operational Deep Dive)
### 4.1 Process Efficiency:
- Order-to-delivery timeline analysis
- Resource utilization metrics
- Automation opportunities
- Quality control measures

### 4.2 Technology và Infrastructure:
- System performance analysis
- Scalability assessment
- Integration opportunities
- Digital transformation roadmap

## 5. KHUYẾN NGHỊ CHIẾN LƯỢC CHI TIẾT (Strategic Recommendations)
### 5.1 Ngắn hạn (30 ngày):
- Immediate action items với timeline cụ thể
- Resource requirements
- Expected outcomes
- Success metrics

### 5.2 Trung hạn (3-6 tháng):
- Strategic initiatives
- Investment requirements
- Implementation roadmap
- Risk mitigation plans

### 5.3 Dài hạn (6-12 tháng):
- Vision và strategic goals
- Market expansion plans
- Technology investments
- Organizational development

## 6. ĐÁNH GIÁ RỦI RO TOÀN DIỆN (Comprehensive Risk Assessment)
### 6.1 Operational Risks:
- Supply chain disruptions
- Quality control issues
- Capacity constraints
- Technology failures

### 6.2 Market Risks:
- Competition threats
- Economic factors
- Regulatory changes
- Customer behavior shifts

### 6.3 Financial Risks:
- Cash flow management
- Pricing pressures
- Cost inflation
- Investment risks

## 7. KẾ HOẠCH HÀNH ĐỘNG CHI TIẾT (Detailed Action Plan)
- Priority matrix cho tất cả recommendations
- Timeline implementation
- Resource allocation
- Success metrics và KPIs
- Review và adjustment process

## 8. PHỤ LỤC VÀ DỮ LIỆU BỔ SUNG (Appendix)
- Detailed data tables
- Calculation methodologies
- Assumptions và limitations
- Glossary of terms

Hãy viết báo cáo như một consultant senior với phong cách chuyên nghiệp, sử dụng bullet points, số liệu cụ thể, và insights actionable. Mỗi section phải có ít nhất 3-5 đoạn văn chi tiết với analysis sâu sắc.";
    }

    /**
     * Call ChatGPT API
     */
    private function callChatGPT(string $prompt): string
    {
        try {
            $client = new \GuzzleHttp\Client();
            $apiKey = env('OPENAI_API_KEY');
            
            $response = $client->post('https://api.openai.com/v1/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => 'gpt-4o-mini',
                    'messages' => [
                        [
                            'role' => 'system',
                            'content' => 'Bạn là một chuyên gia phân tích kinh doanh chuyên về nền tảng thương mại điện tử với 15+ năm kinh nghiệm. Hãy phân tích dữ liệu kinh doanh được cung cấp và tạo một báo cáo tổng hợp CỰC KỲ CHI TIẾT bằng tiếng Việt. Báo cáo phải dài, toàn diện với insights sâu sắc, phân tích xu hướng chi tiết, và khuyến nghị cụ thể có thể thực hiện được. Hãy viết như một báo cáo chuyên nghiệp cho CEO/CTO với đầy đủ số liệu, biểu đồ mô tả, và kế hoạch hành động chi tiết.'
                        ],
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'max_tokens' => 10000,
                    'temperature' => 0.7
                ],
                'timeout' => 120
            ]);

            $data = json_decode($response->getBody(), true);
            
            if (isset($data['choices'][0]['message']['content'])) {
                return $data['choices'][0]['message']['content'];
            }
            
            throw new \Exception('Invalid ChatGPT response format');
            
        } catch (\Exception $e) {
            Log::error('ChatGPT API Error: ' . $e->getMessage());
            return $this->getFallbackAnalysis();
        }
    }

    /**
     * Fallback analysis khi ChatGPT lỗi
     */
    private function getFallbackAnalysis(): string
    {
        return "# BÁO CÁO BUSINESS INTELLIGENCE

## THÔNG BÁO HỆ THỐNG

Hiện tại hệ thống AI phân tích đang gặp sự cố tạm thời. Không thể tạo báo cáo phân tích chi tiết.

## KHUYẾN NGHỊ

1. **Kiểm tra thủ công**: Vui lòng xem xét dữ liệu báo cáo một cách thủ công
2. **Thử lại sau**: Hệ thống AI sẽ được khôi phục trong thời gian sớm nhất
3. **Liên hệ hỗ trợ**: Nếu cần hỗ trợ khẩn cấp, vui lòng liên hệ team IT

Xin lỗi vì sự bất tiện này.";
    }

    /**
     * Tạo PDF và upload lên S3
     */
    private function generateAndUploadPDF(string $aiSummary, string $fromDate, string $toDate): array
    {
        // Tạo HTML content
        $htmlContent = $this->convertAnalysisToHTML($aiSummary, $fromDate, $toDate);

        // Tạo PDF
        $pdf = Pdf::loadHTML($htmlContent)
            ->setPaper('A4', 'portrait')
            ->setOptions([
                'defaultFont' => 'DejaVu Sans',
                'isRemoteEnabled' => true,
                'isHtml5ParserEnabled' => true,
                'isFontSubsettingEnabled' => true,
                'defaultMediaType' => 'print',
                'isCssFloatEnabled' => true,
            ]);
        $pdfBinary = $pdf->output();

        // Upload lên S3
        $fileName = 'business-intelligence/report-' . $fromDate . '-to-' . $toDate . '-' . Carbon::now()->format('Y-m-d-H-i-s') . '.pdf';
        Storage::disk('s3')->put($fileName, $pdfBinary, 'public');
        $s3Url = Storage::disk('s3')->url($fileName);

        return [
            'url' => $s3Url,
            'filename' => $fileName
        ];
    }

    /**
     * Convert analysis text thành HTML để tạo PDF
     */
    private function convertAnalysisToHTML(string $analysisContent, string $fromDate, string $toDate): string
    {
        $date = Carbon::now()->format('d/m/Y H:i');

        // Chuyển đổi markdown-style formatting thành HTML
        $htmlContent = nl2br($analysisContent);
        $htmlContent = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $htmlContent);
        $htmlContent = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $htmlContent);
        $htmlContent = preg_replace('/^# (.*?)$/m', '<h1>$1</h1>', $htmlContent);
        $htmlContent = preg_replace('/^## (.*?)$/m', '<h2>$1</h2>', $htmlContent);
        $htmlContent = preg_replace('/^### (.*?)$/m', '<h3>$1</h3>', $htmlContent);

        return "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <meta http-equiv='Content-Type' content='text/html; charset=utf-8'/>
    <title>Báo cáo Business Intelligence</title>
    <style>
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            margin: 30px;
            color: #333;
            line-height: 1.6;
            font-size: 14px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2563eb;
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 12px;
        }
        .content {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #2563eb;
            font-size: 20px;
            font-weight: 600;
            margin-top: 30px;
        }
        h2 {
            color: #2563eb;
            border-left: 4px solid #2563eb;
            padding-left: 15px;
            margin-top: 30px;
            font-size: 18px;
            font-weight: 600;
        }
        h3 {
            color: #1e40af;
            margin-top: 25px;
            font-size: 16px;
            font-weight: 500;
        }
        .highlight {
            background: #f0f9ff;
            padding: 15px;
            border-left: 4px solid #3b82f6;
            margin: 15px 0;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 8px;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #666;
            font-size: 12px;
        }
        p {
            margin-bottom: 12px;
            text-align: justify;
        }
        strong {
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class='header'>
        <h1>Báo cáo Business Intelligence</h1>
        <p>Khoảng thời gian: {$fromDate} - {$toDate}</p>
        <p>Ngày tạo: {$date} | Được tạo bởi AI Analysis System</p>
    </div>
    <div class='content'>
        {$htmlContent}
    </div>
    <div class='footer'>
        <p>© " . date('Y') . " - Báo cáo được tạo tự động bởi hệ thống AI Business Intelligence</p>
    </div>
</body>
</html>";
    }

    /**
     * Cập nhật báo cáo với kết quả
     */
    private function updateReportWithResults(BusinessIntelligenceReport $report, array $businessData, string $aiSummary, array $pdfResult): void
    {
        // Tính toán statistics
        $stats = $this->calculateStatistics($businessData);

        $report->update([
            'analysis_data' => $businessData,
            'ai_summary' => $aiSummary,
            'pdf_url' => $pdfResult['url'],
            'pdf_filename' => $pdfResult['filename'],
            'total_products' => $stats['total_products'],
            'total_orders' => $stats['total_orders'],
            'total_suppliers' => $stats['total_suppliers'],
            'total_stores' => $stats['total_stores'],
            'total_reports' => $stats['total_reports'],
            'total_revenue' => $stats['total_revenue'],
            'avg_order_value' => $stats['avg_order_value'],
            'status' => 'completed'
        ]);
    }

    /**
     * Tính toán statistics từ business data
     */
    private function calculateStatistics(array $businessData): array
    {
        $stats = [
            'total_products' => 0,
            'total_orders' => 0,
            'total_suppliers' => 0,
            'total_stores' => 0,
            'total_reports' => 0,
            'total_revenue' => 0,
            'avg_order_value' => 0
        ];

        // Count products
        foreach ($businessData['products'] ?? [] as $products) {
            $stats['total_products'] += count($products);
        }

        // Count and calculate orders
        $orderTotals = [];
        foreach ($businessData['orders'] ?? [] as $orders) {
            $stats['total_orders'] += count($orders);
            foreach ($orders as $order) {
                $orderTotals[] = $order['seller_data']['total'] ?? 0;
            }
        }

        // Count suppliers
        foreach ($businessData['supplier_orders'] ?? [] as $suppliers) {
            $stats['total_suppliers'] += count($suppliers);
        }

        // Count stores
        foreach ($businessData['stores'] ?? [] as $stores) {
            $stats['total_stores'] += count($stores);
        }

        // Count reports
        foreach ($businessData['daily_reports'] ?? [] as $reports) {
            $stats['total_reports'] += count($reports);
        }

        // Calculate revenue and AOV
        if (!empty($orderTotals)) {
            $stats['total_revenue'] = array_sum($orderTotals);
            $stats['avg_order_value'] = $stats['total_revenue'] / count($orderTotals);
        }

        return $stats;
    }
}
