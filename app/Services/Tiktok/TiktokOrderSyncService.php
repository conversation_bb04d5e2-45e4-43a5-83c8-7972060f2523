<?php

namespace App\Services\Tiktok;

use App\Models\Store;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Design;
use EcomPHP\TiktokShop\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;

class TiktokOrderSyncService extends TiktokShopService
{
    public function syncTiktokOrders()
    {
        DB::beginTransaction();
        try {
            // Lấy danh sách đơn hàng từ TikTok Shop
            $params = [
                // 'start_time' => '2024-01-01T00:00:00Z', // Thay đổi thời gian bắt đầu phù hợp
                // 'end_time' => '2024-07-01T00:00:00Z', // Thay đổi thời gian kết thúc phù hợp

                // 'order_status' => 'CONFIRMED',
                'sort_order' => 'DESC',
                'page_size' => 50,
            ];
            $orders = $this->getOrderList($params);


            foreach ($orders as $orderData) {
                try {

                    $this->updateOrder($orderData, $this->store);
                } catch (\Exception $e) {
                    Log::error("Error syncing orders for store {$this->store->id}: " . $e->getMessage());
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();

            throw new \Exception('Error processing request: ' . $e->getMessage());
        }
    }

    /**
     * Sync một order cụ thể từ TikTok Shop
     *
     * @param string $orderNumber Order number từ TikTok Shop
     * @return Order
     * @throws \Exception
     */
    public function syncSpecificOrder($orderNumber)
    {
        DB::beginTransaction();
        try {
         

            // Lấy chi tiết order cụ thể từ TikTok Shop
            try {
                $orderDetail = $this->getOrderDetail($orderNumber);

            } catch (\Exception $apiException) {
               
                throw new \Exception("TikTok API error for order {$orderNumber}: " . $apiException->getMessage());
            }

            if (empty($orderDetail)) {
                // Thử tìm order trong danh sách orders gần đây
                $params = [
                    'sort_order' => 'DESC',
                    'page_size' => 100, // Tăng page size để tìm order
                ];

                try {
                    $orders = $this->getOrderList($params);
                    $foundOrder = null;

                    foreach ($orders as $order) {
                        if ($order['id'] == $orderNumber) {
                            $foundOrder = $order;
                            break;
                        }
                    }

                    if ($foundOrder) {
                       
                        $orderDetail = $foundOrder;
                    } else {
                       
                    }
                } catch (\Exception $listException) {
                  
                }

                if (empty($orderDetail)) {
                    throw new \Exception("Order not found on TikTok Shop: {$orderNumber}. Order may be too old, cancelled, or from a different store.");
                }
            }


            // Cập nhật order sử dụng method updateOrder hiện có
            $order = $this->updateOrder($orderDetail, $this->store);

            DB::commit();


            return $order;
        } catch (\Exception $e) {
            DB::rollback();

           
            throw new \Exception('Error syncing specific order: ' . $e->getMessage());
        }
    }

    public function updateOrder($orderData, $store)
    {
        $timeInUS = Carbon::createFromTimestamp($orderData['create_time'], 'America/New_York');
        $timeInVietnam = $timeInUS->timezone('Asia/Ho_Chi_Minh');
        $storeOrderStatus = $this->getOrderStatus($orderData['status']);

        $total = 0;
        $payment = $orderData['payment'];
        if (isset($payment['original_total_product_price'], $payment['seller_discount'], $payment['product_tax'])) {
            $total = $payment['original_total_product_price'] - $payment['seller_discount'] - $payment['product_tax'];
            // Round down to two decimal places
            $total = floor($total * 100) / 100;
        }
        $finalTotal = $total ?: $payment['total_amount'];
        $recipientAddress = $orderData['recipient_address'];
        $districtInfo = $recipientAddress['district_info'] ?? [];
        $order = Order::updateOrCreate(
            ['order_number' => $orderData['id'], 'store_id' => $store->id],
            [
                'created_at' => $timeInVietnam,
                'buyer_note' => $orderData['buyer_message'],
                'store_order_status' => $storeOrderStatus,
                'seller_id' => $store->owner_id,
                'fulfillment_type' => $orderData['shipping_type'],
                //'total' => floatval($orderData['payment']['total_amount']),
                'shipping_cost' => floatval($orderData['payment']['shipping_fee']),
                'shipping_first_name' => $orderData['recipient_address']['name'],
                'shipping_last_name' => '', // Không có dữ liệu
                'shipping_email' => '', // Không có dữ liệu
                'shipping_phone' => $orderData['recipient_address']['phone_number'],
                'shipping_country' => $this->getAddressLevel($districtInfo, 'L0'),
                'shipping_region' => $this->getAddressLevel($districtInfo, 'L1'),
                'shipping_address_line1' => $recipientAddress['address_line1'] ?? '',
                'shipping_city' => $this->getAddressLevel($districtInfo, 'L3'),
                'shipping_zip' => $recipientAddress['postal_code'] ?? '',
                'total' => $finalTotal,
            ]
        );

        if (
            $storeOrderStatus == 'Completed' || $storeOrderStatus == 'Delivered'
            || $storeOrderStatus == 'Awaiting Collection'
        ) {
            //|| $storeOrderStatus == 'Processing' 
            $order->status = "Completed";
        }
        if ($storeOrderStatus == 'Cancelled') {
            $order->status = "Cancelled";
        }
        $order->save();

        $groupedItems = [];
        foreach ($orderData['line_items'] as $lineItem) {
            $skuId = $lineItem['sku_id'];
            if (!isset($groupedItems[$skuId])) {
                $groupedItems[$skuId] = [
                    'item' => $lineItem,
                    'quantity' => 0,
                    'total' => 0
                ];
            }
            $groupedItems[$skuId]['quantity'] += 1;
            $groupedItems[$skuId]['total'] += floatval($lineItem['sale_price']);
        }
      
        foreach ($groupedItems as $skuId => $groupedItem) {

            $lineItem = $groupedItem['item'];
            $product = $this->ensureProduct($store->id, $lineItem, $store->owner_id);

            if (!$product->design) {
                if ($store->auto_add_design == true) {
                    $design = new Design([
                        'name' => ($product->name ?: 'Product') . ' Design',
                        'sku' => $skuId,
                        'mockup' => $product->image,
                        'mockup_front' => $product->image,
                        'seller_id'  => $store->owner_id,
                        'status' => 'Design',
                        'design_fee' => 0.00,
                    ]);
                    $product->design()->save($design);
                }
            }

            OrderItem::updateOrCreate(
                ['order_id' => $order->id, 'product_variant_id' => $skuId],
                [
                    'quantity' => $groupedItem['quantity'],
                    'total' => $groupedItem['total'],
                    'price' => floatval($lineItem['sale_price']),
                    'name' => $lineItem['product_name'],
                    'sku' => $lineItem['sku_name'],
                    'image' => $lineItem['sku_image'],
                    'link' => 'https://shop.tiktok.com/view/product/' . $lineItem['product_id'],
                    'product_id' => $product->id,
                    'fulfill_unit_id' => $lineItem['id'],
                ]
            );
        }

        return $order;
    }

    private function getOrderStatus($status)
    {
        $statusMapping = [
            'AWAITING_SHIPMENT' => 'Awaiting Shipment',
            'AWAITING_COLLECTION' => 'Awaiting Collection',
            'DELIVERED' => 'Delivered',
            'CANCELLED' => 'Cancelled',
            'COMPLETED' => 'Completed',
        ];

        return $statusMapping[$status] ?? 'Processing';
    }

    private function ensureProduct($storeId, $lineItem, $sellerId)
    {
        DB::beginTransaction();

        try {
            $product = Product::lockForUpdate()->where([
                'store_id' => $storeId,
                'tiktok_product_id' => $lineItem['product_id']
            ])->first();

            if (!$product) {
                $product = Product::lockForUpdate()->where([
                    'name' => $lineItem['product_name'],
                    'store_id' => $storeId
                ])->first();
            }

            // dd($product);

            if ($product) {
                // Cập nhật tiktok_product_id nếu chưa có
                if (empty($product->tiktok_product_id)) {
                    $product->tiktok_product_id = $lineItem['product_id'];
                }
                // Cập nhật các thông tin khác nếu cần
                $product->image = $lineItem['sku_image'];
                $product->price = floatval($lineItem['sale_price']);
                $product->sku = $lineItem['sku_id'];
                $product->link = 'https://shop.tiktok.com/view/product/' . $lineItem['sku_id'];
                $product->save();
            } else {
                // Kiểm tra xem sản phẩm đã được seller khác claim chưa
                $existingProduct = Product::where('tiktok_product_id', $lineItem['product_id'])->first();

                if ($existingProduct) {
                    // Log conflict và skip sản phẩm này
                    \Log::warning("Product exclusivity conflict - skipping", [
                        'tiktok_product_id' => $lineItem['product_id'],
                        'existing_store_id' => $existingProduct->store_id,
                        'existing_seller_id' => $existingProduct->seller_id,
                        'requesting_store_id' => $storeId,
                        'requesting_seller_id' => $sellerId,
                        'product_name' => $lineItem['product_name']
                    ]);

                    DB::commit();
                    return $existingProduct; // Trả về sản phẩm đã tồn tại để tránh lỗi
                }

                // Tạo sản phẩm mới nếu không có conflict
                try {
                    $product = Product::create([
                        'tiktok_product_id' => $lineItem['product_id'],
                        'name' => $lineItem['product_name'],
                        'store_id' => $storeId,
                        'seller_id' => $sellerId,
                        'description' => '',
                        'image' => $lineItem['sku_image'],
                        'price' => floatval($lineItem['sale_price']),
                        'active' => true,
                        'sku' => $lineItem['sku_id'],
                        'link' => 'https://shop.tiktok.com/view/product/' . $lineItem['sku_id']
                    ]);
                } catch (\Illuminate\Database\QueryException $e) {
                    // Bắt lỗi duplicate key cụ thể
                    if ($e->errorInfo[1] == 1062) { // MySQL duplicate entry error
                        \Log::warning("Duplicate product detected during creation", [
                            'tiktok_product_id' => $lineItem['product_id'],
                            'store_id' => $storeId,
                            'error' => $e->getMessage()
                        ]);

                        // Thử tìm lại sản phẩm vừa được tạo bởi request khác
                        $product = Product::where('tiktok_product_id', $lineItem['product_id'])->first();
                        if (!$product) {
                            throw $e; // Nếu vẫn không tìm thấy thì throw lỗi gốc
                        }
                    } else {
                        throw $e; // Throw lại các lỗi khác
                    }
                }
            }

            DB::commit();

            return $product;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    private function getAddressLevel($districtInfo, $level)
    {
        if (empty($districtInfo)) {
            return '';
        }
    
        foreach ($districtInfo as $info) {
            if ($info['address_level'] == $level) {
                return $info['address_name'];
            }
        }
        return '';
    }
}
