<?php

namespace App\Services\Tiktok;

use App\Models\TikTokProduct;
use App\Models\TikTokShop;
use App\Services\Tokapi;
use App\Services\OpenAIService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class PodShopService
{
    protected $tokapi;
    protected $aiService;

    public function __construct(Tokapi $tokapi, OpenAIService $aiService)
    {
        $this->tokapi = $tokapi;
        $this->aiService = $aiService;
    }

    public function findAndSavePodShops($keyword)
    {
        for ($page = 30; $page >= 1; $page--) {
            try {
                $response = $this->tokapi->searchProductsByKeyword($keyword, 'BEST_SELLERS', $page);
                $products = $response['e_com_items'] ?? [];

                foreach ($products as $product) {
                    try {
                        if (!isset($product['product_info']) || !isset($product['product_info']['seller_product_info'])) {
                            continue; // Bỏ qua nếu không có thông tin sản phẩm hoặc thông tin người bán
                        }

                        $sellerId = $product['product_info']['seller_product_info']['seller_id'];

                        // Kiểm tra nếu shop đã tồn tại
                        $existingShop = TikTokShop::where('seller_id', $sellerId)->first();
                        if ($existingShop) {
                            if (in_array($existingShop->review_status, ['approved', 'pending'])) {
                                $newSalesCount = $sellerDetails['data']['shop']['sold_count'] ?? 0;
                                $existingShop->updateSalesData($newSalesCount);
                            }
                            continue; // Nếu shop đã tồn tại, bỏ qua
                        }

                        // Lấy chi tiết shop
                        $sellerDetails = $this->tokapi->getSellerDetails($sellerId);

                        if (!empty($sellerDetails['data']['shop'])) {
                            $sellerProductsResponse = $this->tokapi->searchProductsBySellerId($sellerId);
                            $sellerProducts = $sellerProductsResponse['data']['product_view_map']['Store.Product_Page_Feeds.Double']['product_list'] ?? [];

                            // Sử dụng hàm extractImagesAndTitles
                            $products = $this->aiService->extractImagesAndTitles($sellerProducts);

                            $podCheckResult = $this->aiService->checkPODShop($products);

                            // Kiểm tra nếu kết quả không phải là 'YES' hoặc 'NO'
                            if (!in_array($podCheckResult['result'], ['YES', 'NO'])) {
                                continue;
                            }

                            if ($podCheckResult['result'] === 'NO') {
                                continue;
                            }

                            // Xác định niche
                            $niche = $this->aiService->determineNiche($products);

                            $shopRating = $sellerDetails['data']['shop']['shop_rating'] ?? null;
                            if ($shopRating === null || !is_numeric($shopRating)) {
                                $shopRating = 0; // Hoặc giá trị mặc định khác bạn muốn
                            }

                            $shopData = [
                                'seller_id' => $sellerDetails['data']['shop']['seller_id'],
                                'shop_name' => $sellerDetails['data']['shop']['shop_name'],
                                'logo_url' => $sellerDetails['data']['shop']['logo']['url_list'][0] ?? null,
                                'shop_rating' => (float) $shopRating,
                                'sold_count' => $sellerDetails['data']['shop']['sold_count'] ?? 0,
                                'on_sell_product_count' => $sellerDetails['data']['shop']['on_sell_product_count'] ?? 0,
                                'review_count' => $sellerDetails['data']['shop']['review_count'] ?? 0,
                                'is_on_holiday' => $sellerDetails['data']['shop']['is_on_holiday'] ?? false,
                                'display_on_sell_product_count' => $sellerDetails['data']['shop']['display_on_sell_product_count'] ?? null,
                                'biz_type' => $sellerDetails['data']['shop']['biz_type'] ?? 0,
                                'pod_type' => $podCheckResult['result'], // Sử dụng kết quả từ AI để xác định loại POD
                                'niche' => $niche, // Giả định có hàm này để xác định niche
                                //'requires_review' => true, // Đặt mặc định là true để kiểm duyệt lại sau
                            ];

                            TikTokShop::create($shopData);
                        }
                    } catch (\Exception $e) {
                        Log::error("Error processing product for seller ID {$sellerId}: " . $e->getMessage());
                    }
                }
            } catch (\Exception $e) {
                Log::error("Error fetching products for keyword {$keyword} on page {$page}: " . $e->getMessage());
            }
        }
    }

    public function fetchAndSaveProducts($sellerId)
    {
        $scrollParam = null;
        $loopCounter = 0;
        $maxLoops = 100;

        // Get the shop's display_on_sell_product_count
        $shop = TikTokShop::where('seller_id', $sellerId)->first();
        $totalProducts = 0;
        $maxProducts = $shop->display_on_sell_product_count;

        do {
            if ($loopCounter >= $maxLoops || $totalProducts >= $maxProducts) {
                break;
            }

            $response = $this->tokapi->searchProductsBySellerId($sellerId, $scrollParam);
            $products = $response['data']['product_view_map']['Store.Product_Page_Feeds.Double']['product_list'] ?? [];

            foreach ($products as $product) {
                $productId = $product['product_id'];

                // Check if the product is already imported
                $existingProduct = TikTokProduct::where('product_id', $productId)->first();
                if ($existingProduct) {
                    // Stop fetching more products if a duplicate is found
                    continue;
                }

                // Get detailed product info
                $productDetails = $this->tokapi->getProduct($productId);

                if (!empty($productDetails['data']['products'][0])) {
                    $productData = $productDetails['data']['products'][0];
                    $primaryImage = $productData['product_base']['images'][0]['url_list'][0] ?? null;

                    $printType = '';

                    TikTokProduct::create([
                        'shop_id' => $shop->id,
                        'product_id' => $productData['product_id'],
                        'title' => $productData['product_base']['title'],
                        'images' => json_encode(array_column($productData['product_base']['images'], 'url_list')), // Encode image URLs to JSON
                        'primary_image' => $primaryImage, // Set the first image as the primary image
                        'sold_count' => $productData['product_base']['sold_count'] ?? 0,
                        'original_price' => $productData['product_base']['price']['original_price'] ?? null,
                        'real_price' => $productData['product_base']['price']['real_price'] ?? null,
                        'discount' => $productData['product_base']['price']['discount'] ?? null,
                        'print_type' => $printType,
                        'product_type' => $this->determineProductType($productData['product_base']['title']),
                    ]);

                    $totalProducts++;
                }
            }

            // Update scroll param
            $scrollParam = $response['data']['next_scroll_param'] ?? null;
            $loopCounter++;
        } while (!empty($response['data']['has_more']));
    }


    protected function extractPrice($priceString)
    {
        // Extract numerical value from price string (e.g., "125.000₫")
        return floatval(preg_replace('/[^0-9.]/', '', $priceString));
    }



    protected function determineProductType($productTitle)
    {
        // Định nghĩa các từ khóa cho từng loại sản phẩm
        $productKeywordsMap = [
            'Hoodie' => ['hoodie', 'hoody', 'hooded'],
            'Sweatshirt' => ['sweatshirt', 'sweater', 'crewneck', 'jumper'],
            'Shirt' => ['shirt', 'tee', 't-shirt', 'top']
        ];

        // Chuyển tiêu đề sản phẩm sang chữ thường để so sánh không phân biệt hoa thường
        $lowerTitle = strtolower($productTitle);

        // Kiểm tra từ khóa của Hoodie trước
        foreach ($productKeywordsMap['Hoodie'] as $keyword) {
            if (strpos($lowerTitle, $keyword) !== false) {
                return 'Hoodie';
            }
        }

        // Kiểm tra từ khóa của Sweatshirt tiếp theo
        foreach ($productKeywordsMap['Sweatshirt'] as $keyword) {
            if (strpos($lowerTitle, $keyword) !== false) {
                return 'Sweatshirt';
            }
        }

        // Kiểm tra từ khóa của Shirt cuối cùng
        foreach ($productKeywordsMap['Shirt'] as $keyword) {
            if (strpos($lowerTitle, $keyword) !== false) {
                return 'Shirt';
            }
        }

        // Nếu không có từ khóa nào khớp, trả về 'Unknown'
        return 'Unknown';
    }


    public function importShop($shopId)
    {
        try {
            // Fetch shop details
            $sellerDetails = $this->tokapi->getSellerDetails($shopId);

            if (!empty($sellerDetails['data']['shop'])) {
                $shopData = $sellerDetails['data']['shop'];
                $shopRating = $shopData['shop_rating'] ?? null;

                if ($shopRating === null || !is_numeric($shopRating)) {
                    $shopRating = 0; // Default value if shop rating is not available or not numeric
                }

                // Prepare shop data for saving
                $shop = TikTokShop::updateOrCreate(
                    ['seller_id' => $shopId],
                    [
                        'shop_name' => $shopData['shop_name'],
                        'logo_url' => $shopData['logo']['url_list'][0] ?? null,
                        'shop_rating' => (float) $shopRating,
                        'sold_count' => $shopData['sold_count'] ?? 0,
                        'on_sell_product_count' => $shopData['on_sell_product_count'] ?? 0,
                        'review_count' => $shopData['review_count'] ?? 0,
                        'is_on_holiday' => $shopData['is_on_holiday'] ?? false,
                        'display_on_sell_product_count' => $shopData['display_on_sell_product_count'] ?? null,
                        'biz_type' => $shopData['biz_type'] ?? 0,
                        'pod_type' => $this->aiService->checkPODShop([])['result'], // Replace with actual POD check
                        'niche' => $this->aiService->determineNiche([]), // Replace with actual niche determination
                        'review_status' => 'pending', // Set initial review status
                       // 'last_scanned_at' => now(), // Set initial scan time
                    ]
                );

                Notification::make()
                    ->title('Shop imported successfully.')
                    ->success()
                    ->send();
            }
        } catch (\Exception $e) {
            Log::error("Error importing shop ID {$shopId}: " . $e->getMessage());

            Notification::make()
                ->title('Failed to import shop.')
                ->danger()
                ->send();
        }
    }
}
