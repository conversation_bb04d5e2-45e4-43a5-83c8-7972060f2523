<?php

namespace App\Services\Tiktok;

use App\Models\Order;
use App\Models\ProductToUpload;
use App\Models\Store;
use App\Models\Template;
use App\Models\TikTokPayment;
use App\Models\TikTokProduct;
use App\Services\MerchSpyService;
use App\Services\OpenAIService;
use App\Services\Tokapi;
use App\Enums\OrderStatus;
use EcomPHP\TiktokShop\Client;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class TiktokShopService
{
    protected $client;
    protected $store;

    public function __construct($store)
    {
        $this->store = $store;

        // Kiểm tra partnerApp có tồn tại không
        if (!$store->partnerApp) {
            throw new \Exception("Store does not have a partner app configured. Store ID: {$store->id}");
        }

        $proxyParts = null; // Khởi tạo biến $proxyParts
        if ($store->partnerApp && $store->partnerApp->proxy) {
            $proxyParts = $this->parseProxyString($store->partnerApp->proxy);
        }
        $options = [];
        if ($proxyParts) {
            $proxyType = stripos($proxyParts['protocol'], 'socks') !== false
                ? CURLPROXY_SOCKS5
                : CURLPROXY_HTTP;

            // $options = [
            //     'curl' => [
            //         CURLOPT_PROXY => $proxyParts['host'] . ':' . $proxyParts['port'],
            //         CURLOPT_PROXYTYPE => $proxyType,  // Tự động chọn loại proxy
            //         CURLOPT_PROXYAUTH => CURLAUTH_BASIC,
            //         CURLOPT_PROXYUSERPWD => $proxyParts['username'] . ':' . $proxyParts['password'],
            //         CURLOPT_HTTPPROXYTUNNEL => true,
            //         CURLOPT_SSL_VERIFYPEER => false,
            //         CURLOPT_SSL_VERIFYHOST => false,
            //         CURLOPT_TIMEOUT => 30,
            //         CURLOPT_CONNECTTIMEOUT => 10
            //     ]
            // ];
        }

        $this->client = new Client($store->partnerApp->app_key, $store->partnerApp->app_secret, $options);
        $this->client->setAccessToken($store->access_token);
        $this->client->setShopCipher($store->shop_cipher);
    }
    public function getClient()
    {
        return $this->client;
    }
    private function parseProxyString($input)
    {
        if (empty($input)) {
            return null;
        }

        // Format: socks5://*************:50101:rong2512:MZCnBnSnNL
        $parts = explode(':', $input);
        if (count($parts) === 5) {
            return [
                'protocol' => str_replace('/', '', $parts[0]),
                'host' => ltrim($parts[1], '/'),
                'port' => $parts[2],
                'username' => $parts[3],
                'password' => $parts[4]
            ];
        }

        return null;
    }
    public function formatProxyUrl($input)
    {
        if (empty($input)) {
            return null;
        }

        // Format: socks5://*************:50101:username:password
        $parts = explode(':', $input);
        if (count($parts) === 5) {
            $protocol = $parts[0]; // socks5
            $host = $parts[1];     // *************
            $port = $parts[2];     // 50101
            $username = $parts[3]; // rong2512
            $password = $parts[4]; // MZCnBnSnNL

            // Remove // from protocol
            $protocol = str_replace('/', '', $protocol);

            return sprintf(
                '%s://%s:%s@%s:%s',
                $protocol,
                $username,
                $password,
                ltrim($host, '/'), // remove any leading slashes
                $port
            );
        }

        return null;
    }

    public function getWarehouseList()
    {
        try {
            $response = $this->client->Logistic->getWarehouseList();
            return $response['warehouses'] ?? [];
        } catch (\Exception $e) {
            try {
                $auth = $this->client->auth();
                $dataNewToken = $auth->refreshNewToken($this->store->refresh_token);
                if ($dataNewToken['access_token']) {
                    $this->store->update([
                        'access_token' => $dataNewToken['access_token'],
                        'access_token_expire' => $dataNewToken['access_token_expire_in'],
                    ]);
                }
                $this->client->setAccessToken($dataNewToken['access_token']);
                $response = $this->client->Logistic->getWarehouseList();
                return $response['warehouses'] ?? [];
            } catch (\Exception $e) {
                throw new \Exception('Unable to fetch warehouse list after token refresh: ' . $e->getMessage());
            }
        }
    }

    public function uploadImage($base64Image, $useCase)
    {
        try {
            $response = $this->client->Product->uploadProductImage($base64Image, $useCase);
            return $response['uri'];
        } catch (\Exception $e) {
            throw new \Exception('Image upload failed: ' . $e->getMessage());
        }
    }

    public function getPayment($pageToken = null)
    {
        try {
            $params = [
                'page_size' => 100,
                'sort_field' => 'create_time',
                'sort_order' => 'DESC'
            ];

            // Thêm page_token nếu có
            if ($pageToken) {
                $params['page_token'] = $pageToken;
            }

            $response = $this->client->Finance->getPayments($params);
            return $response;
        } catch (\Exception $e) {
            try {
                $auth = $this->client->auth();
                $dataNewToken = $auth->refreshNewToken($this->store->refresh_token);
                if ($dataNewToken['access_token']) {
                    $this->store->update([
                        'access_token' => $dataNewToken['access_token'],
                        'access_token_expire' => $dataNewToken['access_token_expire_in'],
                    ]);
                }
                $this->client->setAccessToken($dataNewToken['access_token']);
                $response = $this->client->Finance->getPayments($params);
                return $response;
            } catch (\Exception $e) {
                throw new \Exception('get Payments failed: ' . $e->getMessage());
            }
        }
    }
    public function createProduct($data)
    {
        try {


            $response = $this->client->Product->createProduct($data);
            return $response;
        } catch (\Exception $e) {
            throw new \Exception('Unable to create product: ' . $e->getMessage());
        }
    }

    public function createProductFromTask(ProductToUpload $product)
    {
        $template = Template::find($product->task->template_id);

        $AI = new OpenAIService();
        $title = $product->title;

        if (!$template) {
            throw new \Exception('Template not found for product ID: ' . $product->id);
        }

        $uploadedImages = [];
        if (!empty($product->product_id)) {
            // Lấy thông tin sản phẩm từ database
            $dbProduct = TikTokProduct::where('product_id', $product->product_id)->first();
            if (!$dbProduct) {
                throw new \Exception('Product not found in database for product ID: ' . $product->product_id);
            }

            $spy_product = json_decode($dbProduct->toJson(), true);
            $spy_img = [$dbProduct->primary_image];
        } else {
            // Lấy thông tin sản phẩm từ API
            $spy_id = $this->getIdFromUrl($product->product_link);
            $merchSpyService = new MerchSpyService();

            $productLink = $merchSpyService->getProductID($spy_id);
            $tiktok_id = $merchSpyService->getIdFromUrl($productLink);

            $spy_product = Cache::remember("tiktok_product_{$tiktok_id}", 60, function () use ($tiktok_id) {
                $api = new Tokapi();
                return $api->getProduct($tiktok_id)['data']['products'][0];
            });
            $spy_img = array_column($spy_product['product_base']['images'], 'url_list')[0];
        }

        foreach ($spy_img as $image) {
            $uploadedImages[] = ['uri' => $this->uploadImage($image, 'MAIN_IMAGE')];
        }

        $sizeChartImageUri = null;
        if (!empty($template->size_chart)) {
            $sizeChartImageUri = $this->uploadImage($template->size_chart, 'SIZE_CHART_IMAGE');
        }

        // Định dạng SKU từ template, bỏ qua các SKU có quantity = 0
        $formattedSkus = array_values(array_filter(array_map(function ($sku) use ($product) {
            return [
                'sku_code' => $sku['sku_code'],
                'seller_sku' => $sku['seller_sku'],
                'price' => [
                    'amount' => strval($sku['price']),
                    'currency' => 'USD',
                ],
                'inventory' => [
                    [
                        'warehouse_id' => strval($product->task->warehouse_id), // Thay thế bằng ID kho hàng thực tế
                        'quantity' => (int)$sku['stock'], // Ensure quantity is an integer
                    ],
                ],
                'sales_attributes' => array_map(function ($attribute) {
                    return [
                        'name' => $attribute['name'],
                        'value_name' => $attribute['value_name']
                    ];
                }, $sku['sales_attributes']),
            ];
        }, $template->skus), function ($sku) {
            return $sku['inventory'][0]['quantity'] > 0;
        }));

        $replacements = [
            'title' => $title,

            'shop_name' => $product->task->store->tiktok_shop_name ?? "My Store",
        ];
        $productDescription = $this->replacePlaceholders($template->description, $replacements);

        // Định dạng dữ liệu sản phẩm để gửi tới TikTok
        $productData = [
            'title' => $title,
            'description' => $productDescription,
            'main_images' => $uploadedImages,
            'skus' => $formattedSkus,
            'package_dimensions' => [
                'height' => '2',
                'length' => '30',
                'unit' => 'CENTIMETER',
                'width' => '25'
            ],
            'package_weight' => [
                'unit' => 'KILOGRAM',
                'value' => '0.2'
            ],
            'is_cod_open' => false,
            'category_id' => (string)$template->category_id,
            'save_mode' => 'LISTING', // Thay thế bằng chế độ lưu thực tế LISTING , AS_DRAFT
            'size_chart' => isset($sizeChartImageUri) ? ['image' => ['uri' => $sizeChartImageUri]] : null,
        ];

        // Gửi yêu cầu tạo sản phẩm tới TikTok
        $new_product =  $this->createProduct($productData);
        try {
            $product->public_product_id = $new_product['product_id'];
            $product->save();
        } catch (Exception $e) {
        }
    }

    function replacePlaceholders($template, $replacements)
    {
        foreach ($replacements as $key => $value) {
            $template = str_replace('{{' . $key . '}}', $value, $template);
        }
        return $template;
    }

    function getIdFromUrl($url)
    {
        return (strpos($url, 'kmediaz') !== false && preg_match('/id=(\d+)/', $url, $matches)) ? $matches[1] : null;
    }

    function processProductImages($images)
    {
        $imageUrls = [];

        // Lấy link ảnh đầu tiên và thêm vào mảng
        if (!empty($images)) {
            $imageUrls[] = str_replace(':800:800', ':1000:1000', $images[0]);

            // Nếu có nhiều hơn 4 ảnh, loại bỏ ảnh cuối cùng
            if (count($images) > 4) {
                array_pop($images);
            }

            // Lấy các link ảnh tiếp theo và thay thế :800:800 bằng :1000:1000
            $remainingImages = [];
            for ($i = 1; $i < count($images); $i++) {
                $remainingImages[] = str_replace(':800:800', ':1000:1000', $images[$i]);
            }

            // Trộn ngẫu nhiên các link ảnh còn lại
            shuffle($remainingImages);

            // Gộp ảnh đầu tiên với các ảnh còn lại đã trộn ngẫu nhiên
            $imageUrls = array_merge($imageUrls, $remainingImages);
        }

        return $imageUrls;
    }

    // theêm phương thức này để lấy danh sách settlements
    public function getOrderStatementTransactions($order_id)
    {
        try {
            $response = $this->client->Finance->getOrderStatementTransactions($order_id);
            return $response ?? [];
        } catch (\Exception $e) {
            $auth = $this->client->auth();
            $dataNewToken = $auth->refreshNewToken($this->store->refresh_token);
            if ($dataNewToken['access_token']) {
                $this->store->update([
                    'access_token' => $dataNewToken['access_token'],
                    'access_token_expire' => $dataNewToken['access_token_expire_in'],
                ]);
            }
            $this->client->setAccessToken($dataNewToken['access_token']);
            $response = $this->client->Finance->getOrderStatementTransactions($order_id);
            return $response  ?? [];
        }
    }
    // Thêm phương thức này để lấy danh sách order
    public function getOrderList($params = [])
    {
        try {
            $response = $this->client->Order->getOrderList($params);
            return $response['orders'] ?? [];
        } catch (\Exception $e) {
            $auth = $this->client->auth();
            $dataNewToken = $auth->refreshNewToken($this->store->refresh_token);
            if ($dataNewToken['access_token']) {
                $this->store->update([
                    'access_token' => $dataNewToken['access_token'],
                    'access_token_expire' => $dataNewToken['access_token_expire_in'],
                ]);
            }
            $this->client->setAccessToken($dataNewToken['access_token']);
            $response = $this->client->Order->getOrderList($params);
            return $response['orders'] ?? [];
        }
    }

    public function getOrderDetail($orderId)
    {
        try {
            // Gọi API để lấy thông tin chi tiết của order
            $response = $this->client->Order->getOrderDetail(['order_id' => $orderId]);
            return $response['order'] ?? [];
        } catch (\Exception $e) {
            // Nếu có lỗi xảy ra, làm mới token và thử lại
            $auth = $this->client->auth();
            $dataNewToken = $auth->refreshNewToken($this->store->refresh_token);
            if ($dataNewToken['access_token']) {
                $this->store->update([
                    'access_token' => $dataNewToken['access_token'],
                    'access_token_expire' => $dataNewToken['access_token_expire_in'],
                ]);
            }
            $this->client->setAccessToken($dataNewToken['access_token']);

            // Thử lại yêu cầu lấy thông tin chi tiết của order
            $response = $this->client->Order->getOrderDetail(['order_id' => $orderId]);
            return $response['order'] ?? [];
        }
    }

    public function getFlashSales()
    {
        try {
            $response = $this->client->Promotion->searchActivities([
                'page' => 1, // Specify the page number
                'page_size' => 50, // Specify the number of results per page
            ]);

            Log::channel('tiktok_api')->info('Flash sales retrieved successfully', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'flash_sales_count' => isset($response['data']['flash_sales']) ? count($response['data']['flash_sales']) : 0
            ]);

            return $response['data']['flash_sales'] ?? [];
        } catch (\Exception $e) {
            Log::channel('tiktok_api')->error('Error fetching flash sales', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Unable to fetch flash sales: ' . $e->getMessage());
        }
    }

    public function getProductList($pageToken = null, $status = 'ALL', $pageSize = 10)
    {
        try {
            $params = [
                'page_size' => $pageSize,
            ];

            $body = [
                'status' => $status,
            ];

            if ($pageToken) {
                $params['page_token'] = $pageToken;
            }

            Log::channel('tiktok_api')->info('TikTok API Request: Product/searchProducts', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'params' => $params,
                'body' => $body
            ]);

            $response = $this->client->Product->searchProducts($params, $body);

            Log::channel('tiktok_api')->info('TikTok API Response: Product/searchProducts', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'products_count' => isset($response['products']) ? count($response['products']) : 0,
                'has_next_page' => isset($response['next_page_token']) && !empty($response['next_page_token'])
            ]);

            return $response ?? [];
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'access token is expired') !== false) {
                // Token đã hết hạn, thử làm mới token
                $this->refreshAccessToken();

                // Thử lại yêu cầu API với token mới
                try {
                    $response = $this->client->Product->searchProducts($params, $body);
                    Log::channel('tiktok_products')->info('Product list retrieved after token refresh', [
                        'store_id' => $this->store->id,
                        'store_name' => $this->store->name ?? 'Unknown',
                        'products_count' => isset($response['products']) ? count($response['products']) : 0
                    ]);
                    return $response ?? [];
                } catch (\Exception $retryException) {
                    Log::channel('tiktok_products')->error('Unable to get product list after token refresh', [
                        'store_id' => $this->store->id,
                        'store_name' => $this->store->name ?? 'Unknown',
                        'error' => $retryException->getMessage()
                    ]);
                    throw new \Exception('Unable to get product list after token refresh: ' . $retryException->getMessage());
                }
            }

            Log::channel('tiktok_api')->error('TikTok API Error: Product/searchProducts', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Unable to get product list: ' . $e->getMessage());
        }
    }

    protected function refreshAccessToken()
    {
        try {
            $auth = $this->client->auth();
            $dataNewToken = $auth->refreshNewToken($this->store->refresh_token);

            if (isset($dataNewToken['access_token'])) {
                $this->store->update([
                    'access_token' => $dataNewToken['access_token'],
                    'access_token_expire' => $dataNewToken['access_token_expire_in'],
                ]);

                $this->client->setAccessToken($dataNewToken['access_token']);
                Log::channel('tiktok_auth')->info("Access token refreshed successfully", [
                    'store_id' => $this->store->id,
                    'store_name' => $this->store->name ?? 'Unknown'
                ]);
            } else {
                Log::channel('tiktok_auth')->error("Failed to refresh access token", [
                    'store_id' => $this->store->id,
                    'store_name' => $this->store->name ?? 'Unknown',
                    'response' => $dataNewToken
                ]);
            }
        } catch (\Exception $e) {
            Log::channel('tiktok_auth')->error("Error refreshing access token", [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    public function getProduct($productId)
    {
        try {
            $response = $this->client->Product->getProduct($productId);

            return $response ?? [];
        } catch (\Exception $e) {
            throw new \Exception('Unable to get product: ' . $e->getMessage());
        }
    }
    public function getProductVariants($productId)
    {
        try {
            $productDetails = $this->getProduct($productId);
            return $productDetails['skus'] ?? [];
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'access token is expired') !== false) {
                // Token đã hết hạn, thử làm mới token
                $this->refreshAccessToken();

                // Thử lại yêu cầu API với token mới
                try {
                    $productDetails = $this->getProduct($productId);
                    return $productDetails['skus'] ?? [];
                } catch (\Exception $retryException) {
                    throw new \Exception("Unable to fetch variants: " . $e->getMessage());
                }
            }
            throw new \Exception("Unable to fetch variants: " . $e->getMessage());
        }
    }
    public function updateProductSku($productId, $skus, $warehouse_id, $productDescription, $saveMode)
    {

        $formattedSkus = array_values(array_filter(array_map(function ($sku) use ($warehouse_id) {
            return [
                'sku_code' => $sku['sku_code'],
                'seller_sku' => $sku['seller_sku'],
                'price' => [
                    'amount' => strval($sku['price']),
                    'currency' => 'USD',
                ],
                'inventory' => [
                    [
                        'warehouse_id' => strval($warehouse_id), // Thay thế bằng ID kho hàng thực tế
                        'quantity' => 300, // Ensure quantity is an integer
                    ],
                ],
                'sales_attributes' => array_map(function ($attribute) {
                    return [
                        'name' => $attribute['name'],
                        'value_name' => $attribute['value_name']
                    ];
                }, $sku['sales_attributes']),
            ];
        }, $skus), function ($sku) {
            return $sku['inventory'][0]['quantity'] > 0;
        }));
        try {

            $data = $this->getProduct($productId);

            $data['category_id'] = (string)$data['category_chains'][2]['id'];
            $data['skus'] = $formattedSkus;
            $data['main_images'] = $this->getMainImageUris($data);
            $data['save_mode'] = $saveMode;

            $response = $this->client->Product->editProduct($productId, $data);
            $data['skus'] = $formattedSkus;
            $replacements = [
                'title' => $data['title'],
                'shop_intro' => '',
                'shop_name' => $product->task->store->tiktok_shop_name ?? "My Store",
            ];
            $productDescription = $this->replacePlaceholders($productDescription, $replacements);
            $data['description'] = $productDescription;
            $data['package_dimensions'] = [
                'height' => '8',
                'length' => '1',
                'unit' => 'INCH',
                'width' => '10'
            ];
            $data['package_weight'] = [
                'unit' => 'POUND',
                'value' => '0.3'
            ];


            return $response ?? [];
        } catch (\Exception $e) {

            throw new \Exception('Update product sku failed: ' . $e->getMessage());
        }
    }
    public function getMainImageUris($product)
    {
        if (isset($product['main_images']) && is_array($product['main_images'])) {
            return array_map(function ($image) {
                return ['uri' => $image['uri']];
            }, $product['main_images']);
        }

        return [];
    }

    public function createAndGetLabelUrl($orderId)
    {
        try {
            $requestData = [
                'order_id' => $orderId,
                'document_type' => 'SHIPPING_LABEL',
            ];

            Log::channel('tiktok_labels')->info('Attempting to create label URL', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'order_id' => $orderId,
                'request_data' => $requestData
            ]);

            $response = $this->client->Fulfillment->createPackages($requestData);

            if (isset($response['shipping_document_url'])) {
                Log::channel('tiktok_labels')->info('Label URL created successfully', [
                    'store_id' => $this->store->id,
                    'store_name' => $this->store->name ?? 'Unknown',
                    'order_id' => $orderId,
                    'url' => $response['shipping_document_url']
                ]);
                return $response['shipping_document_url'];
            } else {
                Log::channel('tiktok_labels')->warning('No shipping document URL in response', [
                    'store_id' => $this->store->id,
                    'store_name' => $this->store->name ?? 'Unknown',
                    'order_id' => $orderId,
                    'response' => $response
                ]);
                return null;
            }
        } catch (\Exception $e) {
            Log::channel('tiktok_labels')->error('Exception while creating label URL', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }


    /**
     * Kiểm tra xem order có thể tạo label không
     */
    private function canGenerateLabel($orderDetail)
    {
        // Kiểm tra cấu trúc dữ liệu
        if (!isset($orderDetail['orders']) || !is_array($orderDetail['orders']) || empty($orderDetail['orders'])) {
            return [
                'can_generate' => false,
                'reason' => "Invalid order data structure. No orders found.",
                'suggestion' => "Please check the order data or contact TikTok support."
            ];
        }

        if (!isset($orderDetail['orders'][0]) || !is_array($orderDetail['orders'][0])) {
            return [
                'can_generate' => false,
                'reason' => "Invalid order data. First order is missing or invalid.",
                'suggestion' => "Please check the order data or contact TikTok support."
            ];
        }

        $shippingType = $orderDetail['orders'][0]['shipping_type'] ?? '';
        $orderStatus = $orderDetail['orders'][0]['order_status'] ?? '';

        // Chỉ có thể tạo label cho đơn hàng TikTok ship
        if ($shippingType !== 'TIKTOK') {
            return [
                'can_generate' => false,
                'reason' => "Shipping type is '{$shippingType}'. Labels can only be generated for TikTok-shipped orders.",
                'suggestion' => "Change shipping method to TikTok shipping or contact TikTok support."
            ];
        }

        // Kiểm tra trạng thái đơn hàng - sử dụng OrderStatus enum
        $validStatuses = [
            OrderStatus::AwaitingShipment->value,
            OrderStatus::Completed->value
        ];

        // if (!in_array($orderStatus, $validStatuses)) {
        //     return [
        //         'can_generate' => false,
        //         'reason' => "Order status is '{$orderStatus}'. Labels can only be generated for orders that are ready for shipping or completed.",
        //         'suggestion' => "Wait for order to reach 'Awaiting Shipment' status or contact TikTok support."
        //     ];
        // }

        return ['can_generate' => true];
    }

    public function syncLabelTracking(Order $order)
    {
        Log::channel('tiktok_labels')->info('Starting label sync process', [
            'store_id' => $this->store->id,
            'store_name' => $this->store->name ?? 'Unknown',
            'order_code' => $order->order_code,
            'order_number' => $order->order_number
        ]);

        $filePath = "label/" . $order->order_code . ".pdf";
        $checkExists = Storage::disk('s3')->exists($filePath);
        if ($checkExists) {
            $labelUrl = Storage::disk('s3')->url($filePath);

            // Đảm bảo cập nhật database ngay cả khi file đã tồn tại
            if (empty($order->label) || $order->label !== $labelUrl) {
                $order->label = $labelUrl;
                $order->save();
                Log::channel('tiktok_labels')->info('Updated database with existing label URL', [
                    'store_id' => $this->store->id,
                    'store_name' => $this->store->name ?? 'Unknown',
                    'order_code' => $order->order_code,
                    'label_url' => $labelUrl
                ]);
            }

            Log::channel('tiktok_labels')->info('Label file already exists', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'order_code' => $order->order_code,
                'label_url' => $labelUrl
            ]);
            return $labelUrl;
        }
        try {
            $response = $this->client->Logistic->getWarehouseList();
        } catch (\Exception $e) {
            Log::channel('tiktok_labels')->warning('Token refresh needed for label sync', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'order_code' => $order->order_code,
                'error' => $e->getMessage()
            ]);
            try {
                $auth = $this->client->auth();
                $dataNewToken = $auth->refreshNewToken($this->store->refresh_token);
                if ($dataNewToken['access_token']) {
                    $this->store->update([
                        'access_token' => $dataNewToken['access_token'],
                        'access_token_expire' => $dataNewToken['access_token_expire_in'],
                    ]);
                }
                $this->client->setAccessToken($dataNewToken['access_token']);
                Log::channel('tiktok_labels')->info('Token refreshed successfully for label sync', [
                    'store_id' => $this->store->id,
                    'store_name' => $this->store->name ?? 'Unknown',
                    'order_code' => $order->order_code
                ]);
            } catch (\Exception $e) {
                Log::channel('tiktok_labels')->error('Failed to refresh token for label sync', [
                    'store_id' => $this->store->id,
                    'store_name' => $this->store->name ?? 'Unknown',
                    'order_code' => $order->order_code,
                    'error' => $e->getMessage()
                ]);
                throw new \Exception('syncLabelTracking: ' . $e->getMessage());
            }
        }
        $orderTiktokDetail = $this->client->Order->getOrderDetail($order->order_number);

        // Log chi tiết order detail để debug
        Log::channel('tiktok_labels')->info('Order detail retrieved', [
            'store_id' => $this->store->id,
            'store_name' => $this->store->name ?? 'Unknown',
            'order_code' => $order->order_code,
            'order_status' => $orderTiktokDetail['orders'][0]['order_status'] ?? 'unknown',
            'shipping_type' => $orderTiktokDetail['orders'][0]['shipping_type'] ?? 'unknown',
            'has_packages' => !empty($orderTiktokDetail['orders'][0]['packages']),
            'packages_count' => isset($orderTiktokDetail['orders'][0]['packages']) ? count($orderTiktokDetail['orders'][0]['packages']) : 0
        ]);

        // Kiểm tra điều kiện tạo label
        $canGenerate = $this->canGenerateLabel($orderTiktokDetail);
        if (!$canGenerate['can_generate']) {
            Log::channel('tiktok_labels')->error('Cannot generate label', [
                'store_id' => $this->store->id,
                'order_code' => $order->order_code,
                'reason' => $canGenerate['reason'],
                'suggestion' => $canGenerate['suggestion']
            ]);
            throw new \Exception($canGenerate['reason'] . ' ' . $canGenerate['suggestion']);
        }

        $packagesId = '';

        if (!empty($orderTiktokDetail['orders'][0]['packages'])) {
            $packagesId = $orderTiktokDetail['orders'][0]['packages'][0]['id'];
            Log::channel('tiktok_labels')->info('Package ID found in order', [
                'store_id' => $this->store->id,
                'order_code' => $order->order_code,
                'package_id' => $packagesId
            ]);
        } else {
            $shippingType = $orderTiktokDetail['orders'][0]['shipping_type'] ?? '';
            $orderStatus = $orderTiktokDetail['orders'][0]['order_status'] ?? '';

            Log::channel('tiktok_labels')->warning('No packages found in order', [
                'store_id' => $this->store->id,
                'order_code' => $order->order_code,
                'shipping_type' => $shippingType,
                'order_status' => $orderStatus
            ]);

            if ($shippingType == 'TIKTOK') {
                try {
                    Log::channel('tiktok_labels')->info('Attempting to create package', [
                        'store_id' => $this->store->id,
                        'order_code' => $order->order_code,
                        'order_number' => $order->order_number
                    ]);

                    $createLabel = $this->client->Fulfillment->createPackages($order->order_number);
                    $packagesId = $createLabel['package_id'] ?? '';

                    if ($packagesId) {
                        Log::channel('tiktok_labels')->info('Package created successfully', [
                            'store_id' => $this->store->id,
                            'order_code' => $order->order_code,
                            'package_id' => $packagesId
                        ]);
                    } else {
                        Log::channel('tiktok_labels')->error('Package creation failed - no package_id returned', [
                            'store_id' => $this->store->id,
                            'order_code' => $order->order_code,
                            'create_response' => $createLabel
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::channel('tiktok_labels')->error('Package creation failed with exception', [
                        'store_id' => $this->store->id,
                        'order_code' => $order->order_code,
                        'error' => $e->getMessage()
                    ]);
                }
            } else {
                Log::channel('tiktok_labels')->warning('Cannot create package - shipping type is not TIKTOK', [
                    'store_id' => $this->store->id,
                    'order_code' => $order->order_code,
                    'shipping_type' => $shippingType,
                    'message' => 'Label can only be generated for TikTok-shipped orders'
                ]);
            }
        }

        if (!empty($packagesId)) {
            Log::channel('tiktok_labels')->info('Getting shipping document', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'order_code' => $order->order_code,
                'package_id' => $packagesId
            ]);

            $getLabel = $this->client->Fulfillment->getPackageShippingDocument($packagesId, 'SHIPPING_LABEL', 'A6');

            //put file upload to backblade cloud
            if (!$checkExists) {
                Storage::disk('s3')->put($filePath, file_get_contents($getLabel['doc_url']), 'public');
                $labelUrl = Storage::disk('s3')->url($filePath);
                $order->label = $labelUrl;
                $order->save();

                Log::channel('tiktok_labels')->info('New label created and saved', [
                    'store_id' => $this->store->id,
                    'store_name' => $this->store->name ?? 'Unknown',
                    'order_code' => $order->order_code,
                    'label_url' => $labelUrl,
                    'file_path' => $filePath
                ]);

                return $labelUrl;
            }
        } else {
            Log::channel('tiktok_labels')->error('No package ID available for label generation', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'order_code' => $order->order_code,
                'order_status' => $orderTiktokDetail['orders'][0]['order_status'] ?? 'unknown',
                'shipping_type' => $orderTiktokDetail['orders'][0]['shipping_type'] ?? 'unknown'
            ]);

            // Throw exception với thông báo rõ ràng
            $shippingType = $orderTiktokDetail['orders'][0]['shipping_type'] ?? 'unknown';
            $orderStatus = $orderTiktokDetail['orders'][0]['order_status'] ?? 'unknown';

            if ($shippingType !== 'TIKTOK') {
                throw new \Exception("Cannot generate label: Order shipping type is '{$shippingType}'. Labels can only be generated for TikTok-shipped orders (shipping_type = 'TIKTOK').");
            } else {
                throw new \Exception("Package not found. Order status: '{$orderStatus}'. The order may not be ready for shipping or may not have been processed by TikTok yet. Please ensure the order is in 'Awaiting Shipment' status.");
            }
        }

        // Refresh order để lấy giá trị label mới nhất từ database
        $order->refresh();
        Log::channel('tiktok_labels')->info('Label sync completed', [
            'store_id' => $this->store->id,
            'store_name' => $this->store->name ?? 'Unknown',
            'order_code' => $order->order_code,
            'final_label' => $order->label
        ]);
        return $order->label;
    }

    /**
     * Lấy số tiền đang hold tại shop TikTok
     *
     * @param array $options Các tùy chọn bổ sung
     * @return array Thông tin về số tiền đang hold
     */
    public function getHoldAmount(array $options = [])
    {
        try {
            // Sử dụng API statements để lấy thông tin chi tiết
            $currentTimestamp = time();
            $daysToLookBack = $options['days_to_look_back'] ?? 60; // Mặc định 60 ngày (2 tháng)
            $startTimestamp = $currentTimestamp - ($daysToLookBack * 24 * 60 * 60);
            $pageSize = $options['page_size'] ?? 20;
            $maxPages = $options['max_pages'] ?? 20;
            $apiVersion = $options['api_version'] ?? '202309';
            $scanBatchId = uniqid('scan_', true);

            // Tính thời gian bắt đầu và kết thúc dưới dạng chuỗi ngày
            $startDate = date('Y-m-d', $startTimestamp);
            $endDate = date('Y-m-d', $currentTimestamp);

            $params = [
                'page_size' => $pageSize,
                'sort_field' => 'statement_time',
                'sort_order' => 'DESC',
                'statement_time_ge' => $startTimestamp, // Từ X ngày trước
                'statement_time_lt' => $currentTimestamp, // Đến hiện tại
                // Có thể thêm lọc theo payment_status nếu cần
                // 'payment_status' => $options['payment_status'] ?? null,
            ];


            // Gọi API để lấy statements với phiên bản API cụ thể
            $response = $this->client->Finance->useVersion($apiVersion)->getStatements($params);

            // Kiểm tra và xử lý mã lỗi từ API
            if (isset($response['code']) && $response['code'] !== 0) {
                $errorCode = $response['code'];
                $errorMessage = $response['message'] ?? 'Lỗi không xác định';

                // Xử lý các mã lỗi cụ thể
                switch ($errorCode) {
                    case 10001: // Lỗi tham số
                        throw \App\Exceptions\TiktokApiException::fromResponse($response, 'Lỗi tham số khi gọi TikTok API: ' . $errorMessage);

                    case 10002: // Lỗi xác thực
                        throw \App\Exceptions\TiktokApiException::fromResponse($response, 'Lỗi xác thực khi gọi TikTok API: ' . $errorMessage);

                    case 429: // Rate limit
                        throw \App\Exceptions\TiktokApiException::fromResponse($response, 'Vượt quá giới hạn request TikTok API: ' . $errorMessage);

                    default:
                        throw \App\Exceptions\TiktokApiException::fromResponse($response, 'Lỗi khi lấy statements từ TikTok API: ' . $errorMessage);
                }
            }

            // Tính tổng số tiền đang hold và điều chỉnh
            $totalHoldAmount = 0;         // Tổng số tiền dương (hold thực sự)
            $totalAdjustments = 0;        // Tổng số tiền âm (điều chỉnh)
            $netAmount = 0;               // Số tiền ròng (dương - âm)
            $holdStatements = [];         // Các statements có số tiền dương
            $adjustmentStatements = [];   // Các statements có số tiền âm
            $allStatements = [];          // Tất cả statements
            $currency = 'USD';
            $nextPageToken = null;

            // Phân tích theo payment_id
            $paymentGroups = [];

            // Xử lý response theo cấu trúc thực tế từ TikTok API
            $responseData = [];

            if (isset($response['next_page_token']) || isset($response['statements'])) {
                // API trả về trực tiếp dữ liệu, không có code và data
                $responseData = $response;
            } else if (isset($response['code']) && $response['code'] === 0 && isset($response['data'])) {
                // API trả về dữ liệu trong trường data
                $responseData = $response['data'];
            } else {
                // Không có dữ liệu
                $responseData = ['next_page_token' => '', 'statements' => []];
            }

            // Lưu next_page_token nếu có
            if (isset($responseData['next_page_token'])) {
                $nextPageToken = $responseData['next_page_token'];
                // Xử lý trường hợp next_page_token rỗng
                if ($nextPageToken === '') {
                    $nextPageToken = null;
                }
            }

            // Xử lý statements
            if (isset($responseData['statements']) && is_array($responseData['statements'])) {
                foreach ($responseData['statements'] as $statement) {
                    // Xử lý tất cả các statement, không lọc theo payment_status
                    if (isset($statement['payment_status'])) {
                        // Sử dụng settlement_amount làm số tiền chính
                        $amount = isset($statement['settlement_amount']) ? floatval($statement['settlement_amount']) : 0;

                        // Thêm thông tin meta vào statement
                        $statement['_meta'] = [
                            'is_positive' => $amount > 0,
                            'is_negative' => $amount < 0,
                            'absolute_amount' => abs($amount),
                            'net_amount' => $amount,
                            'updated_at' => now()->toIso8601String(),
                            'scan_batch_id' => $scanBatchId,
                            'api_version' => $apiVersion,
                            'store_id' => $this->store->id,
                            'store_name' => $this->store->name,
                        ];

                        // Phân tích thêm về statement
                        if (isset($statement['adjustment_amount'])) {
                            $adjustmentAmount = floatval($statement['adjustment_amount']);
                            $statement['_meta']['has_adjustment'] = $adjustmentAmount != 0;
                            $statement['_meta']['adjustment_amount'] = $adjustmentAmount;
                        }

                        if (isset($statement['penalty_amount'])) {
                            $penaltyAmount = floatval($statement['penalty_amount']);
                            $statement['_meta']['has_penalty'] = $penaltyAmount != 0;
                            $statement['_meta']['penalty_amount'] = $penaltyAmount;
                        }

                        // Lưu tất cả statements
                        $allStatements[] = $statement;

                        // Lấy thông tin currency từ statement đầu tiên
                        if (!isset($currency) && isset($statement['currency'])) {
                            $currency = $statement['currency'];
                        }

                        // Phân loại theo số tiền dương/âm
                        if ($amount > 0) {
                            $totalHoldAmount += $amount;
                            $holdStatements[] = $statement;
                        } elseif ($amount < 0) {
                            $totalAdjustments += abs($amount);
                            $adjustmentStatements[] = $statement;
                        }

                        // Tính tổng ròng
                        $netAmount += $amount;

                        // Phân tích theo payment_id
                        $paymentId = $statement['payment_id'] ?? 'unknown';
                        if (!isset($paymentGroups[$paymentId])) {
                            $paymentGroups[$paymentId] = [
                                'total' => 0,
                                'statements' => [],
                                'positive_count' => 0,
                                'negative_count' => 0,
                                'payment_status' => $statement['payment_status'] ?? 'unknown'
                            ];
                        }
                        $paymentGroups[$paymentId]['total'] += $amount;
                        $paymentGroups[$paymentId]['statements'][] = $statement['id'] ?? count($paymentGroups[$paymentId]['statements']);

                        // Cập nhật số lượng statements dương/âm trong nhóm
                        if ($amount > 0) {
                            $paymentGroups[$paymentId]['positive_count']++;
                        } elseif ($amount < 0) {
                            $paymentGroups[$paymentId]['negative_count']++;
                        }
                        }
                    }
                }

            // Nếu có next_page_token, lấy thêm dữ liệu từ các trang tiếp theo
            $pageCount = 1;
            // Sử dụng maxPages từ options hoặc giá trị mặc định
            // $maxPages đã được khởi tạo ở đầu phương thức

            while ($nextPageToken && $pageCount < $maxPages) {
                $params['page_token'] = $nextPageToken;
                try {
                    // Log thông tin về next_page_token trước khi gọi API
                    Log::channel('tiktok_finance')->info('TikTok API Pagination Request', [
                        'store_id' => $this->store->id,
                        'store_name' => $this->store->name ?? 'Unknown',
                        'scan_batch_id' => $scanBatchId,
                        'page_count' => $pageCount,
                        'next_page_token' => $nextPageToken,
                        'api_version' => $apiVersion
                    ]);

                    // Thêm sleep để tránh rate limit
                    if ($pageCount > 1) {
                        usleep(500000); // 0.5 giây
                    }

                    // Gọi API với phiên bản API cụ thể
                    $nextResponse = $this->client->Finance->useVersion($apiVersion)->getStatements($params);

                    // Log chi tiết về response của trang tiếp theo
                    TiktokLogger::paginationInfo('response', $pageCount, [
                        'store_id' => $this->store->id,
                        'store_name' => $this->store->name,
                        'scan_batch_id' => $scanBatchId,
                        'response_code' => $nextResponse['code'] ?? 'unknown',
                        'has_data' => isset($nextResponse['data']),
                        'has_next_page_token' => isset($nextResponse['data']['next_page_token']),
                        'next_page_token' => $nextResponse['data']['next_page_token'] ?? null,
                        'statements_count' => isset($nextResponse['data']['statements']) ? count($nextResponse['data']['statements']) : 0,
                        'request_id' => $nextResponse['request_id'] ?? null
                    ]);

                    // Kiểm tra và xử lý mã lỗi từ API
                    if (isset($nextResponse['code']) && $nextResponse['code'] !== 0) {
                        $errorCode = $nextResponse['code'];
                        $errorMessage = $nextResponse['message'] ?? 'Lỗi không xác định';

                        // Log lỗi chi tiết
                        Log::channel('tiktok_finance')->warning('TikTok API Error in pagination', [
                            'store_id' => $this->store->id,
                            'store_name' => $this->store->name ?? 'Unknown',
                            'scan_batch_id' => $scanBatchId,
                            'page_count' => $pageCount,
                            'error_code' => $errorCode,
                            'error_message' => $errorMessage,
                            'request_id' => $nextResponse['request_id'] ?? null
                        ]);

                        // Xử lý các mã lỗi cụ thể
                        switch ($errorCode) {
                            case 429: // Rate limit
                                // Thử đợi và thử lại
                                Log::channel('tiktok_finance')->info('Rate limit hit, waiting before retry...', [
                                    'store_id' => $this->store->id,
                                    'page_count' => $pageCount
                                ]);
                                sleep(2); // Đợi 2 giây
                                continue 2; // Thử lại vòng lặp while

                            case 10002: // Lỗi xác thực
                                // Có thể cần refresh token
                                break;

                            default:
                                // Các lỗi khác
                                break;
                        }

                        break; // Dừng phân trang nếu có lỗi
                    }

                    // Xử lý response theo cấu trúc thực tế từ TikTok API
                    $nextResponseData = [];

                    if (isset($nextResponse['next_page_token']) || isset($nextResponse['statements'])) {
                        // API trả về trực tiếp dữ liệu, không có code và data
                        $nextResponseData = $nextResponse;
                    } else if (isset($nextResponse['code']) && $nextResponse['code'] === 0 && isset($nextResponse['data'])) {
                        // API trả về dữ liệu trong trường data
                        $nextResponseData = $nextResponse['data'];
                    } else {
                        // Không có dữ liệu hoặc có lỗi
                        break;
                    }

                    // Cập nhật next_page_token
                    $nextPageToken = $nextResponseData['next_page_token'] ?? null;

                    // Xử lý trường hợp next_page_token rỗng
                    if ($nextPageToken === '') {
                        $nextPageToken = null;
                    }

                    // Xử lý statements từ trang tiếp theo
                    if (isset($nextResponseData['statements']) && is_array($nextResponseData['statements'])) {
                        foreach ($nextResponseData['statements'] as $statement) {
                            // Xử lý tất cả các statement, không lọc theo payment_status
                            if (isset($statement['payment_status'])) {
                                // Sử dụng settlement_amount làm số tiền chính
                                $amount = isset($statement['settlement_amount']) ? floatval($statement['settlement_amount']) : 0;

                                // Thêm thông tin meta vào statement
                                $statement['_meta'] = [
                                    'is_positive' => $amount > 0,
                                    'is_negative' => $amount < 0,
                                    'absolute_amount' => abs($amount),
                                    'net_amount' => $amount,
                                    'updated_at' => now()->toIso8601String(),
                                    'scan_batch_id' => $scanBatchId,
                                    'api_version' => $apiVersion,
                                    'store_id' => $this->store->id,
                                    'store_name' => $this->store->name,
                                    'page_count' => $pageCount,
                                ];

                                // Phân tích thêm về statement
                                if (isset($statement['adjustment_amount'])) {
                                    $adjustmentAmount = floatval($statement['adjustment_amount']);
                                    $statement['_meta']['has_adjustment'] = $adjustmentAmount != 0;
                                    $statement['_meta']['adjustment_amount'] = $adjustmentAmount;
                                }

                                if (isset($statement['penalty_amount'])) {
                                    $penaltyAmount = floatval($statement['penalty_amount']);
                                    $statement['_meta']['has_penalty'] = $penaltyAmount != 0;
                                    $statement['_meta']['penalty_amount'] = $penaltyAmount;
                                }

                                // Lưu tất cả statements
                                $allStatements[] = $statement;

                                // Phân loại theo số tiền dương/âm
                                if ($amount > 0) {
                                    $totalHoldAmount += $amount;
                                    $holdStatements[] = $statement;
                                } elseif ($amount < 0) {
                                    $totalAdjustments += abs($amount);
                                    $adjustmentStatements[] = $statement;
                                }

                                // Tính tổng ròng
                                $netAmount += $amount;

                                // Phân tích theo payment_id
                                $paymentId = $statement['payment_id'] ?? 'unknown';
                                if (!isset($paymentGroups[$paymentId])) {
                                    $paymentGroups[$paymentId] = [
                                        'total' => 0,
                                        'statements' => [],
                                        'positive_count' => 0,
                                        'negative_count' => 0,
                                        'payment_status' => $statement['payment_status'] ?? 'unknown'
                                    ];
                                }
                                $paymentGroups[$paymentId]['total'] += $amount;
                                $paymentGroups[$paymentId]['statements'][] = $statement['id'] ?? count($paymentGroups[$paymentId]['statements']);

                                // Cập nhật số lượng statements dương/âm trong nhóm
                                if ($amount > 0) {
                                    $paymentGroups[$paymentId]['positive_count']++;
                                } elseif ($amount < 0) {
                                    $paymentGroups[$paymentId]['negative_count']++;
                                }
                            }
                        }
                    } else {
                        // Không có statements trong trang này
                        break;
                    }
                } catch (\Exception $paginationException) {
                    Log::channel('tiktok_finance')->warning('Error in pagination', [
                        'store_id' => $this->store->id,
                        'store_name' => $this->store->name ?? 'Unknown',
                        'page_count' => $pageCount,
                        'error' => $paginationException->getMessage()
                    ]);
                    break; // Dừng phân trang nếu có lỗi
                }

                $pageCount++;
            }


            return [
                'total_hold_amount' => $totalHoldAmount,
                'total_adjustments' => $totalAdjustments,
                'net_amount' => $netAmount,
                'currency' => $currency,
                'hold_statements' => $holdStatements,
                'adjustment_statements' => $adjustmentStatements,
                'all_statements' => $allStatements,
                'payment_groups' => $paymentGroups,
                'success' => true,
                'request_id' => $response['request_id'] ?? null,
                'scan_batch_id' => $scanBatchId,
                'api_version' => $apiVersion,
                'pages_processed' => $pageCount,
                'scan_duration_seconds' => time() - $currentTimestamp,
                'scan_time' => now()->toDateTimeString(),
                'statements_count' => [
                    'total' => count($allStatements),
                    'positive' => count($holdStatements),
                    'negative' => count($adjustmentStatements)
                ]
            ];
        } catch (\App\Exceptions\TiktokApiException $e) {
            // Log lỗi TikTok API cụ thể
            Log::channel('tiktok_finance')->error('TikTok API Error', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'error_type' => $e->errorType ?? 'Unknown',
                'error_message' => $e->getDetailedMessage(),
                'scan_batch_id' => $scanBatchId ?? 'unknown'
            ]);

            // Nếu lỗi là token hết hạn, thử refresh token và gọi lại
            if ($e->errorType === 'ACCESS_TOKEN_EXPIRED') {
                try {
                    // Refresh token
                    $auth = $this->client->auth();
                    $dataNewToken = $auth->refreshNewToken($this->store->refresh_token);
                    if ($dataNewToken['access_token']) {
                        $this->store->update([
                            'access_token' => $dataNewToken['access_token'],
                            'access_token_expire' => $dataNewToken['access_token_expire_in'],
                        ]);
                    }
                    $this->client->setAccessToken($dataNewToken['access_token']);

                    // Gọi lại API sau khi refresh token
                    return $this->getHoldAmount(); // Gọi đệ quy sau khi refresh token
                } catch (\Exception $refreshException) {
                    Log::channel('tiktok_finance')->error('Token refresh failed', [
                        'store_id' => $this->store->id,
                        'store_name' => $this->store->name ?? 'Unknown',
                        'error' => $refreshException->getMessage()
                    ]);
                    return [
                        'success' => false,
                        'error' => 'Không thể làm mới token: ' . $refreshException->getMessage(),
                        'error_type' => 'REFRESH_TOKEN_FAILED',
                        'command_message' => 'Lỗi làm mới token. Vui lòng kiểm tra lại thông tin xác thực của shop.'
                    ];
                }
            }

            // Trả về thông tin lỗi
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_type' => $e->errorType ?? 'UNKNOWN_ERROR',
                'command_message' => $e->getCommandMessage()
            ];
        } catch (\Exception $e) {
            // Xử lý các lỗi khác
            Log::channel('tiktok_finance')->error('Error getting hold amount', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Thử xác định xem có phải lỗi token không
            $errorMessage = strtolower($e->getMessage());
            if (strpos($errorMessage, 'access token is expired') !== false ||
                strpos($errorMessage, 'token expired') !== false ||
                strpos($errorMessage, 'expired credentials') !== false) {
                try {
                    // Refresh token
                    $auth = $this->client->auth();
                    $dataNewToken = $auth->refreshNewToken($this->store->refresh_token);
                    if ($dataNewToken['access_token']) {
                        $this->store->update([
                            'access_token' => $dataNewToken['access_token'],
                            'access_token_expire' => $dataNewToken['access_token_expire_in'],
                        ]);
                    }
                    $this->client->setAccessToken($dataNewToken['access_token']);

                    // Gọi lại API sau khi refresh token
                    return $this->getHoldAmount(); // Gọi đệ quy sau khi refresh token
                } catch (\Exception $refreshException) {
                    return [
                        'success' => false,
                        'error' => 'Không thể làm mới token: ' . $refreshException->getMessage(),
                        'command_message' => 'Lỗi làm mới token. Vui lòng kiểm tra lại thông tin xác thực của shop.'
                    ];
                }
            }

            return [
                'success' => false,
                'error' => 'Lỗi khi lấy số tiền đang hold: ' . $e->getMessage(),
                'command_message' => 'Lỗi không xác định: ' . $e->getMessage()
            ];
        }
    }
}
