<?php

namespace App\Services\Tiktok;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TiktokStoreService extends TiktokShopService
{
    /**
     * Get TikTok store profile information
     *
     * @return array
     * @throws Exception
     */
    public function getStoreProfile()
    {
        try {
            // Log detailed request information for debugging
            Log::channel('tiktok_api')->info('Making TikTok API request using AffiliateCreator', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'access_token' => $this->store->access_token ? 'EXISTS' : 'NULL',
                'refresh_token' => $this->store->refresh_token ? 'EXISTS' : 'NULL',
                'app_partner_id' => $this->store->app_partner_id ? 'EXISTS' : 'NULL',
                'app_key' => $this->store->partnerApp->app_key ?? 'NULL',
                'app_secret' => $this->store->partnerApp->app_secret ? 'EXISTS' : 'NULL'
            ]);

            // Check if refresh token is available before attempting refresh
            if (empty($this->store->refresh_token)) {
                Log::channel('tiktok_api')->warning('No refresh token available for store', [
                    'store_id' => $this->store->id,
                    'store_name' => $this->store->name ?? 'Unknown'
                ]);
            } else {
                // Proactively refresh token to ensure it's valid
                try {
                    $this->refreshAccessToken();
                    Log::channel('tiktok_api')->info('Access token refreshed proactively', [
                        'store_id' => $this->store->id,
                        'store_name' => $this->store->name ?? 'Unknown'
                    ]);
                } catch (Exception $refreshException) {
                    Log::channel('tiktok_api')->warning('Failed to refresh token proactively', [
                        'store_id' => $this->store->id,
                        'store_name' => $this->store->name ?? 'Unknown',
                        'error' => $refreshException->getMessage()
                    ]);
                    
                    // If refresh fails, continue with existing token
                    if (strpos($refreshException->getMessage(), 'can not find related auth record') !== false) {
                        Log::channel('tiktok_api')->warning('Refresh token is invalid or expired, will try with existing access token', [
                            'store_id' => $this->store->id,
                            'store_name' => $this->store->name ?? 'Unknown'
                        ]);
                    }
                }
            }

            // Try different approaches to get store/shop information
            // First try Authorization resource (most basic)
            try {
                $this->client->useVersion('202309'); // Use default version
                $response = $this->client->Authorization->getAuthorizedShop();
                
                Log::channel('tiktok_api')->info('Authorization getAuthorizedShop response', [
                    'store_id' => $this->store->id,
                    'response' => $response
                ]);
                
                // Check if we have shops array (new format)
                if (isset($response['shops']) && count($response['shops']) > 0) {
                    $shop = $response['shops'][0]; // Get first shop
                    
                    $shopInfo = [
                        'username' => $shop['name'] ?? 'Unknown Shop',
                        'shop_id' => $shop['id'] ?? 'N/A',
                        'region' => $shop['region'] ?? 'N/A',
                        'shop_type' => $shop['shop_type'] ?? 'N/A',
                        'seller_type' => $shop['seller_type'] ?? 'N/A',
                        'shop_status' => $shop['shop_status'] ?? 'ACTIVE',
                        'shop_code' => $shop['code'] ?? 'N/A',
                        'shop_cipher' => $shop['cipher'] ?? 'N/A',
                        'shop_url' => 'https://shop.tiktok.com/' . ($shop['name'] ?? ''),
                        'store_url' => 'https://www.tiktok.com/@' . ($shop['name'] ?? ''),
                        'data_source' => 'authorization_api'
                    ];
                    
                    Log::channel('tiktok_api')->info('Successfully got shop info from Authorization API', [
                        'store_id' => $this->store->id,
                        'shop_info' => $shopInfo
                    ]);
                    
                    return $shopInfo;
                }
                
                // Fallback for old format
                if (isset($response['shop_id']) || isset($response['shop_name']) || isset($response['region'])) {
                    $shopInfo = [
                        'username' => $response['shop_name'] ?? 'Unknown Shop',
                        'shop_id' => $response['shop_id'] ?? 'N/A',
                        'region' => $response['region'] ?? 'N/A',
                        'shop_type' => $response['shop_type'] ?? 'N/A',
                        'seller_type' => $response['seller_type'] ?? 'N/A',
                        'shop_status' => $response['shop_status'] ?? 'N/A',
                        'shop_url' => isset($response['shop_name']) ? 'https://shop.tiktok.com/' . $response['shop_name'] : null,
                        'data_source' => 'authorization_api'
                    ];
                    
                    Log::channel('tiktok_api')->info('Successfully got shop info from Authorization API (old format)', [
                        'store_id' => $this->store->id,
                        'shop_info' => $shopInfo
                    ]);
                    
                    return $shopInfo;
                }
            } catch (Exception $authException) {
                Log::channel('tiktok_api')->warning('Failed to get shop info from Authorization API', [
                    'store_id' => $this->store->id,
                    'error' => $authException->getMessage()
                ]);
            }
            
            // Second try Seller resource to get shop list
            try {
                $this->client->useVersion('202309'); // Use default version for Seller
                $response = $this->client->Seller->getActiveShopList();
                
                Log::channel('tiktok_api')->info('Seller getActiveShopList response', [
                    'store_id' => $this->store->id,
                    'response' => $response
                ]);
                
                // If we get shops, use the first one's information
                if (isset($response['shops']) && count($response['shops']) > 0) {
                    $shop = $response['shops'][0];
                    
                    // Format shop info similar to creator profile
                    $shopInfo = [
                        'username' => $shop['shop_name'] ?? 'Unknown Shop',
                        'shop_id' => $shop['shop_id'] ?? 'N/A',
                        'region' => $shop['region'] ?? 'N/A',
                        'shop_type' => $shop['shop_type'] ?? 'N/A',
                        'seller_type' => $shop['seller_type'] ?? 'N/A',
                        'shop_status' => $shop['shop_status'] ?? 'N/A',
                        'shop_url' => isset($shop['shop_name']) ? 'https://shop.tiktok.com/' . $shop['shop_name'] : null,
                        'data_source' => 'seller_api'
                    ];
                    
                    Log::channel('tiktok_api')->info('Successfully got shop info from Seller API', [
                        'store_id' => $this->store->id,
                        'shop_info' => $shopInfo
                    ]);
                    
                    return $shopInfo;
                }
            } catch (Exception $sellerException) {
                Log::channel('tiktok_api')->warning('Failed to get shop info from Seller API', [
                    'store_id' => $this->store->id,
                    'error' => $sellerException->getMessage()
                ]);
            }
            
            // If Seller API fails, try AffiliateCreator as fallback
            try {
                Log::channel('tiktok_api')->info('Trying AffiliateCreator as fallback');
                
                // Use the existing client with AffiliateCreator resource
                // Set version to 202405 which is required for AffiliateCreator
                $this->client->useVersion('202405');
                
                // Temporarily remove shop_cipher as it's not needed for AffiliateCreator
                $originalShopCipher = $this->store->shop_cipher;
                $this->client->setShopCipher('');
                
                $response = $this->client->AffiliateCreator->getCreatorProfile();
                
                // Restore original shop_cipher
                $this->client->setShopCipher($originalShopCipher);
                
                return $response['data'] ?? [];
            } catch (Exception $affiliateException) {
                Log::channel('tiktok_api')->warning('AffiliateCreator also failed', [
                    'store_id' => $this->store->id,
                    'error' => $affiliateException->getMessage()
                ]);
                
                // Re-throw the affiliate exception to be handled by outer catch
                throw $affiliateException;
            }
        } catch (Exception $e) {
            // Check if it's an access token issue or user type issue
            if (strpos($e->getMessage(), 'access token is invalid') !== false || 
                strpos($e->getMessage(), 'access token is expired') !== false ||
                strpos($e->getMessage(), 'expired') !== false) {
                
                // Only try to refresh if we have a refresh token
                if (empty($this->store->refresh_token)) {
                    Log::channel('tiktok_api')->error('Cannot refresh token - no refresh token available', [
                        'store_id' => $this->store->id,
                        'store_name' => $this->store->name ?? 'Unknown'
                    ]);
                    throw new Exception('Access token is invalid and no refresh token is available. Please re-authenticate this store.');
                }
                
                try {
                    Log::channel('tiktok_api')->info('Attempting token refresh due to invalid/expired token', [
                        'store_id' => $this->store->id,
                        'error' => $e->getMessage()
                    ]);
                    
                    $this->refreshAccessToken();
                    
                    // Retry the request with new token
                    $this->client->useVersion('202405');
                    
                    // Temporarily remove shop_cipher for retry
                    $originalShopCipher = $this->store->shop_cipher;
                    $this->client->setShopCipher('');
                    
                    $response = $this->client->AffiliateCreator->getCreatorProfile();
                    
                    // Restore original shop_cipher
                    $this->client->setShopCipher($originalShopCipher);
                    
                    Log::channel('tiktok_api')->info('AffiliateCreator response after token refresh', [
                        'store_id' => $this->store->id,
                        'response' => $response
                    ]);

                    return $response['data'] ?? [];
                } catch (Exception $retryException) {
                    Log::channel('tiktok_api')->error('Unable to get store profile after token refresh', [
                        'store_id' => $this->store->id,
                        'store_name' => $this->store->name ?? 'Unknown',
                        'error' => $retryException->getMessage()
                    ]);
                    
                    // Check if refresh token is invalid
                    if (strpos($retryException->getMessage(), 'can not find related auth record') !== false) {
                        throw new Exception('Refresh token is invalid or expired. Please re-authenticate this store in TikTok Shop.');
                    }
                    
                    throw new Exception('Unable to get store profile after token refresh: ' . $retryException->getMessage());
                }
            }

            // Check if it's a user type permission issue
            if (strpos($e->getMessage(), 'user type can not access this interface') !== false) {
                Log::channel('tiktok_api')->warning('Store user type cannot access AffiliateCreator interface', [
                    'store_id' => $this->store->id,
                    'store_name' => $this->store->name ?? 'Unknown',
                    'error' => $e->getMessage()
                ]);
                throw new Exception('This store is not configured as an Affiliate Creator and cannot access the creator profile interface. Please check your TikTok Shop settings.');
            }

            Log::channel('tiktok_api')->error('Error fetching store profile', [
                'store_id' => $this->store->id,
                'store_name' => $this->store->name ?? 'Unknown',
                'error' => $e->getMessage()
            ]);
            
            throw new Exception('Unable to fetch store profile: ' . $e->getMessage());
        }
    }

    /**
     * Get store profile formatted for display
     *
     * @return array
     */
    public function getFormattedStoreProfile()
    {
        try {
            $profile = $this->getStoreProfile();
            
            return [
                'username' => $profile['username'] ?? 'N/A',
                'avatar' => $profile['avatar']['url'] ?? null,
                'shop_id' => $profile['shop_id'] ?? 'N/A',
                'region' => $profile['region'] ?? 'N/A',
                'shop_type' => $profile['shop_type'] ?? 'N/A',
                'seller_type' => $profile['seller_type'] ?? 'N/A',
                'shop_status' => $profile['shop_status'] ?? 'N/A',
                'shop_code' => $profile['shop_code'] ?? 'N/A',
                'shop_cipher' => $profile['shop_cipher'] ?? 'N/A',
                'selection_region' => $profile['selection_region'] ?? $profile['region'] ?? 'N/A',
                'register_region' => $profile['register_region'] ?? $profile['region'] ?? 'N/A',
                'user_type' => $profile['user_type'] ?? $profile['seller_type'] ?? 'N/A',
                'creator_user_id' => $profile['creator_user_id'] ?? 'N/A',
                'permissions' => $profile['permissions'] ?? [],
                'store_url' => $profile['store_url'] ?? null,
                'shop_url' => $profile['shop_url'] ?? null,
                'data_source' => $profile['data_source'] ?? 'unknown'
            ];
        } catch (Exception $e) {
            Log::channel('tiktok_api')->error('Error formatting store profile', [
                'store_id' => $this->store->id,
                'error' => $e->getMessage()
            ]);
            
            return [
                'username' => 'Error loading profile',
                'avatar' => null,
                'shop_id' => 'N/A',
                'region' => 'N/A',
                'shop_type' => 'N/A',
                'seller_type' => 'N/A',
                'shop_status' => 'N/A',
                'shop_code' => 'N/A',
                'shop_cipher' => 'N/A',
                'selection_region' => 'N/A',
                'register_region' => 'N/A',
                'user_type' => 'N/A',
                'creator_user_id' => 'N/A',
                'permissions' => [],
                'store_url' => null,
                'shop_url' => null,
                'data_source' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
}