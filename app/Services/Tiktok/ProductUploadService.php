<?php

namespace App\Services\Tiktok;

use App\Models\ProductToUpload;
use App\Enums\ProductUploadStatus;
use Illuminate\Support\Facades\Storage;
use App\Models\Template;

class ProductUploadService
{
    public function uploadProduct(ProductToUpload $record)
    {
        $tiktok = new TiktokShopService($record->store);

        // Get template for other data
        $template = Template::find($record->template_id);
        if (!$template) {
            throw new \Exception('Template not found');
        }

        // Get images from record
        if (empty($record->images)) {

            throw new \Exception('No product images found. Please upload images first.');
        }

        // Upload images to TikTok
        $uploadedImages = [];
        foreach (array_slice($record->images, 0, 9) as $image) {
            // Kiểm tra nếu là URL từ Etsy
            if (filter_var($image, FILTER_VALIDATE_URL)) {
                $uploadedImages[] = [
                    'uri' => $tiktok->uploadImage($image, 'MAIN_IMAGE')
                ];
            } else {
                // URL từ S3 như cũ
                $imageUrl = Storage::disk('s3')->url($image);
                $uploadedImages[] = [
                    'uri' => $tiktok->uploadImage($imageUrl, 'MAIN_IMAGE')
                ];
            }
        }

        // Upload size chart if exists
        $sizeChartImageUri = null;
        if ($template->size_chart) {
            $sizeChartImageUri = $tiktok->uploadImage(
                Storage::disk('s3')->url($template->size_chart),
                'SIZE_CHART_IMAGE'
            );
        }

        // Format SKUs from template
        $formattedSkus = collect($template->skus)
            ->filter(fn($sku) => ($sku['stock'] ?? 0) > 0)
            ->map(function ($sku) use ($record) {
                return [
                    'sku_code' => $sku['sku_code'],
                    'seller_sku' => $sku['seller_sku'],
                    'price' => [
                        'amount' => strval($sku['price']),
                        'currency' => 'USD',
                    ],
                    'inventory' => [[
                        'warehouse_id' => $record->warehouse_id,
                        'quantity' => $sku['stock'],
                    ]],
                    'sales_attributes' => collect($sku['sales_attributes'])
                        ->map(fn($attr) => [
                            'name' => $attr['name'],
                            'value_name' => $attr['value_name']
                        ])
                        ->toArray(),
                ];
            })
            ->values()
            ->toArray();

        // Create product on TikTok
        return $tiktok->createProduct([
            'title' => $record->product_title,
            'description' => $record->description,
            'main_images' => $uploadedImages,
            'skus' => $formattedSkus,
            'brand_id' => $template->brand_id ?? '7184176719711028997',
            'package_weight' => $template->package_weight ?? [
                'unit' => 'KILOGRAM',
                'value' => '0.4'
            ],
            'package_dimensions' => $template->package_dimensions ?? [
                'height' => '5',
                'length' => '20',
                'unit' => 'CENTIMETER',
                'width' => '20'
            ],
            'product_attributes' => [
                [
                    'id' => '100198',
                    'values' => [['id' => '1001186', 'name' => 'Graphic']]
                ],
                [
                    'id' => '100392',
                    'values' => [['id' => '1001124', 'name' => 'Casual']]
                ],
                [
                    'id' => '100393',
                    'values' => [['id' => '1001126', 'name' => 'Crew Neck']]
                ],
                [
                    'id' => '100395',
                    'values' => [['id' => '1001144', 'name' => 'Long Sleeve']]
                ],
                [
                    'id' => '100397',
                    'values' => [
                        ['id' => '1000905', 'name' => 'Autumn'],
                        ['id' => '1001163', 'name' => 'Winter']
                    ]
                ],
                [
                    'id' => '100398',
                    'values' => [
                        ['id' => '1001171', 'name' => 'Cute'],
                        ['id' => '1001178', 'name' => 'Holiday'],
                        ['id' => '1001176', 'name' => 'Street'],
                        ['id' => '1001165', 'name' => 'Basics']
                    ]
                ],
                [
                    'id' => '100399',
                    'values' => [['id' => '1001181', 'name' => 'Loose-fitting']]
                ],
                [
                    'id' => '100401',
                    'values' => [['id' => '1001198', 'name' => 'Do Not Dry Clean']]
                ],
                [
                    'id' => '100409',
                    'values' => [['id' => '1007463', 'name' => 'Pullover Hoodie']]
                ],
                [
                    'id' => '100701',
                    'values' => [['id' => '1000039', 'name' => 'Cotton']]
                ],
                [
                    'id' => '100898',
                    'values' => [
                        ['id' => '1004738', 'name' => 'New Year'],
                        ['id' => '1003609', 'name' => 'Christmas']
                    ]
                ],
                [
                    'id' => '101127',
                    'values' => [['id' => '1005904', 'name' => 'Normal Type']]
                ],
                [
                    'id' => '101610',
                    'values' => [['id' => '1000325', 'name' => 'None']]
                ],
                [
                    'id' => '101619',
                    'values' => [['id' => '1000059', 'name' => 'No']]
                ],
                [
                    'id' => '101395',
                    'values' => [['id' => '1000059', 'name' => 'No']]
                ],
                [
                    'id' => '101400',
                    'values' => [['id' => '1000059', 'name' => 'No']]
                ]
            ],
            'is_cod_open' => false,
            'category_id' => (string)$template->category_id,
            'category_version' => 'v2',
            'save_mode' => $record->save_mode,
            'size_chart' => $sizeChartImageUri ? [
                'image' => ['uri' => $sizeChartImageUri]
            ] : null,
        ]);
    }

    public function uploadDirectly($store, $data)
    {
        // Tạo một temporary ProductToUpload object mà không lưu vào DB
        $tempRecord = new ProductToUpload([
            'store_id' => $store->id,
            'template_id' => $data['template_id'],
            'product_title' => $data['product_title'],
            'description' => $data['description'],
            'warehouse_id' => $data['warehouse_id'],
            'save_mode' => $data['save_mode'],
            'images' => $data['images'],
        ]);

        // Set relationship
        $tempRecord->setRelation('store', $store);

        // Sử dụng lại logic upload hiện có
        return $this->uploadProduct($tempRecord);
    }
}
