<?php

namespace App\Services\Tiktok;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class TiktokApiService
{
    protected string $rapidApiKey;
    protected string $rapidApiHost = 'tiktok-api6.p.rapidapi.com';
    protected string $baseUrl = 'https://tiktok-api6.p.rapidapi.com';

    public function __construct()
    {
        $this->rapidApiKey = config('services.rapidapi.key');
        
        if (empty($this->rapidApiKey)) {
            throw new Exception('RapidAPI key is not configured');
        }
    }

    /**
     * Get user videos from TikTok
     *
     * @param string $username
     * @param int $count
     * @return array
     * @throws Exception
     */
    public function getUserVideos(string $username, int $count = 20): array
    {
        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'x-rapidapi-host' => $this->rapidApiHost,
                'x-rapidapi-key' => $this->rapidApiKey,
            ])->post($this->baseUrl . '/user/videos', [
                'username' => $username,
                'count' => $count
            ]);

            if (!$response->successful()) {
                Log::error('TikTok API request failed', [
                    'username' => $username,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                
                throw new Exception('Failed to fetch TikTok videos: ' . $response->body());
            }

            $data = $response->json();
            
            Log::info('TikTok API request successful', [
                'username' => $username,
                'video_count' => count($data['videos'] ?? [])
            ]);

            return $data;
            
        } catch (Exception $e) {
            Log::error('TikTok API service error', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Get user profile information
     *
     * @param string $username
     * @return array
     * @throws Exception
     */
    public function getUserProfile(string $username): array
    {
        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'x-rapidapi-host' => $this->rapidApiHost,
                'x-rapidapi-key' => $this->rapidApiKey,
            ])->post($this->baseUrl . '/user/info', [
                'username' => $username
            ]);

            if (!$response->successful()) {
                Log::error('TikTok profile API request failed', [
                    'username' => $username,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                
                throw new Exception('Failed to fetch TikTok profile: ' . $response->body());
            }

            $data = $response->json();
            
            Log::info('TikTok profile API request successful', [
                'username' => $username,
                'profile_data' => isset($data['user']) ? 'EXISTS' : 'NULL'
            ]);

            return $data;
            
        } catch (Exception $e) {
            Log::error('TikTok profile API service error', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Sync channel data with profile information
     *
     * @param string $username
     * @return array
     * @throws Exception
     */
    public function syncChannelData(string $username): array
    {
        $profileData = $this->getUserProfile($username);
        $videosData = $this->getUserVideos($username);
        
        return [
            'profile' => $profileData,
            'videos' => $videosData,
            'synced_at' => now()
        ];
    }
}