<?php

namespace App\Services;

use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\SupplierOrder;
use App\Models\SellerFundRequest;
use App\Models\Production;
use App\Models\DesignJob;
use App\Models\PayoutTransaction;
use App\Models\TikTokPayment;
use App\Models\Store;
use App\Enums\OrderStatus;
use App\Enums\SupplierOrderStatus;
use App\Enums\DesignJobStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SellerInvoiceService
{
    public User $seller;
    public Carbon $startDate;
    public Carbon $endDate;

    public function __construct(User $seller, Carbon $startDate, Carbon $endDate)
    {
        $this->seller = $seller;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    /**
     * Lấy danh sách đơn hàng với phân trang
     */
    public function getOrdersQuery()
    {
        return Order::query()
            ->where('seller_id', $this->seller->id)
            ->whereBetween('orders.created_at', [$this->startDate, $this->endDate])
            ->with(['store', 'orderItems.product'])
            ->orderBy('created_at', 'desc');
    }

    /**
     * Lấy chi tiết đơn hàng (order items) - tất cả status
     */
    public function getOrderItemsQuery()
    {
        return OrderItem::query()
            ->whereHas('order', function ($query) {
                $query->where('seller_id', $this->seller->id)
                      ->whereBetween('created_at', [$this->startDate, $this->endDate]);
            })
            ->with(['order.store', 'product'])
            ->orderBy('created_at', 'desc');
    }

    /**
     * Lấy chi phí đi đơn (fulfillment cost) - tất cả status
     */
    public function getFulfillmentCostQuery()
    {
        return SupplierOrder::query()
            ->where('seller_id', $this->seller->id)
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->with(['order', 'supplier'])
            ->orderBy('created_at', 'desc');
    }

    /**
     * Lấy chi phí quảng cáo (advertising cost) - tất cả status
     */
    public function getAdvertisingCostQuery()
    {
        return SellerFundRequest::query()
            ->where('seller_id', $this->seller->id)
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->orderBy('created_at', 'desc');
    }

    /**
     * Lấy chi phí thiết kế (design cost) - tất cả status
     */
    public function getDesignCostQuery()
    {
        return DesignJob::query()
            ->where('created_by', $this->seller->id)
            ->whereBetween('completed_at', [$this->startDate, $this->endDate])
            ->with(['design', 'designer'])
            ->orderBy('completed_at', 'desc');
    }

    /**
     * Lấy chi phí in áo (printing cost) - tất cả status
     */
    public function getPrintingCostQuery()
    {
        return Production::query()
            ->where('seller_id', $this->seller->id)
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->with(['blank'])
            ->orderBy('created_at', 'desc');
    }

    /**
     * Lấy thông tin bank payout - tất cả status
     */
    public function getBankPayoutQuery()
    {
        // Bypass global scope để lấy tất cả stores của seller
        $storeIds = $this->seller->stores()->withoutGlobalScopes()->pluck('bank_account')->filter()->toArray();

        if (empty($storeIds)) {
            return PayoutTransaction::query()->whereRaw('1 = 0');
        }

        return PayoutTransaction::query()
            ->withoutGlobalScopes() // Bypass global scope cho PayoutTransaction
            ->whereIn('card_no', $storeIds)
            ->whereBetween('time', [$this->startDate, $this->endDate])
            ->orderBy('time', 'desc');
    }

    /**
     * Lấy thông tin TikTok payout
     */
    public function getTikTokPayoutQuery()
    {
        // Bypass global scope để lấy tất cả stores của seller
        $storeIds = $this->seller->stores()->withoutGlobalScopes()->pluck('id')->toArray();

        if (empty($storeIds)) {
            return TikTokPayment::query()->whereRaw('1 = 0');
        }

        return TikTokPayment::query()
            ->withoutGlobalScopes() // Bypass global scope cho TikTokPayment
            ->whereIn('store_id', $storeIds)
            ->where('status', 'PAID')
            ->whereBetween('paid_time', [$this->startDate, $this->endDate])
            ->with(['store'])
            ->orderBy('paid_time', 'desc');
    }

    /**
     * Tính tổng chi phí theo loại
     */
    public function calculateCostSummary(): array
    {
        // Chi phí đi đơn
        $fulfillmentCost = $this->getFulfillmentCostQuery()->sum('base_cost');

        // Chi phí quảng cáo
        $advertisingCost = $this->getAdvertisingCostQuery()->sum('amount');

        // Chi phí thiết kế
        $designCost = $this->getDesignCostQuery()
            ->sum(DB::raw('COALESCE(price, 0) + COALESCE(rush_fee, 0)'));

        // Chi phí in áo
        $printingCost = $this->getPrintingCostQuery()
            ->get()
            ->sum(function ($production) {
                return $production->calculateProductionCost();
            });

        return [
            'fulfillment_cost' => $fulfillmentCost,
            'advertising_cost' => $advertisingCost,
            'design_cost' => $designCost,
            'printing_cost' => $printingCost,
            'total_cost' => $fulfillmentCost + $advertisingCost + $designCost + $printingCost,
        ];
    }

    /**
     * Tính tổng bank payout
     */
    public function calculateBankPayoutSummary(): array
    {
        $bankPayout = $this->getBankPayoutQuery();
        $tiktokPayout = $this->getTikTokPayoutQuery();

        return [
            'bank_total' => $bankPayout->sum('amount'),
            'bank_count' => $bankPayout->count(),
            'tiktok_total' => $tiktokPayout->sum('settlement_amount'),
            'tiktok_count' => $tiktokPayout->count(),
            'grand_total' => $bankPayout->sum('amount') + $tiktokPayout->sum('settlement_amount'),
        ];
    }

    /**
     * Tính tổng đơn hàng
     */
    public function calculateOrdersSummary(): array
    {
        $ordersQuery = $this->getOrdersQuery();

        $totalOrders = $ordersQuery->count();
        $completedOrders = (clone $ordersQuery)->where('orders.status', OrderStatus::Completed->value)->count();
        $totalRevenue = (clone $ordersQuery)
            ->where('orders.status', OrderStatus::Completed->value)
            ->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->sum('order_items.total');

        return [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'total_revenue' => $totalRevenue,
            'completion_rate' => $totalOrders > 0 ? ($completedOrders / $totalOrders) * 100 : 0,
        ];
    }

    /**
     * Lấy dữ liệu lỗ tháng trước từ bảng seller_finances
     */
    public function getPreviousMonthLossQuery()
    {
        // Tính tháng trước dựa trên startDate hiện tại
        $previousMonth = $this->startDate->copy()->subMonth();
        $previousMonthKey = $previousMonth->startOfMonth()->toDateString();

        // Truy vấn báo cáo tài chính của tháng trước
        return DB::table('seller_finances')
            ->where('seller_id', $this->seller->id)
            ->where('month', $previousMonthKey)
            ->select([
                'id',
                'seller_id',
                'month',
                'gross_revenue',
                'platform_fees',
                'net_revenue',
                'total_cost',
                'gross_profit',
                'net_profit',
                'previous_loss',
                'adjusted_profit',
                'base_salary',
                'commission',
                'total_salary',
                'payout_on_hold',
                'status',
                'created_at',
                'updated_at'
            ])
            ->orderBy('month', 'desc');
    }
}
