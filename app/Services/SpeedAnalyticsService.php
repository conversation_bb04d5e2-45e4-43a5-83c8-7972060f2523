<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Design;
use App\Models\SupplierOrder;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;


class SpeedAnalyticsService
{
    /**
     * Tính toán tốc độ xử lý order
     */
    public function getOrderSpeed($timeframe = '1m', $sellerId = null, $startDate = null, $endDate = null)
    {
        $query = Order::query();

        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        // Sử dụng custom date range nếu có
        if ($startDate && $endDate) {
            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);
            $totalHours = $start->diffInHours($end);
            $totalDays = max(1, round($start->diffInDays($end))); // Làm tròn và đảm bảo ít nhất 1 ngày
        } else {
            $now = Carbon::now();
            $start = $this->getStartDateByTimeframe($timeframe, $now);
            $end = $now;
            $totalHours = $start->diffInHours($end);
            $totalDays = max(1, round($start->diffInDays($end))); // Làm tròn và đảm bảo ít nhất 1 ngày
        }

        // Tốc độ nhận order trong khoảng thời gian
        $ordersInPeriod = (clone $query)
            ->whereBetween('created_at', [$start, $end])
            ->count();

        // Tốc độ theo giờ và ngày (làm tròn thành số nguyên cho orders)
        $hourlyRate = $totalHours > 0 ? round($ordersInPeriod / $totalHours) : 0;
        $dailyRate = $totalDays > 0 ? round($ordersInPeriod / $totalDays) : 0;

        // Trend analysis - so sánh với period trước đó
        $previousStart = $start->copy()->sub($start->diffInSeconds($end), 'seconds');
        $previousEnd = $start->copy();

        $ordersPreviousPeriod = (clone $query)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->count();

        $growthRate = $ordersPreviousPeriod > 0 ?
            round(($ordersInPeriod - $ordersPreviousPeriod) / $ordersPreviousPeriod * 100, 2) :
            ($ordersInPeriod > 0 ? 100 : 0);

        return [
            'speed' => [
                'hourly' => $hourlyRate,
                'daily_avg' => $dailyRate,
                'weekly_avg' => round($dailyRate * 7), // Làm tròn thành số nguyên
                'total_in_period' => $ordersInPeriod,
                'period_hours' => $totalHours,
                'period_days' => $totalDays
            ],
            'trend' => [
                'growth_rate' => $growthRate,
                'current_period' => $ordersInPeriod,
                'previous_period' => $ordersPreviousPeriod,
                'period_comparison' => $this->formatPeriodComparison($start, $end)
            ],
            'processing_time' => $this->getOrderProcessingTime($sellerId, $start, $end),
            'queue' => $this->getOrderQueue($sellerId)
        ];
    }
    
    /**
     * Tính toán tốc độ design - dựa trên DesignJob hoàn thành
     */
    public function getDesignSpeed($timeframe = '1m', $sellerId = null, $startDate = null, $endDate = null)
    {
        // Sử dụng DesignJob thay vì Design để tính chính xác hơn
        $query = \App\Models\DesignJob::query();

        if ($sellerId) {
            $query->where('created_by', $sellerId); // DesignJob được tạo bởi seller
        }

        // Sử dụng custom date range nếu có
        if ($startDate && $endDate) {
            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);
            $totalHours = $start->diffInHours($end);
            $totalDays = max(1, round($start->diffInDays($end))); // Làm tròn và đảm bảo ít nhất 1 ngày
        } else {
            $now = Carbon::now();
            $start = $this->getStartDateByTimeframe($timeframe, $now);
            $end = $now;
            $totalHours = $start->diffInHours($end);
            $totalDays = max(1, round($start->diffInDays($end))); // Làm tròn và đảm bảo ít nhất 1 ngày
        }

        // Design jobs hoàn thành trong khoảng thời gian (dựa trên completed_at)
        $designJobsCompleted = (clone $query)
            ->where('status', 'completed')
            ->whereNotNull('completed_at')
            ->whereBetween('completed_at', [$start, $end])
            ->count();

        // Tốc độ theo giờ và ngày (làm tròn thành số nguyên cho design jobs)
        $hourlyRate = $totalHours > 0 ? round($designJobsCompleted / $totalHours) : 0;
        $dailyRate = $totalDays > 0 ? round($designJobsCompleted / $totalDays) : 0;

        // Productivity per designer - designers có job hoàn thành trong period
        $activeDesigners = \App\Models\DesignJob::where('status', 'completed')
            ->whereNotNull('completed_at')
            ->whereBetween('completed_at', [$start, $end])
            ->distinct('designer_id')
            ->count('designer_id');

        // Nếu không có designer nào active, lấy tất cả designers
        if ($activeDesigners == 0) {
            $activeDesigners = User::role('Designer')->count();
        }

        return [
            'speed' => [
                'hourly' => $hourlyRate,
                'daily_avg' => $dailyRate,
                'weekly_avg' => round($dailyRate * 7), // Làm tròn thành số nguyên
                'per_designer' => $activeDesigners > 0 ? round($dailyRate / $activeDesigners) : 0, // Làm tròn thành số nguyên
                'total_in_period' => $designJobsCompleted,
                'period_hours' => $totalHours,
                'period_days' => $totalDays
            ],
            'processing_time' => $this->getDesignProcessingTime($sellerId, $start, $end),
            'queue' => $this->getDesignQueue($sellerId),
            'capacity' => [
                'active_designers' => $activeDesigners,
                'theoretical_daily_capacity' => $activeDesigners * 8, // 8 design jobs per designer per day
                'theoretical_period_capacity' => $activeDesigners * 8 * $totalDays,
                'utilization_rate' => $activeDesigners > 0 && $totalDays > 0 ?
                    round($designJobsCompleted / ($activeDesigners * 8 * $totalDays) * 100, 2) : 0
            ]
        ];
    }
    
    /**
     * Tính toán tốc độ fulfillment - từ lúc nhận order đến lúc fulfill (tạo SupplierOrder)
     */
    public function getFulfillmentSpeed($timeframe = '1m', $sellerId = null, $startDate = null, $endDate = null)
    {
        $query = SupplierOrder::query();

        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        // Sử dụng custom date range nếu có
        if ($startDate && $endDate) {
            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);
            $totalHours = $start->diffInHours($end);
            $totalDays = max(1, round($start->diffInDays($end))); // Làm tròn và đảm bảo ít nhất 1 ngày
        } else {
            $now = Carbon::now();
            $start = $this->getStartDateByTimeframe($timeframe, $now);
            $end = $now;
            $totalHours = $start->diffInHours($end);
            $totalDays = max(1, round($start->diffInDays($end))); // Làm tròn và đảm bảo ít nhất 1 ngày
        }

        // Fulfillments được tạo trong khoảng thời gian (từ order đến fulfill)
        $fulfillmentsCreated = (clone $query)
            ->whereBetween('created_at', [$start, $end])
            ->count();

        // Tốc độ theo giờ và ngày (làm tròn thành số nguyên cho fulfillments)
        $hourlyRate = $totalHours > 0 ? round($fulfillmentsCreated / $totalHours) : 0;
        $dailyRate = $totalDays > 0 ? round($fulfillmentsCreated / $totalDays) : 0;

        // Auto vs Manual fulfillment trong period
        $autoFulfillments = (clone $query)
            ->whereHas('order', function($q) {
                $q->whereHas('orderItems.productVariant', function($pv) {
                    $pv->where('auto_fulfill', true);
                });
            })
            ->whereBetween('created_at', [$start, $end])
            ->count();

        $manualFulfillments = $fulfillmentsCreated - $autoFulfillments;

        // Staff productivity - staff active trong period
        $activeFulfillmentStaff = User::role(['Fulfillment', 'Leader'])
            ->where(function($q) use ($start, $end) {
                $q->whereBetween('last_activity', [$start, $end])
                  ->orWhere('last_activity', '>=', $start->copy()->subDays(7)); // Hoặc active trong 7 ngày trước period
            })
            ->count();

        // Nếu không có staff nào active, lấy tất cả fulfillment staff
        if ($activeFulfillmentStaff == 0) {
            $activeFulfillmentStaff = User::role(['Fulfillment', 'Leader'])->count();
        }

        return [
            'speed' => [
                'hourly' => $hourlyRate, // Tốc độ fulfill (từ order đến tạo SupplierOrder)
                'daily_avg' => $dailyRate,
                'weekly_avg' => round($dailyRate * 7), // Làm tròn thành số nguyên
                'per_staff' => $activeFulfillmentStaff > 0 ? round($dailyRate / $activeFulfillmentStaff) : 0, // Làm tròn thành số nguyên
                'total_in_period' => $fulfillmentsCreated, // Số fulfillments được tạo trong kỳ
                'period_hours' => $totalHours,
                'period_days' => $totalDays
            ],
            'processing_time' => $this->getFulfillmentProcessingTime($sellerId, $start, $end),
            'queue' => $this->getFulfillmentQueue($sellerId),
            'automation' => [
                'auto_fulfillment_rate' => $fulfillmentsCreated > 0 ? round($autoFulfillments / $fulfillmentsCreated * 100, 2) : 0,
                'manual_fulfillments' => $manualFulfillments,
                'auto_fulfillments' => $autoFulfillments,
                'total_fulfillments' => $fulfillmentsCreated
            ],
            'capacity' => [
                'active_staff' => $activeFulfillmentStaff,
                'theoretical_daily_capacity' => $activeFulfillmentStaff * 20, // 20 fulfillments per staff per day
                'theoretical_period_capacity' => $activeFulfillmentStaff * 20 * $totalDays,
                'utilization_rate' => $activeFulfillmentStaff > 0 && $totalDays > 0 ?
                    round($manualFulfillments / ($activeFulfillmentStaff * 20 * $totalDays) * 100, 2) : 0
            ]
        ];
    }
    
    /**
     * Phát hiện bottleneck trong hệ thống
     */
    public function detectBottlenecks($sellerId = null, $startDate = null, $endDate = null)
    {
        $timeframe = $this->calculateTimeframeFromDates($startDate, $endDate);
        $orderSpeed = $this->getOrderSpeed($timeframe, $sellerId, $startDate, $endDate);
        $designSpeed = $this->getDesignSpeed($timeframe, $sellerId, $startDate, $endDate);
        $fulfillmentSpeed = $this->getFulfillmentSpeed($timeframe, $sellerId, $startDate, $endDate);
        
        $bottlenecks = [];
        $recommendations = [];
        
        // 1. Design Bottleneck
        if ($orderSpeed['speed']['hourly'] > $designSpeed['speed']['hourly'] * 1.2) {
            $severity = round(($orderSpeed['speed']['hourly'] / max($designSpeed['speed']['hourly'], 1) - 1) * 100, 2);
            $additionalDesigners = ceil(($orderSpeed['speed']['hourly'] - $designSpeed['speed']['hourly']) / 1); // 1 design per hour per designer
            
            $bottlenecks[] = [
                'type' => 'design',
                'severity' => $severity,
                'current_capacity' => $designSpeed['speed']['hourly'],
                'required_capacity' => $orderSpeed['speed']['hourly'],
                'gap' => $orderSpeed['speed']['hourly'] - $designSpeed['speed']['hourly']
            ];
            
            $recommendations[] = [
                'action' => 'hire_designers',
                'count' => $additionalDesigners,
                'priority' => $severity > 50 ? 'high' : 'medium',
                'message' => "Cần tuyển thêm {$additionalDesigners} designer để đáp ứng tốc độ order"
            ];
        }
        
        // 2. Fulfillment Bottleneck
        if ($designSpeed['speed']['hourly'] > $fulfillmentSpeed['speed']['hourly'] * 1.2) {
            $severity = round(($designSpeed['speed']['hourly'] / max($fulfillmentSpeed['speed']['hourly'], 1) - 1) * 100, 2);
            $additionalStaff = ceil(($designSpeed['speed']['hourly'] - $fulfillmentSpeed['speed']['hourly']) / 2.5); // 2.5 fulfillments per hour per staff
            
            $bottlenecks[] = [
                'type' => 'fulfillment',
                'severity' => $severity,
                'current_capacity' => $fulfillmentSpeed['speed']['hourly'],
                'required_capacity' => $designSpeed['speed']['hourly'],
                'gap' => $designSpeed['speed']['hourly'] - $fulfillmentSpeed['speed']['hourly']
            ];
            
            $recommendations[] = [
                'action' => 'hire_fulfillment_staff',
                'count' => $additionalStaff,
                'priority' => $severity > 50 ? 'high' : 'medium',
                'message' => "Cần tuyển thêm {$additionalStaff} nhân viên fulfillment để đáp ứng tốc độ design"
            ];
        }
        
        // 3. Overall System Bottleneck
        if ($orderSpeed['speed']['hourly'] > $fulfillmentSpeed['speed']['hourly'] * 1.5) {
            $severity = round(($orderSpeed['speed']['hourly'] / max($fulfillmentSpeed['speed']['hourly'], 1) - 1) * 100, 2);

            $bottlenecks[] = [
                'type' => 'system',
                'severity' => $severity,
                'current_capacity' => $fulfillmentSpeed['speed']['hourly'],
                'required_capacity' => $orderSpeed['speed']['hourly'],
                'gap' => $orderSpeed['speed']['hourly'] - $fulfillmentSpeed['speed']['hourly'],
                'message' => 'Toàn bộ hệ thống cần được mở rộng'
            ];

            $recommendations[] = [
                'action' => 'scale_system',
                'priority' => 'high',
                'message' => 'Cần mở rộng toàn bộ pipeline xử lý'
            ];
        }
        
        return [
            'bottlenecks' => $bottlenecks,
            'recommendations' => $recommendations,
            'overall_health' => $this->calculateSystemHealth($orderSpeed, $designSpeed, $fulfillmentSpeed)
        ];
    }
    
    /**
     * Tính toán sức khỏe tổng thể của hệ thống
     */
    private function calculateSystemHealth($orderSpeed, $designSpeed, $fulfillmentSpeed)
    {
        $orderRate = $orderSpeed['speed']['hourly'];
        $designRate = $designSpeed['speed']['hourly'];
        $fulfillmentRate = $fulfillmentSpeed['speed']['hourly'];

        // Tính tỷ lệ cân bằng
        $designBalance = $orderRate > 0 ? min($designRate / $orderRate, 1) : 1;
        $fulfillmentBalance = $designRate > 0 ? min($fulfillmentRate / $designRate, 1) : 1;

        $overallHealth = ($designBalance + $fulfillmentBalance) / 2 * 100;

        $status = 'excellent';
        if ($overallHealth < 90) $status = 'good';
        if ($overallHealth < 75) $status = 'warning';
        if ($overallHealth < 60) $status = 'critical';

        return [
            'score' => round($overallHealth, 2),
            'status' => $status,
            'design_balance' => round($designBalance * 100, 2),
            'fulfillment_balance' => round($fulfillmentBalance * 100, 2)
        ];
    }

    /**
     * Tính toán thời gian xử lý order
     */
    private function getOrderProcessingTime($sellerId = null, $startDate = null, $endDate = null)
    {
        $query = Order::query();

        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        // Lọc theo date range nếu có
        if ($startDate && $endDate) {
            $query->whereBetween('orders.created_at', [$startDate, $endDate]);
        }

        // Thời gian từ order đến design - sửa lại join đúng cấu trúc
        $timeToDesign = (clone $query)
            ->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->join('products', 'order_items.product_id', '=', 'products.id')
            ->join('designs', 'products.id', '=', 'designs.product_id')
            ->whereNotNull('designs.created_at')
            ->where('designs.created_at', '>=', DB::raw('orders.created_at')) // Đảm bảo design được tạo sau order
            ->select(DB::raw('AVG(TIMESTAMPDIFF(MINUTE, orders.created_at, designs.created_at)) as avg_minutes'))
            ->value('avg_minutes') ?? 0;

        // Thời gian từ order đến fulfill
        $timeToFulfill = (clone $query)
            ->join('supplier_orders', 'orders.id', '=', 'supplier_orders.order_id')
            ->whereNotNull('supplier_orders.created_at')
            ->where('supplier_orders.created_at', '>=', DB::raw('orders.created_at')) // Đảm bảo fulfill sau order
            ->select(DB::raw('AVG(TIMESTAMPDIFF(MINUTE, orders.created_at, supplier_orders.created_at)) as avg_minutes'))
            ->value('avg_minutes') ?? 0;

        return [
            'avg_time_to_design_minutes' => round(abs($timeToDesign)), // Làm tròn thành số nguyên
            'avg_time_to_design_hours' => round(abs($timeToDesign) / 60, 1), // 1 chữ số thập phân cho giờ
            'avg_time_to_fulfill_minutes' => round(abs($timeToFulfill)),
            'avg_time_to_fulfill_hours' => round(abs($timeToFulfill) / 60, 1)
        ];
    }

    /**
     * Tính toán queue order
     */
    private function getOrderQueue($sellerId = null)
    {
        $query = Order::query();

        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        // Orders cần design - kiểm tra xem có design hoàn thành chưa
        $pendingDesign = (clone $query)
            ->whereDoesntHave('orderItems', function($orderItemQuery) {
                $orderItemQuery->whereHas('product', function($productQuery) {
                    $productQuery->whereHas('design', function($designQuery) {
                        $designQuery->where('status', 'Design');
                    });
                });
            })
            ->count();

        // Orders cần fulfillment - chưa có supplier order
        $pendingFulfillment = (clone $query)
            ->whereDoesntHave('SupplierOrders')
            ->whereIn('store_order_status', ['Processing', 'Awaiting Shipment'])
            ->count();

        return [
            'pending_design' => $pendingDesign,
            'pending_fulfillment' => $pendingFulfillment,
            'total_pending' => $pendingDesign + $pendingFulfillment
        ];
    }

    /**
     * Tính toán thời gian xử lý design - từ lúc nhận design job đến lúc hoàn thành
     */
    private function getDesignProcessingTime($sellerId = null, $startDate = null, $endDate = null)
    {
        $query = \App\Models\DesignJob::query();

        if ($sellerId) {
            $query->where('created_by', $sellerId);
        }

        // Lọc theo date range nếu có (dựa trên completed_at)
        if ($startDate && $endDate) {
            $query->whereBetween('completed_at', [$startDate, $endDate]);
        }

        // Tính thời gian trung bình từ lúc tạo job đến lúc hoàn thành
        $avgDesignTime = $query->where('status', 'completed')
            ->whereNotNull('created_at')
            ->whereNotNull('completed_at')
            ->where('completed_at', '>=', DB::raw('created_at')) // Đảm bảo completed_at >= created_at
            ->select(DB::raw('AVG(TIMESTAMPDIFF(MINUTE, created_at, completed_at)) as avg_minutes'))
            ->value('avg_minutes') ?? 0;

        // Tính thời gian từ assigned đến completed (thời gian designer thực sự làm việc)
        // Sử dụng designer_id để xác định job đã được assign
        $avgWorkingTime = $query->where('status', 'completed')
            ->whereNotNull('designer_id')
            ->whereNotNull('completed_at')
            ->where('completed_at', '>=', DB::raw('created_at'))
            ->select(DB::raw('AVG(TIMESTAMPDIFF(MINUTE, created_at, completed_at)) as avg_minutes'))
            ->value('avg_minutes') ?? 0;

        return [
            'avg_design_time_minutes' => round(abs($avgDesignTime)), // Làm tròn thành số nguyên
            'avg_design_time_hours' => round(abs($avgDesignTime) / 60, 1), // 1 chữ số thập phân cho giờ
            'avg_working_time_minutes' => round(abs($avgWorkingTime)),
            'avg_working_time_hours' => round(abs($avgWorkingTime) / 60, 1),
            'avg_queue_time_minutes' => round(abs($avgDesignTime - $avgWorkingTime)),
            'avg_queue_time_hours' => round(abs($avgDesignTime - $avgWorkingTime) / 60, 1)
        ];
    }

    /**
     * Tính toán queue design - dựa trên DesignJob
     */
    private function getDesignQueue($sellerId = null)
    {
        $query = \App\Models\DesignJob::query();

        if ($sellerId) {
            $query->where('created_by', $sellerId);
        }

        $pendingJobs = (clone $query)->where('status', 'pending')->count();
        $assignedJobs = (clone $query)->where('status', 'assigned')->count();
        $inProgressJobs = (clone $query)->where('status', 'in_progress')->count();
        $underReviewJobs = (clone $query)->where('status', 'under_review')->count();
        $needsRevisionJobs = (clone $query)->where('status', 'needs_revision')->count();

        return [
            'pending' => $pendingJobs,
            'assigned' => $assignedJobs,
            'in_progress' => $inProgressJobs,
            'under_review' => $underReviewJobs,
            'needs_revision' => $needsRevisionJobs,
            'total_queue' => $pendingJobs + $assignedJobs + $inProgressJobs + $underReviewJobs + $needsRevisionJobs,
            'active_work' => $inProgressJobs + $underReviewJobs + $needsRevisionJobs // Jobs đang được làm việc
        ];
    }

    /**
     * Tính toán thời gian xử lý fulfillment - từ lúc nhận order đến lúc fulfill
     */
    private function getFulfillmentProcessingTime($sellerId = null, $startDate = null, $endDate = null)
    {
        $query = SupplierOrder::query()
            ->join('orders', 'supplier_orders.order_id', '=', 'orders.id');

        if ($sellerId) {
            $query->where('orders.seller_id', $sellerId);
        }

        // Lọc theo date range nếu có (dựa trên khi SupplierOrder được tạo)
        if ($startDate && $endDate) {
            $query->whereBetween('supplier_orders.created_at', [$startDate, $endDate]);
        }

        // Tính thời gian từ order được tạo đến khi fulfill (tạo SupplierOrder)
        $avgOrderToFulfillTime = $query
            ->whereNotNull('orders.created_at')
            ->whereNotNull('supplier_orders.created_at')
            ->where('supplier_orders.created_at', '>=', DB::raw('orders.created_at')) // Đảm bảo fulfill sau order
            ->select(DB::raw('AVG(TIMESTAMPDIFF(MINUTE, orders.created_at, supplier_orders.created_at)) as avg_minutes'))
            ->value('avg_minutes') ?? 0;

        return [
            'avg_order_to_fulfill_minutes' => round(abs($avgOrderToFulfillTime)), // Làm tròn thành số nguyên
            'avg_order_to_fulfill_hours' => round(abs($avgOrderToFulfillTime) / 60, 1), // 1 chữ số thập phân cho giờ
            'avg_order_to_fulfill_days' => round(abs($avgOrderToFulfillTime) / (60 * 24), 1) // 1 chữ số thập phân cho ngày
        ];
    }

    /**
     * Tính toán queue fulfillment
     */
    private function getFulfillmentQueue($sellerId = null)
    {
        $query = SupplierOrder::query();

        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        $pendingFulfillments = (clone $query)->where('status', 'Pending')->count();
        $inProgressFulfillments = (clone $query)->whereIn('status', ['AwaitingShipment', 'InProducing'])->count();

        return [
            'pending' => $pendingFulfillments,
            'in_progress' => $inProgressFulfillments,
            'total_queue' => $pendingFulfillments + $inProgressFulfillments
        ];
    }

    /**
     * Tính toán tỷ lệ tăng trưởng
     */
    private function calculateGrowthRate($query, $days = 7)
    {
        $now = Carbon::now();
        $currentPeriod = (clone $query)->where('created_at', '>=', $now->subDays($days))->count();
        $previousPeriod = (clone $query)->where('created_at', '>=', $now->subDays($days * 2))
            ->where('created_at', '<', $now->subDays($days))->count();

        if ($previousPeriod == 0) {
            return $currentPeriod > 0 ? 100 : 0;
        }

        return round(($currentPeriod - $previousPeriod) / $previousPeriod * 100, 2);
    }

    /**
     * Lấy tổng quan tất cả metrics
     */
    public function getDashboardData($sellerId = null, $startDate = null, $endDate = null, $timeframe = '1m')
    {
        return [
            'order_speed' => $this->getOrderSpeed($timeframe, $sellerId, $startDate, $endDate),
            'design_speed' => $this->getDesignSpeed($timeframe, $sellerId, $startDate, $endDate),
            'fulfillment_speed' => $this->getFulfillmentSpeed($timeframe, $sellerId, $startDate, $endDate),
            'timestamp' => Carbon::now()->toISOString(),
            'period' => [
                'start' => $startDate ? $startDate->format('d/m/Y') : null,
                'end' => $endDate ? $endDate->format('d/m/Y') : null,
                'timeframe' => $timeframe
            ]
        ];
    }

    /**
     * Helper method để tính start date dựa trên timeframe
     */
    private function getStartDateByTimeframe($timeframe, $endDate)
    {
        return match($timeframe) {
            '1w' => $endDate->copy()->subWeek(),
            '1m' => $endDate->copy()->subMonth(),
            '3m' => $endDate->copy()->subMonths(3),
            '1y' => $endDate->copy()->subYear(),
            default => $endDate->copy()->subMonth()
        };
    }

    /**
     * Format period comparison text
     */
    private function formatPeriodComparison($start, $end)
    {
        $diffInDays = $start->diffInDays($end);

        if ($diffInDays <= 7) {
            return 'so với tuần trước';
        } elseif ($diffInDays <= 31) {
            return 'so với tháng trước';
        } elseif ($diffInDays <= 93) {
            return 'so với 3 tháng trước';
        } else {
            return 'so với cùng kỳ năm trước';
        }
    }

    /**
     * Calculate timeframe from date range
     */
    private function calculateTimeframeFromDates($startDate, $endDate)
    {
        if (!$startDate || !$endDate) {
            return '1m';
        }

        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        $diffInDays = $start->diffInDays($end);

        if ($diffInDays <= 7) {
            return '1w';
        } elseif ($diffInDays <= 31) {
            return '1m';
        } elseif ($diffInDays <= 93) {
            return '3m';
        } else {
            return '1y';
        }
    }

    /**
     * So sánh dữ liệu 24h gần nhất với 24h trước đó
     * Dùng cho dashboard và cronjob
     */
    public function getDailyComparison($sellerId = null)
    {
        $now = Carbon::now();

        // 24h gần nhất (hiện tại -> 24h trước)
        $last24hStart = $now->copy()->subHours(24);
        $last24hEnd = $now->copy();

        // 24h trước đó (48h trước -> 24h trước)
        $previous24hStart = $now->copy()->subHours(48);
        $previous24hEnd = $now->copy()->subHours(24);

        return [
            'timestamp' => $now->toISOString(),
            'period_last_24h' => [
                'start' => $last24hStart->toISOString(),
                'end' => $last24hEnd->toISOString(),
                'display' => $last24hStart->format('d/m H:i') . ' - ' . $last24hEnd->format('d/m H:i')
            ],
            'period_previous_24h' => [
                'start' => $previous24hStart->toISOString(),
                'end' => $previous24hEnd->toISOString(),
                'display' => $previous24hStart->format('d/m H:i') . ' - ' . $previous24hEnd->format('d/m H:i')
            ],
            'orders' => $this->compareOrdersIn24h($last24hStart, $last24hEnd, $previous24hStart, $previous24hEnd, $sellerId),
            'designs' => $this->compareDesignsIn24h($last24hStart, $last24hEnd, $previous24hStart, $previous24hEnd, $sellerId),
            'fulfillments' => $this->compareFulfillmentsIn24h($last24hStart, $last24hEnd, $previous24hStart, $previous24hEnd, $sellerId)
        ];
    }

    /**
     * So sánh orders trong 2 khoảng thời gian 24h
     */
    private function compareOrdersIn24h($last24hStart, $last24hEnd, $previous24hStart, $previous24hEnd, $sellerId = null)
    {
        $query = Order::query();
        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        // Đếm orders trong 24h gần nhất
        $ordersLast24h = (clone $query)->whereBetween('created_at', [$last24hStart, $last24hEnd])->count();

        // Đếm orders trong 24h trước đó
        $ordersPrevious24h = (clone $query)->whereBetween('created_at', [$previous24hStart, $previous24hEnd])->count();

        // Tính growth rate
        $growthRate = $ordersPrevious24h > 0
            ? round((($ordersLast24h - $ordersPrevious24h) / $ordersPrevious24h) * 100, 2)
            : ($ordersLast24h > 0 ? 100 : 0);

        // Tính tốc độ per hour
        $rateLast24h = round($ordersLast24h / 24, 2);
        $ratePrevious24h = round($ordersPrevious24h / 24, 2);

        return [
            'last_24h' => $ordersLast24h,
            'previous_24h' => $ordersPrevious24h,
            'difference' => $ordersLast24h - $ordersPrevious24h,
            'growth_rate' => $growthRate,
            'rate_last_24h' => $rateLast24h,
            'rate_previous_24h' => $ratePrevious24h,
            'rate_difference' => round($rateLast24h - $ratePrevious24h, 2),
            'alert_level' => $this->getAlertLevel($growthRate),
            'alert_message' => $this->getAlertMessage('orders', $growthRate, $ordersLast24h)
        ];
    }

    /**
     * So sánh design jobs completed trong 2 khoảng thời gian 24h
     */
    private function compareDesignsIn24h($last24hStart, $last24hEnd, $previous24hStart, $previous24hEnd, $sellerId = null)
    {
        $query = \App\Models\DesignJob::where('status', 'completed')->whereNotNull('completed_at');
        if ($sellerId) {
            $query->where('created_by', $sellerId);
        }

        // Đếm design jobs completed trong 24h gần nhất
        $designsLast24h = (clone $query)->whereBetween('completed_at', [$last24hStart, $last24hEnd])->count();

        // Đếm design jobs completed trong 24h trước đó
        $designsPrevious24h = (clone $query)->whereBetween('completed_at', [$previous24hStart, $previous24hEnd])->count();

        // Tính growth rate
        $growthRate = $designsPrevious24h > 0
            ? round((($designsLast24h - $designsPrevious24h) / $designsPrevious24h) * 100, 2)
            : ($designsLast24h > 0 ? 100 : 0);

        // Tính tốc độ per hour
        $rateLast24h = round($designsLast24h / 24, 2);
        $ratePrevious24h = round($designsPrevious24h / 24, 2);

        return [
            'last_24h' => $designsLast24h,
            'previous_24h' => $designsPrevious24h,
            'difference' => $designsLast24h - $designsPrevious24h,
            'growth_rate' => $growthRate,
            'rate_last_24h' => $rateLast24h,
            'rate_previous_24h' => $ratePrevious24h,
            'rate_difference' => round($rateLast24h - $ratePrevious24h, 2),
            'alert_level' => $this->getAlertLevel($growthRate),
            'alert_message' => $this->getAlertMessage('designs', $growthRate, $designsLast24h)
        ];
    }

    /**
     * So sánh fulfillments trong 2 khoảng thời gian 24h
     */
    private function compareFulfillmentsIn24h($last24hStart, $last24hEnd, $previous24hStart, $previous24hEnd, $sellerId = null)
    {
        $query = SupplierOrder::query();
        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        // Đếm fulfillments created trong 24h gần nhất
        $fulfillmentsLast24h = (clone $query)->whereBetween('created_at', [$last24hStart, $last24hEnd])->count();

        // Đếm fulfillments created trong 24h trước đó
        $fulfillmentsPrevious24h = (clone $query)->whereBetween('created_at', [$previous24hStart, $previous24hEnd])->count();

        // Tính growth rate
        $growthRate = $fulfillmentsPrevious24h > 0
            ? round((($fulfillmentsLast24h - $fulfillmentsPrevious24h) / $fulfillmentsPrevious24h) * 100, 2)
            : ($fulfillmentsLast24h > 0 ? 100 : 0);

        // Tính tốc độ per hour
        $rateLast24h = round($fulfillmentsLast24h / 24, 2);
        $ratePrevious24h = round($fulfillmentsPrevious24h / 24, 2);

        return [
            'last_24h' => $fulfillmentsLast24h,
            'previous_24h' => $fulfillmentsPrevious24h,
            'difference' => $fulfillmentsLast24h - $fulfillmentsPrevious24h,
            'growth_rate' => $growthRate,
            'rate_last_24h' => $rateLast24h,
            'rate_previous_24h' => $ratePrevious24h,
            'rate_difference' => round($rateLast24h - $ratePrevious24h, 2),
            'alert_level' => $this->getAlertLevel($growthRate),
            'alert_message' => $this->getAlertMessage('fulfillments', $growthRate, $fulfillmentsLast24h)
        ];
    }

    /**
     * Xác định mức độ cảnh báo dựa trên growth rate
     */
    private function getAlertLevel($growthRate): string
    {
        if ($growthRate > 50) return 'critical';
        if ($growthRate > 30) return 'warning';
        if ($growthRate > 15) return 'info';
        if ($growthRate < -30) return 'danger';
        return 'normal';
    }

    /**
     * Tạo message cảnh báo
     */
    private function getAlertMessage($type, $growthRate, $count): string
    {
        $typeText = match($type) {
            'orders' => 'Orders',
            'designs' => 'Design Jobs',
            'fulfillments' => 'Fulfillments',
            default => $type
        };

        if ($growthRate > 50) {
            return "🔴 CRITICAL: {$typeText} tăng {$growthRate}% trong 24h ({$count}). Cần hành động ngay!";
        } elseif ($growthRate > 30) {
            return "🟡 WARNING: {$typeText} tăng {$growthRate}% trong 24h ({$count}). Cần theo dõi capacity!";
        } elseif ($growthRate > 15) {
            return "🟦 INFO: {$typeText} tăng {$growthRate}% trong 24h ({$count}). Tăng trưởng tốt!";
        } elseif ($growthRate < -30) {
            return "🟠 DANGER: {$typeText} giảm {$growthRate}% trong 24h ({$count}). Cần kiểm tra!";
        }
        return "🟢 NORMAL: {$typeText} thay đổi {$growthRate}% trong 24h ({$count}).";
    }

    /**
     * So sánh trong khoảng thời gian tùy chỉnh (dành cho cronjob 3 giờ)
     */
    public function getCustomPeriodComparison($hours = 3, $sellerId = null)
    {
        $now = Carbon::now();

        // Khoảng thời gian hiện tại (X giờ gần nhất)
        $currentPeriodStart = $now->copy()->subHours($hours);
        $currentPeriodEnd = $now->copy();

        // Khoảng thời gian tương ứng hôm qua
        $previousPeriodStart = $now->copy()->subDay()->subHours($hours);
        $previousPeriodEnd = $now->copy()->subDay();

        return [
            'timestamp' => $now->toISOString(),
            'period_hours' => $hours,
            'current_period' => [
                'start' => $currentPeriodStart->toISOString(),
                'end' => $currentPeriodEnd->toISOString(),
                'display' => $currentPeriodStart->format('d/m H:i') . ' - ' . $currentPeriodEnd->format('d/m H:i')
            ],
            'previous_period' => [
                'start' => $previousPeriodStart->toISOString(),
                'end' => $previousPeriodEnd->toISOString(),
                'display' => $previousPeriodStart->format('d/m H:i') . ' - ' . $previousPeriodEnd->format('d/m H:i')
            ],
            'orders' => $this->compareOrdersInPeriod($currentPeriodStart, $currentPeriodEnd, $previousPeriodStart, $previousPeriodEnd, $hours, $sellerId),
            'designs' => $this->compareDesignsInPeriod($currentPeriodStart, $currentPeriodEnd, $previousPeriodStart, $previousPeriodEnd, $hours, $sellerId),
            'fulfillments' => $this->compareFulfillmentsInPeriod($currentPeriodStart, $currentPeriodEnd, $previousPeriodStart, $previousPeriodEnd, $hours, $sellerId)
        ];
    }

    /**
     * So sánh orders trong khoảng thời gian tùy chỉnh
     */
    private function compareOrdersInPeriod($currentStart, $currentEnd, $previousStart, $previousEnd, $hours, $sellerId = null)
    {
        $query = Order::query();
        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        $currentCount = (clone $query)->whereBetween('created_at', [$currentStart, $currentEnd])->count();
        $previousCount = (clone $query)->whereBetween('created_at', [$previousStart, $previousEnd])->count();

        $growthRate = $previousCount > 0
            ? round((($currentCount - $previousCount) / $previousCount) * 100, 2)
            : ($currentCount > 0 ? 100 : 0);

        $currentRate = round($currentCount / $hours, 2);
        $previousRate = round($previousCount / $hours, 2);

        return [
            'current_period' => $currentCount,
            'previous_period' => $previousCount,
            'difference' => $currentCount - $previousCount,
            'growth_rate' => $growthRate,
            'current_rate_per_hour' => $currentRate,
            'previous_rate_per_hour' => $previousRate,
            'rate_difference' => round($currentRate - $previousRate, 2),
            'alert_level' => $this->getAlertLevel($growthRate),
            'alert_message' => $this->getAlertMessage('orders', $growthRate, $currentCount)
        ];
    }

    /**
     * So sánh design jobs trong khoảng thời gian tùy chỉnh
     */
    private function compareDesignsInPeriod($currentStart, $currentEnd, $previousStart, $previousEnd, $hours, $sellerId = null)
    {
        $query = \App\Models\DesignJob::where('status', 'completed')->whereNotNull('completed_at');
        if ($sellerId) {
            $query->where('created_by', $sellerId);
        }

        $currentCount = (clone $query)->whereBetween('completed_at', [$currentStart, $currentEnd])->count();
        $previousCount = (clone $query)->whereBetween('completed_at', [$previousStart, $previousEnd])->count();

        $growthRate = $previousCount > 0
            ? round((($currentCount - $previousCount) / $previousCount) * 100, 2)
            : ($currentCount > 0 ? 100 : 0);

        $currentRate = round($currentCount / $hours, 2);
        $previousRate = round($previousCount / $hours, 2);

        return [
            'current_period' => $currentCount,
            'previous_period' => $previousCount,
            'difference' => $currentCount - $previousCount,
            'growth_rate' => $growthRate,
            'current_rate_per_hour' => $currentRate,
            'previous_rate_per_hour' => $previousRate,
            'rate_difference' => round($currentRate - $previousRate, 2),
            'alert_level' => $this->getAlertLevel($growthRate),
            'alert_message' => $this->getAlertMessage('designs', $growthRate, $currentCount)
        ];
    }

    /**
     * So sánh fulfillments trong khoảng thời gian tùy chỉnh
     */
    private function compareFulfillmentsInPeriod($currentStart, $currentEnd, $previousStart, $previousEnd, $hours, $sellerId = null)
    {
        $query = SupplierOrder::query();
        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        $currentCount = (clone $query)->whereBetween('created_at', [$currentStart, $currentEnd])->count();
        $previousCount = (clone $query)->whereBetween('created_at', [$previousStart, $previousEnd])->count();

        $growthRate = $previousCount > 0
            ? round((($currentCount - $previousCount) / $previousCount) * 100, 2)
            : ($currentCount > 0 ? 100 : 0);

        $currentRate = round($currentCount / $hours, 2);
        $previousRate = round($previousCount / $hours, 2);

        return [
            'current_period' => $currentCount,
            'previous_period' => $previousCount,
            'difference' => $currentCount - $previousCount,
            'growth_rate' => $growthRate,
            'current_rate_per_hour' => $currentRate,
            'previous_rate_per_hour' => $previousRate,
            'rate_difference' => round($currentRate - $previousRate, 2),
            'alert_level' => $this->getAlertLevel($growthRate),
            'alert_message' => $this->getAlertMessage('fulfillments', $growthRate, $currentCount)
        ];
    }
}
