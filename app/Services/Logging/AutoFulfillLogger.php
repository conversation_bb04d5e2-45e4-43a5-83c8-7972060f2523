<?php

namespace App\Services\Logging;

use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AutoFulfillLogger
{
    private string $correlationId;
    private array $context = [];

    public function __construct()
    {
        $this->correlationId = Str::uuid()->toString();
    }

    /**
     * Set correlation ID for tracking related logs
     */
    public function setCorrelationId(string $correlationId): self
    {
        $this->correlationId = $correlationId;
        return $this;
    }

    /**
     * Add context data that will be included in all logs
     */
    public function addContext(array $context): self
    {
        $this->context = array_merge($this->context, $context);
        return $this;
    }

    /**
     * Clear context data
     */
    public function clearContext(): self
    {
        $this->context = [];
        return $this;
    }

    /**
     * Log command start with basic statistics
     */
    public function logCommandStart(int $totalCandidateOrders, int $eligibleOrdersCount): void
    {
        $context = [
            'total_candidates' => $totalCandidateOrders,
            'eligible_orders' => $eligibleOrdersCount,
            'filtered_out' => $totalCandidateOrders - $eligibleOrdersCount,
        ];

        if ($totalCandidateOrders > 0) {
            $context['success_rate'] = round(($eligibleOrdersCount / $totalCandidateOrders) * 100, 1);
        }

        $message = "START: Tìm thấy {$eligibleOrdersCount}/{$totalCandidateOrders} đơn đủ điều kiện";
        $this->logOperation('info', $message, []);
    }

    /**
     * Log process completion with summary
     */
    public function logProcessComplete(array $stats = []): void
    {
        $successful = $stats['successful_orders'] ?? 0;
        $failed = $stats['failed_orders'] ?? 0;
        $skipped = $stats['skipped_orders'] ?? 0;
        $duration = round(($stats['total_duration_ms'] ?? 0) / 1000, 1);

        $message = "COMPLETE: {$successful} thành công, {$failed} lỗi, {$skipped} bỏ qua ({$duration}s)";

        $context = [];

        $this->logOperation('info', $message, $context);
    }

    /**
     * Log order processing start (simplified)
     */
    public function logOrderProcessingStart(Order $order): void
    {
        $this->logOrder('debug', 'PROCESSING: ' . $order->order_code, [
            'order_code' => $order->order_code,
            'order_id' => $order->id,
        ]);
    }

    /**
     * Log order eligibility check (simplified)
     */
    public function logOrderEligibilityCheck(Order $order, bool $isEligible, array $reasons = []): void
    {
        if (!$isEligible) {
            $reasonText = implode(', ', $reasons);
            $this->logOrder('warning', "SKIPPED: {$order->order_code} → {$reasonText}", []);
        }
    }

    /**
     * Log label printing (simplified)
     */
    public function logLabelPrinting(Order $order, bool $success, string $error = null): void
    {
        if (!$success) {
            $this->logOrder('error', 'LABEL ERROR: ' . $order->order_code . ' - ' . $error, [
                'order_code' => $order->order_code,
                'error' => $error,
            ]);
        }
    }

    /**
     * Log FlashShip order creation with detailed variant information
     */
    public function logFlashShipOrderCreation(Order $order, array $result): void
    {
        $success = $result['success'] ?? false;
        $level = $success ? 'info' : 'error';
        $message = $success ? 'FLASHSHIP CREATE: SUCCESS' : 'FLASHSHIP CREATE: FAILED';

        $context = [
            'order_code' => $order->order_code,
            'order_id' => $order->id,
            'flashship_result' => $result,
            'submission_timestamp' => now()->toISOString(),
        ];

        // Add detailed variant information for FlashShip submission
        if ($success) {
            $flashshipItems = [];
            $orderItems = $order->orderItems ?? collect();
            foreach ($orderItems as $item) {
                $variant = $item->productVariant;
                if ($variant) {
                    $flashshipItems[] = [
                        'item_id' => $item->id ?? null,
                        'variant_id' => $variant->variant_id ?? null,
                        'product_name' => $variant->product?->name ?? 'Unknown',
                        'variant_title' => $variant->title ?? 'Unknown',
                        'quantity' => $item->quantity ?? 0,
                        'size' => $variant->size ?? null,
                        'color' => $variant->color ?? null,
                        'design_files_submitted' => [
                            'front' => !empty($variant->design_front_url),
                            'back' => !empty($variant->design_back_url),
                            'sleeve_left' => !empty($variant->sleeve_left_design_url),
                            'sleeve_right' => !empty($variant->sleeve_right_design_url),
                        ],
                        'supplier_cost' => $variant->cost_price ?? null,
                        'weight' => $variant->weight ?? null,
                    ];
                }
            }
            $context['flashship_items'] = $flashshipItems;
            $context['total_items_submitted'] = count($flashshipItems);
        }

        $this->logOrder($level, $message, $context);

        // Also log to API channel for FlashShip integration tracking
        $this->logToChannel('auto_fulfill_api', $level, $message, $context);
    }

    /**
     * Log FlashShip order sync
     */
    public function logFlashShipOrderSync(Order $order, array $flashshipOrder = null, bool $success = true): void
    {
        $level = $success ? 'info' : 'warning';
        $message = $success ? 'FLASHSHIP SYNC: SUCCESS' : 'FLASHSHIP SYNC: NOT FOUND';

        $this->logOrder($level, $message, []);
    }

    /**
     * Log order fulfillment success (simplified)
     */
    public function logOrderFulfillmentSuccess(Order $order, array $supplierOrder = null): void
    {
        $trackingNumber = $supplierOrder['tracking_number'] ?? null;
        $itemsCount = $order->orderItems ? $order->orderItems->count() : 0;

        if ($trackingNumber) {
            $message = "SUCCESS: {$order->order_code} → {$trackingNumber}";
        } else {
            $message = "SUCCESS: {$order->order_code}";
        }

        $this->logOrder('info', $message, []);
    }

    /**
     * Log order fulfillment failure (short title, detailed context)
     */
    public function logOrderFulfillmentFailure(Order $order, \Exception $exception): void
    {
        $errorMsg = $exception->getMessage();

        // Short title for easy scanning
        $title = "FAILED: {$order->order_code}";

        // Essential context for debugging
        $context = [
            'order_id' => $order->id,
            'order_code' => $order->order_code,
            'seller_id' => $order->seller_id,
            'store_id' => $order->store_id,
            'fulfillment_type' => $order->fulfillment_type,
            'store_order_status' => $order->store_order_status,
            'error_message' => $errorMsg,
        ];

        $this->logOrder('error', $title, $context);
    }

    /**
     * Log performance metrics (simplified)
     */
    public function logPerformanceMetrics(string $operation, float $duration): void
    {
        if ($duration > 5.0) { // Only log slow operations
            $durationFormatted = round($duration, 1);
            $this->logOperation('warning', "SLOW: {$operation} mất {$durationFormatted}s", []);
        }
    }

    /**
     * Log order ineligibility (simplified)
     */
    public function logOrderIneligibilityDetails(Order $order, array $detailedReasons = []): void
    {
        $reasonText = implode(', ', $detailedReasons);
        $this->logOrder('debug', "INELIGIBLE: {$order->order_code} → {$reasonText}", []);
    }

    /**
     * Core logging method for operations (process overview)
     */
    private function logOperation(string $level, string $message, array $context = []): void
    {
        Log::channel('auto_fulfill_operations')->{$level}($message, $context);
    }

    /**
     * Core logging method for individual orders
     */
    private function logOrder(string $level, string $message, array $context = []): void
    {
        // Sanitize message and context for clean logging
        $sanitizedMessage = $this->sanitizeOrderCode($message);
        $sanitizedContext = $this->sanitizeContext($context);

        Log::channel('auto_fulfill_orders')->{$level}($sanitizedMessage, $sanitizedContext);
    }

    /**
     * Sanitize order code to avoid JSON parsing issues in log viewers
     */
    private function sanitizeOrderCode(string $orderCode): string
    {
        // Replace square brackets with parentheses to avoid JSON parsing conflicts
        return str_replace(['[', ']'], ['(', ')'], $orderCode);
    }

    /**
     * Sanitize context data for clean logging
     */
    private function sanitizeContext(array $context): array
    {
        $sanitized = [];
        foreach ($context as $key => $value) {
            if ($key === 'order_code' && is_string($value)) {
                $sanitized[$key] = $this->sanitizeOrderCode($value);
            } else {
                $sanitized[$key] = $value;
            }
        }
        return $sanitized;
    }

    /**
     * Log to specific channel (short title, detailed context)
     */
    public function logToChannel(string $channel, string $level, string $message, array $context = []): void
    {
        // Sanitize message and context to avoid log viewer parsing issues
        $sanitizedMessage = $this->sanitizeOrderCode($message);
        $sanitizedContext = $this->sanitizeContext($context);

        Log::channel($channel)->{$level}($sanitizedMessage, $sanitizedContext);
    }


}
