<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class ZillizVectorService
{
    private $client;
    private $apiKey;
    private $clusterEndpoint;
    private $defaultCollection;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => 60.0,
            'verify' => false,
        ]);
        
        $this->apiKey = env('ZILLIZ_API_KEY');
        $this->clusterEndpoint = env('ZILLIZ_CLUSTER_ENDPOINT', 'in03-0d5e91df40a7f1b.api.gcp-us-west1.zillizcloud.com');
        $this->defaultCollection = env('ZILLIZ_DEFAULT_COLLECTION', 'seller_reports_embeddings');
    }

    /**
     * Tạo collection mới
     */
    public function createCollection($collectionName, $dimension = 1536, $description = '')
    {
        $schema = [
            'collectionName' => $collectionName,
            'description' => $description ?: "Vector collection for {$collectionName}",
            'dimension' => $dimension,
            'metricType' => 'IP',
            'primaryField' => 'id',
            'vectorField' => 'vector'
        ];

        try {
            $response = $this->client->post("https://{$this->clusterEndpoint}/v1/vector/collections/create", [
                'headers' => $this->getHeaders(),
                'json' => $schema
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            return $result['code'] === 200;

        } catch (RequestException $e) {
            return false;
        }
    }

    /**
     * Lấy danh sách collections
     */
    public function listCollections()
    {
        try {
            $response = $this->client->get("https://{$this->clusterEndpoint}/v1/vector/collections", [
                'headers' => $this->getHeaders()
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($result['code'] === 200 && isset($result['data'])) {
                // Nếu data là array of strings, convert thành array of objects
                if (is_array($result['data']) && !empty($result['data'])) {
                    $collections = [];
                    foreach ($result['data'] as $item) {
                        if (is_string($item)) {
                            $collections[] = ['collectionName' => $item];
                        } else {
                            $collections[] = $item;
                        }
                    }
                    return $collections;
                }
                return $result['data'];
            }

            return [];

        } catch (RequestException $e) {
            return [];
        }
    }

    /**
     * Insert vectors vào collection
     */
    public function insertVectors($vectors, $collectionName = null)
    {
        $collectionName = $collectionName ?: $this->defaultCollection;
        
        $insertData = [
            'collectionName' => $collectionName,
            'data' => $vectors
        ];

        try {
            $response = $this->client->post("https://{$this->clusterEndpoint}/v1/vector/insert", [
                'headers' => $this->getHeaders(),
                'json' => $insertData
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            return [
                'success' => $result['code'] === 200,
                'insertCount' => $result['data']['insertCount'] ?? 0,
                'result' => $result
            ];

        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Tìm kiếm vector tương tự
     */
    public function searchSimilar($queryVector, $limit = 5, $collectionName = null)
    {
        $collectionName = $collectionName ?: $this->defaultCollection;
        
        $searchQuery = [
            'collectionName' => $collectionName,
            'vector' => $queryVector,
            'limit' => $limit,
            'outputFields' => ['*'] // Request all fields including dynamic fields
        ];

        try {
            $response = $this->client->post("https://{$this->clusterEndpoint}/v1/vector/search", [
                'headers' => $this->getHeaders(),
                'json' => $searchQuery
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            
            if ($result['code'] === 200) {
                $matches = [];
                foreach ($result['data'] as $match) {
                    // Handle both old metadata format and new dynamic fields format
                    $matchData = [
                        'id' => $match['id'] ?? null,
                        'score' => $match['distance'] ?? 0,
                        'distance' => $match['distance'] ?? 0,
                    ];

                    // Add all dynamic fields directly to match data
                    foreach ($match as $key => $value) {
                        if (!in_array($key, ['id', 'distance', 'vector'])) {
                            $matchData[$key] = $value;
                        }
                    }

                    // Legacy metadata support
                    if (isset($match['metadata']) && is_string($match['metadata'])) {
                        $metadata = json_decode($match['metadata'], true);
                        if ($metadata) {
                            $matchData['metadata'] = $metadata;
                        }
                    }

                    $matches[] = $matchData;
                }
                return [
                    'success' => true,
                    'matches' => $matches
                ];
            }

            return [
                'success' => false,
                'error' => $result['message'] ?? 'Search failed'
            ];

        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Xóa collection
     */
    public function dropCollection($collectionName)
    {
        try {
            $response = $this->client->post("https://{$this->clusterEndpoint}/v1/vector/collections/drop", [
                'headers' => $this->getHeaders(),
                'json' => ['collectionName' => $collectionName]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            return $result['code'] === 200;

        } catch (RequestException $e) {
            return false;
        }
    }



    /**
     * Lấy thống kê collection
     */
    public function getCollectionStats($collectionName = null)
    {
        $collectionName = $collectionName ?: $this->defaultCollection;
        
        try {
            $response = $this->client->get("https://{$this->clusterEndpoint}/v1/vector/collections/describe", [
                'headers' => $this->getHeaders(),
                'query' => ['collectionName' => $collectionName]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            return $result['code'] === 200 ? $result['data'] : null;

        } catch (RequestException $e) {
            return null;
        }
    }

    /**
     * Lấy headers cho API requests
     */
    private function getHeaders()
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ];
    }

    /**
     * Query vectors với filter (không cần vector tương tự)
     */
    public function queryVectors($collectionName, $filterExpression = null, $limit = 100, $outputFields = ['*'])
    {
        try {
            // Sử dụng search với vector dummy để lấy tất cả records
            $dummyVector = array_fill(0, 1536, 0.0); // Vector dummy với 1536 dimensions

            $searchQuery = [
                'collectionName' => $collectionName,
                'vector' => $dummyVector,
                'limit' => $limit,
                'outputFields' => $outputFields
            ];

            // Thêm filter nếu có
            if ($filterExpression) {
                $searchQuery['filter'] = $filterExpression;
            }

            $response = $this->client->post("https://{$this->clusterEndpoint}/v1/vector/search", [
                'headers' => $this->getHeaders(),
                'json' => $searchQuery
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($result['code'] === 200) {
                return [
                    'success' => true,
                    'data' => $result['data'] ?? [],
                    'count' => count($result['data'] ?? [])
                ];
            }

            return [
                'success' => false,
                'error' => $result['message'] ?? 'Query failed',
                'data' => []
            ];

        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Upsert vectors (insert hoặc update nếu đã tồn tại)
     */
    public function upsertVectors($vectors, $collectionName = null)
    {
        $collectionName = $collectionName ?: $this->defaultCollection;

        $upsertData = [
            'collectionName' => $collectionName,
            'data' => $vectors
        ];

        try {
            $response = $this->client->post("https://{$this->clusterEndpoint}/v2/vectordb/entities/upsert", [
                'headers' => $this->getHeaders(),
                'json' => $upsertData
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            return [
                'success' => $result['code'] === 0, // v2 API uses code 0 for success
                'upsertCount' => $result['data']['upsertCount'] ?? 0,
                'result' => $result
            ];

        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Xóa vectors theo filter expression
     */
    public function deleteByFilter($filter, $collectionName = null)
    {
        $collectionName = $collectionName ?: $this->defaultCollection;

        $deleteData = [
            'collectionName' => $collectionName,
            'filter' => $filter
        ];

        try {
            $response = $this->client->post("https://{$this->clusterEndpoint}/v1/vector/delete", [
                'headers' => $this->getHeaders(),
                'json' => $deleteData
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            return [
                'success' => $result['code'] === 200,
                'deleteCount' => $result['data']['deleteCount'] ?? 0,
                'result' => $result
            ];

        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Search vectors sử dụng API v2 (chính thức của Zilliz)
     */
    public function searchV2($queryVector, $limit = 10, $collectionName = null, $outputFields = ['*'])
    {
        $collectionName = $collectionName ?: $this->defaultCollection;

        $searchData = [
            'collectionName' => $collectionName,
            'data' => [$queryVector], // API v2 cần array of vectors
            'limit' => $limit,
            'outputFields' => $outputFields
        ];

        try {
            $response = $this->client->post("https://{$this->clusterEndpoint}/v2/vectordb/entities/search", [
                'headers' => $this->getHeaders(),
                'json' => $searchData
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($result['code'] === 0) { // v2 API uses code 0 for success
                return [
                    'success' => true,
                    'data' => $result['data'] ?? []
                ];
            }

            return [
                'success' => false,
                'error' => $result['message'] ?? 'Search failed'
            ];

        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Semantic search với text query sử dụng API v2
     */
    public function semanticSearchV2($queryText, $limit = 10, $collectionName = null)
    {
        try {
            // Tạo embedding cho query text
            $openAIService = new OpenAIService();
            $embeddingResult = $openAIService->createEmbedding($queryText);

            if (!$embeddingResult['success']) {
                return [
                    'success' => false,
                    'error' => 'Failed to create embedding for query: ' . ($embeddingResult['error'] ?? 'Unknown error')
                ];
            }

            // Search với embedding
            return $this->searchV2($embeddingResult['embedding'], $limit, $collectionName);

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Kiểm tra kết nối
     */
    public function testConnection()
    {
        $collections = $this->listCollections();
        return !empty($collections) || is_array($collections);
    }
}
