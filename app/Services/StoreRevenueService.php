<?php

namespace App\Services;

use App\Models\Store;
use App\Models\TiktokSettlement;
use App\Models\SupplierOrder;
use Illuminate\Support\Facades\DB;
use App\Enums\SupplierOrderStatus;

class StoreRevenueService
{
    public function getStoreRevenueSummary(Store $store, $startDate = null, $endDate = null)
    {
        $query = TiktokSettlement::where('store_id', $store->id);
        
        if ($startDate && $endDate) {
            $query->whereBetween('statement_time', [$startDate, $endDate]);
        }

        $settlements = $query->with(['supplierOrders' => function ($query) {
            $query->whereNotIn('status', [
                SupplierOrderStatus::Cancelled->value,
                SupplierOrderStatus::Refunded->value
            ]);
        }])->get();

        $summary = [
            'total_revenue' => 0,
            'total_cost' => 0,
            'total_profit' => 0,
            'total_orders' => 0,
            'paid_orders' => 0,
            'pending_orders' => 0,
            'average_profit_per_order' => 0,
            'settlements' => [],
        ];

        foreach ($settlements as $settlement) {
            $orderCosts = $settlement->supplierOrders->sum('base_cost');
            $profit = $settlement->revenue_amount - $orderCosts;

            $summary['total_revenue'] += $settlement->revenue_amount;
            $summary['total_cost'] += $orderCosts;
            $summary['total_profit'] += $profit;
            $summary['total_orders']++;

            if ($settlement->supplierOrders->where('paid', true)->count() > 0) {
                $summary['paid_orders']++;
            } else {
                $summary['pending_orders']++;
            }

            $summary['settlements'][] = [
                'settlement_id' => $settlement->id,
                'order_number' => $settlement->tiktok_order_id,
                'statement_time' => $settlement->statement_time,
                'revenue' => $settlement->revenue_amount,
                'costs' => $orderCosts,
                'profit' => $profit,
                'supplier_orders' => $settlement->supplierOrders->map(function ($order) {
                    return [
                        'supplier_order_id' => $order->supplier_order_id,
                        'supplier_name' => $order->supplier->name,
                        'cost' => $order->base_cost,
                        'status' => $order->status,
                        'paid' => $order->paid,
                        'payment_date' => $order->payment_date,
                    ];
                }),
            ];
        }

        // Tính trung bình lợi nhuận trên mỗi đơn
        if ($summary['total_orders'] > 0) {
            $summary['average_profit_per_order'] = $summary['total_profit'] / $summary['total_orders'];
        }

        return $summary;
    }

    public function getStoreProfitTrends(Store $store, $period = 'monthly')
    {
        $groupBy = match($period) {
            'daily' => 'DATE(statement_time)',
            'weekly' => 'YEARWEEK(statement_time)',
            'monthly' => 'DATE_FORMAT(statement_time, "%Y-%m")',
            default => 'DATE_FORMAT(statement_time, "%Y-%m")'
        };

        return DB::table('tiktok_settlements as ts')
            ->join('supplier_orders as so', 'ts.order_id', '=', 'so.order_id')
            ->where('ts.store_id', $store->id)
            ->whereNotIn('so.status', [
                SupplierOrderStatus::Cancelled->value,
                SupplierOrderStatus::Refunded->value
            ])
            ->select(
                DB::raw("{$groupBy} as period"),
                DB::raw('SUM(ts.revenue_amount) as total_revenue'),
                DB::raw('SUM(so.base_cost) as total_cost'),
                DB::raw('SUM(ts.revenue_amount - so.base_cost) as total_profit'),
                DB::raw('COUNT(DISTINCT ts.id) as order_count')
            )
            ->groupBy(DB::raw($groupBy))
            ->orderBy(DB::raw($groupBy))
            ->get();
    }

    public function getUnpaidSupplierOrders(Store $store)
    {
        return SupplierOrder::where('store_id', $store->id)
            ->where('paid', false)
            ->whereNotIn('status', [
                SupplierOrderStatus::Cancelled->value,
                SupplierOrderStatus::Refunded->value
            ])
            ->with(['supplier', 'order'])
            ->get()
            ->groupBy('supplier_id')
            ->map(function ($orders) {
                return [
                    'supplier_name' => $orders->first()->supplier->name,
                    'total_amount' => $orders->sum('base_cost'),
                    'order_count' => $orders->count(),
                    'orders' => $orders->map(function ($order) {
                        return [
                            'order_code' => $order->order_code,
                            'supplier_order_id' => $order->supplier_order_id,
                            'amount' => $order->base_cost,
                            'status' => $order->status,
                            'created_at' => $order->created_at
                        ];
                    })
                ];
            });
    }

    public function getStoreMetrics(Store $store)
    {
        return [
            'revenue_metrics' => $this->getStoreRevenueSummary($store),
            'profit_trends' => $this->getStoreProfitTrends($store),
            'unpaid_orders' => $this->getUnpaidSupplierOrders($store),
            'performance_indicators' => [
                'profit_margin' => $this->calculateProfitMargin($store),
                'average_order_value' => $this->calculateAverageOrderValue($store),
                'payment_efficiency' => $this->calculatePaymentEfficiency($store)
            ]
        ];
    }

    public function calculateProfitMargin(Store $store)
    {
        $summary = $this->getStoreRevenueSummary($store);
        return $summary['total_revenue'] > 0 
            ? ($summary['total_profit'] / $summary['total_revenue']) * 100 
            : 0;
    }

    public function calculateAverageOrderValue(Store $store)
    {
        $summary = $this->getStoreRevenueSummary($store);
        return $summary['total_orders'] > 0 
            ? $summary['total_revenue'] / $summary['total_orders']
            : 0;
    }

    public function calculatePaymentEfficiency(Store $store)
    {
        $summary = $this->getStoreRevenueSummary($store);
        return $summary['total_orders'] > 0
            ? ($summary['paid_orders'] / $summary['total_orders']) * 100
            : 0;
    }
} 