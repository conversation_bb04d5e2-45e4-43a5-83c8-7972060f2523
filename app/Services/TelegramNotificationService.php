<?php

namespace App\Services;

use App\Enums\TiktokShopStatus;
use Telegram\Bot\Laravel\Facades\Telegram;
use Illuminate\Support\Facades\Log;

class TelegramNotificationService
{
    protected $chatIds;
    protected $fulfillmentTopicId = 21;
    protected $newOrderTopicId = 26;
    protected $suspendedProductTopicId = 135;
    protected $bankStatusTopicId = 529;
    protected $customThreadId = 856;
    public function __construct()
    {
        $this->chatIds = config('telegram.chats');
    }

    public function sendFulfillmentNotification($message)
    {
        return $this->sendTopicMessage($this->chatIds['order'], $this->fulfillmentTopicId, $message);
    }
    public function sendNewOrderNotification($message)
    {
        return $this->sendTopicMessage($this->chatIds['order'], $this->newOrderTopicId, $message);
    }
    public function sendOrderNotification($message)
    {
        return $this->sendMessage($this->chatIds['order'], $message);
    }
    public function sendSuspendedProductNotification($message)
    {
        return $this->sendTopicMessage($this->chatIds['order'], $this->suspendedProductTopicId, $message);
    }
    public function sendBackupNotification($message)
    {
        return $this->sendTopicMessage($this->chatIds['code'], 2, $message);
    }
    public function sendBankStatusSummary($message)
    {
        try {
            return $this->sendTopicMessage(
                $this->chatIds['order'],
                $this->bankStatusTopicId,
                $message
            );
        } catch (\Exception $e) {
            Log::error('Bank Status Notification Error: ' . $e->getMessage());
            return false;
        }
    }

    public function sendCustomThreadNotification($message)
    {
        return $this->sendTopicMessage($this->chatIds['order'], $this->customThreadId, $message);
    }

    public function sendFulfillmentVariantCheckNotification($message)
    {
        // Gửi message đến chat riêng cho fulfillment check
        return $this->sendFulfillmentCheckMessage($message);
    }

    public function sendFulfillmentCheckMessage($message)
    {
        try {
            // Sử dụng bot riêng cho fulfillment checker
            $fulfillmentBot = new \Telegram\Bot\Api(config('telegram.bots.fulfillment_bot.token'));

            $fulfillmentBot->sendMessage([
                'chat_id' => $this->chatIds['fulfillment_check'],
                'text' => $message,
                'parse_mode' => 'HTML',
            ]);
            return true;
        } catch (\Exception $e) {
            Log::error('Fulfillment Telegram Notification Error: ' . $e->getMessage());
            return false;
        }
    }

    public function formatSuspendedProductNotification($suspendedProduct)
    {
        return "
<b>🚫 New Suspended Product</b>

<b>Product Details:</b>
Name: <code>{$suspendedProduct->product_name}</code>
Store: {$suspendedProduct->store->name}
Owner: {$suspendedProduct->owner->name}

<a href='" . route('filament.app.resources.suspended-products.index', ['tableSearch' => $suspendedProduct->id]) . "'>View Suspended Product</a>
        ";
    }
    public function sendSystemNotification($message)
    {
        return $this->sendMessage($this->chatIds['system'], $message);
    }

    public function sendCodeNotification($message)
    {
        return $this->sendMessage($this->chatIds['code'], $message);
    }
    protected function sendTopicMessage($chatId, $topicId, $message)
    {
        try {
            Telegram::sendMessage([
                'chat_id' => $chatId,
                'text' => $message,
                'parse_mode' => 'HTML',
                'message_thread_id' => $topicId
            ]);
            return true;
        } catch (\Exception $e) {
            Log::error("Telegram Topic Notification Error: " . $e->getMessage());
            return false;
        }
    }
    protected function sendMessage($chatId, $message)
    {
        try {
            Telegram::sendMessage([
                'chat_id' => $chatId,
                'text' => $message,
                'parse_mode' => 'HTML',
            ]);
            return true;
        } catch (\Exception $e) {
            Log::error('Telegram Notification Error: ' . $e->getMessage());
            return false;
        }
    }

    public function formatOrderNotification($order)
    {
        $storeName = $order->store->name ?? 'Unknown Store';
        $customerName = $order->shipping_first_name . ' ' . $order->shipping_last_name;
        $orderItems = $order->orderItems->map(function ($item) {
            return "- {$item->product->name} (x{$item->quantity})";
        })->join("\n");

        return "
<b>🛍 New Order Received!</b>

<b>Order Details:</b>
Order Number: <code>{$order->order_number}</code>
Store: {$storeName}
Seller: {$order->store->owner->name}
Total: $" . number_format($order->total, 2) . "

<b>Items:</b>
{$orderItems}

<b>Shipping Address:</b>
{$order->shipping_address_line1}
{$order->shipping_city}, {$order->shipping_region} {$order->shipping_zip}
{$order->shipping_country}

<a href='" . route('filament.app.resources.orders.index', ['activeTab' => 'all', 'tableSearch' => $order->order_number]) . "'>View Order</a>
        ";
    }
}
