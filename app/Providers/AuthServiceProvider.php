<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use App\Policies\ExceptionPolicy;
use App\Policies\FeatureRequestPolicy;
use App\Models\FeatureRequest;
use <PERSON>zhanSalleh\FilamentExceptions\Models\Exception;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Exception::class => ExceptionPolicy::class,
        FeatureRequest::class => FeatureRequestPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        //
    }
}
