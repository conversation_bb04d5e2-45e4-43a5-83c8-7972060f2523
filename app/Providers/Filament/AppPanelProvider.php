<?php

namespace App\Providers\Filament;




use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Filament\Pages\Auth\Login;
use App\Filament\Pages\Dashboard;
use App\Filament\App\Pages\ContactSupport;
use App\Livewire\MyCustomProfileComponent;
use Filament\FontProviders\GoogleFontProvider;
use Filament\Forms\Components\FileUpload;
use Filament\MinimalTheme;
use Filament\Navigation\NavigationGroup;
use Filament\Pages\Auth\EditProfile;
use Filament\Support\Enums\MaxWidth;
use Filament\View\PanelsRenderHook;
use Jeffgreco13\FilamentBreezy\BreezyCore;


class AppPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {

        return $panel

            ->darkMode(true)
        ->viteTheme('resources/css/filament/app/theme.css')
            ->navigationGroups([


                NavigationGroup::make('Analytics')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Analytics')
                    ->icon('heroicon-o-chart-bar'),

                NavigationGroup::make('Quality Control')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Quality Control')
                    ->icon('heroicon-o-shield-check'),

                NavigationGroup::make('tools')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Tools')
                    ->icon('heroicon-o-wrench'),

                NavigationGroup::make('seller_management')
                ->collapsed(true)
                ->collapsible(true)
                ->label('Seller Management')
                ->icon('heroicon-o-user-group'),


                NavigationGroup::make('Designer Management')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Designer Management')
                    ->icon('heroicon-o-paint-brush'),

                NavigationGroup::make('Leader Management')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Leader Management')
                    ->icon('heroicon-o-presentation-chart-bar'),

                NavigationGroup::make('store_manager')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Store Manager')
                    ->icon('heroicon-o-building-storefront'),
                NavigationGroup::make('products')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Products')
                    ->icon('heroicon-o-calculator'),
                NavigationGroup::make('order_transactions')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Order & Supplier')
                    ->icon('heroicon-o-shopping-cart'),
                 NavigationGroup::make('Design & Media')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Design & Media')
                    ->icon('heroicon-o-paint-brush'), // Biểu tượng sơn màu
                NavigationGroup::make('ideas')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Ideas')
                    ->icon('heroicon-o-light-bulb'),
                NavigationGroup::make('tiktok_shop')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('TikTok Shop')
                    ->icon('heroicon-o-play'),
                NavigationGroup::make('finance_management')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Finance')
                    ->icon('heroicon-o-banknotes'),

                NavigationGroup::make('mockup_tools')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Mockup Tools')
                    ->icon('heroicon-o-squares-2x2'),

       

                NavigationGroup::make('auto_bot')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Auto Bot')
                    ->icon('heroicon-o-cog'), // Biểu tượng bánh răng

                NavigationGroup::make('spy_idea')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Spy Idea')
                    ->icon('heroicon-o-light-bulb'),

                NavigationGroup::make('resources')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Resources')
                    ->icon('heroicon-o-folder'), // Biểu tượng thư mục

           

                NavigationGroup::make('Media Manager')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Media Manager')
                    ->icon('heroicon-o-photo'),

                NavigationGroup::make('Nội dung')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Nội dung')
                    ->icon('heroicon-o-document-text'),

                NavigationGroup::make('Communication')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Communication')
                    ->icon('heroicon-o-chat-bubble-left-right'),

                NavigationGroup::make('Hệ thống')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('Hệ thống')
                    ->icon('heroicon-o-cog-6-tooth'),


                NavigationGroup::make('AI Management')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('AI Management')
                    ->icon('heroicon-o-cpu-chip'),

                NavigationGroup::make('TikTok')
                    ->collapsed(true)
                    ->collapsible(true)
                    ->label('TikTok')
                    ->icon('heroicon-o-musical-note'),
            ])


            ->breadcrumbs(true)

            ->sidebarCollapsibleOnDesktop()
            ->default()
            ->id('app')
            ->path('app')
            ->brandLogo(fn() => view('filament.brand'))->brandLogoHeight(35)
            ->login(Login::class)
            ->registration(false)

            ->plugins([
                BreezyCore::make()
                    ->myProfile(
                        shouldRegisterUserMenu: true, // Sets the 'account' link in the panel User Menu (default = true)
                        shouldRegisterNavigation: false, // Adds a main navigation item for the My Profile page (default = false)
                        // navigationGroup: 'Settings', // Sets the navigation group for the My Profile page (default = null)
                        hasAvatars: false, // Disable default avatar upload
                        slug: 'my-profile' // Sets the slug for the profile page (default = 'my-profile')
                    )
                    ->enableTwoFactorAuthentication(false) // Disable 2FA component
                    ->enableSanctumTokens(false) // Disable API tokens component
                    ->myProfileComponents([
                        'custom_profile' => MyCustomProfileComponent::class,
                    ])
                    // Remove default components
                    ->withoutMyProfileComponents([
                        'personal_info',
                        'update_password',
                        'two_factor',
                        'sanctum_tokens'
                    ]),
                MinimalTheme::make(),
            ])

            ->colors([
                'danger' => Color::Red,
                'gray' => Color::Gray,
                'info' => Color::Blue,
                'primary' => Color::Rose, // Màu xanh dương đậm, phù hợp với cả chế độ sáng và tối
                'lime' => Color::Lime,
                'success' => Color::Emerald,

                'warning' => Color::Amber, // Warm color suitable for Fire element
                'green' => Color::Emerald,
                'yellow' => Color::Yellow,
                'red' => Color::Amber,
                'amber' => Color::Amber,
                'orange' => Color::Orange,
                'purple' => Color::Purple,
                'blue' => Color::Blue,
            ])

            ->discoverResources(in: app_path('Filament/App/Resources'), for: 'App\\Filament\\App\\Resources')
            ->discoverPages(in: app_path('Filament/App/Pages'), for: 'App\\Filament\\App\\Pages')
            ->pages([
                Dashboard::class,
                ContactSupport::class,
            ])
            ->discoverWidgets(in: app_path('Filament/App/Widgets'), for: 'App\\Filament\\App\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,

            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->renderHook(
                PanelsRenderHook::BODY_START,
                fn() => view('components.flash-message')
            )
            ->renderHook(
                PanelsRenderHook::BODY_END,
                fn() => view('components.chatbot')
            )
            ->renderHook(
                PanelsRenderHook::TOPBAR_END,
                fn() => view('components.support-button')
            )
            ->databaseNotifications()
            ->databaseNotificationsPolling('30s')
            ->maxContentWidth(MaxWidth::Full)
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
