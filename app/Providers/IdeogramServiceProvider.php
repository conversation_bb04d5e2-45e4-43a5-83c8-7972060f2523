<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\IdeogramService;

class IdeogramServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('ideogram', function ($app) {
            // Sử dụng account tốt nhất có sẵn từ database
            return new IdeogramService();
        });

        $this->app->bind(IdeogramService::class, function ($app) {
            // Mỗi lần bind sẽ tạo instance mới với account tốt nhất
            return new IdeogramService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish config file
        $this->publishes([
            __DIR__.'/../../config/ideogram.php' => config_path('ideogram.php'),
        ], 'ideogram-config');

        // Load config
        $this->mergeConfigFrom(
            __DIR__.'/../../config/ideogram.php', 'ideogram'
        );
    }
}
