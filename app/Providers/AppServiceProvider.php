<?php

namespace App\Providers;

use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\ServiceProvider;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Opcodes\LogViewer\Facades\LogViewer;
use App\Models\MediaRequest;
use App\Services\TelegramBotManagementService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        if ($this->app->isLocal()) {
            $this->app->register(\Barryvdh\LaravelIdeHelper\IdeHelperServiceProvider::class);
        }

        // Bind TelegramBotManagementService
        $this->app->bind(TelegramBotManagementService::class, function ($app) {
            return new TelegramBotManagementService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        LogViewer::auth(function ($request) {

            return $request->user()
                && (
                    in_array($request->user()->email, [
                        '<EMAIL>',
                    ])
                    || $request->user()->hasRole('Developer')
                );
        });
        Model::unguard();
        Table::configureUsing(function (Table $table): void {
            $table
                
               // ->striped()
                ->defaultSort('created_at', 'desc')
                ->filtersLayout(FiltersLayout::AboveContent)
                ->filtersTriggerAction(
                    fn (Action $action) => $action
                        ->button()
                        ->label('Filter')->slideOver(),
                )
                ->paginationPageOptions([15,25, 50]);
        });

    }
}
