<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use App\Models\MediaRequest;

class MediaRequestStatusUpdated extends Notification
{
    use Queueable;

    protected $mediaRequest;

    public function __construct(MediaRequest $mediaRequest)
    {
        $this->mediaRequest = $mediaRequest;
    }

    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Media Request Status Updated')
            ->line("The status of your media request '{$this->mediaRequest->title}' has been updated to {$this->mediaRequest->status}.")
            ->action('View Request', url("/admin/media-requests/{$this->mediaRequest->id}"))
            ->line('Thank you for using our application!');
    }

    public function toArray($notifiable): array
    {
        return [
            'media_request_id' => $this->mediaRequest->id,
            'title' => $this->mediaRequest->title,
            'status' => $this->mediaRequest->status,
        ];
    }
} 