<?php

namespace App\Console;

use App\Services\TelegramNotificationService;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * The commands to register.
     *
     * @var array
     */
    protected $commands = [
        \App\Console\Commands\RunAllScheduledCommands::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Auto-fulfill orders every 5 minutes
        $schedule->command('orders:fulfill-auto')->everyFiveMinutes()->withoutOverlapping(360);

        // High-frequency tasks
        // $schedule->command('fetch:merchintel')->everyMinute()->runInBackground();
        // $schedule->command('update:approved-stores')->everyMinute()->runInBackground();
        // $schedule->command('fetch:tiktok-products')->everyMinute()->runInBackground();

        // Medium-frequency tasks
        // $schedule->command('orders:sync-settlements')
        //     ->hourly()
        //     ->withoutOverlapping()
        //     ->runInBackground();
        // $schedule->command('tasks:create-product-upload-tasks')
        //     ->everyFiveMinutes()
        //     ->withoutOverlapping()
        //     ->runInBackground();
        // $schedule->command('products:upload-to-tiktok')->everyFiveMinutes()->withoutOverlapping()->runInBackground();
        $schedule->command('stores:sync-products')->everyFourHours()->withoutOverlapping();
        // Lower-frequency tasks
        $schedule->command('orders:update-status')->everyTwoHours()->withoutOverlapping();
        $schedule->command('stores:sync-orders')->everyFifteenMinutes()->withoutOverlapping();

        // Fulfillment Variant Checker - chạy mỗi 10 phút để kiểm tra các đơn hàng đã fulfill
        $schedule->command('fulfillment:check-variants --send-telegram')
            ->everyTenMinutes()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/fulfillment-variant-check.log'));
            
       // $schedule->command('smsnotes:cleanup')->everyThirtyMinutes()->withoutOverlapping()->runInBackground();
        $schedule->command('proxy:check')->everyFifteenMinutes()->withoutOverlapping();
        // Hourly and daily tasks


        $schedule->command('stores:sync-payments')->daily()->withoutOverlapping();

        // Maintenance tasks
        $schedule->command('log:clear')->days(3);
        $schedule->command('stores:check-bank-status')->dailyAt('09:00');

        // Flash Sale Scheduling
        // $schedule->command('flash-sales:sync all')      // Sync all flash sales
        //     ->everyMinute();

        // $schedule->command('flash-sales:sync update')   // Update existing flash sales
        //     ->everyFifteenMinutes();

        // $schedule->command('flash-sales:sync product')->everyFifteenMinutes();

        // $schedule->command('flash-sales:sync duplicate') // Handle flash sale duplication
        //     ->cron('0 */20 * * *');

        $telegramService = app(TelegramNotificationService::class);
        $schedule->command('backup:clean')
            ->daily()
            ->at('01:00')
            ->timezone('Asia/Ho_Chi_Minh')
            ->onFailure(function () use ($telegramService) {
                try {
                    $telegramService->sendBackupNotification("❌ <b>Backup Clean Failed</b>\n\nTime: " . now()->format('Y-m-d H:i:s'));
                } catch (\Exception $e) {
                    Log::error('Failed to send backup clean failure notification: ' . $e->getMessage());
                }
            })
            ->onSuccess(function () use ($telegramService) {
                try {
                    $telegramService->sendBackupNotification("✅ <b>Backup Clean Completed</b>\n\nTime: " . now()->format('Y-m-d H:i:s'));
                } catch (\Exception $e) {
                    Log::error('Failed to send backup clean success notification: ' . $e->getMessage());
                }
            });

        $schedule->command('backup:run')
            ->daily()
            ->at('01:30')
            ->timezone('Asia/Ho_Chi_Minh')
            ->onFailure(function () use ($telegramService) {
                try {
                    $telegramService->sendBackupNotification("❌ <b>Backup Failed</b>\n\nTime: " . now()->format('Y-m-d H:i:s'));
                } catch (\Exception $e) {
                    Log::error('Failed to send backup failure notification: ' . $e->getMessage());
                }
            })
            ->onSuccess(function () use ($telegramService) {
                try {
                    $telegramService->sendBackupNotification("✅ <b>Backup Completed</b>\n\nTime: " . now()->format('Y-m-d H:i:s'));
                } catch (\Exception $e) {
                    Log::error('Failed to send backup success notification: ' . $e->getMessage());
                }
            });

        // Chạy mỗi phút để kiểm tra và upload sản phẩm pending
        // $schedule->command('products:upload-pending')
        //     ->everyFiveMinutes()
        //     ->withoutOverlapping()
        //     ->runInBackground();

        // Reset số lần sử dụng Product Name Generator vào 0h00 mỗi ngày
        $schedule->command('app:reset-product-name-generator-usage')
                ->dailyAt('00:00')
                ->withoutOverlapping()
                ->appendOutputTo(storage_path('logs/reset-product-name-generator-usage.log'));

        // Hourly job to generate keywords for top products
        $schedule->command('products:generate-keywords')
            ->everyThreeHours()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/products-keywords-schedule.log'));

        // Quét số tiền đang hold tại các shop TikTok mỗi 6 giờ
        $schedule->command('tiktok:scan-hold-amount --min-orders=10 --export')
            ->everySixHours()
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/tiktok-hold-amount-scan.log'));

        // Daily Reports Vector Sync và AI Analysis - chạy tự động lúc 10 PM mỗi ngày
        $schedule->command('vector:sync-daily-reports --auto-summary')
            ->dailyAt('22:00')
            ->timezone('Asia/Ho_Chi_Minh')
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/daily-reports-ai-analysis.log'));

        // TikTok Channels Sync - chạy tự động lúc 10 PM mỗi ngày để đồng bộ tất cả stores
        $schedule->command('tiktok:sync-channels')
            ->dailyAt('22:00')
            ->timezone('Asia/Ho_Chi_Minh')
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/tiktok-channels-sync.log'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
