<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserSetting;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ResetProductNameGeneratorUsage extends Command
{
    /**
     * Tên và chữ ký của lệnh console.
     *
     * @var string
     */
    protected $signature = 'app:reset-product-name-generator-usage';

    /**
     * Mô tả về lệnh console.
     *
     * @var string
     */
    protected $description = 'Reset số lần sử dụng Product Name Generator của tất cả người dùng';

    /**
     * Thực thi lệnh console.
     */
    public function handle()
    {
        $this->info('Bắt đầu reset số lần sử dụng Product Name Generator...');
        Log::info('Bắt đầu reset số lần sử dụng Product Name Generator từ cronjob');
        
        // Khóa cài đặt cho usage
        $usageKey = 'product_name_usage';
        
        // Lấy danh sách tất cả users
        $userCount = User::count();
        $this->info("Tìm thấy {$userCount} người dùng để reset");
        
        // Chuẩn bị dữ liệu mặc định
        $defaultUsageData = [
            'count' => 0,
            'date' => Carbon::today()->toDateString()
        ];
        
        // Lấy danh sách những user đã có cài đặt
        $usersWithSettings = UserSetting::where('key', $usageKey)->get();
        $this->info("Tìm thấy {$usersWithSettings->count()} người dùng có cài đặt usage");
        
        // Reset cho từng user
        $resetCount = 0;
        foreach ($usersWithSettings as $setting) {
            try {
                $setting->value = json_encode($defaultUsageData);
                $setting->save();
                $resetCount++;
            } catch (\Exception $e) {
                Log::error("Lỗi khi reset cho user ID {$setting->user_id}: " . $e->getMessage());
                $this->error("Lỗi khi reset cho user ID {$setting->user_id}: " . $e->getMessage());
            }
        }
        
        $this->info("Đã reset số lần sử dụng cho {$resetCount} người dùng");
        Log::info("Đã reset số lần sử dụng cho {$resetCount} người dùng", [
            'total_users' => $userCount,
            'users_with_settings' => $usersWithSettings->count(),
            'reset_count' => $resetCount,
            'date' => Carbon::today()->toDateString()
        ]);
        
        return 0;
    }
} 