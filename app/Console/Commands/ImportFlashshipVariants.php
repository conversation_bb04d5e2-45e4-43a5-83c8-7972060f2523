<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use App\Models\SupplierProduct;
use App\Models\Supplier;
use App\Traits\StyleNormalization;

class ImportFlashshipVariants extends Command
{
    use StyleNormalization;

    protected $signature = 'import:flashship-variants {--token= : The Flashship Bearer token}';
    protected $description = 'Import variants from Flashship API';

    private $baseUrl = 'https://seller.flashship.net/admin-api/stock-sku/list-variant';
    private $pageSize = 10;
    private $defaultToken = 'your_default_token_here';
    private $deviceId = 'your_device_id_here';

    public function handle()
    {
        $supplier = Supplier::where('name', 'Flashship')->firstOrFail();
        $this->info("Using supplier: {$supplier->name} (ID: {$supplier->id})");

        $token = $this->option('token') ?? $this->defaultToken;
        $this->info('Starting import from Flashship...');

        $page = 1;
        $totalPages = null;
        $importCount = 0;
        $updateCount = 0;

        do {
            $response = $this->fetchPage($page, $token);
            if (!$response || !isset($response['content'])) {
                $this->error("Failed to fetch page {$page}");
                break;
            }

            if ($totalPages === null) {
                $totalElements = $response['totalElements'];
                $totalPages = ceil($totalElements / $this->pageSize) +1;
                $this->info("Found {$totalElements} variants across {$totalPages} pages");
            }

            $this->processVariants($response['content'], $supplier->id, $importCount, $updateCount);
            $this->info("Processed page {$page} of {$totalPages}");
            $page++;

        } while ($page <= $totalPages);

        $this->info("Import completed: {$importCount} new variants imported, {$updateCount} variants updated");
    }

    private function fetchPage(int $page, string $token)
    {
        try {
            $response = Http::withHeaders([
                'accept' => 'application/json, text/plain, */*',
                'accept-language' => 'vi,en-US;q=0.9,en;q=0.8',
                'access-control-allow-origin' => '*',
                'authorization' => 'Bearer ' . $token,
                'content-type' => 'application/json',
                'device-id' => $this->deviceId,
                'origin' => 'https://seller.flashship.net',
                'priority' => 'u=1, i',
                'referer' => 'https://seller.flashship.net/stock',
                'sec-ch-ua' => '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                'sec-ch-ua-mobile' => '?0',
                'sec-ch-ua-platform' => '"macOS"',
                'sec-fetch-dest' => 'empty',
                'sec-fetch-mode' => 'cors', 
                'sec-fetch-site' => 'same-origin',
                'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ])->post($this->baseUrl, [
                'productType' => null,
                'material' => null,
                'color' => null,
                'stockStatus' => [],
                'page' => $page - 1,
                'pageSize' => $this->pageSize
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            $this->error("API request failed: " . $response->status());
            $this->error("Response: " . $response->body());
            return null;

        } catch (\Exception $e) {
            $this->error("Error fetching page {$page}: " . $e->getMessage());
            return null;
        }
    }

    private function processVariants(array $variants, int $supplierId, &$importCount, &$updateCount)
    {
    
        foreach ($variants as $variant) {
         
            try {
                $skuParts = explode('/', $variant['variantSku']);
                if (count($skuParts) !== 4) {
                    $this->warn("Invalid SKU format: {$variant['variantSku']}");
                    continue;
                }

                $normalizedData = [
                    'supplier_id' => $supplierId,
                    'sku' => $variant['variantSku'],
                    'variant_id' => (string)$variant['variantId'],
                    'style' => $this->normalizeStyle($skuParts[1]),
                    'size' => $this->normalizeSize($skuParts[2]),
                    'color' => strtoupper($this->normalizeColor($skuParts[3])),
                    'active' => true,
                    'tiktok' => true,
                    'price' => 0,
                ];

                $product = SupplierProduct::updateOrCreate(
                    [
                        'supplier_id' => $supplierId,
                        'sku' => $variant['variantSku'],
                    ],
                    $normalizedData
                );

                if ($product->wasRecentlyCreated) {
                    $importCount++;
                    $this->info("Created: {$variant['variantSku']} (Color: {$normalizedData['color']}) variant_id: {$normalizedData['variant_id']}");
                } else {
                    $updateCount++;
                    $this->info("Updated: {$variant['variantSku']} (Color: {$normalizedData['color']}) variant_id: {$normalizedData['variant_id']}");
                }

            } catch (\Exception $e) {
                $this->error("Error processing variant {$variant['variantSku']}: " . $e->getMessage());
            }
        }

        $this->reportUnknownValues();
    }
}