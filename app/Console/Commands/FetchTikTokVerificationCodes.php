<?php

namespace App\Console\Commands;

use App\Models\VerificationCode;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FetchTikTokVerificationCodes extends Command
{
    protected $signature = 'tiktok:fetch-verification-codes {--minutes=30 : Look back period in minutes}';
    protected $description = 'Fetch TikTok verification codes from email';

    public function handle()
    {
        try {
            $this->info('Fetching TikTok verification codes...');
            
            // Gmail IMAP configuration
            $mailbox = '{imap.gmail.com:993/imap/ssl}INBOX';
            $username = config('mail.gmail.username');
            $password = config('mail.gmail.password');

            // Safely extract the domain for logging
            $emailParts = explode('@', $username);
            $domain = isset($emailParts[1]) ? $emailParts[1] : 'unknown';
            
            $this->info("Attempting connection with account: " . substr($username, 0, 3) . "...@" . $domain);
            $this->info("Password length: " . (empty($password) ? "EMPTY" : strlen($password) . " characters"));
            
            // Improved connection options
            $opts = [
                'DISABLE_AUTHENTICATOR' => ['GSSAPI', 'NTLM', 'PLAIN'], // Disable problematic auth methods
                'TIMEOUT' => 60
            ];
            
            // Connect with better error handling
            $this->info("Connecting to IMAP server...");
            imap_errors(); // Clear any previous errors
            $imap = @imap_open(
                $mailbox,
                $username,
                $password,
                OP_READONLY,
                1,
                $opts
            );

            if (!$imap) {
                $lastError = imap_last_error();
                $allErrors = imap_errors();
                $errorMsg = "Cannot connect to mailbox: $lastError";
                
                if ($allErrors) {
                    $errorMsg .= "\nAll errors: " . implode(", ", $allErrors);
                }
                
                throw new \Exception($errorMsg);
            }

            $this->info("Successfully connected to IMAP server!");
            
            // Search criteria
            $lookBackMinutes = $this->option('minutes');
            $since = date('d-M-Y', strtotime("-{$lookBackMinutes} minutes"));
            $searchCriteria = 'FROM "<EMAIL>" SUBJECT "is your verification code" SINCE "' . $since . '"';
            
            $this->info("Searching with criteria: " . $searchCriteria);
            $emails = imap_search($imap, $searchCriteria, SE_UID);

            $count = 0;
            
            if ($emails && is_array($emails)) {
                $this->info("Found " . count($emails) . " matching emails");
                
                // Sort emails in reverse order (newest first)
                rsort($emails);
                
                foreach ($emails as $uid) {
                    $overviews = imap_fetch_overview($imap, $uid, FT_UID);
                    
                    if (empty($overviews) || !isset($overviews[0])) {
                        $this->warn("Could not fetch overview for UID: {$uid}");
                        continue;
                    }
                    
                    $overview = $overviews[0];
                    $messageId = $overview->message_id ?? ('NO_ID_' . $uid);
                    
                    // Check if we already have this message
                    $existing = VerificationCode::where('message_id', $messageId)->first();
                    
                    if ($existing) {
                        $this->info("Skipping already processed message: {$messageId}");
                        continue;
                    }
                    
                    // Safely fetch body with error handling
                    try {
                        $body = imap_fetchbody($imap, $uid, 1, FT_UID);
                        if (!$body) {
                            $this->warn("Empty body for UID: {$uid}");
                            $body = '';
                        }
                    } catch (\Exception $e) {
                        $this->warn("Error fetching body for UID {$uid}: " . $e->getMessage());
                        $body = '';
                    }
                    
                    // Extract verification code using regex
                    $code = '';
                    $subject = isset($overview->subject) ? imap_utf8($overview->subject) : '';
                    
                    if (preg_match('/(\d{4,6})(?:\s+is|\s+as)?\s+your\s+verification\s+code/i', $body, $matches)) {
                        $code = isset($matches[1]) ? $matches[1] : '';
                    } elseif (preg_match('/(\d{4,6})(?:\s+is|\s+as)?\s+your\s+verification\s+code/i', $subject, $matches)) {
                        $code = isset($matches[1]) ? $matches[1] : '';
                    }
                    
                    if (empty($code)) {
                        $this->warn("Could not extract code from message: {$messageId}");
                        continue;
                    }
                    
                    // Get sender email with safer extraction
                    $from = isset($overview->from) ? $overview->from : '<EMAIL>';
                    
                    // Safe parse of received date
                    try {
                        $receivedAt = isset($overview->date) ? Carbon::parse($overview->date) : Carbon::now();
                    } catch (\Exception $e) {
                        $this->warn("Could not parse date for UID {$uid}: " . $e->getMessage());
                        $receivedAt = Carbon::now();
                    }
                    
                    // Save to database
                    VerificationCode::create([
                        'email_to' => $username,
                        'email_from' => $from,
                        'subject' => $subject,
                        'code' => $code,
                        'body_preview' => substr(strip_tags(quoted_printable_decode($body)), 0, 500),
                        'message_id' => $messageId,
                        'received_at' => $receivedAt,
                    ]);
                    
                    $count++;
                    $this->info("Saved verification code: {$code}");
                }
            } else {
                $this->info("No matching emails found");
            }

            // Close the connection
            imap_close($imap);
            
            $this->info("Completed! Processed {$count} new verification codes.");
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('Error fetching verification emails: ' . $e->getMessage());
            Log::error('Error fetching verification emails: ' . $e->getMessage());
            
            // Add stack trace for more detailed debugging
            Log::error('Stack trace: ' . $e->getTraceAsString());
            
            return Command::FAILURE;
        }
    }
}