<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TikTokShop;
use App\Services\Tokapi;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class UpdateApprovedStores extends Command
{
    protected $signature = 'update:approved-stores';
    protected $description = 'Update information for approved TikTok stores if last scanned more than 15 minutes ago';

    protected $tokapi;

    public function __construct(Tokapi $tokapi)
    {
        parent::__construct();
        $this->tokapi = $tokapi;
    }

    public function handle()
    {
        $lockName = 'update-approved-stores-lock';
        $lock = Cache::lock($lockName, 900); // Lock for 15 minutes (900 seconds)

        if ($lock->get()) {
            try {
                $fifteenMinutesAgo = Carbon::now()->subMinutes(15);

                $approvedStores = TikTokShop::where(function ($query) use ($fifteenMinutesAgo) {
                        $query->where('last_scanned_at', '<', $fifteenMinutesAgo)
                              ->orWhereNull('last_scanned_at');
                    })
                    ->get();

                foreach ($approvedStores as $store) {
                    try {
                        // Get detailed shop information
                        $sellerDetails = $this->tokapi->getSellerDetails($store->seller_id);

                        if (!empty($sellerDetails['data']['shop'])) {
                            // Update shop details
                            $store->shop_name = $sellerDetails['data']['shop']['shop_name'] ?? $store->shop_name;
                            $store->logo_url = $sellerDetails['data']['shop']['logo']['url_list'][0] ?? $store->logo_url;
                            $shopRating = $sellerDetails['data']['shop']['shop_rating'] ?? null;
                            if ($shopRating === null || !is_numeric($shopRating)) {
                                $shopRating = 0; // Default value if shop rating is not valid
                            }
                            $store->shop_rating = (float) $shopRating;
                            $store->sold_count = $sellerDetails['data']['shop']['sold_count'] ?? $store->sold_count;
                            $store->on_sell_product_count = $sellerDetails['data']['shop']['on_sell_product_count'] ?? $store->on_sell_product_count;
                            $store->review_count = $sellerDetails['data']['shop']['review_count'] ?? $store->review_count;
                            $store->is_on_holiday = $sellerDetails['data']['shop']['is_on_holiday'] ?? $store->is_on_holiday;
                            $store->display_on_sell_product_count = $sellerDetails['data']['shop']['display_on_sell_product_count'] ?? $store->display_on_sell_product_count;
                            $store->biz_type = $sellerDetails['data']['shop']['biz_type'] ?? $store->biz_type;

                            // Update last scanned time
                            //$store->last_scanned_at = Carbon::now();

                            // Save updated shop and sales data
                            $store->save();
                            $store->updateSalesData($store->sold_count);
                            Log::info("Updated TikTok shop: {$store->shop_name} ({$store->seller_id})");
                        }
                    } catch (\Exception $e) {
                       // Log::error("Error updating TikTok shop {$store->seller_id}: " . $e->getMessage());
                    }
                }
            } catch (\Exception $e) {
                Log::error("Error fetching products for approved stores: " . $e->getMessage());
            } finally {
                // Release the lock
                $lock->release();
            }
        } else {
            Log::info("The update-approved-stores command is already running.");
        }
    }
}
