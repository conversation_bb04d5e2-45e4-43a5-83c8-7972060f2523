<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Store;
use App\Models\TikTokPayment;
use App\Services\Tiktok\TiktokOrderSyncService;
use App\Services\Tiktok\TiktokShopService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncAllPayments extends Command
{
    protected $signature = 'stores:sync-payments {store_id?}';
    protected $description = 'Sync orders from TikTok for all stores';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $storeId = $this->argument('store_id');

        $storesQuery = Store::where('app_partner_id', '!=', '');
        if ($storeId) {
            $storesQuery->where('id', $storeId);
        }
        $stores = $storesQuery->orderBy('id', 'desc')->get();

        $count = count($stores);
        $this->info("Total stores to sync: {$count}");

        foreach ($stores as $store) {
            try {
                DB::beginTransaction();
                
                $this->syncStorePayments($store);

                DB::commit();
                $this->info("Payments synced for store: {$store->id}");
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error("Error syncing payments for store {$store->id}: " . $e->getMessage());
                $this->error("Error syncing payments for store: {$store->id} - " . $e->getMessage());
            }
        }
    }

    private function syncStorePayments(Store $store)
    {
        $TiktokShopService = new TiktokShopService($store);
        $nextPageToken = null;
        $pageCount = 1;

        do {
            $this->info("Fetching page {$pageCount} for store {$store->id}...");
            
            try {
                $response = $TiktokShopService->getPayment($nextPageToken);
                
                if ($this->argument('store_id')) {
                    Log::info("Raw API Response:", [
                        'store_id' => $store->id,
                        'page' => $pageCount,
                        'response' => $response  // Log toàn bộ response để kiểm tra
                    ]);
                }

                if (!isset($response['payments'])) {
                    $this->warn("No payments data found for store {$store->id}");
                    break;
                }

                foreach ($response['payments'] as $paymentData) {
                    try{
                        $this->createOrUpdatePayment($store, $paymentData);
                    }catch(\Exception $e){
                        $this->error("Error processing payment: " . $e->getMessage());
                    }
                }

                // Lấy token cho trang tiếp theo
                $nextPageToken = $response['next_page_token'] ?? null;
                $pageCount++;

                // Thêm delay nhỏ để tránh rate limit
                // if ($nextPageToken) {
                //     usleep(500000); // delay 0.5 giây
                // }

            } catch (\Exception $e) {
                $this->error("Error on page {$pageCount} for store {$store->id}: " . $e->getMessage());
                throw $e;
            }

        } while ($nextPageToken); // Tiếp tục nếu còn next_page_token

        $this->info("Completed syncing {$pageCount} pages for store {$store->id}");
    }

    private function createOrUpdatePayment($store, $paymentData)
    {
        try {
            $this->info("Payment: " . $paymentData['id']);
            
            // Debug timestamps
            if (isset($paymentData['paid_time'])) {
                $paidTimeTimestamp = $paymentData['paid_time'];
                $paidTimeLA = Carbon::createFromTimestamp($paidTimeTimestamp)
                    ->setTimezone('America/Los_Angeles');
                
                $this->info('=== Paid Time Debug ===');
                $this->info("Raw timestamp from API: {$paidTimeTimestamp}");
                $this->info("Los Angeles (UTC-8): " . $paidTimeLA->format('Y-m-d H:i:s P'));
                $this->info('=====================');
            }

            // Lưu vào DB với timezone Los Angeles
            $payment = TikTokPayment::updateOrCreate(
                ['payment_id' => $paymentData['id']],
                [
                    'store_id' => $store->id,
                    'owner_id' => $store->owner_id,
                    'amount' => $paymentData['amount']['value'],
                    'currency' => $paymentData['amount']['currency'],
                    'bank_account' => $paymentData['bank_account'],
                    'create_time' => Carbon::createFromTimestamp($paymentData['create_time'])
                        ->setTimezone('America/Los_Angeles')
                        ->format('Y-m-d H:i:s'),
                    'exchange_rate' => $paymentData['exchange_rate'] ?: 0,
                    'paid_time' => isset($paymentData['paid_time']) 
                        ? Carbon::createFromTimestamp($paymentData['paid_time'])
                            ->setTimezone('America/Los_Angeles')
                            ->format('Y-m-d H:i:s')
                        : null,
                    'payment_amount_before_exchange' => $paymentData['payment_amount_before_exchange']['value'],
                    'reserve_amount' => $paymentData['reserve_amount']['value'],
                    'settlement_amount' => $paymentData['settlement_amount']['value'],
                    'status' => $paymentData['status'],
                ]
            );

            $this->info('Payment processed');
        } catch (\Exception $e) {
            $this->info('Error processing payment: ' . $e->getMessage());
        }
    }
}
