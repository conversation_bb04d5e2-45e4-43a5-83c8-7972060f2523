<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductToUpload;
use App\Jobs\UploadProductToTikTok;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use App\Enums\ProductUploadStatus;

class UploadProductsToTikTok extends Command
{
    protected $signature = 'products:upload-to-tiktok';
    protected $description = 'Upload products to TikTok';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // Sử dụng lock để tránh chạy song song
        $lock = Cache::lock('products:upload-to-tiktok', 300); // Lock trong 10 phút

        if ($lock->get()) {
            try {
                // Lấy các sản phẩm có trạng thái 'pending'
                //$products = ProductToUpload::where('status', ProductUploadStatus::Pending)->get();
                $products = ProductToUpload::where('status', ProductUploadStatus::Pending)
                ->whereHas('task', function ($query) {
                    $query->where('active', true);
                })->get();
                if ($products->isEmpty()) {
                    $this->info('No pending products found.');
                    return;
                }

                $this->info('Total pending products: ' . $products->count());

                // Chia các sản phẩm thành các batch để xử lý song song
                $batchSize = 5;
                $chunks = $products->chunk($batchSize);

                $delayMinutes = 0;

                foreach ($chunks as $chunk) {
                    $this->info('Processing batch of size: ' . count($chunk));

                    foreach ($chunk as $product) {
                        $this->info('Processing product ID: ' . $product->id);

                        // Kiểm tra upload_interval
                        $lastUploadedProduct = ProductToUpload::where('status', ProductUploadStatus::Success)
                            ->where('task_id', $product->task_id)
                            ->orderBy('uploaded_at', 'desc')
                            ->first();

                        if ($lastUploadedProduct) {
                            $lastUploadedTime = Carbon::parse($lastUploadedProduct->uploaded_at);
                            $currentTime = Carbon::now();
                            $intervalMinutes = $currentTime->diffInMinutes($lastUploadedTime);

                            if ($intervalMinutes < $product->task->upload_interval) {
                                $delayMinutes += $product->task->upload_interval;
                            }
                        }

                        // Cập nhật trạng thái của sản phẩm thành "processing"
                        $product->update(['status' => ProductUploadStatus::Processing]);

                        // Dispatch job to queue with delay
                        UploadProductToTikTok::dispatch($product)->delay(now()->addMinutes($delayMinutes));
                        $delayMinutes += $product->task->upload_interval;
                    }
                }

                $this->info('All pending products have been dispatched to the queue.');
            } finally {
                // Giải phóng lock sau khi hoàn thành
                $lock->release();
            }
        } else {
            $this->info('Command is already running.');
        }
    }
}
