<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MonitorSettlementProcessing extends Command
{
    protected $signature = 'settlements:monitor';
    
    public function handle()
    {
        // Check orders bị treo
        $stuckOrders = DB::table('orders')
            ->where('settlement_processing', '<=', now()->subHours(1))
            ->count();
            
        if ($stuckOrders > 0) {
            Log::warning("Found {$stuckOrders} stuck orders in settlement processing");
            
            // Auto cleanup
            DB::table('orders')
                ->where('settlement_processing', '<=', now()->subHours(1))
                ->update(['settlement_processing' => null]);
        }
        
        // Check duplicate settlements
        $duplicates = DB::table('tiktok_settlements')
            ->select('tiktok_order_id')
            ->groupBy('tiktok_order_id')
            ->havingRaw('COUNT(*) > 1')
            ->get();
            
        if ($duplicates->isNotEmpty()) {
            Log::error("Found duplicate settlements for orders: " . $duplicates->pluck('tiktok_order_id')->join(', '));
        }
    }
} 