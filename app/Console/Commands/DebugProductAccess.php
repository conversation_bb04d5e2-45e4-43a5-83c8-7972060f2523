<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class DebugProductAccess extends Command
{
    protected $signature = 'debug:product-access {product_id} {user_id}';
    protected $description = 'Debug product access permissions for a specific user and product';

    public function handle()
    {
        $productId = $this->argument('product_id');
        $userId = $this->argument('user_id');

        // Tìm user
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }

        $this->info("=== USER INFO ===");
        $this->info("User: {$user->name} (ID: {$user->id})");
        $this->info("Email: {$user->email}");
        $this->info("Roles: " . $user->roles->pluck('name')->join(', '));

        // Đăng nhập user để test global scope
        Auth::login($user);

        // Tìm product với global scope
        $this->info("\n=== PRODUCT ACCESS WITH GLOBAL SCOPE ===");
        $productWithScope = Product::find($productId);
        
        if (!$productWithScope) {
            $this->error("Product with ID {$productId} not found or not accessible with global scope");
        } else {
            $this->info("Product found with global scope: {$productWithScope->name}");
            $this->info("Seller ID: {$productWithScope->seller_id}");
            $this->info("Store ID: {$productWithScope->store_id}");
        }

        // Tìm product không có global scope
        $this->info("\n=== PRODUCT INFO WITHOUT GLOBAL SCOPE ===");
        $productWithoutScope = Product::withoutGlobalScope('accessScope')->find($productId);
        
        if (!$productWithoutScope) {
            $this->error("Product with ID {$productId} does not exist in database");
            return;
        }

        $this->info("Product exists: {$productWithoutScope->name}");
        $this->info("Seller ID: {$productWithoutScope->seller_id}");
        $this->info("Store ID: {$productWithoutScope->store_id}");
        
        if ($productWithoutScope->seller) {
            $this->info("Seller: {$productWithoutScope->seller->name}");
        }
        
        if ($productWithoutScope->store) {
            $this->info("Store: {$productWithoutScope->store->name}");
            $this->info("Store Owner ID: {$productWithoutScope->store->owner_id}");
        }

        // Kiểm tra quyền truy cập
        $this->info("\n=== ACCESS CHECK ===");
        $this->info("User ID matches Seller ID: " . ($user->id == $productWithoutScope->seller_id ? 'YES' : 'NO'));
        
        if ($productWithoutScope->store) {
            $this->info("User ID matches Store Owner ID: " . ($user->id == $productWithoutScope->store->owner_id ? 'YES' : 'NO'));
        }

        // Kiểm tra team relationships nếu user là Leader
        if ($user->hasRole('Leader')) {
            $this->info("\n=== LEADER RELATIONSHIPS ===");
            $managedSellers = $user->leaderManagedSellers()->pluck('id')->toArray();
            $this->info("Managed Seller IDs: " . implode(', ', $managedSellers));
            $this->info("Product seller in managed list: " . (in_array($productWithoutScope->seller_id, $managedSellers) ? 'YES' : 'NO'));
        }
    }
}
