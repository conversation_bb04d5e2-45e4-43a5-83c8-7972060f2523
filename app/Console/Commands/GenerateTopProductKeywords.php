<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\ProductStatisticsController;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateTopProductKeywords extends Command
{
    protected $signature = 'products:generate-keywords';
    protected $description = 'Generate keywords for top selling products hourly';

    protected ProductStatisticsController $productStatisticsController;

    public function __construct(ProductStatisticsController $productStatisticsController)
    {
        parent::__construct();
        $this->productStatisticsController = $productStatisticsController;
    }

    public function handle(): int
    {
        $this->info('Starting top product keywords generation...');
        
        try {
            // Call the API method directly
            $response = $this->productStatisticsController->getTopFiveProducts();
            
            // Check if the response was successful
            $responseData = json_decode($response->getContent(), true);
            if ($response->getStatusCode() === 200 && isset($responseData['success']) && $responseData['success']) {
                $keywordCount = $responseData['data']['meta']['count'] ?? 0;
                $this->info("Successfully generated {$keywordCount} keywords for top products");
                Log::info("Scheduled job: Generated {$keywordCount} keywords for top products");
                return self::SUCCESS;
            } else {
                $errorMessage = $responseData['message'] ?? 'Unknown error';
                $this->error("Failed to generate keywords: {$errorMessage}");
                Log::error("Scheduled job failed: {$errorMessage}");
                return self::FAILURE;
            }
        } catch (\Exception $e) {
            $this->error("Exception during keyword generation: {$e->getMessage()}");
            Log::error("Scheduled job exception: {$e->getMessage()}", [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return self::FAILURE;
        }
    }
} 