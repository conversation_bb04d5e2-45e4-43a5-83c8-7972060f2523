<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Order;
use App\Models\ProductVariant;
use App\Livewire\FlashshipForm;
use App\Fullfillment\FlashShip;

class TestRealFlashShipOrder extends Command
{
    protected $signature = 'test:real-flashship-order {order_code?}';
    protected $description = 'Test creating a real FlashShip order to see if API works';

    public function handle()
    {
        $this->info("🧪 TEST TẠO REAL FLASHSHIP ORDER");
        $this->info("===============================\n");

        try {
            // Get order code from argument or find one
            $orderCode = $this->argument('order_code');
            
            if (!$orderCode) {
                // Find an eligible order
                $order = Order::whereIn('store_order_status', ["Processing", "Awaiting Shipment", "Awaiting Collection", "Awaiting"])
                    ->whereDoesntHave('SupplierOrders')
                    ->whereHas('orderItems', function ($query) {
                        $query->whereHas('productVariant', function ($subQuery) {
                            $subQuery->withoutGlobalScope('access')
                                ->where('auto_fulfill', true)
                                ->whereHas('supplier', function ($supplierQuery) {
                                    $supplierQuery->where('name', 'Flashship');
                                });
                        });
                    })
                    ->first();
                
                if (!$order) {
                    $this->error("❌ Không tìm thấy order nào có thể test");
                    return 1;
                }
                
                $orderCode = $order->order_code;
            } else {
                $order = Order::where('order_code', $orderCode)->first();
                if (!$order) {
                    $this->error("❌ Không tìm thấy order: $orderCode");
                    return 1;
                }
            }
            
            $this->info("📋 Testing với order: {$order->order_code}");
            $this->line("   Status: {$order->store_order_status}");
            $this->line("   Items: " . $order->orderItems->count());
            $this->newLine();
            
            // Test 1: Check current SupplierOrders
            $this->info("=== TEST 1: Kiểm tra SupplierOrders hiện tại ===");
            $existingOrders = $order->SupplierOrders()->where('supplier_id', 1)->get();
            $this->line("📊 Existing SupplierOrders: " . $existingOrders->count());
            
            foreach ($existingOrders as $so) {
                $this->line("  🎯 ID: {$so->id} | Supplier Order ID: {$so->supplier_order_id} | Status: {$so->status->value}");
            }
            $this->newLine();
            
            // Test 2: Try FlashshipForm flow
            $this->info("=== TEST 2: Test FlashshipForm flow ===");
            
            try {
                $flashshipForm = new FlashshipForm();
                $flashshipForm->mount($order, 'Flashship');
                
                $this->info("✅ FlashshipForm mounted successfully");
                $this->line("📋 Form data keys: " . implode(', ', array_keys($flashshipForm->formData ?? [])));
                
                // Get the prepared order data without actually submitting
                // Use reflection to access private method
                $reflection = new \ReflectionClass($flashshipForm);
                $method = $reflection->getMethod('prepareOrderData');
                $method->setAccessible(true);
                $orderData = $method->invoke($flashshipForm, false);
                $this->line("📦 Prepared order data:");
                $this->line(json_encode($orderData, JSON_PRETTY_PRINT));
                $this->newLine();
                
                // Test createOrder API call
                $this->info("=== TEST 3: Test FlashShip::createOrder() ===");
                $response = FlashShip::createOrder($orderData);
                
                $this->line("📥 CreateOrder Response:");
                $this->line("Success: " . ($response['success'] ? 'true' : 'false'));
                $this->line("Data: " . json_encode($response['data'], JSON_PRETTY_PRINT));
                $this->line("Message: " . ($response['message'] ?? 'null'));
                $this->newLine();
                
                if ($response['success']) {
                    $this->info("✅ CreateOrder thành công!");
                    
                    // Test getOrder to see if we can retrieve it
                    $this->info("=== TEST 4: Test FlashShip::getOrder() ===");
                    
                    // Try different ways to get the order
                    $getResponse1 = FlashShip::getOrder($order->order_code);
                    $this->line("📥 getOrder(order_code) Response:");
                    $this->line(json_encode($getResponse1, JSON_PRETTY_PRINT));
                    
                    // Try with partner order ID if available
                    if (isset($orderData['partner_order_id'])) {
                        $getResponse2 = FlashShip::getOrderByPartnerOrderId($orderData['partner_order_id']);
                        $this->line("📥 getOrderByPartnerOrderId Response:");
                        $this->line(json_encode($getResponse2, JSON_PRETTY_PRINT));
                    }
                    
                    // Try with supplier order ID if available
                    if (isset($response['data']) && (is_string($response['data']) || is_numeric($response['data']))) {
                        $getResponse3 = FlashShip::getOrder($response['data']);
                        $this->line("📥 getOrder(supplier_order_id) Response:");
                        $this->line(json_encode($getResponse3, JSON_PRETTY_PRINT));
                    }
                    
                } else {
                    $this->warn("⚠️  CreateOrder failed: " . ($response['message'] ?? 'Unknown error'));
                }
                
            } catch (\Exception $e) {
                $this->error("❌ FlashshipForm error: " . $e->getMessage());
                $this->line("Stack trace: " . $e->getTraceAsString());
            }
            
            // Test 5: Check what happens with manual submission
            if ($this->confirm('Bạn có muốn thử submit order thật không? (Sẽ tạo SupplierOrder thật)')) {
                $this->info("=== TEST 5: Manual submission ===");
                
                try {
                    $result = $flashshipForm->confirmSubmit(false); // Don't mark as completed
                    $this->info("✅ Manual submission completed");
                    
                    // Check if SupplierOrder was created
                    $order->refresh();
                    $newSupplierOrders = $order->SupplierOrders()->where('supplier_id', 1)->get();
                    $this->line("📊 SupplierOrders after submission: " . $newSupplierOrders->count());
                    
                    $latestOrder = $newSupplierOrders->sortByDesc('created_at')->first();
                    if ($latestOrder) {
                        $this->line("🎯 Latest SupplierOrder:");
                        $this->line("   ID: {$latestOrder->id}");
                        $this->line("   Supplier Order ID: {$latestOrder->supplier_order_id}");
                        $this->line("   Status: {$latestOrder->status->value}");
                        $this->line("   Data: " . ($latestOrder->data ? 'Has data' : 'No data'));
                    }
                    
                } catch (\Exception $e) {
                    $this->error("❌ Manual submission failed: " . $e->getMessage());
                }
            }
            
            $this->newLine();
            $this->info("🎯 KẾT LUẬN:");
            $this->info("============");
            $this->line("1. ✅ Đã test FlashshipForm flow");
            $this->line("2. ✅ Đã test FlashShip API calls");
            $this->line("3. ✅ Đã kiểm tra response formats");
            $this->warn("4. ⚠️  Cần xem kết quả để hiểu vấn đề");
            
        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
