<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddIndexesForAutoFulfillOptimization extends Command
{
    protected $signature = 'db:add-auto-fulfill-indexes';
    protected $description = 'Thêm các index tối ưu auto-fulfill cho các bảng lớn, thay cho migration';

    public function handle()
    {
        $this->info('🚀 Bắt đầu thêm index tối ưu auto-fulfill...');
        $this->warn('⚠️  Quá trình này có thể mất 5-15 phút tùy theo kích thước dữ liệu');

        $indexes = [
            // suppliers (nhỏ nhất - nhanh nhất)
            ['suppliers', 'suppliers_name_index', 'ALTER TABLE suppliers ADD INDEX suppliers_name_index (name)', 'Tối ưu filter supplier name'],

            // order_items (trung bình)
            ['order_items', 'order_items_product_variant_id_index', 'ALTER TABLE order_items ADD INDEX order_items_product_variant_id_index (product_variant_id)', 'Tối ưu JOIN với product_variants'],

            // orders (lớn)
            ['orders', 'orders_fulfillment_type_index', 'ALTER TABLE orders ADD INDEX orders_fulfillment_type_index (fulfillment_type)', 'Tối ưu filter fulfillment_type'],
            ['orders', 'orders_status_created_at_auto_fulfill', 'ALTER TABLE orders ADD INDEX orders_status_created_at_auto_fulfill (store_order_status, created_at)', 'Tối ưu filter chính: status + date'],
            ['orders', 'orders_fulfillment_created_at', 'ALTER TABLE orders ADD INDEX orders_fulfillment_created_at (fulfillment_type, created_at)', 'Tối ưu TIKTOK + date condition'],

            // product_variants (lớn nhất - chậm nhất)
            ['product_variants', 'product_variants_auto_fulfill_index', 'ALTER TABLE product_variants ADD INDEX product_variants_auto_fulfill_index (auto_fulfill)', 'Tối ưu filter auto_fulfill'],
            ['product_variants', 'product_variants_variant_auto_fulfill', 'ALTER TABLE product_variants ADD INDEX product_variants_variant_auto_fulfill (variant_id, auto_fulfill)', 'Tối ưu JOIN + filter'],
            ['product_variants', 'product_variants_supplier_auto_fulfill', 'ALTER TABLE product_variants ADD INDEX product_variants_supplier_auto_fulfill (supplier_id, auto_fulfill)', 'Tối ưu supplier JOIN + filter'],
        ];

        $totalStartTime = microtime(true);
        $successCount = 0;
        $skipCount = 0;
        $errorCount = 0;

        $this->newLine();
        $this->info("📊 Sẽ tạo " . count($indexes) . " indexes...");
        $this->newLine();

        foreach ($indexes as $i => [$table, $indexName, $sql, $description]) {
            $this->line("🔍 [" . ($i + 1) . "/" . count($indexes) . "] Kiểm tra index <info>{$indexName}</info> trên bảng <info>{$table}</info>");
            $this->line("   📝 Mục đích: {$description}");

            try {
                $exists = collect(DB::select("SHOW INDEX FROM {$table}"))
                    ->pluck('Key_name')
                    ->contains($indexName);

                if ($exists) {
                    $this->info("   ✅ Index đã tồn tại - bỏ qua");
                    $skipCount++;
                } else {
                    $this->warn("   🔨 Đang tạo index (có thể mất vài phút)...");
                    $startTime = microtime(true);

                    DB::statement($sql);

                    $duration = round(microtime(true) - $startTime, 2);
                    $this->info("   ✅ Hoàn thành trong {$duration}s");
                    $successCount++;
                }
            } catch (\Exception $e) {
                $this->error("   ❌ Lỗi: " . $e->getMessage());
                $errorCount++;
            }

            $this->newLine();
        }

        $totalDuration = round(microtime(true) - $totalStartTime, 2);

        $this->info("🎉 HOÀN THÀNH!");
        $this->info("📊 Kết quả:");
        $this->info("   ✅ Tạo mới: {$successCount}");
        $this->info("   ⏭️  Bỏ qua: {$skipCount}");
        $this->info("   ❌ Lỗi: {$errorCount}");
        $this->info("   ⏱️  Tổng thời gian: {$totalDuration}s");

        if ($successCount > 0) {
            $this->info("🚀 Auto-fulfill query sẽ nhanh hơn đáng kể!");
        }
    }
}
