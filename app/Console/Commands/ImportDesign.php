<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportDesign extends Command
{
    protected $signature = 'import:designs {--batch=5 : Number of designs per batch}';
    protected $description = 'Import all available designs in batches from VPS to production server';

    public function handle()
    {
        $this->info('Starting design import process...');

        $batchSize = (int)$this->option('batch');

        $maxCloneId = $this->getMaxCloneIdFromProduction();
        $startId = max(1, $maxCloneId + 1);
        $maxDesignId = $this->getMaxDesignIdFromVps();

        $this->info("Importing designs in batches of $batchSize, starting from ID $startId");

        $totalImported = 0;
        $bar = $this->output->createProgressBar($maxDesignId - $startId + 1);
        $bar->start();

        while ($startId <= $maxDesignId) {
            $designs = $this->getDesignBatchFromVps($startId, $batchSize);
            if ($designs->isEmpty()) {
                break;
            }

            $batchImported = 0;
            foreach ($designs as $design) {
                $designFiles = $this->getDesignFilesFromVps($design->id);
                if ($this->importDesignToProduction($design, $designFiles)) {
                    $batchImported++;
                    $totalImported++;
                }
                $bar->advance();
                $startId = $design->id + 1;
            }

            $this->newLine();
            $this->info("Batch completed: Imported $batchImported/$batchSize designs. Total: $totalImported");

           
        }

        $bar->finish();
        $this->newLine(2);
        $this->info("Import process completed. Total imported: $totalImported designs.");
        $this->info("Next start ID: $startId");
    }

    protected function getMaxCloneIdFromProduction()
    {
        return DB::connection('mysql_production')
            ->table('designs')
            ->max('clone_id') ?? 0;
    }

    protected function getMaxDesignIdFromVps()
    {
        return DB::connection('mysql_vps')
            ->table('design')
            ->max('id');
    }

    protected function getDesignBatchFromVps($startId, $batchSize)
    {
        return DB::connection('mysql_vps')
            ->table('design')
            ->where('id', '>=', $startId)
            ->orderBy('id')
            ->limit($batchSize)
            ->get();
    }

    protected function getDesignFilesFromVps($designId)
    {
        return DB::connection('mysql_vps')
            ->table('design_image')
            ->where('design_id', $designId)
            ->get();
    }

    protected function importDesignToProduction($design, $designFiles)
    {
        try {
            DB::connection('mysql_production')->transaction(function () use ($design, $designFiles) {
                $newDesign = DB::connection('mysql_production')
                    ->table('designs')
                    ->insertGetId([
                        'clone_id' => $design->id,
                        'name' => $design->title,
                        'sub_title' => $design->subtitle,
                        'mockup' => $design->preview,
                        'type' => $design->type,
                        'created_at' => now(),
                        'status' => 'Uploaded'
                    ]);

                foreach ($designFiles as $file) {
                    DB::connection('mysql_production')
                        ->table('design_files')
                        ->insert([
                            'design_id' => $newDesign,
                            'file_url' => $file->src,
                        ]);
                }
            });
            return true;
        } catch (\Exception $e) {
            $this->error("Failed to import design ID {$design->id}: " . $e->getMessage());
            return false;
        }
    }
}
