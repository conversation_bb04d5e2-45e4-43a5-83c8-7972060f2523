<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ZillizVectorService;
use App\Services\OpenAIService;
use App\Services\VectorSync\DailyReportVectorService;
use App\Services\VectorSync\OrderVectorService;
use App\Services\VectorSync\ProductVectorService;
use App\Services\VectorSync\StoreVectorService;
use App\Services\VectorSync\SupplierOrderVectorService;
use App\Services\VectorSync\BusinessSummaryService;
use App\Services\VectorSync\DocumentVectorService;

use App\Services\LarkService;
use App\Services\AiPromptService;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class VectorSyncAllCommand extends Command
{
    protected $signature = 'vector:sync-all {--batch=50} {--dry-run} {--from-date=} {--to-date=} {--save-text} {--auto-summary} {--sync-orders} {--sync-products} {--sync-stores} {--sync-supplier-orders} {--sync-business} {--sync-documents}';
    protected $description = 'Sync ALL data types to Zilliz vector database: Daily Reports, Orders, Products, Stores, Supplier Orders, Business Summaries, Documents';

    /**
     * Laravel Command compatibility methods
     */
    public function writeln($message)
    {
        $this->line($message);
    }

    public function createProgressBar($max = 0)
    {
        return $this->output->createProgressBar($max);
    }

    private $zilliz;
    private $openAI;
    private $dailyReportVectorService;
    private $orderVectorService;
    private $productVectorService;
    private $storeVectorService;
    private $supplierOrderVectorService;
    private $businessSummaryService;
    private $documentVectorService;

    public function __construct()
    {
        parent::__construct();
        $this->zilliz = new ZillizVectorService();
        $this->openAI = new OpenAIService();

        // Initialize all vector services
        $this->dailyReportVectorService = new DailyReportVectorService($this->zilliz, $this->openAI);
        $this->orderVectorService = new OrderVectorService($this->zilliz, $this->openAI);
        $this->productVectorService = new ProductVectorService($this->zilliz, $this->openAI);
        $this->storeVectorService = new StoreVectorService($this->zilliz, $this->openAI);
        $this->supplierOrderVectorService = new SupplierOrderVectorService($this->zilliz, $this->openAI);
        $this->businessSummaryService = new BusinessSummaryService($this->zilliz, $this->openAI);
        $this->documentVectorService = new DocumentVectorService($this->zilliz, $this->openAI);

        // DailyReportSummaryService đã được xóa - sử dụng DailyReportVectorService thay thế
    }

    public function handle()
    {
        $batchSize = $this->option('batch');
        $isDryRun = $this->option('dry-run');
        $fromDate = $this->option('from-date');
        $toDate = $this->option('to-date');
        $saveText = $this->option('save-text');
        $autoSummary = $this->option('auto-summary');

        // Vector sync options
        $syncOrders = $this->option('sync-orders');
        $syncProducts = $this->option('sync-products');
        $syncStores = $this->option('sync-stores');
        $syncSupplierOrders = $this->option('sync-supplier-orders');
        $syncBusiness = $this->option('sync-business');
        $syncDocuments = $this->option('sync-documents');

        $this->info('🚀 Starting comprehensive vector sync...');

        // Determine what to sync
        $syncAll = !($syncOrders || $syncProducts || $syncStores || $syncSupplierOrders || $syncBusiness || $syncDocuments);

        if ($syncAll) {
            $this->info('📊 Syncing ALL data types (no specific flags provided)');
        } else {
            $this->info('📊 Syncing selected data types only');
        }

        if ($isDryRun) {
            $this->warn('🧪 DRY RUN MODE - No vectors will be inserted');
        }

        if ($saveText) {
            $this->info('💾 TEXT BACKUP MODE - All generated texts will be saved to S3');
        }

        if ($autoSummary) {
            $this->info('🤖 AUTO SUMMARY MODE - Will generate AI summaries after sync');
        }

        if ($fromDate || $toDate) {
            $this->info("📅 Date range: {$fromDate} to {$toDate}");
        }

        // 1. Sync Daily Reports (always sync this as it's the main data)
        if ($syncAll || true) { // Always sync daily reports
            $this->info('📊 Syncing Daily Reports...');
            $this->info('📊 Đang đồng bộ Báo cáo Hàng ngày...');

            $allTexts = $this->dailyReportVectorService->syncDailyReports(
                $batchSize,
                $isDryRun,
                $fromDate,
                $toDate,
                $this,
                $saveText
            );

            // Save texts to S3 if option enabled
            if ($saveText && !empty($allTexts)) {
                $s3Url = $this->saveTextsToS3($allTexts, 'daily_reports');
                if ($s3Url) {
                    $this->info("📄 Daily Reports texts saved to S3: {$s3Url}");
                }
            }
        }

        // 2. Sync Orders
        if ($syncAll || $syncOrders) {
            $this->info('🛒 Syncing Orders...');

            try {
                $allTexts = $this->orderVectorService->syncOrders(
                    $batchSize,
                    $isDryRun,
                    $fromDate,
                    $toDate,
                    $this,
                    $saveText
                );

                // Save texts to S3 if option enabled
                if ($saveText && !empty($allTexts)) {
                    $s3Url = $this->saveTextsToS3($allTexts, 'orders');
                    if ($s3Url) {
                        $this->info("📄 Orders texts saved to S3: {$s3Url}");
                    }
                }
            } catch (\Exception $e) {
                $this->error("❌ Orders sync error: " . $e->getMessage());
            }
        }

        // 3. Sync Products (COMMENTED OUT)
        if ($syncAll || $syncProducts) {
            $this->info('📦 Syncing Products...');
            $this->warn('⚠️  Products sync temporarily disabled. Skipping...');

            // TODO: Re-enable when needed
            // try {
            //     $allTexts = $this->productVectorService->syncProducts(
            //         $batchSize,
            //         $isDryRun,
            //         $fromDate,
            //         $toDate,
            //         $this,
            //         $saveText
            //     );
            //
            //     // Save texts to S3 if option enabled
            //     if ($saveText && !empty($allTexts)) {
            //         $s3Url = $this->saveTextsToS3($allTexts, 'products');
            //         if ($s3Url) {
            //             $this->info("📄 Products texts saved to S3: {$s3Url}");
            //         }
            //     }
            // } catch (\Exception $e) {
            //     $this->error("❌ Products sync error: " . $e->getMessage());
            // }
        }

        // 4. Sync Stores
        if ($syncAll || $syncStores) {
            $this->info('🏪 Syncing Stores...');

            try {
                $allTexts = $this->storeVectorService->syncStores(
                    $batchSize,
                    $isDryRun,
                    $fromDate,
                    $toDate,
                    $this,
                    $saveText
                );

                // Save texts to S3 if option enabled
                if ($saveText && !empty($allTexts)) {
                    $s3Url = $this->saveTextsToS3($allTexts, 'stores');
                    if ($s3Url) {
                        $this->info("📄 Stores texts saved to S3: {$s3Url}");
                    }
                }
            } catch (\Exception $e) {
                $this->error("❌ Stores sync error: " . $e->getMessage());
            }
        }

        // 5. Sync Supplier Orders
        if ($syncAll || $syncSupplierOrders) {
            $this->info('📋 Syncing Supplier Orders...');

            try {
                $allTexts = $this->supplierOrderVectorService->syncSupplierOrders(
                    $batchSize,
                    $isDryRun,
                    $fromDate,
                    $toDate,
                    $this,
                    $saveText
                );

                // Save texts to S3 if option enabled
                if ($saveText && !empty($allTexts)) {
                    $s3Url = $this->saveTextsToS3($allTexts, 'supplier_orders');
                    if ($s3Url) {
                        $this->info("📄 Supplier Orders texts saved to S3: {$s3Url}");
                    }
                }
            } catch (\Exception $e) {
                $this->error("❌ Supplier orders sync error: " . $e->getMessage());
            }
        }

        // 6. Sync Business Summaries (TODO: Implement syncBusinessSummaries method)
        if ($syncAll || $syncBusiness) {
            $this->info('📈 Syncing Business Summaries...');
            $this->warn('⚠️  Business Summaries sync not implemented yet. Skipping...');

            // TODO: Implement BusinessSummaryService::syncBusinessSummaries method
            // try {
            //     $allTexts = $this->businessSummaryService->syncBusinessSummaries(
            //         $batchSize,
            //         $isDryRun,
            //         $fromDate,
            //         $toDate,
            //         $this,
            //         $saveText
            //     );
            //
            //     // Save texts to S3 if option enabled
            //     if ($saveText && !empty($allTexts)) {
            //         $s3Url = $this->saveTextsToS3($allTexts, 'business_summaries');
            //         if ($s3Url) {
            //             $this->info("📄 Business Summaries texts saved to S3: {$s3Url}");
            //         }
            //     }
            // } catch (\Exception $e) {
            //     $this->error("❌ Business summaries sync error: " . $e->getMessage());
            // }
        }

        // 7. Sync Documents
        if ($syncAll || $syncDocuments) {
            $this->info('📄 Syncing Documents...');

            try {
                $allTexts = $this->documentVectorService->syncDocuments(
                    $batchSize,
                    $isDryRun,
                    $fromDate,
                    $toDate,
                    $this,
                    $saveText
                );

                // Save texts to S3 if option enabled
                if ($saveText && !empty($allTexts)) {
                    $s3Url = $this->saveTextsToS3($allTexts, 'documents');
                    if ($s3Url) {
                        $this->info("📄 Documents texts saved to S3: {$s3Url}");
                    }
                }
            } catch (\Exception $e) {
                $this->error("❌ Documents sync error: " . $e->getMessage());
            }
        }

        // Auto Summary if option enabled
        if ($autoSummary && !$isDryRun) {
            $this->info('🤖 Starting Auto Summary generation...');
            $this->generateAutoSummaries($fromDate, $toDate);
        }

        $this->info('✅ Comprehensive vector sync completed successfully!');
    }

    /**
     * Generate ONE COMPREHENSIVE SUMMARY for ALL SELLERS in date range
     */
    private function generateAutoSummaries($fromDate, $toDate)
    {
        try {
            // Tạo tóm tắt cho từng ngày trong khoảng thời gian
            $dates = $this->getDateRange($fromDate, $toDate);

            $this->info("📅 Processing " . count($dates) . " date(s): " . implode(', ', $dates));

            $allDatesData = [];
            $totalReports = 0;
            $totalSellers = 0;

            foreach ($dates as $date) {
                $this->info("🗓️ Collecting data for date: {$date}");

                try {
                    // Lấy dữ liệu TẤT CẢ SELLERS cho ngày này (không tạo BI report riêng)
                    $result = $this->summaryService->getAllTodayReports($date);

                    if ($result['success'] && $result['count'] > 0) {
                        $allDatesData[$date] = $result['data'];
                        $totalReports += $result['count'];

                        // Đếm số sellers unique
                        $sellersInDate = array_unique(array_column($result['data'], 'seller_id'));
                        $totalSellers += count($sellersInDate);

                        $this->line("   ✅ {$date}: " . count($sellersInDate) . " sellers, {$result['count']} reports");
                    } else {
                        $this->line("   ⚪ {$date}: No reports");
                    }

                } catch (\Exception $e) {
                    $this->line("   ❌ {$date}: Exception - {$e->getMessage()}");
                }
            }

            if (empty($allDatesData)) {
                $this->warn('⚠️ No data found for the specified date range');
                return;
            }

            // Kiểm tra duplicate reports
            $this->info("🔍 Checking for duplicate reports...");
            $duplicateCount = $this->checkForDuplicates($allDatesData);

            // Tạo MỘT BÁO CÁO TỔNG duy nhất
            $this->info("🤖 Creating comprehensive summary report...");
            $biReportId = $this->createComprehensiveSummary($dates, $allDatesData, $totalReports, $totalSellers);

            if ($biReportId) {
                $this->info("🎯 Comprehensive Summary Created:");
                $this->info("   📊 BI Report ID: {$biReportId}");
                $this->info("   📅 Date range: " . implode(' to ', [min($dates), max($dates)]));
                $this->info("   👥 Total sellers: {$totalSellers}");
                $this->info("   📋 Total reports: {$totalReports}");
            } else {
                $this->error("❌ Failed to create comprehensive summary");
            }

        } catch (\Exception $e) {
            $this->error("❌ Error in generateAutoSummaries: " . $e->getMessage());
        }
    }

    /**
     * Create ONE COMPREHENSIVE SUMMARY with detailed analysis
     */
    private function createComprehensiveSummary($dates, $allDatesData, $totalReports, $totalSellers)
    {
        try {
            // Phân tích dữ liệu chi tiết
            $analysis = $this->analyzeAllData($allDatesData);

            // Tạo prompt cho ChatGPT để tạo báo cáo chi tiết
            $prompt = $this->createComprehensivePrompt($dates, $analysis, $totalReports, $totalSellers);

            // Gọi ChatGPT để tạo báo cáo chi tiết
            $messages = [
                ['role' => 'user', 'content' => $prompt]
            ];
            $summaryResult = $this->openAI->chatCompletion($messages, 'gpt-4o-mini', 3000);

            if (!$summaryResult['success']) {
                $this->error("❌ Failed to generate AI summary: " . $summaryResult['error']);
                return null;
            }

            $comprehensiveSummary = $summaryResult['content'];

            // Tạo title
            $dateRange = count($dates) === 1 ? $dates[0] : min($dates) . ' đến ' . max($dates);
            $title = "Báo cáo Tổng hợp Daily Reports - {$dateRange}";

            // Tạo PDF và upload lên S3
            $pdfResult = $this->generateAndUploadPDF($comprehensiveSummary, $dateRange, $title);

            // Tạo raw embedding data để lưu trữ
            $rawEmbeddingData = $this->prepareRawEmbeddingData($allDatesData);

            // Tạo raw embedding prompt để lưu trữ
            $rawEmbeddingPrompt = $this->prepareRawEmbeddingPrompt($allDatesData);

            // Lưu vào BI Report
            $biReport = \App\Models\BusinessIntelligenceReport::create([
                'title' => $title,
                'period' => count($dates) === 1 ? 'daily' : 'custom',
                'from_date' => min($dates),
                'to_date' => max($dates),
                'analysis_data' => [
                    'type' => 'comprehensive_daily_summary',
                    'date_range' => $dates,
                    'total_sellers' => $totalSellers,
                    'total_reports' => $totalReports,
                    'detailed_analysis' => $analysis,
                    'generated_by' => 'auto_summary_command',
                    'ai_model' => 'gpt-4o-mini'
                ],
                'ai_summary' => $comprehensiveSummary,
                'raw_prompt' => $prompt,
                'raw_embedding_data' => $rawEmbeddingData,
                'raw_embedding_prompt' => $rawEmbeddingPrompt,
                'pdf_url' => $pdfResult['url'] ?? '',
                'pdf_filename' => $pdfResult['filename'] ?? '',
                'total_reports' => $totalReports,
                'total_products' => $analysis['total_products'] ?? 0,
                'total_orders' => $analysis['total_orders'] ?? 0,
                'total_suppliers' => 0,
                'total_stores' => 0,
                'total_revenue' => 0,
                'avg_order_value' => 0,
                'status' => 'completed',
                'error_message' => null,
                'created_by' => 1
            ]);

            return $biReport->id;

        } catch (\Exception $e) {
            $this->error("❌ Error creating comprehensive summary: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Analyze all data to extract insights
     */
    private function analyzeAllData($allDatesData)
    {
        $analysis = [
            'sellers_performance' => [],
            'total_products' => 0,
            'total_videos' => 0,
            'total_orders' => 0,
            'niches' => [],
            'accounts' => [],
            'high_performers' => [],
            'need_attention' => [],
            'trends' => []
        ];

        foreach ($allDatesData as $date => $reports) {
            foreach ($reports as $report) {
                $sellerId = $report['seller_id'];
                $sellerName = $report['seller_name'] ?? "Seller {$sellerId}";

                // Initialize seller if not exists
                if (!isset($analysis['sellers_performance'][$sellerName])) {
                    $analysis['sellers_performance'][$sellerName] = [
                        'seller_id' => $sellerId,
                        'seller_name' => $sellerName,
                        'reports_count' => 0,
                        'activity_level' => $report['seller_activity'] ?? 'Unknown',
                        'summary' => $report['report_summary'] ?? '',
                        'sales_data' => $report['sales_data'] ?? '',
                        'dates' => []
                    ];
                }

                $analysis['sellers_performance'][$sellerName]['reports_count']++;
                $analysis['sellers_performance'][$sellerName]['dates'][] = $date;

                // Cập nhật sales_data nếu có dữ liệu mới
                if (!empty($report['sales_data'])) {
                    $analysis['sellers_performance'][$sellerName]['sales_data'] = $report['sales_data'];
                }

                // Extract metrics directly from sales_data JSON
                $salesData = $report['sales_data'] ?? '';
                $this->extractMetricsFromSalesData($salesData, $analysis);
            }
        }

        // Classify sellers
        foreach ($analysis['sellers_performance'] as $sellerName => $performance) {
            if ($performance['reports_count'] >= 2 && strpos($performance['activity_level'], 'tích cực') !== false) {
                $analysis['high_performers'][] = $sellerName;
            } elseif ($performance['reports_count'] === 1 || strpos($performance['activity_level'], 'thấp') !== false) {
                $analysis['need_attention'][] = $sellerName;
            }
        }

        return $analysis;
    }

    /**
     * Extract metrics from sales_data JSON (more accurate than text parsing)
     */
    private function extractMetricsFromSalesData($salesDataJson, &$analysis)
    {
        if (empty($salesDataJson)) {
            return;
        }

        $salesData = json_decode($salesDataJson, true);
        if (!$salesData) {
            return;
        }

        // Extract products
        if (isset($salesData['dang_san_pham_so_luong'])) {
            $analysis['total_products'] += (int)$salesData['dang_san_pham_so_luong'];
        }

        // Extract videos
        if (isset($salesData['dang_video_so_luong'])) {
            $analysis['total_videos'] += (int)$salesData['dang_video_so_luong'];
        }

        // Extract orders
        if (isset($salesData['don_hang_tong'])) {
            $analysis['total_orders'] += (int)$salesData['don_hang_tong'];
        }

        // Extract niches
        if (isset($salesData['dang_san_pham_niche'])) {
            $niches = explode(',', $salesData['dang_san_pham_niche']);
            foreach ($niches as $niche) {
                $niche = trim($niche);
                if (!empty($niche) && !in_array($niche, $analysis['niches'])) {
                    $analysis['niches'][] = $niche;
                }
            }
        }
    }



    /**
     * Create optimized prompt for ChatGPT (following best practices)
     */
    private function createComprehensivePrompt($dates, $analysis, $totalReports, $totalSellers)
    {
        $dateRange = count($dates) === 1 ? $dates[0] : min($dates) . ' đến ' . max($dates);

        // Tạo chi tiết sellers gọn gàng
        $sellersDetail = $this->prepareSellersDetailForGPT($analysis['sellers_performance']);

        return "Bạn là AI chuyên gia phân tích kinh doanh hệ thống POD US. Dưới đây là tổng hợp Daily Reports của ngày {$dateRange}.

THÔNG TIN TỔNG QUAN:
- Tổng số sellers: {$totalSellers}
- Tổng số báo cáo: {$totalReports}
- Tổng sản phẩm đăng: {$analysis['total_products']}
- Tổng video đăng: {$analysis['total_videos']}
- Tổng đơn hàng: {$analysis['total_orders']}
- Niches hoạt động: " . implode(', ', $analysis['niches']) . "

PHÂN LOẠI SELLERS:
- High Performers: " . implode(', ', $analysis['high_performers']) . "
- Sellers cần chú ý: " . (empty($analysis['need_attention']) ? '[không có]' : implode(', ', $analysis['need_attention'])) . "

CHI TIẾT SELLERS:
{$sellersDetail}

YÊU CẦU:
Viết báo cáo tổng hợp theo format:
- Tổng quan hiệu suất toàn hệ thống
- Thống kê chính
- Phân tích và đánh giá các sellers xuất sắc
- Các seller cần hỗ trợ (nếu có)
- Phân tích hiệu quả của các niches đang hoạt động
- Tóm tắt hoạt động từng seller
- Đề xuất cải thiện cho từng nhóm seller và toàn hệ thống
- Dự báo xu hướng, khuyến nghị chiến lược tiếp theo

Viết chi tiết, logic, có thể trình bày dạng bảng nếu cần. Ngôn ngữ chuyên nghiệp, rõ ràng, giúp quản lý ra quyết định.";
    }

    /**
     * Prepare sellers detail with formatted sales_data for GPT
     */
    private function prepareSellersDetailForGPT($sellersPerformance)
    {
        $details = [];

        foreach ($sellersPerformance as $sellerName => $performance) {
            $activityDesc = strtolower($performance['activity_level']);

            $detail = "- {$sellerName}: {$performance['reports_count']} báo cáo, {$activityDesc}";

            // Format sales_data đẹp hơn cho GPT
            if (!empty($performance['sales_data'])) {
                $salesData = json_decode($performance['sales_data'], true);
                if ($salesData) {
                    $formattedSalesData = $this->formatSalesDataForGPT($salesData);
                    $detail .= ". Sales data: " . $formattedSalesData;
                } else {
                    // Fallback nếu không parse được JSON
                    $detail .= ". Sales data: " . $performance['sales_data'];
                }
            }

            $detail .= ".";
            $details[] = $detail;
        }

        return implode("\n", $details);
    }

    /**
     * Format sales_data JSON thành text đẹp cho GPT
     */
    private function formatSalesDataForGPT($salesData)
    {
        $formatted = [];

        // Sản phẩm
        if (isset($salesData['dang_san_pham_so_luong']) && $salesData['dang_san_pham_so_luong']) {
            $formatted[] = "đăng {$salesData['dang_san_pham_so_luong']} sản phẩm";
            if (!empty($salesData['dang_san_pham_niche'])) {
                $formatted[count($formatted)-1] .= " ({$salesData['dang_san_pham_niche']})";
            }
        }

        // Video
        if (isset($salesData['dang_video_so_luong']) && $salesData['dang_video_so_luong']) {
            $videoText = "{$salesData['dang_video_so_luong']} video";
            if (!empty($salesData['dang_video_acc'])) {
                $videoText .= " (acc: {$salesData['dang_video_acc']})";
            }
            $formatted[] = $videoText;
        }

        // Đơn hàng
        if (isset($salesData['don_hang_tong']) && $salesData['don_hang_tong']) {
            $orderText = "{$salesData['don_hang_tong']} đơn hàng";
            if (!empty($salesData['don_hang_ghi_chu'])) {
                $orderText .= " ({$salesData['don_hang_ghi_chu']})";
            }
            $formatted[] = $orderText;
        }

        // Quảng cáo
        if (isset($salesData['chay_quang_cao_ngan_sach']) && $salesData['chay_quang_cao_ngan_sach']) {
            $adText = "QC {$salesData['chay_quang_cao_ngan_sach']} VND";
            if (!empty($salesData['chay_quang_cao_ket_qua'])) {
                $adText .= " ({$salesData['chay_quang_cao_ket_qua']})";
            }
            $formatted[] = $adText;
        }

        // Khách hàng
        if (isset($salesData['khach_hang_phan_hoi']) && $salesData['khach_hang_phan_hoi']) {
            $customerText = "{$salesData['khach_hang_phan_hoi']} phản hồi";
            if (!empty($salesData['khach_hang_feedback'])) {
                $customerText .= " (feedback: {$salesData['khach_hang_feedback']})";
            }
            $formatted[] = $customerText;
        }

        // Shop Health
        if (!empty($salesData['kiem_tra_shop_health'])) {
            $formatted[] = "shop health: {$salesData['kiem_tra_shop_health']}";
        }

        return implode(', ', $formatted);
    }



    /**
     * Prepare clean data for GPT (remove unnecessary fields) - DEPRECATED
     */
    private function prepareCleanDataForGPT($sellersPerformance)
    {
        $cleanData = [];

        foreach ($sellersPerformance as $sellerName => $performance) {
            $cleanData[] = [
                'seller_name' => $sellerName,
                'seller_id' => $performance['seller_id'],
                'reports_count' => $performance['reports_count'],
                'activity_level' => $performance['activity_level'],
                'summary' => $performance['summary']
                // Loại bỏ 'dates' vì không cần thiết cho single date analysis
            ];
        }

        return json_encode($cleanData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Check for duplicate reports and log warnings
     */
    private function checkForDuplicates($allDatesData)
    {
        $duplicateCount = 0;
        $seenReports = [];

        foreach ($allDatesData as $date => $reports) {
            foreach ($reports as $report) {
                // Tạo hash từ nội dung chính để detect duplicate
                $contentHash = md5(
                    ($report['seller_id'] ?? '') .
                    ($report['report_date'] ?? '') .
                    ($report['text'] ?? '') .
                    ($report['activities'] ?? '') .
                    ($report['sales_data'] ?? '')
                );

                if (isset($seenReports[$contentHash])) {
                    $duplicateCount++;
                    $this->warn("⚠️ Duplicate content detected: Report ID {$report['report_id']} similar to {$seenReports[$contentHash]}");
                } else {
                    $seenReports[$contentHash] = $report['report_id'] ?? 'unknown';
                }
            }
        }

        if ($duplicateCount > 0) {
            $this->warn("⚠️ Found {$duplicateCount} potential duplicate reports. Consider checking upstream sync process.");
        } else {
            $this->info("✅ No duplicate content detected.");
        }

        return $duplicateCount;
    }

    /**
     * Get active sellers in date range
     */
    private function getActiveSellers($fromDate, $toDate)
    {
        $query = \Illuminate\Support\Facades\DB::table('daily_reports')
            ->join('users', 'daily_reports.seller_id', '=', 'users.id')
            ->select('users.id', 'users.name')
            ->distinct();

        if ($fromDate) {
            $query->where('daily_reports.report_date', '>=', $fromDate);
        }
        if ($toDate) {
            $query->where('daily_reports.report_date', '<=', $toDate);
        }

        return $query->get();
    }

    /**
     * Generate PDF and upload to S3
     */
    private function generateAndUploadPDF($content, $dateRange, $title)
    {
        try {
            // Convert markdown-like content to HTML
            $htmlContent = $this->convertToHTML($content, $dateRange, $title);

            // Generate PDF
            $pdf = Pdf::loadHTML($htmlContent)
                ->setPaper('A4', 'portrait')
                ->setOptions([
                    'defaultFont' => 'DejaVu Sans',
                    'isRemoteEnabled' => true,
                    'isHtml5ParserEnabled' => true,
                    'isFontSubsettingEnabled' => true,
                    'defaultMediaType' => 'print',
                    'isCssFloatEnabled' => true,
                ]);

            $pdfBinary = $pdf->output();

            // Upload to S3
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $filename = "daily-reports-summary/comprehensive_report_{$timestamp}.pdf";

            $saved = Storage::disk('s3')->put($filename, $pdfBinary, 'public');

            if ($saved) {
                $url = Storage::disk('s3')->url($filename);
                $this->info("📄 PDF saved to S3: {$filename}");
                return [
                    'url' => $url,
                    'filename' => $filename
                ];
            }

            return ['url' => '', 'filename' => ''];

        } catch (\Exception $e) {
            $this->error("❌ Error generating PDF: " . $e->getMessage());
            return ['url' => '', 'filename' => ''];
        }
    }

    /**
     * Convert content to HTML for PDF
     */
    private function convertToHTML($content, $dateRange, $title)
    {
        // Convert markdown-like formatting to HTML
        $htmlContent = $content;

        // Convert headers
        $htmlContent = preg_replace('/^📊 \*\*(.*?)\*\*$/m', '<h1 style="color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px;">📊 $1</h1>', $htmlContent);
        $htmlContent = preg_replace('/^🎯 \*\*(.*?)\*\*$/m', '<h2 style="color: #059669; margin-top: 25px;">🎯 $1</h2>', $htmlContent);
        $htmlContent = preg_replace('/^📈 \*\*(.*?)\*\*$/m', '<h2 style="color: #059669; margin-top: 25px;">📈 $1</h2>', $htmlContent);
        $htmlContent = preg_replace('/^👑 \*\*(.*?)\*\*$/m', '<h2 style="color: #d97706; margin-top: 25px;">👑 $1</h2>', $htmlContent);
        $htmlContent = preg_replace('/^⚠️ \*\*(.*?)\*\*$/m', '<h2 style="color: #dc2626; margin-top: 25px;">⚠️ $1</h2>', $htmlContent);
        $htmlContent = preg_replace('/^🎯 \*\*(.*?)\*\*$/m', '<h2 style="color: #7c3aed; margin-top: 25px;">🎯 $1</h2>', $htmlContent);
        $htmlContent = preg_replace('/^📋 \*\*(.*?)\*\*$/m', '<h2 style="color: #0891b2; margin-top: 25px;">📋 $1</h2>', $htmlContent);
        $htmlContent = preg_replace('/^💡 \*\*(.*?)\*\*$/m', '<h2 style="color: #ea580c; margin-top: 25px;">💡 $1</h2>', $htmlContent);
        $htmlContent = preg_replace('/^🔮 \*\*(.*?)\*\*$/m', '<h2 style="color: #9333ea; margin-top: 25px;">🔮 $1</h2>', $htmlContent);

        // Convert bold text
        $htmlContent = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $htmlContent);

        // Convert bullet points
        $htmlContent = preg_replace('/^- (.*)$/m', '<li style="margin: 5px 0;">$1</li>', $htmlContent);

        // Wrap lists
        $htmlContent = preg_replace('/(<li.*?<\/li>\s*)+/s', '<ul style="margin: 10px 0; padding-left: 20px;">$0</ul>', $htmlContent);

        // Convert line breaks
        $htmlContent = nl2br($htmlContent);

        $date = Carbon::now()->format('d/m/Y H:i');

        return "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>{$title}</title>
    <style>
        body { font-family: 'DejaVu Sans', sans-serif; line-height: 1.6; margin: 40px; color: #333; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 3px solid #2563eb; padding-bottom: 20px; }
        .header h1 { color: #2563eb; margin: 0; font-size: 24px; }
        .header p { color: #666; margin: 5px 0; }
        .content { margin: 20px 0; }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px; }
        h1, h2 { margin-top: 25px; margin-bottom: 15px; }
        ul { margin: 10px 0; padding-left: 20px; }
        li { margin: 5px 0; }
        strong { color: #2563eb; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>{$title}</h1>
        <p>Khoảng thời gian: {$dateRange}</p>
        <p>Ngày tạo: {$date} | Được tạo bởi AI Analysis System</p>
    </div>
    <div class='content'>
        {$htmlContent}
    </div>
    <div class='footer'>
        <p>© " . date('Y') . " - Báo cáo được tạo tự động bởi hệ thống AI Business Intelligence</p>
    </div>
</body>
</html>";
    }

    /**
     * Prepare raw embedding data for storage
     */
    private function prepareRawEmbeddingData($allDatesData)
    {
        $rawData = [
            'source' => 'zilliz_vector_database',
            'collection_name' => 'daily_reports_embeddings',
            'extraction_timestamp' => now()->toISOString(),
            'total_dates' => count($allDatesData),
            'dates_data' => []
        ];

        foreach ($allDatesData as $date => $reports) {
            $dateData = [
                'date' => $date,
                'reports_count' => count($reports),
                'reports' => []
            ];

            foreach ($reports as $report) {
                // Chỉ lưu dữ liệu cần thiết (loại bỏ vector và metadata không cần thiết)
                $reportData = [
                    // Essential identifiers
                    'report_id' => $report['report_id'] ?? null,
                    'seller_id' => $report['seller_id'] ?? null,
                    'seller_name' => $report['seller_name'] ?? null,
                    'report_date' => $report['report_date'] ?? null,

                    // Business data (cần thiết cho analysis)
                    'status' => $report['status'] ?? null,
                    'activities' => $report['activities'] ?? null,
                    'sales_data' => $report['sales_data'] ?? null,
                    'report_summary' => $report['report_summary'] ?? null,
                    'seller_activity' => $report['seller_activity'] ?? null,
                    'text' => $report['text'] ?? null,

                    // Technical metadata (chỉ để audit)
                    'distance' => $report['distance'] ?? null,
                    'embedding_dimension' => $report['embedding_dimension'] ?? null,
                    'embedding_model' => $report['embedding_model'] ?? null,

                    // Vector info (không lưu vector thực tế để tiết kiệm dung lượng)
                    'has_vector_embedding' => isset($report['vector_embedding']) && is_array($report['vector_embedding']),
                    'vector_length' => isset($report['vector_embedding']) && is_array($report['vector_embedding'])
                        ? count($report['vector_embedding'])
                        : 0
                ];

                $dateData['reports'][] = $reportData;
            }

            $rawData['dates_data'][$date] = $dateData;
        }

        // Tính toán summary statistics
        $allSellerIds = [];
        foreach ($rawData['dates_data'] as $dateData) {
            $sellerIds = array_column($dateData['reports'], 'seller_id');
            $allSellerIds = array_merge($allSellerIds, $sellerIds);
        }

        $rawData['summary_statistics'] = [
            'total_reports' => array_sum(array_column($rawData['dates_data'], 'reports_count')),
            'unique_sellers' => count(array_unique($allSellerIds)),
            'date_range' => [
                'from' => min(array_keys($rawData['dates_data'])),
                'to' => max(array_keys($rawData['dates_data']))
            ]
        ];

        return json_encode($rawData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Prepare raw embedding prompt data for storage (individual reports as sent to ChatGPT)
     */
    private function prepareRawEmbeddingPrompt($allDatesData)
    {
        $embeddingPrompts = [
            'note' => 'Mỗi report được gửi riêng biệt cho ChatGPT tạo embedding (1 report = 1 vector)',
            'embedding_model' => 'text-embedding-ada-002',
            'max_tokens' => 8191,
            'extraction_timestamp' => now()->toISOString(),
            'individual_prompts' => []
        ];

        // Lưu từng text riêng biệt như thực tế đã gửi cho ChatGPT
        foreach ($allDatesData as $date => $reports) {
            foreach ($reports as $report) {
                $originalText = $report['text'] ?? '';
                if (!empty($originalText)) {
                    $embeddingPrompts['individual_prompts'][] = [
                        'report_id' => $report['report_id'] ?? null,
                        'seller_id' => $report['seller_id'] ?? null,
                        'seller_name' => $report['seller_name'] ?? null,
                        'report_date' => $date,
                        'text_length' => strlen($originalText),
                        'tokens_estimate' => ceil(strlen($originalText) / 4), // Rough estimate: 1 token ≈ 4 chars
                        'embedding_text' => $originalText // Text thực tế gửi cho ChatGPT
                    ];
                }
            }
        }

        // Thống kê
        $embeddingPrompts['summary'] = [
            'total_individual_embeddings' => count($embeddingPrompts['individual_prompts']),
            'total_characters' => array_sum(array_column($embeddingPrompts['individual_prompts'], 'text_length')),
            'estimated_tokens' => array_sum(array_column($embeddingPrompts['individual_prompts'], 'tokens_estimate')),
            'avg_chars_per_report' => count($embeddingPrompts['individual_prompts']) > 0
                ? round(array_sum(array_column($embeddingPrompts['individual_prompts'], 'text_length')) / count($embeddingPrompts['individual_prompts']))
                : 0
        ];

        return json_encode($embeddingPrompts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Get date range array
     */
    private function getDateRange($fromDate, $toDate)
    {
        if (!$fromDate && !$toDate) {
            return [now()->format('Y-m-d')];
        }

        $start = $fromDate ? \Carbon\Carbon::parse($fromDate) : now();
        $end = $toDate ? \Carbon\Carbon::parse($toDate) : now();

        $dates = [];
        for ($date = $start; $date->lte($end); $date->addDay()) {
            $dates[] = $date->format('Y-m-d');
        }

        return $dates;
    }

    /**
     * Save texts to S3 with data type prefix
     */
    private function saveTextsToS3($allTexts, $dataType = 'daily_reports')
    {
        try {
            $timestamp = now()->format('Y-m-d_H-i-s');
            $filename = "{$dataType}-texts/{$dataType}_{$timestamp}.json";

            $completeData = [
                'sync_timestamp' => $timestamp,
                'total_reports' => count($allTexts),
                'daily_reports' => $allTexts,
                'metadata' => [
                    'generated_by' => 'VectorSyncAllCommand',
                    'purpose' => 'Daily Reports texts backup before embedding',
                    'format' => 'Optimized daily reports data for API consumption'
                ]
            ];

            $saved = Storage::disk('s3')->put(
                $filename,
                json_encode($completeData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
            );

            if ($saved) {
                $url = Storage::disk('s3')->url($filename);
                $this->info("💾 Daily Reports texts saved to S3: {$filename}");
                $this->info("📊 Total reports saved: " . count($allTexts));
                return $url;
            }

            $this->warn("❌ Failed to save Daily Reports texts to S3");
            return null;

        } catch (\Exception $e) {
            $this->error("❌ Error saving Daily Reports texts to S3: " . $e->getMessage());
            return null;
        }
    }
}
   