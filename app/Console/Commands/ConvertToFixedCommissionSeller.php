<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ConvertToFixedCommissionSeller extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seller:convert-to-fixed-commission 
        {seller? : ID người bán hoặc email}
        {--rate= : Tỷ lệ % hoa hồng cố định}
        {--no-salary : Không nhận lương cơ bản}
        {--all : Chuyển đổi tất cả sellers}
        {--dry-run : Chỉ kiểm tra, không thực hiện thay đổi}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Chuyển đổi seller sang loại hình nhận % hoa hồng cố định';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Validate arguments
        if (!$this->argument('seller') && !$this->option('all')) {
            $this->error('Vui lòng cung cấp ID/email seller hoặc sử dụng --all');
            return 1;
        }

        if (!$this->option('rate') && !$this->option('dry-run')) {
            $this->error('Vui lòng cung cấp tỷ lệ % hoa hồng (--rate)');
            return 1;
        }
        
        // Lấy danh sách sellers cần chuyển đổi
        $query = User::role('seller');
        
        if ($this->argument('seller')) {
            $sellerIdentifier = $this->argument('seller');
            // Xác định nếu đầu vào là ID hoặc email
            $isEmail = filter_var($sellerIdentifier, FILTER_VALIDATE_EMAIL);
            
            if ($isEmail) {
                $query->where('email', $sellerIdentifier);
            } else {
                $query->where('id', $sellerIdentifier);
            }
        }
        
        $sellers = $query->get();
        
        if ($sellers->isEmpty()) {
            $this->error('Không tìm thấy seller nào!');
            return 1;
        }
        
        $this->info("Tìm thấy " . $sellers->count() . " seller.");
        
        if ($this->option('dry-run')) {
            $this->line("Đây là chế độ dry-run, không có thay đổi nào được thực hiện.");
            $this->showSellersTable($sellers);
            return 0;
        }
        
        $commissionRate = floatval($this->option('rate'));
        if ($commissionRate <= 0 || $commissionRate > 100) {
            $this->error('Tỷ lệ % hoa hồng phải lớn hơn 0 và nhỏ hơn hoặc bằng 100');
            return 1;
        }
        
        $hasSalary = !$this->option('no-salary');
        
        // Xác nhận từ người dùng
        if (!$this->confirmConversion($sellers, $commissionRate, $hasSalary)) {
            $this->line('Đã hủy thao tác.');
            return 0;
        }
        
        // Thực hiện chuyển đổi
        try {
            DB::beginTransaction();
            
            $count = 0;
            foreach ($sellers as $seller) {
                $oldSettings = [
                    'has_fixed_commission' => $seller->has_fixed_commission,
                    'fixed_commission_rate' => $seller->fixed_commission_rate,
                    'has_base_salary' => $seller->has_base_salary
                ];
                
                $seller->has_fixed_commission = true;
                $seller->fixed_commission_rate = $commissionRate;
                $seller->has_base_salary = $hasSalary;
                $seller->save();
                
                $this->line("Đã chuyển đổi seller: {$seller->name} (ID: {$seller->id})");
                $this->line("  - % hoa hồng cố định: {$commissionRate}%");
                $this->line("  - Có lương cơ bản: " . ($hasSalary ? 'Có' : 'Không'));
                
                Log::info('Chuyển đổi seller sang hoa hồng cố định', [
                    'seller_id' => $seller->id,
                    'seller_name' => $seller->name,
                    'old_settings' => $oldSettings,
                    'new_settings' => [
                        'has_fixed_commission' => true,
                        'fixed_commission_rate' => $commissionRate,
                        'has_base_salary' => $hasSalary
                    ]
                ]);
                
                $count++;
            }
            
            DB::commit();
            $this->info("Đã chuyển đổi thành công {$count} seller.");
            
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("Lỗi: " . $e->getMessage());
            Log::error('Lỗi chuyển đổi seller sang hoa hồng cố định', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
        
        return 0;
    }
    
    /**
     * Hiển thị bảng sellers
     */
    private function showSellersTable($sellers)
    {
        $rows = [];
        foreach ($sellers as $seller) {
            $rows[] = [
                'ID' => $seller->id,
                'Tên' => $seller->name,
                'Email' => $seller->email,
                'Level' => $seller->sellerLevel ? $seller->sellerLevel->name : 'N/A',
                'Loại hoa hồng' => $seller->has_fixed_commission ? 'Cố định' : 'Doanh số',
                'Tỷ lệ %' => $seller->has_fixed_commission ? $seller->fixed_commission_rate . '%' : 'Theo doanh số',
                'Lương cơ bản' => $seller->has_base_salary ? 'Có' : 'Không'
            ];
        }
        
        $this->table(
            ['ID', 'Tên', 'Email', 'Level', 'Loại hoa hồng', 'Tỷ lệ %', 'Lương cơ bản'],
            $rows
        );
    }
    
    /**
     * Xác nhận thao tác chuyển đổi
     */
    private function confirmConversion($sellers, $rate, $hasSalary)
    {
        $this->line('');
        $this->line('Bạn sắp thực hiện chuyển đổi ' . $sellers->count() . ' seller sang loại hình hoa hồng cố định:');
        $this->line("- Tỷ lệ % hoa hồng: {$rate}%");
        $this->line('- Có lương cơ bản: ' . ($hasSalary ? 'Có' : 'Không'));
        $this->line('');
        
        $this->showSellersTable($sellers);
        
        return $this->confirm('Bạn có chắc chắn muốn thực hiện?');
    }
}
