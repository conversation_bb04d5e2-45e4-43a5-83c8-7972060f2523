<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TelegramBotManagementService;
use Telegram\Bot\Laravel\Facades\Telegram;
use Illuminate\Support\Facades\Log;

class TelegramBotSetupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:setup
                            {--setup-webhook : Setup webhook for the bot}
                            {--get-info : Get bot and webhook information}
                            {--get-invite-link : Get invite link for sellers}
                            {--stats : Show telegram users statistics}
                            {--remove-webhook : Remove webhook (for development)}
                            {--force-http : Force HTTP webhook (for development only)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup and manage Telegram bot for notifications';

    protected $botManagementService;

    public function __construct(TelegramBotManagementService $botManagementService)
    {
        parent::__construct();
        $this->botManagementService = $botManagementService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🤖 Telegram Bot Setup & Management');
        $this->newLine();

        if ($this->option('setup-webhook')) {
            return $this->setupWebhook();
        }

        if ($this->option('get-info')) {
            return $this->getBotInfo();
        }

        if ($this->option('get-invite-link')) {
            return $this->getInviteLink();
        }

        if ($this->option('stats')) {
            return $this->showStatistics();
        }

        if ($this->option('remove-webhook')) {
            return $this->removeWebhook();
        }

        // Default: show menu
        $this->showMenu();
    }

    protected function showMenu()
    {
        $this->info('Available options:');
        $this->line('  --setup-webhook    Setup webhook for the bot');
        $this->line('  --get-info         Get bot and webhook information');
        $this->line('  --get-invite-link  Get invite link for sellers');
        $this->line('  --stats            Show telegram users statistics');
        $this->newLine();
        $this->comment('Example: php artisan telegram:setup --setup-webhook');
    }

    protected function setupWebhook()
    {
        try {
            $this->info('🔧 Setting up webhook...');

            $webhookUrl = config('telegram.bots.special_bot.webhook_url');

            if (empty($webhookUrl)) {
                $this->error('❌ Webhook URL not configured. Please set TELEGRAM_WEBHOOK_URL in your .env file.');
                return 1;
            }

            // Check if URL is HTTP and warn user
            if (str_starts_with($webhookUrl, 'http://') && !$this->option('force-http')) {
                $this->error('❌ Telegram requires HTTPS URLs for webhooks.');
                $this->line('');
                $this->comment('Solutions:');
                $this->line('1. Use HTTPS URL: https://kmediaz.com/api/telegram/special/webhook');
                $this->line('2. For development, use: --force-http (not recommended)');
                $this->line('3. Use ngrok: ngrok http 80');
                return 1;
            }

            $this->line("Webhook URL: {$webhookUrl}");

            $response = Telegram::bot('special_bot')->setWebhook([
                'url' => $webhookUrl,
                'max_connections' => 100,
                'allowed_updates' => ['message', 'callback_query'],
            ]);

            if ($response) {
                $this->info('✅ Webhook setup successfully!');
                $this->newLine();

                // Get webhook info to verify
                $webhookInfo = Telegram::bot('special_bot')->getWebhookInfo();
                $this->displayWebhookInfo($webhookInfo);

                return 0;
            } else {
                $this->error('❌ Failed to setup webhook');
                return 1;
            }

        } catch (\Exception $e) {
            $this->error('❌ Error setting up webhook: ' . $e->getMessage());
            Log::error('Telegram webhook setup error', ['error' => $e->getMessage()]);
            return 1;
        }
    }

    protected function removeWebhook()
    {
        try {
            $this->info('🗑️ Removing webhook...');

            $response = Telegram::bot('special_bot')->removeWebhook();

            if ($response) {
                $this->info('✅ Webhook removed successfully!');
                $this->comment('Bot will now use polling mode for development.');
                return 0;
            } else {
                $this->error('❌ Failed to remove webhook');
                return 1;
            }

        } catch (\Exception $e) {
            $this->error('❌ Error removing webhook: ' . $e->getMessage());
            return 1;
        }
    }

    protected function getBotInfo()
    {
        try {
            $this->info('📊 Getting bot information...');
            $this->newLine();

            $status = $this->botManagementService->getBotStatus();

            if (isset($status['error'])) {
                $this->error('❌ Error getting bot info: ' . $status['error']);
                return 1;
            }

            // Bot Info
            $this->info('🤖 Bot Information:');
            $botInfo = $status['bot_info'];
            $this->line("  ID: {$botInfo['id']}");
            $this->line("  Username: @{$botInfo['username']}");
            $this->line("  Name: {$botInfo['first_name']}");
            $this->line("  Is Bot: " . ($botInfo['is_bot'] ? 'Yes' : 'No'));
            $this->newLine();

            // Webhook Info
            $this->info('🔗 Webhook Information:');
            $this->displayWebhookInfo($status['webhook_info']);

            // Statistics
            $this->info('📈 Statistics:');
            $stats = $status['statistics'];
            $this->line("  Total Telegram Users: {$stats['total_telegram_users']}");
            $this->line("  Active Users: {$stats['active_telegram_users']}");
            $this->line("  Linked Users: {$stats['linked_telegram_users']}");
            $this->line("  Unlinked Users: {$stats['unlinked_telegram_users']}");
            $this->line("  System Users with Telegram: {$stats['users_with_telegram']}");

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Error getting bot info: ' . $e->getMessage());
            return 1;
        }
    }

    protected function getInviteLink()
    {
        try {
            $this->info('🔗 Getting invite link...');

            $botInfo = Telegram::bot('special_bot')->getMe();
            $botUsername = $botInfo->getUsername();

            $inviteLink = "https://t.me/{$botUsername}";

            $this->newLine();
            $this->info('📱 Invite Link for Sellers:');
            $this->line($inviteLink);
            $this->newLine();

            $this->comment('Instructions for sellers:');
            $this->line('1. Click the link above or search for @' . $botUsername . ' on Telegram');
            $this->line('2. Click "Start" button');
            $this->line('3. The system will automatically register them');
            $this->line('4. Admin can link their Telegram account to system user if needed');
            $this->newLine();

            $this->info('💡 You can also share this message:');
            $this->line('---');
            $this->line("🤖 Join our notification bot: {$inviteLink}");
            $this->line('Click the link and press Start to receive order notifications!');
            $this->line('---');

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Error getting invite link: ' . $e->getMessage());
            return 1;
        }
    }

    protected function showStatistics()
    {
        try {
            $this->info('📊 Telegram Users Statistics');
            $this->newLine();

            $stats = $this->botManagementService->getStatistics();

            $this->table(
                ['Metric', 'Count'],
                [
                    ['Total Telegram Users', $stats['total_telegram_users']],
                    ['Active Users', $stats['active_telegram_users']],
                    ['Linked Users', $stats['linked_telegram_users']],
                    ['Unlinked Users', $stats['unlinked_telegram_users']],
                    ['System Users with Telegram', $stats['users_with_telegram']],
                ]
            );

            // Recent users
            $recentUsers = $this->botManagementService->getTelegramUsers(['active' => true]);

            if ($recentUsers->count() > 0) {
                $this->newLine();
                $this->info('📋 Recent Active Users:');

                $tableData = $recentUsers->take(10)->map(function ($user) {
                    return [
                        $user->display_name,
                        $user->username ? '@' . $user->username : 'No username',
                        $user->user ? $user->user->name : 'Not linked',
                        $user->last_interaction_at ? $user->last_interaction_at->diffForHumans() : 'Never',
                    ];
                })->toArray();

                $this->table(
                    ['Name', 'Username', 'Linked User', 'Last Interaction'],
                    $tableData
                );
            }

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Error getting statistics: ' . $e->getMessage());
            return 1;
        }
    }

    protected function displayWebhookInfo($webhookInfo)
    {
        if (is_array($webhookInfo)) {
            $url = $webhookInfo['url'] ?? 'Not set';
            $pendingUpdates = $webhookInfo['pending_update_count'] ?? 0;
            $lastError = $webhookInfo['last_error_message'] ?? 'None';
        } else {
            $url = $webhookInfo->getUrl() ?: 'Not set';
            $pendingUpdates = $webhookInfo->getPendingUpdateCount();
            $lastError = $webhookInfo->getLastErrorMessage() ?: 'None';
        }

        $this->line("  URL: {$url}");
        $this->line("  Pending Updates: {$pendingUpdates}");
        $this->line("  Last Error: {$lastError}");
        $this->newLine();
    }
}
