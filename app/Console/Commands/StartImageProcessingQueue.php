<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class StartImageProcessingQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:start-all {--timeout=60}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start queue worker for all queues (default + image-processing)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $timeout = $this->option('timeout');

        $this->info('Starting queue worker for all queues...');
        $this->info("Timeout: {$timeout} seconds");
        $this->info('Queues: default, image-processing');
        $this->info('Press Ctrl+C to stop');

        // Chạy queue worker cho cả default và image-processing queue
        Artisan::call('queue:work', [
            '--queue' => 'default,image-processing',
            '--timeout' => $timeout,
            '--tries' => 3,
            '--delay' => 3,
            '--memory' => 512,
            '--sleep' => 3,
            '--max-jobs' => 100,
            '--max-time' => 3600, // 1 hour
        ]);

        $this->info('Queue worker stopped.');
    }
}
