<?php

namespace App\Console\Commands;

use App\Models\Trademark;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class ImportTrademarkFromJson extends Command
{
    protected $signature = 'trademarks:import-json {file? : Path to JSON file with trademarks (default: public/keytm.json)}';
    protected $description = 'Import trademarks from a JSON file in random order, avoiding duplicates';

    public function handle()
    {
        $filePath = $this->argument('file') ?? public_path('keytm.json');
        
        if (!File::exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }
        
        try {
            $jsonContent = File::get($filePath);
            $data = json_decode($jsonContent, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error('Invalid JSON file: ' . json_last_error_msg());
                return 1;
            }
            
            if (empty($data) || !is_array($data)) {
                $this->error('No valid data found in the JSON file');
                return 1;
            }
            
            // Extract keywords from the JSON data
            $keywords = array_map(function ($item) {
                return $item['key'] ?? null;
            }, $data);
            
            // Filter out null values
            $keywords = array_filter($keywords);
            
            // Remove duplicates
            $keywords = array_unique($keywords);
            
            // Randomize the order
            shuffle($keywords);
            
            $this->info('Found ' . count($keywords) . ' unique keywords in the file');
            
            // Get admin user for created_by
            $admin = User::role('super_admin')->first();
            
            if (!$admin) {
                $this->error('No admin user found to set as creator');
                return 1;
            }
            
            $bar = $this->output->createProgressBar(count($keywords));
            $bar->start();
            
            DB::beginTransaction();
            
            try {
                $imported = 0;
                $skipped = 0;
                
                foreach ($keywords as $keyword) {
                    $keyword = trim($keyword);
                    
                    if (empty($keyword)) {
                        $skipped++;
                        $bar->advance();
                        continue;
                    }
                    
                    $exists = Trademark::where('keyword', $keyword)->exists();
                    
                    if ($exists) {
                        $skipped++;
                        $bar->advance();
                        continue;
                    }
                    
                    Trademark::create([
                        'keyword' => $keyword,
                        'created_by' => $admin->id,
                    ]);
                    
                    $imported++;
                    $bar->advance();
                }
                
                DB::commit();
                $bar->finish();
                $this->newLine(2);
                
                $this->info("Import completed: {$imported} trademarks imported, {$skipped} skipped");
                
                return 0;
            } catch (\Exception $e) {
                DB::rollBack();
                $this->error('Error importing trademarks: ' . $e->getMessage());
                return 1;
            }
        } catch (\Exception $e) {
            $this->error('Error reading or processing file: ' . $e->getMessage());
            return 1;
        }
    }
} 