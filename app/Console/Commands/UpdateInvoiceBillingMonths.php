<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Invoice;
use Carbon\Carbon;

class UpdateInvoiceBillingMonths extends Command
{
    protected $signature = 'invoices:update-billing-months';
    protected $description = 'Update billing_month for invoices based on issue_date';

    public function handle()
    {
        $this->info('Bắt đầu cập nhật billing_month cho các hóa đơn...');

        // Lấy tất cả hóa đơn có billing_month là null
        $invoices = Invoice::whereNull('billing_month')->get();
        
        $count = 0;
        foreach ($invoices as $invoice) {
            try {
                // Lấy issue_date và chuyển về tháng trước
                $issueDate = Carbon::parse($invoice->issue_date);
                $billingMonth = $issueDate->copy()->subMonth()->startOfMonth();
                
                // Log để debug
                $this->info("Invoice #{$invoice->id}: Issue date: {$invoice->issue_date} -> Billing month: {$billingMonth->format('Y-m-d')}");
                
                // Cập nhật billing_month
                $invoice->update([
                    'billing_month' => $billingMonth
                ]);
                
                $count++;
            } catch (\Exception $e) {
                $this->error("Lỗi khi cập nhật hóa đơn #{$invoice->id}: " . $e->getMessage());
            }
        }

        $this->info("Đã cập nhật thành công {$count} hóa đơn.");
    }
}