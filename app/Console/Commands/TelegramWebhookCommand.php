<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Telegram\Bot\Laravel\Facades\Telegram;
use Illuminate\Support\Facades\Log;

class TelegramWebhookCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:webhook
                            {--setup : Thiết lập webhook}
                            {--remove : Gỡ bỏ webhook}
                            {--info : Xem thông tin webhook}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Quản lý webhook của Bot Telegram';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Đang xử lý yêu cầu Telegram Webhook...');

        try {
            if ($this->option('setup')) {
                $this->setupWebhook();
            } elseif ($this->option('remove')) {
                $this->removeWebhook();
            } elseif ($this->option('info')) {
                $this->getWebhookInfo();
            } else {
                $this->info('Vui lòng cung cấp một tùy chọn (--setup, --remove, --info)');
                $this->showHelp();
            }
        } catch (\Exception $e) {
            $this->error('Lỗi: ' . $e->getMessage());
            Log::error('Telegram Webhook Command Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }

    /**
     * Thiết lập webhook
     */
    protected function setupWebhook()
    {
        $this->info('Đang thiết lập webhook...');

        // Lấy webhook URL từ config
        $url = config('telegram.bots.mybot.webhook_url');

        if (empty($url)) {
            $this->error('Không tìm thấy webhook_url trong cấu hình. Vui lòng kiểm tra file config/telegram.php');
            return;
        }

        $this->info('URL Webhook: ' . $url);

        // Cấu hình webhook params
        $params = [
            'url' => $url,
            'max_connections' => 100,
            'allowed_updates' => ['message', 'callback_query', 'inline_query'],
        ];

        // Certificate path (nếu có)
        $certificate = config('telegram.bots.mybot.certificate_path', null);
        if (!empty($certificate) && file_exists($certificate)) {
            $params['certificate'] = $certificate;
            $this->info('Sử dụng certificate: ' . $certificate);
        }

        // Thiết lập webhook
        $response = Telegram::setWebhook($params);

        if ($response) {
            $this->info('✅ Thiết lập webhook thành công!');
            $this->getWebhookInfo();
        } else {
            $this->error('❌ Không thể thiết lập webhook.');
        }
    }

    /**
     * Gỡ bỏ webhook
     */
    protected function removeWebhook()
    {
        if ($this->confirm('Bạn có chắc chắn muốn gỡ bỏ webhook không?', true)) {
            $this->info('Đang gỡ bỏ webhook...');

            $dropPendingUpdates = $this->confirm('Bạn có muốn xóa các cập nhật đang chờ xử lý không?', false);

            $response = Telegram::removeWebhook([
                'drop_pending_updates' => $dropPendingUpdates,
            ]);

            if ($response) {
                $this->info('✅ Đã gỡ bỏ webhook thành công!');
                if ($dropPendingUpdates) {
                    $this->info('Các cập nhật đang chờ xử lý đã được xóa.');
                }
            } else {
                $this->error('❌ Không thể gỡ bỏ webhook.');
            }
        }
    }

    /**
     * Lấy thông tin webhook
     */
    protected function getWebhookInfo()
    {
        $this->info('Đang lấy thông tin webhook...');

        try {
            $webhookInfo = Telegram::getWebhookInfo();
            $info = $webhookInfo->toArray();

            $this->table(
                ['Property', 'Value'],
                [
                    ['URL', $info['url'] ?? 'Not set'],
                    ['Has Custom Certificate', ($info['has_custom_certificate'] ?? false) ? 'Yes' : 'No'],
                    ['Pending Update Count', $info['pending_update_count'] ?? 0],
                    ['Max Connections', $info['max_connections'] ?? 40],
                    ['Last Error Date', isset($info['last_error_date']) ? date('Y-m-d H:i:s', $info['last_error_date']) : 'N/A'],
                    ['Last Error Message', $info['last_error_message'] ?? 'None'],
                    ['IP Address', $info['ip_address'] ?? 'N/A'],
                ]
            );

            if (empty($info['url'])) {
                $this->warn('⚠️ Webhook chưa được thiết lập. Sử dụng lệnh "php artisan telegram:webhook --setup" để thiết lập.');
            } elseif (!empty($info['last_error_message'])) {
                $this->error('⚠️ Webhook có lỗi gần đây: ' . ($info['last_error_message'] ?? 'Unknown error'));
            } else {
                $this->info('✅ Webhook đang hoạt động bình thường.');
            }

            // Hiển thị đường dẫn webhook hiện tại
            if (!empty($info['url'])) {
                $this->info('Đường dẫn webhook hiện tại:');
                $this->line($info['url']);

                $configUrl = config('telegram.bots.mybot.webhook_url');
                if ($configUrl != $info['url']) {
                    $this->warn('⚠️ Đường dẫn webhook trong config khác với đường dẫn đã thiết lập:');
                    $this->line("- Config URL: {$configUrl}");
                    $this->line("- Actual URL: {$info['url']}");
                }
            }
        } catch (\Exception $e) {
            $this->error('Không thể lấy thông tin webhook: ' . $e->getMessage());
            Log::error('Telegram Webhook Info Error', ['error' => $e->getMessage()]);
        }
    }
}
