<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SupplierOrder;

class UpdateSupplierIdAndDeleteDuplicates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'supplier_orders:update_and_cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update supplier_id in supplier_orders table and delete rows with duplicate order_code and supplier_id';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->updateSupplierId();
        $this->deleteDuplicateRows();
        $this->updateStatus();
    }

    /**
     * Update supplier_id in supplier_orders table based on order relationship.
     *
     * @return void
     */
    protected function updateSupplierId()
    {
        $supplierOrders = SupplierOrder::whereNull('handler_id')->get();


        foreach ($supplierOrders as $supplierOrder) {
            if ($supplierOrder->order) {
                $supplierOrder->handler_id = $supplierOrder->order->handler_id;
                $supplierOrder->save();
            }
        }

        $this->info('Supplier IDs updated successfully.');
    }

    /**
     * Delete rows with duplicate order_code and supplier_id.
     *
     * @return void
     */
    protected function deleteDuplicateRows()
    {
        $duplicates = SupplierOrder::select('order_code', 'supplier_id')
            ->groupBy('order_code', 'supplier_id')
            ->havingRaw('COUNT(*) > 1')
            ->get();

        foreach ($duplicates as $duplicate) {
            SupplierOrder::where('order_code', $duplicate->order_code)
                ->where('supplier_id', $duplicate->supplier_id)
                ->where('id', '!=', function ($query) use ($duplicate) {
                    $query->select('id')
                          ->from('supplier_orders')
                          ->where('order_code', $duplicate->order_code)
                          ->where('supplier_id', $duplicate->supplier_id)
                          ->orderBy('id')
                          ->limit(1);
                })
                ->delete();
        }

        $this->info('Duplicate rows deleted successfully.');
    }
    protected function updateStatus()
    {
        SupplierOrder::where('status', 'Processing')
            ->update(['status' => 'AwaitingShipment']);

        $this->info('Statuses updated from Processing to AwaitingShipment successfully.');
    }
}
