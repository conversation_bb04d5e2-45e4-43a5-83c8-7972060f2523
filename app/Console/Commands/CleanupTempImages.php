<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class CleanupTempImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:temp-images';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup old temporary images';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Lấy danh sách folder theo ngày
        $directories = Storage::disk('s3')->directories('products/temp');
        
        $deleted = 0;
        foreach ($directories as $dir) {
            // Lấy ngày từ tên folder (Y-m-d)
            $folderDate = Carbon::createFromFormat('Y-m-d', basename($dir));
            
            // Xóa folder cũ hơn 5 ngày
            if ($folderDate->diffInDays(now()) >= 5) {
                Storage::disk('s3')->deleteDirectory($dir);
                $deleted++;
            }
        }

        $this->info("Deleted {$deleted} old temporary folders");
    }
} 