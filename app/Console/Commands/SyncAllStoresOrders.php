<?php

namespace App\Console\Commands;

use App\Enums\TiktokShopStatus;
use Illuminate\Console\Command;
use App\Models\Store;
use App\Services\Tiktok\TiktokOrderSyncService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncAllStoresOrders extends Command
{
    protected $signature = 'stores:sync-orders {store_id?}';
    protected $description = 'Sync orders from TikTok for all stores';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $store_id = $this->argument('store_id');
        if ($store_id) {
            $stores = Store::where('id', $store_id)
                ->whereIn('tiktok_shop_status', [TiktokShopStatus::Live, TiktokShopStatus::NotConnected])
                ->get();
        } else {
            $stores = Store::where('app_partner_id', '!=', '')
                ->whereIn('tiktok_shop_status', [TiktokShopStatus::Live, TiktokShopStatus::NotConnected])
                ->orderBy('id','desc')
                ->get();
        }
        
        $count = count($stores);
        if (!app()->environment('local', 'development')) {
            $this->info("Total :  {$count}");
        }

        foreach ($stores as $store) {
            try {
                DB::beginTransaction();

                $tiktokOrderSyncService = new TiktokOrderSyncService($store);
                $tiktokOrderSyncService->syncTiktokOrders();

                // Cập nhật thời gian đồng bộ cuối cùng
                $store->update(['last_sync_tiktok_api' => now()]);

                DB::commit();

                if (!app()->environment('local', 'development')) {
                    $this->info("Orders synced for store: {$store->id}");
                }
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error("Error syncing orders for store {$store->id}: " . $e->getMessage());
                if (!app()->environment('local', 'development')) {
                    $this->info("Error syncing orders for store: {$store->id}" . $e->getMessage());
                }
            }
        }
    }
}
