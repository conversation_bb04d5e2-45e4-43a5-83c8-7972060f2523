<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Order;
use App\Livewire\FlashshipForm;
use App\Models\SupplierOrder;

class TestRealFlashShipFulfill extends Command
{
    protected $signature = 'test:real-flashship-fulfill {order_code?}';
    protected $description = 'Test real FlashShip fulfill using FlashshipForm with real order data';

    public function handle()
    {
        $this->info("🧪 TEST REAL FLASHSHIP FULFILL");
        $this->info("==============================\n");

        try {
            // Get order code from argument or find one
            $orderCode = $this->argument('order_code');
            
            if (!$orderCode) {
                // Find an eligible order
                $order = Order::whereIn('store_order_status', ["Processing", "Awaiting Shipment", "Awaiting Collection", "Awaiting"])
                    ->whereDoesntHave('SupplierOrders')
                    ->whereHas('orderItems', function ($query) {
                        $query->whereHas('productVariant', function ($subQuery) {
                            $subQuery->withoutGlobalScope('access')
                                ->where('auto_fulfill', true)
                                ->whereHas('supplier', function ($supplierQuery) {
                                    $supplierQuery->where('name', 'Flashship');
                                });
                        });
                    })
                    ->first();
                
                if (!$order) {
                    $this->error("❌ Không tìm thấy order nào có thể test");
                    return 1;
                }
                
                $orderCode = $order->order_code;
            } else {
                $order = Order::where('order_code', $orderCode)->first();
                if (!$order) {
                    $this->error("❌ Không tìm thấy order: $orderCode");
                    return 1;
                }
            }
            
            $this->info("📋 Testing với order: {$order->order_code}");
            $this->line("   Status: {$order->store_order_status}");
            $this->line("   Items: " . $order->orderItems->count());
            $this->line("   Customer: {$order->shipping_first_name} {$order->shipping_last_name}");
            $this->line("   Address: {$order->shipping_address_line1}");
            $this->line("   City: {$order->shipping_city}");
            $this->newLine();
            
            // Check current SupplierOrders
            $this->info("=== KIỂM TRA SUPPLIERORDERS HIỆN TẠI ===");
            $existingOrders = $order->SupplierOrders()->where('supplier_id', 1)->get();
            $this->line("📊 Existing SupplierOrders: " . $existingOrders->count());
            
            foreach ($existingOrders as $so) {
                $this->line("  🎯 ID: {$so->id} | Supplier Order ID: {$so->supplier_order_id} | Status: {$so->status->value}");
            }
            $this->newLine();
            
            // Initialize FlashshipForm
            $this->info("=== KHỞI TẠO FLASHSHIPFORM ===");
            
            $flashshipForm = new FlashshipForm();
            $flashshipForm->mount($order, 'Flashship');
            
            $this->info("✅ FlashshipForm mounted successfully");
            $this->line("📋 Form data items: " . count($flashshipForm->formData['items'] ?? []));
            $this->newLine();
            
            // Show form data details
            $this->info("=== CHI TIẾT FORM DATA ===");
            if (isset($flashshipForm->formData['items'])) {
                foreach ($flashshipForm->formData['items'] as $index => $item) {
                    $this->line("Item {$index}:");
                    $this->line("  - Selected: " . ($item['selected'] ? 'Yes' : 'No'));
                    $this->line("  - Variant: " . ($item['variant'] ?? 'N/A'));
                    $this->line("  - Quantity: " . ($item['quantity'] ?? 'N/A'));
                    $this->line("  - Front Design: " . (strlen($item['front_design'] ?? '') > 0 ? 'Has design' : 'No design'));
                    $this->line("  - Back Design: " . (strlen($item['back_design'] ?? '') > 0 ? 'Has design' : 'No design'));
                }
            }
            $this->newLine();
            
            // Get prepared order data
            $this->info("=== CHUẨN BỊ ORDER DATA ===");
            
            // Use reflection to access private method
            $reflection = new \ReflectionClass($flashshipForm);
            $method = $reflection->getMethod('prepareOrderData');
            $method->setAccessible(true);
            $orderData = $method->invoke($flashshipForm, false);
            
            $this->line("📦 Prepared order data:");
            $this->line("Order ID: " . $orderData['order_id']);
            $this->line("Customer: {$orderData['buyer_first_name']} {$orderData['buyer_last_name']}");
            $this->line("Email: " . $orderData['buyer_email']);
            $this->line("Phone: " . $orderData['buyer_phone']);
            $this->line("Address: " . $orderData['buyer_address1']);
            $this->line("City: " . $orderData['buyer_city']);
            $this->line("Province: " . $orderData['buyer_province_code']);
            $this->line("Zip: " . $orderData['buyer_zip']);
            $this->line("Country: " . $orderData['buyer_country_code']);
            $this->line("Products count: " . count($orderData['products']));
            
            foreach ($orderData['products'] as $index => $product) {
                $this->line("  Product {$index}: variant_id={$product['variant_id']}, quantity={$product['quantity']}");
            }
            $this->newLine();
            
            // Validate address
            $address = $orderData['buyer_address1'];
            $hasNumber = preg_match('/\d/', $address);
            $hasLetter = preg_match('/[a-zA-Z]/', $address);
            $hasAsterisk = strpos($address, '*') !== false;
            
            $this->info("=== KIỂM TRA ĐỊA CHỈ ===");
            $this->line("Address: '$address'");
            $this->line("Has number: " . ($hasNumber ? 'Yes' : 'No'));
            $this->line("Has letter: " . ($hasLetter ? 'Yes' : 'No'));
            $this->line("Has asterisk: " . ($hasAsterisk ? 'Yes' : 'No'));
            
            if (!$hasNumber || !$hasLetter || $hasAsterisk) {
                $this->warn("⚠️  Address không hợp lệ cho FlashShip API!");
                $this->line("FlashShip yêu cầu: có cả số và chữ, không có ký tự *");
            } else {
                $this->info("✅ Address hợp lệ");
            }
            $this->newLine();
            
            // Ask for confirmation
            if ($this->confirm('Bạn có muốn thực hiện fulfill order này không?')) {
                $this->info("=== THỰC HIỆN FULFILL ===");
                
                try {
                    // Call confirmSubmit like in the UI
                    $flashshipForm->confirmSubmit(false);
                    
                    $this->info("✅ Fulfill completed successfully!");
                    
                    // Check if SupplierOrder was created
                    $order->refresh();
                    $newSupplierOrders = $order->SupplierOrders()->where('supplier_id', 1)->get();
                    $this->line("📊 SupplierOrders after fulfill: " . $newSupplierOrders->count());
                    
                    $latestOrder = $newSupplierOrders->sortByDesc('created_at')->first();
                    if ($latestOrder) {
                        $this->line("🎯 Latest SupplierOrder:");
                        $this->line("   ID: {$latestOrder->id}");
                        $this->line("   Supplier Order ID: " . ($latestOrder->supplier_order_id ?: 'NULL'));
                        $this->line("   Status: {$latestOrder->status->value}");
                        $this->line("   Created: {$latestOrder->created_at}");
                    }
                    
                } catch (\Exception $e) {
                    $this->error("❌ Fulfill failed: " . $e->getMessage());
                    $this->line("Stack trace: " . $e->getTraceAsString());
                }
            } else {
                $this->line("⏭️  Skipped fulfill");
            }
            
            $this->newLine();
            $this->info("🎯 KẾT LUẬN:");
            $this->info("============");
            $this->line("✅ Đã test FlashshipForm với order thật");
            $this->line("✅ Đã kiểm tra data structure và validation");
            $this->line("✅ Đã xác định vấn đề với address format");
            
        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
            $this->line("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
        
        return 0;
    }
}
