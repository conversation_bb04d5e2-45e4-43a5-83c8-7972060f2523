<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SupplierOrder;
use App\Models\Order;
use Illuminate\Support\Facades\DB;

class InvestigateHistoricalData extends Command
{
    protected $signature = 'investigate:historical-data';
    protected $description = 'Investigate historical data to understand why auto-fulfill worked before';

    public function handle()
    {
        $this->info("🔍 ĐIỀU TRA DỮ LIỆU LỊCH SỬ");
        $this->info("============================\n");

        try {
            // Check SupplierOrders created by auto-fulfill
            $this->info("=== KIỂM TRA SUPPLIERORDERS ĐÃ TẠO ===");
            
            $supplierOrders = SupplierOrder::where('supplier_id', 1) // FlashShip
                ->orderBy('created_at', 'desc')
                ->limit(20)
                ->get();
            
            $this->line("📊 Tổng số SupplierOrders FlashShip: " . SupplierOrder::where('supplier_id', 1)->count());
            $this->line("📊 SupplierOrders 30 ngày qua: " . SupplierOrder::where('supplier_id', 1)->where('created_at', '>=', now()->subDays(30))->count());
            $this->line("📊 SupplierOrders 7 ngày qua: " . SupplierOrder::where('supplier_id', 1)->where('created_at', '>=', now()->subDays(7))->count());
            $this->newLine();
            
            if ($supplierOrders->count() > 0) {
                $this->info("📋 Recent SupplierOrders:");
                foreach ($supplierOrders->take(10) as $so) {
                    $this->line("  🎯 {$so->order_code} | {$so->supplier_order_id} | {$so->created_at}");
                    $statusValue = $so->status ? $so->status->value : 'null';
                    $this->line("      Status: {$statusValue} | Data: " . (strlen($so->data ?? '') > 0 ? 'Has data' : 'No data'));
                }
                $this->newLine();
                
                // Analyze the data structure
                $this->info("=== PHÂN TÍCH CẤU TRÚC DATA ===");
                $recentOrder = $supplierOrders->first();
                if ($recentOrder && $recentOrder->data) {
                    $this->line("📋 Sample data structure:");
                    $data = json_decode($recentOrder->data, true);
                    if ($data) {
                        $this->line("Data keys: " . implode(', ', array_keys($data)));
                        $this->line("Sample data: " . json_encode($data, JSON_PRETTY_PRINT));
                    }
                }
                $this->newLine();
            } else {
                $this->warn("❌ Không tìm thấy SupplierOrders nào cho FlashShip!");
                $this->newLine();
            }
            
            // Check if there are orders that should be auto-fulfilled
            $this->info("=== KIỂM TRA ORDERS CÓ THỂ AUTO-FULFILL ===");
            
            $eligibleOrders = Order::whereIn('store_order_status', ["Processing", "Awaiting Shipment", "Awaiting Collection", "Awaiting"])
                ->whereDoesntHave('SupplierOrders')
                ->whereHas('orderItems', function ($query) {
                    $query->whereHas('productVariant', function ($subQuery) {
                        $subQuery->withoutGlobalScope('access')
                            ->where('auto_fulfill', true)
                            ->whereHas('supplier', function ($supplierQuery) {
                                $supplierQuery->where('name', 'Flashship');
                            });
                    });
                })
                ->limit(10)
                ->get();
            
            $this->line("📊 Orders có thể auto-fulfill: " . $eligibleOrders->count());
            
            if ($eligibleOrders->count() > 0) {
                foreach ($eligibleOrders as $order) {
                    $this->line("  🎯 {$order->order_code} | {$order->store_order_status} | {$order->created_at}");
                }
            }
            $this->newLine();
            
            // Check FlashShip API behavior changes
            $this->info("=== KIỂM TRA THAY ĐỔI API BEHAVIOR ===");
            
            // Look for patterns in existing SupplierOrders
            if ($supplierOrders->count() > 0) {
                $this->line("📋 Phân tích supplier_order_id patterns:");
                
                $patterns = [];
                foreach ($supplierOrders as $so) {
                    if ($so->supplier_order_id) {
                        $pattern = $this->analyzeOrderIdPattern($so->supplier_order_id);
                        $patterns[$pattern] = ($patterns[$pattern] ?? 0) + 1;
                    }
                }
                
                foreach ($patterns as $pattern => $count) {
                    $this->line("  - $pattern: $count orders");
                }
                $this->newLine();
            }
            
            // Check if auto-fulfill was working by looking at creation patterns
            $this->info("=== PHÂN TÍCH THỜI GIAN TẠO SUPPLIERORDERS ===");
            
            $creationStats = DB::table('supplier_orders')
                ->where('supplier_id', 1)
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as count'),
                    DB::raw('MIN(created_at) as first_order'),
                    DB::raw('MAX(created_at) as last_order')
                )
                ->groupBy(DB::raw('DATE(created_at)'))
                ->orderBy('date', 'desc')
                ->limit(14)
                ->get();
            
            if ($creationStats->count() > 0) {
                $this->line("📊 SupplierOrders created per day (last 14 days):");
                foreach ($creationStats as $stat) {
                    $this->line("  📅 {$stat->date}: {$stat->count} orders ({$stat->first_order} - {$stat->last_order})");
                }
                $this->newLine();
                
                // Look for gaps that might indicate when auto-fulfill stopped working
                $dates = $creationStats->pluck('date')->toArray();
                $this->info("🔍 Tìm kiếm gaps trong auto-fulfill:");
                
                for ($i = 0; $i < count($dates) - 1; $i++) {
                    $current = \Carbon\Carbon::parse($dates[$i]);
                    $next = \Carbon\Carbon::parse($dates[$i + 1]);
                    $daysDiff = $current->diffInDays($next);
                    
                    if ($daysDiff > 1) {
                        $this->warn("  ⚠️  Gap detected: {$daysDiff} days between {$dates[$i]} and {$dates[$i + 1]}");
                    }
                }
            }
            $this->newLine();
            
            // Check form_data to understand how orders were created
            $this->info("=== PHÂN TÍCH FORM_DATA ===");
            
            $formDataSamples = SupplierOrder::where('supplier_id', 1)
                ->whereNotNull('form_data')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
            
            if ($formDataSamples->count() > 0) {
                $this->line("📋 Sample form_data structures:");
                foreach ($formDataSamples as $sample) {
                    $formData = $sample->form_data;
                    if (is_array($formData)) {
                        $this->line("  🎯 {$sample->order_code}:");
                        $this->line("      Keys: " . implode(', ', array_keys($formData)));
                        if (isset($formData['items']) && is_array($formData['items'])) {
                            $this->line("      Items count: " . count($formData['items']));
                        }
                    }
                }
            }
            $this->newLine();
            
            $this->info("🎯 PHÂN TÍCH KẾT QUẢ:");
            $this->info("====================");
            
            if ($supplierOrders->count() > 0) {
                $this->info("✅ Auto-fulfill ĐÃ TỪNG HOẠT ĐỘNG - có SupplierOrders được tạo");
                
                $recentCount = SupplierOrder::where('supplier_id', 1)
                    ->where('created_at', '>=', now()->subDays(7))
                    ->count();
                
                if ($recentCount == 0) {
                    $this->warn("⚠️  Nhưng KHÔNG CÓ SupplierOrders nào trong 7 ngày qua");
                    $this->line("   → Auto-fulfill có thể đã ngừng hoạt động gần đây");
                } else {
                    $this->info("✅ Vẫn có SupplierOrders được tạo trong 7 ngày qua");
                }
                
                $this->newLine();
                $this->line("🔍 Có thể nguyên nhân:");
                $this->line("1. FlashShip API thay đổi response format");
                $this->line("2. Credentials/environment thay đổi");
                $this->line("3. Logic xử lý response thay đổi (như đã phát hiện)");
                $this->line("4. Điều kiện auto-fulfill thay đổi");
                
            } else {
                $this->warn("❌ CHƯA BAO GIỜ CÓ SupplierOrders FlashShip");
                $this->line("   → Auto-fulfill có thể chưa bao giờ hoạt động");
                $this->line("   → Hoặc có vấn đề với database/config");
            }
            
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    private function analyzeOrderIdPattern($orderId)
    {
        if (is_numeric($orderId)) {
            return 'Numeric';
        } elseif (preg_match('/^FS\d+/', $orderId)) {
            return 'FlashShip Format (FS...)';
        } elseif (preg_match('/^[A-Z]+-/', $orderId)) {
            return 'Custom Format (XXX-...)';
        } else {
            return 'Other';
        }
    }
}
