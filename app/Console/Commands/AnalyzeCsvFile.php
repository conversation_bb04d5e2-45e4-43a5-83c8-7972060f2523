<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\HeadingRowImport;

class AnalyzeCsvFile extends Command
{
    protected $signature = 'csv:analyze {file}';
    protected $description = 'Analyze the structure of a CSV file';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $file = $this->argument('file');

        // Check if the file exists
        if (!file_exists($file)) {
            $this->error("File not found: $file");
            return;
        }

        // Analyze the file headers
        $headings = (new HeadingRowImport)->toArray($file);
        $this->info("File headers:");
        print_r($headings[0][0]);

        // Read the first few rows of the file to check the delimiter
        $data = Excel::toArray([], $file);
  
        $this->info("First few rows:");
        print_r(array_slice($data[0], 0, 5));
    }
}
