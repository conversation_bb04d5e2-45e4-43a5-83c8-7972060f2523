<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Fullfillment\FlashShip;
use App\Models\Order;
use Exception;

class TestFlashShipAPI extends Command
{
    protected $signature = 'test:flashship-api';
    protected $description = 'Test FlashShip API response format and compare with current logic';

    public function handle()
    {
        $this->info("🔍 KIỂM TRA FLASHSHIP API RESPONSE FORMAT");
        $this->info("=========================================\n");

        try {
            // Test 1: Kiểm tra getToken()
            $this->info("=== TEST 1: Kiểm tra getToken() ===");
            $token = FlashShip::getToken();
            
            if ($token) {
                $this->info("✅ Token lấy thành công: " . substr($token, 0, 20) . "...");
            } else {
                $this->error("❌ Không thể lấy token");
                return 1;
            }
            $this->newLine();
            
            // Test 2: Kiểm tra createOrder response format với data không hợp lệ
            $this->info("=== TEST 2: Test createOrder với data mẫu (expect error) ===");
            
            $sampleOrderData = [
                'partner_order_id' => 'TEST_' . time(),
                'shipment' => 1,
                'label' => [
                    'name' => 'Test Customer',
                    'phone' => '0123456789',
                    'address' => 'Test Address',
                    'province' => 'Ho Chi Minh',
                    'district' => 'District 1',
                    'ward' => 'Ward 1'
                ],
                'items' => [
                    [
                        'product_id' => 'test-product',
                        'variant_id' => 'test-variant',
                        'quantity' => 1,
                        'design_front' => 'https://example.com/front.png',
                        'design_back' => 'https://example.com/back.png'
                    ]
                ]
            ];
            
            $this->info("📤 Gửi test order với data:");
            $this->line(json_encode($sampleOrderData, JSON_PRETTY_PRINT));
            
            $response = FlashShip::createOrder($sampleOrderData);
            
            $this->info("📥 Response nhận được:");
            $this->line("Success: " . ($response['success'] ? 'true' : 'false'));
            $this->line("Data: " . json_encode($response['data'], JSON_PRETTY_PRINT));
            $this->line("Message: " . ($response['message'] ?? 'null'));
            $this->newLine();
            
            // Test 3: Kiểm tra getOrder response format
            $this->info("=== TEST 3: Test getOrder với order_code mẫu ===");
            
            $testOrderCode = 'TEST_ORDER_123';
            $orderResponse = FlashShip::getOrder($testOrderCode);
            
            $this->info("📥 getOrder Response:");
            $this->line("Success: " . ($orderResponse['success'] ? 'true' : 'false'));
            $this->line("Data: " . json_encode($orderResponse['data'], JSON_PRETTY_PRINT));
            $this->line("Message: " . ($orderResponse['message'] ?? 'null'));
            $this->newLine();
            
            // Test 4: Kiểm tra getOrderByPartnerOrderId
            $this->info("=== TEST 4: Test getOrderByPartnerOrderId ===");
            
            $partnerOrderResponse = FlashShip::getOrderByPartnerOrderId('TEST_PARTNER_123');
            
            $this->info("📥 getOrderByPartnerOrderId Response:");
            $this->line("Success: " . ($partnerOrderResponse['success'] ? 'true' : 'false'));
            $this->line("Data: " . json_encode($partnerOrderResponse['data'], JSON_PRETTY_PRINT));
            $this->line("Message: " . ($partnerOrderResponse['message'] ?? 'null'));
            $this->newLine();
            
            // Test 5: Phân tích response structure
            $this->info("=== TEST 5: Phân tích Response Structure ===");
            
            $responses = [
                'createOrder' => $response,
                'getOrder' => $orderResponse,
                'getOrderByPartnerOrderId' => $partnerOrderResponse
            ];
            
            foreach ($responses as $method => $resp) {
                $this->line("📋 Method: $method");
                $this->line("  - Has 'success' field: " . (isset($resp['success']) ? 'Yes' : 'No'));
                $this->line("  - Has 'data' field: " . (isset($resp['data']) ? 'Yes' : 'No'));
                $this->line("  - Has 'message' field: " . (isset($resp['message']) ? 'Yes' : 'No'));
                
                if (isset($resp['data'])) {
                    $this->line("  - Data type: " . gettype($resp['data']));
                    if (is_array($resp['data'])) {
                        $this->line("  - Data keys: " . implode(', ', array_keys($resp['data'])));
                    }
                }
                $this->newLine();
            }
            
            // Test 6: So sánh với logic xử lý hiện tại
            $this->info("=== TEST 6: So sánh với logic xử lý hiện tại ===");
            
            $this->line("🔍 Logic hiện tại trong FlashShip::execute():");
            $this->line("return [");
            $this->line("    'success' => isset(\$responseBody['msg']) ? (\$responseBody['msg'] == 'success') : true,");
            $this->line("    'data' => \$responseBody['data'] ?? \$responseBody ?? [],");
            $this->line("    'message' => \$responseBody['err'] ?? null,");
            $this->line("];");
            $this->newLine();
            
            $this->warn("⚠️  PHÂN TÍCH TIỀM ẨN:");
            $this->line("1. Nếu API không trả về 'msg' field → success = true (có thể sai)");
            $this->line("2. Nếu API không trả về 'data' field → data = toàn bộ response");
            $this->line("3. Message chỉ lấy từ 'err' field");
            $this->newLine();
            
            // Test 7: Kiểm tra với real order nếu có
            $this->info("=== TEST 7: Kiểm tra với real order (nếu có) ===");
            
            $realOrder = Order::whereIn('store_order_status', ['Processing', 'Awaiting Shipment'])
                ->first();
            
            if ($realOrder) {
                $this->line("📋 Test với real order: {$realOrder->order_code}");
                $realResponse = FlashShip::getOrder($realOrder->order_code);
                
                $this->info("📥 Real Order Response:");
                $this->line(json_encode($realResponse, JSON_PRETTY_PRINT));
                $this->newLine();
            } else {
                $this->line("ℹ️  Không tìm thấy order thực để test");
                $this->newLine();
            }
            
            $this->info("🎯 KẾT LUẬN:");
            $this->info("============");
            $this->line("✅ Đã test các API endpoints của FlashShip");
            $this->line("✅ Kiểm tra response format và structure");
            $this->line("✅ So sánh với logic xử lý hiện tại");
            $this->warn("⚠️  Cần xem kết quả để xác định có vấn đề gì không");
            
        } catch (Exception $e) {
            $this->error("❌ Lỗi khi test API: " . $e->getMessage());
            $this->line("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
        
        return 0;
    }
}
