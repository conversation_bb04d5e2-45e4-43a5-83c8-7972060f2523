<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Order;
use App\Models\ProductVariant;
use App\Livewire\FlashshipForm;
use App\Services\Tiktok\TiktokShopService;
use Illuminate\Support\Facades\Log;
use App\Services\TelegramNotificationService;
use Illuminate\Support\Carbon;

class AutoFulfillOrders extends Command
{
    protected $signature = 'orders:fulfill-auto';
    protected $description = 'Automatically fulfill eligible orders';

    protected $telegramService;

    public function __construct(TelegramNotificationService $telegramService)
    {
        parent::__construct();
        $this->telegramService = $telegramService;
    }


    public function handle()
    {
        $startTime = microtime(true);

        $this->info("🚀 Bắt đầu Auto-Fulfill Command...");
        Log::channel('auto_fulfill_command')->info("🚀 Bắt đầu Auto-Fulfill Command...");

        // Quét và lấy danh sách đơn hàng đủ điều kiện
        $this->info("🔍 Đang quét đơn hàng...");
        $eligibleOrders = $this->getEligibleOrders();

        $this->info("✅ Quét được {$eligibleOrders->count()} đơn hàng đủ điều kiện");
        Log::channel('auto_fulfill_command')->info("START: Tìm thấy {$eligibleOrders->count()} đơn đủ điều kiện");

        $stats = [
            'total_orders' => $eligibleOrders->count(),
            'processed_orders' => 0,
            'successful_orders' => 0,
            'failed_orders' => 0,
            'skipped_orders' => 0,
        ];

        foreach ($eligibleOrders as $order) {
            $stats['processed_orders']++;

            try {
                $result = $this->processOrder($order);
                if ($result === 'success') {
                    $stats['successful_orders']++;
                } elseif ($result === 'skipped') {
                    $stats['skipped_orders']++;
                } else {
                    $stats['failed_orders']++;
                }
            } catch (\Exception $e) {
                $stats['failed_orders']++;
            }
        }

        // Log process completion with stats
        $stats['total_duration_ms'] = round((microtime(true) - $startTime) * 1000, 2);
        $duration = round($stats['total_duration_ms'] / 1000, 1);
        Log::channel('auto_fulfill_command')->info("COMPLETE: {$stats['successful_orders']} thành công, {$stats['failed_orders']} lỗi, {$stats['skipped_orders']} bỏ qua ({$duration}s)");

        $this->info("Auto-fulfill completed: {$stats['successful_orders']} successful, {$stats['failed_orders']} failed, {$stats['skipped_orders']} skipped out of {$stats['total_orders']} eligible orders.");
        Log::channel('auto_fulfill_command')->info("Auto-fulfill completed: {$stats['successful_orders']} successful, {$stats['failed_orders']} failed, {$stats['skipped_orders']} skipped out of {$stats['total_orders']} eligible orders.");
    }

    /**
     * Lấy danh sách đơn hàng đủ điều kiện auto-fulfill
     * Sử dụng query tối ưu với EXISTS và indexes
     */
    private function getEligibleOrders()
    {
        $oneHourAgo = Carbon::now()->subHour();
        $sevenDaysAgo = Carbon::now()->subDays(7);

        $startTime = microtime(true);

        // Sử dụng EXISTS thay vì JOIN để tránh duplicate và cải thiện performance
        $query = Order::whereIn('store_order_status', ["Processing", "Awaiting Shipment", "Awaiting Collection", "Awaiting"])
            ->whereDoesntHave('SupplierOrders')
            ->where('created_at', '>=', $sevenDaysAgo)
            ->where(function ($query) use ($oneHourAgo) {
                $query->where('fulfillment_type', '!=', 'TIKTOK')
                    ->orWhere(function ($subQuery) use ($oneHourAgo) {
                        $subQuery->where('fulfillment_type', 'TIKTOK')
                            ->where('created_at', '<=', $oneHourAgo);
                    });
            })
            ->whereExists(function ($query) {
                $query->select(\DB::raw(1))
                    ->from('order_items')
                    ->whereColumn('order_items.order_id', 'orders.id')
                    ->whereExists(function ($subQuery) {
                        $subQuery->select(\DB::raw(1))
                            ->from('product_variants')
                            ->whereColumn('product_variants.variant_id', 'order_items.product_variant_id')
                            ->where('product_variants.auto_fulfill', true)
                            ->whereExists(function ($supplierQuery) {
                                $supplierQuery->select(\DB::raw(1))
                                    ->from('suppliers')
                                    ->whereColumn('suppliers.id', 'product_variants.supplier_id')
                                    ->where('suppliers.name', 'Flashship');
                            });
                    });
            })
            // Thêm eager loading để tránh N+1 queries trong processOrder
            ->with([
                'orderItems.productVariant.supplier',
                'orderItems.productVariant.product.design'
            ])
            // ->orderBy('created_at', 'asc')
            ->limit(100);

        // Log query để debug nếu cần
        Log::channel('auto_fulfill_command')->info("QUERY SQL: " . $query->toSql());
        Log::channel('auto_fulfill_command')->info("QUERY BINDINGS: " . json_encode($query->getBindings()));

        $result = $query->get();

        $queryTime = (microtime(true) - $startTime) * 1000;
        Log::channel('auto_fulfill_command')->info("QUERY TIME: {$queryTime}ms");

        return $result;
    }

    private function processOrder(Order $order): string
    {
        // CRITICAL SAFETY CHECK: Verify all items are eligible before fulfilling
        // This includes canAutoFulfill() check for design files
        // Sử dụng eager loaded data để tránh N+1 queries
        foreach ($order->orderItems as $item) {
            $variant = $item->productVariant; // Đã được eager load

            // Nếu không có variant trong eager load, query lại (fallback)
            if (!$variant) {
                $variant = ProductVariant::withoutGlobalScope('access')
                    ->where('variant_id', $item->product_variant_id)
                    ->whereHas('supplier', function ($query) {
                        $query->where('name', 'Flashship');
                    })
                    ->first();
            }

            if (!$variant || !$variant->auto_fulfill || !$variant->canAutoFulfill()) {
                Log::channel('auto_fulfill_command')->warning("SAFETY CHECK FAILED: Order {$order->order_code} has ineligible item {$item->product_variant_id}");
                return 'skipped';
            }

            // Kiểm tra supplier từ eager loaded data
            if (!$variant->supplier || $variant->supplier->name !== 'Flashship') {
                Log::channel('auto_fulfill_command')->warning("SAFETY CHECK FAILED: Order {$order->order_code} - supplier not Flashship for item {$item->product_variant_id}");
                return 'skipped';
            }
        }

        try {
            $this->fulfillOrder($order);
            return 'success';
        } catch (\Exception $e) {
            return 'failed';
        }
    }

    private function fulfillOrder(Order $order)
    {
        $startTime = microtime(true);

        try {
            // Print label for TikTok orders if needed
            if ($order->fulfillment_type === 'TIKTOK' && empty($order->label)) {
                $labelStartTime = microtime(true);
                $this->printLabel($order);
                $order->refresh();
            }

            // Create and submit FlashShip order
            $flashshipStartTime = microtime(true);
            $flashshipForm = new FlashshipForm();
            $flashshipForm->mount($order, 'Flashship');

            // Submit the order and capture any errors
            $flashshipError = null;
            try {
                $result = $flashshipForm->confirmSubmit(true);
            } catch (\Exception $e) {
                $flashshipError = $e->getMessage();
            }
            // Performance metrics can be logged here if needed

            // Check if order was created successfully
            if (!$order->SupplierOrders()->exists()) {

                // Log FlashShip API failure with detailed context
                $orderCode = str_replace(['[', ']'], ['(', ')'], $order->order_code);
                // Log lỗi với format dễ đọc
                $errorData = [
                    'order_id' => $order->id,
                    'order_code' => str_replace(['[', ']'], ['(', ')'], $order->order_code),
                    'seller_id' => $order->seller_id,
                    'flashship_error' => $flashshipError,
                    'result_from_confirmSubmit' => $result ?? null
                ];

                Log::channel('auto_fulfill_orders')->error("FLASHSHIP API FAILED: {$orderCode}\n" . json_encode($errorData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

                $errorMessage = $flashshipError ? "FlashShip API error: {$flashshipError}" : "Order was not created in FlashShip: FlashShip API error during order creation";
                throw new \Exception($errorMessage);
            }

            // Log successful fulfillment with detailed item and variant info
            $supplierOrder = $order->SupplierOrders()->first();
            $orderCode = str_replace(['[', ']'], ['(', ')'], $order->order_code);

            // Collect detailed item information
            $itemsInfo = [];
            foreach ($order->orderItems as $item) {
                $variant = $item->productVariant;
                $itemsInfo[] = [
                    'item_id' => $item->id,
                    'product_variant_id' => $item->product_variant_id,
                    'variant_name' => $variant->variant_name ?? 'Unknown',
                    'variant_fulfill_name' => $variant->variant_fulfill_name ?? null,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'total_price' => $item->quantity * $item->unit_price,
                    'supplier_name' => $variant->supplier->name ?? 'Unknown',
                    'auto_fulfill_enabled' => $variant->auto_fulfill ?? false,
                    'design_files' => [
                        'front' => !empty($variant->design_front_url),
                        'back' => !empty($variant->design_back_url),
                        'sleeve_left' => !empty($variant->sleeve_left_design_url),
                        'sleeve_right' => !empty($variant->sleeve_right_design_url),
                    ],
                ];
            }

            Log::channel('auto_fulfill_orders')->info("SUCCESS: {$orderCode}", [
                'order_id' => $order->id,
                'order_code' => str_replace(['[', ']'], ['(', ')'], $order->order_code),
                'seller_id' => $order->seller_id,
                'store_id' => $order->store_id,
                'fulfillment_type' => $order->fulfillment_type,
                'store_order_status' => $order->store_order_status,
                'supplier_order_id' => $supplierOrder->id ?? null,
                'tracking_number' => $supplierOrder->tracking_number ?? null,
                'total_items' => $order->orderItems->count(),
                'total_quantity' => $order->orderItems->sum('quantity'),
                'total_amount' => $order->orderItems->sum(function($item) {
                    return $item->quantity * $item->unit_price;
                }),
                'items_detail' => $itemsInfo,
            ]);


            $this->telegramService->sendFulfillmentNotification("✅ Order {$order->order_code} auto-fulfilled successfully.");
        } catch (\Exception $e) {
            $this->telegramService->sendFulfillmentNotification("❌ Failed to auto-fulfill order {$order->order_code}: " . $e->getMessage());
            throw $e; // Re-throw to be caught in processOrder
        }
    }

    private function printLabel(Order $order)
    {
        try {
            $tiktok = new TiktokShopService($order->store);
            $label = $tiktok->syncLabelTracking($order);
            $order->label = $label;
            $order->save();

            // Label printing success - can be logged if needed
        } catch (\Exception $e) {
            // Label printing failure - can be logged if needed
            $this->telegramService->sendFulfillmentNotification("❌ Failed to print label for order {$order->order_code}: " . $e->getMessage());
            throw $e; // Re-throw the exception to be caught in the fulfillOrder method
        }
    }
}
