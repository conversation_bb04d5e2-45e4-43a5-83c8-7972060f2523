<?php

namespace App\Console\Commands;

use App\Models\Store;
use App\Models\TiktokChannel;
use App\Models\TiktokVideo;
use App\Services\Tiktok\TiktokStoreService;
use App\Services\Tiktok\TiktokApiService;
use Illuminate\Console\Command;
use Exception;

class SyncTiktokChannels extends Command
{
    protected $signature = 'tiktok:sync-channels {--store-id= : Sync specific store ID} {--limit= : Limit number of stores to process} {--offset= : Skip first N stores} {--debug : Show detailed debug information}';
    protected $description = 'Sync TikTok channels data from stores and fetch videos via RapidAPI';

    public function handle()
    {
        $this->info('Starting TikTok channels synchronization...');
        
        try {
            $storeId = $this->option('store-id');
            $limit = $this->option('limit');
            $offset = $this->option('offset');
            
            if ($storeId) {
                $stores = Store::where('id', $storeId)->get();
            } else {
                $stores = $this->getConfiguredStores($limit, $offset);
            }
            
            if ($stores->isEmpty()) {
                $this->warn('No configured stores found.');
                return 0;
            }
            
            $totalStores = $this->getTotalConfiguredStoresCount();
            $processingCount = $stores->count();
            
            $this->info("Found {$totalStores} total configured stores. Processing {$processingCount} stores" . 
                       ($offset ? " (starting from #{$offset})" : '') . 
                       ($limit ? " (limit: {$limit})" : ''));
            
            $tiktokApiService = new TiktokApiService();
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($stores as $index => $store) {
                $storeNumber = ($offset ?? 0) + $index + 1;
                $this->info("Processing store #{$storeNumber}: {$store->name} (ID: {$store->id})");
                
                try {
                    $username = $this->getStoreUsername($store);
                    
                    if (!$username) {
                        $this->warn("  ⚠️  Skipping store {$store->name} - No valid username found");
                        $errorCount++;
                        continue;
                    }
                    
                    $this->info("  Found username: {$username}");
                    
                    $channel = $this->createOrUpdateChannel($store, $username, $tiktokApiService);
                    $this->syncChannelVideos($channel, $username, $tiktokApiService);
                    
                    $this->info("  ✅ Successfully synced channel for {$username}");
                    $successCount++;
                    
                } catch (Exception $e) {
                    $this->error("  ❌ Error processing store {$store->name}: " . $e->getMessage());
                    $errorCount++;
                }
            }
            
            $this->info("\n📊 Sync Summary:");
            $this->info("✅ Success: {$successCount} stores");
            $this->info("❌ Errors: {$errorCount} stores");
            $this->info("📝 Total processed: " . ($successCount + $errorCount) . " stores");
            
            $this->info('TikTok channels synchronization completed!');
            return 0;
            
        } catch (Exception $e) {
            $this->error('Sync failed: ' . $e->getMessage());
            return 1;
        }
    }
    
    protected function getConfiguredStores($limit = null, $offset = null)
    {
        $query = Store::whereNotNull('access_token')
            ->whereNotNull('app_partner_id')
            ->whereHas('partnerApp', function ($query) {
                $query->whereNotNull('app_key')
                      ->whereNotNull('app_secret');
            })
            ->orderBy('id');
        
        if ($offset) {
            $query->skip($offset);
        }
        
        if ($limit) {
            $query->take($limit);
        }
        
        return $query->get();
    }
    
    protected function getTotalConfiguredStoresCount()
    {
        return Store::whereNotNull('access_token')
            ->whereNotNull('app_partner_id')
            ->whereHas('partnerApp', function ($query) {
                $query->whereNotNull('app_key')
                      ->whereNotNull('app_secret');
            })
            ->count();
    }
    
    protected function getStoreUsername(Store $store): ?string
    {
        try {
            $tiktokService = new TiktokStoreService($store);
            $profile = $tiktokService->getFormattedStoreProfile();
            
            // Debug output if requested
            if ($this->option('debug')) {
                $this->info("  Debug: Raw profile data for store {$store->name}:");
                $this->line("  " . json_encode($profile, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            }
            
            // Check if the profile contains error messages
            if (is_string($profile) && (
                strpos($profile, 'Error') !== false || 
                strpos($profile, 'error') !== false ||
                strpos($profile, 'fail') !== false ||
                strpos($profile, 'loading') !== false
            )) {
                $this->warn("  TikTok API returned error for store {$store->name}: {$profile}");
                return null;
            }
            
            // Extract username from various possible locations
            $username = null;
            
            if (is_array($profile)) {
                $username = $profile['shop_data']['seller_name'] ?? 
                           $profile['seller_info']['seller_name'] ?? 
                           $profile['seller_info']['username'] ?? 
                           $profile['shop_data']['username'] ?? 
                           $profile['username'] ?? 
                           $profile['shop_name'] ?? 
                           $profile['seller_name'] ??
                           null;
            }
            
            // Validate username
            if (!$username || 
                strlen($username) < 2 || 
                strpos($username, 'Error') !== false ||
                strpos($username, 'error') !== false ||
                strpos($username, 'loading') !== false) {
                
                if ($this->option('debug')) {
                    $this->warn("  Debug: Invalid username '{$username}' extracted for store {$store->name}");
                    $this->line("  Available keys in profile: " . implode(', ', array_keys($profile ?? [])));
                }
                
                $this->warn("  Invalid username extracted for store {$store->name}");
                return null;
            }
            
            return $username;
                   
        } catch (Exception $e) {
            $this->warn("  Exception getting profile for store {$store->name}: " . $e->getMessage());
            if ($this->option('debug')) {
                $this->error("  Debug: Exception trace: " . $e->getTraceAsString());
            }
            return null;
        }
    }
    
    protected function createOrUpdateChannel(Store $store, string $username, TiktokApiService $apiService): TiktokChannel
    {
        try {
            $profileData = $apiService->getUserProfile($username);
            $userInfo = $profileData['user'] ?? [];
            
            $channel = TiktokChannel::updateOrCreate(
                ['username' => $username],
                [
                    'user_id' => $store->owner_id ?? 1,
                    'display_name' => $userInfo['display_name'] ?? $username,
                    'description' => $userInfo['bio'] ?? null,
                    'avatar_url' => $userInfo['avatar_url'] ?? null,
                    'follower_count' => $userInfo['follower_count'] ?? 0,
                    'following_count' => $userInfo['following_count'] ?? 0,
                    'video_count' => $userInfo['video_count'] ?? 0,
                    'likes_count' => $userInfo['likes_count'] ?? 0,
                    'is_verified' => $userInfo['is_verified'] ?? false,
                    'is_active' => true,
                    'api_data' => $profileData,
                    'last_synced_at' => now(),
                ]
            );
            
            return $channel;
            
        } catch (Exception $e) {
            $this->warn("  Could not fetch profile via API for {$username}, creating basic channel");
            
            return TiktokChannel::updateOrCreate(
                ['username' => $username],
                [
                    'user_id' => $store->owner_id ?? 1,
                    'display_name' => $username,
                    'is_active' => true,
                    'last_synced_at' => now(),
                ]
            );
        }
    }
    
    protected function syncChannelVideos(TiktokChannel $channel, string $username, TiktokApiService $apiService): void
    {
        try {
            $videosData = $apiService->getUserVideos($username, 20);
            $videos = $videosData['videos'] ?? [];
            
            $this->info("    Found " . count($videos) . " videos");
            
            foreach ($videos as $videoData) {
                $this->syncSingleVideo($channel, $videoData);
            }
            
            $channel->update([
                'video_count' => count($videos),
                'last_synced_at' => now(),
            ]);
            
        } catch (Exception $e) {
            $this->warn("    Could not fetch videos for {$username}: " . $e->getMessage());
        }
    }
    
    protected function syncSingleVideo(TiktokChannel $channel, array $videoData): void
    {
        $statistics = $videoData['statistics'] ?? [];
        
        TiktokVideo::updateOrCreate(
            [
                'video_id' => $videoData['video_id'],
                'tiktok_channel_id' => $channel->id,
            ],
            [
                'description' => $videoData['description'] ?? null,
                'create_time' => isset($videoData['create_time']) ? 
                    \Carbon\Carbon::createFromTimestamp($videoData['create_time']) : null,
                'author' => $videoData['author'] ?? null,
                'author_id' => $videoData['author_id'] ?? null,
                'author_name' => $videoData['author_name'] ?? null,
                'number_of_comments' => $statistics['number_of_comments'] ?? 0,
                'number_of_hearts' => $statistics['number_of_hearts'] ?? 0,
                'number_of_plays' => $statistics['number_of_plays'] ?? 0,
                'number_of_reposts' => $statistics['number_of_reposts'] ?? 0,
                'number_of_saves' => $statistics['number_of_saves'] ?? 0,
                'cover_url' => $videoData['cover'] ?? null,
                'download_url' => $videoData['download_url'] ?? null,
                'unwatermarked_download_url' => $videoData['unwatermarked_download_url'] ?? null,
                'video_definition' => $videoData['video_definition'] ?? null,
                'bitrate' => $videoData['bitrate'] ?? null,
                'duration' => $videoData['duration'] ?? null,
                'avatar_thumb' => $videoData['avatar_thumb'] ?? null,
                'raw_data' => $videoData,
            ]
        );
    }
}
