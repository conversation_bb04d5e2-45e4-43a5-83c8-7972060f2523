<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\FlashSale;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class UpdateFlashSaleRenewalStatus extends Command
{
    protected $signature = 'flashsale:update-renewal-status';
    protected $description = 'Update needs_renewal status for all active flash sales';

    public function handle()
    {
        $this->info('Updating flash sale renewal status...');
        
        // Get all ongoing flash sales
        $flashSales = FlashSale::where('status', 'ONGOING')->get();
        
        $now = now();
        $isUSSleepingTime = $now->hour >= 0 && $now->hour <= 6; // 0h-6h sáng US time
        
        foreach ($flashSales as $flashSale) {
            if (!$flashSale->end_time) {
                continue;
            }
            
            $endTime = Carbon::parse($flashSale->end_time);
            $now = now();
            // Sử dụng $now->diffInHours($endTime) để có số dương khi end_time ở tương lai
            $hoursUntilEnd = $now->diffInHours($endTime);
            
            // Nếu đang là giờ ngủ ở US và còn dưới 24h, ưu tiên gia hạn ngay
            $needsRenewal = $isUSSleepingTime ? $hoursUntilEnd <= 24 : $hoursUntilEnd <= 12;
            
            // Only update if needs_renewal status has changed
            if ($needsRenewal !== $flashSale->needs_renewal) {
                $flashSale->needs_renewal = $needsRenewal;
                $flashSale->hours_until_renewal = $hoursUntilEnd;
                $flashSale->save();
                
                if ($needsRenewal) {
                    $this->info("Marking flash sale for renewal: " . $flashSale->title);
                    Log::info("Marking flash sale for renewal", [
                        'title' => $flashSale->title,
                        'hours_until_end' => $hoursUntilEnd,
                        'us_sleeping_time' => $isUSSleepingTime
                    ]);
                }
            }
        }
        
        $this->info('Flash sale renewal status update completed.');
    }
}
