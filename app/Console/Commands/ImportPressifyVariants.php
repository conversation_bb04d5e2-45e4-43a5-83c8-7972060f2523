<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SupplierProduct;
use App\Models\Supplier;
use Illuminate\Support\Facades\Http;
use App\Traits\StyleNormalization;

class ImportPressifyVariants extends Command
{
    use StyleNormalization;

    protected $signature = 'import:pressify-variants';
    protected $description = 'Import variants from Pressify API';

    public function handle()
    {
        try {
            $supplier = Supplier::firstOrCreate(['name' => 'Pressify']);
            $this->info("Using supplier: {$supplier->name} (ID: {$supplier->id})");

            $response = Http::get('https://pressify.us/api/products');
            if (!$response->successful()) {
                $this->error("Failed to fetch variants from Pressify API");
                return 1;
            }

            $variants = $response->json('data');
            $importCount = 0;
            $updateCount = 0;

            foreach ($variants as $variant) {
                try {
                    $normalizedData = [
                        'variant_id' => (string)$variant['variant_id'],
                        'style' => $this->normalizeStyle($variant['style']),
                        'color' => strtoupper($this->normalizeColor($variant['color'])),
                        'size' => $this->normalizeSize($variant['size']),
                        'active' => (bool)$variant['active'],
                        'price' => floatval($variant['price']),
                        'tiktok' => true
                    ];

                    $product = SupplierProduct::updateOrCreate(
                        [
                            'supplier_id' => $supplier->id,
                            'sku' => $variant['sku']
                        ],
                        $normalizedData
                    );

                    if ($product->wasRecentlyCreated) {
                        $importCount++;
                        $this->info("Created: {$variant['sku']} (Color: {$normalizedData['color']})");
                    } else {
                        $updateCount++;
                        $this->info("Updated: {$variant['sku']} (Color: {$normalizedData['color']})");
                    }

                } catch (\Exception $e) {
                    $this->error("Error processing variant {$variant['sku']}: " . $e->getMessage());
                }
            }

            $this->reportUnknownValues();
            $this->info("\nImport completed:");
            $this->info("- Created: {$importCount}");
            $this->info("- Updated: {$updateCount}");
            return 0;

        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return 1;
        }
    }
}