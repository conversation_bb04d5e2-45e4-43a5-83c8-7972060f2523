<?php

namespace App\Console\Commands;

use App\Models\SupplierOrder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class ScanDuplicateVariantOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scan:duplicate-variant-orders
                            {--from= : Start date (Y-m-d format)}
                            {--to= : End date (Y-m-d format)}
                            {--supplier= : Filter by supplier name}
                            {--export : Export results to CSV}
                            {--fix : Attempt to fix affected orders}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scan supplier orders for duplicate variants with different designs (bug detection)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Scanning for Supplier Orders with duplicate variants...');

        // Parse date filters
        $fromDate = $this->option('from') ? Carbon::parse($this->option('from'))->startOfDay() : null;
        $toDate = $this->option('to') ? Carbon::parse($this->option('to'))->endOfDay() : null;
        $supplierFilter = $this->option('supplier');

        if ($fromDate && $toDate) {
            $this->info("📅 Date range: {$fromDate->format('Y-m-d')} to {$toDate->format('Y-m-d')}");
        }

        if ($supplierFilter) {
            $this->info("🏭 Supplier filter: {$supplierFilter}");
        }

        // Build query
        $query = SupplierOrder::whereNotNull('form_data');

        if ($fromDate) {
            $query->where('created_at', '>=', $fromDate);
        }

        if ($toDate) {
            $query->where('created_at', '<=', $toDate);
        }

        if ($supplierFilter) {
            $query->whereHas('supplier', function($q) use ($supplierFilter) {
                $q->where('name', 'like', "%{$supplierFilter}%");
            });
        }

        $supplierOrders = $query->with(['supplier', 'order', 'seller'])->get();

        $this->info("📊 Found {$supplierOrders->count()} supplier orders to analyze");

        $affectedOrders = [];
        $totalAnalyzed = 0;
        $multiItemOrders = 0;
        $duplicateVariantOrders = 0;

        $progressBar = $this->output->createProgressBar($supplierOrders->count());
        $progressBar->start();

        foreach ($supplierOrders as $supplierOrder) {
            $progressBar->advance();

            try {
                $formData = $supplierOrder->form_data;

                // Handle both array and JSON string
                if (is_string($formData)) {
                    $formData = json_decode($formData, true);
                }

                if (!isset($formData['items']) || !is_array($formData['items'])) {
                    continue;
                }

                $totalAnalyzed++;

                // Get selected items
                $selectedItems = array_filter($formData['items'], function($item) {
                    return isset($item['selected']) && $item['selected'] === true;
                });

                if (count($selectedItems) < 2) {
                    continue; // Skip orders with 2 or fewer items
                }

                $multiItemOrders++;

                // Check for duplicate variants with different designs
                $variantDesignMap = [];
                $hasDuplicateVariants = false;

                foreach ($selectedItems as $itemId => $item) {
                    $variant = $item['variant'] ?? null;
                    $design = $item['design'] ?? null;

                    if (!$variant || !$design) {
                        continue;
                    }

                    if (isset($variantDesignMap[$variant])) {
                        // Same variant found, check if different design
                        if ($variantDesignMap[$variant] !== $design) {
                            $hasDuplicateVariants = true;
                            break;
                        }
                    } else {
                        $variantDesignMap[$variant] = $design;
                    }
                }

                if ($hasDuplicateVariants) {
                    $duplicateVariantOrders++;

                    $affectedOrders[] = [
                        'supplier_order_id' => $supplierOrder->id,
                        'order_code' => $supplierOrder->order_code,
                        'original_order_code' => $supplierOrder->order?->order_code,
                        'supplier_name' => $supplierOrder->supplier?->name,
                        'seller_name' => $supplierOrder->seller?->name,
                        'created_at' => $supplierOrder->created_at->format('Y-m-d H:i:s'),
                        'item_count' => count($selectedItems),
                        'variant_design_map' => $variantDesignMap,
                        'duplicate_variants' => $this->findDuplicateVariants($selectedItems),
                    ];
                }

            } catch (\Exception $e) {
                $this->error("Error processing supplier order {$supplierOrder->id}: " . $e->getMessage());
                continue;
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        // Display results
        $this->displayResults($totalAnalyzed, $multiItemOrders, $duplicateVariantOrders, $affectedOrders);

        // Export to CSV if requested
        if ($this->option('export')) {
            $this->exportToCsv($affectedOrders);
        }

        // Fix orders if requested
        if ($this->option('fix') && !empty($affectedOrders)) {
            $this->fixAffectedOrders($affectedOrders);
        }

        return 0;
    }

    private function findDuplicateVariants(array $selectedItems): array
    {
        $variantCounts = [];
        $duplicates = [];

        foreach ($selectedItems as $item) {
            $variant = $item['variant'] ?? null;
            if ($variant) {
                $variantCounts[$variant] = ($variantCounts[$variant] ?? 0) + 1;
            }
        }

        foreach ($variantCounts as $variant => $count) {
            if ($count > 1) {
                $duplicates[$variant] = $count;
            }
        }

        return $duplicates;
    }

    private function displayResults(int $totalAnalyzed, int $multiItemOrders, int $duplicateVariantOrders, array $affectedOrders): void
    {
        $this->info('📈 SCAN RESULTS:');
        $this->table(
            ['Metric', 'Count', 'Percentage'],
            [
                ['Total Analyzed', number_format($totalAnalyzed), '100%'],
                ['Multi-item Orders (>2 items)', number_format($multiItemOrders), $totalAnalyzed > 0 ? round(($multiItemOrders / $totalAnalyzed) * 100, 2) . '%' : '0%'],
                ['🚨 Affected by Bug', number_format($duplicateVariantOrders), $totalAnalyzed > 0 ? round(($duplicateVariantOrders / $totalAnalyzed) * 100, 2) . '%' : '0%'],
            ]
        );

        if (!empty($affectedOrders)) {
            $this->error("🚨 Found {$duplicateVariantOrders} orders affected by duplicate variant bug!");
            $this->newLine();

            // Show first 10 affected orders
            $displayOrders = array_slice($affectedOrders, 0, 10);
            $this->table(
                ['Supplier Order ID', 'Order Code', 'Original Order', 'Supplier', 'Items', 'Created At'],
                array_map(function($order) {
                    return [
                        $order['supplier_order_id'],
                        $order['order_code'],
                        $order['original_order_code'] ?? 'N/A',
                        $order['supplier_name'] ?? 'N/A',
                        $order['item_count'],
                        $order['created_at'],
                    ];
                }, $displayOrders)
            );

            if (count($affectedOrders) > 10) {
                $this->info("... and " . (count($affectedOrders) - 10) . " more orders");
            }
        } else {
            $this->info('✅ No orders found with duplicate variant bug!');
        }
    }

    private function exportToCsv(array $affectedOrders): void
    {
        if (empty($affectedOrders)) {
            $this->info('No data to export.');
            return;
        }

        $filename = 'duplicate_variant_orders_' . date('Y-m-d_H-i-s') . '.csv';

        // Create CSV content in memory
        $csvContent = '';
        $handle = fopen('php://temp', 'r+');

        // CSV Headers
        fputcsv($handle, [
            'Supplier Order ID',
            'Order Code',
            'Original Order Code',
            'Supplier Name',
            'Seller Name',
            'Item Count',
            'Created At',
            'Duplicate Variants',
            'Variant-Design Map'
        ]);

        // CSV Data
        foreach ($affectedOrders as $order) {
            fputcsv($handle, [
                $order['supplier_order_id'],
                $order['order_code'],
                $order['original_order_code'] ?? 'N/A',
                $order['supplier_name'] ?? 'N/A',
                $this->convertToEnglishName($order['seller_name'] ?? 'N/A'),
                $order['item_count'],
                $order['created_at'],
                json_encode($order['duplicate_variants']),
                json_encode($order['variant_design_map'])
            ]);
        }

        // Get CSV content
        rewind($handle);
        $csvContent = stream_get_contents($handle);
        fclose($handle);

        try {
            // Upload to S3 only
            $s3Path = 'exports/duplicate-variant-orders/' . $filename;
            Storage::disk('s3')->put($s3Path, $csvContent);

            // Generate public URL
            $url = Storage::disk('s3')->url($s3Path);

            $this->info("📄 Results exported successfully!");
            $this->info("🔗 Download link: {$url}");
            $this->info("📊 Total records: " . count($affectedOrders));
            $this->info("💾 File uploaded to S3 only (not saved on server)");

        } catch (\Exception $e) {
            $this->error("❌ Failed to upload to S3: " . $e->getMessage());
            $this->error("💥 Export failed - file not saved anywhere");
            $this->info("Please check S3 configuration and try again");
        }
    }

    private function convertToEnglishName(string $name): string
    {
        // Vietnamese to English character mapping
        $vietnamese = [
            'à', 'á', 'ạ', 'ả', 'ã', 'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ',
            'è', 'é', 'ẹ', 'ẻ', 'ẽ', 'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',
            'ì', 'í', 'ị', 'ỉ', 'ĩ',
            'ò', 'ó', 'ọ', 'ỏ', 'õ', 'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ', 'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ',
            'ù', 'ú', 'ụ', 'ủ', 'ũ', 'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ',
            'ỳ', 'ý', 'ỵ', 'ỷ', 'ỹ',
            'đ',
            'À', 'Á', 'Ạ', 'Ả', 'Ã', 'Â', 'Ầ', 'Ấ', 'Ậ', 'Ẩ', 'Ẫ', 'Ă', 'Ằ', 'Ắ', 'Ặ', 'Ẳ', 'Ẵ',
            'È', 'É', 'Ẹ', 'Ẻ', 'Ẽ', 'Ê', 'Ề', 'Ế', 'Ệ', 'Ể', 'Ễ',
            'Ì', 'Í', 'Ị', 'Ỉ', 'Ĩ',
            'Ò', 'Ó', 'Ọ', 'Ỏ', 'Õ', 'Ô', 'Ồ', 'Ố', 'Ộ', 'Ổ', 'Ỗ', 'Ơ', 'Ờ', 'Ớ', 'Ợ', 'Ở', 'Ỡ',
            'Ù', 'Ú', 'Ụ', 'Ủ', 'Ũ', 'Ư', 'Ừ', 'Ứ', 'Ự', 'Ử', 'Ữ',
            'Ỳ', 'Ý', 'Ỵ', 'Ỷ', 'Ỹ',
            'Đ'
        ];

        $english = [
            'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
            'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
            'i', 'i', 'i', 'i', 'i',
            'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
            'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
            'y', 'y', 'y', 'y', 'y',
            'd',
            'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A',
            'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E',
            'I', 'I', 'I', 'I', 'I',
            'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
            'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U',
            'Y', 'Y', 'Y', 'Y', 'Y',
            'D'
        ];

        return str_replace($vietnamese, $english, $name);
    }

    private function fixAffectedOrders(array $affectedOrders): void
    {
        $this->warn('🔧 Fix functionality not implemented yet.');
        $this->info('This would require careful analysis of each case to determine the correct action.');
        $this->info('Possible fixes:');
        $this->info('- Split orders with different designs into separate supplier orders');
        $this->info('- Update form_data to remove grouping logic');
        $this->info('- Notify customers about potential issues');
    }
}
