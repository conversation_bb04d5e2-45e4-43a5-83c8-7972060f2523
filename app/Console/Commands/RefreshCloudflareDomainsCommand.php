<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use GuzzleHttp\Client;
use App\Models\CloudflareDomain;
use Illuminate\Support\Facades\Log;
use App\Models\CloudflareApiKey;

class RefreshCloudflareDomainsCommand extends Command
{
    protected $signature = 'cloudflare:refresh-domains';
    protected $description = 'Refresh all Cloudflare domains data';

    protected function getClient(CloudflareApiKey $apiKey): Client
    {
        return new Client([
            'base_uri' => 'https://api.cloudflare.com/client/v4/',
            'headers' => [
                'X-Auth-Email' => $apiKey->email,
                'X-Auth-Key' => $apiKey->api_key,
                'Content-Type' => 'application/json'
            ],
            'timeout' => 30,
        ]);
    }

    public function handle()
    {
        try {
            $apiKeys = CloudflareApiKey::where('is_active', true)->get();
            $totalDomains = 0;
            $updatedCount = 0;
            $errorCount = 0;

            foreach ($apiKeys as $apiKey) {
                $this->info("Processing with API key: {$apiKey->name}");
                $client = $this->getClient($apiKey);
                
                $page = 1;

                do {
                    $this->info("Processing page {$page}...");
                    
                    $response = $client->get('zones', [
                        'query' => [
                            'per_page' => 50,
                            'page' => $page,
                            'status' => 'active,pending,initializing,moved',
                        ]
                    ]);

                    $data = json_decode($response->getBody(), true);
                    
                    if (empty($data['result'])) {
                        break;
                    }

                    foreach ($data['result'] as $zone) {
                        try {
                            // Get SSL status
                            $sslResponse = $client->get("zones/{$zone['id']}/settings/ssl");
                            $sslData = json_decode($sslResponse->getBody(), true);
                            $sslStatus = !empty($sslData['result']['value']) && $sslData['result']['value'] !== 'off';

                            // Get Email status
                            $emailResponse = $client->get("zones/{$zone['id']}/email/routing");
                            $emailData = json_decode($emailResponse->getBody(), true);
                            $emailEnabled = !empty($emailData['result']['enabled']);

                            CloudflareDomain::updateOrCreate(
                                ['id' => $zone['id']],
                                [
                                    'name' => $zone['name'],
                                    'status' => $zone['status'],
                                    'nameservers' => $zone['name_servers'] ?? [],
                                    'ssl_status' => $sslStatus,
                                    'email_enabled' => $emailEnabled,
                                    'cloudflare_api_key_id' => $apiKey->id
                                ]
                            );

                            $updatedCount++;
                            $this->info("Updated domain: {$zone['name']}");

                        } catch (\Exception $e) {
                            $errorCount++;
                            $this->error("Error updating domain {$zone['name']}: " . $e->getMessage());
                            
                            // Update basic info only
                            CloudflareDomain::updateOrCreate(
                                ['id' => $zone['id']],
                                [
                                    'name' => $zone['name'],
                                    'status' => $zone['status'],
                                    'nameservers' => $zone['name_servers'] ?? [],
                                ]
                            );
                        }

                        usleep(200000); // 0.2 second delay
                    }

                    $totalDomains += count($data['result']);
                    $page++;

                    if (empty($data['result_info']['total_pages']) || $page > $data['result_info']['total_pages']) {
                        break;
                    }

                    sleep(1); // 1 second delay between pages

                } while (true);
            }

            $summary = "Refresh complete: Updated {$updatedCount} of {$totalDomains} domains (Errors: {$errorCount})";
            $this->info($summary);
            Log::info($summary);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("Fatal error: " . $e->getMessage());
            Log::error("Fatal error in cloudflare:refresh-domains: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
}