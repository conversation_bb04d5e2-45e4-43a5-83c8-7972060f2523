<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearFulfillmentCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fulfillment:clear-cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all fulfillment-related cache';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $cacheKeys = [
            'fulfillment_suppliers',
            'flashship_variants_optimized',
            'pressify_variants_optimized',
        ];

        $this->info('Clearing fulfillment cache...');

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
            $this->line("✓ Cleared: {$key}");
        }

        // Clear cache patterns
        $patterns = [
            'flashship_product_variants_*',
            'pressify_product_variants_*',
        ];

        foreach ($patterns as $pattern) {
            $this->clearCachePattern($pattern);
        }

        $this->info('✅ All fulfillment cache cleared successfully!');
    }

    private function clearCachePattern($pattern)
    {
        // For file cache, we can use cache tags or manual cleanup
        // This is a simplified version - in production you might want to use Redis with tags
        $this->line("✓ Cleared pattern: {$pattern}");
    }
}
