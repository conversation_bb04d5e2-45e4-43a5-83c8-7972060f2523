<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TikTokPayment;
use Carbon\Carbon;

class CheckTikTokPaymentStats extends Command
{
    protected $signature = 'check:tiktok-payment-stats';
    protected $description = 'Check TikTok Payment statistics for debugging';

    public function handle()
    {
        $this->info('=== KIỂM TRA THỐNG KÊ TIKTOK PAYMENTS ===');
        
        // Kiểm tra tổng số records trong bảng
        $totalRecords = TikTokPayment::count();
        $this->info("Tổng số records trong bảng tiktok_payments: {$totalRecords}");
        
        if ($totalRecords == 0) {
            $this->warn('Không có dữ liệu TikTok Payments nào trong database!');
            return;
        }
        
        // Kiểm tra các status có trong database
        $statuses = TikTokPayment::select('status')
            ->groupBy('status')
            ->pluck('status')
            ->toArray();
        $this->info('Các status có trong database: ' . implode(', ', $statuses));
        
        // Kiểm tra số lượng theo status
        foreach ($statuses as $status) {
            $count = TikTokPayment::where('status', $status)->count();
            $this->info("- Status '{$status}': {$count} records");
        }
        
        // Kiểm tra dữ liệu 30 ngày gần đây
        $thirtyDaysAgo = Carbon::now()->subDays(30);
        $this->info("\n=== THỐNG KÊ 30 NGÀY GẦN ĐÂY (từ {$thirtyDaysAgo->format('Y-m-d')}) ===");
        
        $recentRecords = TikTokPayment::where('paid_time', '>=', $thirtyDaysAgo)->count();
        $this->info("Tổng records có paid_time >= 30 ngày trước: {$recentRecords}");
        
        $paidRecords = TikTokPayment::where('paid_time', '>=', $thirtyDaysAgo)
            ->where('status', 'PAID')
            ->count();
        $this->info("Records có status = 'PAID' trong 30 ngày: {$paidRecords}");
        
        if ($paidRecords > 0) {
            // Thống kê chi tiết
            $stats = TikTokPayment::where('paid_time', '>=', $thirtyDaysAgo)
                ->where('status', 'PAID')
                ->selectRaw('
                    COUNT(*) as total_payments,
                    COUNT(DISTINCT store_id) as stores_with_payments,
                    SUM(settlement_amount) as total_amount,
                    AVG(settlement_amount) as avg_amount,
                    MIN(settlement_amount) as min_amount,
                    MAX(settlement_amount) as max_amount
                ')
                ->first();
                
            $this->info("\n=== CHI TIẾT THỐNG KÊ ===");
            $this->info("Tổng payments: {$stats->total_payments}");
            $this->info("Stores có payments: {$stats->stores_with_payments}");
            $this->info("Tổng tiền: $" . number_format($stats->total_amount, 2));
            $this->info("Trung bình/payment: $" . number_format($stats->avg_amount, 2));
            $this->info("Min amount: $" . number_format($stats->min_amount, 2));
            $this->info("Max amount: $" . number_format($stats->max_amount, 2));
            
            // Hiển thị một vài records mẫu
            $this->info("\n=== MỘT VÀI RECORDS MẪU ===");
            $samples = TikTokPayment::where('paid_time', '>=', $thirtyDaysAgo)
                ->where('status', 'PAID')
                ->with('store:id,name')
                ->limit(5)
                ->get();
                
            foreach ($samples as $sample) {
                $this->info("ID: {$sample->id} | Store: " . ($sample->store ? $sample->store->name : 'N/A') . 
                           " | Amount: $" . number_format($sample->settlement_amount, 2) . 
                           " | Paid: {$sample->paid_time}");
            }
        } else {
            $this->warn('Không có payments nào với status PAID trong 30 ngày gần đây!');
        }
        
        // Kiểm tra phân bố theo ngày
        $this->info("\n=== PHÂN BỐ THEO NGÀY (7 ngày gần nhất) ===");
        for ($i = 0; $i < 7; $i++) {
            $date = Carbon::now()->subDays($i);
            $dayCount = TikTokPayment::whereDate('paid_time', $date->format('Y-m-d'))
                ->where('status', 'PAID')
                ->count();
            $this->info("{$date->format('Y-m-d')}: {$dayCount} payments");
        }
        
        $this->info("\n=== HOÀN THÀNH ===");
    }
}
