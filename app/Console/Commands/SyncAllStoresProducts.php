<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Store;
use App\Models\Product;
use App\Services\Tiktok\TiktokShopService;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SyncAllStoresProducts extends Command
{
    protected $signature = 'stores:sync-products {store_id?}';
    protected $description = 'Sync all products from TikTok for all stores including pagination';

    public function handle()
    {
        $store_id = $this->argument('store_id');
        if ($store_id) {
            $stores = Store::where('id', $store_id)->get();
        } else {
            $stores = Store::where('app_partner_id', '!=', '')
                ->where('status', 'Active')

                ->orderBy('id', 'desc')
                ->get();
        }

        $count = $stores->count();
        $this->info("Total stores: {$count}");

        foreach ($stores as $store) {
            $this->info("Syncing products for store: {$store->id}");
            try {
                $TiktokShopService = new TiktokShopService($store);
                // Chỉ lấy 50 sản phẩm mới nhất
                $response = $TiktokShopService->getProductList(null, 'ACTIVATE', 50);
                $products = $response['products'] ?? [];
                $totalCount = $response['total_count'] ?? 0;

                if (!empty($products)) {
                    foreach ($products as $product) {
                        $existingProduct = Product::where('tiktok_product_id', $product['id'])
                            ->where('store_id', $store->id)
                            ->first();

                        if (!$existingProduct) {
                            $new_product = $TiktokShopService->getProduct($product['id']);
                            $this->saveProduct($store, $new_product);
                        }
                    }

                    // Cập nhật thông tin store với sản phẩm mới nhất
                    $lastCreatedProduct = $products[0]['create_time'] ?? null;
                    if ($lastCreatedProduct) {
                        $datetime = Carbon::createFromTimestamp($lastCreatedProduct, 'America/New_York');
                        $datetime->setTimezone('Asia/Ho_Chi_Minh');
                        $store->update([
                            'last_created_tiktok_product' => $datetime,
                            'tiktok_product_count' => $totalCount
                        ]);
                    }
                    $this->info("Synced " . count($products) . " products for store {$store->id}");
                } else {
                    $store->update([
                        'last_created_tiktok_product' => null,
                        'tiktok_product_count' => 0
                    ]);
                    $this->info("No products found for store: {$store->id}");
                }
            } catch (\Exception $e) {
                if (strpos($e->getMessage(), 'seller is inactived') !== false) {
                    $store->update(['status' => 'Suspended']);
                    $this->warn("Store {$store->id} has been marked as inactive due to: " . $e->getMessage());
                } else {
                    $this->error("Error syncing products for store: {$store->id} - " . $e->getMessage());
                }
            }
        }

        $this->info("Product synchronization completed for all stores.");
    }

    protected function saveProduct($store, $product)
    {

        Product::updateOrCreate(
            [
                'tiktok_product_id' => $product['id'],
                'store_id' => $store->id
            ],
            [
                'name' =>  $product['title'] ?? '',
                'seller_id' => $store->owner_id,
                'description' => $product['description'] ?? '',
                'image' => $product['main_images'][0]['urls'][0] ?? null,
                'price' => 0,
                'active' => true,
                'sku' => $product['sku_list'][0]['id'] ?? null,
                'link' => 'https://shop.tiktok.com/view/product/' . $product['id']
            ]
        );
    }
}
