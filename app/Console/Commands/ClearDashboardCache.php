<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use App\Models\User;

class ClearDashboardCache extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'dashboard:clear-cache {--user-id= : Clear cache for specific user}';

    /**
     * The console command description.
     */
    protected $description = 'Clear dashboard widget cache to fix Livewire snapshot issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->option('user-id');
        
        if ($userId) {
            $this->clearUserCache($userId);
        } else {
            $this->clearAllUsersCache();
        }
        
        $this->info('Dashboard cache cleared successfully!');
    }

    /**
     * Clear cache for specific user
     */
    private function clearUserCache($userId)
    {
        $cacheKeys = [
            'overall_stats_overview_' . $userId,
            'overall_orders_chart_week_' . $userId,
            'overall_orders_chart_month_' . $userId,
            'overall_orders_chart_quarter_' . $userId,
            'overall_fulfill_chart_week_' . $userId,
            'overall_fulfill_chart_month_' . $userId,
            'overall_fulfill_chart_quarter_' . $userId,
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        $this->info("Cache cleared for user ID: {$userId}");
    }

    /**
     * Clear cache for all users with Leader/User Manager roles
     */
    private function clearAllUsersCache()
    {
        $users = User::role(['Leader', 'User Manager', 'super_admin'])->get();
        
        foreach ($users as $user) {
            $this->clearUserCache($user->id);
        }

        $this->info("Cache cleared for {$users->count()} users");
    }
}
