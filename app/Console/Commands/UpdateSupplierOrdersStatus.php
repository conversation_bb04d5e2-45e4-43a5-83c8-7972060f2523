<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SupplierOrder;
use App\Models\Supplier;
use Illuminate\Support\Facades\Log;

class UpdateSupplierOrdersStatus extends Command
{
    protected $signature = 'orders:update-status';
    protected $description = 'Update status for all supplier orders from all suppliers';


    public function handle()
    {
        // Chỉ update orders trong 30 ngày gần đây và chưa hoàn thành
        $orders = SupplierOrder::where('created_at', '>=', now()->subDays(30))
            ->whereNotIn('status', ['Completed', 'Refunded', 'Cancelled'])
            ->get();

        $this->info("Found {$orders->count()} orders to update (created within 30 days and not completed)");
        Log::channel('flashship_sync_status')->info("Found {$orders->count()} orders to update (created within 30 days and not completed) sync cả đơn cancel");
        
        $successCount = 0;
        $failedCount = 0;
        $skippedCount = 0;

        foreach ($orders as $order) {
            try {
                // Skip orders đã completed hoặc cancelled
                // if (in_array($order->status, ['Completed', 'Refunded'])) {
                //     $skippedCount++;
                //     continue;
                // }

                $order->updateStatus();
                $successCount++;
                $this->info("Updated status for order ID: {$order->order_code}");
                Log::channel('flashship_sync_status')->info("Updated status for order ID: {$order->order_code}");
            } catch (\Exception $e) {
                $failedCount++;
                $this->error("Failed to update status for order ID: {$order->order_code}. Error: {$e->getMessage()}");
                Log::channel('flashship_sync_status')->error("Failed to update status for order ID: {$order->order_code}. Error: {$e->getMessage()}");
            }
        }

        $this->info("Status update completed: {$successCount} success, {$failedCount} failed, {$skippedCount} skipped");
        Log::channel('flashship_sync_status')->info("Status update completed: {$successCount} success, {$failedCount} failed, {$skippedCount} skipped");
    }

    public function handle2()
    {
        $this->info('Starting to update supplier orders status...');
        $suppliers = Supplier::all();

        foreach ($suppliers as $supplier) {
            $supplierOrders = SupplierOrder::where('supplier_id', $supplier->id)->whereIn('status', ['Pending', 'AwaitingShipment', 'InProducing'])->get();

            foreach ($supplierOrders as $order) {
                try {
                    $order->updateStatus(); // Ensure updateStatus method is applicable for all suppliers
                    $this->info("Updated status for supplier order ID: {$order->id} from supplier: {$supplier->name}");
                } catch (\Exception $e) {
                    $this->error("Failed to update status for supplier order ID: {$order->id} from supplier: {$supplier->name} with error: " . $e->getMessage());
                }
            }
        }
    }
}
