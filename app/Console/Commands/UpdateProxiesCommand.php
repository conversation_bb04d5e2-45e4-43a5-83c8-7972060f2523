<?php

namespace App\Console\Commands;

use App\Models\PartnerApp;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class UpdateProxiesCommand extends Command
{
    protected $signature = 'app:update-proxies';
    protected $description = 'Update proxies for all partner apps';

    public function handle()
    {
        $proxies = collect([
            '***************:14741:kd0502:kd0502',
            '***************:39318:kd0502:kd0502',
            '*************:39817:kd0502:kd0502'
        ])->shuffle();

        PartnerApp::orderBy('id')->get()->each(function ($app) use ($proxies) {
            $proxyIndex = $app->id % count($proxies);
            $proxy = 'socks5://' . str_replace(
                ':keyduc_proxyapp:keyduc_proxyapp',
                '@keyduc_proxyapp:keyduc_proxyapp',
                $proxies[$proxyIndex]
            );
            
            $app->update(['proxy' => $proxy]);
            $this->info("Updated proxy for app ID {$app->id}: {$proxy}");
        });

        $this->info('All proxies have been updated successfully!');
    }
} 