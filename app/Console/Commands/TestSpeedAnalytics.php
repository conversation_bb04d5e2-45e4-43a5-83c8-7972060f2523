<?php

namespace App\Console\Commands;

use App\Services\SpeedAnalyticsService;
use Illuminate\Console\Command;

class TestSpeedAnalytics extends Command
{
    protected $signature = 'analytics:test-speed {--seller_id=}';
    protected $description = 'Test speed analytics service and display results';

    public function handle()
    {
        $sellerId = $this->option('seller_id');
        $speedAnalytics = new SpeedAnalyticsService();

        $this->info('🚀 Testing Speed Analytics Service...');
        $this->newLine();

        try {
            // Test Order Speed
            $this->info('📊 ORDER SPEED ANALYSIS');
            $this->line('================================');
            $orderSpeed = $speedAnalytics->getOrderSpeed('1m', $sellerId);

            // Debug: Kiểm tra số liệu thực tế
            $this->warn('🔍 DEBUG ORDER DATA:');
            $now = \Carbon\Carbon::now();
            $start = $now->copy()->subMonth();
            $totalOrders = \App\Models\Order::whereBetween('created_at', [$start, $now])->count();
            $totalSupplierOrders = \App\Models\SupplierOrder::whereBetween('created_at', [$start, $now])->count();
            $completedSupplierOrders = \App\Models\SupplierOrder::where('status', 'Completed')->whereBetween('updated_at', [$start, $now])->count();

            $this->line("Period: {$start->format('Y-m-d')} to {$now->format('Y-m-d')}");
            $this->line("Orders created in period: {$totalOrders}");
            $this->line("SupplierOrders created in period: {$totalSupplierOrders}");
            $this->line("SupplierOrders completed in period: {$completedSupplierOrders}");
            $this->newLine();
            
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Orders/Hour', $orderSpeed['speed']['hourly']],
                    ['Daily Average', $orderSpeed['speed']['daily_avg']],
                    ['Weekly Average', $orderSpeed['speed']['weekly_avg']],
                    ['Total in Period', $orderSpeed['speed']['total_in_period']],
                    ['Period Days', $orderSpeed['speed']['period_days']],
                    ['Growth Rate', $orderSpeed['trend']['growth_rate'] . '%'],
                    ['Current Period', $orderSpeed['trend']['current_period']],
                    ['Previous Period', $orderSpeed['trend']['previous_period']],
                    ['Pending Design', $orderSpeed['queue']['pending_design']],
                    ['Pending Fulfillment', $orderSpeed['queue']['pending_fulfillment']],
                    ['Avg Time to Design (hours)', $orderSpeed['processing_time']['avg_time_to_design_hours']],
                    ['Avg Time to Fulfill (hours)', $orderSpeed['processing_time']['avg_time_to_fulfill_hours']],
                ]
            );

            $this->newLine();

            // Test Design Speed
            $this->info('🎨 DESIGN SPEED ANALYSIS');
            $this->line('================================');
            $designSpeed = $speedAnalytics->getDesignSpeed('24h', $sellerId);
            
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Design Jobs/Hour', $designSpeed['speed']['hourly']],
                    ['Daily Average', $designSpeed['speed']['daily_avg']],
                    ['Per Designer', $designSpeed['speed']['per_designer']],
                    ['Total Jobs Completed', $designSpeed['speed']['total_in_period']],
                    ['Active Designers', $designSpeed['capacity']['active_designers']],
                    ['Utilization Rate', $designSpeed['capacity']['utilization_rate'] . '%'],
                    ['Theoretical Daily Capacity', $designSpeed['capacity']['theoretical_daily_capacity']],
                    ['Theoretical Period Capacity', $designSpeed['capacity']['theoretical_period_capacity']],
                    ['Queue Pending', $designSpeed['queue']['pending']],
                    ['Queue Assigned', $designSpeed['queue']['assigned']],
                    ['Queue In Progress', $designSpeed['queue']['in_progress']],
                    ['Queue Under Review', $designSpeed['queue']['under_review']],
                    ['Queue Needs Revision', $designSpeed['queue']['needs_revision']],
                    ['Total Queue', $designSpeed['queue']['total_queue']],
                    ['Active Work', $designSpeed['queue']['active_work']],
                    ['Avg Total Time (hours)', $designSpeed['processing_time']['avg_design_time_hours']],
                    ['Avg Working Time (hours)', $designSpeed['processing_time']['avg_working_time_hours']],
                    ['Avg Queue Time (hours)', $designSpeed['processing_time']['avg_queue_time_hours']],
                ]
            );

            $this->newLine();

            // Test Fulfillment Speed
            $this->info('📦 FULFILLMENT SPEED ANALYSIS');
            $this->line('================================');
            $fulfillmentSpeed = $speedAnalytics->getFulfillmentSpeed('24h', $sellerId);
            
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Fulfillments/Hour', $fulfillmentSpeed['speed']['hourly']],
                    ['Daily Average', $fulfillmentSpeed['speed']['daily_avg']],
                    ['Per Staff', $fulfillmentSpeed['speed']['per_staff']],
                    ['Total in Period', $fulfillmentSpeed['speed']['total_in_period']],
                    ['Active Staff', $fulfillmentSpeed['capacity']['active_staff']],
                    ['Utilization Rate', $fulfillmentSpeed['capacity']['utilization_rate'] . '%'],
                    ['Auto Fulfillment Rate', $fulfillmentSpeed['automation']['auto_fulfillment_rate'] . '%'],
                    ['Manual Fulfillments', $fulfillmentSpeed['automation']['manual_fulfillments']],
                    ['Auto Fulfillments', $fulfillmentSpeed['automation']['auto_fulfillments']],
                    ['Total Fulfillments', $fulfillmentSpeed['automation']['total_fulfillments']],
                    ['Queue Total', $fulfillmentSpeed['queue']['total_queue']],
                    ['Avg Order→Fulfill Time (days)', $fulfillmentSpeed['processing_time']['avg_order_to_fulfill_days']],
                ]
            );

            $this->newLine();

            // Test Bottleneck Detection
            $this->info('⚠️  BOTTLENECK ANALYSIS');
            $this->line('================================');
            $bottlenecks = $speedAnalytics->detectBottlenecks($sellerId);
            
            if (empty($bottlenecks['bottlenecks'])) {
                $this->info('✅ No bottlenecks detected! System is running smoothly.');
            } else {
                $this->warn('🚨 Bottlenecks detected:');
                foreach ($bottlenecks['bottlenecks'] as $bottleneck) {
                    $this->line("- {$bottleneck['type']}: {$bottleneck['severity']}% severity");
                    $this->line("  Current: {$bottleneck['current_capacity']}/hour");
                    $this->line("  Required: {$bottleneck['required_capacity']}/hour");
                    $this->line("  Gap: " . ($bottleneck['gap'] ?? 0) . " units/hour");
                }
            }

            $this->newLine();

            if (!empty($bottlenecks['recommendations'])) {
                $this->info('💡 RECOMMENDATIONS');
                $this->line('================================');
                foreach ($bottlenecks['recommendations'] as $recommendation) {
                    $priority = strtoupper($recommendation['priority']);
                    $this->line("[{$priority}] {$recommendation['message']}");
                    if (isset($recommendation['count'])) {
                        $this->line("   → Need to hire: {$recommendation['count']} people");
                    }
                }
            }

            $this->newLine();

            // System Health
            $health = $bottlenecks['overall_health'];
            $this->info('❤️  SYSTEM HEALTH');
            $this->line('================================');
            $this->table(
                ['Metric', 'Value', 'Status'],
                [
                    ['Overall Score', $health['score'] . '%', $health['status']],
                    ['Design Balance', $health['design_balance'] . '%', $health['design_balance'] >= 80 ? 'Good' : 'Needs Attention'],
                    ['Fulfillment Balance', $health['fulfillment_balance'] . '%', $health['fulfillment_balance'] >= 80 ? 'Good' : 'Needs Attention'],
                ]
            );

            // Color-coded status
            $statusColor = match($health['status']) {
                'excellent' => 'info',
                'good' => 'info',
                'warning' => 'warn',
                'critical' => 'error',
                default => 'line'
            };
            
            $this->$statusColor("System Status: " . strtoupper($health['status']));

            $this->newLine();
            $this->info('✅ Speed Analytics Test Completed Successfully!');

            // Test Dashboard Data
            $this->newLine();
            $this->info('📊 TESTING DASHBOARD DATA CACHE');
            $this->line('================================');
            
            $start = microtime(true);
            $dashboardData = $speedAnalytics->getDashboardData($sellerId);
            $end = microtime(true);
            
            $this->info("Dashboard data loaded in " . round(($end - $start) * 1000, 2) . "ms");
            $this->line("Cache timestamp: " . $dashboardData['timestamp']);

        } catch (\Exception $e) {
            $this->error('❌ Error testing speed analytics: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }
}
