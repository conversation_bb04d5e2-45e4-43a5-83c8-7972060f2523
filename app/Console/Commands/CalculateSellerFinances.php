<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\SellerFinance;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CalculateSellerFinances extends Command
{
    protected $signature = 'seller:calculate-finances 
        {--month= : Tháng cần tính (format: YYYY-MM)} 
        {--seller= : ID của seller cụ thể}
        {--force : Bắt buộc tính toán lại kể cả đã có dữ liệu}
        {--notify : G<PERSON>i thông báo cho seller sau khi tính xong}';

    protected $description = 'Tính toán tài chính cho sellers';

    public function handle()
    {
        $month = $this->option('month') 
            ? Carbon::createFromFormat('Y-m', $this->option('month'))
            : Carbon::now()->startOfMonth();

        $this->info("Tháng tính toán: " . $month->format('M Y'));

        // Kiểm tra nếu đã tính toán và không force
        if (!$this->option('force') && $this->isMonthCalculated($month)) {
            $this->warn("Tháng này đã được tính toán. Sử dụng --force để tính lại.");
            return;
        }

        try {
            DB::beginTransaction();

            // Bỏ phần xóa báo cáo cũ ở đây vì đã xử lý trong generateMonthlyReport
            if ($this->option('force')) {
                $this->info("Sẽ tính toán lại tất cả báo cáo.");
            }

            // Lấy danh sách sellers
            $query = User::role('seller');
            if ($sellerId = $this->option('seller')) {
                $query->where('id', $sellerId);
            }

            $sellers = $query->get();
            if ($sellers->isEmpty()) {
                $this->error("Không tìm thấy seller nào!");
                return;
            }

            $this->info("Bắt đầu tính toán cho {$sellers->count()} sellers...");
            
            // Hiển thị thông tin về loại seller
            $fixedCommissionCount = $sellers->where('has_fixed_commission', true)->count();
            $regularSellerCount = $sellers->where('has_fixed_commission', false)->count();
            $this->info("- Seller theo doanh số: $regularSellerCount");
            $this->info("- Seller % cố định: $fixedCommissionCount");
            
            $bar = $this->output->createProgressBar($sellers->count());

            $results = [
                'success' => [],
                'errors' => []
            ];

            foreach ($sellers as $seller) {
                try {
                    // Tạo/cập nhật báo cáo
                    $report = SellerFinance::generateMonthlyReport($seller->id, $month);
                    
                    // Gửi thông báo nếu có yêu cầu
                    if ($this->option('notify')) {
                        $this->notifySeller($seller, $report);
                    }

                    $results['success'][] = [
                        'seller_id' => $seller->id,
                        'name' => $seller->name,
                        'has_fixed_commission' => $seller->has_fixed_commission,
                        'fixed_commission_rate' => $seller->fixed_commission_rate,
                        'has_base_salary' => $seller->has_base_salary
                    ];

                } catch (\Exception $e) {
                    $results['errors'][] = [
                        'seller_id' => $seller->id,
                        'name' => $seller->name,
                        'error' => $e->getMessage()
                    ];
                    Log::error("Lỗi tính toán cho seller", [
                        'seller_id' => $seller->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }

                $bar->advance();
            }

            $bar->finish();
            DB::commit();

            // Hiển thị kết quả
            $this->newLine(2);
            $this->info("Hoàn thành!");
            $this->info("- Thành công: " . count($results['success']));
            $this->info("- Lỗi: " . count($results['errors']));

            $fixedCommissionComplete = collect($results['success'])->where('has_fixed_commission', true)->count();
            $regularComplete = collect($results['success'])->where('has_fixed_commission', false)->count();
            $this->info("- Hoàn thành seller theo doanh số: $regularComplete");
            $this->info("- Hoàn thành seller % cố định: $fixedCommissionComplete");

            if (!empty($results['errors'])) {
                $this->error("\nDanh sách lỗi:");
                foreach ($results['errors'] as $error) {
                    $this->line("- Seller {$error['name']} (ID: {$error['seller_id']}): {$error['error']}");
                }
            }

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    protected function isMonthCalculated($month)
    {
        return SellerFinance::whereYear('month', $month->year)
            ->whereMonth('month', $month->month)
            ->where('status', 'completed')
            ->exists();
    }

    protected function notifySeller($seller, $report)
    {
        try {
            // Gửi thông báo qua Telegram hoặc email tùy theo cấu hình
            Log::info("Gửi thông báo báo cáo tài chính cho seller", [
                'seller_id' => $seller->id,
                'seller_name' => $seller->name,
                'month' => $report->month->format('Y-m'),
                'report_id' => $report->id
            ]);

            // Phân biệt loại seller trong thông báo
            $message = "Báo cáo tài chính tháng {$report->month->format('m/Y')} đã được tạo.\n";
            
            if ($seller->has_fixed_commission) {
                $message .= "Loại seller: Cố định " . $seller->fixed_commission_rate . "%\n";
            } else {
                $sellerLevel = $seller->sellerLevel;
                $levelName = $sellerLevel ? $sellerLevel->name : 'Chưa xác định';
                $message .= "Loại seller: Theo doanh số (Level: $levelName)\n";
            }
            
            $message .= "Doanh thu: " . number_format($report->gross_revenue) . "\n";
            $message .= "Lợi nhuận: " . number_format($report->net_profit) . "\n";
            $message .= "Tổng thu nhập: " . number_format($report->total_salary) . "\n";
            $message .= "Tháng tính doanh số: " . $report->month->format('m/Y') . "\n";
            $message .= "Lưu ý: Khi tạo invoice thanh toán, vui lòng chọn tháng tính doanh số chính xác để hệ thống ghi nhận đúng khoản thanh toán.\n";
            
            // Đây là nơi để thêm code gửi thông báo qua Telegram, email, etc.
            
            return true;
        } catch (\Exception $e) {
            Log::error("Lỗi gửi thông báo cho seller", [
                'seller_id' => $seller->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
} 