<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\IdeogramService;
use App\Models\IdeogramAccount;

class IdeogramCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ideogram {action} {--account=} {--prompt=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Quản lý và test Ideogram service';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'list':
                $this->listAccounts();
                break;

            case 'status':
                $this->showStatus();
                break;

            case 'test':
                $this->testConnection();
                break;

            case 'create':
                $this->createImage();
                break;

            case 'reset':
                $this->resetDailyUsage();
                break;

            default:
                $this->error('Action không hợp lệ. Sử dụng: list, status, test, create, reset');
                $this->showHelp();
        }
    }

    private function listAccounts()
    {
        $accounts = IdeogramAccount::orderBy('priority', 'desc')->get();

        if ($accounts->isEmpty()) {
            $this->warn('Không có tài khoản nào. Vui lòng thêm tài khoản qua admin panel.');
            return;
        }

        $this->info('Danh sách tài khoản Ideogram:');
        $this->table(
            ['ID', 'Tên', 'Hoạt động', 'Mặc định', 'Ưu tiên', 'Sử dụng hôm nay', 'Trạng thái'],
            $accounts->map(function ($account) {
                return [
                    $account->id,
                    $account->name,
                    $account->is_active ? '✓' : '✗',
                    $account->is_default ? '✓' : '✗',
                    $account->priority,
                    $account->daily_usage . '/' . $account->daily_limit,
                    $account->status
                ];
            })
        );
    }

    private function showStatus()
    {
        try {
            $stats = IdeogramService::getUsageStats();

            $this->info('Thống kê sử dụng Ideogram:');
            $this->line('Tổng số tài khoản: ' . $stats['total_accounts']);
            $this->line('Tài khoản hoạt động: ' . $stats['active_accounts']);
            $this->line('Tài khoản khả dụng hôm nay: ' . $stats['available_today']);
            $this->line('Tổng sử dụng hôm nay: ' . $stats['total_usage_today'] . '/' . $stats['total_limit_today']);
            $this->line('Tổng sử dụng tất cả: ' . $stats['total_usage_all_time']);

        } catch (\Exception $e) {
            $this->error('Lỗi: ' . $e->getMessage());
        }
    }

    private function testConnection()
    {
        try {
            $accountId = $this->option('account');

            if ($accountId) {
                $service = IdeogramService::withAccount($accountId);
                $account = $service->getCurrentAccount();
                $this->info('Test kết nối với tài khoản: ' . $account->name);
            } else {
                $service = new IdeogramService();
                $account = $service->getCurrentAccount();
                $this->info('Test kết nối với tài khoản tốt nhất: ' . $account->name);
            }

            $result = $service->login();
            $this->info('✓ Kết nối thành công!');
            $this->line('User ID: ' . ($result['user_model']['user_id'] ?? 'N/A'));
            $this->line('User Handle: ' . ($result['user_model']['display_handle'] ?? 'N/A'));

        } catch (\Exception $e) {
            $this->error('✗ Kết nối thất bại: ' . $e->getMessage());
        }
    }

    private function createImage()
    {
        $prompt = $this->option('prompt');

        if (!$prompt) {
            $prompt = $this->ask('Nhập prompt để tạo ảnh:');
        }

        if (!$prompt) {
            $this->error('Prompt không được để trống');
            return;
        }

        try {
            $accountId = $this->option('account');

            if ($accountId) {
                $service = IdeogramService::withAccount($accountId);
            } else {
                $service = new IdeogramService();
            }

            $this->info('Đang tạo ảnh với prompt: ' . $prompt);
            $this->info('Sử dụng tài khoản: ' . $service->getCurrentAccount()->name);

            $result = $service->createImage($prompt);

            if ($result['success']) {
                $this->info('✓ Tạo ảnh thành công!');
                $this->line('Request ID: ' . $result['request_id']);
                $this->line('Số ảnh: ' . count($result['images']));

                foreach ($result['images'] as $index => $imageUrl) {
                    $this->line('Ảnh ' . ($index + 1) . ': ' . $imageUrl);
                }

                if (isset($result['account_used'])) {
                    $this->line('Tài khoản đã sử dụng: ' . $result['account_used']['name']);
                    $this->line('Số lần sử dụng hôm nay: ' . $result['account_used']['daily_usage']);
                }
            } else {
                $this->error('✗ Tạo ảnh thất bại: ' . $result['error']);
            }

        } catch (\Exception $e) {
            $this->error('✗ Lỗi: ' . $e->getMessage());
        }
    }

    private function resetDailyUsage()
    {
        try {
            $count = IdeogramService::resetAllDailyUsage();
            $this->info("✓ Đã reset daily usage cho {$count} tài khoản");
        } catch (\Exception $e) {
            $this->error('✗ Lỗi: ' . $e->getMessage());
        }
    }

    private function showHelp()
    {
        $this->line('');
        $this->line('Cách sử dụng:');
        $this->line('  php artisan ideogram list                    - Hiển thị danh sách tài khoản');
        $this->line('  php artisan ideogram status                 - Hiển thị thống kê sử dụng');
        $this->line('  php artisan ideogram test                   - Test kết nối với tài khoản tốt nhất');
        $this->line('  php artisan ideogram test --account=1       - Test kết nối với tài khoản cụ thể');
        $this->line('  php artisan ideogram create --prompt="..."  - Tạo ảnh với prompt');
        $this->line('  php artisan ideogram reset                  - Reset daily usage cho tất cả tài khoản');
    }
}
