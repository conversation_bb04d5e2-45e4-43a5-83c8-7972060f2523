<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\App;
use Exception;

class AnalyzeCommitDifference extends Command
{
    protected $signature = 'analyze:commit-difference';
    protected $description = 'Analyze why code worked before commit a0d8c28 but not after';

    public function handle()
    {
        $this->info("🔍 PHÂN TÍCH TẠI SAO CODE TRƯỚC ĐÂY CHẠY ĐƯỢC");
        $this->info("===========================================\n");

        try {
            // Simulate commit a0d8c28 logic
            $this->info("=== SIMULATE COMMIT a0d8c28 LOGIC ===");
            
            $apiUrl = App::environment('production') 
                ? 'https://api.flashship.net/seller-api-v2/' 
                : 'https://devpod.flashship.net/seller-api-v2/';
            
            // Get token first
            $credentials = [
                'username' => App::environment('production') ? env('FLASHSHIP_USERNAME') : 'pod080',
                'password' => App::environment('production') ? env('FLASHSHIP_PASSWORD') : '@Test123456'
            ];
            
            $tokenResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post($apiUrl . 'token', $credentials);
            
            $tokenData = $tokenResponse->json();
            $token = $tokenData['data']['access_token'] ?? null;
            
            if (!$token) {
                $this->error("Cannot get token");
                return 1;
            }
            
            // Test getOrder with a0d8c28 logic
            $this->info("📋 Testing getOrder with a0d8c28 logic...");
            
            $getResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token
            ])->get($apiUrl . 'orders/TEST_ORDER_123');
            
            $this->line("Raw response status: " . $getResponse->status());
            $this->line("Raw response body: '" . $getResponse->body() . "'");
            $this->line("Response successful: " . ($getResponse->successful() ? 'true' : 'false'));
            
            // Simulate a0d8c28 logic exactly
            $this->info("\n=== A0D8C28 LOGIC SIMULATION ===");
            
            try {
                if ($getResponse->successful()) {
                    $responseBody = $getResponse->json();
                    $this->line("Response JSON: " . json_encode($responseBody));
                    
                    // This is the EXACT logic from a0d8c28
                    $result = [
                        'success' => $responseBody['msg'] == 'success' ? true : false,
                        'data' => $responseBody['data'] ?? [],
                        'message' => $responseBody['err'] ?? null,
                    ];
                    
                    $this->line("A0D8C28 would return: " . json_encode($result, JSON_PRETTY_PRINT));
                    
                } else {
                    $this->line("Response not successful, would go to catch block");
                }
            } catch (Exception $e) {
                $this->line("Exception caught: " . $e->getMessage());
                
                // This is where a0d8c28 had the bug - duplicate catch blocks
                // But let's see what the first catch would do
                if ($getResponse->successful()) {
                    $responseBody = $getResponse->json();
                    $this->line("In catch but response successful, returning JSON: " . json_encode($responseBody));
                    // This line would return the raw JSON response!
                    $result = $responseBody;
                } else {
                    $result = [
                        'success' => false,
                        'message' => $getResponse->body() ?? 'An error occurred with the API request'
                    ];
                }
                
                $this->line("A0D8C28 catch block would return: " . json_encode($result, JSON_PRETTY_PRINT));
            }
            
            $this->info("\n=== ANALYSIS ===");
            
            // Key insight: In a0d8c28, when response was successful but JSON was null,
            // it would throw an exception trying to access $responseBody['msg']
            // Then the catch block would check if response was successful again
            // and return the raw $responseBody (which could be null)
            
            $this->warn("🔍 PHÁT HIỆN QUAN TRỌNG:");
            $this->line("1. Trong a0d8c28, khi API trả về empty response:");
            $this->line("   - response->successful() = true");
            $this->line("   - response->json() = null");
            $this->line("   - Truy cập \$responseBody['msg'] sẽ throw exception");
            $this->line("   - Catch block sẽ kiểm tra response->successful() lại");
            $this->line("   - Và return \$responseBody (null)");
            $this->newLine();
            
            $this->line("2. Trong cronjob AutoFulfillOrders:");
            $this->line("   - \$flashshipOrder = \$flashshipService->getOrder(\$order->order_code);");
            $this->line("   - if (\$flashshipOrder && isset(\$flashshipOrder['id'])) {");
            $this->line("   - Nếu \$flashshipOrder = null → condition false");
            $this->line("   - Sẽ throw exception: 'Order was not created in FlashShip'");
            $this->newLine();
            
            $this->info("3. Nhưng tại sao trước đây lại chạy được?");
            
            // Let's check if there were any other changes
            $this->info("\n=== KIỂM TRA CÁC YẾU TỐ KHÁC ===");
            
            $this->line("Có thể có những thay đổi khác:");
            $this->line("1. FlashShip API behavior thay đổi");
            $this->line("2. Cronjob logic thay đổi");
            $this->line("3. Environment/config thay đổi");
            $this->line("4. Data format thay đổi");
            
            // Test with different order codes that might exist
            $this->info("\n=== TEST VỚI ORDER CODES KHÁC NHAU ===");
            
            $testCodes = [
                'K-Seller[2]-Store[1]-576624746497282782', // Real order from earlier test
                'FS123456', // FlashShip format
                'TEST_ORDER', // Simple format
            ];
            
            foreach ($testCodes as $code) {
                $this->line("\nTesting with order code: $code");
                
                $testResponse = Http::withHeaders([
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $token
                ])->get($apiUrl . 'orders/' . $code);
                
                $this->line("  Status: " . $testResponse->status());
                $this->line("  Body: '" . $testResponse->body() . "'");
                $this->line("  JSON: " . json_encode($testResponse->json()));
            }
            
            $this->info("\n🎯 KẾT LUẬN:");
            $this->info("============");
            $this->warn("Có thể có những lý do sau:");
            $this->line("1. ✅ FlashShip API trước đây trả về format khác");
            $this->line("2. ✅ Có orders thực sự tồn tại trên FlashShip");
            $this->line("3. ✅ Cronjob logic khác (không dùng getOrder)");
            $this->line("4. ✅ Environment/credentials khác");
            $this->line("5. ✅ Bug trong a0d8c28 thực ra không ảnh hưởng vì lý do khác");
            
        } catch (Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
