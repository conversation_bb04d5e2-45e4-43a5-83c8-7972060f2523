<?php

namespace App\Console\Commands;

use App\Models\PartnerApp;
use App\Services\TelegramNotificationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CheckProxyStatus extends Command
{
    protected $signature = 'proxy:check';
    protected $description = 'Check proxy status for active PartnerApps every 15 minutes';

    public function handle()
    {
        $telegramService = new TelegramNotificationService();
        $activeApps = PartnerApp::where('status', 'active')
            ->whereNotNull('proxy')
            ->get();

        $errorMessages = [];
        foreach ($activeApps as $app) {
            try {
                $proxyConfig = $this->parseProxyString($app->proxy);

                if (!$proxyConfig) {
                    $errorMessages[] = "❌ {$app->app_name}";
                    continue;
                }

                $proxyUrl = "{$proxyConfig['protocol']}://{$proxyConfig['username']}:{$proxyConfig['password']}@{$proxyConfig['host']}:{$proxyConfig['port']}";

                $response = Http::timeout(10)
                    ->withOptions([
                        'proxy' => $proxyUrl,
                        'verify' => false
                    ])
                    ->get('https://api.myip.com');

                if (!$response->successful()) {
                    $errorMessages[] = "❌ {$app->app_name}";
                }
            } catch (\Exception $e) {
                $errorMessages[] = "❌ {$app->app_name}";
            }
        }

        if (!empty($errorMessages)) {
            $now = now()->format('H:i');
            $telegramMessage = "⚠️ Proxy Error ({$now}):\n" . implode("\n", $errorMessages);
            $telegramService->sendCustomThreadNotification($telegramMessage);
        }
    }
    private function parseProxyString($input)
    {
        if (empty($input)) {
            return null;
        }

        try {
            $protocol = 'http'; // Default protocol
            $remainder = $input;

            // Kiểm tra nếu có protocol://
            if (str_contains($input, '://')) {
                $protocol = substr($input, 0, strpos($input, '://'));
                $remainder = substr($input, strlen($protocol) + 3);
            }

            // Format 1: IP:PORT@USERNAME:PASSWORD
            if (str_contains($remainder, '@')) {
                list($ipPort, $auth) = explode('@', $remainder);
                list($host, $port) = explode(':', $ipPort);
                list($username, $password) = explode(':', $auth);
            }
            // Format 2: IP:PORT:USERNAME:PASSWORD
            else if (substr_count($remainder, ':') === 3) {
                $parts = explode(':', $remainder);
                $host = $parts[0];
                $port = $parts[1];
                $username = $parts[2];
                $password = $parts[3];
            }
            // Invalid format
            else {
                Log::error('Invalid proxy format', ['input' => $input]);
                return null;
            }

            return [
                'protocol' => $protocol,
                'host' => $host,
                'port' => $port,
                'username' => $username,
                'password' => $password
            ];
        } catch (\Exception $e) {
            Log::error('Failed to parse proxy string', ['input' => $input, 'error' => $e->getMessage()]);
            return null;
        }
    }
}
