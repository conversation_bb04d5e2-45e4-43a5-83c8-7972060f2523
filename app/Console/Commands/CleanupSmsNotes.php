<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanupSmsNotes extends Command
{
    // The name and signature of the console command.
    protected $signature = 'smsnotes:cleanup';

    // The console command description.
    protected $description = 'Delete SMS notes older than 30 minutes';

    // Execute the console command.
    public function handle()
    {
        $threshold = Carbon::now()->subMinutes(30);

        // Delete records older than the threshold
        DB::table('sms_notes')->where('created_at', '<', $threshold)->delete();

        $this->info('SMS notes older than 30 minutes have been deleted.');
    }
}