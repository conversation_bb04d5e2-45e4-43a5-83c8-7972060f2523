<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ReconcileInvoiceBillingMonths extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invoices:reconcile-billing-months 
        {--fix : Sửa lỗi tự động}
        {--dry-run : Chỉ kiểm tra, không thực hiện thay đổi}
        {--month= : Tháng cụ thể cần kiểm tra (format YYYY-MM)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Kiểm tra và đối chiếu billing_month của hóa đơn với tháng báo cáo tài chính';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Bắt đầu kiểm tra và đối chiếu billing_month...');

        // L<PERSON>y tháng cần kiểm tra
        $month = null;
        if ($this->option('month')) {
            try {
                $month = Carbon::createFromFormat('Y-m', $this->option('month'));
                $this->info("Kiểm tra hóa đơn cho tháng: " . $month->format('m/Y'));
            } catch (\Exception $e) {
                $this->error("Định dạng tháng không hợp lệ. Sử dụng định dạng YYYY-MM");
                return 1;
            }
        }

        // Lấy danh sách các invoice items có seller_finance_id
        $query = InvoiceItem::whereNotNull('seller_finance_id')
            ->with(['invoice', 'sellerFinance']);

        // Lọc theo tháng nếu có
        if ($month) {
            $query->whereHas('sellerFinance', function($q) use ($month) {
                $q->whereMonth('month', $month->month)
                  ->whereYear('month', $month->year);
            });
        }

        $invoiceItems = $query->get();
        
        if ($invoiceItems->isEmpty()) {
            $this->info("Không tìm thấy hóa đơn nào để kiểm tra.");
            return 0;
        }

        $this->info("Tìm thấy " . $invoiceItems->count() . " mục hóa đơn để kiểm tra.");

        $issues = [];
        $fixed = 0;

        foreach ($invoiceItems as $item) {
            // Bỏ qua nếu không có invoice hoặc seller finance
            if (!$item->invoice || !$item->sellerFinance) {
                continue;
            }

            // Chỉ quan tâm các hóa đơn có seller_finance_id nhưng chưa có billing_month
            // hoặc billing_month khác với month của seller finance
            $invoice = $item->invoice;
            $finance = $item->sellerFinance;
            
            $financeMonth = Carbon::parse($finance->month)->startOfMonth();
            $billingMonth = $invoice->billing_month ? Carbon::parse($invoice->billing_month)->startOfMonth() : null;
            
            $hasMismatch = !$billingMonth || 
                $billingMonth->month != $financeMonth->month || 
                $billingMonth->year != $financeMonth->year;
            
            if ($hasMismatch) {
                $issues[] = [
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'seller_id' => $finance->seller_id,
                    'seller_name' => $finance->seller->name,
                    'finance_month' => $financeMonth->format('m/Y'),
                    'billing_month' => $billingMonth ? $billingMonth->format('m/Y') : 'NULL',
                    'amount' => $item->amount,
                ];
                
                // Sửa lỗi nếu được yêu cầu và không phải dry run
                if ($this->option('fix') && !$this->option('dry-run')) {
                    try {
                        $invoice->update([
                            'billing_month' => $financeMonth
                        ]);
                        $fixed++;
                        Log::info("Đã cập nhật billing_month cho hóa đơn", [
                            'invoice_id' => $invoice->id,
                            'invoice_number' => $invoice->invoice_number,
                            'old_billing_month' => $billingMonth ? $billingMonth->format('Y-m-d') : 'NULL',
                            'new_billing_month' => $financeMonth->format('Y-m-d')
                        ]);
                    } catch (\Exception $e) {
                        Log::error("Lỗi khi cập nhật billing_month", [
                            'invoice_id' => $invoice->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
        }
        
        if (empty($issues)) {
            $this->info("Không tìm thấy vấn đề nào, tất cả hóa đơn đều có billing_month đúng!");
            return 0;
        }
        
        // Hiển thị danh sách vấn đề
        $this->table(
            ['Invoice ID', 'Số hóa đơn', 'Seller ID', 'Tên seller', 'Tháng báo cáo', 'Tháng thanh toán', 'Số tiền'],
            $issues
        );
        
        $this->info("Tìm thấy " . count($issues) . " vấn đề.");
        
        if ($this->option('fix')) {
            if ($this->option('dry-run')) {
                $this->warn("Chế độ dry-run: Không có thay đổi nào được thực hiện.");
            } else {
                $this->info("Đã sửa $fixed/" . count($issues) . " vấn đề.");
            }
        } else {
            $this->info("Sử dụng --fix để tự động sửa các vấn đề.");
        }
        
        return 0;
    }
}
