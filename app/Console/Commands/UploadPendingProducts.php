<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductToUpload;
use App\Services\Tiktok\ProductUploadService;
use App\Enums\ProductUploadStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UploadPendingProducts extends Command
{
    protected $signature = 'products:upload-pending';
    protected $description = 'Upload pending products to TikTok Shop';
    
    const MAX_ATTEMPTS = 3; // Số lần thử tối đa

    public function handle()
    {
        try {
            // Reset các sản phẩm bị treo ở trạng thái Processing quá 30 phút
            ProductToUpload::where('status', ProductUploadStatus::Processing)
                ->where('updated_at', '<', Carbon::now()->subMinutes(30))
                ->update([
                    'status' => ProductUploadStatus::Pending,
                    'error_message' => 'Reset from stuck processing state'
                ]);

            // Lấy và lock 5 sản phẩm pending để xử lý
            $pendingProducts = DB::transaction(function () {
                $products = ProductToUpload::readyToUpload()
                    ->where(function ($query) {
                        $query->where('status', ProductUploadStatus::Pending)
                            ->orWhere(function ($q) {
                                $q->where('status', ProductUploadStatus::Failed)
                                    ->where('attempts', '<', self::MAX_ATTEMPTS)
                                    ->where('updated_at', '<', Carbon::now()->subMinutes(15));
                            });
                    })
                    ->orderBy('scheduled_at')
                    ->limit(5)
                    ->lockForUpdate()
                    ->get();

                foreach ($products as $product) {
                    $attempts = $product->attempts + 1;
                    $product->update([
                        'status' => ProductUploadStatus::Processing,
                        'attempts' => $attempts,
                        'last_attempt_at' => now(),
                        'error_message' => $attempts == self::MAX_ATTEMPTS ? 
                            'Maximum retry attempts reached' : 
                            $product->error_message
                    ]);
                }

                return $products;
            }, 3);

            if ($pendingProducts->isEmpty()) {
                $this->info('No pending products to upload.');
                return;
            }

            $this->info("Processing {$pendingProducts->count()} products.");
            $uploadService = new ProductUploadService();

            foreach ($pendingProducts as $product) {
                try {
                    $this->info("Uploading product: {$product->product_title} (Attempt {$product->attempts} of " . self::MAX_ATTEMPTS . ")");
                    
                    $response = $uploadService->uploadProduct($product);

                    if ($response['product_id']) {
                        $product->update([
                            'status' => ProductUploadStatus::Completed,
                            'uploaded_at' => now(),
                            'product_id' => $response['product_id'],
                            'error_message' => null,
                        ]);

                        $this->info("✓ Successfully uploaded product ID: {$response['product_id']}");
                    } else {
                        $errorMessage = $response['message'] ?? 'Unknown error';
                        throw new \Exception($errorMessage);
                    }
                } catch (\Exception $e) {
                    $status = $product->attempts >= self::MAX_ATTEMPTS ? 
                        ProductUploadStatus::Failed : 
                        ProductUploadStatus::Pending;

                    $product->update([
                        'status' => $status,
                        'error_message' => $e->getMessage()
                    ]);

                    $this->error("✗ Failed to upload {$product->product_title}: {$e->getMessage()} (Attempt {$product->attempts} of " . self::MAX_ATTEMPTS . ")");
                }

                sleep(1);
            }
        } catch (\Exception $e) {
            $this->error("Error processing products: {$e->getMessage()}");
        }
    }
} 