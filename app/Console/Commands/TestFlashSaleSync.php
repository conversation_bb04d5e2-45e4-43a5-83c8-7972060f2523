<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Store;
use App\Models\FlashSale;
use App\Jobs\TiktokSyncStoreFlashSales;
use App\Jobs\TiktokProductFlashSale;
use Illuminate\Support\Facades\Log;

class TestFlashSaleSync extends Command
{
    protected $signature = 'flashsale:test {--store=} {--flash-sale=}';
    protected $description = 'Test flash sale synchronization';

    public function handle()
    {
        $storeId = $this->option('store');
        $flashSaleId = $this->option('flash-sale');

        try {
            if ($flashSaleId) {
                // Test specific flash sale sync
                $flashSale = FlashSale::findOrFail($flashSaleId);
                $this->info("Testing sync for flash sale: {$flashSale->title}");
                
                TiktokProductFlashSale::dispatchSync($flashSale);
                
                $flashSale->refresh();
                $this->info("Flash sale sync completed:");
                $this->info("- Status: {$flashSale->status}");
                $this->info("- Product Count: {$flashSale->product_count}");
                if (isset($flashSale->meta_data['tiktok_status'])) {
                    $this->info("- Tiktok Status: {$flashSale->meta_data['tiktok_status']}");
                }
            } 
            elseif ($storeId) {
                // Test store-wide sync
                $store = Store::findOrFail($storeId);
                $this->info("Testing sync for store: {$store->name}");
                
                // Get existing flash sales before sync
                $existingFlashSales = FlashSale::where('store_id', $store->id)
                    ->whereDate('created_at', today())
                    ->pluck('id')
                    ->toArray();
                
                TiktokSyncStoreFlashSales::dispatchSync($store);
                
                // Get flash sales after sync to identify new ones
                $currentFlashSales = FlashSale::where('store_id', $store->id)
                    ->whereDate('created_at', today())
                    ->get();
                
                $this->info("\nSync Results:");
                $this->info("Total Flash Sales: " . $currentFlashSales->count());
                
                foreach ($currentFlashSales as $flashSale) {
                    $status = in_array($flashSale->id, $existingFlashSales) ? 'Updated' : 'Created';
                    $this->info("\n{$status} Flash Sale:");
                    $this->info("- Title: {$flashSale->title}");
                    $this->info("- Status: {$flashSale->status}");
                    $this->info("- Product Count: {$flashSale->product_count}");
                    if (isset($flashSale->meta_data['tiktok_status'])) {
                        $this->info("- Tiktok Status: {$flashSale->meta_data['tiktok_status']}");
                    }
                }
            } 
            else {
                // List available stores and flash sales
                $this->info("Available stores:");
                Store::all()->each(function ($store) {
                    $this->info("\nStore ID: {$store->id} - {$store->name}");
                    $this->info("Auto Renew: " . ($store->auto_renew_flash_sale ? 'Yes' : 'No'));
                    
                    $flashSales = FlashSale::where('store_id', $store->id)
                        ->whereDate('created_at', today())
                        ->get();
                    
                    if ($flashSales->isNotEmpty()) {
                        $this->info("Flash Sales:");
                        foreach ($flashSales as $flashSale) {
                            $this->info("- ID: {$flashSale->id}");
                            $this->info("  Title: {$flashSale->title}");
                            $this->info("  Status: {$flashSale->status}");
                            $this->info("  Products: {$flashSale->product_count}");
                            if (isset($flashSale->meta_data['tiktok_status'])) {
                                $this->info("  Tiktok Status: {$flashSale->meta_data['tiktok_status']}");
                            }
                        }
                    } else {
                        $this->info("No flash sales found today");
                    }
                });

                $this->info("\nUsage:");
                $this->info("Test specific flash sale: php artisan flashsale:test --flash-sale=<id>");
                $this->info("Test store sync: php artisan flashsale:test --store=<id>");
            }
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            Log::channel('tiktok-flashdeal')->error('Flash sale test error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
