<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\App;
use Exception;

class TestFlashShipRawAPI extends Command
{
    protected $signature = 'test:flashship-raw-api';
    protected $description = 'Test FlashShip API raw responses to understand the actual format';

    public function handle()
    {
        $this->info("🔍 KIỂM TRA RAW FLASHSHIP API RESPONSES");
        $this->info("=====================================\n");

        try {
            // Get API URL and credentials
            $apiUrl = App::environment('production') 
                ? 'https://api.flashship.net/seller-api-v2/' 
                : 'https://devpod.flashship.net/seller-api-v2/';
            
            $credentials = [
                'username' => App::environment('production') ? env('FLASHSHIP_USERNAME') : 'pod080',
                'password' => App::environment('production') ? env('FLASHSHIP_PASSWORD') : '@Test123456'
            ];
            
            $this->info("🌐 API URL: " . $apiUrl);
            $this->info("👤 Username: " . $credentials['username']);
            $this->newLine();
            
            // Test 1: Get Token - Raw Response
            $this->info("=== TEST 1: Get Token - Raw Response ===");
            
            $tokenResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post($apiUrl . 'token', $credentials);
            
            $this->line("Status Code: " . $tokenResponse->status());
            $this->line("Raw Body: " . $tokenResponse->body());
            $this->line("JSON Decoded: " . json_encode($tokenResponse->json(), JSON_PRETTY_PRINT));
            $this->newLine();
            
            $tokenData = $tokenResponse->json();
            $token = null;
            
            if (isset($tokenData['data']['access_token'])) {
                $token = $tokenData['data']['access_token'];
                $this->info("✅ Token extracted: " . substr($token, 0, 20) . "...");
            } else {
                $this->error("❌ Cannot extract token");
                return 1;
            }
            $this->newLine();
            
            // Test 2: Test createOrder - Raw Response
            $this->info("=== TEST 2: Test createOrder - Raw Response ===");
            
            $orderData = [
                'partner_order_id' => 'TEST_RAW_' . time(),
                'shipment' => 1,
                'label' => [
                    'name' => 'Test Customer',
                    'phone' => '0123456789',
                    'address' => 'Test Address',
                    'province' => 'Ho Chi Minh',
                    'district' => 'District 1',
                    'ward' => 'Ward 1'
                ]
            ];
            
            $createResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token
            ])->post($apiUrl . 'orders/shirt-add', $orderData);
            
            $this->line("Status Code: " . $createResponse->status());
            $this->line("Raw Body: " . $createResponse->body());
            $this->line("JSON Decoded: " . json_encode($createResponse->json(), JSON_PRETTY_PRINT));
            $this->newLine();
            
            // Test 3: Test getOrder - Raw Response
            $this->info("=== TEST 3: Test getOrder - Raw Response ===");
            
            $getResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token
            ])->get($apiUrl . 'orders/TEST_ORDER_123');
            
            $this->line("Status Code: " . $getResponse->status());
            $this->line("Raw Body: " . $getResponse->body());
            $this->line("JSON Decoded: " . json_encode($getResponse->json(), JSON_PRETTY_PRINT));
            $this->newLine();
            
            // Test 4: Test getOrderByPartnerOrderId - Raw Response
            $this->info("=== TEST 4: Test getOrderByPartnerOrderId - Raw Response ===");
            
            $partnerData = ['partner_order_id' => 'TEST_PARTNER_123'];
            $partnerResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token
            ])->post($apiUrl . 'orders/partner-order-id', $partnerData);
            
            $this->line("Status Code: " . $partnerResponse->status());
            $this->line("Raw Body: " . $partnerResponse->body());
            $this->line("JSON Decoded: " . json_encode($partnerResponse->json(), JSON_PRETTY_PRINT));
            $this->newLine();
            
            // Test 5: Analyze Response Patterns
            $this->info("=== TEST 5: Analyze Response Patterns ===");
            
            $responses = [
                'token' => $tokenResponse->json(),
                'createOrder' => $createResponse->json(),
                'getOrder' => $getResponse->json(),
                'getOrderByPartnerOrderId' => $partnerResponse->json()
            ];
            
            foreach ($responses as $endpoint => $response) {
                $this->line("📋 Endpoint: $endpoint");
                $this->line("  - Has 'msg' field: " . (isset($response['msg']) ? 'Yes (' . $response['msg'] . ')' : 'No'));
                $this->line("  - Has 'data' field: " . (isset($response['data']) ? 'Yes' : 'No'));
                $this->line("  - Has 'err' field: " . (isset($response['err']) ? 'Yes (' . $response['err'] . ')' : 'No'));
                $this->line("  - Has 'code' field: " . (isset($response['code']) ? 'Yes (' . $response['code'] . ')' : 'No'));
                
                if (isset($response['data'])) {
                    $this->line("  - Data type: " . gettype($response['data']));
                    if (is_array($response['data']) && !empty($response['data'])) {
                        $this->line("  - Data keys: " . implode(', ', array_keys($response['data'])));
                    }
                }
                $this->newLine();
            }
            
            // Test 6: Compare with current logic
            $this->info("=== TEST 6: Compare with current logic ===");
            
            $this->warn("🔍 CURRENT LOGIC ANALYSIS:");
            $this->line("Current: 'success' => isset(\$responseBody['msg']) ? (\$responseBody['msg'] == 'success') : true");
            $this->newLine();
            
            foreach ($responses as $endpoint => $response) {
                $currentLogicSuccess = isset($response['msg']) ? ($response['msg'] == 'success') : true;
                $actualSuccess = isset($response['msg']) && $response['msg'] == 'success';
                
                $this->line("📋 $endpoint:");
                $this->line("  - Current logic would return success: " . ($currentLogicSuccess ? 'true' : 'false'));
                $this->line("  - Actual success should be: " . ($actualSuccess ? 'true' : 'false'));
                $this->line("  - Match: " . ($currentLogicSuccess === $actualSuccess ? '✅ Yes' : '❌ No'));
                $this->newLine();
            }
            
            $this->info("🎯 CRITICAL FINDINGS:");
            $this->info("====================");
            
            // Check if any response doesn't have 'msg' field
            $noMsgResponses = array_filter($responses, function($response) {
                return !isset($response['msg']);
            });
            
            if (!empty($noMsgResponses)) {
                $this->error("❌ FOUND RESPONSES WITHOUT 'msg' FIELD:");
                foreach ($noMsgResponses as $endpoint => $response) {
                    $this->line("  - $endpoint: Current logic would return success=true incorrectly");
                }
            } else {
                $this->info("✅ All responses have 'msg' field");
            }
            
            $this->newLine();
            $this->warn("⚠️  RECOMMENDATIONS:");
            $this->line("1. Check if response is successful HTTP status first");
            $this->line("2. Then check if 'msg' field exists and equals 'success'");
            $this->line("3. Handle cases where 'msg' field is missing");
            $this->line("4. Consider checking 'code' field for error codes");
            
        } catch (Exception $e) {
            $this->error("❌ Error testing raw API: " . $e->getMessage());
            $this->line("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
        
        return 0;
    }
}
