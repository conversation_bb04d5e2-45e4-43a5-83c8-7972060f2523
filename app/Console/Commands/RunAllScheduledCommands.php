<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class RunAllScheduledCommands extends Command
{
    protected $signature = 'schedule:run-all {--parallel : Run commands in parallel} {--force : Force run all commands regardless of schedule}';
    protected $description = 'Run all scheduled commands immediately';

    public function handle()
    {
        $parallel = $this->option('parallel');

        if ($parallel) {
            $this->runParallel();
        } else {
            $this->runSequential();
        }
    }

    private function runSequential()
    {
        $this->info('🚀 Running all scheduled commands sequentially...');

        $commands = [
            'orders:fulfill-auto',
            'stores:sync-orders',
            'orders:update-status',
            'stores:sync-products',
            'proxy:check',
            'stores:sync-payments',
            'stores:check-bank-status',
            'products:generate-keywords',
        ];

        $successCount = 0;
        $failedCount = 0;

        foreach ($commands as $command) {
            $this->info("🔄 Running: {$command}");
            try {
                $exitCode = $this->call($command);
                if ($exitCode === 0) {
                    $this->info("✅ Completed: {$command}");
                    $successCount++;
                } else {
                    $this->error("❌ Failed: {$command} (exit code: {$exitCode})");
                    $failedCount++;
                }
            } catch (\Exception $e) {
                $this->error("❌ Failed: {$command} - " . $e->getMessage());
                $failedCount++;
            }
            $this->newLine();
        }

        $this->info("🎉 All commands completed!");
        $this->info("✅ Success: {$successCount}");
        $this->info("❌ Failed: {$failedCount}");
    }

    private function runParallel()
    {
        $this->info('🚀 Running all scheduled commands in parallel...');

        $commands = [
            'orders:fulfill-auto',
            'stores:sync-orders',
            'orders:update-status',
            'stores:sync-products',
            'proxy:check',
        ];

        $this->info("⏳ Starting all processes...");

        // Start all processes in background
        $commandString = '';
        foreach ($commands as $i => $command) {
            $this->info("🔄 Starting: {$command}");
            if ($i > 0) {
                $commandString .= ' & ';
            }
            $commandString .= "php artisan {$command}";
        }
        $commandString .= ' & wait';

        $this->info("📋 Executing: {$commandString}");

        // Execute all commands in parallel
        $startTime = time();
        $output = shell_exec($commandString);
        $endTime = time();
        $duration = $endTime - $startTime;

        $this->info("🎉 All parallel commands completed in {$duration} seconds!");

        if ($output) {
            $this->info("📄 Output:");
            $this->line($output);
        }
    }
}
