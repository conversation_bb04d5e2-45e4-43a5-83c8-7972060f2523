<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SupplierProduct;
use App\Models\Supplier;
use Illuminate\Support\Facades\Http;
use App\Traits\StyleNormalization;

class ImportGearmentVariants extends Command
{
    use StyleNormalization;

    protected $signature = 'import:gearment-variants';
    protected $description = 'Import variants from Gearment API';

    public function handle()
    {
        try {
            $supplier = Supplier::firstOrCreate(['name' => 'Gearment']);
            SupplierProduct::where('supplier_id', $supplier->id)->delete();
            $this->info("Using supplier: {$supplier->name} (ID: {$supplier->id})");

            $response = Http::post('https://api.gearment.com/v2/?act=products', [
                'api_key' => 'SmQU9k7NkrrpwuQJ',
                'api_signature' => 'JuqJsDpGMqKBV3l8PpC0QHB4yRkozux6'
            ], [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ]);
            if (!$response->successful()) {
                $this->error("Failed to fetch variants from Gearment API");
                return 1;
            }

            $products = $response->json('result');
            $importCount = 0;
            $updateCount = 0;

            foreach ($products as $product) {
                foreach ($product['variants'] as $variant) {
                    try {
                        $normalizedData = [
                            'variant_id' => (string)$variant['variant_id'],
                            'style' => $this->normalizeStyle($variant['name']) ?: $variant['name'],
                            'color' => $this->normalizeColor($variant['color']) ?: strtoupper($variant['color']),
                            'size' => $this->normalizeSize($variant['size']) ?: $variant['size'],
                            'active' => $variant['availability_status'] === 'in_stock',
                            'price' => floatval($variant['price']),
                            'tiktok' => true
                        ];

                        $sku = "{$variant['name']}-{$variant['variant_id']}";
                        
                        $product = SupplierProduct::updateOrCreate(
                            [
                                'supplier_id' => $supplier->id,
                                'sku' => $sku
                            ],
                            $normalizedData
                        );

                        if ($product->wasRecentlyCreated) {
                            $importCount++;
                            $this->info("Created: {$sku} (Color: {$normalizedData['color']})");
                        } else {
                            $updateCount++;
                            $this->info("Updated: {$sku} (Color: {$normalizedData['color']})");
                        }

                    } catch (\Exception $e) {
                        $this->error("Error processing variant  " . $e->getMessage());
                    }
                }
            }

            $this->reportUnknownValues();
            $this->info("\nImport completed:");
            $this->info("- Created: {$importCount}");
            $this->info("- Updated: {$updateCount}");
            return 0;

        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return 1;
        }
    }
}