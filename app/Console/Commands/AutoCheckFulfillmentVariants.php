<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Order;
use App\Models\SupplierOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Services\TelegramNotificationService;
use Illuminate\Support\Facades\Cache;

class AutoCheckFulfillmentVariants extends Command
{
    protected $signature = 'fulfillment:check-variants {--send-telegram} {--clear-cache} {--show-cache}';
    protected $description = 'Tự động kiểm tra variant fulfillment cho các đơn hàng hôm nay';

    public function handle()
    {
        $this->info('🔍 Bắt đầu kiểm tra Fulfillment Variants...');
        $this->info('=====================================');

        // Log bắt đầu command
        Log::channel('check_fulfill')->info('=== BẮT ĐẦU KIỂM TRA FULFILLMENT VARIANTS ===', [
            'timestamp' => Carbon::now()->toDateTimeString(),
            'command' => 'fulfillment:check-variants',
            'options' => [
                'send_telegram' => $this->option('send-telegram'),
                'clear_cache' => $this->option('clear-cache'),
                'show_cache' => $this->option('show-cache'),
            ]
        ]);

        // Xử lý các tùy chọn
        if ($this->option('clear-cache')) {
            $this->clearTodayCache();
            $this->info('✅ Đã xóa cache hôm nay');
            Log::channel('check_fulfill')->info('Cache đã được xóa');
            return 0;
        }

        if ($this->option('show-cache')) {
            $this->showCacheStatus();
            return 0;
        }

        // Lấy supplier orders hôm nay (logic đúng - không bỏ sót vì check lần lượt)
        $today = Carbon::today();
        $supplierOrders = $this->getTodaySupplierOrders($today);

        $this->info("📊 Tìm thấy {$supplierOrders->count()} supplier orders hôm nay");

        // Lọc ra những supplier orders chưa được check
        $uncheckedSupplierOrders = $this->filterUncheckedSupplierOrders($supplierOrders);

        $totalSupplierOrders = $supplierOrders->count();
        $uncheckedCount = $uncheckedSupplierOrders->count();
        $checkedCount = $totalSupplierOrders - $uncheckedCount;

        // Log thống kê
        Log::channel('check_fulfill')->info('THỐNG KÊ SUPPLIER ORDERS HÔM NAY', [
            'total_supplier_orders' => $totalSupplierOrders,
            'checked_count' => $checkedCount,
            'unchecked_count' => $uncheckedCount,
            'date' => Carbon::today()->format('Y-m-d')
        ]);

        $this->info("🔍 Cần kiểm tra {$uncheckedCount} supplier orders mới (Đã check: {$checkedCount})");

        if ($supplierOrders->isEmpty()) {
            $this->info('✅ Không có supplier order nào cần kiểm tra');
            Log::channel('check_fulfill')->info('Không có supplier order nào hôm nay');
            return 0;
        }

        if ($uncheckedSupplierOrders->isEmpty()) {
            $this->info('✅ Tất cả supplier orders hôm nay đã được kiểm tra');
            Log::channel('check_fulfill')->info('Tất cả supplier orders đã được kiểm tra');
            return 0;
        }

        $allIssues = [];
        $totalChecked = 0;
        $ordersWithIssues = 0;
        $checkedSupplierOrderIds = [];

        foreach ($uncheckedSupplierOrders as $supplierOrder) {
            $order = $supplierOrder->order;
            $this->line("\n--- Kiểm tra Supplier Order: {$supplierOrder->id} (Đơn: {$order->order_code}) ---");

            $issues = $this->checkSupplierOrder($order, $supplierOrder);

            if (!empty($issues)) {
                $ordersWithIssues++;
                $allIssues[] = [
                    'order_code' => $order->order_code,
                    'supplier' => $supplierOrder->supplier->name ?? 'N/A',
                    'supplier_order_id' => $supplierOrder->supplier_order_id ?? 'N/A',
                    'issues' => $issues,
                    'created_at' => $order->created_at->format('d/m/Y H:i'),
                ];

                // Log lỗi chi tiết với thông tin quantity
                $formData = $this->getFormDataVariants($supplierOrder);
                $logStatus = $this->getLogStatusVariants($supplierOrder);
                $original = $order->orderItems->toArray();

                Log::channel('check_fulfill')->error('PHÁT HIỆN VẤN ĐỀ FULFILLMENT', [
                    'supplier_order_id' => $supplierOrder->id,
                    'order_code' => $order->order_code,
                    'supplier' => $supplierOrder->supplier->name ?? 'N/A',
                    'supplier_order_code' => $supplierOrder->supplier_order_id ?? 'N/A',
                    'issues_count' => count($issues),
                    'issues' => $issues,
                    'quantity_summary' => [
                        'original_total' => array_sum(array_column($original, 'quantity')),
                        'form_data_total' => array_sum(array_column($formData, 'quantity')),
                        'log_status_total' => array_sum(array_column($logStatus, 'quantity')),
                        'original_count' => count($original),
                        'form_data_count' => count($formData),
                        'log_status_count' => count($logStatus),
                    ],
                    'order_created_at' => $order->created_at->toDateTimeString(),
                    'supplier_order_created_at' => $supplierOrder->created_at->toDateTimeString(),
                ]);

                $this->warn("  ⚠️  {$supplierOrder->supplier->name}: " . count($issues) . " vấn đề");
                foreach ($issues as $issue) {
                    $this->line("    • {$issue}");
                }
            } else {
                $this->info("  ✅ {$supplierOrder->supplier->name}: OK");

                // Log thành công
                Log::channel('check_fulfill')->info('SUPPLIER ORDER OK', [
                    'supplier_order_id' => $supplierOrder->id,
                    'order_code' => $order->order_code,
                    'supplier' => $supplierOrder->supplier->name ?? 'N/A',
                ]);
            }

            // Đánh dấu supplier order đã được check
            $this->markSupplierOrderAsChecked($supplierOrder->id);
            $checkedSupplierOrderIds[] = $supplierOrder->id;
            $totalChecked++;
        }

        // Tóm tắt kết quả
        $this->info("\n📈 KẾT QUẢ KIỂM TRA:");
        $this->info("- Tổng supplier orders hôm nay: {$totalSupplierOrders}");
        $this->info("- Supplier orders đã check trước đó: {$checkedCount}");
        $this->info("- Supplier orders check lần này: {$totalChecked}");
        $this->info("- Supplier orders có vấn đề: {$ordersWithIssues}");
        $this->info("- Tổng vấn đề: " . count($allIssues));
        $this->info("- IDs đã check lần này: " . implode(', ', $checkedSupplierOrderIds));

        // Log tóm tắt kết quả
        Log::channel('check_fulfill')->info('KẾT QUẢ KIỂM TRA HOÀN THÀNH', [
            'total_supplier_orders_today' => $totalSupplierOrders,
            'previously_checked' => $checkedCount,
            'checked_this_run' => $totalChecked,
            'orders_with_issues' => $ordersWithIssues,
            'total_issues' => count($allIssues),
            'checked_supplier_order_ids' => $checkedSupplierOrderIds,
            'execution_time' => Carbon::now()->toDateTimeString(),
        ]);

        // Log kết quả
        $this->logResults($allIssues, $totalChecked, $ordersWithIssues);

        // Gửi Telegram báo cáo nếu có vấn đề
        if (!empty($allIssues) && $this->option('send-telegram')) {
            $this->sendTelegramReport($allIssues, $totalChecked, $ordersWithIssues);
            Log::channel('check_fulfill')->info('Đã gửi báo cáo Telegram', [
                'issues_count' => count($allIssues),
                'orders_with_issues' => $ordersWithIssues
            ]);
        }

        if (empty($allIssues)) {
            $this->info("\n🎉 Tất cả đơn hàng đều OK!");
            Log::channel('check_fulfill')->info('=== HOÀN THÀNH: TẤT CẢ ĐƠN HÀNG OK ===');
        } else {
            $this->warn("\n⚠️  Phát hiện vấn đề cần xử lý!");
            Log::channel('check_fulfill')->warning('=== HOÀN THÀNH: CÓ VẤN ĐỀ CẦN XỬ LÝ ===', [
                'total_issues' => count($allIssues),
                'orders_with_issues' => $ordersWithIssues
            ]);
        }

        return 0;
    }

    private function getTodaySupplierOrders($today)
    {
        return SupplierOrder::with([
            'order:id,order_code,created_at', // Chỉ lấy fields cần thiết
            'order.orderItems:id,order_id,quantity,name,sku,image', // Tối ưu fields
            'order.orderItems.productVariant:id,variant_name,variant_id,variant_fulfill_name', // Tối ưu fields
            'supplier:id,name' // Chỉ lấy fields cần thiết
        ])
        ->whereNotNull('form_data')
        ->whereNotNull('log_status')
        ->whereDate('created_at', $today)
        ->limit(10)
        ->get();
    }

    private function filterUncheckedSupplierOrders($supplierOrders)
    {
        return $supplierOrders->filter(function ($supplierOrder) {
            return !$this->isSupplierOrderChecked($supplierOrder->id);
        });
    }

    private function getCacheKey($supplierOrderId)
    {
        $today = Carbon::today()->format('Y-m-d');
        return "fulfillment_variant_checked_{$today}_{$supplierOrderId}";
    }

    private function isSupplierOrderChecked($supplierOrderId)
    {
        $cacheKey = $this->getCacheKey($supplierOrderId);
        return Cache::has($cacheKey);
    }

    private function markSupplierOrderAsChecked($supplierOrderId)
    {
        $cacheKey = $this->getCacheKey($supplierOrderId);
        // Cache 24 tiếng (1440 phút) - đủ để tránh check trùng lặp trong ngày
        Cache::put($cacheKey, Carbon::now()->toDateTimeString(), 1440);

        $this->line("    📝 Đã cache supplier order {$supplierOrderId}");
    }

    private function clearTodayCache()
    {
        $today = Carbon::today();
        $supplierOrders = $this->getTodaySupplierOrders($today);

        $cleared = 0;
        foreach ($supplierOrders as $supplierOrder) {
            $cacheKey = $this->getCacheKey($supplierOrder->id);
            if (Cache::forget($cacheKey)) {
                $cleared++;
            }
        }

        $this->info("🗑️ Đã xóa {$cleared} cache entries hôm nay");
    }

    private function showCacheStatus()
    {
        $today = Carbon::today();
        $supplierOrders = $this->getTodaySupplierOrders($today);

        $this->info("📊 TRẠNG THÁI CACHE HÔM NAY:");
        $this->info("============================");

        $cached = 0;
        $uncached = 0;

        foreach ($supplierOrders as $supplierOrder) {
            $isChecked = $this->isSupplierOrderChecked($supplierOrder->id);
            $status = $isChecked ? '✅ Đã check' : '❌ Chưa check';

            $this->line("Supplier Order {$supplierOrder->id} ({$supplierOrder->order->order_code}): {$status}");

            if ($isChecked) {
                $cached++;
            } else {
                $uncached++;
            }
        }

        $this->info("\n📈 TỔNG KẾT:");
        $this->info("- Tổng supplier orders: {$supplierOrders->count()}");
        $this->info("- Đã check (cached): {$cached}");
        $this->info("- Chưa check: {$uncached}");
    }

    private function checkSupplierOrder($order, $supplierOrder)
    {
        // Sử dụng logic từ FulfillmentVariantChecker
        $formData = $this->getFormDataVariants($supplierOrder);
        $logStatus = $this->getLogStatusVariants($supplierOrder);
        $original = $order->orderItems->toArray();

        return $this->compareVariants($original, $formData, $logStatus);
    }

    private function getFormDataVariants($supplierOrder)
    {
        $formData = $supplierOrder->form_data ?? [];
        $variants = [];
        
        if (isset($formData['items'])) {
            foreach ($formData['items'] as $item) {
                if (isset($item['selected']) && $item['selected']) {
                    $variants[] = [
                        'variant' => $item['variant'] ?? 'N/A',
                        'quantity' => $item['quantity'] ?? 0,
                        'my_sku' => $item['my_sku'] ?? 'N/A',
                        'mockup_front' => $item['mockup_front'] ?? null,
                        'mockup_back' => $item['mockup_back'] ?? null,
                        'front_design' => $item['front_design'] ?? null,
                        'back_design' => $item['back_design'] ?? null,
                    ];
                }
            }
        }
        
        return $variants;
    }

    private function getLogStatusVariants($supplierOrder)
    {
        $logStatus = $supplierOrder->log_status ?? [];
        $variants = [];
        
        if (isset($logStatus['data']['products'])) {
            foreach ($logStatus['data']['products'] as $item) {
                $variants[] = [
                    'variant_id' => $item['variant_id'] ?? 'N/A',
                    'quantity' => $item['quantity'] ?? 0,
                    'line_id' => $item['lineId'] ?? 'N/A',
                    'variant_sku' => $item['variant_sku'] ?? 'N/A',
                    'mockup_front' => $item['mockup_front'] ?? null,
                    'mockup_back' => $item['mockup_back'] ?? null,
                    'front_print_url' => $item['front_print_url'] ?? null,
                    'back_print_url' => $item['back_print_url'] ?? null,
                ];
            }
        }
        
        return $variants;
    }

    private function compareVariants($original, $formData, $logStatus)
    {
        $issues = [];

        $originalCount = count($original);
        $formDataCount = count($formData);
        $logStatusCount = count($logStatus);

        // So sánh số lượng items
        if ($originalCount !== $formDataCount) {
            $issues[] = "Số lượng variant không khớp: TikTok gốc ({$originalCount} items) vs Form Data ({$formDataCount} variants)";
        }

        if ($formDataCount !== $logStatusCount) {
            $issues[] = "Số lượng variant không khớp: Form Data ({$formDataCount} variants) vs Log Status ({$logStatusCount} variants)";
        }

        // So sánh tổng quantity
        $originalTotalQty = array_sum(array_column($original, 'quantity'));
        $formDataTotalQty = array_sum(array_column($formData, 'quantity'));
        $logStatusTotalQty = array_sum(array_column($logStatus, 'quantity'));

        if ($originalTotalQty !== $formDataTotalQty) {
            $issues[] = "Tổng quantity không khớp: TikTok gốc ({$originalTotalQty}) vs Form Data ({$formDataTotalQty})";
        }

        if ($formDataTotalQty !== $logStatusTotalQty) {
            $issues[] = "Tổng quantity không khớp: Form Data ({$formDataTotalQty}) vs Log Status ({$logStatusTotalQty})";
        }

        // Kiểm tra quantity và ảnh
        foreach ($formData as $index => $formVariant) {
            if (isset($logStatus[$index])) {
                $logVariant = $logStatus[$index];
                
                // Kiểm tra quantity
                $formQuantity = $formVariant['quantity'] ?? 0;
                $logQuantity = $logVariant['quantity'] ?? 0;
                
                if ($formQuantity !== $logQuantity) {
                    $variantId = $formVariant['variant'] ?? ($index + 1);
                    $issues[] = "Variant {$variantId}: Số lượng không khớp - Form Data ({$formQuantity}) vs Log Status ({$logQuantity})";
                }

                // Kiểm tra ảnh
                $formImageCount = 0;
                if (!empty($formVariant['mockup_front'])) $formImageCount++;
                if (!empty($formVariant['mockup_back'])) $formImageCount++;
                if (!empty($formVariant['front_design'])) $formImageCount++;
                if (!empty($formVariant['back_design'])) $formImageCount++;
                
                $logImageCount = 0;
                if (!empty($logVariant['mockup_front'])) $logImageCount++;
                if (!empty($logVariant['mockup_back'])) $logImageCount++;
                if (!empty($logVariant['front_print_url'])) $logImageCount++;
                if (!empty($logVariant['back_print_url'])) $logImageCount++;
                
                if ($formImageCount !== $logImageCount) {
                    $variantId = $formVariant['variant'] ?? ($index + 1);
                    $issues[] = "Variant {$variantId}: Số ảnh không khớp - Form Data ({$formImageCount} ảnh) vs Log Status ({$logImageCount} ảnh)";
                }
            }
        }
        
        return $issues;
    }

    private function logResults($allIssues, $totalChecked, $ordersWithIssues)
    {
        Log::channel('single')->info('Fulfillment Variant Check Results', [
            'date' => Carbon::now()->format('Y-m-d H:i:s'),
            'total_checked' => $totalChecked,
            'orders_with_issues' => $ordersWithIssues,
            'total_issues' => count($allIssues),
            'issues' => $allIssues,
        ]);
    }

    private function sendTelegramReport($allIssues, $totalChecked, $ordersWithIssues)
    {
        try {
            $this->info("📱 Gửi báo cáo Telegram...");

            $message = $this->formatTelegramMessage($allIssues, $totalChecked, $ordersWithIssues);

            $telegramService = app(TelegramNotificationService::class);
            $result = $telegramService->sendFulfillmentVariantCheckNotification($message);

            if ($result) {
                $this->info("✅ Telegram đã được gửi thành công");
                Log::channel('check_fulfill')->info('Telegram notification gửi thành công');
            } else {
                $this->error("❌ Telegram gửi thất bại");
                Log::channel('check_fulfill')->error('Telegram notification gửi thất bại');
            }
        } catch (\Exception $e) {
            $this->error("❌ Lỗi gửi Telegram: " . $e->getMessage());
            Log::channel('check_fulfill')->error('Lỗi gửi Telegram notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    private function formatTelegramMessage($allIssues, $totalChecked, $ordersWithIssues)
    {
        $date = Carbon::now()->format('d/m/Y H:i');
        $checkerUrl = config('app.url') . '/app/fulfillment-variant-checker';

        $message = "🔍 <b>BÁO CÁO KIỂM TRA FULFILLMENT VARIANTS</b>\n";
        $message .= "📅 Thời gian: {$date}\n\n";

        $message .= "📊 <b>TỔNG QUAN:</b>\n";
        $message .= "• Tổng supplier orders: {$totalChecked}\n";
        $message .= "• Đơn có vấn đề: {$ordersWithIssues}\n";
        $message .= "• Tổng vấn đề: " . count($allIssues) . "\n\n";

        if (!empty($allIssues)) {
            $message .= "⚠️ <b>CHI TIẾT VẤN ĐỀ:</b>\n";

            foreach ($allIssues as $index => $issue) {
                if ($index >= 5) { // Giới hạn 5 đơn đầu tiên để tránh message quá dài
                    $remaining = count($allIssues) - 5;
                    $message .= "... và {$remaining} đơn khác\n";
                    break;
                }

                $orderUrl = config('app.url') . '/app/orders?tableSearch=' . urlencode($issue['order_code']);

                $message .= "\n🛒 <b>{$issue['order_code']}</b>\n";
                $message .= "🏭 Supplier: {$issue['supplier']}\n";
                $message .= "📦 Order ID: {$issue['supplier_order_id']}\n";
                $message .= "🕐 Thời gian: {$issue['created_at']}\n";
                $message .= "🔗 <a href=\"{$orderUrl}\">Xem đơn hàng</a>\n";

                foreach ($issue['issues'] as $problemIndex => $problem) {
                    if ($problemIndex >= 3) { // Giới hạn 3 vấn đề đầu tiên
                        $remainingProblems = count($issue['issues']) - 3;
                        $message .= "   ... và {$remainingProblems} vấn đề khác\n";
                        break;
                    }
                    $message .= "   • {$problem}\n";
                }
            }
        } else {
            $message .= "✅ <b>TẤT CẢ ĐƠN HÀNG ĐỀU OK!</b>\n";
        }

        $message .= "\n🔗 <a href=\"{$checkerUrl}\">Xem chi tiết tại Fulfillment Variant Checker</a>";

        return $message;
    }
}
