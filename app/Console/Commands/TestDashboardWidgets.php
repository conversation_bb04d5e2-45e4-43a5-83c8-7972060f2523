<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Order;
use App\Filament\App\Widgets\OverallStatsOverview;
use App\Filament\App\Widgets\OverallOrdersChart;
use App\Filament\App\Widgets\OverallFulfillChart;

class TestDashboardWidgets extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'dashboard:test-widgets {--user-id= : Test for specific user ID}';

    /**
     * The console command description.
     */
    protected $description = 'Test dashboard widgets to debug data issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->option('user-id');
        
        if ($userId) {
            $user = User::find($userId);
            if (!$user) {
                $this->error("User with ID {$userId} not found");
                return;
            }
        } else {
            // Tìm user có role Leader hoặc User Manager
            $user = User::role(['Leader', 'User Manager', 'super_admin'])->first();
            if (!$user) {
                $this->error('No Leader/User Manager found');
                return;
            }
        }

        $this->info("Testing widgets for user: {$user->name} (ID: {$user->id})");
        
        // Login as user để test
        Auth::login($user);
        
        $this->testManagedSellers($user);
        $this->testOrdersData($user);
        $this->testStatsWidget();
        $this->testOrdersChart();
        $this->testFulfillChart();
    }

    private function testManagedSellers($user)
    {
        $this->info("\n🔍 Testing Managed Sellers...");
        
        if ($user->hasRole('super_admin')) {
            $sellers = User::role('Seller')->get();
            $this->info("Super admin - Found {$sellers->count()} sellers");
        } elseif ($user->hasRole(['Leader', 'User Manager'])) {
            $sellers = $user->managedSellers()->role('Seller')->get();
            $this->info("Leader/Manager - Found {$sellers->count()} managed sellers");
        } else {
            $this->warn("User has no appropriate role");
            return;
        }

        if ($sellers->count() > 0) {
            $this->table(
                ['ID', 'Name', 'Email'],
                $sellers->take(5)->map(function($seller) {
                    return [$seller->id, $seller->name, $seller->email];
                })->toArray()
            );
        }
    }

    private function testOrdersData($user)
    {
        $this->info("\n📊 Testing Orders Data...");
        
        // Get managed sellers
        if ($user->hasRole('super_admin')) {
            $sellerIds = User::role('Seller')->pluck('id')->toArray();
        } elseif ($user->hasRole(['Leader', 'User Manager'])) {
            $sellerIds = $user->leaderManagedSellers()->role('Seller')->pluck('id')->toArray();
        } else {
            $sellerIds = [];
        }

        if (empty($sellerIds)) {
            $this->warn("No seller IDs found");
            return;
        }

        // Test orders count
        $totalOrders = Order::whereIn('user_id', $sellerIds)->count();
        $completedOrders = Order::whereIn('user_id', $sellerIds)
            ->where('status', 'Completed')
            ->count();
        
        $this->info("Total orders: {$totalOrders}");
        $this->info("Completed orders: {$completedOrders}");
        
        // Test recent orders
        $recentOrders = Order::whereIn('user_id', $sellerIds)
            ->whereBetween('created_at', [now()->subDays(7), now()])
            ->count();
        
        $this->info("Orders in last 7 days: {$recentOrders}");
    }

    private function testStatsWidget()
    {
        $this->info("\n📈 Testing Stats Widget...");

        try {
            $widget = new OverallStatsOverview();

            // Sử dụng reflection để gọi protected method
            $reflection = new \ReflectionClass($widget);
            $method = $reflection->getMethod('getStats');
            $method->setAccessible(true);
            $stats = $method->invoke($widget);

            $this->info("Stats widget returned " . count($stats) . " stats");

            foreach ($stats as $index => $stat) {
                $this->info("  {$index}: {$stat->getLabel()} = {$stat->getValue()}");
            }
        } catch (\Exception $e) {
            $this->error("Stats widget error: " . $e->getMessage());
        }
    }

    private function testOrdersChart()
    {
        $this->info("\n📊 Testing Orders Chart Widget...");

        try {
            $widget = new OverallOrdersChart();

            // Sử dụng reflection để gọi protected method
            $reflection = new \ReflectionClass($widget);
            $method = $reflection->getMethod('getData');
            $method->setAccessible(true);
            $data = $method->invoke($widget);

            $this->info("Chart data structure:");
            $this->info("  Labels count: " . count($data['labels'] ?? []));
            $this->info("  Datasets count: " . count($data['datasets'] ?? []));

            if (!empty($data['datasets'])) {
                foreach ($data['datasets'] as $index => $dataset) {
                    $dataSum = array_sum($dataset['data'] ?? []);
                    $this->info("  Dataset {$index} ({$dataset['label']}): {$dataSum} total");
                }
            }
        } catch (\Exception $e) {
            $this->error("Orders chart widget error: " . $e->getMessage());
        }
    }

    private function testFulfillChart()
    {
        $this->info("\n🚚 Testing Fulfill Chart Widget...");

        try {
            $widget = new OverallFulfillChart();

            // Sử dụng reflection để gọi protected method
            $reflection = new \ReflectionClass($widget);
            $method = $reflection->getMethod('getData');
            $method->setAccessible(true);
            $data = $method->invoke($widget);

            $this->info("Fulfill chart data structure:");
            $this->info("  Labels count: " . count($data['labels'] ?? []));
            $this->info("  Datasets count: " . count($data['datasets'] ?? []));

            if (!empty($data['datasets'])) {
                foreach ($data['datasets'] as $index => $dataset) {
                    $dataSum = array_sum($dataset['data'] ?? []);
                    $this->info("  Dataset {$index} ({$dataset['label']}): {$dataSum} total");
                }
            }
        } catch (\Exception $e) {
            $this->error("Fulfill chart widget error: " . $e->getMessage());
        }
    }
}
