<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Store;
use App\Services\TelegramNotificationService;
use App\Enums\TiktokShopStatus;

class CheckBankStatusForStoresWithOrders extends Command
{
    protected $signature = 'stores:check-bank-status';
    protected $description = 'Check bank status for live stores with orders and send a summary notification';

    public function handle(TelegramNotificationService $telegramService)
    {
        $storesWithOrders = Store::has('orders')
            ->where('tiktok_shop_status', TiktokShopStatus::Live->value)
            ->with('owner') // eager load owner
            ->get();

        // Nhóm stores theo seller
        $sellerGroups = [];
        foreach ($storesWithOrders as $store) {
            if (empty($store->card) || empty($store->bank_account)) {
                $sellerId = $store->owner->id;
                $sellerGroups[$sellerId]['seller'] = $store->owner->name;
                $sellerGroups[$sellerId]['stores'][] = [
                    'name' => $store->name,
                    'status' => $this->getBankStatus($store),
                ];
            }
        }

        if (!empty($sellerGroups)) {
            $messages = $this->formatSummaryMessage($sellerGroups);
            foreach ($messages as $message) {
                $telegramService->sendBankStatusSummary($message);
                if (count($messages) > 1) sleep(1);
            }
        }

        $this->info("Bank status check completed.");
    }

    private function getBankStatus($store)
    {
        if (empty($store->card) && empty($store->bank_account)) {
            return "No bank account";
        } elseif (!empty($store->card) && empty($store->bank_account)) {
            return "Not updated in system";
        } elseif (empty($store->card) && !empty($store->bank_account)) {
            return "Not added on TikTok";
        }
        return "Unknown";
    }

    private function formatSummaryMessage($sellerGroups)
    {
        $messages = [];
        $currentMessage = "<b>🏦 Bank Account Status Summary</b>\n\n";
        $totalStores = 0;

        foreach ($sellerGroups as $group) {
            $sellerSection = "<b>👤 Seller: {$group['seller']}</b>\n<pre>";
            $sellerSection .= sprintf("%-25s | %s\n", "Store", "Status");
            $sellerSection .= str_repeat("-", 45) . "\n";

            foreach ($group['stores'] as $store) {
                $sellerSection .= sprintf(
                    "%-25s | %s\n",
                    mb_substr($store['name'], 0, 25),
                    $store['status']
                );
                $totalStores++;
            }
            $sellerSection .= "</pre>\n\n";

            // Kiểm tra nếu tin nhắn hiện tại + section mới > 4000 ký tự
            if (strlen($currentMessage . $sellerSection) > 4000) {
                $messages[] = $currentMessage;
                $currentMessage = "<b>🏦 Bank Account Status Summary (Continued)</b>\n\n" . $sellerSection;
            } else {
                $currentMessage .= $sellerSection;
            }
        }

        // Thêm summary vào tin nhắn cuối
        $summary = sprintf("<b>Total stores needing attention: %d</b>\n", $totalStores);
        $summary .= "<b>Action Required:</b> Please review and update bank information for these stores.";

        if (strlen($currentMessage . $summary) > 4000) {
            $messages[] = $currentMessage;
            $messages[] = $summary;
        } else {
            $currentMessage .= $summary;
            $messages[] = $currentMessage;
        }

        return $messages;
    }
}
