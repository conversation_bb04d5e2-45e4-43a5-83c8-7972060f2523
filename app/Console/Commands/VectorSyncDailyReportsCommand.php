<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ZillizVectorService;
use App\Services\OpenAIService;
use App\Services\VectorSync\DailyReportVectorService;
use App\Models\BusinessIntelligenceReport;
use App\Models\Document;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class VectorSyncDailyReportsCommand extends Command
{
    protected $signature = 'vector:sync-daily-reports {--batch=50} {--dry-run} {--from-date=} {--to-date=} {--save-text} {--auto-summary} {--all-reports}';
    protected $description = 'Sync Daily Reports to Zilliz vector database (individual reports sync). Date format: d-m-Y (e.g., 25-12-2024). Use --all-reports to sync all reports without date filter.';

    /**
     * Laravel Command compatibility methods
     */
    public function writeln($message)
    {
        $this->line($message);
    }

    public function createProgressBar($max = 0)
    {
        return $this->output->createProgressBar($max);
    }

    private $zilliz;
    private $openAI;
    private $dailyReportVectorService;

    public function __construct()
    {
        parent::__construct();
        $this->zilliz = new ZillizVectorService();
        $this->openAI = new OpenAIService();
        $this->dailyReportVectorService = new DailyReportVectorService($this->zilliz, $this->openAI);
    }

    public function handle()
    {
        $batchSize = $this->option('batch');
        $isDryRun = $this->option('dry-run');
        $fromDate = $this->option('from-date');
        $toDate = $this->option('to-date');
        $saveText = $this->option('save-text');
        $autoSummary = $this->option('auto-summary');
        $allReports = $this->option('all-reports');

        // Xử lý tùy chọn --all-reports
        if ($allReports) {
            $fromDate = null;
            $toDate = null;
            $this->info("📅 All reports mode enabled - syncing all daily reports without date filter");
        } else {
            // Nếu không có date range, mặc định lấy ngày hôm nay
            if (!$fromDate && !$toDate) {
                $fromDate = now()->format('Y-m-d');
                $toDate = now()->format('Y-m-d');
                $this->info("📅 No date range specified, defaulting to today: {$fromDate}");
            }
        }

        $this->info('🚀 Starting Daily Reports vector sync...');
        $this->info('📊 Đang bắt đầu đồng bộ vector cho Báo cáo Hàng ngày...');

        if ($isDryRun) {
            $this->warn('🧪 DRY RUN MODE - No vectors will be inserted');
        }

        if ($saveText) {
            $this->info('💾 TEXT BACKUP MODE - All generated texts will be saved to S3');
        }

        if ($autoSummary) {
            $this->info('🤖 AUTO SUMMARY MODE - Will generate AI summaries after sync');
        }

        if ($allReports) {
            $this->info("📅 Date range: ALL REPORTS (no date filter)");
        } else {
            $this->info("📅 Date range: {$fromDate} to {$toDate}");
        }

        // Sync Daily Reports using dedicated service
        $this->info('📊 Syncing Daily Reports...');
        $allTexts = $this->dailyReportVectorService->syncDailyReports(
            $batchSize,
            $isDryRun,
            $fromDate,
            $toDate,
            $this,
            $saveText
        );

        // Đảm bảo $allTexts là array để tránh lỗi count()
        if ($allTexts === null) {
            $allTexts = [];
        }

        // Save texts to S3 if option enabled
        if ($saveText && !empty($allTexts)) {
            $s3Url = $this->saveTextsToS3($allTexts);
            if ($s3Url) {
                $this->info("📄 Daily Reports texts saved to S3: {$s3Url}");
            }
        }

        // Auto Summary if option enabled
        if ($autoSummary && !$isDryRun) {
            $this->info('🤖 Starting AI Analysis and Report Generation...');
            if ($allReports) {
                $this->generateAIReport(null, null);
            } else {
                $this->generateAIReport($fromDate, $toDate);
            }
        }

        $this->info('✅ Daily Reports vector sync completed successfully!');
        $this->info("📊 Total reports processed: " . count($allTexts));
    }

    /**
     * Save texts to S3 with timestamp
     */
    private function saveTextsToS3($allTexts)
    {
        try {
            // Đảm bảo $allTexts là array
            if ($allTexts === null) {
                $allTexts = [];
            }

            $timestamp = now()->format('Y-m-d_H-i-s');
            $filename = "daily-reports-texts/daily_reports_{$timestamp}.json";

            $completeData = [
                'sync_timestamp' => $timestamp,
                'total_reports' => count($allTexts),
                'daily_reports' => $allTexts,
                'metadata' => [
                    'generated_by' => 'VectorSyncDailyReportsCommand',
                    'purpose' => 'Daily Reports texts backup before embedding',
                    'format' => 'Individual daily reports data for vector embedding'
                ]
            ];

            $saved = Storage::disk('s3')->put(
                $filename,
                json_encode($completeData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
            );

            if ($saved) {
                $url = Storage::disk('s3')->url($filename);
                $this->info("💾 Daily Reports texts saved to S3: {$filename}");
                $this->info("📊 Total reports saved: " . count($allTexts));
                return $url;
            }

            $this->warn("❌ Failed to save Daily Reports texts to S3");
            return null;

        } catch (\Exception $e) {
            $this->error("❌ Error saving Daily Reports texts to S3: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Tạo báo cáo AI từ vectors đã embedding
     */
    private function generateAIReport($fromDate, $toDate)
    {
        try {
            $this->info('📊 Collecting embedded vectors from Zilliz...');

            // 1. Thu thập dữ liệu từ vector database
            $vectorData = $this->collectDailyReportVectors($fromDate, $toDate);

            if (empty($vectorData)) {
                $this->warn('⚠️ No vector data found for the specified date range');
                return;
            }

            $this->info("📈 Found " . count($vectorData) . " embedded reports");

            // 2. Tạo AI analysis
            $this->info('🤖 Generating AI analysis...');
            $aiSummary = $this->generateAIAnalysis($vectorData);

            if (!$aiSummary) {
                $this->error('❌ Failed to generate AI analysis');
                return;
            }

            // 3. Tạo PDF và upload S3
            $this->info('📄 Generating PDF report...');
            $pdfResult = $this->generateAndUploadPDF($aiSummary, $fromDate, $toDate);

            // 4. Lưu báo cáo vào database
            $this->info('💾 Saving report to database...');
            $reportId = $this->saveReportToDatabase($vectorData, $aiSummary, $pdfResult, $fromDate, $toDate);

            if ($reportId) {
                $this->info("✅ AI Report created successfully!");
                $this->info("📊 Report ID: {$reportId}");
                if ($pdfResult['url']) {
                    $this->info("📄 PDF URL: {$pdfResult['url']}");
                }
            } else {
                $this->error('❌ Failed to save report to database');
            }

        } catch (\Exception $e) {
            $this->error("❌ Error generating AI report: " . $e->getMessage());
        }
    }

    /**
     * Thu thập dữ liệu vectors từ Zilliz theo date range sử dụng queryVectors
     */
    private function collectDailyReportVectors($fromDate, $toDate)
    {
        try {
            $collectionName = 'daily_reports_embeddings';

            // Đầu tiên kiểm tra collection stats
            $this->info("🔍 Checking collection stats...");
            $stats = $this->zilliz->getCollectionStats($collectionName);
            if ($stats) {
                $this->info("📊 Collection stats: " . json_encode($stats));
            } else {
                $this->warn("⚠️ Could not get collection stats");
            }

            // Thử semantic search với query chung để lấy data
            $this->info("🔍 Using semantic search to collect data...");
            $queries = [
                "daily report seller activity",
                "báo cáo hàng ngày seller"
            ];

            $allData = [];
            $seenIds = [];

            foreach ($queries as $query) {
                $this->info("🔍 Searching with query: {$query}");

                $result = $this->zilliz->semanticSearchV2($query, 50, $collectionName);

                if ($result['success'] && !empty($result['data'])) {
                    foreach ($result['data'] as $item) {
                        $reportId = $item['report_id'] ?? null;

                        // Tránh duplicate
                        if ($reportId && !in_array($reportId, $seenIds)) {
                            // Filter by date range if needed
                            if ($this->isInDateRange($item, $fromDate, $toDate)) {
                                $allData[] = $item;
                                $seenIds[] = $reportId;
                            }
                        }
                    }

                    $this->info("📊 Query '{$query}' returned " . count($result['data']) . " records");
                } else {
                    $this->warn("⚠️ Query '{$query}' failed: " . ($result['error'] ?? 'No data'));
                }

                // Giới hạn để tránh quá tải
                if (count($allData) >= 100) {
                    $this->info("📊 Reached 100 records limit");
                    break;
                }
            }

            $this->info("📊 Total unique records collected: " . count($allData));
            return $allData;

        } catch (\Exception $e) {
            $this->error("Error collecting vector data: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Kiểm tra xem report có trong date range không
     */
    private function isInDateRange($reportData, $fromDate, $toDate)
    {
        $reportDate = $reportData['report_date'] ?? null;

        if (!$reportDate) {
            return true; // Nếu không có date thì include
        }

        if ($fromDate && $reportDate < $fromDate) {
            return false;
        }

        if ($toDate && $reportDate > $toDate) {
            return false;
        }

        return true;
    }

    /**
     * Tạo AI analysis từ vector data
     */
    private function generateAIAnalysis($vectorData)
    {
        try {
            // Tạo context từ vector data
            $context = $this->createAnalysisContext($vectorData);

            // Tạo prompt cho ChatGPT
            $prompt = $this->createAnalysisPrompt($context);

            // Gọi ChatGPT
            $messages = [
                ['role' => 'user', 'content' => $prompt]
            ];

            $result = $this->openAI->chatCompletion($messages, 'gpt-4o-mini', 3000);

            if ($result['success']) {
                return $result['content'];
            } else {
                $this->error("ChatGPT API error: " . ($result['error'] ?? 'Unknown error'));
                return null;
            }

        } catch (\Exception $e) {
            $this->error("Error generating AI analysis: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Tạo context từ vector data để phân tích
     */
    private function createAnalysisContext($vectorData)
    {
        $context = "DAILY REPORTS ANALYSIS DATA:\n\n";

        // Thống kê tổng quan
        $totalReports = count($vectorData);
        $sellers = array_unique(array_column($vectorData, 'seller_name'));
        $totalSellers = count($sellers);

        $context .= "TỔNG QUAN:\n";
        $context .= "- Tổng số báo cáo: {$totalReports}\n";
        $context .= "- Tổng số sellers: {$totalSellers}\n";
        $context .= "- Danh sách sellers: " . implode(', ', $sellers) . "\n\n";

        // Chi tiết từng báo cáo
        $context .= "CHI TIẾT BÁO CÁO:\n";
        foreach ($vectorData as $index => $report) {
            $context .= "Báo cáo " . ($index + 1) . ":\n";
            $context .= "- Seller: " . ($report['seller_name'] ?? 'Unknown') . "\n";
            $context .= "- Ngày: " . ($report['report_date'] ?? 'Unknown') . "\n";
            $context .= "- Trạng thái: " . ($report['status'] ?? 'Unknown') . "\n";

            if (!empty($report['report_summary'])) {
                $context .= "- Tóm tắt: " . $report['report_summary'] . "\n";
            }

            if (!empty($report['sales_data'])) {
                $context .= "- Dữ liệu bán hàng: " . $report['sales_data'] . "\n";
            }

            if (!empty($report['activities'])) {
                $context .= "- Hoạt động: " . $report['activities'] . "\n";
            }

            $context .= "\n";
        }

        return $context;
    }

    /**
     * Tạo prompt cho ChatGPT phân tích từ database
     */
    private function createAnalysisPrompt($context)
    {
        $fromDate = $this->option('from-date');
        $toDate = $this->option('to-date');
        $allReports = $this->option('all-reports');

        // Xử lý date range cho prompt
        if ($allReports) {
            $dateRange = "tất cả báo cáo";
        } else {
            // Nếu không có date options, sử dụng ngày hiện tại
            if (!$fromDate && !$toDate) {
                $fromDate = now()->format('Y-m-d');
                $toDate = now()->format('Y-m-d');
            }

            $dateRange = ($fromDate === $toDate)
                ? "ngày {$fromDate}"
                : "từ {$fromDate} đến {$toDate}";
        }

        // Lấy prompt từ database sử dụng key
        $promptDocument = Document::where('key', 'daily-reports-analysis-prompt')
            ->where('status', 'published')
            ->first();

        if (!$promptDocument) {
            $this->warn('⚠️ Không tìm thấy prompt document, sử dụng prompt mặc định');
            return $this->getDefaultPrompt($dateRange, $context);
        }

        $this->info('📋 Sử dụng prompt từ database document');

        // Thay thế placeholders trong prompt
        $prompt = $promptDocument->content;
        $prompt = str_replace('[NGÀY/KHOẢNG THỜI GIAN]', $dateRange, $prompt);

        // Thêm context data vào cuối prompt
        $prompt .= "\n\n## DỮ LIỆU CẦN PHÂN TÍCH:\n\n{$context}";

        return $prompt;
    }

    /**
     * Prompt mặc định nếu không tìm thấy trong database
     */
    private function getDefaultPrompt($dateRange, $context)
    {
        return "Bạn là chuyên gia phân tích kinh doanh cấp cao trong lĩnh vực Print on Demand (POD). Công ty có hơn 50 sellers đang bán hàng qua nền tảng TikTok Shop Mỹ, fulfill qua xưởng Flashship.

Nhiệm vụ của bạn là dựa trên báo cáo dữ liệu hàng ngày sau đây, hãy viết một bản **EXECUTIVE SUMMARY** ngắn gọn, súc tích, đầy đủ thông tin, dễ hiểu dành cho Giám đốc công ty, người không có thời gian đọc quá nhiều số liệu chi tiết.

BÁO CÁO CẦN ĐÁP ỨNG CÁC YÊU CẦU SAU:
1. **TỔNG QUAN HIỆU SUẤT HỆ THỐNG:** (2-3 dòng tóm tắt ngắn gọn về tổng thể tình hình bán hàng của toàn bộ hệ thống trong khoảng thời gian {$dateRange}, bao gồm xu hướng chính, số đơn, doanh thu nổi bật, tăng/giảm so với kỳ trước)
2. **SELLERS XUẤT SẮC:** (Chỉ liệt kê nếu có những sellers đạt kết quả vượt trội, nêu rõ số đơn, doanh thu, và yếu tố thành công đặc biệt như sản phẩm hot, video viral, tỉ lệ chuyển đổi cao,...)
3. **SELLERS CẦN CHÚ Ý:** (Chỉ liệt kê nếu có sellers có hiệu suất giảm mạnh, số đơn thấp hơn trung bình, hoặc có rủi ro cần can thiệp. Nêu rõ lý do nếu có thể)
4. **INSIGHTS QUAN TRỌNG:** (Phân tích những điểm đặc biệt, xu hướng, cơ hội hoặc rủi ro tiềm ẩn, thay đổi về sản phẩm, thị trường, quảng cáo,...)
5. **HÀNH ĐỘNG CẦN THIẾT:** (Đưa ra các đề xuất hành động cụ thể và ưu tiên cao để cải thiện hiệu suất bán hàng hoặc tối ưu vận hành, ví dụ: Tập trung vào seller A, push sản phẩm B, cải tiến quy trình fulfill, tăng ngân sách ads,...)

LƯU Ý:
- Chỉ nêu tên seller khi có sự khác biệt rõ rệt, tránh liệt kê dài dòng.
- Ưu tiên **insights mang tính hành động** (actionable) và giúp **sếp ra quyết định nhanh chóng**.
- Viết bằng tiếng Việt, ngắn gọn, súc tích, đúng trọng tâm.

Dữ liệu đầu vào: {$context}
";
    }

    /**
     * Tạo PDF và upload lên S3 (text thuần)
     */
    private function generateAndUploadPDF($content, $fromDate, $toDate)
    {
        try {
            $dateRange = $fromDate && $toDate ? "{$fromDate} đến {$toDate}" : "Tùy chỉnh";
            $date = Carbon::now()->format('d/m/Y H:i');

            // Tạo HTML đơn giản với text thuần
            $htmlContent = "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Báo cáo AI - Daily Reports Analysis</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }
        .content {
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class='header'>
        <h2>Daily Reports Analysis</h2>
        <p>Khoảng thời gian: {$dateRange}</p>
        <p>Ngày tạo: {$date}</p>
    </div>
    <div class='content'>{$content}</div>
</body>
</html>";

            // Generate PDF
            $pdf = Pdf::loadHTML($htmlContent)
                ->setPaper('A4', 'portrait')
                ->setOptions([
                    'defaultFont' => 'DejaVu Sans',
                ]);

            $pdfBinary = $pdf->output();

            // Upload to S3
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $filename = "daily-reports-ai-analysis/ai_report_{$timestamp}.pdf";

            $saved = Storage::disk('s3')->put($filename, $pdfBinary, 'public');

            if ($saved) {
                $url = Storage::disk('s3')->url($filename);
                return [
                    'url' => $url,
                    'filename' => $filename
                ];
            }

            return ['url' => '', 'filename' => ''];

        } catch (\Exception $e) {
            $this->error("Error generating PDF: " . $e->getMessage());
            return ['url' => '', 'filename' => ''];
        }
    }



    /**
     * Lưu báo cáo vào database
     */
    private function saveReportToDatabase($vectorData, $aiSummary, $pdfResult, $fromDate, $toDate)
    {
        try {
            $allReports = $this->option('all-reports');

            if ($allReports) {
                $dateRange = "Tất cả báo cáo";
                $title = "AI Analysis - Daily Reports (All Reports)";
                $period = 'all';
            } else {
                $dateRange = $fromDate && $toDate ? "{$fromDate} đến {$toDate}" : "Tùy chỉnh";
                $title = "AI Analysis - Daily Reports ({$dateRange})";
                $period = $fromDate && $toDate ? 'custom' : 'daily';
            }

            $report = BusinessIntelligenceReport::create([
                'title' => $title,
                'period' => $period,
                'from_date' => $fromDate ?: now()->format('Y-m-d'),
                'to_date' => $toDate ?: now()->format('Y-m-d'),
                'analysis_data' => [
                    'type' => 'daily_reports_ai_analysis',
                    'total_vectors' => count($vectorData),
                    'generated_by' => 'VectorSyncDailyReportsCommand',
                    'ai_model' => 'gpt-4o-mini',
                    'vector_collection' => 'daily_reports_embeddings'
                ],
                'ai_summary' => $aiSummary,
                'raw_prompt' => $this->createAnalysisPrompt($this->createAnalysisContext($vectorData)),
                'raw_embedding_data' => json_encode($vectorData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
                'pdf_url' => $pdfResult['url'] ?? '',
                'pdf_filename' => $pdfResult['filename'] ?? '',
                'total_reports' => count($vectorData),
                'total_products' => 0,
                'total_orders' => 0,
                'total_suppliers' => 0,
                'total_stores' => 0,
                'total_revenue' => 0,
                'avg_order_value' => 0,
                'status' => 'completed',
                'error_message' => null,
                'created_by' => 1 // System user
            ]);

            return $report->id;

        } catch (\Exception $e) {
            $this->error("Error saving report to database: " . $e->getMessage());
            return null;
        }
    }
}
