<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Store;
use App\Models\FlashSale;
use App\Jobs\TiktokSyncStoreFlashSales;
use App\Jobs\TiktokProductFlashSale;
use App\Enums\TiktokShopStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SyncStoresFlashSales extends Command
{
    protected $signature = 'flash-sales:sync {type=all} {--chunk=20}';
    protected $description = 'Sync flash sales based on type (all, update, product, duplicate)';

    public function handle()
    {
        $type = $this->argument('type');
        $this->info("Starting flash sales sync for type: {$type}");

        try {
            switch ($type) {
                case 'all':
                    $this->syncAllStores();
                    break;
                case 'update':
                    $this->syncOngoingFlashSales();
                    break;
                case 'product':
                    $this->syncProductUpdates();
                    break;
                case 'duplicate':
                    $this->syncDuplicates();
                    break;
            }
        } catch (\Exception $e) {
            Log::error('Failed to run flash sales sync', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            $this->error("Error running sync: {$e->getMessage()}");
        }
    }

    protected function syncAllStores()
    {
        $lastUpdate = Carbon::now()->subHour();
        
        $stores = Store::query()
            ->where('auto_renew_flash_sale', true)
            ->where('app_partner_id', '!=', '')
            ->where('status', 'Active')
            ->where('tiktok_shop_status', TiktokShopStatus::Live->value)
            ->where(function($query) use ($lastUpdate) {
                $query->where('time_flashdeal', '<', $lastUpdate)
                      ->orWhereNull('time_flashdeal');
            })
            ->orderBy('time_flashdeal', 'asc', 'nulls first')
            ->limit(20)
            ->lockForUpdate()
            ->get();

        if ($stores->isEmpty()) {
            $this->info('No stores need syncing at this time.');
            return;
        }

        foreach ($stores as $store) {
            try {
                $store->update(['time_flashdeal' => Carbon::now()->subMinutes(55)]);
                TiktokSyncStoreFlashSales::dispatchSync($store);
                $store->update(['time_flashdeal' => now()]);
                $this->info("Successfully synced store: {$store->name}");
            } catch (\Exception $e) {
                $store->update(['time_flashdeal' => $lastUpdate]);
      
                $this->error("Error syncing store {$store->name}: {$e->getMessage()}");
            }
        }
    }

    protected function syncOngoingFlashSales()
    {
        $lastUpdate = Carbon::now()->subMinutes(30);
        
        $flashSales = FlashSale::where('deal_status', 'ONGOING')
            ->where('updated_at', '<', $lastUpdate)
            ->orderBy('updated_at', 'asc')
            ->limit(100)
            ->get();

        if ($flashSales->isEmpty()) {
            $this->info('No ongoing flash sales need updating.');
            return;
        }

        foreach ($flashSales as $flashSale) {
            try {
                $store = $flashSale->store;
                if (!$store || $store->status !== 'Active') {
                    continue;
                }

                TiktokProductFlashSale::dispatchSync($flashSale, $store)
                    ;
                    
                $this->info("Dispatched update job for flash sale: {$flashSale->deal_name}");
            } catch (\Exception $e) {
                Log::error('Failed to dispatch flash sale update job', [
                    'flash_sale_id' => $flashSale->id,
                    'error' => $e->getMessage()
                ]);
                $this->error("Error dispatching job for flash sale {$flashSale->deal_name}: {$e->getMessage()}");
            }
        }
    }

    protected function syncProductUpdates()
    {
        $lastUpdate = Carbon::now()->subMinutes(10);
        
        $flashSales = FlashSale::where('deal_status', 'PRODUCT UPDATE')
            ->where('updated_at', '<', $lastUpdate)
            ->orderBy('updated_at', 'asc')
            ->limit(100)
            ->get();

        if ($flashSales->isEmpty()) {
            $this->info('No flash sales need product updates.');
            return;
        }

        foreach ($flashSales as $flashSale) {
            try {
                $store = $flashSale->store;
                if (!$store || $store->status !== 'Active') {
                    $flashSale->update(['deal_status' => 'ONGOING']);
                    continue;
                }

                TiktokProductFlashSale::dispatchSync($flashSale, $store)
                    ;
                    
                $this->info("Dispatched product update job for flash sale: {$flashSale->deal_name}");
            } catch (\Exception $e) {
                Log::error('Failed to dispatch product update job', [
                    'flash_sale_id' => $flashSale->id,
                    'error' => $e->getMessage()
                ]);
                $this->error("Error dispatching job for flash sale {$flashSale->deal_name}: {$e->getMessage()}");
            }
        }
    }

    protected function syncDuplicates()
    {
        $lastUpdate = Carbon::now()->subMinutes(20);
        
        $flashSales = FlashSale::where('deal_status', 'DUPLICATED')
            ->where('updated_at', '<', $lastUpdate)
            ->orderBy('updated_at', 'asc')
            ->limit(100)
            ->get();

        if ($flashSales->isEmpty()) {
            $this->info('No duplicated flash sales need processing.');
            return;
        }

        foreach ($flashSales as $flashSale) {
            try {
                $store = $flashSale->store;
                if (!$store || $store->status !== 'Active') {
                    $flashSale->update(['deal_status' => 'ONGOING']);
                    continue;
                }

                TiktokProductFlashSale::dispatchSync($flashSale, $store)
                    ;
                    
                $this->info("Dispatched duplicate processing job for flash sale: {$flashSale->deal_name}");
            } catch (\Exception $e) {
                Log::error('Failed to dispatch duplicate processing job', [
                    'flash_sale_id' => $flashSale->id,
                    'error' => $e->getMessage()
                ]);
                $this->error("Error dispatching job for flash sale {$flashSale->deal_name}: {$e->getMessage()}");
            }
        }
    }
}