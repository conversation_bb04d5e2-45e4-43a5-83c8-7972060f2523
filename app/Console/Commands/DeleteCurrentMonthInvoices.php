<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use Carbon\Carbon;
use Illuminate\Console\Command;

class DeleteCurrentMonthInvoices extends Command
{
    protected $signature = 'invoices:delete-current-month {--force : Force deletion without confirmation}';
    protected $description = 'Delete all invoices for the current month';

    public function handle()
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();
        
        $count = Invoice::whereBetween('created_at', [$startOfMonth, $endOfMonth])->count();
        
        if ($count === 0) {
            $this->info('No invoices found for the current month.');
            return 0;
        }
        
        $this->info("Found {$count} invoices from " . $startOfMonth->format('d/m/Y') . " to " . $endOfMonth->format('d/m/Y'));
        
        if (!$this->option('force') && !$this->confirm('Are you sure you want to delete these invoices? This action cannot be undone.')) {
            $this->info('Operation cancelled.');
            return 0;
        }
        
        Invoice::whereBetween('created_at', [$startOfMonth, $endOfMonth])->delete();
        
        $this->info("Successfully deleted {$count} invoices for the current month.");
        return 0;
    }
}