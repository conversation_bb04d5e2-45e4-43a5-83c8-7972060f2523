<?php

namespace App\Console\Commands;

use App\Models\Store;
use App\Models\TiktokHoldStatement;
use App\Services\Tiktok\TiktokShopService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ScanTiktokHoldAmountCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tiktok:scan-hold-amount
        {--store_id= : ID của store cụ thể (nếu không có sẽ quét tất cả)}
        {--min-orders=10 : Số lượng đơn hàng tối thiểu để quét}
        {--export : Xuất kết quả ra file CSV}
        {--dry-run : Chỉ xem thông tin, không cập nhật database}
        {--days=60 : Số ngày lùi về quá khứ để lấy dữ liệu (mặc định: 60 ngày ~ 2 tháng)}
        {--max-pages=20 : <PERSON><PERSON> trang tối đa để lấy dữ liệu}
        {--page-size=20 : Số lượng statement trên mỗi trang}
        {--api-version=202309 : Phiên bản API TikTok Shop}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Quét tất cả các store TikTok để lấy thông tin số tiền đang hold';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Bắt đầu quét thông tin số tiền đang hold tại các shop TikTok...');

        // Lấy tham số từ command
        $storeId = $this->option('store_id');
        $minOrders = $this->option('min-orders');
        $shouldExport = $this->option('export');
        $dryRun = $this->option('dry-run');

        // Lấy các tham số mới
        $days = (int)$this->option('days');
        $maxPages = (int)$this->option('max-pages');
        $pageSize = (int)$this->option('page-size');
        $apiVersion = $this->option('api-version');

        // Tính thời gian bắt đầu và kết thúc
        $startDate = now()->subDays($days)->format('Y-m-d');
        $endDate = now()->format('Y-m-d');

        // Hiển thị thông tin về các tham số
        $this->info("Tham số quét:");
        $this->line("- Khoảng thời gian: Từ {$startDate} đến {$endDate} ({$days} ngày)");
        $this->line("- Số trang tối đa: {$maxPages} trang");
        $this->line("- Số lượng statement trên mỗi trang: {$pageSize}");
        $this->line("- Phiên bản API: {$apiVersion}");
        $this->newLine();

        if ($dryRun) {
            $this->info("Chế độ dry-run: Sẽ không cập nhật database.");
        }

        // Khởi tạo mảng để lưu kết quả
        $results = [];
        $totalHoldAmount = 0;
        $totalAdjustmentAmount = 0;
        $totalNetAmount = 0;
        $successCount = 0;
        $errorCount = 0;

        // Lấy danh sách store
        $query = Store::query();

        // Nếu có store_id, chỉ quét store đó
        if ($storeId) {
            $query->where('id', $storeId);
        }

        // Lấy các store có ít nhất $minOrders đơn hàng
        if (!$storeId) {
            $query->whereRaw("(SELECT COUNT(*) FROM orders WHERE orders.store_id = stores.id) >= ?", [$minOrders]);
        }

        // Lấy các store có thông tin Partner App
        $query->whereHas('partnerApp');

        // Đếm tổng số store sẽ quét
        $totalStores = $query->count();
        $this->info("Tìm thấy {$totalStores} store để quét.");

        // Tạo progress bar
        $bar = $this->output->createProgressBar($totalStores);
        $bar->start();

        // Quét từng store
        $query->chunk(10, function ($stores) use (&$results, &$totalHoldAmount, &$totalAdjustmentAmount, &$totalNetAmount, &$successCount, &$errorCount, $bar, $dryRun, $days, $maxPages, $pageSize, $apiVersion) {
            foreach ($stores as $store) {
                try {
                    // Khởi tạo TiktokShopService
                    $tiktokService = new TiktokShopService($store);

                    // Chuẩn bị các tùy chọn cho getHoldAmount
                    $options = [
                        'days_to_look_back' => $days,
                        'max_pages' => $maxPages,
                        'page_size' => $pageSize,
                        'api_version' => $apiVersion,
                    ];

                    // Gọi phương thức getHoldAmount với các tùy chọn
                    $result = $tiktokService->getHoldAmount($options);

                    // Nếu thành công, lưu kết quả
                    if ($result['success']) {
                        $holdAmount = $result['total_hold_amount'];
                        $currency = $result['currency'];
                        $holdStatements = $result['hold_statements'] ?? [];

                        // Lấy thông tin về số tiền điều chỉnh (âm) và số tiền ròng
                        $adjustmentAmount = $result['total_adjustments'] ?? 0;
                        $netAmount = $result['net_amount'] ?? 0;
                        $adjustmentStatements = $result['adjustment_statements'] ?? [];

                        $results[] = [
                            'store_id' => $store->id,
                            'store_name' => $store->name,
                            'tiktok_shop_name' => $store->tiktok_shop_name,
                            'hold_amount' => $holdAmount,
                            'adjustment_amount' => $adjustmentAmount,
                            'net_amount' => $netAmount,
                            'currency' => $currency,
                            'statements_count' => count($holdStatements),
                            'adjustment_statements_count' => count($adjustmentStatements),
                            'success' => true,
                            'error' => null
                        ];

                        // Cập nhật tổng số tiền đang hold, điều chỉnh và ròng
                        $totalHoldAmount += $holdAmount;
                        $totalAdjustmentAmount += $adjustmentAmount;
                        $totalNetAmount += $netAmount;
                        $successCount++;

                        // Cập nhật thông tin vào store nếu không phải chế độ dry-run
                        if (!$dryRun) {
                            // Bắt đầu transaction để đảm bảo tính nhất quán dữ liệu
                            DB::beginTransaction();

                            try {
                                // Cập nhật tổng số tiền đang hold và số tiền ròng vào store
                                // $store->tiktok_payout_on_hold = $netAmount; // Lưu số tiền ròng thay vì chỉ số tiền dương
                                // $store->save();

                                // Xóa các statement cũ của store này (nếu cần)
                                // TiktokHoldStatement::where('store_id', $store->id)->delete();

                                // Lưu chi tiết từng statement vào bảng tiktok_hold_statements
                                // Xử lý cả statements dương và âm
                                $allStatementsList = array_merge($holdStatements, $adjustmentStatements);

                                // Xóa tất cả statements cũ của store này (nếu cần)
                                TiktokHoldStatement::where('store_id', $store->id)->delete();

                                // Lưu tất cả statements mới
                                foreach ($allStatementsList as $statement) {
                                    // Lưu tất cả các statement, không lọc theo payment_status
                                    if (isset($statement['payment_status'])) {

                                        // Lấy settlement_amount từ statement
                                        $settlementAmount = isset($statement['settlement_amount']) ?
                                            floatval($statement['settlement_amount']) : 0;

                                        // Chuẩn bị dữ liệu chi tiết hơn cho raw_data
                                        $rawData = $statement;
                                        // $rawData['_meta'] = [
                                        //     'is_positive' => $settlementAmount > 0,
                                        //     'is_negative' => $settlementAmount < 0,
                                        //     'absolute_amount' => abs($settlementAmount),
                                        //     'net_amount' => $settlementAmount,
                                        //     'updated_at' => now()->toIso8601String(),
                                        //     'payment_group' => $statement['payment_id'] ?? 'unknown',
                                        //     'hold_amount' => $holdAmount,
                                        //     'adjustment_amount' => $adjustmentAmount,
                                        //     'total_net_amount' => $netAmount,
                                        // ];

                                        // Tạo statement mới
                                        TiktokHoldStatement::create([
                                            'store_id' => $store->id,
                                            'statement_id' => $statement['id'],
                                            'statement_time' => isset($statement['statement_time']) ?
                                                date('Y-m-d H:i:s', $statement['statement_time']) : null,
                                            'settlement_amount' => $settlementAmount,
                                            'currency' => $statement['currency'] ?? $currency,
                                            'payment_status' => $statement['payment_status'],
                                            'payment_id' => $statement['payment_id'] ?? null,
                                            'raw_data' => $rawData,
                                        ]);
                                    }
                                }

                                // Commit transaction
                                DB::commit();

                                // Không cần log thông tin cập nhật
                            } catch (\Exception $e) {
                                // Rollback transaction nếu có lỗi
                                DB::rollBack();

                                // Log lỗi nghiêm trọng
                                Log::error("Lỗi khi lưu hold statements cho store {$store->id}: " . $e->getMessage());

                                // Throw exception để xử lý ở catch bên ngoài
                                throw $e;
                            }
                        }

                        // Lấy thông tin thêm từ kết quả API
                        $scanBatchId = $result['scan_batch_id'] ?? 'unknown';
                        $apiVersion = $result['api_version'] ?? 'unknown';
                        $pagesProcessed = $result['pages_processed'] ?? 0;
                        $scanDuration = $result['scan_duration_seconds'] ?? 0;
                        $statementsCount = $result['statements_count'] ?? ['total' => 0, 'positive' => 0, 'negative' => 0];
                        $allStatements = $result['all_statements'] ?? [];

                        // Phân tích payment_status
                        $paymentStatusCounts = [];
                        foreach ($allStatements as $statement) {
                            $status = $statement['payment_status'] ?? 'UNKNOWN';
                            if (!isset($paymentStatusCounts[$status])) {
                                $paymentStatusCounts[$status] = 0;
                            }
                            $paymentStatusCounts[$status]++;
                        }

                        // Hiển thị thông tin store ngay khi quét xong
                        $bar->clear();
                        $this->info(
                            "Store ID: {$store->id} | " .
                            "Tên: " . str_pad($store->name, 20) . " | " .
                            "TikTok Shop: " . str_pad($store->tiktok_shop_name ?? 'N/A', 20) . " | " .
                            "Số tiền hold: " . str_pad("{$holdAmount} {$currency}", 15) . " | " .
                            "Điều chỉnh: " . str_pad("{$adjustmentAmount} {$currency}", 15) . " | " .
                            "Ròng (đã lưu): " . str_pad("{$netAmount} {$currency}", 15) . " | " .
                            "Statements: " . count($holdStatements) . " dương, " . count($adjustmentStatements) . " âm"
                        );

                        // Hiển thị thông tin chi tiết về quét
                        $this->line("  <fg=blue>Chi tiết quét:</> API v{$apiVersion}, {$pagesProcessed} trang, {$scanDuration}s, Batch ID: {$scanBatchId}");

                        // Hiển thị thông tin về payment_status nếu có statements
                        if (!empty($paymentStatusCounts)) {
                            $statusInfo = [];
                            foreach ($paymentStatusCounts as $status => $count) {
                                $statusInfo[] = "{$status}: {$count}";
                            }
                            $this->line("  <fg=green>Payment Status:</> " . implode(', ', $statusInfo));
                        }

                        // Hiển thị thông tin về statement đầu tiên nếu có
                        if (!empty($allStatements)) {
                            $firstStatement = $allStatements[0];
                            $statementId = $firstStatement['id'] ?? 'N/A';
                            $statementCurrency = isset($firstStatement['currency']) ? $firstStatement['currency'] : $currency;
                            $statementAmount = isset($firstStatement['settlement_amount']) ?
                                "{$firstStatement['settlement_amount']} {$statementCurrency}" : 'N/A';
                            $statementStatus = $firstStatement['payment_status'] ?? 'N/A';
                            $this->line("  <fg=yellow>Sample Statement:</> ID: {$statementId}, Amount: {$statementAmount}, Status: {$statementStatus}");
                        }

                        // Hiển thị thông tin về việc lưu số tiền ròng
                        if ($netAmount != $holdAmount) {
                            $this->line("  <fg=yellow>Lưu ý:</> Số tiền ròng ({$netAmount} {$currency}) đã được lưu vào database thay vì chỉ số tiền dương ({$holdAmount} {$currency})");
                        }

                        // Hiển thị chi tiết statements
                        if (!empty($allStatements)) {
                            $this->line("  <fg=green>Chi tiết statements:</>");

                            // Tạo bảng hiển thị
                            $headers = ['ID', 'Thời gian', 'Số tiền', 'Trạng thái'];
                            $rows = [];

                            // Không lọc statements theo payment_status
                            // Giới hạn số lượng statements hiển thị
                            $statementsToShow = array_slice($allStatements, 0, 10);

                            foreach ($statementsToShow as $statement) {
                                $statementId = $statement['id'] ?? 'N/A';
                                $statementTime = isset($statement['statement_time']) ?
                                    date('Y-m-d', $statement['statement_time']) : 'N/A';
                                $statementCurrency = isset($statement['currency']) ? $statement['currency'] : $currency;
                                $statementAmount = isset($statement['settlement_amount']) ?
                                    "{$statement['settlement_amount']} {$statementCurrency}" : 'N/A';
                                $statementStatus = $statement['payment_status'] ?? 'N/A';

                                $rows[] = [$statementId, $statementTime, $statementAmount, $statementStatus];
                            }

                            // Hiển thị bảng
                            $this->table($headers, $rows);
                        }



                        $bar->display();
                    } else {
                        // Nếu có lỗi, lưu thông tin lỗi
                        $results[] = [
                            'store_id' => $store->id,
                            'store_name' => $store->name,
                            'tiktok_shop_name' => $store->tiktok_shop_name,
                            'hold_amount' => 0,
                            'adjustment_amount' => 0,
                            'net_amount' => 0,
                            'currency' => 'USD',
                            'statements_count' => 0,
                            'adjustment_statements_count' => 0,
                            'success' => false,
                            'error' => $result['error'] ?? 'Unknown error',
                            'error_type' => $result['error_type'] ?? 'UNKNOWN_ERROR'
                        ];

                        $errorCount++;

                        // Hiển thị thông tin lỗi chi tiết ngay khi quét
                        $bar->clear();

                        // Sử dụng command_message nếu có
                        $errorMessage = $result['command_message'] ?? $result['error'] ?? 'Unknown error';
                        $errorType = $result['error_type'] ?? 'UNKNOWN_ERROR';

                        $this->error(
                            "Store ID: {$store->id} | " .
                            "Tên: " . str_pad($store->name, 20) . " | " .
                            "Lỗi: " . $errorMessage
                        );

                        // Hiển thị thêm thông tin về loại lỗi nếu có
                        if ($errorType) {
                            $this->line("  <fg=yellow>Loại lỗi:</> {$errorType}");
                        }

                        $bar->display();
                    }
                } catch (\Exception $e) {
                    // Nếu có exception, lưu thông tin lỗi
                    $errorMessage = $e->getMessage();
                    $errorType = 'EXCEPTION';
                    $commandMessage = 'Lỗi không xác định: ' . $e->getMessage();

                    // Kiểm tra xem có phải là TiktokApiException không
                    if ($e instanceof \App\Exceptions\TiktokApiException) {
                        $errorType = $e->errorType ?? 'API_ERROR';
                        $commandMessage = $e->getCommandMessage();
                    }

                    $results[] = [
                        'store_id' => $store->id,
                        'store_name' => $store->name,
                        'tiktok_shop_name' => $store->tiktok_shop_name,
                        'hold_amount' => 0,
                        'adjustment_amount' => 0,
                        'net_amount' => 0,
                        'currency' => 'USD',
                        'statements_count' => 0,
                        'adjustment_statements_count' => 0,
                        'success' => false,
                        'error' => $errorMessage,
                        'error_type' => $errorType,
                        'command_message' => $commandMessage
                    ];

                    $errorCount++;
                    // Chỉ log lỗi nghiêm trọng
                    if ($errorType === 'SYSTEM_ERROR' || $errorType === 'EXCEPTION') {
                        Log::error("Lỗi khi quét store {$store->id}: " . $e->getMessage(), [
                            'error_type' => $errorType
                        ]);
                    }

                    // Hiển thị thông tin lỗi chi tiết ngay khi quét
                    $bar->clear();
                    $this->error(
                        "Store ID: {$store->id} | " .
                        "Tên: " . str_pad($store->name, 20) . " | " .
                        "Lỗi: " . $commandMessage
                    );

                    // Hiển thị thêm thông tin về loại lỗi
                    $this->line("  <fg=yellow>Loại lỗi:</> {$errorType}");

                    $bar->display();
                }

                // Tạm dừng giữa các request để tránh quá tải
                sleep(1);

                // Cập nhật progress bar
                $bar->advance();
            }
        });

        // Kết thúc progress bar
        $bar->finish();
        $this->newLine(2);

        // Hiển thị kết quả
        $this->newLine();
        $this->info("=== KẾT QUẢ QUÉT ===");
        $this->info("✅ Quét hoàn tất!");
        $this->info("📊 Tổng số store đã quét: " . count($results));
        $this->info("✓ Số store thành công: {$successCount}");
        $this->info("✗ Số store lỗi: {$errorCount}");
        $this->info("💰 Tổng số tiền dương (hold): {$totalHoldAmount} USD");
        $this->info("📉 Tổng số tiền âm (điều chỉnh): {$totalAdjustmentAmount} USD");
        $this->info("💵 Tổng số tiền ròng (đã lưu vào database): {$totalNetAmount} USD");

        if (!$dryRun) {
            // Đếm số lượng statement đã lưu và cập nhật hôm nay
            $today = now()->format('Y-m-d');
            $statementsCount = TiktokHoldStatement::whereIn('store_id', array_column($results, 'store_id'))->count();
            $statementsCreatedToday = TiktokHoldStatement::whereIn('store_id', array_column($results, 'store_id'))
                ->whereDate('created_at', $today)
                ->count();
            $statementsUpdatedToday = TiktokHoldStatement::whereIn('store_id', array_column($results, 'store_id'))
                ->whereDate('updated_at', $today)
                ->whereDate('created_at', '!=', $today)
                ->count();

            $this->info("💾 Đã cập nhật thông tin vào database cho {$successCount} store.");
            $this->info("📊 Tổng số statement: {$statementsCount}");
            $this->info("📊 Số statement tạo mới hôm nay: {$statementsCreatedToday}");
            $this->info("📊 Số statement cập nhật hôm nay: {$statementsUpdatedToday}");
        } else {
            $this->info("🔍 Chế độ dry-run: Không cập nhật database.");
        }
        $this->newLine();

        // Hiển thị danh sách store có số tiền đang hold > 0
        $storesWithHold = array_filter($results, function ($item) {
            return $item['hold_amount'] > 0;
        });

        if (count($storesWithHold) > 0) {
            $this->info("💵 DANH SÁCH STORE CÓ SỐ TIỀN DƯƠNG > 0:");
            $this->table(
                ['Store ID', 'Store Name', 'TikTok Shop Name', 'Số tiền dương', 'Số tiền âm', 'Số tiền ròng (đã lưu)', 'Currency', 'Statements'],
                array_map(function ($item) {
                    return [
                        $item['store_id'],
                        $item['store_name'],
                        $item['tiktok_shop_name'] ?? 'N/A',
                        $item['hold_amount'],
                        $item['adjustment_amount'] ?? 0,
                        $item['net_amount'] ?? $item['hold_amount'],
                        $item['currency'],
                        ($item['statements_count'] ?? 0) . ' dương, ' . ($item['adjustment_statements_count'] ?? 0) . ' âm'
                    ];
                }, $storesWithHold)
            );
        } else {
            $this->info("ℹ️ Không có store nào có số tiền đang hold > 0.");
        }

        // Hiển thị danh sách store có lỗi
        $storesWithError = array_filter($results, function ($item) {
            return $item['success'] === false;
        });

        if (count($storesWithError) > 0) {
            $this->info("❌ DANH SÁCH STORE CÓ LỖI:");
            $this->table(
                ['Store ID', 'Store Name', 'TikTok Shop Name', 'Loại lỗi', 'Thông báo lỗi'],
                array_map(function ($item) {
                    return [
                        $item['store_id'],
                        $item['store_name'],
                        $item['tiktok_shop_name'] ?? 'N/A',
                        $item['error_type'] ?? 'UNKNOWN_ERROR',
                        $item['command_message'] ?? $item['error'] ?? 'Unknown error'
                    ];
                }, $storesWithError)
            );
        }

        // Xuất kết quả ra file CSV nếu có tham số --export
        if ($shouldExport) {
            $this->exportToCsv($results);
        }

        return Command::SUCCESS;
    }

    /**
     * Xuất kết quả ra file CSV
     *
     * @param array $results
     * @return void
     */
    protected function exportToCsv(array $results)
    {
        $filename = 'tiktok_hold_amount_' . date('Y-m-d_H-i-s') . '.csv';
        $path = storage_path('app/public/' . $filename);

        // Tạo file CSV
        $file = fopen($path, 'w');

        // Thêm header
        fputcsv($file, [
            'Store ID',
            'Store Name',
            'TikTok Shop Name',
            'Hold Amount',
            'Adjustment Amount',
            'Net Amount',
            'Currency',
            'Statements Count',
            'Adjustment Statements Count',
            'Success',
            'Error Type',
            'Error Message',
            'Command Message'
        ]);

        // Thêm dữ liệu
        foreach ($results as $result) {
            fputcsv($file, [
                $result['store_id'],
                $result['store_name'],
                $result['tiktok_shop_name'] ?? 'N/A',
                $result['hold_amount'],
                $result['adjustment_amount'] ?? 0,
                $result['net_amount'] ?? $result['hold_amount'],
                $result['currency'],
                $result['statements_count'],
                $result['adjustment_statements_count'] ?? 0,
                $result['success'] ? 'Yes' : 'No',
                $result['error_type'] ?? '',
                $result['error'] ?? '',
                $result['command_message'] ?? ''
            ]);
        }

        // Đóng file
        fclose($file);

        // Tạo URL để tải file
        $url = url('storage/' . $filename);

        $this->info("\nKết quả đã được xuất ra file CSV:");
        $this->info($url);
    }
}
