<?php

namespace App\Console\Commands;

use App\Jobs\ProcessTiktokSettlement;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncAllStoresTiktokSettlements extends Command
{
    protected $signature = 'orders:sync-settlements {--limit=500} {--store_id=} {--debug}';
    protected $description = 'Sync settlements for completed orders';

    public function handle()
    {
        $limit = $this->option('limit');
        $storeId = $this->option('store_id');
        $debug = $this->option('debug');
        $lockKey = 'sync_settlements_running';

        if ($debug) {
            $this->info("Running in debug mode");
        }

        // Kiểm tra lock để tránh chạy đồng thời
        if (!Cache::add($lockKey, true, 300)) {
            $this->info('Another sync process is running');
            return;
        }

        try {
            // Lấy danh sách đơn hàng cần xử lý
            $orders = $this->getOrdersToProcess($limit, $storeId);

            if ($orders->isEmpty()) {
                $this->info("No orders need settlement sync.");
                return;
            }

            $this->info("Found {$orders->count()} orders to process.");

            if ($debug) {
                $this->debugOrders($orders);
                return;
            }

            // Tạo batch job để xử lý
            $this->processBatch($orders);

        } catch (\Exception $e) {
            Log::error("Error in settlement sync: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            $this->error("Error in settlement sync: " . $e->getMessage());
        } finally {
            Cache::forget($lockKey);
        }
    }

    protected function getOrdersToProcess($limit, $storeId)
    {
        $query = Order::with(['store.partnerApp'])
            ->whereHas('store', function ($query) use ($storeId) {
                $query->where('app_partner_id', '!=', '')
                      ->whereNotNull('app_partner_id');
                if ($storeId) {
                    $query->where('id', $storeId);
                }
            })
            // ->where('created_at', '>=', now()->subMonths(2))
            ->where('updated_at', '<', now()->subHours(8))
            ->where('settlement_amount', 0)
            // ->where(function($query) {
            //     $query->where('store_order_status', 'Completed')
            //     ->orWhere('store_order_status', 'Delivered')
            //     ->orWhere('store_order_status', 'Processing')
            //     ->orWhere('store_order_status', 'Awaiting Collection');
            // })
            ->orderBy('created_at', 'asc');
            

        return $query->get();
    }

    protected function debugOrders($orders)
    {
        foreach ($orders as $order) {
            $this->info("Order Details:");
            $this->table(
                ['Order Number', 'Store ID', 'Status', 'Created At'],
                [[
                    $order->order_number,
                    $order->store_id,
                    $order->store_order_status,
                    $order->created_at
                ]]
            );
        }
    }

    protected function processBatch($orders)
    {
        $batch = Bus::batch([])
            ->name('Process Settlements ' . now()->format('Y-m-d H:i:s'))
            ->allowFailures()
            ->onQueue('settlements')
            ->dispatch();

        foreach ($orders as $order) {
            $batch->add(new ProcessTiktokSettlement($order));
        }

        $this->info("Batch job created with ID: " . $batch->id);
        $this->info("Use 'php artisan queue:work --queue=settlements' to process the jobs");
    }
}