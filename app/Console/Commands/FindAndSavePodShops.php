<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Tiktok\PodShopService;

class FindAndSavePodShops extends Command
{
    protected $signature = 'find:save-pod-shops {keyword?}';
    protected $description = 'Find and save POD shops based on a keyword';

    protected $podShopService;

    public function __construct(PodShopService $podShopService)
    {
        parent::__construct();
        $this->podShopService = $podShopService;
    }

    public function handle()
    {
        $keyword = $this->argument('keyword') ?? "Shirt";
        $this->info('Starting to find and save POD shops for keyword: ' . $keyword);

        try {
            $this->podShopService->findAndSavePodShops($keyword);
            $this->info('Successfully processed POD shops for keyword: ' . $keyword);
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage() .$e->getLine());
        }
    }
}
