<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\Log;

// Bootstrap Laravel application
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Test FlashShip Cancel Orders Logging ===\n\n";

try {
    // Test 1: Kiểm tra xem channel có tồn tại không
    echo "1. Kiểm tra cấu hình channel 'flashship_cancel_orders'...\n";
    $channels = config('logging.channels');
    
    if (isset($channels['flashship_cancel_orders'])) {
        echo "✓ Channel 'flashship_cancel_orders' đã được cấu hình\n";
        echo "  - Driver: " . $channels['flashship_cancel_orders']['driver'] . "\n";
        echo "  - Path: " . $channels['flashship_cancel_orders']['path'] . "\n";
        echo "  - Level: " . $channels['flashship_cancel_orders']['level'] . "\n";
        echo "  - Days: " . $channels['flashship_cancel_orders']['days'] . "\n";
    } else {
        echo "✗ Channel 'flashship_cancel_orders' không tồn tại\n";
        exit(1);
    }
    
    echo "\n";
    
    // Test 2: Kiểm tra thư mục log có tồn tại không
    echo "2. Kiểm tra thư mục log...\n";
    $logPath = storage_path('logs');
    if (!is_dir($logPath)) {
        echo "✗ Thư mục logs không tồn tại: $logPath\n";
        exit(1);
    }
    echo "✓ Thư mục logs tồn tại: $logPath\n";
    
    // Kiểm tra quyền ghi
    if (!is_writable($logPath)) {
        echo "✗ Không có quyền ghi vào thư mục logs\n";
        exit(1);
    }
    echo "✓ Có quyền ghi vào thư mục logs\n";
    
    echo "\n";
    
    // Test 3: Thử ghi log
    echo "3. Thử ghi log vào channel 'flashship_cancel_orders'...\n";
    
    $testData = [
        'test_time' => now()->toDateTimeString(),
        'supplier_order_id_db' => 12345,
        'order_code' => 'TEST_ORDER_001',
        'supplier_order_id' => 'FLASHSHIP_TEST_001',
        'response' => [
            'status' => 'success',
            'message' => 'Test cancel order',
            'test' => true
        ]
    ];
    
    Log::channel('flashship_cancel_orders')->info('Test Cancel Order Log', $testData);
    echo "✓ Đã ghi log thành công\n";
    
    echo "\n";
    
    // Test 4: Kiểm tra file log có được tạo không
    echo "4. Kiểm tra file log đã được tạo...\n";
    $expectedLogFile = storage_path('logs/cancel-orders.log');
    $todayLogFile = storage_path('logs/cancel-orders-' . date('Y-m-d') . '.log');
    
    $logFileExists = false;
    $actualLogFile = '';
    
    if (file_exists($expectedLogFile)) {
        $logFileExists = true;
        $actualLogFile = $expectedLogFile;
    } elseif (file_exists($todayLogFile)) {
        $logFileExists = true;
        $actualLogFile = $todayLogFile;
    }
    
    if ($logFileExists) {
        echo "✓ File log đã được tạo: $actualLogFile\n";
        
        // Đọc nội dung log mới nhất
        $logContent = file_get_contents($actualLogFile);
        $lines = explode("\n", trim($logContent));
        $lastLine = end($lines);
        
        echo "✓ Nội dung log mới nhất:\n";
        echo "  $lastLine\n";
        
        // Kiểm tra xem có chứa test data không
        if (strpos($lastLine, 'Test Cancel Order Log') !== false) {
            echo "✓ Log chứa đúng message test\n";
        } else {
            echo "⚠ Log không chứa message test mong đợi\n";
        }
        
        if (strpos($lastLine, 'TEST_ORDER_001') !== false) {
            echo "✓ Log chứa đúng test data\n";
        } else {
            echo "⚠ Log không chứa test data mong đợi\n";
        }
        
    } else {
        echo "✗ File log không được tạo\n";
        echo "  Đã kiểm tra: $expectedLogFile\n";
        echo "  Đã kiểm tra: $todayLogFile\n";
    }
    
    echo "\n";
    
    // Test 5: Thử ghi nhiều loại log level
    echo "5. Thử ghi các loại log level khác nhau...\n";
    
    Log::channel('flashship_cancel_orders')->debug('Debug message', ['type' => 'debug']);
    Log::channel('flashship_cancel_orders')->info('Info message', ['type' => 'info']);
    Log::channel('flashship_cancel_orders')->warning('Warning message', ['type' => 'warning']);
    Log::channel('flashship_cancel_orders')->error('Error message', ['type' => 'error']);
    
    echo "✓ Đã ghi tất cả các log level\n";
    
    echo "\n=== Test hoàn thành ===\n";
    echo "Nếu tất cả test đều pass, channel 'flashship_cancel_orders' đang hoạt động bình thường.\n";
    echo "Kiểm tra file log tại: " . ($actualLogFile ?? $todayLogFile) . "\n";
    
} catch (Exception $e) {
    echo "✗ Lỗi trong quá trình test: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
