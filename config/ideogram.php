<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Ideogram Service Configuration
    |--------------------------------------------------------------------------
    |
    | Cấu hình cho dịch vụ Ideogram AI image generation
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Legacy Settings (Deprecated)
    |--------------------------------------------------------------------------
    |
    | Các cài đặt này đã được thay thế bằng hệ thống quản lý nhiều tài khoản
    | trong database thông qua Filament admin panel.
    |
    | Tất cả thông tin authorization, cookie sẽ được lưu trong bảng
    | ideogram_accounts và quản lý qua giao diện admin.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    |
    | Cài đặt cho API Ideogram
    |
    */
    'api' => [
        'base_url' => 'https://ideogram.ai/api',
        'timeout' => 60,
        'max_generation_timeout' => 40000, // 40 giây
        'poll_interval' => 2000, // 2 giây
        'wait_safety_margin' => 1000, // 1 giây
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Generation Settings
    |--------------------------------------------------------------------------
    |
    | Cài đặt mặc định cho việc tạo ảnh
    |
    */
    'generation' => [
        'model_version' => 'V_1_5',
        'use_autoprompt_option' => 'ON',
        'sampling_speed' => 0,
        'style_expert' => 'AUTO',
        'resolution' => [
            'width' => 1024,
            'height' => 1024
        ],
        'color_palette' => [
            ['color_hex' => '#F24B59'],
            ['color_hex' => '#49D906'],
            ['color_hex' => '#AED919'],
            ['color_hex' => '#F2B29B'],
            ['color_hex' => '#BF1E10'],
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Account Management
    |--------------------------------------------------------------------------
    |
    | Cài đặt cho việc quản lý tài khoản
    |
    */
    'accounts' => [
        'default_daily_limit' => 100,
        'auto_switch_on_limit' => true,
        'cache_duration' => 3600, // 1 giờ
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Cài đặt logging cho Ideogram service
    |
    */
    'logging' => [
        'enabled' => true,
        'level' => 'info',
        'log_requests' => true,
        'log_responses' => false, // Tắt để tránh log sensitive data
    ],
];
