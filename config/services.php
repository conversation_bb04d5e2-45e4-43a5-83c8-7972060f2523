<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */
    // 'cloudflare' => [
    //     'api_token' => env('CLOUDFLARE_API_TOKEN') ?? '-SqgmG4T2D_VfaW6YKUL7jfOcLUCFw59V2hW0LHE',
    //     'account_id' => env('CLOUDFLARE_ACCOUNT_ID') ?? '31ee43a9f25bb8114d931113655047b7',
    //     'key' => env('CLOUDFLARE_KEY') ?? '1eafe43e4d785de343e553196b72d87090884',
    //     'email' => env('CLOUDFLARE_EMAIL') ?? '<EMAIL>',
    //     'default_ip' => env('CLOUDFLARE_DEFAULT_IP') ?? '',
    // ],
    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],
    'pressify' => [
        'api_key' => env('PRESSIFY_API_KEY') ?? 'daseNsGZ-Vdrh-q8gY-xlRU',
        'api_key_test' => env('PRESSIFY_API_KEY_TEST') ?? 'daseNsGZ-Vdrh-q8gY-xlRU',
    ],
    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'fpt' => [
        'tts_api_key' => env('FPT_TTS_API_KEY') ?? '2BjR4IoPeoUb3J57BTLr6N2eSJxJjFTN',
    ],

    'oxylabs' => [
        'username' => env('OXYLABS_USERNAME'),
        'password' => env('OXYLABS_PASSWORD'),
    ],
    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
    ],
    
    'rapidapi' => [
        'key' => env('RAPIDAPI_KEY'),
    ],
];
