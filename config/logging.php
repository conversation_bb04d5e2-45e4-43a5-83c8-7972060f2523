<?php

use Monolog\Handler\NullHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\SyslogUdpHandler;
use Monolog\Processor\PsrLogMessageProcessor;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'stack'),

    /*
    |--------------------------------------------------------------------------
    | Deprecations Log Channel
    |--------------------------------------------------------------------------
    |
    | This option controls the log channel that should be used to log warnings
    | regarding deprecated PHP and library features. This allows you to get
    | your application ready for upcoming major versions of dependencies.
    |
    */

    'deprecations' => [
        'channel' => env('LOG_DEPRECATIONS_CHANNEL', 'null'),
        'trace' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['single', 'tiktok-flashdeal'],
            'ignore_exceptions' => false,
        ],


        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'replace_placeholders' => true,
        ],
        'auto-fulfill' => [
            'driver' => 'daily',
            'path' => storage_path('logs/auto-fulfill/auto-fulfill.log'),
            'level' => 'debug',
            'days' => 14,
        ],

        'check_fulfill' => [
            'driver' => 'daily',
            'path' => storage_path('logs/check-fulfill/check-fulfill.log'),
            'level' => 'debug',
            'days' => 30,
        ],

        // Proxy Logging Channels
        'proxy' => [
            'driver' => 'daily',
            'path' => storage_path('logs/proxy/proxy.log'),
            'level' => 'debug',
            'days' => 30,
            'replace_placeholders' => true,
        ],

        'proxy_by_proxies_com' => [
            'driver' => 'daily',
            'path' => storage_path('logs/proxy/proxies_com.log'),
            'level' => 'debug',
            'days' => 30,
            'replace_placeholders' => true,
        ],

        // Auto-Fulfill Logging Channels
        'auto_fulfill_operations' => [
            'driver' => 'daily',
            'path' => storage_path('logs/auto-fulfill/operations.log'),
            'level' => 'debug',
            'days' => 30,
            'permission' => 0664,
        ],

        'auto_fulfill_orders' => [
            'driver' => 'daily',
            'path' => storage_path('logs/auto-fulfill/order-fulfillment.log'),
            'level' => 'debug',
            'days' => 30,
            'permission' => 0664,
        ],

        'auto_fulfill_command' => [
            'driver' => 'daily',
            'path' => storage_path('logs/auto-fulfill/command.log'),
            'level' => 'debug',
            'days' => 30,
            'permission' => 0664,
        ],

        'auto_fulfill_errors' => [
            'driver' => 'daily',
            'path' => storage_path('logs/auto-fulfill/errors.log'),
            'level' => 'warning',
            'days' => 60,
            'permission' => 0664,
        ],

        'auto_fulfill_performance' => [
            'driver' => 'daily',
            'path' => storage_path('logs/auto-fulfill/performance.log'),
            'level' => 'debug',
            'days' => 7,
            'permission' => 0664,
        ],

        'auto_fulfill_api' => [
            'driver' => 'daily',
            'path' => storage_path('logs/auto-fulfill/api-calls.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
        ],

        'auto_fulfill_eligibility' => [
            'driver' => 'daily',
            'path' => storage_path('logs/auto-fulfill/eligibility.log'),
            'level' => 'debug',
            'days' => 30,
            'permission' => 0664,
        ],

        'ideogram' => [
            'driver' => 'daily',
            'path' => storage_path('logs/ideogram/ideogram.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
        ],

        'ideogram_ideo-sync' => [
            'driver' => 'daily',
            'path' => storage_path('logs/ideogram/ideo-sync.log'),
            'level' => 'debug',
            'days' => 30,
            'permission' => 0664,
            'replace_placeholders' => true,
        ],
        'tiktok-flashdeal' => [
            'driver' => 'daily',
            'path' => storage_path('logs/flashdeal/tiktok-flashdeal.log'),
            'level' => 'debug',
            'days' => 14,
        ],
  

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 14,
            'replace_placeholders' => true,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => env('LOG_LEVEL', 'critical'),
            'replace_placeholders' => true,
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => env('LOG_PAPERTRAIL_HANDLER', SyslogUdpHandler::class),
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
                'connectionString' => 'tls://'.env('PAPERTRAIL_URL').':'.env('PAPERTRAIL_PORT'),
            ],
            'processors' => [PsrLogMessageProcessor::class],
        ],

        'stderr' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => StreamHandler::class,
            'formatter' => env('LOG_STDERR_FORMATTER'),
            'with' => [
                'stream' => 'php://stderr',
            ],
            'processors' => [PsrLogMessageProcessor::class],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => env('LOG_LEVEL', 'debug'),
            'facility' => LOG_USER,
            'replace_placeholders' => true,
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => env('LOG_LEVEL', 'debug'),
            'replace_placeholders' => true,
        ],

        'null' => [
            'driver' => 'monolog',
            'handler' => NullHandler::class,
        ],

        'emergency' => [
            'path' => storage_path('logs/laravel.log'),
        ],

        'ideogram' => [
            'driver' => 'daily',
            'path' => storage_path('logs/ideogram.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 14,
            'replace_placeholders' => true,
        ],

        'flash-sales' => [
            'driver' => 'daily',
            'path' => storage_path('logs/flash-sales.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 7,
            'permission' => 0664,
        ],

        'settlements' => [
            'driver' => 'daily',
            'path' => storage_path('logs/settlements.log'),
            'level' => 'debug',
            'days' => 14,
        ],

        'upload-products' => [
            'driver' => 'daily',
            'path' => storage_path('logs/upload-products.log'),
            'level' => 'debug',
            'days' => 14,
        ],

        // FlashShip Log Channels

        'flashship' => [
            'driver' => 'daily',
            'path' => storage_path('logs/flashship/flashship.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
        ],

        'flashship_cancel_orders' => [
            'driver' => 'daily',
            'path' => storage_path('logs/flashship/cancel-orders.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
        ],

        'flashship_sync_status' => [
            'driver' => 'daily',
            'path' => storage_path('logs/flashship/sync-status.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
        ],

        'flashship_create_order' => [
            'driver' => 'daily',
            'path' => storage_path('logs/flashship/create-order.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
        ],

        'flashship_update_order' => [
            'driver' => 'daily',
            'path' => storage_path('logs/flashship/update-order.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
        ],

        'flashship_call_api' => [
            'driver' => 'daily',
            'path' => storage_path('logs/flashship/call-api.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
        ],

        // TikTok Shop Log Channels
        'tiktok_api' => [
            'driver' => 'daily',
            'path' => storage_path('logs/tiktok/api.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0775,
        ],

        'tiktok_auth' => [
            'driver' => 'daily',
            'path' => storage_path('logs/tiktok/auth.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0775,
        ],

        'tiktok_orders' => [
            'driver' => 'daily',
            'path' => storage_path('logs/tiktok/orders.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0775,
        ],

        'tiktok_labels' => [
            'driver' => 'daily',
            'path' => storage_path('logs/tiktok/labels.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0775,
        ],

        'tiktok_products' => [
            'driver' => 'daily',
            'path' => storage_path('logs/tiktok/products.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0775,
        ],

        'tiktok_finance' => [
            'driver' => 'daily',
            'path' => storage_path('logs/tiktok/finance.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0775,
        ],

        'tiktok_sync_store' => [
            'driver' => 'daily',
            'path' => storage_path('logs/tiktok/sync-store.log'),
            'level' => 'debug',
            'days' => 30,
            'permission' => 0775,
        ],

        // Lark Log Channels
        'lark_api' => [
            'driver' => 'daily',
            'path' => storage_path('logs/lark/api.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
            'tap' => [App\Logging\EnsureDirectoryPermissions::class],
        ],

        'lark_auth' => [
            'driver' => 'daily',
            'path' => storage_path('logs/lark/auth.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
            'tap' => [App\Logging\EnsureDirectoryPermissions::class],
        ],

        'lark_messages' => [
            'driver' => 'daily',
            'path' => storage_path('logs/lark/messages.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
            'tap' => [App\Logging\EnsureDirectoryPermissions::class],
        ],

        'lark_webhooks' => [
            'driver' => 'daily',
            'path' => storage_path('logs/lark/webhooks.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
            'tap' => [App\Logging\EnsureDirectoryPermissions::class],
        ],

        'lark_images' => [
            'driver' => 'daily',
            'path' => storage_path('logs/lark/images.log'),
            'level' => 'debug',
            'days' => 14,
            'permission' => 0664,
            'tap' => [App\Logging\EnsureDirectoryPermissions::class],
        ],

        'lark_errors' => [
            'driver' => 'daily',
            'path' => storage_path('logs/lark/errors.log'),
            'level' => 'error',
            'days' => 30,
            'permission' => 0664,
            'tap' => [App\Logging\EnsureDirectoryPermissions::class],
        ],

        // Product Variant Mapping Log Channels
        'variant_mapping' => [
            'driver' => 'daily',
            'path' => storage_path('logs/variant-mapping/mapping.log'),
            'level' => 'debug',
            'days' => 30,
            'permission' => 0664,
        ],

        'variant_mapping_success' => [
            'driver' => 'daily',
            'path' => storage_path('logs/variant-mapping/success.log'),
            'level' => 'info',
            'days' => 14,
            'permission' => 0664,
        ],

        'variant_mapping_errors' => [
            'driver' => 'daily',
            'path' => storage_path('logs/variant-mapping/errors.log'),
            'level' => 'error',
            'days' => 60,
            'permission' => 0664,
        ],

        'variant_mapping_analysis' => [
            'driver' => 'daily',
            'path' => storage_path('logs/variant-mapping/analysis.log'),
            'level' => 'debug',
            'days' => 30,
            'permission' => 0664,
        ],
    ],

];
