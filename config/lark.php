<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Lark Bot Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Lark (Feishu) bot integration
    |
    */

    'default_app' => [
        'app_id' => env('LARK_APP_ID'),
        'app_secret' => env('LARK_APP_SECRET'),
    ],

    'api' => [
        'base_url' => 'https://open.feishu.cn/open-apis',
        'auth_url' => 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal',
        'message_url' => 'https://open.feishu.cn/open-apis/im/v1/messages',
        'bot_info_url' => 'https://open.feishu.cn/open-apis/bot/v3/info',
        'chat_info_url' => 'https://open.feishu.cn/open-apis/im/v1/chats',
    ],

    'default_chats' => [
        'system' => env('LARK_SYSTEM_CHAT_ID'),
        'notifications' => env('LARK_NOTIFICATIONS_CHAT_ID'),
    ],

    'timeout' => 30, // seconds

    'retry' => [
        'max_attempts' => 3,
        'delay' => 1000, // milliseconds
    ],
];
